version: 2.1

orbs:
  h1: h1nyc/elion-build-deploy@1.5.0
  slack: circleci/slack@3.4.2
  aws-cli: circleci/aws-cli@4.0.0

defaults:
  lower-env-cluster: &lower-env-defaults
    cluster-name: h1-product-development
  production-cluster: &production-defaults
    cluster-name: h1-product-production
  non-prod-defaults: &non-prod-defaults
    cluster-name: h1-app-eks-ci
  default-deployable-images: &default-deployable-images
    images: >
      ${AWS_ECR_ACCOUNT_URL}/h1-search/resource-server:${DOCKER_VERSION_TAG}
  default-deployable-non-prod-images: &default-deployable-non-prod-images
    images: >
      ${AWS_ECR_ACCOUNT_URL_NON_PROD}/h1-search/resource-server:${DOCKER_VERSION_TAG},
  default-watchable-deployments: &default-watchable-deployments
    deployments: >
      search.resource-server

commands:
  oidc_aws_account:
    description: "OIDC auth into aws account"
    parameters:
      account-id:
        type: integer
    steps:
      - aws-cli/setup:
          role_arn: "arn:aws:iam::<< parameters.account-id >>:role/circleci_oidc_auth"
          role_session_name: circleci-job

jobs:
  notify-slack:
    parameters:
      environment:
        type: string
    docker:
      - image: "cimg/base:stable"
    steps:
      - slack/notify:
          message: "${CIRCLE_PULL_REQUEST} deployed to <<parameters.environment>> environment"

workflows:
  h1-search:
    jobs:
      # TESTS AND CHECKS
      - h1/build-image:
          name: "Build image for tests"
          image-name: "h1-search"

      - h1/run-tests:
          name: "Run tests"
          service-name: "search"
          node-version: "20.19.1"
          image-name: "h1-search"
          pre-steps:
            - checkout
            - h1/set-npm-registry
          requires:
            - "Build image for tests"

      - h1/ci-checks:
          name: "Run CI checks"
          node-version: "20.19.1"
          pre-steps:
            - checkout
            - h1/set-npm-registry
            - h1/install-dependencies
            - h1/build-packages

      # PUBLISH NPM PACKAGES

      - h1/publish:
          name: "Publish package"
          node-version: "20.19.1"
          filters:
            branches:
              only:
                - master
          requires:
            - Run tests
            - Run CI checks

      # BUILDS

      - h1/build-and-push-image:
          name: "Build resource server"
          pre-steps:
            - oidc_aws_account:
                account-id: ************
          repo: "h1-search/resource-server"
          dockerfile: "./docker/Dockerfile.resource-server"
          requires:
            - Publish package

      - h1/build-and-push-image:
          name: "Build resource server non-prod"
          pre-steps:
            - oidc_aws_account:
                account-id: ************
          repo: "h1-search/resource-server"
          account-url: AWS_ECR_ACCOUNT_URL_NON_PROD
          region: AWS_REGION_NON_PROD
          dockerfile: "./docker/Dockerfile.resource-server"
          requires:
            - Publish package

      # DEPLOYS

      - h1/apply-deployment-updates:
          name: "Deploy to develop"
          pre-steps:
            - oidc_aws_account:
                account-id: ************
          namespace: "develop"
          kustomize-env-folder: "develop"
          docker-id: ${AWS_ECR_ACCOUNT_URL_NON_PROD}
          aws-region: ${AWS_REGION_NON_PROD}
          <<: *non-prod-defaults
          <<: *default-deployable-non-prod-images
          <<: *default-watchable-deployments
          requires:
            - Build resource server non-prod

      - notify-slack:
          name: "Notify Slack - Deployed to develop"
          environment: "development"
          requires:
            - Deploy to develop

      - h1/run-integration-tests:
          name: "Run integration tests"
          collection: "4045169-fea7d380-a39f-4b20-a65b-c1ae536ae3a0"
          environment: "4045169-63a62c6c-367c-46ac-88a3-1cc9fe5ba3e2"
          requires:
            - Deploy to develop
          pre-steps:
            - checkout

      - approve-to-production:
          name: "Approve production + staging data deployment"
          type: approval
          requires:
            - Deploy to develop

      - h1/apply-deployment-updates:
          name: "Deploy to staging-data"
          pre-steps:
            - oidc_aws_account:
                account-id: ************
          namespace: "staging-data"
          kustomize-env-folder: "staging-data"
          docker-id: ${AWS_ECR_ACCOUNT_URL_NON_PROD}
          aws-region: ${AWS_REGION_NON_PROD}
          <<: *non-prod-defaults
          <<: *default-deployable-non-prod-images
          <<: *default-watchable-deployments
          requires:
            - Approve production + staging data deployment

      - h1/apply-deployment-updates:
          name: "Deploy to production"
          pre-steps:
            - oidc_aws_account:
                account-id: ************
          namespace: "production"
          kustomize-env-folder: "production"
          <<: *production-defaults
          <<: *default-deployable-images
          <<: *default-watchable-deployments
          requires:
            - Approve production + staging data deployment

      - notify-slack:
          name: "Notify Slack - Deployed to production"
          environment: "production"
          requires:
            - Deploy to production
