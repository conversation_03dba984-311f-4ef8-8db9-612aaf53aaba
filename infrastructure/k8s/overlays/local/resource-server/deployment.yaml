apiVersion: apps/v1
kind: Deployment
metadata:
  name: search.resource-server
  annotations:
    reloader.stakater.com/search: "true"
spec:
  replicas: 1
  template:
    metadata:
      labels:
        app: search.resource-server
        admission.datadoghq.com/enabled: "false"
    spec:
      containers:
        - name: search-resource-server
          command: ["yarn", "workspace", "@h1eng/search-resource-server", "start:dev"]
