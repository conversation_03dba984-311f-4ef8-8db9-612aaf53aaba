apiVersion: apps/v1
kind: Deployment
metadata:
  name: search.resource-server
spec:
  replicas: 2
  selector:
    matchLabels:
      app: search.resource-server
  template:
    metadata:
      labels:
        app: search.resource-server
    spec:
      containers:
        - name: search-resource-server
          resources:
            limits:
              cpu: 1500m
              memory: 5Gi
            requests:
              cpu: 260m
              memory: 1Gi
