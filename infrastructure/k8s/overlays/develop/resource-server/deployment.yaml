apiVersion: apps/v1
kind: Deployment
metadata:
  name: search.resource-server
spec:
  selector:
    matchLabels:
      app: search.resource-server
  template:
    metadata:
      labels:
        app: search.resource-server
        admission.datadoghq.com/enabled: "false"
    spec:
      containers:
        - name: search-resource-server
          image: 588738588589.dkr.ecr.us-east-2.amazonaws.com/h1-search/resource-server:latest
