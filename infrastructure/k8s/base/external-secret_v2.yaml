apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: search-eso 
spec:
  secretStoreRef:
    name: secretstore-sample
    kind: ClusterSecretStore
  refreshInterval: "10s"
  target:
    name: search-eso
    creationPolicy: 'Owner'
    template:
      metadata:
        annotations:
          reloader.stakater.com/match: "true"
  dataFrom:
  - extract:
      key: develop/search
