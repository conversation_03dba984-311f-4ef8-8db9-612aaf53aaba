apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    reloader.stakater.com/search: "true"
  name: search.resource-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: search.resource-server
  template:
    metadata:
      labels:
        app: search.resource-server
        admission.datadoghq.com/enabled: "true"
    spec:
      terminationGracePeriodSeconds: 240
      containers:
        - envFrom:
            - secretRef:
                name: search-eso
            - configMapRef:
                name: search.resource-server
          image: 247756923940.dkr.ecr.us-east-1.amazonaws.com/h1-search/resource-server:latest
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /_health
              port: 9000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 30
          name: search-resource-server
          lifecycle:
            preStop:
              exec:
                # because we pipe datadog (yarn start | pino-datadog) the signals don't make it
                # to the node processes from Kubernetes. This prestop hook finds the processes
                # and sends the signal. This is the best option. Others include a start script, etc.
                command: ["/bin/sh", "-c", "kill -SIGTERM $(pgrep -f index.ts)"]
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /_health
              port: 9000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 30
          resources:
            limits:
              cpu: 1500m
              memory: 5Gi
            requests:
              cpu: 150m
              memory: 500Mi
