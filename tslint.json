{"defaultSeverity": "error", "extends": ["tslint:recommended"], "jsRules": {}, "rules": {"trailing-comma": "never", "interface-name": [true, "never-prefix"], "max-classes-per-file": false, "no-var-requires": false, "variable-name": [true, "ban-keywords", "check-format", "allow-leading-underscore", "allow-pascal-case"], "member-access": [true, "no-public"], "no-console": [false, "log", "error"], "arrow-parens": ["as-needed", {"requireForBlockBody": true}], "object-literal-sort-keys": false, "object-literal-key-quotes": false, "max-line-length": false, "comment-format": false, "ordered-imports": false, "no-empty": [true, "allow-empty-catch", "allow-empty-functions"], "curly": [true, "ignore-same-line"], "semicolon": [true, "always", "ignore-bound-class-methods"], "cyclomatic-complexity": {"severity": "warning", "options": [5]}}, "rulesDirectory": [], "linterOptions": {"exclude": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/excel-cli/**", "packages/common/secrets/**", "packages/apis/excel-api/**", "packages/common/logger/**", "packages/entities/cli/**", "packages/apis/data-api/**", "packages/app/h1-app/**", "packages/common/apm/**", "packages/common/analytics-tracking/**"]}}