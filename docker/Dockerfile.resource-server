FROM node:20.19.1-alpine

ARG NPM_TOKEN

RUN echo "registry=https://registry.npmjs.org/" > ~/.npmrc
RUN echo "//registry.npmjs.org/:_authToken=${NPM_TOKEN}" >> ~/.npmrc

RUN yarn global add ts-node@10.1.0
RUN yarn global add pino-datadog

WORKDIR /h1

COPY packages/resource-server/ ./
COPY yarn.lock .

RUN yarn install --prod

# If DD_DISABLED is true, don't pipe the logs to datadog. This is because staging-data and develop are using open search now.
# Prod is the only environment that should be using datadog.
CMD /bin/sh -c "if [ \"$DD_DISABLED\" = \"true\" ]; then yarn start; else yarn start | pino-datadog; fi"
