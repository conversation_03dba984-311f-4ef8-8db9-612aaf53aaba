# Search

This monorepo contains the necessary packages for the H1 Search Domain.

## Getting started

After cloning the repository, you will need to install and link the dependencies for the monorepo:

```
lerna bootstrap
```

## Packages

### resource-server

The resource server exposes the RPC methods, which allows outside clients access to the search service.

[More info](./packages/resoure-server/README.md)

### sdk

This SDK provides the RPC client implementation.

[More info](./packages/sdk/README.md)

## Deploying

The search resource server is hosted on Kubernetes. CircleCI manages building, pushing and deploying the image.

To deploy a package, run `lerna publish`.

Once the version number on changed packages has been bumped, CircleCI will build and deploy the changed package(s) to develop. From the CircleCI workflow, the build can be promoted to staging, staging data, and production.
