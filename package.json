{"name": "h1-search", "version": "1.23.66-alpha1", "main": "index.js", "license": "UNLICENSED", "private": true, "workspaces": ["packages/*"], "scripts": {"test:setup": "docker-compose up deps", "test:teardown": "docker-compose stop", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint": "eslint . --ext .ts,.js", "prepare": "husky install"}, "dependencies": {"nodemon": "^2.0.4", "ts-node": "^10.9.1", "typescript": "^4.7.4", "@types/geojson": "^7946.0.13"}, "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/jest": "^29.2.5", "@types/jest-when": "^3.5.2", "@types/node": "^18.11.18", "@typescript-eslint/eslint-plugin": "^5.33.0", "@typescript-eslint/parser": "^5.33.0", "eslint": "^8.22.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.3.1", "jest-junit": "^16.0.0", "jest-when": "^3.5.2", "lerna": "^6.3.0", "lint-staged": "^13.0.3", "prettier": "^2.1.2", "ts-jest": "^29.0.3"}}