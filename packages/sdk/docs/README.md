@h1nyc/search-sdk

# @h1nyc/search-sdk

## Table of contents

### Modules

- [api](modules/api.md)
- [api/response](modules/api_response.md)
- [api/response/ResponseInterfaces](modules/api_response_responseinterfaces.md)
- [clients](modules/clients.md)
- [clients/KolSearchResourceClient](modules/clients_kolsearchresourceclient.md)
- [constants](modules/constants.md)
- [flow](modules/flow.md)
- [flow/identities](modules/flow_identities.md)
- [flow/mapping](modules/flow_mapping.md)
- [flow/merge](modules/flow_merge.md)
- [flow/quieres](modules/flow_quieres.md)
- [flow/types](modules/flow_types.md)
- [index](modules/index.md)
- [indices](modules/indices.md)
- [indices/peopleIndex](modules/indices_peopleindex.md)
- [interfaces](modules/interfaces.md)
- [interfaces/Claims](modules/interfaces_claims.md)
- [interfaces/ElasticOptions](modules/interfaces_elasticoptions.md)
- [interfaces/KOLResultInterfaces](modules/interfaces_kolresultinterfaces.md)
- [interfaces/SortOptions](modules/interfaces_sortoptions.md)
- [interfaces/documentScores](modules/interfaces_documentscores.md)
- [interfaces/elasticDocuments](modules/interfaces_elasticdocuments.md)
- [interfaces/filterInterfaces](modules/interfaces_filterinterfaces.md)
- [interfaces/helpers](modules/interfaces_helpers.md)
- [interfaces/pagination](modules/interfaces_pagination.md)
- [interfaces/searchInterfaces](modules/interfaces_searchinterfaces.md)
- [resources](modules/resources.md)
- [resources/ClaimsResource](modules/resources_claimsresource.md)
- [resources/ClaimsResourceClient](modules/resources_claimsresourceclient.md)
- [resources/KolSearchResource](modules/resources_kolsearchresource.md)
- [resources/NameSearchResource](modules/resources_namesearchresource.md)
- [resources/NameSearchResourceClient](modules/resources_namesearchresourceclient.md)
- [types](modules/types.md)
- [util](modules/util.md)
- [util/elasticUtils](modules/util_elasticutils.md)
- [util/facetUtils](modules/util_facetutils.md)
- [util/filterUtils](modules/util_filterutils.md)
- [util/transformer](modules/util_transformer.md)
