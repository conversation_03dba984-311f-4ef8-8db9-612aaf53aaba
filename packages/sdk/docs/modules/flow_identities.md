[@h1nyc/search-sdk](../README.md) / flow/identities

# Module: flow/identities

## Table of contents

### Functions

- [getSearchQueryAggTarget](flow_identities.md#getsearchqueryaggtarget)
- [getSearchQueryTarget](flow_identities.md#getsearchquerytarget)
- [isExcludedKeyWord](flow_identities.md#isexcludedkeyword)
- [isFieldSum](flow_identities.md#isfieldsum)

## Functions

### getSearchQueryAggTarget

▸ `Const`**getSearchQueryAggTarget**(`metric`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)): [*collaboratorCount*](../enums/flow_types.metrics.md#collaboratorcount) \| *_score*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md) |

**Returns:** [*collaboratorCount*](../enums/flow_types.metrics.md#collaboratorcount) \| *_score*

Defined in: [packages/sdk/src/flow/identities.ts:30](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L30)

___

### getSearchQueryTarget

▸ `Const`**getSearchQueryTarget**(`metric`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)): *field* \| *script*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md) |

**Returns:** *field* \| *script*

Defined in: [packages/sdk/src/flow/identities.ts:24](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L24)

___

### isExcludedKeyWord

▸ `Const`**isExcludedKeyWord**(`metric`: [*Metrics*](../enums/flow_types.metrics.md)): *boolean*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*Metrics*](../enums/flow_types.metrics.md) |

**Returns:** *boolean*

Defined in: [packages/sdk/src/flow/identities.ts:18](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L18)

___

### isFieldSum

▸ `Const`**isFieldSum**(`metric`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)): *boolean*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md) |

**Returns:** *boolean*

Defined in: [packages/sdk/src/flow/identities.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L8)
