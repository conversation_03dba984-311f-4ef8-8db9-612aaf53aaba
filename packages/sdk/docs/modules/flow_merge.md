[@h1nyc/search-sdk](../README.md) / flow/merge

# Module: flow/merge

## Table of contents

### Functions

- [fsiToFiltersArray](flow_merge.md#fsitofiltersarray)
- [getFilterOptions](flow_merge.md#getfilteroptions)

## Functions

### fsiToFiltersArray

▸ `Const`**fsiToFiltersArray**(`hasClaims`: *boolean*, `hasProfile`: *boolean*, `fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `tags?`: *string*[], `isExcel`: *boolean*, `dateOverRide?`: { `max`: *number* ; `min`: *number*  }): [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)[]

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`hasClaims` | *boolean* | - |
`hasProfile` | *boolean* | - |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) | - |
`tags` | *string*[] | ... |
`isExcel` | *boolean* | - |
`dateOverRide?` | { `max`: *number* ; `min`: *number*  } | - |

**Returns:** [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)[]

Defined in: [packages/sdk/src/flow/merge.ts:23](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/merge.ts#L23)

___

### getFilterOptions

▸ `Const`**getFilterOptions**(`fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `mqpm`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md), `tags?`: *string*[]): *object*

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) | - |
`mqpm` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) | - |
`tags` | *string*[] | ... |

**Returns:** *object*

Name | Type |
------ | ------ |
`filters` | { `bool`: { `must`: *any*[]  }  }[] |
`realCount` | *number* |

Defined in: [packages/sdk/src/flow/merge.ts:72](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/merge.ts#L72)
