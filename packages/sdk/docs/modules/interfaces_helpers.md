[@h1nyc/search-sdk](../README.md) / interfaces/helpers

# Module: interfaces/helpers

## Table of contents

### Type aliases

- [Without](interfaces_helpers.md#without)

## Type aliases

### Without

Ƭ **Without**<T, K\>: *Pick*<T, *Exclude*<keyof T, K\>\>

#### Type parameters:

Name | Type |
------ | ------ |
`T` | - |
`K` | keyof T |

Defined in: [packages/sdk/src/interfaces/helpers.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/helpers.ts#L1)
