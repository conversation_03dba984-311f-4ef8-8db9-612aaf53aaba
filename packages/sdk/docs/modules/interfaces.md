[@h1nyc/search-sdk](../README.md) / interfaces

# Module: interfaces

## Table of contents

### Enumerations

- [CardViewOpt](../enums/interfaces.cardviewopt.md)
- [CustomSortWeightValues](../enums/interfaces.customsortweightvalues.md)
- [FilterTypes](../enums/interfaces.filtertypes.md)
- [FilterTypesEnum](../enums/interfaces.filtertypesenum.md)
- [GenericSearchEnum](../enums/interfaces.genericsearchenum.md)
- [IndexTypes](../enums/interfaces.indextypes.md)
- [InputState](../enums/interfaces.inputstate.md)
- [SearchTypes](../enums/interfaces.searchtypes.md)
- [SearchView](../enums/interfaces.searchview.md)
- [SortDirection](../enums/interfaces.sortdirection.md)

### Interfaces

- [ActiveFilterBreakDown](../interfaces/interfaces.activefilterbreakdown.md)
- [AggregationFilterCount](../interfaces/interfaces.aggregationfiltercount.md)
- [CheckboxFilter](../interfaces/interfaces.checkboxfilter.md)
- [ClaimsDiagnosis](../interfaces/interfaces.claimsdiagnosis.md)
- [ClaimsFilter](../interfaces/interfaces.claimsfilter.md)
- [ClaimsProcedure](../interfaces/interfaces.claimsprocedure.md)
- [DateRangeFilter](../interfaces/interfaces.daterangefilter.md)
- [DocumentRanges](../interfaces/interfaces.documentranges.md)
- [DocumentScoreRange](../interfaces/interfaces.documentscorerange.md)
- [ElasticSearchDocuments](../interfaces/interfaces.elasticsearchdocuments.md)
- [FilterInterface](../interfaces/interfaces.filterinterface.md)
- [FilterSearchInterface](../interfaces/interfaces.filtersearchinterface.md)
- [FilterViewInterface](../interfaces/interfaces.filterviewinterface.md)
- [FiltersPropsInterface](../interfaces/interfaces.filterspropsinterface.md)
- [GenericFilterInterface](../interfaces/interfaces.genericfilterinterface.md)
- [GenericSearchDocument](../interfaces/interfaces.genericsearchdocument.md)
- [GenericSearchFilterInterface](../interfaces/interfaces.genericsearchfilterinterface.md)
- [GenericSearchInterface](../interfaces/interfaces.genericsearchinterface.md)
- [GenericSearchResultInterface](../interfaces/interfaces.genericsearchresultinterface.md)
- [GetCollaboratorsInterface](../interfaces/interfaces.getcollaboratorsinterface.md)
- [GetLiteProfileInterface](../interfaces/interfaces.getliteprofileinterface.md)
- [InjectedTagAssignment](../interfaces/interfaces.injectedtagassignment.md)
- [InjectedTagAssignmentUser](../interfaces/interfaces.injectedtagassignmentuser.md)
- [KOLDataRequest](../interfaces/interfaces.koldatarequest.md)
- [KOLDocumentCounts](../interfaces/interfaces.koldocumentcounts.md)
- [LiteProfile](../interfaces/interfaces.liteprofile.md)
- [LiteProfiles](../interfaces/interfaces.liteprofiles.md)
- [NameSuggestFilterSearchInterface](../interfaces/interfaces.namesuggestfiltersearchinterface.md)
- [NumberFilter](../interfaces/interfaces.numberfilter.md)
- [Pagination](../interfaces/interfaces.pagination.md)
- [PaginationOptions](../interfaces/interfaces.paginationoptions.md)
- [PaginationResponse](../interfaces/interfaces.paginationresponse.md)
- [PeopleSearchDocument](../interfaces/interfaces.peoplesearchdocument.md)
- [PersonName](../interfaces/interfaces.personname.md)
- [PersonsDocumentSummarySearchInterface](../interfaces/interfaces.personsdocumentsummarysearchinterface.md)
- [ResultCardAffiliation](../interfaces/interfaces.resultcardaffiliation.md)
- [ResultCardData](../interfaces/interfaces.resultcarddata.md)
- [RootSearchObject](../interfaces/interfaces.rootsearchobject.md)
- [SavedSearchBy](../interfaces/interfaces.savedsearchby.md)
- [SavedSearchByConstructor](../interfaces/interfaces.savedsearchbyconstructor.md)
- [SavedSearchFilterValues](../interfaces/interfaces.savedsearchfiltervalues.md)
- [ScoredDocumentData](../interfaces/interfaces.scoreddocumentdata.md)
- [ScoredDocumentFilterSearchInterface](../interfaces/interfaces.scoreddocumentfiltersearchinterface.md)
- [ScoredDocumentResult](../interfaces/interfaces.scoreddocumentresult.md)
- [ScoredDocumentResults](../interfaces/interfaces.scoreddocumentresults.md)
- [SearchFilterBuilderInterface](../interfaces/interfaces.searchfilterbuilderinterface.md)
- [SearchFilterOptions](../interfaces/interfaces.searchfilteroptions.md)
- [SortOptions](../interfaces/interfaces.sortoptions.md)
- [TagDocument](../interfaces/interfaces.tagdocument.md)
- [TagInterface](../interfaces/interfaces.taginterface.md)
- [TextFilter](../interfaces/interfaces.textfilter.md)
- [WeightedSortBy](../interfaces/interfaces.weightedsortby.md)
- [Works](../interfaces/interfaces.works.md)
- [WorksCount](../interfaces/interfaces.workscount.md)
- [YearRangeFilter](../interfaces/interfaces.yearrangefilter.md)

### Type aliases

- [KOLType](interfaces.md#koltype)
- [NamedSortBy](interfaces.md#namedsortby)
- [PersonCard](interfaces.md#personcard)
- [PersonSearchResponse](interfaces.md#personsearchresponse)
- [ScoredResultCardData](interfaces.md#scoredresultcarddata)
- [SearchFilterValue](interfaces.md#searchfiltervalue)
- [Without](interfaces.md#without)

### Variables

- [DefaultScoreWeights](interfaces.md#defaultscoreweights)
- [MAX\_BULK\_SELECT\_SIZE](interfaces.md#max_bulk_select_size)
- [SEARCH\_RESULT\_LIMIT](interfaces.md#search_result_limit)

### Functions

- [isGenericSearchInterface](interfaces.md#isgenericsearchinterface)

## Type aliases

### KOLType

Ƭ **KOLType**: *Institutional Pedigree* \| *Industry Partner* \| *Rising Star* \| *Research Scholar*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L12)

___

### NamedSortBy

Ƭ **NamedSortBy**: *diagnoses* \| *procedures* \| *publications* \| *trials* \| *citations* \| *socialMediaMentions* \| *grants* \| *payments* \| *congresses* \| *collaborators* \| *referralsReceived* \| *referralsSent*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:155](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L155)

___

### PersonCard

Ƭ **PersonCard**: [*Without*](interfaces_helpers.md#without)<[*ResultCardData*](../interfaces/interfaces_kolresultinterfaces.resultcarddata.md), *score* \| *trendHistory* \| *trendPresent*\> & { `scores`: [*ScoredDocumentData*](../interfaces/interfaces_documentscores.scoreddocumentdata.md) ; `totalWorks`: *number*  } & { `privateTagAssignments?`: [*InjectedTagAssignment*](../interfaces/interfaces_elasticdocuments.injectedtagassignment.md)[] ; `programmaticTags?`: { `__typename`: *ProgrammaticTag* ; `id`: *string* ; `name`: *string*  }[] ; `publicTagAssignments?`: [*InjectedTagAssignment*](../interfaces/interfaces_elasticdocuments.injectedtagassignment.md)[]  }

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:128](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L128)

___

### PersonSearchResponse

Ƭ **PersonSearchResponse**: [*PaginationResponse*](../interfaces/interfaces_pagination.paginationresponse.md)<[*PersonCard*](interfaces_elasticdocuments.md#personcard)\> & { `filterCounts`: [*AggregationFilterCount*](../interfaces/interfaces_elasticdocuments.aggregationfiltercount.md)[] ; `normalizedRange`: [*DocumentScoreRange*](../interfaces/interfaces_documentscores.documentscorerange.md) ; `ranges`: [*DocumentRanges*](../interfaces/interfaces_documentscores.documentranges.md)  }

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:153](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L153)

___

### ScoredResultCardData

Ƭ **ScoredResultCardData**: [*PersonCard*](interfaces_elasticdocuments.md#personcard)

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:109](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L109)

___

### SearchFilterValue

Ƭ **SearchFilterValue**: { [name: string]: [*SearchFilterValue*](interfaces_filterinterfaces.md#searchfiltervalue);  } \| *string*[] \| *number* \| *number*[] \| *null* \| *undefined*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:452](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L452)

___

### Without

Ƭ **Without**<T, K\>: *Pick*<T, *Exclude*<keyof T, K\>\>

#### Type parameters:

Name | Type |
------ | ------ |
`T` | - |
`K` | keyof T |

Defined in: [packages/sdk/src/interfaces/helpers.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/helpers.ts#L1)

## Variables

### DefaultScoreWeights

• `Const` **DefaultScoreWeights**: [*WeightedSortBy*](../interfaces/interfaces_filterinterfaces.weightedsortby.md)

The weights of document types

**`todo:`** grants

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:209](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L209)

___

### MAX\_BULK\_SELECT\_SIZE

• `Const` **MAX\_BULK\_SELECT\_SIZE**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L8)

___

### SEARCH\_RESULT\_LIMIT

• `Const` **SEARCH\_RESULT\_LIMIT**: *9000*= 9000

The maximum number of search results to return

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L6)

## Functions

### isGenericSearchInterface

▸ **isGenericSearchInterface**(`obj`: *any*): obj is GenericSearchInterface

#### Parameters:

Name | Type |
------ | ------ |
`obj` | *any* |

**Returns:** obj is GenericSearchInterface

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:288](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L288)
