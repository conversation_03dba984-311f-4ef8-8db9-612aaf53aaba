[@h1nyc/search-sdk](../README.md) / interfaces/ElasticOptions

# Module: interfaces/ElasticOptions

## Table of contents

### Interfaces

- [ElasticOptions](../interfaces/interfaces_elasticoptions.elasticoptions.md)

### Type aliases

- [ElasticSearchType](interfaces_elasticoptions.md#elasticsearchtype)

## Type aliases

### ElasticSearchType

Ƭ **ElasticSearchType**: *dfs_query_then_fetch* \| *query_then_fetch*

Defined in: [packages/sdk/src/interfaces/ElasticOptions.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/ElasticOptions.ts#L1)
