[@h1nyc/search-sdk](../README.md) / flow

# Module: flow

## Table of contents

### Enumerations

- [Metrics](../enums/flow.metrics.md)
- [ScoreTypes](../enums/flow.scoretypes.md)

### Interfaces

- [MetricQueryPreMap](../interfaces/flow.metricquerypremap.md)
- [MetricQueryPreNestedPlanerMinMax](../interfaces/flow.metricqueryprenestedplanerminmax.md)

### Functions

- [buildYearPathRangeFilter](flow.md#buildyearpathrangefilter)
- [fieldsToMultiMatches](flow.md#fieldstomultimatches)
- [fsiMapped](flow.md#fsimapped)
- [fsiMapping](flow.md#fsimapping)
- [fsiToFiltersArray](flow.md#fsitofiltersarray)
- [getFilterOptions](flow.md#getfilteroptions)
- [getSearchQueryAggTarget](flow.md#getsearchqueryaggtarget)
- [getSearchQueryTarget](flow.md#getsearchquerytarget)
- [isExcludedKeyWord](flow.md#isexcludedkeyword)
- [isFieldSum](flow.md#isfieldsum)
- [term](flow.md#term)

## Functions

### buildYearPathRangeFilter

▸ `Const`**buildYearPathRangeFilter**(`paths`: *string*[], `minDate?`: *number*, `maxDate?`: *number*): *any*[]

#### Parameters:

Name | Type |
------ | ------ |
`paths` | *string*[] |
`minDate?` | *number* |
`maxDate?` | *number* |

**Returns:** *any*[]

Defined in: [packages/sdk/src/flow/quieres.ts:21](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/quieres.ts#L21)

___

### fieldsToMultiMatches

▸ `Const`**fieldsToMultiMatches**(`querys`: *string*[], `fields`: *string*[]): { `multi_match`: { `fields`: *string*[] ; `operator`: *string* = "and"; `query`: *string* ; `type`: *string* = "phrase" }  }[]

#### Parameters:

Name | Type |
------ | ------ |
`querys` | *string*[] |
`fields` | *string*[] |

**Returns:** { `multi_match`: { `fields`: *string*[] ; `operator`: *string* = "and"; `query`: *string* ; `type`: *string* = "phrase" }  }[]

Defined in: [packages/sdk/src/flow/quieres.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/quieres.ts#L9)

___

### fsiMapped

▸ `Const`**fsiMapped**(`isExcel?`: *boolean*, `hasClaims?`: *boolean*, `hasProfile?`: *boolean*): *object*

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`isExcel` | *boolean* | false |
`hasClaims` | *boolean* | false |
`hasProfile` | *boolean* | false |

**Returns:** *object*

Defined in: [packages/sdk/src/flow/mapping.ts:350](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/mapping.ts#L350)

___

### fsiMapping

▸ `Const`**fsiMapping**(`isExcel?`: *boolean*, `hasClaims?`: *boolean*, `hasProfile?`: *boolean*): [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md)[]

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`isExcel` | *boolean* | false |
`hasClaims` | *boolean* | false |
`hasProfile` | *boolean* | false |

**Returns:** [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md)[]

Defined in: [packages/sdk/src/flow/mapping.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/mapping.ts#L3)

___

### fsiToFiltersArray

▸ `Const`**fsiToFiltersArray**(`hasClaims`: *boolean*, `hasProfile`: *boolean*, `fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `tags?`: *string*[], `isExcel`: *boolean*, `dateOverRide?`: { `max`: *number* ; `min`: *number*  }): [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)[]

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`hasClaims` | *boolean* | - |
`hasProfile` | *boolean* | - |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) | - |
`tags` | *string*[] | ... |
`isExcel` | *boolean* | - |
`dateOverRide?` | { `max`: *number* ; `min`: *number*  } | - |

**Returns:** [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)[]

Defined in: [packages/sdk/src/flow/merge.ts:23](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/merge.ts#L23)

___

### getFilterOptions

▸ `Const`**getFilterOptions**(`fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `mqpm`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md), `tags?`: *string*[]): *object*

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) | - |
`mqpm` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) | - |
`tags` | *string*[] | ... |

**Returns:** *object*

Name | Type |
------ | ------ |
`filters` | { `bool`: { `must`: *any*[]  }  }[] |
`realCount` | *number* |

Defined in: [packages/sdk/src/flow/merge.ts:72](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/merge.ts#L72)

___

### getSearchQueryAggTarget

▸ `Const`**getSearchQueryAggTarget**(`metric`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)): [*collaboratorCount*](../enums/flow_types.metrics.md#collaboratorcount) \| *_score*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md) |

**Returns:** [*collaboratorCount*](../enums/flow_types.metrics.md#collaboratorcount) \| *_score*

Defined in: [packages/sdk/src/flow/identities.ts:30](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L30)

___

### getSearchQueryTarget

▸ `Const`**getSearchQueryTarget**(`metric`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)): *field* \| *script*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md) |

**Returns:** *field* \| *script*

Defined in: [packages/sdk/src/flow/identities.ts:24](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L24)

___

### isExcludedKeyWord

▸ `Const`**isExcludedKeyWord**(`metric`: [*Metrics*](../enums/flow_types.metrics.md)): *boolean*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*Metrics*](../enums/flow_types.metrics.md) |

**Returns:** *boolean*

Defined in: [packages/sdk/src/flow/identities.ts:18](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L18)

___

### isFieldSum

▸ `Const`**isFieldSum**(`metric`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)): *boolean*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md) |

**Returns:** *boolean*

Defined in: [packages/sdk/src/flow/identities.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L8)

___

### term

▸ `Const`**term**(`feild`: *string*, `query`: *string*): *object*

#### Parameters:

Name | Type |
------ | ------ |
`feild` | *string* |
`query` | *string* |

**Returns:** *object*

Name | Type |
------ | ------ |
`term` | {} |

Defined in: [packages/sdk/src/flow/quieres.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/quieres.ts#L1)
