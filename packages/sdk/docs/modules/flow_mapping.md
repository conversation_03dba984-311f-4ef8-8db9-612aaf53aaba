[@h1nyc/search-sdk](../README.md) / flow/mapping

# Module: flow/mapping

## Table of contents

### Functions

- [fsiMapped](flow_mapping.md#fsimapped)
- [fsiMapping](flow_mapping.md#fsimapping)

## Functions

### fsiMapped

▸ `Const`**fsiMapped**(`isExcel?`: *boolean*, `hasClaims?`: *boolean*, `hasProfile?`: *boolean*): *object*

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`isExcel` | *boolean* | false |
`hasClaims` | *boolean* | false |
`hasProfile` | *boolean* | false |

**Returns:** *object*

Defined in: [packages/sdk/src/flow/mapping.ts:350](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/mapping.ts#L350)

___

### fsiMapping

▸ `Const`**fsiMapping**(`isExcel?`: *boolean*, `hasClaims?`: *boolean*, `hasProfile?`: *boolean*): [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md)[]

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`isExcel` | *boolean* | false |
`hasClaims` | *boolean* | false |
`hasProfile` | *boolean* | false |

**Returns:** [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md)[]

Defined in: [packages/sdk/src/flow/mapping.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/mapping.ts#L3)
