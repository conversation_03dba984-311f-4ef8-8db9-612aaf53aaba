[@h1nyc/search-sdk](../README.md) / util/filterUtils

# Module: util/filterUtils

## Table of contents

### Variables

- [nonInitiatingFilters](util_filterutils.md#noninitiatingfilters)

### Functions

- [getSortWeight](util_filterutils.md#getsortweight)
- [hasTagsInSearchFilters](util_filterutils.md#hastagsinsearchfilters)
- [hasValidFiltersForSearch](util_filterutils.md#hasvalidfiltersforsearch)
- [transformFilters](util_filterutils.md#transformfilters)

## Variables

### nonInitiatingFilters

• `Const` **nonInitiatingFilters**: *string*[]

List of filter keys that when exist as the only filters will not execute a search.
Keys must match what is in `SavedSearchFilterValues` in path notation (foo.bar.baz)

Defined in: [packages/sdk/src/util/filterUtils.ts:14](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L14)

## Functions

### getSortWeight

▸ `Const`**getSortWeight**(`sortBy?`: [*WeightedSortBy*](../interfaces/interfaces_filterinterfaces.weightedsortby.md), `sortPath?`: *string*): *number*

#### Parameters:

Name | Type |
------ | ------ |
`sortBy?` | [*WeightedSortBy*](../interfaces/interfaces_filterinterfaces.weightedsortby.md) |
`sortPath?` | *string* |

**Returns:** *number*

Defined in: [packages/sdk/src/util/filterUtils.ts:190](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L190)

___

### hasTagsInSearchFilters

▸ `Const`**hasTagsInSearchFilters**(`filters`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md)): *boolean*

Check whether the filters contains tag IDs

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`filters` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) | Search filters    |

**Returns:** *boolean*

Defined in: [packages/sdk/src/util/filterUtils.ts:53](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L53)

___

### hasValidFiltersForSearch

▸ `Const`**hasValidFiltersForSearch**(`filters`: [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md)): *boolean*

Check that the filter values are valid.

This checks that there are initiating filter values in the list of filters.

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`filters` | [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md) | Filter values    |

**Returns:** *boolean*

Defined in: [packages/sdk/src/util/filterUtils.ts:23](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L23)

___

### transformFilters

▸ `Const`**transformFilters**(`fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `profileEnabled`: *boolean*, `claimsEnabled`: *boolean*, `referralsEnabled`: *boolean*): [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md)

Transforms the value of the FilterSearchInterface into SavedSearchFilterValues

Hopefully, this will not be needed anymore when we change the front end to
send the SavedSearchFilterValues to the API directly.

#### Parameters:

Name | Type |
------ | ------ |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) |
`profileEnabled` | *boolean* |
`claimsEnabled` | *boolean* |
`referralsEnabled` | *boolean* |

**Returns:** [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md)

Defined in: [packages/sdk/src/util/filterUtils.ts:78](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L78)
