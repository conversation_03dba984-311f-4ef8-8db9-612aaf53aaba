[@h1nyc/search-sdk](../README.md) / resources/ClaimsResource

# Module: resources/ClaimsResource

## Table of contents

### Enumerations

- [DiagnosesFields](../enums/resources_claimsresource.diagnosesfields.md)
- [ProceduresFields](../enums/resources_claimsresource.proceduresfields.md)

### Interfaces

- [ClaimsResource](../interfaces/resources_claimsresource.claimsresource.md)

### Type aliases

- [DiagnosesSortOptions](resources_claimsresource.md#diagnosessortoptions)
- [ProceduresSortOptions](resources_claimsresource.md#proceduressortoptions)

## Type aliases

### DiagnosesSortOptions

Ƭ **DiagnosesSortOptions**: [*SortOptions*](../interfaces/interfaces_sortoptions.sortoptions.md)<[*DiagnosesFields*](../enums/resources_claimsresource.diagnosesfields.md)\>

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:26](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L26)

___

### ProceduresSortOptions

Ƭ **ProceduresSortOptions**: [*SortOptions*](../interfaces/interfaces_sortoptions.sortoptions.md)<[*ProceduresFields*](../enums/resources_claimsresource.proceduresfields.md)\>

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:27](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L27)
