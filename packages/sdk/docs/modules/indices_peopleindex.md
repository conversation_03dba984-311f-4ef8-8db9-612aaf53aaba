[@h1nyc/search-sdk](../README.md) / indices/peopleIndex

# Module: indices/peopleIndex

## Table of contents

### Interfaces

- [EsIndexFacetMeta](../interfaces/indices_peopleindex.esindexfacetmeta.md)
- [EsIndexFieldMeta](../interfaces/indices_peopleindex.esindexfieldmeta.md)
- [EsIndexFilterMeta](../interfaces/indices_peopleindex.esindexfiltermeta.md)
- [EsIndexMetricMeta](../interfaces/indices_peopleindex.esindexmetricmeta.md)

### Variables

- [peopleFacets](indices_peopleindex.md#peoplefacets)
- [peopleFields](indices_peopleindex.md#peoplefields)

## Variables

### peopleFacets

• `Const` **peopleFacets**: [*EsIndexFacetMeta*](../interfaces/indices_peopleindex.esindexfacetmeta.md)[]

Defined in: [packages/sdk/src/indices/peopleIndex.ts:390](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L390)

___

### peopleFields

• `Const` **peopleFields**: [*EsIndexFieldMeta*](../interfaces/indices_peopleindex.esindexfieldmeta.md)[]

Defined in: [packages/sdk/src/indices/peopleIndex.ts:116](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L116)
