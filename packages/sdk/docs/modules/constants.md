[@h1nyc/search-sdk](../README.md) / constants

# Module: constants

## Table of contents

### Variables

- [RPC\_NAMESPACE\_CLAIMS\_SEARCH](constants.md#rpc_namespace_claims_search)
- [RPC\_NAMESPACE\_KOL\_SEARCH](constants.md#rpc_namespace_kol_search)
- [RPC\_NAMESPACE\_NAME\_SEARCH](constants.md#rpc_namespace_name_search)

## Variables

### RPC\_NAMESPACE\_CLAIMS\_SEARCH

• `Const` **RPC\_NAMESPACE\_CLAIMS\_SEARCH**: *searchClaims*= "searchClaims"

Defined in: [packages/sdk/src/constants.ts:2](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/constants.ts#L2)

___

### RPC\_NAMESPACE\_KOL\_SEARCH

• `Const` **RPC\_NAMESPACE\_KOL\_SEARCH**: *searchKol*= "searchKol"

Defined in: [packages/sdk/src/constants.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/constants.ts#L1)

___

### RPC\_NAMESPACE\_NAME\_SEARCH

• `Const` **RPC\_NAMESPACE\_NAME\_SEARCH**: *searchNameSearch*= "searchNameSearch"

Defined in: [packages/sdk/src/constants.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/constants.ts#L3)
