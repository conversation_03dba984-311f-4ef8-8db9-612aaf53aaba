[@h1nyc/search-sdk](../README.md) / util/elasticUtils

# Module: util/elasticUtils

## Table of contents

### Interfaces

- [EsBooleanQuery](../interfaces/util_elasticutils.esbooleanquery.md)

### Type aliases

- [EsBooleanClause](util_elasticutils.md#esbooleanclause)
- [EsQuery](util_elasticutils.md#esquery)

### Functions

- [ignoreScore](util_elasticutils.md#ignorescore)
- [matchAll](util_elasticutils.md#matchall)
- [minScore](util_elasticutils.md#minscore)
- [nestedFilter](util_elasticutils.md#nestedfilter)
- [total](util_elasticutils.md#total)
- [weightedScore](util_elasticutils.md#weightedscore)
- [worksDateFilter](util_elasticutils.md#worksdatefilter)

## Type aliases

### EsBooleanClause

Ƭ **EsBooleanClause**: *any*

Defined in: [packages/sdk/src/util/elasticUtils.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L1)

___

### EsQuery

Ƭ **EsQuery**: *any*

Defined in: [packages/sdk/src/util/elasticUtils.ts:10](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L10)

## Functions

### ignoreScore

▸ `Const`**ignoreScore**(`query`: *any*): *object*

This wraps an ElasticSearch query with a boolean query that will ignore the score.  This is done
for when we need to calculate a count for filtering purposes but we do not want the count to add
to the overall score of the query.

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |

**Returns:** *object*

Name | Type |
------ | ------ |
`function_score` | { `functions`: { `script_score`: { `script`: *string* = "0" }  }[] ; `query`: *any*  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:17](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L17)

___

### matchAll

▸ `Const`**matchAll**(`query`: *any*): *object*

This is a clause added to a min-max query that will ensure that correct minimum values and hit
counts are obtained.  It forces elastic search to visit all records including the ones that didn't
this particular (sub)query.

For example, in min/max queries, without this clause you might get a "min: 1, max: 275" but with this
clause you will get a "min: 0, max: 275".

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |

**Returns:** *object*

Name | Type |
------ | ------ |
`bool` | { `filter`: { `match_all`: {}  }[] ; `should`: *any*  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:196](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L196)

___

### minScore

▸ `Const`**minScore**(`query`: *any*, `minScoreValue`: *number*): *object*

This is used to wrap an existing scored query with a min_score restriction.

Since we often use scores to represent a count, this is often used as a "min count".

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |
`minScoreValue` | *number* |

**Returns:** *object*

Name | Type |
------ | ------ |
`function_score` | { `min_score`: *number* ; `query`: *any*  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:140](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L140)

___

### nestedFilter

▸ `Const`**nestedFilter**(`fullPath`: *string*, `filter`: [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[]): *object*

This is used to filter a nested object

#### Parameters:

Name | Type |
------ | ------ |
`fullPath` | *string* |
`filter` | [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[] |

**Returns:** *object*

Name | Type |
------ | ------ |
`nested` | { `path`: *string* ; `query`: { `bool`: { `filter`: [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[]  }  }  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:119](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L119)

___

### total

▸ `Const`**total**(`path`: *string*, `filter?`: [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[], `base?`: *boolean*, `additional?`: *any*, `containedInsideAMust?`: *boolean*): { `function_score`: { `field_value_factor`: { `field`: *string*  }  } ; `match_all`: *undefined*  } \| { `function_score`: *undefined* ; `match_all`: {}  } \| { `nested`: *any*  }

This performs a count of nested documents if the is path is not nested, otherwise it
will sum up the values.

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`path` | *string* | - |
`filter?` | [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[] | - |
`base?` | *boolean* | - |
`additional?` | *any* | - |
`containedInsideAMust` | *boolean* | false |

**Returns:** { `function_score`: { `field_value_factor`: { `field`: *string*  }  } ; `match_all`: *undefined*  } \| { `function_score`: *undefined* ; `match_all`: {}  } \| { `nested`: *any*  }

Defined in: [packages/sdk/src/util/elasticUtils.ts:73](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L73)

___

### weightedScore

▸ `Const`**weightedScore**(`query`: *any*, `path`: *string*, `base`: *undefined* \| *boolean*, `minScoreValue`: *number*, `maxScoreValue`: *number*, `weight`: *number*): *object*

This is used to wrap an existing score and adjust it according to the min, max, and
weight values passed in.

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |
`path` | *string* |
`base` | *undefined* \| *boolean* |
`minScoreValue` | *number* |
`maxScoreValue` | *number* |
`weight` | *number* |

**Returns:** *object*

Name | Type |
------ | ------ |
`function_score` | { `boost_mode`: *string* = "replace"; `functions`: { `script_score`: { `script`: { `params`: { `maximum`: *number* ; `minimum`: *number* ; `weight`: *number*  } ; `source`: *string*  }  }  }[] ; `query`: *any*  } \| { `boost_mode`: *string* = "replace"; `functions`: { `script_score`: { `script`: { `params`: { `maximum`: *number* ; `minimum`: *number* ; `weight`: *number*  } ; `source`: *string*  }  }  }[] ; `query`: *any*  } \| { `boost_mode`: *string* = "replace"; `functions`: { `script_score`: { `script`: { `params`: { `maximum`: *number* ; `minimum`: *number* ; `weight`: *number*  } ; `source`: *string*  }  }  }[] ; `query`: *any* ; `score_mode`: *string* = "sum" } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:151](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L151)

___

### worksDateFilter

▸ `Const`**worksDateFilter**(`path`: *string*, `minDateValue`: *number*, `maxDateValue?`: *number*): *object*

This creates a date filter for the given path and min/max date values.

If a date range is specified, then the documents must be in that range, or the date field must not exist at
all in the document.

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`path` | *string* | This is the path into the ES index.   |
`minDateValue` | *number* | This is in epoch milliseconds.   |
`maxDateValue?` | *number* | This is in epoch milliseconds.  If this is not specified, the search will just generate   a date search >= minDateValue.    |

**Returns:** *object*

Name | Type |
------ | ------ |
`bool` | { `should`: ({ `bool`: *undefined* ; `range`: {}  } \| { `bool`: { `must_not`: { `exists`: { `field`: *string*  }  }  } ; `range`: *undefined*  })[]  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:41](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L41)
