[@h1nyc/search-sdk](../README.md) / util/facetUtils

# Module: util/facetUtils

## Table of contents

### Functions

- [buildFacetAggregations](util_facetutils.md#buildfacetaggregations)

## Functions

### buildFacetAggregations

▸ `Const`**buildFacetAggregations**(`keyword`: *boolean*): *object*

#### Parameters:

Name | Type |
------ | ------ |
`keyword` | *boolean* |

**Returns:** *object*

Defined in: [packages/sdk/src/util/facetUtils.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/facetUtils.ts#L3)
