[@h1nyc/search-sdk](../README.md) / resources

# Module: resources

## Table of contents

### Enumerations

- [DiagnosesFields](../enums/resources.diagnosesfields.md)
- [ProceduresFields](../enums/resources.proceduresfields.md)

### Classes

- [ClaimsResourceClient](../classes/resources.claimsresourceclient.md)
- [NameSearchResourceClient](../classes/resources.namesearchresourceclient.md)

### Interfaces

- [ClaimsResource](../interfaces/resources.claimsresource.md)
- [KolSearchResource](../interfaces/resources.kolsearchresource.md)
- [NameSearchResource](../interfaces/resources.namesearchresource.md)

### Type aliases

- [DiagnosesSortOptions](resources.md#diagnosessortoptions)
- [ProceduresSortOptions](resources.md#proceduressortoptions)

## Type aliases

### DiagnosesSortOptions

Ƭ **DiagnosesSortOptions**: [*SortOptions*](../interfaces/interfaces_sortoptions.sortoptions.md)<[*DiagnosesFields*](../enums/resources_claimsresource.diagnosesfields.md)\>

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:26](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L26)

___

### ProceduresSortOptions

Ƭ **ProceduresSortOptions**: [*SortOptions*](../interfaces/interfaces_sortoptions.sortoptions.md)<[*ProceduresFields*](../enums/resources_claimsresource.proceduresfields.md)\>

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:27](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L27)
