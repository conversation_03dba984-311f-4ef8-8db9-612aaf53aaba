[@h1nyc/search-sdk](../README.md) / util/transformer

# Module: util/transformer

## Table of contents

### Functions

- [esNumberValToUnixTime](util_transformer.md#esnumbervaltounixtime)
- [transformResults](util_transformer.md#transformresults)

## Functions

### esNumberValToUnixTime

▸ `Const`**esNumberValToUnixTime**<T\>(`items`: T[]): *function*

#### Type parameters:

Name | Type |
------ | ------ |
`T` | *unknown* |

#### Parameters:

Name | Type |
------ | ------ |
`items` | T[] |

**Returns:** *function*

Defined in: [packages/sdk/src/util/transformer.ts:521](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/transformer.ts#L521)

___

### transformResults

▸ `Const`**transformResults**(`fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `searchResults`: [*SearchResultsRootObject*](../interfaces/api_response_responseinterfaces.searchresultsrootobject.md), `minMaxResults`: [*MinMaxResult*](../interfaces/api_response_responseinterfaces.minmaxresult.md)[], `claimsEnabled`: *boolean*, `referralsEnabled`: *boolean*): *Promise*<[*PersonSearchResponse*](interfaces_elasticdocuments.md#personsearchresponse)\>

#### Parameters:

Name | Type |
------ | ------ |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) |
`searchResults` | [*SearchResultsRootObject*](../interfaces/api_response_responseinterfaces.searchresultsrootobject.md) |
`minMaxResults` | [*MinMaxResult*](../interfaces/api_response_responseinterfaces.minmaxresult.md)[] |
`claimsEnabled` | *boolean* |
`referralsEnabled` | *boolean* |

**Returns:** *Promise*<[*PersonSearchResponse*](interfaces_elasticdocuments.md#personsearchresponse)\>

Defined in: [packages/sdk/src/util/transformer.ts:54](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/transformer.ts#L54)
