[@h1nyc/search-sdk](../README.md) / interfaces/KOLResultInterfaces

# Module: interfaces/KOLResultInterfaces

## Table of contents

### Enumerations

- [CardViewOpt](../enums/interfaces_kolresultinterfaces.cardviewopt.md)

### Interfaces

- [KOLDataRequest](../interfaces/interfaces_kolresultinterfaces.koldatarequest.md)
- [LiteProfile](../interfaces/interfaces_kolresultinterfaces.liteprofile.md)
- [LiteProfiles](../interfaces/interfaces_kolresultinterfaces.liteprofiles.md)
- [ResultCardAffiliation](../interfaces/interfaces_kolresultinterfaces.resultcardaffiliation.md)
- [ResultCardData](../interfaces/interfaces_kolresultinterfaces.resultcarddata.md)
- [TagInterface](../interfaces/interfaces_kolresultinterfaces.taginterface.md)
- [Works](../interfaces/interfaces_kolresultinterfaces.works.md)
- [WorksCount](../interfaces/interfaces_kolresultinterfaces.workscount.md)

### Type aliases

- [KOLType](interfaces_kolresultinterfaces.md#koltype)
- [ScoredResultCardData](interfaces_kolresultinterfaces.md#scoredresultcarddata)

### Variables

- [MAX\_BULK\_SELECT\_SIZE](interfaces_kolresultinterfaces.md#max_bulk_select_size)
- [SEARCH\_RESULT\_LIMIT](interfaces_kolresultinterfaces.md#search_result_limit)

## Type aliases

### KOLType

Ƭ **KOLType**: *Institutional Pedigree* \| *Industry Partner* \| *Rising Star* \| *Research Scholar*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L12)

___

### ScoredResultCardData

Ƭ **ScoredResultCardData**: [*PersonCard*](interfaces_elasticdocuments.md#personcard)

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:109](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L109)

## Variables

### MAX\_BULK\_SELECT\_SIZE

• `Const` **MAX\_BULK\_SELECT\_SIZE**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L8)

___

### SEARCH\_RESULT\_LIMIT

• `Const` **SEARCH\_RESULT\_LIMIT**: *9000*= 9000

The maximum number of search results to return

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L6)
