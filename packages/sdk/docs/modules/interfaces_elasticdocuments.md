[@h1nyc/search-sdk](../README.md) / interfaces/elasticDocuments

# Module: interfaces/elasticDocuments

## Table of contents

### Enumerations

- [GenericSearchEnum](../enums/interfaces_elasticdocuments.genericsearchenum.md)

### Interfaces

- [AggregationFilterCount](../interfaces/interfaces_elasticdocuments.aggregationfiltercount.md)
- [ElasticSearchDocuments](../interfaces/interfaces_elasticdocuments.elasticsearchdocuments.md)
- [GenericSearchDocument](../interfaces/interfaces_elasticdocuments.genericsearchdocument.md)
- [InjectedTagAssignment](../interfaces/interfaces_elasticdocuments.injectedtagassignment.md)
- [InjectedTagAssignmentUser](../interfaces/interfaces_elasticdocuments.injectedtagassignmentuser.md)
- [PeopleSearchDocument](../interfaces/interfaces_elasticdocuments.peoplesearchdocument.md)
- [PersonName](../interfaces/interfaces_elasticdocuments.personname.md)
- [TagDocument](../interfaces/interfaces_elasticdocuments.tagdocument.md)

### Type aliases

- [PersonCard](interfaces_elasticdocuments.md#personcard)
- [PersonSearchResponse](interfaces_elasticdocuments.md#personsearchresponse)

## Type aliases

### PersonCard

Ƭ **PersonCard**: [*Without*](interfaces_helpers.md#without)<[*ResultCardData*](../interfaces/interfaces_kolresultinterfaces.resultcarddata.md), *score* \| *trendHistory* \| *trendPresent*\> & { `scores`: [*ScoredDocumentData*](../interfaces/interfaces_documentscores.scoreddocumentdata.md) ; `totalWorks`: *number*  } & { `privateTagAssignments?`: [*InjectedTagAssignment*](../interfaces/interfaces_elasticdocuments.injectedtagassignment.md)[] ; `programmaticTags?`: { `__typename`: *ProgrammaticTag* ; `id`: *string* ; `name`: *string*  }[] ; `publicTagAssignments?`: [*InjectedTagAssignment*](../interfaces/interfaces_elasticdocuments.injectedtagassignment.md)[]  }

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:128](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L128)

___

### PersonSearchResponse

Ƭ **PersonSearchResponse**: [*PaginationResponse*](../interfaces/interfaces_pagination.paginationresponse.md)<[*PersonCard*](interfaces_elasticdocuments.md#personcard)\> & { `filterCounts`: [*AggregationFilterCount*](../interfaces/interfaces_elasticdocuments.aggregationfiltercount.md)[] ; `normalizedRange`: [*DocumentScoreRange*](../interfaces/interfaces_documentscores.documentscorerange.md) ; `ranges`: [*DocumentRanges*](../interfaces/interfaces_documentscores.documentranges.md)  }

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:153](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L153)
