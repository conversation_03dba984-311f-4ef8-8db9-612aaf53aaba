[@h1nyc/search-sdk](../README.md) / index

# Module: index

## Table of contents

### Enumerations

- [CardViewOpt](../enums/index.cardviewopt.md)
- [CustomSortWeightValues](../enums/index.customsortweightvalues.md)
- [DiagnosesFields](../enums/index.diagnosesfields.md)
- [FilterTypes](../enums/index.filtertypes.md)
- [FilterTypesEnum](../enums/index.filtertypesenum.md)
- [GenericSearchEnum](../enums/index.genericsearchenum.md)
- [IndexTypes](../enums/index.indextypes.md)
- [InputState](../enums/index.inputstate.md)
- [Metrics](../enums/index.metrics.md)
- [ProceduresFields](../enums/index.proceduresfields.md)
- [ScoreTypes](../enums/index.scoretypes.md)
- [SearchTypes](../enums/index.searchtypes.md)
- [SearchView](../enums/index.searchview.md)
- [SortDirection](../enums/index.sortdirection.md)

### Classes

- [ClaimsResourceClient](../classes/index.claimsresourceclient.md)
- [KolSearchResourceClient](../classes/index.kolsearchresourceclient.md)
- [NameSearchResourceClient](../classes/index.namesearchresourceclient.md)

### Interfaces

- [ActiveFilterBreakDown](../interfaces/index.activefilterbreakdown.md)
- [AggregationFilterCount](../interfaces/index.aggregationfiltercount.md)
- [Aggregations](../interfaces/index.aggregations.md)
- [CheckboxFilter](../interfaces/index.checkboxfilter.md)
- [ClaimsDiagnosis](../interfaces/index.claimsdiagnosis.md)
- [ClaimsFilter](../interfaces/index.claimsfilter.md)
- [ClaimsProcedure](../interfaces/index.claimsprocedure.md)
- [ClaimsResource](../interfaces/index.claimsresource.md)
- [DateRangeFilter](../interfaces/index.daterangefilter.md)
- [DocumentRanges](../interfaces/index.documentranges.md)
- [DocumentScoreRange](../interfaces/index.documentscorerange.md)
- [ElasticSearchDocuments](../interfaces/index.elasticsearchdocuments.md)
- [EsBooleanQuery](../interfaces/index.esbooleanquery.md)
- [EsIndexFacetMeta](../interfaces/index.esindexfacetmeta.md)
- [EsIndexFieldMeta](../interfaces/index.esindexfieldmeta.md)
- [EsIndexFilterMeta](../interfaces/index.esindexfiltermeta.md)
- [EsIndexMetricMeta](../interfaces/index.esindexmetricmeta.md)
- [FilterInterface](../interfaces/index.filterinterface.md)
- [FilterSearchInterface](../interfaces/index.filtersearchinterface.md)
- [FilterViewInterface](../interfaces/index.filterviewinterface.md)
- [FiltersPropsInterface](../interfaces/index.filterspropsinterface.md)
- [GenericFilterInterface](../interfaces/index.genericfilterinterface.md)
- [GenericSearchDocument](../interfaces/index.genericsearchdocument.md)
- [GenericSearchFilterInterface](../interfaces/index.genericsearchfilterinterface.md)
- [GenericSearchInterface](../interfaces/index.genericsearchinterface.md)
- [GenericSearchResultInterface](../interfaces/index.genericsearchresultinterface.md)
- [GetCollaboratorsInterface](../interfaces/index.getcollaboratorsinterface.md)
- [GetLiteProfileInterface](../interfaces/index.getliteprofileinterface.md)
- [Hits](../interfaces/index.hits.md)
- [InjectedTagAssignment](../interfaces/index.injectedtagassignment.md)
- [InjectedTagAssignmentUser](../interfaces/index.injectedtagassignmentuser.md)
- [KOLDataRequest](../interfaces/index.koldatarequest.md)
- [KOLDocumentCounts](../interfaces/index.koldocumentcounts.md)
- [KolSearchResource](../interfaces/index.kolsearchresource.md)
- [LiteProfile](../interfaces/index.liteprofile.md)
- [LiteProfiles](../interfaces/index.liteprofiles.md)
- [MaxCountOrMinCount](../interfaces/index.maxcountormincount.md)
- [MetricQueryPreMap](../interfaces/index.metricquerypremap.md)
- [MetricQueryPreNestedPlanerMinMax](../interfaces/index.metricqueryprenestedplanerminmax.md)
- [MinMaxHits](../interfaces/index.minmaxhits.md)
- [MinMaxResult](../interfaces/index.minmaxresult.md)
- [NameSearchResource](../interfaces/index.namesearchresource.md)
- [NameSuggestFilterSearchInterface](../interfaces/index.namesuggestfiltersearchinterface.md)
- [NumberFilter](../interfaces/index.numberfilter.md)
- [Pagination](../interfaces/index.pagination.md)
- [PaginationOptions](../interfaces/index.paginationoptions.md)
- [PaginationResponse](../interfaces/index.paginationresponse.md)
- [PeopleSearchDocument](../interfaces/index.peoplesearchdocument.md)
- [PersonName](../interfaces/index.personname.md)
- [PersonsDocumentSummarySearchInterface](../interfaces/index.personsdocumentsummarysearchinterface.md)
- [ResultCardAffiliation](../interfaces/index.resultcardaffiliation.md)
- [ResultCardData](../interfaces/index.resultcarddata.md)
- [RootSearchObject](../interfaces/index.rootsearchobject.md)
- [SavedSearchBy](../interfaces/index.savedsearchby.md)
- [SavedSearchByConstructor](../interfaces/index.savedsearchbyconstructor.md)
- [SavedSearchFilterValues](../interfaces/index.savedsearchfiltervalues.md)
- [ScoredDocumentData](../interfaces/index.scoreddocumentdata.md)
- [ScoredDocumentFilterSearchInterface](../interfaces/index.scoreddocumentfiltersearchinterface.md)
- [ScoredDocumentResult](../interfaces/index.scoreddocumentresult.md)
- [ScoredDocumentResults](../interfaces/index.scoreddocumentresults.md)
- [SearchFilterBuilderInterface](../interfaces/index.searchfilterbuilderinterface.md)
- [SearchFilterOptions](../interfaces/index.searchfilteroptions.md)
- [SearchResultsCongress](../interfaces/index.searchresultscongress.md)
- [SearchResultsDiagnoses](../interfaces/index.searchresultsdiagnoses.md)
- [SearchResultsFields](../interfaces/index.searchresultsfields.md)
- [SearchResultsHits](../interfaces/index.searchresultshits.md)
- [SearchResultsHitsItem](../interfaces/index.searchresultshitsitem.md)
- [SearchResultsInner\_hits](../interfaces/index.searchresultsinner_hits.md)
- [SearchResultsLocationsItem](../interfaces/index.searchresultslocationsitem.md)
- [SearchResultsPayment\_amounts](../interfaces/index.searchresultspayment_amounts.md)
- [SearchResultsPayments](../interfaces/index.searchresultspayments.md)
- [SearchResultsProcedures](../interfaces/index.searchresultsprocedures.md)
- [SearchResultsPublication\_citations](../interfaces/index.searchresultspublication_citations.md)
- [SearchResultsPublication\_social\_media](../interfaces/index.searchresultspublication_social_media.md)
- [SearchResultsPublications](../interfaces/index.searchresultspublications.md)
- [SearchResultsReferralsReceived](../interfaces/index.searchresultsreferralsreceived.md)
- [SearchResultsReferralsSent](../interfaces/index.searchresultsreferralssent.md)
- [SearchResultsRootObject](../interfaces/index.searchresultsrootobject.md)
- [SearchResultsTrials](../interfaces/index.searchresultstrials.md)
- [SearchResults\_nested](../interfaces/index.searchresults_nested.md)
- [SearchResults\_shards](../interfaces/index.searchresults_shards.md)
- [SearchResults\_source](../interfaces/index.searchresults_source.md)
- [Shards](../interfaces/index.shards.md)
- [SortOptions](../interfaces/index.sortoptions.md)
- [TagDocument](../interfaces/index.tagdocument.md)
- [TagInterface](../interfaces/index.taginterface.md)
- [TextFilter](../interfaces/index.textfilter.md)
- [WeightedSortBy](../interfaces/index.weightedsortby.md)
- [Works](../interfaces/index.works.md)
- [WorksCount](../interfaces/index.workscount.md)
- [YearRangeFilter](../interfaces/index.yearrangefilter.md)

### Type aliases

- [DiagnosesSortOptions](index.md#diagnosessortoptions)
- [EsBooleanClause](index.md#esbooleanclause)
- [EsQuery](index.md#esquery)
- [KOLType](index.md#koltype)
- [NamedSortBy](index.md#namedsortby)
- [PersonCard](index.md#personcard)
- [PersonSearchResponse](index.md#personsearchresponse)
- [ProceduresSortOptions](index.md#proceduressortoptions)
- [ScoredResultCardData](index.md#scoredresultcarddata)
- [SearchFilterValue](index.md#searchfiltervalue)
- [Without](index.md#without)

### Variables

- [DefaultScoreWeights](index.md#defaultscoreweights)
- [MAX\_BULK\_SELECT\_SIZE](index.md#max_bulk_select_size)
- [RPC\_NAMESPACE\_CLAIMS\_SEARCH](index.md#rpc_namespace_claims_search)
- [RPC\_NAMESPACE\_KOL\_SEARCH](index.md#rpc_namespace_kol_search)
- [RPC\_NAMESPACE\_NAME\_SEARCH](index.md#rpc_namespace_name_search)
- [SEARCH\_RESULT\_LIMIT](index.md#search_result_limit)
- [nonInitiatingFilters](index.md#noninitiatingfilters)
- [peopleFacets](index.md#peoplefacets)
- [peopleFields](index.md#peoplefields)

### Functions

- [buildFacetAggregations](index.md#buildfacetaggregations)
- [buildYearPathRangeFilter](index.md#buildyearpathrangefilter)
- [esNumberValToUnixTime](index.md#esnumbervaltounixtime)
- [fieldsToMultiMatches](index.md#fieldstomultimatches)
- [fsiMapped](index.md#fsimapped)
- [fsiMapping](index.md#fsimapping)
- [fsiToFiltersArray](index.md#fsitofiltersarray)
- [getFilterOptions](index.md#getfilteroptions)
- [getSearchQueryAggTarget](index.md#getsearchqueryaggtarget)
- [getSearchQueryTarget](index.md#getsearchquerytarget)
- [getSortWeight](index.md#getsortweight)
- [hasTagsInSearchFilters](index.md#hastagsinsearchfilters)
- [hasValidFiltersForSearch](index.md#hasvalidfiltersforsearch)
- [ignoreScore](index.md#ignorescore)
- [isExcludedKeyWord](index.md#isexcludedkeyword)
- [isFieldSum](index.md#isfieldsum)
- [isGenericSearchInterface](index.md#isgenericsearchinterface)
- [matchAll](index.md#matchall)
- [minScore](index.md#minscore)
- [nestedFilter](index.md#nestedfilter)
- [term](index.md#term)
- [total](index.md#total)
- [transformFilters](index.md#transformfilters)
- [transformResults](index.md#transformresults)
- [weightedScore](index.md#weightedscore)
- [worksDateFilter](index.md#worksdatefilter)

## Type aliases

### DiagnosesSortOptions

Ƭ **DiagnosesSortOptions**: [*SortOptions*](../interfaces/interfaces_sortoptions.sortoptions.md)<[*DiagnosesFields*](../enums/resources_claimsresource.diagnosesfields.md)\>

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:26](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L26)

___

### EsBooleanClause

Ƭ **EsBooleanClause**: *any*

Defined in: [packages/sdk/src/util/elasticUtils.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L1)

___

### EsQuery

Ƭ **EsQuery**: *any*

Defined in: [packages/sdk/src/util/elasticUtils.ts:10](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L10)

___

### KOLType

Ƭ **KOLType**: *Institutional Pedigree* \| *Industry Partner* \| *Rising Star* \| *Research Scholar*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L12)

___

### NamedSortBy

Ƭ **NamedSortBy**: *diagnoses* \| *procedures* \| *publications* \| *trials* \| *citations* \| *socialMediaMentions* \| *grants* \| *payments* \| *congresses* \| *collaborators* \| *referralsReceived* \| *referralsSent*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:155](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L155)

___

### PersonCard

Ƭ **PersonCard**: [*Without*](interfaces_helpers.md#without)<[*ResultCardData*](../interfaces/interfaces_kolresultinterfaces.resultcarddata.md), *score* \| *trendHistory* \| *trendPresent*\> & { `scores`: [*ScoredDocumentData*](../interfaces/interfaces_documentscores.scoreddocumentdata.md) ; `totalWorks`: *number*  } & { `privateTagAssignments?`: [*InjectedTagAssignment*](../interfaces/interfaces_elasticdocuments.injectedtagassignment.md)[] ; `programmaticTags?`: { `__typename`: *ProgrammaticTag* ; `id`: *string* ; `name`: *string*  }[] ; `publicTagAssignments?`: [*InjectedTagAssignment*](../interfaces/interfaces_elasticdocuments.injectedtagassignment.md)[]  }

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:128](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L128)

___

### PersonSearchResponse

Ƭ **PersonSearchResponse**: [*PaginationResponse*](../interfaces/interfaces_pagination.paginationresponse.md)<[*PersonCard*](interfaces_elasticdocuments.md#personcard)\> & { `filterCounts`: [*AggregationFilterCount*](../interfaces/interfaces_elasticdocuments.aggregationfiltercount.md)[] ; `normalizedRange`: [*DocumentScoreRange*](../interfaces/interfaces_documentscores.documentscorerange.md) ; `ranges`: [*DocumentRanges*](../interfaces/interfaces_documentscores.documentranges.md)  }

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:153](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L153)

___

### ProceduresSortOptions

Ƭ **ProceduresSortOptions**: [*SortOptions*](../interfaces/interfaces_sortoptions.sortoptions.md)<[*ProceduresFields*](../enums/resources_claimsresource.proceduresfields.md)\>

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:27](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L27)

___

### ScoredResultCardData

Ƭ **ScoredResultCardData**: [*PersonCard*](interfaces_elasticdocuments.md#personcard)

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:109](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L109)

___

### SearchFilterValue

Ƭ **SearchFilterValue**: { [name: string]: [*SearchFilterValue*](interfaces_filterinterfaces.md#searchfiltervalue);  } \| *string*[] \| *number* \| *number*[] \| *null* \| *undefined*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:452](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L452)

___

### Without

Ƭ **Without**<T, K\>: *Pick*<T, *Exclude*<keyof T, K\>\>

#### Type parameters:

Name | Type |
------ | ------ |
`T` | - |
`K` | keyof T |

Defined in: [packages/sdk/src/interfaces/helpers.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/helpers.ts#L1)

## Variables

### DefaultScoreWeights

• `Const` **DefaultScoreWeights**: [*WeightedSortBy*](../interfaces/interfaces_filterinterfaces.weightedsortby.md)

The weights of document types

**`todo:`** grants

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:209](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L209)

___

### MAX\_BULK\_SELECT\_SIZE

• `Const` **MAX\_BULK\_SELECT\_SIZE**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L8)

___

### RPC\_NAMESPACE\_CLAIMS\_SEARCH

• `Const` **RPC\_NAMESPACE\_CLAIMS\_SEARCH**: *searchClaims*= "searchClaims"

Defined in: [packages/sdk/src/constants.ts:2](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/constants.ts#L2)

___

### RPC\_NAMESPACE\_KOL\_SEARCH

• `Const` **RPC\_NAMESPACE\_KOL\_SEARCH**: *searchKol*= "searchKol"

Defined in: [packages/sdk/src/constants.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/constants.ts#L1)

___

### RPC\_NAMESPACE\_NAME\_SEARCH

• `Const` **RPC\_NAMESPACE\_NAME\_SEARCH**: *searchNameSearch*= "searchNameSearch"

Defined in: [packages/sdk/src/constants.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/constants.ts#L3)

___

### SEARCH\_RESULT\_LIMIT

• `Const` **SEARCH\_RESULT\_LIMIT**: *9000*= 9000

The maximum number of search results to return

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L6)

___

### nonInitiatingFilters

• `Const` **nonInitiatingFilters**: *string*[]

List of filter keys that when exist as the only filters will not execute a search.
Keys must match what is in `SavedSearchFilterValues` in path notation (foo.bar.baz)

Defined in: [packages/sdk/src/util/filterUtils.ts:14](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L14)

___

### peopleFacets

• `Const` **peopleFacets**: [*EsIndexFacetMeta*](../interfaces/indices_peopleindex.esindexfacetmeta.md)[]

Defined in: [packages/sdk/src/indices/peopleIndex.ts:390](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L390)

___

### peopleFields

• `Const` **peopleFields**: [*EsIndexFieldMeta*](../interfaces/indices_peopleindex.esindexfieldmeta.md)[]

Defined in: [packages/sdk/src/indices/peopleIndex.ts:116](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L116)

## Functions

### buildFacetAggregations

▸ `Const`**buildFacetAggregations**(`keyword`: *boolean*): *object*

#### Parameters:

Name | Type |
------ | ------ |
`keyword` | *boolean* |

**Returns:** *object*

Defined in: [packages/sdk/src/util/facetUtils.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/facetUtils.ts#L3)

___

### buildYearPathRangeFilter

▸ `Const`**buildYearPathRangeFilter**(`paths`: *string*[], `minDate?`: *number*, `maxDate?`: *number*): *any*[]

#### Parameters:

Name | Type |
------ | ------ |
`paths` | *string*[] |
`minDate?` | *number* |
`maxDate?` | *number* |

**Returns:** *any*[]

Defined in: [packages/sdk/src/flow/quieres.ts:21](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/quieres.ts#L21)

___

### esNumberValToUnixTime

▸ `Const`**esNumberValToUnixTime**<T\>(`items`: T[]): *function*

#### Type parameters:

Name | Type |
------ | ------ |
`T` | *unknown* |

#### Parameters:

Name | Type |
------ | ------ |
`items` | T[] |

**Returns:** *function*

Defined in: [packages/sdk/src/util/transformer.ts:521](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/transformer.ts#L521)

___

### fieldsToMultiMatches

▸ `Const`**fieldsToMultiMatches**(`querys`: *string*[], `fields`: *string*[]): { `multi_match`: { `fields`: *string*[] ; `operator`: *string* = "and"; `query`: *string* ; `type`: *string* = "phrase" }  }[]

#### Parameters:

Name | Type |
------ | ------ |
`querys` | *string*[] |
`fields` | *string*[] |

**Returns:** { `multi_match`: { `fields`: *string*[] ; `operator`: *string* = "and"; `query`: *string* ; `type`: *string* = "phrase" }  }[]

Defined in: [packages/sdk/src/flow/quieres.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/quieres.ts#L9)

___

### fsiMapped

▸ `Const`**fsiMapped**(`isExcel?`: *boolean*, `hasClaims?`: *boolean*, `hasProfile?`: *boolean*): *object*

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`isExcel` | *boolean* | false |
`hasClaims` | *boolean* | false |
`hasProfile` | *boolean* | false |

**Returns:** *object*

Defined in: [packages/sdk/src/flow/mapping.ts:350](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/mapping.ts#L350)

___

### fsiMapping

▸ `Const`**fsiMapping**(`isExcel?`: *boolean*, `hasClaims?`: *boolean*, `hasProfile?`: *boolean*): [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md)[]

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`isExcel` | *boolean* | false |
`hasClaims` | *boolean* | false |
`hasProfile` | *boolean* | false |

**Returns:** [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md)[]

Defined in: [packages/sdk/src/flow/mapping.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/mapping.ts#L3)

___

### fsiToFiltersArray

▸ `Const`**fsiToFiltersArray**(`hasClaims`: *boolean*, `hasProfile`: *boolean*, `fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `tags?`: *string*[], `isExcel`: *boolean*, `dateOverRide?`: { `max`: *number* ; `min`: *number*  }): [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)[]

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`hasClaims` | *boolean* | - |
`hasProfile` | *boolean* | - |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) | - |
`tags` | *string*[] | ... |
`isExcel` | *boolean* | - |
`dateOverRide?` | { `max`: *number* ; `min`: *number*  } | - |

**Returns:** [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)[]

Defined in: [packages/sdk/src/flow/merge.ts:23](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/merge.ts#L23)

___

### getFilterOptions

▸ `Const`**getFilterOptions**(`fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `mqpm`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md), `tags?`: *string*[]): *object*

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) | - |
`mqpm` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) | - |
`tags` | *string*[] | ... |

**Returns:** *object*

Name | Type |
------ | ------ |
`filters` | { `bool`: { `must`: *any*[]  }  }[] |
`realCount` | *number* |

Defined in: [packages/sdk/src/flow/merge.ts:72](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/merge.ts#L72)

___

### getSearchQueryAggTarget

▸ `Const`**getSearchQueryAggTarget**(`metric`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)): [*collaboratorCount*](../enums/flow_types.metrics.md#collaboratorcount) \| *_score*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md) |

**Returns:** [*collaboratorCount*](../enums/flow_types.metrics.md#collaboratorcount) \| *_score*

Defined in: [packages/sdk/src/flow/identities.ts:30](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L30)

___

### getSearchQueryTarget

▸ `Const`**getSearchQueryTarget**(`metric`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)): *field* \| *script*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md) |

**Returns:** *field* \| *script*

Defined in: [packages/sdk/src/flow/identities.ts:24](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L24)

___

### getSortWeight

▸ `Const`**getSortWeight**(`sortBy?`: [*WeightedSortBy*](../interfaces/interfaces_filterinterfaces.weightedsortby.md), `sortPath?`: *string*): *number*

#### Parameters:

Name | Type |
------ | ------ |
`sortBy?` | [*WeightedSortBy*](../interfaces/interfaces_filterinterfaces.weightedsortby.md) |
`sortPath?` | *string* |

**Returns:** *number*

Defined in: [packages/sdk/src/util/filterUtils.ts:190](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L190)

___

### hasTagsInSearchFilters

▸ `Const`**hasTagsInSearchFilters**(`filters`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md)): *boolean*

Check whether the filters contains tag IDs

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`filters` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) | Search filters    |

**Returns:** *boolean*

Defined in: [packages/sdk/src/util/filterUtils.ts:53](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L53)

___

### hasValidFiltersForSearch

▸ `Const`**hasValidFiltersForSearch**(`filters`: [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md)): *boolean*

Check that the filter values are valid.

This checks that there are initiating filter values in the list of filters.

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`filters` | [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md) | Filter values    |

**Returns:** *boolean*

Defined in: [packages/sdk/src/util/filterUtils.ts:23](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L23)

___

### ignoreScore

▸ `Const`**ignoreScore**(`query`: *any*): *object*

This wraps an ElasticSearch query with a boolean query that will ignore the score.  This is done
for when we need to calculate a count for filtering purposes but we do not want the count to add
to the overall score of the query.

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |

**Returns:** *object*

Name | Type |
------ | ------ |
`function_score` | { `functions`: { `script_score`: { `script`: *string* = "0" }  }[] ; `query`: *any*  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:17](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L17)

___

### isExcludedKeyWord

▸ `Const`**isExcludedKeyWord**(`metric`: [*Metrics*](../enums/flow_types.metrics.md)): *boolean*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*Metrics*](../enums/flow_types.metrics.md) |

**Returns:** *boolean*

Defined in: [packages/sdk/src/flow/identities.ts:18](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L18)

___

### isFieldSum

▸ `Const`**isFieldSum**(`metric`: [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md)): *boolean*

#### Parameters:

Name | Type |
------ | ------ |
`metric` | [*MetricQueryPreMap*](../interfaces/flow_types.metricquerypremap.md) \| [*MetricQueryPreNestedPlanerMinMax*](../interfaces/flow_types.metricqueryprenestedplanerminmax.md) |

**Returns:** *boolean*

Defined in: [packages/sdk/src/flow/identities.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/identities.ts#L8)

___

### isGenericSearchInterface

▸ **isGenericSearchInterface**(`obj`: *any*): obj is GenericSearchInterface

#### Parameters:

Name | Type |
------ | ------ |
`obj` | *any* |

**Returns:** obj is GenericSearchInterface

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:288](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L288)

___

### matchAll

▸ `Const`**matchAll**(`query`: *any*): *object*

This is a clause added to a min-max query that will ensure that correct minimum values and hit
counts are obtained.  It forces elastic search to visit all records including the ones that didn't
this particular (sub)query.

For example, in min/max queries, without this clause you might get a "min: 1, max: 275" but with this
clause you will get a "min: 0, max: 275".

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |

**Returns:** *object*

Name | Type |
------ | ------ |
`bool` | { `filter`: { `match_all`: {}  }[] ; `should`: *any*  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:196](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L196)

___

### minScore

▸ `Const`**minScore**(`query`: *any*, `minScoreValue`: *number*): *object*

This is used to wrap an existing scored query with a min_score restriction.

Since we often use scores to represent a count, this is often used as a "min count".

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |
`minScoreValue` | *number* |

**Returns:** *object*

Name | Type |
------ | ------ |
`function_score` | { `min_score`: *number* ; `query`: *any*  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:140](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L140)

___

### nestedFilter

▸ `Const`**nestedFilter**(`fullPath`: *string*, `filter`: [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[]): *object*

This is used to filter a nested object

#### Parameters:

Name | Type |
------ | ------ |
`fullPath` | *string* |
`filter` | [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[] |

**Returns:** *object*

Name | Type |
------ | ------ |
`nested` | { `path`: *string* ; `query`: { `bool`: { `filter`: [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[]  }  }  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:119](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L119)

___

### term

▸ `Const`**term**(`feild`: *string*, `query`: *string*): *object*

#### Parameters:

Name | Type |
------ | ------ |
`feild` | *string* |
`query` | *string* |

**Returns:** *object*

Name | Type |
------ | ------ |
`term` | {} |

Defined in: [packages/sdk/src/flow/quieres.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/quieres.ts#L1)

___

### total

▸ `Const`**total**(`path`: *string*, `filter?`: [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[], `base?`: *boolean*, `additional?`: *any*, `containedInsideAMust?`: *boolean*): { `function_score`: { `field_value_factor`: { `field`: *string*  }  } ; `match_all`: *undefined*  } \| { `function_score`: *undefined* ; `match_all`: {}  } \| { `nested`: *any*  }

This performs a count of nested documents if the is path is not nested, otherwise it
will sum up the values.

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`path` | *string* | - |
`filter?` | [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[] | - |
`base?` | *boolean* | - |
`additional?` | *any* | - |
`containedInsideAMust` | *boolean* | false |

**Returns:** { `function_score`: { `field_value_factor`: { `field`: *string*  }  } ; `match_all`: *undefined*  } \| { `function_score`: *undefined* ; `match_all`: {}  } \| { `nested`: *any*  }

Defined in: [packages/sdk/src/util/elasticUtils.ts:73](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L73)

___

### transformFilters

▸ `Const`**transformFilters**(`fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `profileEnabled`: *boolean*, `claimsEnabled`: *boolean*, `referralsEnabled`: *boolean*): [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md)

Transforms the value of the FilterSearchInterface into SavedSearchFilterValues

Hopefully, this will not be needed anymore when we change the front end to
send the SavedSearchFilterValues to the API directly.

#### Parameters:

Name | Type |
------ | ------ |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) |
`profileEnabled` | *boolean* |
`claimsEnabled` | *boolean* |
`referralsEnabled` | *boolean* |

**Returns:** [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md)

Defined in: [packages/sdk/src/util/filterUtils.ts:78](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L78)

___

### transformResults

▸ `Const`**transformResults**(`fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `searchResults`: [*SearchResultsRootObject*](../interfaces/api_response_responseinterfaces.searchresultsrootobject.md), `minMaxResults`: [*MinMaxResult*](../interfaces/api_response_responseinterfaces.minmaxresult.md)[], `claimsEnabled`: *boolean*, `referralsEnabled`: *boolean*): *Promise*<[*PersonSearchResponse*](interfaces_elasticdocuments.md#personsearchresponse)\>

#### Parameters:

Name | Type |
------ | ------ |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) |
`searchResults` | [*SearchResultsRootObject*](../interfaces/api_response_responseinterfaces.searchresultsrootobject.md) |
`minMaxResults` | [*MinMaxResult*](../interfaces/api_response_responseinterfaces.minmaxresult.md)[] |
`claimsEnabled` | *boolean* |
`referralsEnabled` | *boolean* |

**Returns:** *Promise*<[*PersonSearchResponse*](interfaces_elasticdocuments.md#personsearchresponse)\>

Defined in: [packages/sdk/src/util/transformer.ts:54](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/transformer.ts#L54)

___

### weightedScore

▸ `Const`**weightedScore**(`query`: *any*, `path`: *string*, `base`: *undefined* \| *boolean*, `minScoreValue`: *number*, `maxScoreValue`: *number*, `weight`: *number*): *object*

This is used to wrap an existing score and adjust it according to the min, max, and
weight values passed in.

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |
`path` | *string* |
`base` | *undefined* \| *boolean* |
`minScoreValue` | *number* |
`maxScoreValue` | *number* |
`weight` | *number* |

**Returns:** *object*

Name | Type |
------ | ------ |
`function_score` | { `boost_mode`: *string* = "replace"; `functions`: { `script_score`: { `script`: { `params`: { `maximum`: *number* ; `minimum`: *number* ; `weight`: *number*  } ; `source`: *string*  }  }  }[] ; `query`: *any*  } \| { `boost_mode`: *string* = "replace"; `functions`: { `script_score`: { `script`: { `params`: { `maximum`: *number* ; `minimum`: *number* ; `weight`: *number*  } ; `source`: *string*  }  }  }[] ; `query`: *any*  } \| { `boost_mode`: *string* = "replace"; `functions`: { `script_score`: { `script`: { `params`: { `maximum`: *number* ; `minimum`: *number* ; `weight`: *number*  } ; `source`: *string*  }  }  }[] ; `query`: *any* ; `score_mode`: *string* = "sum" } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:151](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L151)

___

### worksDateFilter

▸ `Const`**worksDateFilter**(`path`: *string*, `minDateValue`: *number*, `maxDateValue?`: *number*): *object*

This creates a date filter for the given path and min/max date values.

If a date range is specified, then the documents must be in that range, or the date field must not exist at
all in the document.

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`path` | *string* | This is the path into the ES index.   |
`minDateValue` | *number* | This is in epoch milliseconds.   |
`maxDateValue?` | *number* | This is in epoch milliseconds.  If this is not specified, the search will just generate   a date search >= minDateValue.    |

**Returns:** *object*

Name | Type |
------ | ------ |
`bool` | { `should`: ({ `bool`: *undefined* ; `range`: {}  } \| { `bool`: { `must_not`: { `exists`: { `field`: *string*  }  }  } ; `range`: *undefined*  })[]  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:41](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L41)
