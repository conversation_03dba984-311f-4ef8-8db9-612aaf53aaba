[@h1nyc/search-sdk](../README.md) / indices

# Module: indices

## Table of contents

### Interfaces

- [EsIndexFacetMeta](../interfaces/indices.esindexfacetmeta.md)
- [EsIndexFieldMeta](../interfaces/indices.esindexfieldmeta.md)
- [EsIndexFilterMeta](../interfaces/indices.esindexfiltermeta.md)
- [EsIndexMetricMeta](../interfaces/indices.esindexmetricmeta.md)

### Variables

- [peopleFacets](indices.md#peoplefacets)
- [peopleFields](indices.md#peoplefields)

## Variables

### peopleFacets

• `Const` **peopleFacets**: [*EsIndexFacetMeta*](../interfaces/indices_peopleindex.esindexfacetmeta.md)[]

Defined in: [packages/sdk/src/indices/peopleIndex.ts:390](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L390)

___

### peopleFields

• `Const` **peopleFields**: [*EsIndexFieldMeta*](../interfaces/indices_peopleindex.esindexfieldmeta.md)[]

Defined in: [packages/sdk/src/indices/peopleIndex.ts:116](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L116)
