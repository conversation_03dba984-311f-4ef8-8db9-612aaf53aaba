[@h1nyc/search-sdk](../README.md) / interfaces/filterInterfaces

# Module: interfaces/filterInterfaces

## Table of contents

### Enumerations

- [CustomSortWeightValues](../enums/interfaces_filterinterfaces.customsortweightvalues.md)
- [FilterTypes](../enums/interfaces_filterinterfaces.filtertypes.md)
- [FilterTypesEnum](../enums/interfaces_filterinterfaces.filtertypesenum.md)
- [IndexTypes](../enums/interfaces_filterinterfaces.indextypes.md)
- [InputState](../enums/interfaces_filterinterfaces.inputstate.md)
- [SearchTypes](../enums/interfaces_filterinterfaces.searchtypes.md)
- [SearchView](../enums/interfaces_filterinterfaces.searchview.md)

### Interfaces

- [ActiveFilterBreakDown](../interfaces/interfaces_filterinterfaces.activefilterbreakdown.md)
- [CheckboxFilter](../interfaces/interfaces_filterinterfaces.checkboxfilter.md)
- [ClaimsFilter](../interfaces/interfaces_filterinterfaces.claimsfilter.md)
- [DateRangeFilter](../interfaces/interfaces_filterinterfaces.daterangefilter.md)
- [FilterInterface](../interfaces/interfaces_filterinterfaces.filterinterface.md)
- [FilterSearchInterface](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md)
- [FilterViewInterface](../interfaces/interfaces_filterinterfaces.filterviewinterface.md)
- [FiltersPropsInterface](../interfaces/interfaces_filterinterfaces.filterspropsinterface.md)
- [GenericFilterInterface](../interfaces/interfaces_filterinterfaces.genericfilterinterface.md)
- [GenericSearchFilterInterface](../interfaces/interfaces_filterinterfaces.genericsearchfilterinterface.md)
- [GenericSearchInterface](../interfaces/interfaces_filterinterfaces.genericsearchinterface.md)
- [GenericSearchResultInterface](../interfaces/interfaces_filterinterfaces.genericsearchresultinterface.md)
- [GetCollaboratorsInterface](../interfaces/interfaces_filterinterfaces.getcollaboratorsinterface.md)
- [GetLiteProfileInterface](../interfaces/interfaces_filterinterfaces.getliteprofileinterface.md)
- [NameSuggestFilterSearchInterface](../interfaces/interfaces_filterinterfaces.namesuggestfiltersearchinterface.md)
- [NumberFilter](../interfaces/interfaces_filterinterfaces.numberfilter.md)
- [PersonsDocumentSummarySearchInterface](../interfaces/interfaces_filterinterfaces.personsdocumentsummarysearchinterface.md)
- [SavedSearchBy](../interfaces/interfaces_filterinterfaces.savedsearchby.md)
- [SavedSearchByConstructor](../interfaces/interfaces_filterinterfaces.savedsearchbyconstructor.md)
- [SavedSearchFilterValues](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md)
- [SearchFilterBuilderInterface](../interfaces/interfaces_filterinterfaces.searchfilterbuilderinterface.md)
- [SearchFilterOptions](../interfaces/interfaces_filterinterfaces.searchfilteroptions.md)
- [TextFilter](../interfaces/interfaces_filterinterfaces.textfilter.md)
- [WeightedSortBy](../interfaces/interfaces_filterinterfaces.weightedsortby.md)
- [YearRangeFilter](../interfaces/interfaces_filterinterfaces.yearrangefilter.md)

### Type aliases

- [NamedSortBy](interfaces_filterinterfaces.md#namedsortby)
- [SearchFilterValue](interfaces_filterinterfaces.md#searchfiltervalue)

### Variables

- [DefaultScoreWeights](interfaces_filterinterfaces.md#defaultscoreweights)

### Functions

- [isGenericSearchInterface](interfaces_filterinterfaces.md#isgenericsearchinterface)

## Type aliases

### NamedSortBy

Ƭ **NamedSortBy**: *diagnoses* \| *procedures* \| *publications* \| *trials* \| *citations* \| *socialMediaMentions* \| *grants* \| *payments* \| *congresses* \| *collaborators* \| *referralsReceived* \| *referralsSent*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:155](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L155)

___

### SearchFilterValue

Ƭ **SearchFilterValue**: { [name: string]: [*SearchFilterValue*](interfaces_filterinterfaces.md#searchfiltervalue);  } \| *string*[] \| *number* \| *number*[] \| *null* \| *undefined*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:452](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L452)

## Variables

### DefaultScoreWeights

• `Const` **DefaultScoreWeights**: [*WeightedSortBy*](../interfaces/interfaces_filterinterfaces.weightedsortby.md)

The weights of document types

**`todo:`** grants

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:209](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L209)

## Functions

### isGenericSearchInterface

▸ **isGenericSearchInterface**(`obj`: *any*): obj is GenericSearchInterface

#### Parameters:

Name | Type |
------ | ------ |
`obj` | *any* |

**Returns:** obj is GenericSearchInterface

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:288](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L288)
