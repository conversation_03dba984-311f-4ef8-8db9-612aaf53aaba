[@h1nyc/search-sdk](../README.md) / flow/quieres

# Module: flow/quieres

## Table of contents

### Functions

- [buildYearPathRangeFilter](flow_quieres.md#buildyearpathrangefilter)
- [fieldsToMultiMatches](flow_quieres.md#fieldstomultimatches)
- [term](flow_quieres.md#term)

## Functions

### buildYearPathRangeFilter

▸ `Const`**buildYearPathRangeFilter**(`paths`: *string*[], `minDate?`: *number*, `maxDate?`: *number*): *any*[]

#### Parameters:

Name | Type |
------ | ------ |
`paths` | *string*[] |
`minDate?` | *number* |
`maxDate?` | *number* |

**Returns:** *any*[]

Defined in: [packages/sdk/src/flow/quieres.ts:21](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/quieres.ts#L21)

___

### fieldsToMultiMatches

▸ `Const`**fieldsToMultiMatches**(`querys`: *string*[], `fields`: *string*[]): { `multi_match`: { `fields`: *string*[] ; `operator`: *string* = "and"; `query`: *string* ; `type`: *string* = "phrase" }  }[]

#### Parameters:

Name | Type |
------ | ------ |
`querys` | *string*[] |
`fields` | *string*[] |

**Returns:** { `multi_match`: { `fields`: *string*[] ; `operator`: *string* = "and"; `query`: *string* ; `type`: *string* = "phrase" }  }[]

Defined in: [packages/sdk/src/flow/quieres.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/quieres.ts#L9)

___

### term

▸ `Const`**term**(`feild`: *string*, `query`: *string*): *object*

#### Parameters:

Name | Type |
------ | ------ |
`feild` | *string* |
`query` | *string* |

**Returns:** *object*

Name | Type |
------ | ------ |
`term` | {} |

Defined in: [packages/sdk/src/flow/quieres.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/quieres.ts#L1)
