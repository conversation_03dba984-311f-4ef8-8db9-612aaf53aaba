[@h1nyc/search-sdk](../README.md) / util

# Module: util

## Table of contents

### Interfaces

- [EsBooleanQuery](../interfaces/util.esbooleanquery.md)

### Type aliases

- [EsBooleanClause](util.md#esbooleanclause)
- [EsQuery](util.md#esquery)

### Variables

- [nonInitiatingFilters](util.md#noninitiatingfilters)

### Functions

- [buildFacetAggregations](util.md#buildfacetaggregations)
- [esNumberValToUnixTime](util.md#esnumbervaltounixtime)
- [getSortWeight](util.md#getsortweight)
- [hasTagsInSearchFilters](util.md#hastagsinsearchfilters)
- [hasValidFiltersForSearch](util.md#hasvalidfiltersforsearch)
- [ignoreScore](util.md#ignorescore)
- [matchAll](util.md#matchall)
- [minScore](util.md#minscore)
- [nestedFilter](util.md#nestedfilter)
- [total](util.md#total)
- [transformFilters](util.md#transformfilters)
- [transformResults](util.md#transformresults)
- [weightedScore](util.md#weightedscore)
- [worksDateFilter](util.md#worksdatefilter)

## Type aliases

### EsBooleanClause

Ƭ **EsBooleanClause**: *any*

Defined in: [packages/sdk/src/util/elasticUtils.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L1)

___

### EsQuery

Ƭ **EsQuery**: *any*

Defined in: [packages/sdk/src/util/elasticUtils.ts:10](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L10)

## Variables

### nonInitiatingFilters

• `Const` **nonInitiatingFilters**: *string*[]

List of filter keys that when exist as the only filters will not execute a search.
Keys must match what is in `SavedSearchFilterValues` in path notation (foo.bar.baz)

Defined in: [packages/sdk/src/util/filterUtils.ts:14](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L14)

## Functions

### buildFacetAggregations

▸ `Const`**buildFacetAggregations**(`keyword`: *boolean*): *object*

#### Parameters:

Name | Type |
------ | ------ |
`keyword` | *boolean* |

**Returns:** *object*

Defined in: [packages/sdk/src/util/facetUtils.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/facetUtils.ts#L3)

___

### esNumberValToUnixTime

▸ `Const`**esNumberValToUnixTime**<T\>(`items`: T[]): *function*

#### Type parameters:

Name | Type |
------ | ------ |
`T` | *unknown* |

#### Parameters:

Name | Type |
------ | ------ |
`items` | T[] |

**Returns:** *function*

Defined in: [packages/sdk/src/util/transformer.ts:521](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/transformer.ts#L521)

___

### getSortWeight

▸ `Const`**getSortWeight**(`sortBy?`: [*WeightedSortBy*](../interfaces/interfaces_filterinterfaces.weightedsortby.md), `sortPath?`: *string*): *number*

#### Parameters:

Name | Type |
------ | ------ |
`sortBy?` | [*WeightedSortBy*](../interfaces/interfaces_filterinterfaces.weightedsortby.md) |
`sortPath?` | *string* |

**Returns:** *number*

Defined in: [packages/sdk/src/util/filterUtils.ts:190](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L190)

___

### hasTagsInSearchFilters

▸ `Const`**hasTagsInSearchFilters**(`filters`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md)): *boolean*

Check whether the filters contains tag IDs

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`filters` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) | Search filters    |

**Returns:** *boolean*

Defined in: [packages/sdk/src/util/filterUtils.ts:53](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L53)

___

### hasValidFiltersForSearch

▸ `Const`**hasValidFiltersForSearch**(`filters`: [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md)): *boolean*

Check that the filter values are valid.

This checks that there are initiating filter values in the list of filters.

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`filters` | [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md) | Filter values    |

**Returns:** *boolean*

Defined in: [packages/sdk/src/util/filterUtils.ts:23](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L23)

___

### ignoreScore

▸ `Const`**ignoreScore**(`query`: *any*): *object*

This wraps an ElasticSearch query with a boolean query that will ignore the score.  This is done
for when we need to calculate a count for filtering purposes but we do not want the count to add
to the overall score of the query.

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |

**Returns:** *object*

Name | Type |
------ | ------ |
`function_score` | { `functions`: { `script_score`: { `script`: *string* = "0" }  }[] ; `query`: *any*  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:17](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L17)

___

### matchAll

▸ `Const`**matchAll**(`query`: *any*): *object*

This is a clause added to a min-max query that will ensure that correct minimum values and hit
counts are obtained.  It forces elastic search to visit all records including the ones that didn't
this particular (sub)query.

For example, in min/max queries, without this clause you might get a "min: 1, max: 275" but with this
clause you will get a "min: 0, max: 275".

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |

**Returns:** *object*

Name | Type |
------ | ------ |
`bool` | { `filter`: { `match_all`: {}  }[] ; `should`: *any*  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:196](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L196)

___

### minScore

▸ `Const`**minScore**(`query`: *any*, `minScoreValue`: *number*): *object*

This is used to wrap an existing scored query with a min_score restriction.

Since we often use scores to represent a count, this is often used as a "min count".

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |
`minScoreValue` | *number* |

**Returns:** *object*

Name | Type |
------ | ------ |
`function_score` | { `min_score`: *number* ; `query`: *any*  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:140](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L140)

___

### nestedFilter

▸ `Const`**nestedFilter**(`fullPath`: *string*, `filter`: [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[]): *object*

This is used to filter a nested object

#### Parameters:

Name | Type |
------ | ------ |
`fullPath` | *string* |
`filter` | [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[] |

**Returns:** *object*

Name | Type |
------ | ------ |
`nested` | { `path`: *string* ; `query`: { `bool`: { `filter`: [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[]  }  }  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:119](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L119)

___

### total

▸ `Const`**total**(`path`: *string*, `filter?`: [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[], `base?`: *boolean*, `additional?`: *any*, `containedInsideAMust?`: *boolean*): { `function_score`: { `field_value_factor`: { `field`: *string*  }  } ; `match_all`: *undefined*  } \| { `function_score`: *undefined* ; `match_all`: {}  } \| { `nested`: *any*  }

This performs a count of nested documents if the is path is not nested, otherwise it
will sum up the values.

#### Parameters:

Name | Type | Default value |
------ | ------ | ------ |
`path` | *string* | - |
`filter?` | [*EsBooleanQuery*](../interfaces/util_elasticutils.esbooleanquery.md)[] | - |
`base?` | *boolean* | - |
`additional?` | *any* | - |
`containedInsideAMust` | *boolean* | false |

**Returns:** { `function_score`: { `field_value_factor`: { `field`: *string*  }  } ; `match_all`: *undefined*  } \| { `function_score`: *undefined* ; `match_all`: {}  } \| { `nested`: *any*  }

Defined in: [packages/sdk/src/util/elasticUtils.ts:73](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L73)

___

### transformFilters

▸ `Const`**transformFilters**(`fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `profileEnabled`: *boolean*, `claimsEnabled`: *boolean*, `referralsEnabled`: *boolean*): [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md)

Transforms the value of the FilterSearchInterface into SavedSearchFilterValues

Hopefully, this will not be needed anymore when we change the front end to
send the SavedSearchFilterValues to the API directly.

#### Parameters:

Name | Type |
------ | ------ |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) |
`profileEnabled` | *boolean* |
`claimsEnabled` | *boolean* |
`referralsEnabled` | *boolean* |

**Returns:** [*SavedSearchFilterValues*](../interfaces/interfaces_filterinterfaces.savedsearchfiltervalues.md)

Defined in: [packages/sdk/src/util/filterUtils.ts:78](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/filterUtils.ts#L78)

___

### transformResults

▸ `Const`**transformResults**(`fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `searchResults`: [*SearchResultsRootObject*](../interfaces/api_response_responseinterfaces.searchresultsrootobject.md), `minMaxResults`: [*MinMaxResult*](../interfaces/api_response_responseinterfaces.minmaxresult.md)[], `claimsEnabled`: *boolean*, `referralsEnabled`: *boolean*): *Promise*<[*PersonSearchResponse*](interfaces_elasticdocuments.md#personsearchresponse)\>

#### Parameters:

Name | Type |
------ | ------ |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) |
`searchResults` | [*SearchResultsRootObject*](../interfaces/api_response_responseinterfaces.searchresultsrootobject.md) |
`minMaxResults` | [*MinMaxResult*](../interfaces/api_response_responseinterfaces.minmaxresult.md)[] |
`claimsEnabled` | *boolean* |
`referralsEnabled` | *boolean* |

**Returns:** *Promise*<[*PersonSearchResponse*](interfaces_elasticdocuments.md#personsearchresponse)\>

Defined in: [packages/sdk/src/util/transformer.ts:54](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/transformer.ts#L54)

___

### weightedScore

▸ `Const`**weightedScore**(`query`: *any*, `path`: *string*, `base`: *undefined* \| *boolean*, `minScoreValue`: *number*, `maxScoreValue`: *number*, `weight`: *number*): *object*

This is used to wrap an existing score and adjust it according to the min, max, and
weight values passed in.

#### Parameters:

Name | Type |
------ | ------ |
`query` | *any* |
`path` | *string* |
`base` | *undefined* \| *boolean* |
`minScoreValue` | *number* |
`maxScoreValue` | *number* |
`weight` | *number* |

**Returns:** *object*

Name | Type |
------ | ------ |
`function_score` | { `boost_mode`: *string* = "replace"; `functions`: { `script_score`: { `script`: { `params`: { `maximum`: *number* ; `minimum`: *number* ; `weight`: *number*  } ; `source`: *string*  }  }  }[] ; `query`: *any*  } \| { `boost_mode`: *string* = "replace"; `functions`: { `script_score`: { `script`: { `params`: { `maximum`: *number* ; `minimum`: *number* ; `weight`: *number*  } ; `source`: *string*  }  }  }[] ; `query`: *any*  } \| { `boost_mode`: *string* = "replace"; `functions`: { `script_score`: { `script`: { `params`: { `maximum`: *number* ; `minimum`: *number* ; `weight`: *number*  } ; `source`: *string*  }  }  }[] ; `query`: *any* ; `score_mode`: *string* = "sum" } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:151](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L151)

___

### worksDateFilter

▸ `Const`**worksDateFilter**(`path`: *string*, `minDateValue`: *number*, `maxDateValue?`: *number*): *object*

This creates a date filter for the given path and min/max date values.

If a date range is specified, then the documents must be in that range, or the date field must not exist at
all in the document.

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`path` | *string* | This is the path into the ES index.   |
`minDateValue` | *number* | This is in epoch milliseconds.   |
`maxDateValue?` | *number* | This is in epoch milliseconds.  If this is not specified, the search will just generate   a date search >= minDateValue.    |

**Returns:** *object*

Name | Type |
------ | ------ |
`bool` | { `should`: ({ `bool`: *undefined* ; `range`: {}  } \| { `bool`: { `must_not`: { `exists`: { `field`: *string*  }  }  } ; `range`: *undefined*  })[]  } |

Defined in: [packages/sdk/src/util/elasticUtils.ts:41](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L41)
