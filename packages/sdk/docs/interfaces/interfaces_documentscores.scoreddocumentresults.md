[@h1nyc/search-sdk](../README.md) / [interfaces/documentScores](../modules/interfaces_documentscores.md) / ScoredDocumentResults

# Interface: ScoredDocumentResults

[interfaces/documentScores](../modules/interfaces_documentscores.md).ScoredDocumentResults

## Hierarchy

* **ScoredDocumentResults**

## Table of contents

### Properties

- [normalizedRange](interfaces_documentscores.scoreddocumentresults.md#normalizedrange)
- [persons](interfaces_documentscores.scoreddocumentresults.md#persons)
- [ranges](interfaces_documentscores.scoreddocumentresults.md#ranges)

## Properties

### normalizedRange

• **normalizedRange**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:72](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L72)

___

### persons

• **persons**: [*ScoredDocumentData*](interfaces_documentscores.scoreddocumentdata.md)[]

Defined in: [packages/sdk/src/interfaces/documentScores.ts:70](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L70)

___

### ranges

• **ranges**: [*DocumentRanges*](interfaces_documentscores.documentranges.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:71](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L71)
