[@h1nyc/search-sdk](../README.md) / [api/response/ResponseInterfaces](../modules/api_response_responseinterfaces.md) / SearchResultsRootObject

# Interface: SearchResultsRootObject

[api/response/ResponseInterfaces](../modules/api_response_responseinterfaces.md).SearchResultsRootObject

## Hierarchy

* **SearchResultsRootObject**

## Table of contents

### Properties

- [\_shards](api_response_responseinterfaces.searchresultsrootobject.md#_shards)
- [aggregations](api_response_responseinterfaces.searchresultsrootobject.md#aggregations)
- [hits](api_response_responseinterfaces.searchresultsrootobject.md#hits)
- [timed\_out](api_response_responseinterfaces.searchresultsrootobject.md#timed_out)
- [took](api_response_responseinterfaces.searchresultsrootobject.md#took)

## Properties

### \_shards

• **\_shards**: [*SearchResults\_shards*](api_response_responseinterfaces.searchresults_shards.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:41](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L41)

___

### aggregations

• **aggregations**: AggregationFilterCount

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:43](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L43)

___

### hits

• **hits**: [*SearchResultsHits*](api_response_responseinterfaces.searchresultshits.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:42](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L42)

___

### timed\_out

• **timed\_out**: *boolean*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:40](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L40)

___

### took

• **took**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:39](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L39)
