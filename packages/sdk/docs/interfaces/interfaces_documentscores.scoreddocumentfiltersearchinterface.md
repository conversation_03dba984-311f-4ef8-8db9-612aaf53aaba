[@h1nyc/search-sdk](../README.md) / [interfaces/documentScores](../modules/interfaces_documentscores.md) / ScoredDocumentFilterSearchInterface

# Interface: ScoredDocumentFilterSearchInterface

[interfaces/documentScores](../modules/interfaces_documentscores.md).ScoredDocumentFilterSearchInterface

## Hierarchy

* [*FilterSearchInterface*](interfaces_filterinterfaces.filtersearchinterface.md)

  ↳ **ScoredDocumentFilterSearchInterface**

## Table of contents

### Properties

- [activeFilters](interfaces_documentscores.scoreddocumentfiltersearchinterface.md#activefilters)
- [filters](interfaces_documentscores.scoreddocumentfiltersearchinterface.md#filters)
- [from](interfaces_documentscores.scoreddocumentfiltersearchinterface.md#from)
- [pageNum](interfaces_documentscores.scoreddocumentfiltersearchinterface.md#pagenum)
- [pageSize](interfaces_documentscores.scoreddocumentfiltersearchinterface.md#pagesize)
- [projectId](interfaces_documentscores.scoreddocumentfiltersearchinterface.md#projectid)
- [query](interfaces_documentscores.scoreddocumentfiltersearchinterface.md#query)
- [scroll\_id](interfaces_documentscores.scoreddocumentfiltersearchinterface.md#scroll_id)
- [sortBy](interfaces_documentscores.scoreddocumentfiltersearchinterface.md#sortby)
- [userId](interfaces_documentscores.scoreddocumentfiltersearchinterface.md#userid)

## Properties

### activeFilters

• **activeFilters**: [*ActiveFilterBreakDown*](interfaces_filterinterfaces.activefilterbreakdown.md)

Inherited from: [FilterSearchInterface](interfaces_filterinterfaces.filtersearchinterface.md).[activeFilters](interfaces_filterinterfaces.filtersearchinterface.md#activefilters)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:242](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L242)

___

### filters

• **filters**: [*FilterInterface*](interfaces_filterinterfaces.filterinterface.md)

Inherited from: [FilterSearchInterface](interfaces_filterinterfaces.filtersearchinterface.md).[filters](interfaces_filterinterfaces.filtersearchinterface.md#filters)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:234](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L234)

___

### from

• **from**: *number*

Inherited from: [FilterSearchInterface](interfaces_filterinterfaces.filtersearchinterface.md).[from](interfaces_filterinterfaces.filtersearchinterface.md#from)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:236](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L236)

___

### pageNum

• **pageNum**: *never*

Overrides: [FilterSearchInterface](interfaces_filterinterfaces.filtersearchinterface.md).[pageNum](interfaces_filterinterfaces.filtersearchinterface.md#pagenum)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:79](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L79)

___

### pageSize

• **pageSize**: *never*

Overrides: [FilterSearchInterface](interfaces_filterinterfaces.filtersearchinterface.md).[pageSize](interfaces_filterinterfaces.filtersearchinterface.md#pagesize)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:78](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L78)

___

### projectId

• **projectId**: *string*

Inherited from: [FilterSearchInterface](interfaces_filterinterfaces.filtersearchinterface.md).[projectId](interfaces_filterinterfaces.filtersearchinterface.md#projectid)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:233](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L233)

___

### query

• **query**: *string*[]

Inherited from: [FilterSearchInterface](interfaces_filterinterfaces.filtersearchinterface.md).[query](interfaces_filterinterfaces.filtersearchinterface.md#query)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:232](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L232)

___

### scroll\_id

• **scroll\_id**: *never*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:77](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L77)

___

### sortBy

• **sortBy**: [*WeightedSortBy*](interfaces_filterinterfaces.weightedsortby.md)

Inherited from: [FilterSearchInterface](interfaces_filterinterfaces.filtersearchinterface.md).[sortBy](interfaces_filterinterfaces.filtersearchinterface.md#sortby)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:241](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L241)

___

### userId

• **userId**: *string*

Inherited from: [FilterSearchInterface](interfaces_filterinterfaces.filtersearchinterface.md).[userId](interfaces_filterinterfaces.filtersearchinterface.md#userid)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:231](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L231)
