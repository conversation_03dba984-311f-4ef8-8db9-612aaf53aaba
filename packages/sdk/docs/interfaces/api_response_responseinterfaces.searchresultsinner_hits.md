[@h1nyc/search-sdk](../README.md) / [api/response/ResponseInterfaces](../modules/api_response_responseinterfaces.md) / SearchResultsInner_hits

# Interface: SearchResultsInner\_hits

[api/response/ResponseInterfaces](../modules/api_response_responseinterfaces.md).SearchResultsInner_hits

## Hierarchy

* **SearchResultsInner_hits**

## Table of contents

### Properties

- [congress](api_response_responseinterfaces.searchresultsinner_hits.md#congress)
- [diagnoses\_amounts](api_response_responseinterfaces.searchresultsinner_hits.md#diagnoses_amounts)
- [microBloggingCount](api_response_responseinterfaces.searchresultsinner_hits.md#microbloggingcount)
- [payment\_amounts](api_response_responseinterfaces.searchresultsinner_hits.md#payment_amounts)
- [payments](api_response_responseinterfaces.searchresultsinner_hits.md#payments)
- [procedures\_amounts](api_response_responseinterfaces.searchresultsinner_hits.md#procedures_amounts)
- [profile](api_response_responseinterfaces.searchresultsinner_hits.md#profile)
- [publication\_citations](api_response_responseinterfaces.searchresultsinner_hits.md#publication_citations)
- [publications](api_response_responseinterfaces.searchresultsinner_hits.md#publications)
- [referralsReceived\_count](api_response_responseinterfaces.searchresultsinner_hits.md#referralsreceived_count)
- [referralsSent\_count](api_response_responseinterfaces.searchresultsinner_hits.md#referralssent_count)
- [trials](api_response_responseinterfaces.searchresultsinner_hits.md#trials)

## Properties

### congress

• **congress**: [*SearchResultsCongress*](api_response_responseinterfaces.searchresultscongress.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:107](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L107)

___

### diagnoses\_amounts

• **diagnoses\_amounts**: [*SearchResultsDiagnoses*](api_response_responseinterfaces.searchresultsdiagnoses.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:112](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L112)

___

### microBloggingCount

• **microBloggingCount**: [*SearchResultsPublication\_social\_media*](api_response_responseinterfaces.searchresultspublication_social_media.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:111](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L111)

___

### payment\_amounts

• **payment\_amounts**: [*SearchResultsPayment\_amounts*](api_response_responseinterfaces.searchresultspayment_amounts.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:108](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L108)

___

### payments

• **payments**: [*SearchResultsPayments*](api_response_responseinterfaces.searchresultspayments.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:109](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L109)

___

### procedures\_amounts

• **procedures\_amounts**: [*SearchResultsProcedures*](api_response_responseinterfaces.searchresultsprocedures.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:113](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L113)

___

### profile

• **profile**: [*SearchResultsPublications*](api_response_responseinterfaces.searchresultspublications.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:114](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L114)

___

### publication\_citations

• **publication\_citations**: [*SearchResultsPublication\_citations*](api_response_responseinterfaces.searchresultspublication_citations.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:105](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L105)

___

### publications

• **publications**: [*SearchResultsPublications*](api_response_responseinterfaces.searchresultspublications.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:110](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L110)

___

### referralsReceived\_count

• **referralsReceived\_count**: [*SearchResultsReferralsReceived*](api_response_responseinterfaces.searchresultsreferralsreceived.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:115](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L115)

___

### referralsSent\_count

• **referralsSent\_count**: [*SearchResultsReferralsSent*](api_response_responseinterfaces.searchresultsreferralssent.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:116](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L116)

___

### trials

• **trials**: [*SearchResultsTrials*](api_response_responseinterfaces.searchresultstrials.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:106](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L106)
