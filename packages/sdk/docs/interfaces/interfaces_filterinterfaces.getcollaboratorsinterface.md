[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / GetCollaboratorsInterface

# Interface: GetCollaboratorsInterface

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).GetCollaboratorsInterface

## Hierarchy

* **GetCollaboratorsInterface**

## Table of contents

### Properties

- [dateRange](interfaces_filterinterfaces.getcollaboratorsinterface.md#daterange)
- [personId](interfaces_filterinterfaces.getcollaboratorsinterface.md#personid)
- [terms](interfaces_filterinterfaces.getcollaboratorsinterface.md#terms)

## Properties

### dateRange

• **dateRange**: { `max`: *null* \| *number* ; `min`: *number*  }

#### Type declaration:

Name | Type |
------ | ------ |
`max` | *null* \| *number* |
`min` | *number* |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:303](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L303)

___

### personId

• **personId**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:301](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L301)

___

### terms

• `Optional` **terms**: *undefined* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:302](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L302)
