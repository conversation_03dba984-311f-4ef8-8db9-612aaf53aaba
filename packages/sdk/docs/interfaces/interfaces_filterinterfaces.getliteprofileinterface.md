[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / GetLiteProfileInterface

# Interface: GetLiteProfileInterface

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).GetLiteProfileInterface

## Hierarchy

* **GetLiteProfileInterface**

## Table of contents

### Properties

- [personIds](interfaces_filterinterfaces.getliteprofileinterface.md#personids)

## Properties

### personIds

• **personIds**: *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:310](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L310)
