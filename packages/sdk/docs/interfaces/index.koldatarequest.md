[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / KOLDataRequest

# Interface: KOLDataRequest

[index](../modules/index.md).KOLDataRequest

## Hierarchy

* **KOLDataRequest**

## Table of contents

### Properties

- [created](index.koldatarequest.md#created)
- [personId](index.koldatarequest.md#personid)
- [projectId](index.koldatarequest.md#projectid)
- [userId](index.koldatarequest.md#userid)

## Properties

### created

• `Optional` **created**: *undefined* \| Date

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:106](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L106)

___

### personId

• **personId**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:105](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L105)

___

### projectId

• **projectId**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:104](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L104)

___

### userId

• **userId**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:103](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L103)
