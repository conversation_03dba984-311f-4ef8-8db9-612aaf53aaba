[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / FilterSearchInterface

# Interface: FilterSearchInterface

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).FilterSearchInterface

## Hierarchy

* **FilterSearchInterface**

  ↳ [*ScoredDocumentFilterSearchInterface*](interfaces_documentscores.scoreddocumentfiltersearchinterface.md)

  ↳ [*ScoredDocumentFilterSearchInterface*](index.scoreddocumentfiltersearchinterface.md)

  ↳ [*ScoredDocumentFilterSearchInterface*](interfaces.scoreddocumentfiltersearchinterface.md)

## Table of contents

### Properties

- [activeFilters](interfaces_filterinterfaces.filtersearchinterface.md#activefilters)
- [filters](interfaces_filterinterfaces.filtersearchinterface.md#filters)
- [from](interfaces_filterinterfaces.filtersearchinterface.md#from)
- [pageNum](interfaces_filterinterfaces.filtersearchinterface.md#pagenum)
- [pageSize](interfaces_filterinterfaces.filtersearchinterface.md#pagesize)
- [projectId](interfaces_filterinterfaces.filtersearchinterface.md#projectid)
- [query](interfaces_filterinterfaces.filtersearchinterface.md#query)
- [sortBy](interfaces_filterinterfaces.filtersearchinterface.md#sortby)
- [userId](interfaces_filterinterfaces.filtersearchinterface.md#userid)

## Properties

### activeFilters

• **activeFilters**: [*ActiveFilterBreakDown*](interfaces_filterinterfaces.activefilterbreakdown.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:242](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L242)

___

### filters

• **filters**: [*FilterInterface*](interfaces_filterinterfaces.filterinterface.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:234](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L234)

___

### from

• **from**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:236](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L236)

___

### pageNum

• `Optional` **pageNum**: *undefined* \| *number*

**`deprecated`** 

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:240](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L240)

___

### pageSize

• **pageSize**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:235](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L235)

___

### projectId

• **projectId**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:233](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L233)

___

### query

• **query**: *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:232](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L232)

___

### sortBy

• **sortBy**: [*WeightedSortBy*](interfaces_filterinterfaces.weightedsortby.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:241](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L241)

___

### userId

• **userId**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:231](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L231)
