[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / PeopleSearchDocument

# Interface: PeopleSearchDocument

[index](../modules/index.md).PeopleSearchDocument

## Hierarchy

* **PeopleSearchDocument**

## Table of contents

### Properties

- [\_id](index.peoplesearchdocument.md#_id)
- [collaborators](index.peoplesearchdocument.md#collaborators)
- [departments](index.peoplesearchdocument.md#departments)
- [designations](index.peoplesearchdocument.md#designations)
- [id](index.peoplesearchdocument.md#id)
- [institutions](index.peoplesearchdocument.md#institutions)
- [locations](index.peoplesearchdocument.md#locations)
- [name](index.peoplesearchdocument.md#name)
- [names](index.peoplesearchdocument.md#names)
- [personNames](index.peoplesearchdocument.md#personnames)
- [projects](index.peoplesearchdocument.md#projects)
- [schools](index.peoplesearchdocument.md#schools)
- [scores](index.peoplesearchdocument.md#scores)
- [specialty](index.peoplesearchdocument.md#specialty)
- [titles](index.peoplesearchdocument.md#titles)

## Properties

### \_id

• **\_id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L12)

___

### collaborators

• **collaborators**: *number*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:35](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L35)

___

### departments

• **departments**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:31](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L31)

___

### designations

• **designations**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:27](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L27)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:11](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L11)

___

### institutions

• **institutions**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:33](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L33)

___

### locations

• **locations**: (*null* \| { `city`: *string* ; `state`: *string*  })[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:29](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L29)

___

### name

• **name**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L13)

___

### names

• **names**: { `first`: *string* ; `last`: *string* ; `middle`: *string*  }

#### Type declaration:

Name | Type |
------ | ------ |
`first` | *string* |
`last` | *string* |
`middle` | *string* |

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:21](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L21)

___

### personNames

• **personNames**: { `first`: *string* ; `id`: *string* ; `last`: *string* ; `middle`: *string* ; `projectIds`: *string*[]  }[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:14](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L14)

___

### projects

• **projects**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:34](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L34)

___

### schools

• **schools**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:32](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L32)

___

### scores

• **scores**: { `projectId`: *string* ; `score`: *number*  }[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:30](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L30)

___

### specialty

• **specialty**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:28](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L28)

___

### titles

• **titles**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:26](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L26)
