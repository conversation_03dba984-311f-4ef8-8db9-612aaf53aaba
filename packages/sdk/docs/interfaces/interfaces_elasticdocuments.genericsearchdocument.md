[@h1nyc/search-sdk](../README.md) / [interfaces/elasticDocuments](../modules/interfaces_elasticdocuments.md) / GenericSearchDocument

# Interface: GenericSearchDocument

[interfaces/elasticDocuments](../modules/interfaces_elasticdocuments.md).GenericSearchDocument

## Hierarchy

* **GenericSearchDocument**

## Table of contents

### Properties

- [\_id](interfaces_elasticdocuments.genericsearchdocument.md#_id)
- [citationCount](interfaces_elasticdocuments.genericsearchdocument.md#citationcount)
- [collaborators](interfaces_elasticdocuments.genericsearchdocument.md#collaborators)
- [date](interfaces_elasticdocuments.genericsearchdocument.md#date)
- [docType](interfaces_elasticdocuments.genericsearchdocument.md#doctype)
- [id](interfaces_elasticdocuments.genericsearchdocument.md#id)
- [paymentSum](interfaces_elasticdocuments.genericsearchdocument.md#paymentsum)
- [personNames](interfaces_elasticdocuments.genericsearchdocument.md#personnames)
- [person\_ids](interfaces_elasticdocuments.genericsearchdocument.md#person_ids)
- [terms](interfaces_elasticdocuments.genericsearchdocument.md#terms)
- [type](interfaces_elasticdocuments.genericsearchdocument.md#type)

## Properties

### \_id

• **\_id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:42](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L42)

___

### citationCount

• `Optional` **citationCount**: *undefined* \| *number*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:55](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L55)

___

### collaborators

• **collaborators**: *number*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:45](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L45)

___

### date

• **date**: *undefined* \| *string* \| *number*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:46](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L46)

___

### docType

• **docType**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:43](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L43)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:41](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L41)

___

### paymentSum

• `Optional` **paymentSum**: *undefined* \| *number*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:54](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L54)

___

### personNames

• **personNames**: { `first`: *string* ; `id`: *string* ; `last`: *string* ; `middle`: *string* ; `projectIds`: *string*[]  }[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:47](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L47)

___

### person\_ids

• **person\_ids**: { `ids`: *string*[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`ids` | *string*[] |

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:44](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L44)

___

### terms

• **terms**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:39](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L39)

___

### type

• **type**: [*GenericSearchEnum*](../enums/interfaces_elasticdocuments.genericsearchenum.md)

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:40](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L40)
