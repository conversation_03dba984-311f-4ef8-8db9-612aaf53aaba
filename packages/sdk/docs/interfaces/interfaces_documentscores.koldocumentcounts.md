[@h1nyc/search-sdk](../README.md) / [interfaces/documentScores](../modules/interfaces_documentscores.md) / KOLDocumentCounts

# Interface: KOLDocumentCounts

[interfaces/documentScores](../modules/interfaces_documentscores.md).KOLDocumentCounts

## Hierarchy

* **KOLDocumentCounts**

## Table of contents

### Properties

- [citationCount](interfaces_documentscores.koldocumentcounts.md#citationcount)
- [congressCount](interfaces_documentscores.koldocumentcounts.md#congresscount)
- [diagnoses](interfaces_documentscores.koldocumentcounts.md#diagnoses)
- [grantCount](interfaces_documentscores.koldocumentcounts.md#grantcount)
- [grantSum](interfaces_documentscores.koldocumentcounts.md#grantsum)
- [paymentCount](interfaces_documentscores.koldocumentcounts.md#paymentcount)
- [paymentSum](interfaces_documentscores.koldocumentcounts.md#paymentsum)
- [personId](interfaces_documentscores.koldocumentcounts.md#personid)
- [procedures](interfaces_documentscores.koldocumentcounts.md#procedures)
- [publicationCount](interfaces_documentscores.koldocumentcounts.md#publicationcount)
- [socialMediaMentions](interfaces_documentscores.koldocumentcounts.md#socialmediamentions)
- [totalCollaborators](interfaces_documentscores.koldocumentcounts.md#totalcollaborators)
- [trialCount](interfaces_documentscores.koldocumentcounts.md#trialcount)

## Properties

### citationCount

• **citationCount**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:11](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L11)

___

### congressCount

• **congressCount**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:17](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L17)

___

### diagnoses

• **diagnoses**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:21](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L21)

___

### grantCount

• `Optional` **grantCount**: *undefined* \| *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:15](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L15)

___

### grantSum

• **grantSum**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:16](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L16)

___

### paymentCount

• `Optional` **paymentCount**: *undefined* \| *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L13)

___

### paymentSum

• **paymentSum**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:14](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L14)

___

### personId

• **personId**: *string*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L9)

___

### procedures

• **procedures**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:20](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L20)

___

### publicationCount

• **publicationCount**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:10](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L10)

___

### socialMediaMentions

• **socialMediaMentions**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:19](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L19)

___

### totalCollaborators

• **totalCollaborators**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:18](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L18)

___

### trialCount

• **trialCount**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L12)
