[@h1nyc/search-sdk](../README.md) / [interfaces/documentScores](../modules/interfaces_documentscores.md) / ScoredDocumentResult

# Interface: ScoredDocumentResult

[interfaces/documentScores](../modules/interfaces_documentscores.md).ScoredDocumentResult

## Hierarchy

* **ScoredDocumentResult**

## Table of contents

### Properties

- [maxValue](interfaces_documentscores.scoreddocumentresult.md#maxvalue)
- [minValue](interfaces_documentscores.scoreddocumentresult.md#minvalue)
- [normalizedValue](interfaces_documentscores.scoreddocumentresult.md#normalizedvalue)
- [percentile](interfaces_documentscores.scoreddocumentresult.md#percentile)
- [value](interfaces_documentscores.scoreddocumentresult.md#value)

## Properties

### maxValue

• **maxValue**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:28](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L28)

___

### minValue

• **minValue**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:27](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L27)

___

### normalizedValue

• **normalizedValue**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:26](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L26)

___

### percentile

• **percentile**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:29](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L29)

___

### value

• **value**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:25](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L25)
