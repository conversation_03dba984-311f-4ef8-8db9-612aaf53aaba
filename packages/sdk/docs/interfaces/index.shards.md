[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / Shards

# Interface: Shards

[index](../modules/index.md).Shards

## Hierarchy

* **Shards**

## Table of contents

### Properties

- [failed](index.shards.md#failed)
- [skipped](index.shards.md#skipped)
- [successful](index.shards.md#successful)
- [total](index.shards.md#total)

## Properties

### failed

• **failed**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:15](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L15)

___

### skipped

• **skipped**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:14](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L14)

___

### successful

• **successful**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:13](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L13)

___

### total

• **total**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:12](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L12)
