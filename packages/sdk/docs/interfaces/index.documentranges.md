[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / DocumentRanges

# Interface: DocumentRanges

[index](../modules/index.md).DocumentRanges

## Hierarchy

* **DocumentRanges**

## Table of contents

### Properties

- [citationCount](index.documentranges.md#citationcount)
- [congressCount](index.documentranges.md#congresscount)
- [diagnoses](index.documentranges.md#diagnoses)
- [grantCount](index.documentranges.md#grantcount)
- [grantSum](index.documentranges.md#grantsum)
- [paymentCount](index.documentranges.md#paymentcount)
- [paymentSum](index.documentranges.md#paymentsum)
- [procedures](index.documentranges.md#procedures)
- [publicationCount](index.documentranges.md#publicationcount)
- [referralsReceived](index.documentranges.md#referralsreceived)
- [referralsSent](index.documentranges.md#referralssent)
- [socialMediaMentions](index.documentranges.md#socialmediamentions)
- [totalCollaborators](index.documentranges.md#totalcollaborators)
- [trialCount](index.documentranges.md#trialcount)

## Properties

### citationCount

• **citationCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:54](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L54)

___

### congressCount

• **congressCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:60](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L60)

___

### diagnoses

• **diagnoses**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:64](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L64)

___

### grantCount

• **grantCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:58](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L58)

___

### grantSum

• **grantSum**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:59](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L59)

___

### paymentCount

• **paymentCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:56](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L56)

___

### paymentSum

• **paymentSum**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:57](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L57)

___

### procedures

• **procedures**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:63](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L63)

___

### publicationCount

• **publicationCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:53](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L53)

___

### referralsReceived

• **referralsReceived**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:65](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L65)

___

### referralsSent

• **referralsSent**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:66](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L66)

___

### socialMediaMentions

• **socialMediaMentions**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:62](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L62)

___

### totalCollaborators

• **totalCollaborators**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:61](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L61)

___

### trialCount

• **trialCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:55](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L55)
