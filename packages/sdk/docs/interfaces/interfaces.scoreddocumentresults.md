[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / ScoredDocumentResults

# Interface: ScoredDocumentResults

[interfaces](../modules/interfaces.md).ScoredDocumentResults

## Hierarchy

* **ScoredDocumentResults**

## Table of contents

### Properties

- [normalizedRange](interfaces.scoreddocumentresults.md#normalizedrange)
- [persons](interfaces.scoreddocumentresults.md#persons)
- [ranges](interfaces.scoreddocumentresults.md#ranges)

## Properties

### normalizedRange

• **normalizedRange**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:72](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L72)

___

### persons

• **persons**: [*ScoredDocumentData*](interfaces_documentscores.scoreddocumentdata.md)[]

Defined in: [packages/sdk/src/interfaces/documentScores.ts:70](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L70)

___

### ranges

• **ranges**: [*DocumentRanges*](interfaces_documentscores.documentranges.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:71](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L71)
