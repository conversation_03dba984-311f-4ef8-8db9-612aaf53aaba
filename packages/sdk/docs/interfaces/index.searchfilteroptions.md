[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / SearchFilterOptions

# Interface: SearchFilterOptions

[index](../modules/index.md).SearchFilterOptions

TextFilter or CheckboxFilter options
i.e. anything that has options to be selected

## Hierarchy

* **SearchFilterOptions**

## Table of contents

### Properties

- [city](index.searchfilteroptions.md#city)
- [claims](index.searchfilteroptions.md#claims)
- [congresses](index.searchfilteroptions.md#congresses)
- [country](index.searchfilteroptions.md#country)
- [grants](index.searchfilteroptions.md#grants)
- [institution](index.searchfilteroptions.md#institution)
- [institutionType](index.searchfilteroptions.md#institutiontype)
- [npi](index.searchfilteroptions.md#npi)
- [pastAndPresentWorkInstitutions](index.searchfilteroptions.md#pastandpresentworkinstitutions)
- [payments](index.searchfilteroptions.md#payments)
- [presentWorkInstitutions](index.searchfilteroptions.md#presentworkinstitutions)
- [profile](index.searchfilteroptions.md#profile)
- [publications](index.searchfilteroptions.md#publications)
- [referrals](index.searchfilteroptions.md#referrals)
- [specialty](index.searchfilteroptions.md#specialty)
- [state](index.searchfilteroptions.md#state)
- [studentInstitutions](index.searchfilteroptions.md#studentinstitutions)
- [tags](index.searchfilteroptions.md#tags)
- [trials](index.searchfilteroptions.md#trials)
- [zipCode](index.searchfilteroptions.md#zipcode)

## Properties

### city

• **city**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:403](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L403)

___

### claims

• **claims**: { `diagnosesICD`: Option[] ; `proceduresCPT`: Option[] ; `proceduresHCPC`: Option[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`diagnosesICD` | Option[] |
`proceduresCPT` | Option[] |
`proceduresHCPC` | Option[] |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:414](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L414)

___

### congresses

• **congresses**: { `name`: Option[] ; `organizerName`: Option[] ; `sessionType`: Option[] ; `type`: Option[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`name` | Option[] |
`organizerName` | Option[] |
`sessionType` | Option[] |
`type` | Option[] |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:433](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L433)

___

### country

• **country**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:401](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L401)

___

### grants

• **grants**: { `funder`: Option[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`funder` | Option[] |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:439](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L439)

___

### institution

• **institution**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:405](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L405)

___

### institutionType

• `Optional` **institutionType**: *undefined* \| Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:410](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L410)

___

### npi

• **npi**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:409](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L409)

___

### pastAndPresentWorkInstitutions

• **pastAndPresentWorkInstitutions**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:407](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L407)

___

### payments

• **payments**: { `company`: Option[] ; `drugOrDevice`: Option[] ; `fundingType`: Option[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`company` | Option[] |
`drugOrDevice` | Option[] |
`fundingType` | Option[] |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:442](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L442)

___

### presentWorkInstitutions

• **presentWorkInstitutions**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:406](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L406)

___

### profile

• **profile**: { `role`: Option[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`role` | Option[] |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:423](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L423)

___

### publications

• **publications**: { `journal`: Option[] ; `type`: Option[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`journal` | Option[] |
`type` | Option[] |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:419](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L419)

___

### referrals

• **referrals**: { `serviceLine`: Option[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`serviceLine` | Option[] |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:447](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L447)

___

### specialty

• **specialty**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:400](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L400)

___

### state

• **state**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:402](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L402)

___

### studentInstitutions

• **studentInstitutions**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:408](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L408)

___

### tags

• **tags**: { `name`: Option[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`name` | Option[] |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:411](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L411)

___

### trials

• **trials**: { `funderType`: Option[] ; `phase`: Option[] ; `sponsor`: Option[] ; `status`: Option[] ; `studyType`: Option[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`funderType` | Option[] |
`phase` | Option[] |
`sponsor` | Option[] |
`status` | Option[] |
`studyType` | Option[] |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:426](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L426)

___

### zipCode

• **zipCode**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:404](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L404)
