[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / Pagination

# Interface: Pagination

[index](../modules/index.md).Pagination

## Hierarchy

* **Pagination**

## Table of contents

### Properties

- [from](index.pagination.md#from)
- [pageSize](index.pagination.md#pagesize)
- [total](index.pagination.md#total)

## Properties

### from

• **from**: *number*

The first index of the result set

Defined in: [packages/sdk/src/interfaces/pagination.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L5)

___

### pageSize

• **pageSize**: *number*

The page size

Defined in: [packages/sdk/src/interfaces/pagination.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L9)

___

### total

• **total**: *number*

The total number of results

Defined in: [packages/sdk/src/interfaces/pagination.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L13)
