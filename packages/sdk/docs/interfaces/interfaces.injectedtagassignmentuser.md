[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / InjectedTagAssignmentUser

# Interface: InjectedTagAssignmentUser

[interfaces](../modules/interfaces.md).InjectedTagAssignmentUser

## Hierarchy

* **InjectedTagAssignmentUser**

## Table of contents

### Properties

- [\_\_typename](interfaces.injectedtagassignmentuser.md#__typename)
- [firstName](interfaces.injectedtagassignmentuser.md#firstname)
- [id](interfaces.injectedtagassignmentuser.md#id)
- [lastName](interfaces.injectedtagassignmentuser.md#lastname)

## Properties

### \_\_typename

• **\_\_typename**: *User*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:101](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L101)

___

### firstName

• **firstName**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:103](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L103)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:102](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L102)

___

### lastName

• **lastName**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:104](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L104)
