[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / ClaimsDiagnosis

# Interface: ClaimsDiagnosis

[index](../modules/index.md).ClaimsDiagnosis

## Hierarchy

* **ClaimsDiagnosis**

## Table of contents

### Properties

- [codeScheme](index.claimsdiagnosis.md#codescheme)
- [count](index.claimsdiagnosis.md#count)
- [description](index.claimsdiagnosis.md#description)
- [diagnosisCode](index.claimsdiagnosis.md#diagnosiscode)
- [internalCount](index.claimsdiagnosis.md#internalcount)
- [percentOfClaims](index.claimsdiagnosis.md#percentofclaims)
- [percentage](index.claimsdiagnosis.md#percentage)

## Properties

### codeScheme

• **codeScheme**: *string*

Defined in: [packages/sdk/src/interfaces/Claims.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/Claims.ts#L8)

___

### count

• **count**: *string*

Defined in: [packages/sdk/src/interfaces/Claims.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/Claims.ts#L6)

___

### description

• **description**: *string*

Defined in: [packages/sdk/src/interfaces/Claims.ts:2](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/Claims.ts#L2)

___

### diagnosisCode

• **diagnosisCode**: *string*

Defined in: [packages/sdk/src/interfaces/Claims.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/Claims.ts#L3)

___

### internalCount

• **internalCount**: *number*

Defined in: [packages/sdk/src/interfaces/Claims.ts:7](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/Claims.ts#L7)

___

### percentOfClaims

• **percentOfClaims**: *number*

Defined in: [packages/sdk/src/interfaces/Claims.ts:4](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/Claims.ts#L4)

___

### percentage

• **percentage**: *number*

Defined in: [packages/sdk/src/interfaces/Claims.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/Claims.ts#L5)
