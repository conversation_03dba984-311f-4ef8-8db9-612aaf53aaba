[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / MinMaxResult

# Interface: MinMaxResult

[index](../modules/index.md).MinMaxResult

## Hierarchy

* **MinMaxResult**

## Table of contents

### Properties

- [max](index.minmaxresult.md#max)
- [min](index.minmaxresult.md#min)
- [name](index.minmaxresult.md#name)

## Properties

### max

• **max**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:170](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L170)

___

### min

• **min**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:169](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L169)

___

### name

• **name**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:168](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L168)
