[@h1nyc/search-sdk](../README.md) / [api/response](../modules/api_response.md) / SearchResultsHits

# Interface: SearchResultsHits

[api/response](../modules/api_response.md).SearchResultsHits

## Hierarchy

* **SearchResultsHits**

## Table of contents

### Properties

- [hits](api_response.searchresultshits.md#hits)
- [max\_score](api_response.searchresultshits.md#max_score)
- [total](api_response.searchresultshits.md#total)

## Properties

### hits

• **hits**: [*SearchResultsHitsItem*](api_response_responseinterfaces.searchresultshitsitem.md)[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:54](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L54)

___

### max\_score

• **max\_score**: *null* \| *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:53](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L53)

___

### total

• **total**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:52](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L52)
