[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / Hits

# Interface: Hits

[index](../modules/index.md).Hits

## Hierarchy

* **Hits**

## Table of contents

### Properties

- [max\_score](index.hits.md#max_score)
- [total](index.hits.md#total)

## Properties

### max\_score

• **max\_score**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:19](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L19)

___

### total

• **total**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:18](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L18)
