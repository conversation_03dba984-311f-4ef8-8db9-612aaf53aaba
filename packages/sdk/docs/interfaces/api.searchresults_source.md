[@h1nyc/search-sdk](../README.md) / [api](../modules/api.md) / SearchResults_source

# Interface: SearchResults\_source

[api](../modules/api.md).SearchResults_source

## Hierarchy

* **SearchResults_source**

## Table of contents

### Properties

- [citationTotal](api.searchresults_source.md#citationtotal)
- [collaboratorCount](api.searchresults_source.md#collaboratorcount)
- [congress](api.searchresults_source.md#congress)
- [congressCount](api.searchresults_source.md#congresscount)
- [diagnosesCount](api.searchresults_source.md#diagnosescount)
- [firstName](api.searchresults_source.md#firstname)
- [graduationYear](api.searchresults_source.md#graduationyear)
- [id](api.searchresults_source.md#id)
- [institutions](api.searchresults_source.md#institutions)
- [lastName](api.searchresults_source.md#lastname)
- [locations](api.searchresults_source.md#locations)
- [microBloggingTotal](api.searchresults_source.md#microbloggingtotal)
- [middleName](api.searchresults_source.md#middlename)
- [name](api.searchresults_source.md#name)
- [pastAndPresentWorkInstitutions](api.searchresults_source.md#pastandpresentworkinstitutions)
- [paymentTotal](api.searchresults_source.md#paymenttotal)
- [payments](api.searchresults_source.md#payments)
- [presentWorkInstitutions](api.searchresults_source.md#presentworkinstitutions)
- [proceduresCount](api.searchresults_source.md#procedurescount)
- [publicationCount](api.searchresults_source.md#publicationcount)
- [publications](api.searchresults_source.md#publications)
- [referralsReceivedCount](api.searchresults_source.md#referralsreceivedcount)
- [referralsSentCount](api.searchresults_source.md#referralssentcount)
- [specialty](api.searchresults_source.md#specialty)
- [studentInstitutions](api.searchresults_source.md#studentinstitutions)
- [titles](api.searchresults_source.md#titles)
- [totalWorks](api.searchresults_source.md#totalworks)
- [trialCount](api.searchresults_source.md#trialcount)
- [trials](api.searchresults_source.md#trials)

## Properties

### citationTotal

• **citationTotal**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:76](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L76)

___

### collaboratorCount

• **collaboratorCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:80](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L80)

___

### congress

• **congress**: { `endDate`: *number*  }[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:97](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L97)

___

### congressCount

• **congressCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:81](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L81)

___

### diagnosesCount

• **diagnosesCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:73](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L73)

___

### firstName

• **firstName**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:83](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L83)

___

### graduationYear

• **graduationYear**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:72](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L72)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:89](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L89)

___

### institutions

• **institutions**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:68](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L68)

___

### lastName

• **lastName**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:85](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L85)

___

### locations

• `Optional` **locations**: *undefined* \| [*SearchResultsLocationsItem*](api_response_responseinterfaces.searchresultslocationsitem.md)[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:86](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L86)

___

### microBloggingTotal

• **microBloggingTotal**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:77](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L77)

___

### middleName

• `Optional` **middleName**: *undefined* \| *null* \| *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:84](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L84)

___

### name

• **name**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:82](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L82)

___

### pastAndPresentWorkInstitutions

• **pastAndPresentWorkInstitutions**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:70](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L70)

___

### paymentTotal

• **paymentTotal**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:87](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L87)

___

### payments

• **payments**: { `paymentDate`: *number*  }[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:96](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L96)

___

### presentWorkInstitutions

• **presentWorkInstitutions**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:69](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L69)

___

### proceduresCount

• **proceduresCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:74](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L74)

___

### publicationCount

• **publicationCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:75](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L75)

___

### publications

• **publications**: { `datePublished`: *number*  }[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:95](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L95)

___

### referralsReceivedCount

• **referralsReceivedCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:90](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L90)

___

### referralsSentCount

• **referralsSentCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:91](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L91)

___

### specialty

• **specialty**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:79](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L79)

___

### studentInstitutions

• **studentInstitutions**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:71](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L71)

___

### titles

• **titles**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:88](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L88)

___

### totalWorks

• **totalWorks**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:92](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L92)

___

### trialCount

• **trialCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:78](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L78)

___

### trials

• **trials**: { `primaryCompletionDate`: *number* ; `startDate`: *number*  }[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:94](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L94)
