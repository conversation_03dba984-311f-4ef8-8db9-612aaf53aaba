[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / MetricQueryPreMap

# Interface: MetricQueryPreMap

[index](../modules/index.md).MetricQueryPreMap

## Hierarchy

* **MetricQueryPreMap**

## Table of contents

### Properties

- [dateFilterPath](index.metricquerypremap.md#datefilterpath)
- [filterPaths](index.metricquerypremap.md#filterpaths)
- [innerHits](index.metricquerypremap.md#innerhits)
- [matchFields](index.metricquerypremap.md#matchfields)
- [metric](index.metricquerypremap.md#metric)
- [minFieldLevel](index.metricquerypremap.md#minfieldlevel)
- [nested](index.metricquerypremap.md#nested)
- [scoreMode](index.metricquerypremap.md#scoremode)

## Properties

### dateFilterPath

• `Optional` **dateFilterPath**: *undefined* \| *string*[]

Defined in: [packages/sdk/src/flow/types.ts:40](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L40)

___

### filterPaths

• **filterPaths**: { `fsiKey`: *string* ; `indexPath`: *string*  }[]

Defined in: [packages/sdk/src/flow/types.ts:48](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L48)

___

### innerHits

• **innerHits**: *any*

Defined in: [packages/sdk/src/flow/types.ts:38](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L38)

___

### matchFields

• **matchFields**: *undefined* \| *string*[]

Defined in: [packages/sdk/src/flow/types.ts:49](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L49)

___

### metric

• **metric**: [*Metrics*](../enums/flow_types.metrics.md)

Defined in: [packages/sdk/src/flow/types.ts:36](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L36)

___

### minFieldLevel

• **minFieldLevel**: *undefined* \| { `field?`: *undefined* \| *string* ; `fsiPath`: *string* ; `scoreMode?`: *undefined* \| *string*  }

Defined in: [packages/sdk/src/flow/types.ts:41](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L41)

___

### nested

• **nested**: [*Metrics*](../enums/flow_types.metrics.md)

Defined in: [packages/sdk/src/flow/types.ts:37](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L37)

___

### scoreMode

• **scoreMode**: [*ScoreTypes*](../enums/flow_types.scoretypes.md)

Defined in: [packages/sdk/src/flow/types.ts:39](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L39)
