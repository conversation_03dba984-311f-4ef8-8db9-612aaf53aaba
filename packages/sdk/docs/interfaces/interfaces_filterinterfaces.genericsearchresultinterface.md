[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / GenericSearchResultInterface

# Interface: GenericSearchResultInterface

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).GenericSearchResultInterface

## Hierarchy

* **GenericSearchResultInterface**

## Table of contents

### Properties

- [id](interfaces_filterinterfaces.genericsearchresultinterface.md#id)
- [internalCount](interfaces_filterinterfaces.genericsearchresultinterface.md#internalcount)
- [type](interfaces_filterinterfaces.genericsearchresultinterface.md#type)

## Properties

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:313](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L313)

___

### internalCount

• `Optional` **internalCount**: *undefined* \| *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:315](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L315)

___

### type

• **type**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:314](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L314)
