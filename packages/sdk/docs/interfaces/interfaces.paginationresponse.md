[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / PaginationResponse

# Interface: PaginationResponse<T\>

[interfaces](../modules/interfaces.md).PaginationResponse

## Type parameters

Name |
------ |
`T` |

## Hierarchy

* [*Pagination*](interfaces_pagination.pagination.md)

  ↳ **PaginationResponse**

## Table of contents

### Properties

- [from](interfaces.paginationresponse.md#from)
- [pageSize](interfaces.paginationresponse.md#pagesize)
- [results](interfaces.paginationresponse.md#results)
- [total](interfaces.paginationresponse.md#total)

## Properties

### from

• **from**: *number*

The first index of the result set

Inherited from: [Pagination](interfaces_pagination.pagination.md).[from](interfaces_pagination.pagination.md#from)

Defined in: [packages/sdk/src/interfaces/pagination.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L5)

___

### pageSize

• **pageSize**: *number*

The page size

Inherited from: [Pagination](interfaces_pagination.pagination.md).[pageSize](interfaces_pagination.pagination.md#pagesize)

Defined in: [packages/sdk/src/interfaces/pagination.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L9)

___

### results

• **results**: T[]

Defined in: [packages/sdk/src/interfaces/pagination.ts:17](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L17)

___

### total

• **total**: *number*

The total number of results

Inherited from: [Pagination](interfaces_pagination.pagination.md).[total](interfaces_pagination.pagination.md#total)

Defined in: [packages/sdk/src/interfaces/pagination.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L13)
