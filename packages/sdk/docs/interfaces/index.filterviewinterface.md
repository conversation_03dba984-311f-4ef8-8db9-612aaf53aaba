[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / FilterViewInterface

# Interface: FilterViewInterface

[index](../modules/index.md).FilterViewInterface

## Hierarchy

* **FilterViewInterface**

## Table of contents

### Properties

- [filters](index.filterviewinterface.md#filters)
- [title](index.filterviewinterface.md#title)

## Properties

### filters

• **filters**: [*FilterInterface*](interfaces_filterinterfaces.filterinterface.md)[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:273](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L273)

___

### title

• **title**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:274](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L274)
