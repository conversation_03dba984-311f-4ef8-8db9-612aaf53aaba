[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / ClaimsResource

# Interface: ClaimsResource

[index](../modules/index.md).ClaimsResource

## Hierarchy

* **ClaimsResource**

## Table of contents

### Methods

- [getDiagnoses](index.claimsresource.md#getdiagnoses)
- [getProcedures](index.claimsresource.md#getprocedures)
- [isReady](index.claimsresource.md#isready)

## Methods

### getDiagnoses

▸ **getDiagnoses**(`personId`: *string*, `terms?`: *string*[], `page?`: [*PaginationOptions*](interfaces_pagination.paginationoptions.md), `sort?`: [*DiagnosesSortOptions*](../modules/resources_claimsresource.md#diagnosessortoptions)): *Promise*<[*PaginationResponse*](interfaces_pagination.paginationresponse.md)<[*ClaimsDiagnosis*](interfaces_claims.claimsdiagnosis.md)\>\>

Search for diagnosis claims for a given person.

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`personId` | *string* | The id of the person   |
`terms?` | *string*[] | Search terms   |
`page?` | [*PaginationOptions*](interfaces_pagination.paginationoptions.md) | Pagination options   |
`sort?` | [*DiagnosesSortOptions*](../modules/resources_claimsresource.md#diagnosessortoptions) | Sort options    |

**Returns:** *Promise*<[*PaginationResponse*](interfaces_pagination.paginationresponse.md)<[*ClaimsDiagnosis*](interfaces_claims.claimsdiagnosis.md)\>\>

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:40](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L40)

___

### getProcedures

▸ **getProcedures**(`personId`: *string*, `terms?`: *string*[], `page?`: [*PaginationOptions*](interfaces_pagination.paginationoptions.md), `sort?`: [*ProceduresSortOptions*](../modules/resources_claimsresource.md#proceduressortoptions)): *Promise*<[*PaginationResponse*](interfaces_pagination.paginationresponse.md)<[*ClaimsProcedure*](interfaces_claims.claimsprocedure.md)\>\>

Search for procedure claims for a given person.

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`personId` | *string* | The id of the person   |
`terms?` | *string*[] | Search terms   |
`page?` | [*PaginationOptions*](interfaces_pagination.paginationoptions.md) | Pagination options   |
`sort?` | [*ProceduresSortOptions*](../modules/resources_claimsresource.md#proceduressortoptions) | Sort options    |

**Returns:** *Promise*<[*PaginationResponse*](interfaces_pagination.paginationresponse.md)<[*ClaimsProcedure*](interfaces_claims.claimsprocedure.md)\>\>

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:55](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L55)

___

### isReady

▸ **isReady**(): *Promise*<*boolean*\>

**Returns:** *Promise*<*boolean*\>

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:30](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L30)
