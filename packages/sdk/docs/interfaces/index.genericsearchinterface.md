[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / GenericSearchInterface

# Interface: GenericSearchInterface

[index](../modules/index.md).GenericSearchInterface

## Hierarchy

* **GenericSearchInterface**

## Table of contents

### Properties

- [combinedSearch](index.genericsearchinterface.md#combinedsearch)
- [filters](index.genericsearchinterface.md#filters)
- [personId](index.genericsearchinterface.md#personid)
- [terms](index.genericsearchinterface.md#terms)

## Properties

### combinedSearch

• `Optional` **combinedSearch**: *undefined* \| *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:285](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L285)

___

### filters

• `Optional` **filters**: *undefined* \| [*GenericSearchFilterInterface*](interfaces_filterinterfaces.genericsearchfilterinterface.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:284](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L284)

___

### personId

• **personId**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:283](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L283)

___

### terms

• **terms**: *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:282](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L282)
