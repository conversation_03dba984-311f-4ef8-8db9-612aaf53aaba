[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / ClaimsFilter

# Interface: ClaimsFilter

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).ClaimsFilter

## Hierarchy

* [*GenericFilterInterface*](interfaces_filterinterfaces.genericfilterinterface.md)

  ↳ **ClaimsFilter**

## Table of contents

### Properties

- [index](interfaces_filterinterfaces.claimsfilter.md#index)
- [keyName](interfaces_filterinterfaces.claimsfilter.md#keyname)
- [options](interfaces_filterinterfaces.claimsfilter.md#options)
- [path](interfaces_filterinterfaces.claimsfilter.md#path)
- [query](interfaces_filterinterfaces.claimsfilter.md#query)
- [type](interfaces_filterinterfaces.claimsfilter.md#type)
- [values](interfaces_filterinterfaces.claimsfilter.md#values)

## Properties

### index

• **index**: [*IndexTypes*](../enums/interfaces_filterinterfaces.indextypes.md)

The name of the index

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[index](interfaces_filterinterfaces.genericfilterinterface.md#index)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:346](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L346)

___

### keyName

• **keyName**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:390](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L390)

___

### options

• **options**: Option[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:388](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L388)

___

### path

• **path**: *string*

The dot-delimited path within the elastic document

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[path](interfaces_filterinterfaces.genericfilterinterface.md#path)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:342](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L342)

___

### query

• **query**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:391](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L391)

___

### type

• **type**: [*FilterTypesEnum*](../enums/interfaces_filterinterfaces.filtertypesenum.md)

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[type](interfaces_filterinterfaces.genericfilterinterface.md#type)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:338](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L338)

___

### values

• **values**: *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:389](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L389)
