[@h1nyc/search-sdk](../README.md) / [indices/peopleIndex](../modules/indices_peopleindex.md) / EsIndexFacetMeta

# Interface: EsIndexFacetMeta

[indices/peopleIndex](../modules/indices_peopleindex.md).EsIndexFacetMeta

A "facet" in this context is a set of counts of unique values for a given filter field.
The facets in traditional search are built using aggregations in ES, which will be populated
by the data below. A simple structure to capture the field name, a boolean flag for enabling a
facet and an optional facet name overwrite (which can be used to match up data or for display
purposes) is enough to cover what is needed.

## Hierarchy

* **EsIndexFacetMeta**

## Table of contents

### Properties

- [enabled](indices_peopleindex.esindexfacetmeta.md#enabled)
- [field](indices_peopleindex.esindexfacetmeta.md#field)
- [name](indices_peopleindex.esindexfacetmeta.md#name)

## Properties

### enabled

• **enabled**: *boolean*

Defined in: [packages/sdk/src/indices/peopleIndex.ts:386](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L386)

___

### field

• **field**: *string*

Defined in: [packages/sdk/src/indices/peopleIndex.ts:385](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L385)

___

### name

• `Optional` **name**: *undefined* \| *string*

Defined in: [packages/sdk/src/indices/peopleIndex.ts:387](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L387)
