[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / SearchResults_shards

# Interface: SearchResults\_shards

[index](../modules/index.md).SearchResults_shards

## Hierarchy

* **SearchResults_shards**

## Table of contents

### Properties

- [failed](index.searchresults_shards.md#failed)
- [skipped](index.searchresults_shards.md#skipped)
- [successful](index.searchresults_shards.md#successful)
- [total](index.searchresults_shards.md#total)

## Properties

### failed

• **failed**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:49](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L49)

___

### skipped

• **skipped**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:48](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L48)

___

### successful

• **successful**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:47](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L47)

___

### total

• **total**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:46](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L46)
