[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / NameSuggestFilterSearchInterface

# Interface: NameSuggestFilterSearchInterface

[index](../modules/index.md).NameSuggestFilterSearchInterface

## Hierarchy

* **NameSuggestFilterSearchInterface**

## Table of contents

### Properties

- [activeFilters](index.namesuggestfiltersearchinterface.md#activefilters)
- [filters](index.namesuggestfiltersearchinterface.md#filters)
- [projectId](index.namesuggestfiltersearchinterface.md#projectid)
- [query](index.namesuggestfiltersearchinterface.md#query)
- [sortBy](index.namesuggestfiltersearchinterface.md#sortby)
- [userId](index.namesuggestfiltersearchinterface.md#userid)

## Properties

### activeFilters

• `Optional` **activeFilters**: *undefined* \| [*ActiveFilterBreakDown*](interfaces_filterinterfaces.activefilterbreakdown.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:251](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L251)

___

### filters

• `Optional` **filters**: *undefined* \| [*FilterInterface*](interfaces_filterinterfaces.filterinterface.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:249](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L249)

___

### projectId

• **projectId**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:248](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L248)

___

### query

• **query**: *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:247](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L247)

___

### sortBy

• `Optional` **sortBy**: *undefined* \| [*WeightedSortBy*](interfaces_filterinterfaces.weightedsortby.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:250](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L250)

___

### userId

• **userId**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:246](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L246)
