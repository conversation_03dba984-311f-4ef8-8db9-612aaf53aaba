[@h1nyc/search-sdk](../README.md) / [interfaces/KOLResultInterfaces](../modules/interfaces_kolresultinterfaces.md) / ResultCardAffiliation

# Interface: ResultCardAffiliation

[interfaces/KOLResultInterfaces](../modules/interfaces_kolresultinterfaces.md).ResultCardAffiliation

## Hierarchy

* **ResultCardAffiliation**

## Table of contents

### Properties

- [address](interfaces_kolresultinterfaces.resultcardaffiliation.md#address)
- [affiliationType](interfaces_kolresultinterfaces.resultcardaffiliation.md#affiliationtype)
- [department](interfaces_kolresultinterfaces.resultcardaffiliation.md#department)
- [id](interfaces_kolresultinterfaces.resultcardaffiliation.md#id)
- [institution](interfaces_kolresultinterfaces.resultcardaffiliation.md#institution)
- [isPastAffiliation](interfaces_kolresultinterfaces.resultcardaffiliation.md#ispastaffiliation)
- [school](interfaces_kolresultinterfaces.resultcardaffiliation.md#school)
- [titles](interfaces_kolresultinterfaces.resultcardaffiliation.md#titles)

## Properties

### address

• **address**: *null* \| { `city`: *null* \| *string* ; `country`: *null* \| *string* ; `id`: *string* ; `state`: *null* \| *string* ; `street1`: *null* \| *string* ; `street2`: *null* \| *string* ; `street3`: *null* \| *string* ; `zip`: *null* \| *string*  }

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:48](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L48)

___

### affiliationType

• **affiliationType**: *null* \| *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:38](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L38)

___

### department

• **department**: *null* \| *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:36](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L36)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:34](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L34)

___

### institution

• **institution**: { `id`: *string* ; `name`: *null* \| *string* ; `ultimateParent`: *null* \| { `id`: *string* ; `name`: *null* \| *string*  }  }

#### Type declaration:

Name | Type |
------ | ------ |
`id` | *string* |
`name` | *null* \| *string* |
`ultimateParent` | *null* \| { `id`: *string* ; `name`: *null* \| *string*  } |

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:39](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L39)

___

### isPastAffiliation

• `Optional` **isPastAffiliation**: *undefined* \| *boolean*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:47](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L47)

___

### school

• **school**: *null* \| *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:37](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L37)

___

### titles

• **titles**: *string*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:35](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L35)
