[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / NumberFilter

# Interface: NumberFilter

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).NumberFilter

## Hierarchy

* [*GenericFilterInterface*](interfaces_filterinterfaces.genericfilterinterface.md)

  ↳ **NumberFilter**

## Table of contents

### Properties

- [index](interfaces_filterinterfaces.numberfilter.md#index)
- [path](interfaces_filterinterfaces.numberfilter.md#path)
- [type](interfaces_filterinterfaces.numberfilter.md#type)
- [value](interfaces_filterinterfaces.numberfilter.md#value)

## Properties

### index

• **index**: [*IndexTypes*](../enums/interfaces_filterinterfaces.indextypes.md)

The name of the index

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[index](interfaces_filterinterfaces.genericfilterinterface.md#index)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:346](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L346)

___

### path

• **path**: *string*

The dot-delimited path within the elastic document

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[path](interfaces_filterinterfaces.genericfilterinterface.md#path)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:342](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L342)

___

### type

• **type**: [*FilterTypesEnum*](../enums/interfaces_filterinterfaces.filtertypesenum.md)

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[type](interfaces_filterinterfaces.genericfilterinterface.md#type)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:338](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L338)

___

### value

• **value**: *null* \| *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:379](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L379)
