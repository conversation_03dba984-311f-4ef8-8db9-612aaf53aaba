[@h1nyc/search-sdk](../README.md) / [api](../modules/api.md) / SearchResultsRootObject

# Interface: SearchResultsRootObject

[api](../modules/api.md).SearchResultsRootObject

## Hierarchy

* **SearchResultsRootObject**

## Table of contents

### Properties

- [\_shards](api.searchresultsrootobject.md#_shards)
- [aggregations](api.searchresultsrootobject.md#aggregations)
- [hits](api.searchresultsrootobject.md#hits)
- [timed\_out](api.searchresultsrootobject.md#timed_out)
- [took](api.searchresultsrootobject.md#took)

## Properties

### \_shards

• **\_shards**: [*SearchResults\_shards*](api_response_responseinterfaces.searchresults_shards.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:41](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L41)

___

### aggregations

• **aggregations**: AggregationFilterCount

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:43](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L43)

___

### hits

• **hits**: [*SearchResultsHits*](api_response_responseinterfaces.searchresultshits.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:42](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L42)

___

### timed\_out

• **timed\_out**: *boolean*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:40](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L40)

___

### took

• **took**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:39](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L39)
