[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / SearchResultsPublication_social_media

# Interface: SearchResultsPublication\_social\_media

[index](../modules/index.md).SearchResultsPublication_social_media

## Hierarchy

* **SearchResultsPublication_social_media**

## Table of contents

### Properties

- [hits](index.searchresultspublication_social_media.md#hits)

## Properties

### hits

• **hits**: [*SearchResultsHits*](api_response_responseinterfaces.searchresultshits.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:123](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L123)
