[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / ElasticSearchDocuments

# Interface: ElasticSearchDocuments

[index](../modules/index.md).ElasticSearchDocuments

## Hierarchy

* **ElasticSearchDocuments**

## Table of contents

### Properties

- [doc](index.elasticsearchdocuments.md#doc)
- [type](index.elasticsearchdocuments.md#type)

## Properties

### doc

• **doc**: *any*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:74](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L74)

___

### type

• **type**: [*GenericSearchEnum*](../enums/interfaces_elasticdocuments.genericsearchenum.md)

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:73](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L73)
