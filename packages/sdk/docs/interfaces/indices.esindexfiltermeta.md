[@h1nyc/search-sdk](../README.md) / [indices](../modules/indices.md) / EsIndexFilterMeta

# Interface: EsIndexFilterMeta

[indices](../modules/indices.md).EsIndexFilterMeta

## Hierarchy

* **EsIndexFilterMeta**

## Table of contents

### Properties

- [filterPath](indices.esindexfiltermeta.md#filterpath)
- [noneValue](indices.esindexfiltermeta.md#nonevalue)
- [path](indices.esindexfiltermeta.md#path)
- [paths](indices.esindexfiltermeta.md#paths)
- [type](indices.esindexfiltermeta.md#type)

## Properties

### filterPath

• `Optional` **filterPath**: *undefined* \| *string*

The contains the path to the value in SavedSearchFilterValues.

Defined in: [packages/sdk/src/indices/peopleIndex.ts:37](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L37)

___

### noneValue

• `Optional` **noneValue**: *undefined* \| *string*

If this value exists, then apply the 'must_not' logic for this value in the filter.

Defined in: [packages/sdk/src/indices/peopleIndex.ts:42](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L42)

___

### path

• `Optional` **path**: *undefined* \| *string*

The contains the path(s) to the value in the ES index relative to the path of this
this field.

For example, if "field.path" has "diagnoses" and "filter.path" has "terms"
then the full pathname would be "diagnoses.terms".

Defined in: [packages/sdk/src/indices/peopleIndex.ts:31](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L31)

___

### paths

• `Optional` **paths**: *undefined* \| *string*[]

Defined in: [packages/sdk/src/indices/peopleIndex.ts:32](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L32)

___

### type

• **type**: *keyword* \| *worksDate* \| *terms* \| *minTotal* \| *date*

This is the type of elastic search query that is used by this filter.

Defined in: [packages/sdk/src/indices/peopleIndex.ts:22](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L22)
