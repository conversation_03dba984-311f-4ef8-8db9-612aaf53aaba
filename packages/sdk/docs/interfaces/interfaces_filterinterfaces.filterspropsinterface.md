[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / FiltersPropsInterface

# Interface: FiltersPropsInterface

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).FiltersPropsInterface

## Hierarchy

* **FiltersPropsInterface**

## Table of contents

### Properties

- [filters](interfaces_filterinterfaces.filterspropsinterface.md#filters)
- [title](interfaces_filterinterfaces.filterspropsinterface.md#title)

## Properties

### filters

• `Optional` **filters**: *undefined* \| [*FilterInterface*](interfaces_filterinterfaces.filterinterface.md)[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:268](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L268)

___

### title

• `Optional` **title**: *undefined* \| *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:267](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L267)
