[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / PeopleSearchDocument

# Interface: PeopleSearchDocument

[interfaces](../modules/interfaces.md).PeopleSearchDocument

## Hierarchy

* **PeopleSearchDocument**

## Table of contents

### Properties

- [\_id](interfaces.peoplesearchdocument.md#_id)
- [collaborators](interfaces.peoplesearchdocument.md#collaborators)
- [departments](interfaces.peoplesearchdocument.md#departments)
- [designations](interfaces.peoplesearchdocument.md#designations)
- [id](interfaces.peoplesearchdocument.md#id)
- [institutions](interfaces.peoplesearchdocument.md#institutions)
- [locations](interfaces.peoplesearchdocument.md#locations)
- [name](interfaces.peoplesearchdocument.md#name)
- [names](interfaces.peoplesearchdocument.md#names)
- [personNames](interfaces.peoplesearchdocument.md#personnames)
- [projects](interfaces.peoplesearchdocument.md#projects)
- [schools](interfaces.peoplesearchdocument.md#schools)
- [scores](interfaces.peoplesearchdocument.md#scores)
- [specialty](interfaces.peoplesearchdocument.md#specialty)
- [titles](interfaces.peoplesearchdocument.md#titles)

## Properties

### \_id

• **\_id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L12)

___

### collaborators

• **collaborators**: *number*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:35](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L35)

___

### departments

• **departments**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:31](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L31)

___

### designations

• **designations**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:27](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L27)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:11](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L11)

___

### institutions

• **institutions**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:33](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L33)

___

### locations

• **locations**: (*null* \| { `city`: *string* ; `state`: *string*  })[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:29](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L29)

___

### name

• **name**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L13)

___

### names

• **names**: { `first`: *string* ; `last`: *string* ; `middle`: *string*  }

#### Type declaration:

Name | Type |
------ | ------ |
`first` | *string* |
`last` | *string* |
`middle` | *string* |

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:21](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L21)

___

### personNames

• **personNames**: { `first`: *string* ; `id`: *string* ; `last`: *string* ; `middle`: *string* ; `projectIds`: *string*[]  }[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:14](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L14)

___

### projects

• **projects**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:34](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L34)

___

### schools

• **schools**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:32](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L32)

___

### scores

• **scores**: { `projectId`: *string* ; `score`: *number*  }[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:30](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L30)

___

### specialty

• **specialty**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:28](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L28)

___

### titles

• **titles**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:26](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L26)
