[@h1nyc/search-sdk](../README.md) / [interfaces/searchInterfaces](../modules/interfaces_searchinterfaces.md) / RootSearchObject

# Interface: RootSearchObject

[interfaces/searchInterfaces](../modules/interfaces_searchinterfaces.md).RootSearchObject

## Hierarchy

* **RootSearchObject**

## Table of contents

### Properties

- [\_shards](interfaces_searchinterfaces.rootsearchobject.md#_shards)
- [aggregations](interfaces_searchinterfaces.rootsearchobject.md#aggregations)
- [hits](interfaces_searchinterfaces.rootsearchobject.md#hits)
- [timed\_out](interfaces_searchinterfaces.rootsearchobject.md#timed_out)
- [took](interfaces_searchinterfaces.rootsearchobject.md#took)

## Properties

### \_shards

• **\_shards**: ShardsNameSearch

Defined in: [packages/sdk/src/interfaces/searchInterfaces.ts:149](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/searchInterfaces.ts#L149)

___

### aggregations

• **aggregations**: AggregationFilterCount

Defined in: [packages/sdk/src/interfaces/searchInterfaces.ts:151](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/searchInterfaces.ts#L151)

___

### hits

• **hits**: Hits

Defined in: [packages/sdk/src/interfaces/searchInterfaces.ts:150](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/searchInterfaces.ts#L150)

___

### timed\_out

• **timed\_out**: *boolean*

Defined in: [packages/sdk/src/interfaces/searchInterfaces.ts:148](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/searchInterfaces.ts#L148)

___

### took

• **took**: *number*

Defined in: [packages/sdk/src/interfaces/searchInterfaces.ts:147](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/searchInterfaces.ts#L147)
