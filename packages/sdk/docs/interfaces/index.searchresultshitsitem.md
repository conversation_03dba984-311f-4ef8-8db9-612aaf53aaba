[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / SearchResultsHitsItem

# Interface: SearchResultsHitsItem

[index](../modules/index.md).SearchResultsHitsItem

## Hierarchy

* **SearchResultsHitsItem**

## Table of contents

### Properties

- [\_id](index.searchresultshitsitem.md#_id)
- [\_index](index.searchresultshitsitem.md#_index)
- [\_nested](index.searchresultshitsitem.md#_nested)
- [\_score](index.searchresultshitsitem.md#_score)
- [\_source](index.searchresultshitsitem.md#_source)
- [\_type](index.searchresultshitsitem.md#_type)
- [fields](index.searchresultshitsitem.md#fields)
- [inner\_hits](index.searchresultshitsitem.md#inner_hits)

## Properties

### \_id

• **\_id**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:59](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L59)

___

### \_index

• **\_index**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:57](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L57)

___

### \_nested

• `Optional` **\_nested**: *undefined* \| [*SearchResults\_nested*](api_response_responseinterfaces.searchresults_nested.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:63](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L63)

___

### \_score

• **\_score**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:60](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L60)

___

### \_source

• `Optional` **\_source**: *undefined* \| [*SearchResults\_source*](api_response_responseinterfaces.searchresults_source.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:61](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L61)

___

### \_type

• **\_type**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:58](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L58)

___

### fields

• `Optional` **fields**: *undefined* \| [*SearchResultsFields*](api_response_responseinterfaces.searchresultsfields.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:64](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L64)

___

### inner\_hits

• `Optional` **inner\_hits**: *undefined* \| [*SearchResultsInner\_hits*](api_response_responseinterfaces.searchresultsinner_hits.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:62](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L62)
