[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / YearRangeFilter

# Interface: YearRangeFilter

[interfaces](../modules/interfaces.md).YearRangeFilter

## Hierarchy

* **YearRangeFilter**

## Table of contents

### Properties

- [max](interfaces.yearrangefilter.md#max)
- [min](interfaces.yearrangefilter.md#min)

## Properties

### max

• **max**: *null* \| *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:202](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L202)

___

### min

• **min**: *null* \| *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:201](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L201)
