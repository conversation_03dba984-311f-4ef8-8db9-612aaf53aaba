[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / PersonName

# Interface: PersonName

[index](../modules/index.md).PersonName

## Hierarchy

* **PersonName**

## Table of contents

### Properties

- [first](index.personname.md#first)
- [id](index.personname.md#id)
- [last](index.personname.md#last)
- [middle](index.personname.md#middle)
- [projectIds](index.personname.md#projectids)

## Properties

### first

• **first**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:79](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L79)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:82](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L82)

___

### last

• **last**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:81](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L81)

___

### middle

• **middle**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:80](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L80)

___

### projectIds

• **projectIds**: *string*[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:78](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L78)
