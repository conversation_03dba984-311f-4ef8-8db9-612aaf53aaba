[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / LiteProfile

# Interface: LiteProfile

[interfaces](../modules/interfaces.md).LiteProfile

## Hierarchy

* **LiteProfile**

## Table of contents

### Properties

- [city](interfaces.liteprofile.md#city)
- [designations](interfaces.liteprofile.md#designations)
- [email](interfaces.liteprofile.md#email)
- [firstName](interfaces.liteprofile.md#firstname)
- [id](interfaces.liteprofile.md#id)
- [lastName](interfaces.liteprofile.md#lastname)
- [payments](interfaces.liteprofile.md#payments)
- [phoneNumbers](interfaces.liteprofile.md#phonenumbers)
- [state](interfaces.liteprofile.md#state)
- [titles](interfaces.liteprofile.md#titles)

## Properties

### city

• **city**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:119](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L119)

___

### designations

• **designations**: *string*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:123](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L123)

___

### email

• **email**: *string*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:122](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L122)

___

### firstName

• **firstName**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:116](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L116)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:115](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L115)

___

### lastName

• **lastName**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:117](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L117)

___

### payments

• **payments**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:124](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L124)

___

### phoneNumbers

• **phoneNumbers**: *string*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:118](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L118)

___

### state

• **state**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:120](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L120)

___

### titles

• **titles**: *string*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:121](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L121)
