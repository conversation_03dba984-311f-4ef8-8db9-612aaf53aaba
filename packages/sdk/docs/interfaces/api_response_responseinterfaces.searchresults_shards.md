[@h1nyc/search-sdk](../README.md) / [api/response/ResponseInterfaces](../modules/api_response_responseinterfaces.md) / SearchResults_shards

# Interface: SearchResults\_shards

[api/response/ResponseInterfaces](../modules/api_response_responseinterfaces.md).SearchResults_shards

## Hierarchy

* **SearchResults_shards**

## Table of contents

### Properties

- [failed](api_response_responseinterfaces.searchresults_shards.md#failed)
- [skipped](api_response_responseinterfaces.searchresults_shards.md#skipped)
- [successful](api_response_responseinterfaces.searchresults_shards.md#successful)
- [total](api_response_responseinterfaces.searchresults_shards.md#total)

## Properties

### failed

• **failed**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:49](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L49)

___

### skipped

• **skipped**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:48](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L48)

___

### successful

• **successful**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:47](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L47)

___

### total

• **total**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:46](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L46)
