[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / PersonsDocumentSummarySearchInterface

# Interface: PersonsDocumentSummarySearchInterface

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).PersonsDocumentSummarySearchInterface

## Hierarchy

* **PersonsDocumentSummarySearchInterface**

## Table of contents

### Properties

- [personIds](interfaces_filterinterfaces.personsdocumentsummarysearchinterface.md#personids)

## Properties

### personIds

• **personIds**: *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:298](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L298)
