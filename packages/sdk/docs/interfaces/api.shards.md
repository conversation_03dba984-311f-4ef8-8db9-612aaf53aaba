[@h1nyc/search-sdk](../README.md) / [api](../modules/api.md) / Shards

# Interface: Shards

[api](../modules/api.md).Shards

## Hierarchy

* **Shards**

## Table of contents

### Properties

- [failed](api.shards.md#failed)
- [skipped](api.shards.md#skipped)
- [successful](api.shards.md#successful)
- [total](api.shards.md#total)

## Properties

### failed

• **failed**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:15](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L15)

___

### skipped

• **skipped**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:14](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L14)

___

### successful

• **successful**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L13)

___

### total

• **total**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L12)
