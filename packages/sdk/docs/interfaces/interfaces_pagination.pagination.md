[@h1nyc/search-sdk](../README.md) / [interfaces/pagination](../modules/interfaces_pagination.md) / Pagination

# Interface: Pagination

[interfaces/pagination](../modules/interfaces_pagination.md).Pagination

## Hierarchy

* **Pagination**

  ↳ [*PaginationResponse*](interfaces_pagination.paginationresponse.md)

  ↳ [*PaginationResponse*](index.paginationresponse.md)

  ↳ [*PaginationResponse*](interfaces.paginationresponse.md)

## Table of contents

### Properties

- [from](interfaces_pagination.pagination.md#from)
- [pageSize](interfaces_pagination.pagination.md#pagesize)
- [total](interfaces_pagination.pagination.md#total)

## Properties

### from

• **from**: *number*

The first index of the result set

Defined in: [packages/sdk/src/interfaces/pagination.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L5)

___

### pageSize

• **pageSize**: *number*

The page size

Defined in: [packages/sdk/src/interfaces/pagination.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L9)

___

### total

• **total**: *number*

The total number of results

Defined in: [packages/sdk/src/interfaces/pagination.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L13)
