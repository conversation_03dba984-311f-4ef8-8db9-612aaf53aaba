[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / SavedSearchBy

# Interface: SavedSearchBy

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).SavedSearchBy

## Hierarchy

* [*SavedSearchByConstructor*](interfaces_filterinterfaces.savedsearchbyconstructor.md)

  ↳ **SavedSearchBy**

## Table of contents

### Properties

- [citation](interfaces_filterinterfaces.savedsearchby.md#citation)
- [congress](interfaces_filterinterfaces.savedsearchby.md#congress)
- [created](interfaces_filterinterfaces.savedsearchby.md#created)
- [diagnoses](interfaces_filterinterfaces.savedsearchby.md#diagnoses)
- [filters](interfaces_filterinterfaces.savedsearchby.md#filters)
- [grant](interfaces_filterinterfaces.savedsearchby.md#grant)
- [id](interfaces_filterinterfaces.savedsearchby.md#id)
- [microBloggingCount](interfaces_filterinterfaces.savedsearchby.md#microbloggingcount)
- [name](interfaces_filterinterfaces.savedsearchby.md#name)
- [payment](interfaces_filterinterfaces.savedsearchby.md#payment)
- [private](interfaces_filterinterfaces.savedsearchby.md#private)
- [procedures](interfaces_filterinterfaces.savedsearchby.md#procedures)
- [publication](interfaces_filterinterfaces.savedsearchby.md#publication)
- [referralsReceived](interfaces_filterinterfaces.savedsearchby.md#referralsreceived)
- [referralsSent](interfaces_filterinterfaces.savedsearchby.md#referralssent)
- [searchTerms](interfaces_filterinterfaces.savedsearchby.md#searchterms)
- [trial](interfaces_filterinterfaces.savedsearchby.md#trial)
- [userId](interfaces_filterinterfaces.savedsearchby.md#userid)

## Properties

### citation

• **citation**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[citation](interfaces_filterinterfaces.savedsearchbyconstructor.md#citation)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:171](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L171)

___

### congress

• **congress**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[congress](interfaces_filterinterfaces.savedsearchbyconstructor.md#congress)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:175](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L175)

___

### created

• `Optional` **created**: *undefined* \| *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:193](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L193)

___

### diagnoses

• **diagnoses**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[diagnoses](interfaces_filterinterfaces.savedsearchbyconstructor.md#diagnoses)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:177](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L177)

___

### filters

• **filters**: [*SavedSearchFilterValues*](interfaces_filterinterfaces.savedsearchfiltervalues.md)

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[filters](interfaces_filterinterfaces.savedsearchbyconstructor.md#filters)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:188](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L188)

___

### grant

• **grant**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[grant](interfaces_filterinterfaces.savedsearchbyconstructor.md#grant)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:176](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L176)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:192](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L192)

___

### microBloggingCount

• **microBloggingCount**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[microBloggingCount](interfaces_filterinterfaces.savedsearchbyconstructor.md#microbloggingcount)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:172](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L172)

___

### name

• **name**: *string*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[name](interfaces_filterinterfaces.savedsearchbyconstructor.md#name)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:184](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L184)

___

### payment

• **payment**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[payment](interfaces_filterinterfaces.savedsearchbyconstructor.md#payment)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:174](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L174)

___

### private

• **private**: *boolean*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[private](interfaces_filterinterfaces.savedsearchbyconstructor.md#private)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:186](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L186)

___

### procedures

• **procedures**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[procedures](interfaces_filterinterfaces.savedsearchbyconstructor.md#procedures)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:178](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L178)

___

### publication

• **publication**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[publication](interfaces_filterinterfaces.savedsearchbyconstructor.md#publication)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:170](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L170)

___

### referralsReceived

• **referralsReceived**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[referralsReceived](interfaces_filterinterfaces.savedsearchbyconstructor.md#referralsreceived)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:179](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L179)

___

### referralsSent

• **referralsSent**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[referralsSent](interfaces_filterinterfaces.savedsearchbyconstructor.md#referralssent)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:180](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L180)

___

### searchTerms

• **searchTerms**: *string*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[searchTerms](interfaces_filterinterfaces.savedsearchbyconstructor.md#searchterms)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:185](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L185)

___

### trial

• **trial**: *number*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[trial](interfaces_filterinterfaces.savedsearchbyconstructor.md#trial)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:173](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L173)

___

### userId

• **userId**: *string*

Inherited from: [SavedSearchByConstructor](interfaces_filterinterfaces.savedsearchbyconstructor.md).[userId](interfaces_filterinterfaces.savedsearchbyconstructor.md#userid)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:187](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L187)
