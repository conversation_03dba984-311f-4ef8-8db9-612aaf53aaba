[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / SavedSearchFilterValues

# Interface: SavedSearchFilterValues

[index](../modules/index.md).SavedSearchFilterValues

## Hierarchy

* **SavedSearchFilterValues**

## Indexable

▪ [name: *string*]: [*SearchFilterValue*](../modules/interfaces_filterinterfaces.md#searchfiltervalue)

## Table of contents

### Properties

- [city](index.savedsearchfiltervalues.md#city)
- [claims](index.savedsearchfiltervalues.md#claims)
- [congresses](index.savedsearchfiltervalues.md#congresses)
- [country](index.savedsearchfiltervalues.md#country)
- [dateRange](index.savedsearchfiltervalues.md#daterange)
- [graduationYearRange](index.savedsearchfiltervalues.md#graduationyearrange)
- [grants](index.savedsearchfiltervalues.md#grants)
- [institution](index.savedsearchfiltervalues.md#institution)
- [npi](index.savedsearchfiltervalues.md#npi)
- [pastAndPresentWorkInstitutions](index.savedsearchfiltervalues.md#pastandpresentworkinstitutions)
- [payments](index.savedsearchfiltervalues.md#payments)
- [presentWorkInstitutions](index.savedsearchfiltervalues.md#presentworkinstitutions)
- [profile](index.savedsearchfiltervalues.md#profile)
- [publications](index.savedsearchfiltervalues.md#publications)
- [referrals](index.savedsearchfiltervalues.md#referrals)
- [specialty](index.savedsearchfiltervalues.md#specialty)
- [state](index.savedsearchfiltervalues.md#state)
- [studentInstitutions](index.savedsearchfiltervalues.md#studentinstitutions)
- [tags](index.savedsearchfiltervalues.md#tags)
- [trials](index.savedsearchfiltervalues.md#trials)
- [zipCode](index.savedsearchfiltervalues.md#zipcode)

## Properties

### city

• `Optional` **city**: *undefined* \| *null* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:466](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L466)

___

### claims

• `Optional` **claims**: *undefined* \| *null* \| { `diagnoses?`: *undefined* \| *null* \| *string*[] ; `diagnosesMinCount?`: *undefined* \| *null* \| *number* ; `procedures?`: *undefined* \| *null* \| *string*[] ; `proceduresMinCount?`: *undefined* \| *null* \| *number*  }

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:503](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L503)

___

### congresses

• `Optional` **congresses**: *undefined* \| *null* \| { `minCount?`: *undefined* \| *null* \| *number* ; `name?`: *undefined* \| *null* \| *string*[] ; `organizerName?`: *undefined* \| *null* \| *string*[]  }

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:498](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L498)

___

### country

• `Optional` **country**: *undefined* \| *null* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:464](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L464)

___

### dateRange

• `Optional` **dateRange**: *undefined* \| *null* \| *number*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:476](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L476)

___

### graduationYearRange

• `Optional` **graduationYearRange**: *undefined* \| *null* \| { `max?`: *undefined* \| *null* \| *number* ; `min?`: *undefined* \| *null* \| *number*  }

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:472](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L472)

___

### grants

• `Optional` **grants**: *undefined* \| *null* \| { `funder?`: *undefined* \| *null* \| *string*[] ; `minAmount?`: *undefined* \| *null* \| *number*  }

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:509](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L509)

___

### institution

• `Optional` **institution**: *undefined* \| *null* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:468](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L468)

___

### npi

• `Optional` **npi**: *undefined* \| *null* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:477](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L477)

___

### pastAndPresentWorkInstitutions

• `Optional` **pastAndPresentWorkInstitutions**: *undefined* \| *null* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:470](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L470)

___

### payments

• `Optional` **payments**: *undefined* \| *null* \| { `company?`: *undefined* \| *null* \| *string*[] ; `drugOrDevice?`: *undefined* \| *null* \| *string*[] ; `fundingType?`: *undefined* \| *null* \| *string*[] ; `minAmount?`: *undefined* \| *null* \| *number*  }

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:513](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L513)

___

### presentWorkInstitutions

• `Optional` **presentWorkInstitutions**: *undefined* \| *null* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:469](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L469)

___

### profile

• `Optional` **profile**: *undefined* \| *null* \| { `role?`: *undefined* \| *null* \| *string*[]  }

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:489](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L489)

___

### publications

• `Optional` **publications**: *undefined* \| *null* \| { `journal?`: *undefined* \| *null* \| *string*[] ; `minCount?`: *undefined* \| *null* \| *number* ; `socialMediaMinCount?`: *undefined* \| *null* \| *number* ; `type?`: *undefined* \| *null* \| *string*[]  }

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:483](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L483)

___

### referrals

• `Optional` **referrals**: *undefined* \| *null* \| { `minReferralsReceived?`: *undefined* \| *null* \| *number* ; `minReferralsSent?`: *undefined* \| *null* \| *number* ; `serviceLine?`: *undefined* \| *null* \| *string*[]  }

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:519](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L519)

___

### specialty

• `Optional` **specialty**: *undefined* \| *null* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:463](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L463)

___

### state

• `Optional` **state**: *undefined* \| *null* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:465](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L465)

___

### studentInstitutions

• `Optional` **studentInstitutions**: *undefined* \| *null* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:471](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L471)

___

### tags

• `Optional` **tags**: *undefined* \| *null* \| { `privateTagIds?`: *undefined* \| *null* \| *string*[] ; `programmaticTagIds?`: *undefined* \| *null* \| *string*[] ; `publicTagIds?`: *undefined* \| *null* \| *string*[]  }

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:478](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L478)

___

### trials

• `Optional` **trials**: *undefined* \| *null* \| { `minCount?`: *undefined* \| *null* \| *number* ; `phase?`: *undefined* \| *null* \| *string*[] ; `status?`: *undefined* \| *null* \| *string*[] ; `studyType?`: *undefined* \| *null* \| *string*[]  }

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:492](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L492)

___

### zipCode

• `Optional` **zipCode**: *undefined* \| *null* \| *string*[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:467](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L467)
