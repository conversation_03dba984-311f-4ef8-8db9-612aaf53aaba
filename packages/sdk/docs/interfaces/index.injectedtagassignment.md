[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / InjectedTagAssignment

# Interface: InjectedTagAssignment

[index](../modules/index.md).InjectedTagAssignment

## Hierarchy

* **InjectedTagAssignment**

## Table of contents

### Properties

- [\_\_typename](index.injectedtagassignment.md#__typename)
- [assignedAt](index.injectedtagassignment.md#assignedat)
- [assignedBy](index.injectedtagassignment.md#assignedby)
- [id](index.injectedtagassignment.md#id)
- [tag](index.injectedtagassignment.md#tag)

## Properties

### \_\_typename

• **\_\_typename**: *TagAssignment*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:121](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L121)

___

### assignedAt

• **assignedAt**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:124](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L124)

___

### assignedBy

• **assignedBy**: [*InjectedTagAssignmentUser*](interfaces_elasticdocuments.injectedtagassignmentuser.md)

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:123](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L123)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:122](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L122)

___

### tag

• **tag**: TagAssignmentTag

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:125](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L125)
