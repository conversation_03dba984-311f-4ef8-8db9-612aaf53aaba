[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / WeightedSortBy

# Interface: WeightedSortBy

[index](../modules/index.md).WeightedSortBy

## Hierarchy

* **WeightedSortBy**

## Table of contents

### Properties

- [citation](index.weightedsortby.md#citation)
- [congress](index.weightedsortby.md#congress)
- [diagnoses](index.weightedsortby.md#diagnoses)
- [grant](index.weightedsortby.md#grant)
- [microBloggingCount](index.weightedsortby.md#microbloggingcount)
- [payment](index.weightedsortby.md#payment)
- [procedures](index.weightedsortby.md#procedures)
- [publication](index.weightedsortby.md#publication)
- [referralsReceived](index.weightedsortby.md#referralsreceived)
- [referralsSent](index.weightedsortby.md#referralssent)
- [trial](index.weightedsortby.md#trial)

## Properties

### citation

• **citation**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:171](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L171)

___

### congress

• **congress**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:175](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L175)

___

### diagnoses

• **diagnoses**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:177](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L177)

___

### grant

• **grant**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:176](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L176)

___

### microBloggingCount

• **microBloggingCount**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:172](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L172)

___

### payment

• **payment**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:174](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L174)

___

### procedures

• **procedures**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:178](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L178)

___

### publication

• **publication**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:170](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L170)

___

### referralsReceived

• **referralsReceived**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:179](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L179)

___

### referralsSent

• **referralsSent**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:180](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L180)

___

### trial

• **trial**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:173](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L173)
