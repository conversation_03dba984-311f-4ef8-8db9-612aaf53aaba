[@h1nyc/search-sdk](../README.md) / [interfaces/documentScores](../modules/interfaces_documentscores.md) / ScoredDocumentData

# Interface: ScoredDocumentData

[interfaces/documentScores](../modules/interfaces_documentscores.md).ScoredDocumentData

## Hierarchy

* **ScoredDocumentData**

## Table of contents

### Properties

- [citations](interfaces_documentscores.scoreddocumentdata.md#citations)
- [collaborators](interfaces_documentscores.scoreddocumentdata.md#collaborators)
- [congresses](interfaces_documentscores.scoreddocumentdata.md#congresses)
- [diagnoses](interfaces_documentscores.scoreddocumentdata.md#diagnoses)
- [grants](interfaces_documentscores.scoreddocumentdata.md#grants)
- [grantsCount](interfaces_documentscores.scoreddocumentdata.md#grantscount)
- [h1Score](interfaces_documentscores.scoreddocumentdata.md#h1score)
- [normalizedRange](interfaces_documentscores.scoreddocumentdata.md#normalizedrange)
- [payments](interfaces_documentscores.scoreddocumentdata.md#payments)
- [paymentsCount](interfaces_documentscores.scoreddocumentdata.md#paymentscount)
- [personId](interfaces_documentscores.scoreddocumentdata.md#personid)
- [procedures](interfaces_documentscores.scoreddocumentdata.md#procedures)
- [publications](interfaces_documentscores.scoreddocumentdata.md#publications)
- [referralsReceived](interfaces_documentscores.scoreddocumentdata.md#referralsreceived)
- [referralsSent](interfaces_documentscores.scoreddocumentdata.md#referralssent)
- [socialMediaMentions](interfaces_documentscores.scoreddocumentdata.md#socialmediamentions)
- [trials](interfaces_documentscores.scoreddocumentdata.md#trials)

## Properties

### citations

• **citations**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:36](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L36)

___

### collaborators

• **collaborators**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:43](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L43)

___

### congresses

• **congresses**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:42](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L42)

___

### diagnoses

• **diagnoses**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:46](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L46)

___

### grants

• **grants**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:40](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L40)

___

### grantsCount

• **grantsCount**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:41](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L41)

___

### h1Score

• **h1Score**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:49](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L49)

___

### normalizedRange

• **normalizedRange**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:34](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L34)

___

### payments

• **payments**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:38](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L38)

___

### paymentsCount

• **paymentsCount**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:39](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L39)

___

### personId

• **personId**: *string*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:33](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L33)

___

### procedures

• **procedures**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:45](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L45)

___

### publications

• **publications**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:35](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L35)

___

### referralsReceived

• `Optional` **referralsReceived**: *undefined* \| [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:47](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L47)

___

### referralsSent

• `Optional` **referralsSent**: *undefined* \| [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:48](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L48)

___

### socialMediaMentions

• **socialMediaMentions**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:44](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L44)

___

### trials

• **trials**: [*ScoredDocumentResult*](interfaces_documentscores.scoreddocumentresult.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:37](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L37)
