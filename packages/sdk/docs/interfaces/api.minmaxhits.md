[@h1nyc/search-sdk](../README.md) / [api](../modules/api.md) / MinMaxHits

# Interface: MinMaxHits

[api](../modules/api.md).MinMaxHits

## Hierarchy

* **MinMaxHits**

## Table of contents

### Properties

- [\_shards](api.minmaxhits.md#_shards)
- [aggregations](api.minmaxhits.md#aggregations)
- [hits](api.minmaxhits.md#hits)
- [timed\_out](api.minmaxhits.md#timed_out)
- [took](api.minmaxhits.md#took)

## Properties

### \_shards

• **\_shards**: [*Shards*](api_response_responseinterfaces.shards.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:7](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L7)

___

### aggregations

• **aggregations**: [*Aggregations*](api_response_responseinterfaces.aggregations.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L9)

___

### hits

• **hits**: [*Hits*](api_response_responseinterfaces.hits.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L8)

___

### timed\_out

• **timed\_out**: *boolean*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L6)

___

### took

• **took**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L5)
