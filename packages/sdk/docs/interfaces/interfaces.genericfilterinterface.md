[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / GenericFilterInterface

# Interface: GenericFilterInterface

[interfaces](../modules/interfaces.md).GenericFilterInterface

## Hierarchy

* **GenericFilterInterface**

## Table of contents

### Properties

- [index](interfaces.genericfilterinterface.md#index)
- [path](interfaces.genericfilterinterface.md#path)
- [type](interfaces.genericfilterinterface.md#type)

## Properties

### index

• **index**: [*IndexTypes*](../enums/interfaces_filterinterfaces.indextypes.md)

The name of the index

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:346](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L346)

___

### path

• **path**: *string*

The dot-delimited path within the elastic document

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:342](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L342)

___

### type

• **type**: [*FilterTypesEnum*](../enums/interfaces_filterinterfaces.filtertypesenum.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:338](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L338)
