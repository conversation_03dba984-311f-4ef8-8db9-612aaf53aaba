[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / ScoredDocumentResult

# Interface: ScoredDocumentResult

[index](../modules/index.md).ScoredDocumentResult

## Hierarchy

* **ScoredDocumentResult**

## Table of contents

### Properties

- [maxValue](index.scoreddocumentresult.md#maxvalue)
- [minValue](index.scoreddocumentresult.md#minvalue)
- [normalizedValue](index.scoreddocumentresult.md#normalizedvalue)
- [percentile](index.scoreddocumentresult.md#percentile)
- [value](index.scoreddocumentresult.md#value)

## Properties

### maxValue

• **maxValue**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:28](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L28)

___

### minValue

• **minValue**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:27](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L27)

___

### normalizedValue

• **normalizedValue**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:26](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L26)

___

### percentile

• **percentile**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:29](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L29)

___

### value

• **value**: *number*

Defined in: [packages/sdk/src/interfaces/documentScores.ts:25](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L25)
