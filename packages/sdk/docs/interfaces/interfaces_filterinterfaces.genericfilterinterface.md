[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / GenericFilterInterface

# Interface: GenericFilterInterface

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).GenericFilterInterface

## Hierarchy

* **GenericFilterInterface**

  ↳ [*CheckboxFilter*](interfaces_filterinterfaces.checkboxfilter.md)

  ↳ [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md)

  ↳ [*ClaimsFilter*](interfaces_filterinterfaces.claimsfilter.md)

  ↳ [*CheckboxFilter*](index.checkboxfilter.md)

  ↳ [*NumberFilter*](index.numberfilter.md)

  ↳ [*ClaimsFilter*](index.claimsfilter.md)

  ↳ [*CheckboxFilter*](interfaces.checkboxfilter.md)

  ↳ [*NumberFilter*](interfaces.numberfilter.md)

  ↳ [*ClaimsFilter*](interfaces.claimsfilter.md)

## Table of contents

### Properties

- [index](interfaces_filterinterfaces.genericfilterinterface.md#index)
- [path](interfaces_filterinterfaces.genericfilterinterface.md#path)
- [type](interfaces_filterinterfaces.genericfilterinterface.md#type)

## Properties

### index

• **index**: [*IndexTypes*](../enums/interfaces_filterinterfaces.indextypes.md)

The name of the index

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:346](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L346)

___

### path

• **path**: *string*

The dot-delimited path within the elastic document

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:342](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L342)

___

### type

• **type**: [*FilterTypesEnum*](../enums/interfaces_filterinterfaces.filtertypesenum.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:338](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L338)
