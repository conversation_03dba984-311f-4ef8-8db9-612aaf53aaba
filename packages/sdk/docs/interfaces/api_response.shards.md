[@h1nyc/search-sdk](../README.md) / [api/response](../modules/api_response.md) / Shards

# Interface: Shards

[api/response](../modules/api_response.md).Shards

## Hierarchy

* **Shards**

## Table of contents

### Properties

- [failed](api_response.shards.md#failed)
- [skipped](api_response.shards.md#skipped)
- [successful](api_response.shards.md#successful)
- [total](api_response.shards.md#total)

## Properties

### failed

• **failed**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:15](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L15)

___

### skipped

• **skipped**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:14](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L14)

___

### successful

• **successful**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L13)

___

### total

• **total**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L12)
