[@h1nyc/search-sdk](../README.md) / [interfaces/ElasticOptions](../modules/interfaces_elasticoptions.md) / ElasticOptions

# Interface: ElasticOptions

[interfaces/ElasticOptions](../modules/interfaces_elasticoptions.md).ElasticOptions

## Hierarchy

* **ElasticOptions**

## Table of contents

### Properties

- [multi](interfaces_elasticoptions.elasticoptions.md#multi)
- [preference](interfaces_elasticoptions.elasticoptions.md#preference)
- [searchType](interfaces_elasticoptions.elasticoptions.md#searchtype)

## Properties

### multi

• `Optional` **multi**: *undefined* \| *boolean*

Defined in: [packages/sdk/src/interfaces/ElasticOptions.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/ElasticOptions.ts#L5)

___

### preference

• `Optional` **preference**: *undefined* \| *string*

Defined in: [packages/sdk/src/interfaces/ElasticOptions.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/ElasticOptions.ts#L6)

___

### searchType

• `Optional` **searchType**: *undefined* \| *dfs_query_then_fetch* \| *query_then_fetch*

Defined in: [packages/sdk/src/interfaces/ElasticOptions.ts:4](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/ElasticOptions.ts#L4)
