[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / NumberFilter

# Interface: NumberFilter

[index](../modules/index.md).NumberFilter

## Hierarchy

* [*GenericFilterInterface*](interfaces_filterinterfaces.genericfilterinterface.md)

  ↳ **NumberFilter**

## Table of contents

### Properties

- [index](index.numberfilter.md#index)
- [path](index.numberfilter.md#path)
- [type](index.numberfilter.md#type)
- [value](index.numberfilter.md#value)

## Properties

### index

• **index**: [*IndexTypes*](../enums/interfaces_filterinterfaces.indextypes.md)

The name of the index

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[index](interfaces_filterinterfaces.genericfilterinterface.md#index)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:346](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L346)

___

### path

• **path**: *string*

The dot-delimited path within the elastic document

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[path](interfaces_filterinterfaces.genericfilterinterface.md#path)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:342](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L342)

___

### type

• **type**: [*FilterTypesEnum*](../enums/interfaces_filterinterfaces.filtertypesenum.md)

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[type](interfaces_filterinterfaces.genericfilterinterface.md#type)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:338](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L338)

___

### value

• **value**: *null* \| *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:379](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L379)
