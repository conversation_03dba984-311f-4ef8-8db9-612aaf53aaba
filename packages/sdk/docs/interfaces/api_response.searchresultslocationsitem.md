[@h1nyc/search-sdk](../README.md) / [api/response](../modules/api_response.md) / SearchResultsLocationsItem

# Interface: SearchResultsLocationsItem

[api/response](../modules/api_response.md).SearchResultsLocationsItem

## Hierarchy

* **SearchResultsLocationsItem**

## Table of contents

### Properties

- [city](api_response.searchresultslocationsitem.md#city)
- [country](api_response.searchresultslocationsitem.md#country)
- [state](api_response.searchresultslocationsitem.md#state)

## Properties

### city

• **city**: *null* \| *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:101](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L101)

___

### country

• **country**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:100](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L100)

___

### state

• **state**: *null* \| *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:102](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L102)
