[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / Pagination

# Interface: Pagination

[interfaces](../modules/interfaces.md).Pagination

## Hierarchy

* **Pagination**

## Table of contents

### Properties

- [from](interfaces.pagination.md#from)
- [pageSize](interfaces.pagination.md#pagesize)
- [total](interfaces.pagination.md#total)

## Properties

### from

• **from**: *number*

The first index of the result set

Defined in: [packages/sdk/src/interfaces/pagination.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L5)

___

### pageSize

• **pageSize**: *number*

The page size

Defined in: [packages/sdk/src/interfaces/pagination.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L9)

___

### total

• **total**: *number*

The total number of results

Defined in: [packages/sdk/src/interfaces/pagination.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/pagination.ts#L13)
