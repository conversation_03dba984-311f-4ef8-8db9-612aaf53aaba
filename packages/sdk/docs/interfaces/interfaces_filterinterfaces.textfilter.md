[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / TextFilter

# Interface: TextFilter

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).TextFilter

## Hierarchy

* [*CheckboxFilter*](interfaces_filterinterfaces.checkboxfilter.md)

  ↳ **TextFilter**

## Table of contents

### Properties

- [index](interfaces_filterinterfaces.textfilter.md#index)
- [keyName](interfaces_filterinterfaces.textfilter.md#keyname)
- [mustNotValue](interfaces_filterinterfaces.textfilter.md#mustnotvalue)
- [options](interfaces_filterinterfaces.textfilter.md#options)
- [path](interfaces_filterinterfaces.textfilter.md#path)
- [query](interfaces_filterinterfaces.textfilter.md#query)
- [type](interfaces_filterinterfaces.textfilter.md#type)
- [values](interfaces_filterinterfaces.textfilter.md#values)

## Properties

### index

• **index**: [*IndexTypes*](../enums/interfaces_filterinterfaces.indextypes.md)

The name of the index

Inherited from: [CheckboxFilter](interfaces_filterinterfaces.checkboxfilter.md).[index](interfaces_filterinterfaces.checkboxfilter.md#index)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:346](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L346)

___

### keyName

• **keyName**: *string*

The dot-delimited path in the redux store

Inherited from: [CheckboxFilter](interfaces_filterinterfaces.checkboxfilter.md).[keyName](interfaces_filterinterfaces.checkboxfilter.md#keyname)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:366](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L366)

___

### mustNotValue

• `Optional` **mustNotValue**: *undefined* \| *string*

The value that, if selected, signifies searching with a "must-not"

Inherited from: [CheckboxFilter](interfaces_filterinterfaces.checkboxfilter.md).[mustNotValue](interfaces_filterinterfaces.checkboxfilter.md#mustnotvalue)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:371](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L371)

___

### options

• **options**: { `count?`: *undefined* \| *number* ; `name`: *string*  }[]

The available options

Inherited from: [CheckboxFilter](interfaces_filterinterfaces.checkboxfilter.md).[options](interfaces_filterinterfaces.checkboxfilter.md#options)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:353](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L353)

___

### path

• **path**: *string*

The dot-delimited path within the elastic document

Inherited from: [CheckboxFilter](interfaces_filterinterfaces.checkboxfilter.md).[path](interfaces_filterinterfaces.checkboxfilter.md#path)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:342](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L342)

___

### query

• **query**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:375](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L375)

___

### type

• **type**: [*FilterTypesEnum*](../enums/interfaces_filterinterfaces.filtertypesenum.md)

Inherited from: [CheckboxFilter](interfaces_filterinterfaces.checkboxfilter.md).[type](interfaces_filterinterfaces.checkboxfilter.md#type)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:338](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L338)

___

### values

• **values**: *string*[]

The selected values: a subset of options

Inherited from: [CheckboxFilter](interfaces_filterinterfaces.checkboxfilter.md).[values](interfaces_filterinterfaces.checkboxfilter.md#values)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:361](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L361)
