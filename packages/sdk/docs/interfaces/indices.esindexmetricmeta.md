[@h1nyc/search-sdk](../README.md) / [indices](../modules/indices.md) / EsIndexMetricMeta

# Interface: EsIndexMetricMeta

[indices](../modules/indices.md).EsIndexMetricMeta

## Hierarchy

* **EsIndexMetricMeta**

## Table of contents

### Properties

- [dependsOnSortPath](indices.esindexmetricmeta.md#dependsonsortpath)
- [innerHitsName](indices.esindexmetricmeta.md#innerhitsname)
- [innerHitsSize](indices.esindexmetricmeta.md#innerhitssize)
- [path](indices.esindexmetricmeta.md#path)
- [sortPath](indices.esindexmetricmeta.md#sortpath)
- [totalsUnfilteredByKeyword](indices.esindexmetricmeta.md#totalsunfilteredbykeyword)

## Properties

### dependsOnSortPath

• `Optional` **dependsOnSortPath**: *undefined* \| *string*

If this does not have a sort path, but it depends on a sort path to be
valid/available, then this is the path it depends on.

Defined in: [packages/sdk/src/indices/peopleIndex.ts:64](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L64)

___

### innerHitsName

• `Optional` **innerHitsName**: *undefined* \| *string*

The name to use for the inner_hits property, if needed.

Defined in: [packages/sdk/src/indices/peopleIndex.ts:69](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L69)

___

### innerHitsSize

• `Optional` **innerHitsSize**: *undefined* \| *number*

The override value to set in the inner hits, otherwise it defaults to 10000

Defined in: [packages/sdk/src/indices/peopleIndex.ts:74](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L74)

___

### path

• `Optional` **path**: *undefined* \| *string*

The contains the path(s) to the value in the ES index relative to the path of this
this field.  If it is not specified, then it means the parent has the full path.

For example, if "field.path" has "diagnoses" and "filter.path" has "terms"
then the full pathname would be "diagnoses.terms".

Defined in: [packages/sdk/src/indices/peopleIndex.ts:53](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L53)

___

### sortPath

• `Optional` **sortPath**: *undefined* \| *string*

The contains the path to the value in sortBy (WeightedSortBy).

Defined in: [packages/sdk/src/indices/peopleIndex.ts:58](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L58)

___

### totalsUnfilteredByKeyword

• `Optional` **totalsUnfilteredByKeyword**: *undefined* \| *boolean*

Indicates if this field global totals are filtered by keyword search

Defined in: [packages/sdk/src/indices/peopleIndex.ts:79](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L79)
