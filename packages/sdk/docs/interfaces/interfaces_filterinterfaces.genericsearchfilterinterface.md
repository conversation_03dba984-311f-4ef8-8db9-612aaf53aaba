[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / GenericSearchFilterInterface

# Interface: GenericSearchFilterInterface

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).GenericSearchFilterInterface

## Hierarchy

* **GenericSearchFilterInterface**

## Table of contents

### Properties

- [publications](interfaces_filterinterfaces.genericsearchfilterinterface.md#publications)

## Properties

### publications

• `Optional` **publications**: *undefined* \| *Partial*<{ `journal`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `minCount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) ; `socialMediaMinCount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) ; `type`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)  }\>

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:278](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L278)
