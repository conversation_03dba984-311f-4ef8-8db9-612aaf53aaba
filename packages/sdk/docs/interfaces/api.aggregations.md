[@h1nyc/search-sdk](../README.md) / [api](../modules/api.md) / Aggregations

# Interface: Aggregations

[api](../modules/api.md).Aggregations

## Hierarchy

* **Aggregations**

## Table of contents

### Properties

- [max\_count](api.aggregations.md#max_count)
- [min\_count](api.aggregations.md#min_count)

## Properties

### max\_count

• **max\_count**: [*MaxCountOrMinCount*](api_response_responseinterfaces.maxcountormincount.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:22](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L22)

___

### min\_count

• **min\_count**: [*MaxCountOrMinCount*](api_response_responseinterfaces.maxcountormincount.md)

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:23](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L23)
