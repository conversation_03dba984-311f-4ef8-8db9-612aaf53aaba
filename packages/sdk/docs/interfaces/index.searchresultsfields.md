[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / SearchResultsFields

# Interface: SearchResultsFields

[index](../modules/index.md).SearchResultsFields

## Hierarchy

* **SearchResultsFields**

## Table of contents

### Properties

- [diagnoses.internalCount](index.searchresultsfields.md#diagnoses.internalcount)
- [payments.amount](index.searchresultsfields.md#payments.amount)
- [procedures.internalCount](index.searchresultsfields.md#procedures.internalcount)
- [publications.citationCount](index.searchresultsfields.md#publications.citationcount)
- [publications.microBloggingCount](index.searchresultsfields.md#publications.microbloggingcount)

## Properties

### diagnoses.internalCount

• `Optional` **diagnoses.internalCount**: *undefined* \| *number*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:133](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L133)

___

### payments.amount

• `Optional` **payments.amount**: *undefined* \| *number*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:130](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L130)

___

### procedures.internalCount

• `Optional` **procedures.internalCount**: *undefined* \| *number*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:132](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L132)

___

### publications.citationCount

• `Optional` **publications.citationCount**: *undefined* \| *number*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:129](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L129)

___

### publications.microBloggingCount

• `Optional` **publications.microBloggingCount**: *undefined* \| *number*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:131](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L131)
