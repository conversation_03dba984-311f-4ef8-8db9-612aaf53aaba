[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / ActiveFilterBreakDown

# Interface: ActiveFilterBreakDown

[index](../modules/index.md).ActiveFilterBreakDown

## Hierarchy

* **ActiveFilterBreakDown**

## Table of contents

### Properties

- [claims](index.activefilterbreakdown.md#claims)
- [congress](index.activefilterbreakdown.md#congress)
- [payments](index.activefilterbreakdown.md#payments)
- [people](index.activefilterbreakdown.md#people)
- [profile](index.activefilterbreakdown.md#profile)
- [pubs](index.activefilterbreakdown.md#pubs)
- [referrals](index.activefilterbreakdown.md#referrals)
- [tags](index.activefilterbreakdown.md#tags)
- [trials](index.activefilterbreakdown.md#trials)

## Properties

### claims

• **claims**: *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:261](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L261)

___

### congress

• **congress**: *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:259](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L259)

___

### payments

• **payments**: *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:258](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L258)

___

### people

• **people**: *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:255](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L255)

___

### profile

• **profile**: *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:262](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L262)

___

### pubs

• **pubs**: *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:257](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L257)

___

### referrals

• **referrals**: *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:263](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L263)

___

### tags

• **tags**: *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:260](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L260)

___

### trials

• **trials**: *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:256](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L256)
