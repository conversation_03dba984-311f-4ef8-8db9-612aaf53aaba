[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / EsBooleanQuery

# Interface: EsBooleanQuery

[index](../modules/index.md).EsBooleanQuery

## Hierarchy

* **EsBooleanQuery**

## Table of contents

### Properties

- [filter](index.esbooleanquery.md#filter)
- [minimum\_should\_match](index.esbooleanquery.md#minimum_should_match)
- [must](index.esbooleanquery.md#must)
- [should](index.esbooleanquery.md#should)

## Properties

### filter

• `Optional` **filter**: *undefined* \| *any*[]

Defined in: [packages/sdk/src/util/elasticUtils.ts:7](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L7)

___

### minimum\_should\_match

• `Optional` **minimum\_should\_match**: *undefined* \| *number*

Defined in: [packages/sdk/src/util/elasticUtils.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L5)

___

### must

• `Optional` **must**: *undefined* \| *any*[]

Defined in: [packages/sdk/src/util/elasticUtils.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L6)

___

### should

• `Optional` **should**: *undefined* \| *any*[]

Defined in: [packages/sdk/src/util/elasticUtils.ts:4](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L4)
