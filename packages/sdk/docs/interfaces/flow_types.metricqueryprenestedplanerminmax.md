[@h1nyc/search-sdk](../README.md) / [flow/types](../modules/flow_types.md) / MetricQueryPreNestedPlanerMinMax

# Interface: MetricQueryPreNestedPlanerMinMax

[flow/types](../modules/flow_types.md).MetricQueryPreNestedPlanerMinMax

## Hierarchy

* [*MetricQueryPreMap*](flow_types.metricquerypremap.md)

  ↳ **MetricQueryPreNestedPlanerMinMax**

## Table of contents

### Properties

- [dateFilterPath](flow_types.metricqueryprenestedplanerminmax.md#datefilterpath)
- [dateFilters](flow_types.metricqueryprenestedplanerminmax.md#datefilters)
- [filterCount](flow_types.metricqueryprenestedplanerminmax.md#filtercount)
- [filterPaths](flow_types.metricqueryprenestedplanerminmax.md#filterpaths)
- [filters](flow_types.metricqueryprenestedplanerminmax.md#filters)
- [hasQuery](flow_types.metricqueryprenestedplanerminmax.md#hasquery)
- [innerHits](flow_types.metricqueryprenestedplanerminmax.md#innerhits)
- [isFieldAgg](flow_types.metricqueryprenestedplanerminmax.md#isfieldagg)
- [isKeyWordExcluded](flow_types.metricqueryprenestedplanerminmax.md#iskeywordexcluded)
- [isMinCount](flow_types.metricqueryprenestedplanerminmax.md#ismincount)
- [keywordQuery](flow_types.metricqueryprenestedplanerminmax.md#keywordquery)
- [matchFields](flow_types.metricqueryprenestedplanerminmax.md#matchfields)
- [metric](flow_types.metricqueryprenestedplanerminmax.md#metric)
- [minCount](flow_types.metricqueryprenestedplanerminmax.md#mincount)
- [minFieldLevel](flow_types.metricqueryprenestedplanerminmax.md#minfieldlevel)
- [nested](flow_types.metricqueryprenestedplanerminmax.md#nested)
- [scoreMode](flow_types.metricqueryprenestedplanerminmax.md#scoremode)
- [searchQueryAggTarget](flow_types.metricqueryprenestedplanerminmax.md#searchqueryaggtarget)
- [searchQueryTarget](flow_types.metricqueryprenestedplanerminmax.md#searchquerytarget)

## Properties

### dateFilterPath

• `Optional` **dateFilterPath**: *undefined* \| *string*[]

Inherited from: [MetricQueryPreMap](flow_types.metricquerypremap.md).[dateFilterPath](flow_types.metricquerypremap.md#datefilterpath)

Defined in: [packages/sdk/src/flow/types.ts:40](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L40)

___

### dateFilters

• **dateFilters**: *any*[]

Defined in: [packages/sdk/src/flow/types.ts:31](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L31)

___

### filterCount

• **filterCount**: *number*

Defined in: [packages/sdk/src/flow/types.ts:24](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L24)

___

### filterPaths

• **filterPaths**: { `fsiKey`: *string* ; `indexPath`: *string*  }[]

Inherited from: [MetricQueryPreMap](flow_types.metricquerypremap.md).[filterPaths](flow_types.metricquerypremap.md#filterpaths)

Defined in: [packages/sdk/src/flow/types.ts:48](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L48)

___

### filters

• **filters**: *any*[]

Defined in: [packages/sdk/src/flow/types.ts:23](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L23)

___

### hasQuery

• **hasQuery**: *boolean*

Defined in: [packages/sdk/src/flow/types.ts:28](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L28)

___

### innerHits

• **innerHits**: *any*

Inherited from: [MetricQueryPreMap](flow_types.metricquerypremap.md).[innerHits](flow_types.metricquerypremap.md#innerhits)

Defined in: [packages/sdk/src/flow/types.ts:38](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L38)

___

### isFieldAgg

• **isFieldAgg**: *boolean*

Defined in: [packages/sdk/src/flow/types.ts:30](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L30)

___

### isKeyWordExcluded

• **isKeyWordExcluded**: *boolean*

Defined in: [packages/sdk/src/flow/types.ts:27](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L27)

___

### isMinCount

• **isMinCount**: *boolean*

Defined in: [packages/sdk/src/flow/types.ts:29](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L29)

___

### keywordQuery

• **keywordQuery**: *any*[]

Defined in: [packages/sdk/src/flow/types.ts:25](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L25)

___

### matchFields

• **matchFields**: *undefined* \| *string*[]

Inherited from: [MetricQueryPreMap](flow_types.metricquerypremap.md).[matchFields](flow_types.metricquerypremap.md#matchfields)

Defined in: [packages/sdk/src/flow/types.ts:49](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L49)

___

### metric

• **metric**: [*Metrics*](../enums/flow_types.metrics.md)

Inherited from: [MetricQueryPreMap](flow_types.metricquerypremap.md).[metric](flow_types.metricquerypremap.md#metric)

Defined in: [packages/sdk/src/flow/types.ts:36](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L36)

___

### minCount

• **minCount**: *number*

Defined in: [packages/sdk/src/flow/types.ts:26](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L26)

___

### minFieldLevel

• **minFieldLevel**: *undefined* \| { `field?`: *undefined* \| *string* ; `fsiPath`: *string* ; `scoreMode?`: *undefined* \| *string*  }

Inherited from: [MetricQueryPreMap](flow_types.metricquerypremap.md).[minFieldLevel](flow_types.metricquerypremap.md#minfieldlevel)

Defined in: [packages/sdk/src/flow/types.ts:41](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L41)

___

### nested

• **nested**: [*Metrics*](../enums/flow_types.metrics.md)

Inherited from: [MetricQueryPreMap](flow_types.metricquerypremap.md).[nested](flow_types.metricquerypremap.md#nested)

Defined in: [packages/sdk/src/flow/types.ts:37](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L37)

___

### scoreMode

• **scoreMode**: [*ScoreTypes*](../enums/flow_types.scoretypes.md)

Inherited from: [MetricQueryPreMap](flow_types.metricquerypremap.md).[scoreMode](flow_types.metricquerypremap.md#scoremode)

Defined in: [packages/sdk/src/flow/types.ts:39](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L39)

___

### searchQueryAggTarget

• **searchQueryAggTarget**: [*collaboratorCount*](../enums/flow_types.metrics.md#collaboratorcount) \| *_score*

Defined in: [packages/sdk/src/flow/types.ts:33](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L33)

___

### searchQueryTarget

• **searchQueryTarget**: *field* \| *script*

Defined in: [packages/sdk/src/flow/types.ts:32](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L32)
