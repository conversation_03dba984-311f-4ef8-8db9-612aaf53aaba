[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / TagInterface

# Interface: TagInterface

[index](../modules/index.md).TagInterface

## Hierarchy

* **TagInterface**

## Table of contents

### Properties

- [active](index.taginterface.md#active)
- [created](index.taginterface.md#created)
- [id](index.taginterface.md#id)
- [isH1](index.taginterface.md#ish1)
- [name](index.taginterface.md#name)
- [updated](index.taginterface.md#updated)
- [userEmail](index.taginterface.md#useremail)

## Properties

### active

• **active**: *boolean*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:98](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L98)

___

### created

• **created**: Date

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:96](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L96)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:93](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L93)

___

### isH1

• **isH1**: *boolean*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:95](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L95)

___

### name

• **name**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:94](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L94)

___

### updated

• **updated**: Date

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:97](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L97)

___

### userEmail

• **userEmail**: *null* \| *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:99](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L99)
