[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / DateRangeFilter

# Interface: DateRangeFilter

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).DateRangeFilter

## Hierarchy

* **DateRangeFilter**

## Table of contents

### Properties

- [active](interfaces_filterinterfaces.daterangefilter.md#active)
- [max](interfaces_filterinterfaces.daterangefilter.md#max)
- [min](interfaces_filterinterfaces.daterangefilter.md#min)

## Properties

### active

• **active**: *null* \| *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:198](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L198)

___

### max

• **max**: *null* \| *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:197](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L197)

___

### min

• **min**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:196](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L196)
