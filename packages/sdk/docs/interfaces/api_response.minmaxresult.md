[@h1nyc/search-sdk](../README.md) / [api/response](../modules/api_response.md) / MinMaxResult

# Interface: MinMaxResult

[api/response](../modules/api_response.md).MinMaxResult

## Hierarchy

* **MinMaxResult**

## Table of contents

### Properties

- [max](api_response.minmaxresult.md#max)
- [min](api_response.minmaxresult.md#min)
- [name](api_response.minmaxresult.md#name)

## Properties

### max

• **max**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:170](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L170)

___

### min

• **min**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:169](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L169)

___

### name

• **name**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:168](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L168)
