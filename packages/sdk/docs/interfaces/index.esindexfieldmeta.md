[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / EsIndexFieldMeta

# Interface: EsIndexFieldMeta

[index](../modules/index.md).EsIndexFieldMeta

Elastic Search Index Metadata

## Hierarchy

* **EsIndexFieldMeta**

## Table of contents

### Properties

- [base](index.esindexfieldmeta.md#base)
- [filters](index.esindexfieldmeta.md#filters)
- [metrics](index.esindexfieldmeta.md#metrics)
- [path](index.esindexfieldmeta.md#path)
- [perm](index.esindexfieldmeta.md#perm)

## Properties

### base

• `Optional` **base**: *undefined* \| *boolean*

This indicates that it is a base value (i.e. "collaboratorCount" (a number) vs. "congress" (array of objects)

base = !nested

Defined in: [packages/sdk/src/indices/peopleIndex.ts:96](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L96)

___

### filters

• `Optional` **filters**: *undefined* \| [*EsIndexFilterMeta*](indices_peopleindex.esindexfiltermeta.md)[]

This indicates that this query field can also be filtered on in the UI (an H1 filter).

Note that it is possible for a field to have both filters and metrics.

Defined in: [packages/sdk/src/indices/peopleIndex.ts:103](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L103)

___

### metrics

• `Optional` **metrics**: *undefined* \| [*EsIndexMetricMeta*](indices_peopleindex.esindexmetricmeta.md)[]

This indicates that min/max metrics should be generated for the given paths.

Defined in: [packages/sdk/src/indices/peopleIndex.ts:108](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L108)

___

### path

• **path**: *string*

This is the path name of this data inside the ES index.

Defined in: [packages/sdk/src/indices/peopleIndex.ts:89](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L89)

___

### perm

• `Optional` **perm**: *undefined* \| *claims* \| *profile* \| *referrals*

This indicates what type of permission (if any) the user needs to access these types of metrics/fields.

Defined in: [packages/sdk/src/indices/peopleIndex.ts:113](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/indices/peopleIndex.ts#L113)
