[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / DateRangeFilter

# Interface: DateRangeFilter

[interfaces](../modules/interfaces.md).DateRangeFilter

## Hierarchy

* **DateRangeFilter**

## Table of contents

### Properties

- [active](interfaces.daterangefilter.md#active)
- [max](interfaces.daterangefilter.md#max)
- [min](interfaces.daterangefilter.md#min)

## Properties

### active

• **active**: *null* \| *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:198](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L198)

___

### max

• **max**: *null* \| *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:197](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L197)

___

### min

• **min**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:196](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L196)
