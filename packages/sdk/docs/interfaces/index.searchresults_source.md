[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / SearchResults_source

# Interface: SearchResults\_source

[index](../modules/index.md).SearchResults_source

## Hierarchy

* **SearchResults_source**

## Table of contents

### Properties

- [citationTotal](index.searchresults_source.md#citationtotal)
- [collaboratorCount](index.searchresults_source.md#collaboratorcount)
- [congress](index.searchresults_source.md#congress)
- [congressCount](index.searchresults_source.md#congresscount)
- [diagnosesCount](index.searchresults_source.md#diagnosescount)
- [firstName](index.searchresults_source.md#firstname)
- [graduationYear](index.searchresults_source.md#graduationyear)
- [id](index.searchresults_source.md#id)
- [institutions](index.searchresults_source.md#institutions)
- [lastName](index.searchresults_source.md#lastname)
- [locations](index.searchresults_source.md#locations)
- [microBloggingTotal](index.searchresults_source.md#microbloggingtotal)
- [middleName](index.searchresults_source.md#middlename)
- [name](index.searchresults_source.md#name)
- [pastAndPresentWorkInstitutions](index.searchresults_source.md#pastandpresentworkinstitutions)
- [paymentTotal](index.searchresults_source.md#paymenttotal)
- [payments](index.searchresults_source.md#payments)
- [presentWorkInstitutions](index.searchresults_source.md#presentworkinstitutions)
- [proceduresCount](index.searchresults_source.md#procedurescount)
- [publicationCount](index.searchresults_source.md#publicationcount)
- [publications](index.searchresults_source.md#publications)
- [referralsReceivedCount](index.searchresults_source.md#referralsreceivedcount)
- [referralsSentCount](index.searchresults_source.md#referralssentcount)
- [specialty](index.searchresults_source.md#specialty)
- [studentInstitutions](index.searchresults_source.md#studentinstitutions)
- [titles](index.searchresults_source.md#titles)
- [totalWorks](index.searchresults_source.md#totalworks)
- [trialCount](index.searchresults_source.md#trialcount)
- [trials](index.searchresults_source.md#trials)

## Properties

### citationTotal

• **citationTotal**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:76](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L76)

___

### collaboratorCount

• **collaboratorCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:80](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L80)

___

### congress

• **congress**: { `endDate`: *number*  }[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:97](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L97)

___

### congressCount

• **congressCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:81](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L81)

___

### diagnosesCount

• **diagnosesCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:73](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L73)

___

### firstName

• **firstName**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:83](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L83)

___

### graduationYear

• **graduationYear**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:72](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L72)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:89](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L89)

___

### institutions

• **institutions**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:68](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L68)

___

### lastName

• **lastName**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:85](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L85)

___

### locations

• `Optional` **locations**: *undefined* \| [*SearchResultsLocationsItem*](api_response_responseinterfaces.searchresultslocationsitem.md)[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:86](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L86)

___

### microBloggingTotal

• **microBloggingTotal**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:77](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L77)

___

### middleName

• `Optional` **middleName**: *undefined* \| *null* \| *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:84](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L84)

___

### name

• **name**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:82](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L82)

___

### pastAndPresentWorkInstitutions

• **pastAndPresentWorkInstitutions**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:70](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L70)

___

### paymentTotal

• **paymentTotal**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:87](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L87)

___

### payments

• **payments**: { `paymentDate`: *number*  }[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:96](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L96)

___

### presentWorkInstitutions

• **presentWorkInstitutions**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:69](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L69)

___

### proceduresCount

• **proceduresCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:74](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L74)

___

### publicationCount

• **publicationCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:75](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L75)

___

### publications

• **publications**: { `datePublished`: *number*  }[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:95](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L95)

___

### referralsReceivedCount

• **referralsReceivedCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:90](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L90)

___

### referralsSentCount

• **referralsSentCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:91](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L91)

___

### specialty

• **specialty**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:79](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L79)

___

### studentInstitutions

• **studentInstitutions**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:71](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L71)

___

### titles

• **titles**: *string*[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:88](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L88)

___

### totalWorks

• **totalWorks**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:92](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L92)

___

### trialCount

• **trialCount**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:78](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L78)

___

### trials

• **trials**: { `primaryCompletionDate`: *number* ; `startDate`: *number*  }[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:94](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L94)
