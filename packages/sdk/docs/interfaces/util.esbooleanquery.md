[@h1nyc/search-sdk](../README.md) / [util](../modules/util.md) / EsBooleanQuery

# Interface: EsBooleanQuery

[util](../modules/util.md).EsBooleanQuery

## Hierarchy

* **EsBooleanQuery**

## Table of contents

### Properties

- [filter](util.esbooleanquery.md#filter)
- [minimum\_should\_match](util.esbooleanquery.md#minimum_should_match)
- [must](util.esbooleanquery.md#must)
- [should](util.esbooleanquery.md#should)

## Properties

### filter

• `Optional` **filter**: *undefined* \| *any*[]

Defined in: [packages/sdk/src/util/elasticUtils.ts:7](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L7)

___

### minimum\_should\_match

• `Optional` **minimum\_should\_match**: *undefined* \| *number*

Defined in: [packages/sdk/src/util/elasticUtils.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L5)

___

### must

• `Optional` **must**: *undefined* \| *any*[]

Defined in: [packages/sdk/src/util/elasticUtils.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L6)

___

### should

• `Optional` **should**: *undefined* \| *any*[]

Defined in: [packages/sdk/src/util/elasticUtils.ts:4](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/util/elasticUtils.ts#L4)
