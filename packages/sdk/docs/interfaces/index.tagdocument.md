[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / TagDocument

# Interface: TagDocument

[index](../modules/index.md).TagDocument

## Hierarchy

* **TagDocument**

## Table of contents

### Properties

- [\_id](index.tagdocument.md#_id)
- [active](index.tagdocument.md#active)
- [created](index.tagdocument.md#created)
- [id](index.tagdocument.md#id)
- [isH1](index.tagdocument.md#ish1)
- [name](index.tagdocument.md#name)
- [personNames](index.tagdocument.md#personnames)
- [projectId](index.tagdocument.md#projectid)
- [tag](index.tagdocument.md#tag)
- [updated](index.tagdocument.md#updated)
- [userEmail](index.tagdocument.md#useremail)

## Properties

### \_id

• **\_id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:86](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L86)

___

### active

• **active**: *boolean*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:90](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L90)

___

### created

• **created**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:93](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L93)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:87](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L87)

___

### isH1

• **isH1**: *boolean*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:89](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L89)

___

### name

• **name**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:88](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L88)

___

### personNames

• **personNames**: [*PersonName*](interfaces_elasticdocuments.personname.md)[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:96](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L96)

___

### projectId

• **projectId**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:92](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L92)

___

### tag

• **tag**: *boolean*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:95](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L95)

___

### updated

• **updated**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:94](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L94)

___

### userEmail

• `Optional` **userEmail**: *undefined* \| *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:91](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L91)
