[@h1nyc/search-sdk](../README.md) / [interfaces/documentScores](../modules/interfaces_documentscores.md) / DocumentRanges

# Interface: DocumentRanges

[interfaces/documentScores](../modules/interfaces_documentscores.md).DocumentRanges

## Hierarchy

* **DocumentRanges**

## Table of contents

### Properties

- [citationCount](interfaces_documentscores.documentranges.md#citationcount)
- [congressCount](interfaces_documentscores.documentranges.md#congresscount)
- [diagnoses](interfaces_documentscores.documentranges.md#diagnoses)
- [grantCount](interfaces_documentscores.documentranges.md#grantcount)
- [grantSum](interfaces_documentscores.documentranges.md#grantsum)
- [paymentCount](interfaces_documentscores.documentranges.md#paymentcount)
- [paymentSum](interfaces_documentscores.documentranges.md#paymentsum)
- [procedures](interfaces_documentscores.documentranges.md#procedures)
- [publicationCount](interfaces_documentscores.documentranges.md#publicationcount)
- [referralsReceived](interfaces_documentscores.documentranges.md#referralsreceived)
- [referralsSent](interfaces_documentscores.documentranges.md#referralssent)
- [socialMediaMentions](interfaces_documentscores.documentranges.md#socialmediamentions)
- [totalCollaborators](interfaces_documentscores.documentranges.md#totalcollaborators)
- [trialCount](interfaces_documentscores.documentranges.md#trialcount)

## Properties

### citationCount

• **citationCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:54](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L54)

___

### congressCount

• **congressCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:60](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L60)

___

### diagnoses

• **diagnoses**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:64](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L64)

___

### grantCount

• **grantCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:58](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L58)

___

### grantSum

• **grantSum**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:59](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L59)

___

### paymentCount

• **paymentCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:56](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L56)

___

### paymentSum

• **paymentSum**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:57](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L57)

___

### procedures

• **procedures**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:63](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L63)

___

### publicationCount

• **publicationCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:53](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L53)

___

### referralsReceived

• **referralsReceived**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:65](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L65)

___

### referralsSent

• **referralsSent**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:66](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L66)

___

### socialMediaMentions

• **socialMediaMentions**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:62](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L62)

___

### totalCollaborators

• **totalCollaborators**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:61](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L61)

___

### trialCount

• **trialCount**: [*DocumentScoreRange*](interfaces_documentscores.documentscorerange.md)

Defined in: [packages/sdk/src/interfaces/documentScores.ts:55](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/documentScores.ts#L55)
