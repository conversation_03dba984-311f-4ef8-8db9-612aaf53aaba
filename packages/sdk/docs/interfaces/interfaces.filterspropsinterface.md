[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / FiltersPropsInterface

# Interface: FiltersPropsInterface

[interfaces](../modules/interfaces.md).FiltersPropsInterface

## Hierarchy

* **FiltersPropsInterface**

## Table of contents

### Properties

- [filters](interfaces.filterspropsinterface.md#filters)
- [title](interfaces.filterspropsinterface.md#title)

## Properties

### filters

• `Optional` **filters**: *undefined* \| [*FilterInterface*](interfaces_filterinterfaces.filterinterface.md)[]

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:268](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L268)

___

### title

• `Optional` **title**: *undefined* \| *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:267](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L267)
