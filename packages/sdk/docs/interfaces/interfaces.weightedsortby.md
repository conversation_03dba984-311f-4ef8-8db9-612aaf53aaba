[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / WeightedSortBy

# Interface: WeightedSortBy

[interfaces](../modules/interfaces.md).WeightedSortBy

## Hierarchy

* **WeightedSortBy**

## Table of contents

### Properties

- [citation](interfaces.weightedsortby.md#citation)
- [congress](interfaces.weightedsortby.md#congress)
- [diagnoses](interfaces.weightedsortby.md#diagnoses)
- [grant](interfaces.weightedsortby.md#grant)
- [microBloggingCount](interfaces.weightedsortby.md#microbloggingcount)
- [payment](interfaces.weightedsortby.md#payment)
- [procedures](interfaces.weightedsortby.md#procedures)
- [publication](interfaces.weightedsortby.md#publication)
- [referralsReceived](interfaces.weightedsortby.md#referralsreceived)
- [referralsSent](interfaces.weightedsortby.md#referralssent)
- [trial](interfaces.weightedsortby.md#trial)

## Properties

### citation

• **citation**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:171](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L171)

___

### congress

• **congress**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:175](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L175)

___

### diagnoses

• **diagnoses**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:177](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L177)

___

### grant

• **grant**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:176](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L176)

___

### microBloggingCount

• **microBloggingCount**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:172](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L172)

___

### payment

• **payment**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:174](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L174)

___

### procedures

• **procedures**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:178](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L178)

___

### publication

• **publication**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:170](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L170)

___

### referralsReceived

• **referralsReceived**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:179](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L179)

___

### referralsSent

• **referralsSent**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:180](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L180)

___

### trial

• **trial**: *number*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:173](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L173)
