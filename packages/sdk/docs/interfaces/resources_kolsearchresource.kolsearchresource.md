[@h1nyc/search-sdk](../README.md) / [resources/KolSearchResource](../modules/resources_kolsearchresource.md) / KolSearchResource

# Interface: KolSearchResource

[resources/KolSearchResource](../modules/resources_kolsearchresource.md).KolSearchResource

## Hierarchy

* **KolSearchResource**

## Implemented by

* [*KolSearchResourceClient*](../classes/clients_kolsearchresourceclient.kolsearchresourceclient.md)
* [*KolSearchResourceClient*](../classes/clients.kolsearchresourceclient.md)
* [*KolSearchResourceClient*](../classes/index.kolsearchresourceclient.md)

## Table of contents

### Methods

- [isReady](resources_kolsearchresource.kolsearchresource.md#isready)
- [keywordSearch](resources_kolsearchresource.kolsearchresource.md#keywordsearch)

## Methods

### isReady

▸ **isReady**(): *Promise*<*boolean*\>

**Returns:** *Promise*<*boolean*\>

Defined in: [packages/sdk/src/resources/KolSearchResource.ts:2](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/KolSearchResource.ts#L2)
