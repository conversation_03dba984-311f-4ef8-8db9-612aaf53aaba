[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / SavedSearchByConstructor

# Interface: SavedSearchByConstructor

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).SavedSearchByConstructor

## Hierarchy

* [*WeightedSortBy*](interfaces_filterinterfaces.weightedsortby.md)

  ↳ **SavedSearchByConstructor**

  ↳↳ [*SavedSearchBy*](interfaces_filterinterfaces.savedsearchby.md)

  ↳↳ [*SavedSearchBy*](index.savedsearchby.md)

  ↳↳ [*SavedSearchBy*](interfaces.savedsearchby.md)

## Table of contents

### Properties

- [citation](interfaces_filterinterfaces.savedsearchbyconstructor.md#citation)
- [congress](interfaces_filterinterfaces.savedsearchbyconstructor.md#congress)
- [diagnoses](interfaces_filterinterfaces.savedsearchbyconstructor.md#diagnoses)
- [filters](interfaces_filterinterfaces.savedsearchbyconstructor.md#filters)
- [grant](interfaces_filterinterfaces.savedsearchbyconstructor.md#grant)
- [microBloggingCount](interfaces_filterinterfaces.savedsearchbyconstructor.md#microbloggingcount)
- [name](interfaces_filterinterfaces.savedsearchbyconstructor.md#name)
- [payment](interfaces_filterinterfaces.savedsearchbyconstructor.md#payment)
- [private](interfaces_filterinterfaces.savedsearchbyconstructor.md#private)
- [procedures](interfaces_filterinterfaces.savedsearchbyconstructor.md#procedures)
- [publication](interfaces_filterinterfaces.savedsearchbyconstructor.md#publication)
- [referralsReceived](interfaces_filterinterfaces.savedsearchbyconstructor.md#referralsreceived)
- [referralsSent](interfaces_filterinterfaces.savedsearchbyconstructor.md#referralssent)
- [searchTerms](interfaces_filterinterfaces.savedsearchbyconstructor.md#searchterms)
- [trial](interfaces_filterinterfaces.savedsearchbyconstructor.md#trial)
- [userId](interfaces_filterinterfaces.savedsearchbyconstructor.md#userid)

## Properties

### citation

• **citation**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[citation](interfaces_filterinterfaces.weightedsortby.md#citation)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:171](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L171)

___

### congress

• **congress**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[congress](interfaces_filterinterfaces.weightedsortby.md#congress)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:175](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L175)

___

### diagnoses

• **diagnoses**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[diagnoses](interfaces_filterinterfaces.weightedsortby.md#diagnoses)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:177](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L177)

___

### filters

• **filters**: [*SavedSearchFilterValues*](interfaces_filterinterfaces.savedsearchfiltervalues.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:188](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L188)

___

### grant

• **grant**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[grant](interfaces_filterinterfaces.weightedsortby.md#grant)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:176](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L176)

___

### microBloggingCount

• **microBloggingCount**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[microBloggingCount](interfaces_filterinterfaces.weightedsortby.md#microbloggingcount)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:172](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L172)

___

### name

• **name**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:184](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L184)

___

### payment

• **payment**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[payment](interfaces_filterinterfaces.weightedsortby.md#payment)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:174](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L174)

___

### private

• **private**: *boolean*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:186](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L186)

___

### procedures

• **procedures**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[procedures](interfaces_filterinterfaces.weightedsortby.md#procedures)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:178](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L178)

___

### publication

• **publication**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[publication](interfaces_filterinterfaces.weightedsortby.md#publication)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:170](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L170)

___

### referralsReceived

• **referralsReceived**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[referralsReceived](interfaces_filterinterfaces.weightedsortby.md#referralsreceived)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:179](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L179)

___

### referralsSent

• **referralsSent**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[referralsSent](interfaces_filterinterfaces.weightedsortby.md#referralssent)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:180](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L180)

___

### searchTerms

• **searchTerms**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:185](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L185)

___

### trial

• **trial**: *number*

Inherited from: [WeightedSortBy](interfaces_filterinterfaces.weightedsortby.md).[trial](interfaces_filterinterfaces.weightedsortby.md#trial)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:173](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L173)

___

### userId

• **userId**: *string*

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:187](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L187)
