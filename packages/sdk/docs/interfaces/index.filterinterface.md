[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / FilterInterface

# Interface: FilterInterface

[index](../modules/index.md).FilterInterface

## Hierarchy

* **FilterInterface**

## Table of contents

### Properties

- [city](index.filterinterface.md#city)
- [claims](index.filterinterface.md#claims)
- [congresses](index.filterinterface.md#congresses)
- [country](index.filterinterface.md#country)
- [dateRangePicker](index.filterinterface.md#daterangepicker)
- [graduationYearRange](index.filterinterface.md#graduationyearrange)
- [grants](index.filterinterface.md#grants)
- [institution](index.filterinterface.md#institution)
- [institutionType](index.filterinterface.md#institutiontype)
- [npi](index.filterinterface.md#npi)
- [pastAndPresentWorkInstitutions](index.filterinterface.md#pastandpresentworkinstitutions)
- [payments](index.filterinterface.md#payments)
- [presentWorkInstitutions](index.filterinterface.md#presentworkinstitutions)
- [profile](index.filterinterface.md#profile)
- [publications](index.filterinterface.md#publications)
- [referrals](index.filterinterface.md#referrals)
- [specialty](index.filterinterface.md#specialty)
- [state](index.filterinterface.md#state)
- [studentInstitutions](index.filterinterface.md#studentinstitutions)
- [tags](index.filterinterface.md#tags)
- [trials](index.filterinterface.md#trials)
- [zipCode](index.filterinterface.md#zipcode)

## Properties

### city

• **city**: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:87](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L87)

___

### claims

• **claims**: { `diagnosesICD`: [*ClaimsFilter*](interfaces_filterinterfaces.claimsfilter.md) ; `diagnosesICDMinCount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) ; `proceduresCPT`: [*ClaimsFilter*](interfaces_filterinterfaces.claimsfilter.md) ; `proceduresCPTMinCount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) ; `proceduresHCPC`: [*ClaimsFilter*](interfaces_filterinterfaces.claimsfilter.md) ; `proceduresHCPCMinCount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md)  }

#### Type declaration:

Name | Type |
------ | ------ |
`diagnosesICD` | [*ClaimsFilter*](interfaces_filterinterfaces.claimsfilter.md) |
`diagnosesICDMinCount` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |
`proceduresCPT` | [*ClaimsFilter*](interfaces_filterinterfaces.claimsfilter.md) |
`proceduresCPTMinCount` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |
`proceduresHCPC` | [*ClaimsFilter*](interfaces_filterinterfaces.claimsfilter.md) |
`proceduresHCPCMinCount` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:130](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L130)

___

### congresses

• **congresses**: { `minCount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) ; `name`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `organizerName`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `sessionType`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `type`: [*CheckboxFilter*](interfaces_filterinterfaces.checkboxfilter.md)  }

#### Type declaration:

Name | Type |
------ | ------ |
`minCount` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |
`name` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`organizerName` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`sessionType` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`type` | [*CheckboxFilter*](interfaces_filterinterfaces.checkboxfilter.md) |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:123](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L123)

___

### country

• **country**: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:85](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L85)

___

### dateRangePicker

• **dateRangePicker**: [*DateRangeFilter*](interfaces_filterinterfaces.daterangefilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:95](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L95)

___

### graduationYearRange

• **graduationYearRange**: [*YearRangeFilter*](interfaces_filterinterfaces.yearrangefilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:93](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L93)

___

### grants

• **grants**: { `funder`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `minAmount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md)  }

#### Type declaration:

Name | Type |
------ | ------ |
`funder` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`minAmount` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:138](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L138)

___

### institution

• **institution**: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:89](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L89)

___

### institutionType

• **institutionType**: [*CheckboxFilter*](interfaces_filterinterfaces.checkboxfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:94](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L94)

___

### npi

• **npi**: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:96](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L96)

___

### pastAndPresentWorkInstitutions

• **pastAndPresentWorkInstitutions**: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:91](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L91)

___

### payments

• **payments**: { `company`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `drugOrDevice`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `fundingType`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `minAmount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md)  }

#### Type declaration:

Name | Type |
------ | ------ |
`company` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`drugOrDevice` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`fundingType` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`minAmount` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:142](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L142)

___

### presentWorkInstitutions

• **presentWorkInstitutions**: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:90](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L90)

___

### profile

• **profile**: { `role`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)  }

#### Type declaration:

Name | Type |
------ | ------ |
`role` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:112](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L112)

___

### publications

• **publications**: { `journal`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `minCount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) ; `socialMediaMinCount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) ; `type`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)  }

#### Type declaration:

Name | Type |
------ | ------ |
`journal` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`minCount` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |
`socialMediaMinCount` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |
`type` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:106](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L106)

___

### referrals

• **referrals**: { `minReferralsReceived`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) ; `minReferralsSent`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) ; `serviceLine`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)  }

#### Type declaration:

Name | Type |
------ | ------ |
`minReferralsReceived` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |
`minReferralsSent` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |
`serviceLine` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:148](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L148)

___

### specialty

• **specialty**: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:84](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L84)

___

### state

• **state**: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:86](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L86)

___

### studentInstitutions

• **studentInstitutions**: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:92](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L92)

___

### tags

• **tags**: { `name`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `privateTagIds?`: *undefined* \| *null* \| *string*[] ; `programmaticTagIds?`: *undefined* \| *null* \| *string*[] ; `publicTagIds?`: *undefined* \| *null* \| *string*[]  }

#### Type declaration:

Name | Type |
------ | ------ |
`name` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`privateTagIds?` | *undefined* \| *null* \| *string*[] |
`programmaticTagIds?` | *undefined* \| *null* \| *string*[] |
`publicTagIds?` | *undefined* \| *null* \| *string*[] |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:97](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L97)

___

### trials

• **trials**: { `funderType`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `minCount`: [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) ; `phase`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `sponsor`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `status`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md) ; `studyType`: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)  }

#### Type declaration:

Name | Type |
------ | ------ |
`funderType` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`minCount` | [*NumberFilter*](interfaces_filterinterfaces.numberfilter.md) |
`phase` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`sponsor` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`status` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |
`studyType` | [*TextFilter*](interfaces_filterinterfaces.textfilter.md) |

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:115](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L115)

___

### zipCode

• **zipCode**: [*TextFilter*](interfaces_filterinterfaces.textfilter.md)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:88](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L88)
