[@h1nyc/search-sdk](../README.md) / [api](../modules/api.md) / SearchResultsHits

# Interface: SearchResultsHits

[api](../modules/api.md).SearchResultsHits

## Hierarchy

* **SearchResultsHits**

## Table of contents

### Properties

- [hits](api.searchresultshits.md#hits)
- [max\_score](api.searchresultshits.md#max_score)
- [total](api.searchresultshits.md#total)

## Properties

### hits

• **hits**: [*SearchResultsHitsItem*](api_response_responseinterfaces.searchresultshitsitem.md)[]

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:54](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L54)

___

### max\_score

• **max\_score**: *null* \| *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:53](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L53)

___

### total

• **total**: *number*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:52](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/api/response/ResponseInterfaces.ts#L52)
