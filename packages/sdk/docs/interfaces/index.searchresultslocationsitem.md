[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / SearchResultsLocationsItem

# Interface: SearchResultsLocationsItem

[index](../modules/index.md).SearchResultsLocationsItem

## Hierarchy

* **SearchResultsLocationsItem**

## Table of contents

### Properties

- [city](index.searchresultslocationsitem.md#city)
- [country](index.searchresultslocationsitem.md#country)
- [state](index.searchresultslocationsitem.md#state)

## Properties

### city

• **city**: *null* \| *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:101](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L101)

___

### country

• **country**: *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:100](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L100)

___

### state

• **state**: *null* \| *string*

Defined in: [packages/sdk/src/api/response/ResponseInterfaces.ts:102](https://github.com/shore-group/h1-search/blob/4c1cfbb/packages/sdk/src/api/response/ResponseInterfaces.ts#L102)
