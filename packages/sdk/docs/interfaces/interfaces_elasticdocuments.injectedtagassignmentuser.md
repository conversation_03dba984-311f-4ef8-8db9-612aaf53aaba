[@h1nyc/search-sdk](../README.md) / [interfaces/elasticDocuments](../modules/interfaces_elasticdocuments.md) / InjectedTagAssignmentUser

# Interface: InjectedTagAssignmentUser

[interfaces/elasticDocuments](../modules/interfaces_elasticdocuments.md).InjectedTagAssignmentUser

## Hierarchy

* **InjectedTagAssignmentUser**

## Table of contents

### Properties

- [\_\_typename](interfaces_elasticdocuments.injectedtagassignmentuser.md#__typename)
- [firstName](interfaces_elasticdocuments.injectedtagassignmentuser.md#firstname)
- [id](interfaces_elasticdocuments.injectedtagassignmentuser.md#id)
- [lastName](interfaces_elasticdocuments.injectedtagassignmentuser.md#lastname)

## Properties

### \_\_typename

• **\_\_typename**: *User*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:101](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L101)

___

### firstName

• **firstName**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:103](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L103)

___

### id

• **id**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:102](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L102)

___

### lastName

• **lastName**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:104](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L104)
