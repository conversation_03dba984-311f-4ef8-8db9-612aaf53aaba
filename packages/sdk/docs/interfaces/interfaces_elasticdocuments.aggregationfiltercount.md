[@h1nyc/search-sdk](../README.md) / [interfaces/elasticDocuments](../modules/interfaces_elasticdocuments.md) / AggregationFilterCount

# Interface: AggregationFilterCount

[interfaces/elasticDocuments](../modules/interfaces_elasticdocuments.md).AggregationFilterCount

## Hierarchy

* **AggregationFilterCount**

## Table of contents

### Properties

- [buckets](interfaces_elasticdocuments.aggregationfiltercount.md#buckets)
- [field](interfaces_elasticdocuments.aggregationfiltercount.md#field)

## Properties

### buckets

• **buckets**: { `doc_count`: *number* ; `key`: *string*  }[]

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:147](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L147)

___

### field

• **field**: *string*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:146](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L146)
