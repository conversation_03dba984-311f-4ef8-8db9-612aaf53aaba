[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / CheckboxFilter

# Interface: CheckboxFilter

[interfaces](../modules/interfaces.md).CheckboxFilter

## Hierarchy

* [*GenericFilterInterface*](interfaces_filterinterfaces.genericfilterinterface.md)

  ↳ **CheckboxFilter**

## Table of contents

### Properties

- [index](interfaces.checkboxfilter.md#index)
- [keyName](interfaces.checkboxfilter.md#keyname)
- [mustNotValue](interfaces.checkboxfilter.md#mustnotvalue)
- [options](interfaces.checkboxfilter.md#options)
- [path](interfaces.checkboxfilter.md#path)
- [type](interfaces.checkboxfilter.md#type)
- [values](interfaces.checkboxfilter.md#values)

## Properties

### index

• **index**: [*IndexTypes*](../enums/interfaces_filterinterfaces.indextypes.md)

The name of the index

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[index](interfaces_filterinterfaces.genericfilterinterface.md#index)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:346](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L346)

___

### keyName

• **keyName**: *string*

The dot-delimited path in the redux store

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:366](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L366)

___

### mustNotValue

• `Optional` **mustNotValue**: *undefined* \| *string*

The value that, if selected, signifies searching with a "must-not"

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:371](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L371)

___

### options

• **options**: { `count?`: *undefined* \| *number* ; `name`: *string*  }[]

The available options

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:353](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L353)

___

### path

• **path**: *string*

The dot-delimited path within the elastic document

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[path](interfaces_filterinterfaces.genericfilterinterface.md#path)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:342](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L342)

___

### type

• **type**: [*FilterTypesEnum*](../enums/interfaces_filterinterfaces.filtertypesenum.md)

Inherited from: [GenericFilterInterface](interfaces_filterinterfaces.genericfilterinterface.md).[type](interfaces_filterinterfaces.genericfilterinterface.md#type)

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:338](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L338)

___

### values

• **values**: *string*[]

The selected values: a subset of options

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:361](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L361)
