[@h1nyc/search-sdk](../README.md) / [interfaces/KOLResultInterfaces](../modules/interfaces_kolresultinterfaces.md) / KOLDataRequest

# Interface: KOLDataRequest

[interfaces/KOLResultInterfaces](../modules/interfaces_kolresultinterfaces.md).KOLDataRequest

## Hierarchy

* **KOLDataRequest**

## Table of contents

### Properties

- [created](interfaces_kolresultinterfaces.koldatarequest.md#created)
- [personId](interfaces_kolresultinterfaces.koldatarequest.md#personid)
- [projectId](interfaces_kolresultinterfaces.koldatarequest.md#projectid)
- [userId](interfaces_kolresultinterfaces.koldatarequest.md#userid)

## Properties

### created

• `Optional` **created**: *undefined* \| Date

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:106](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L106)

___

### personId

• **personId**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:105](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L105)

___

### projectId

• **projectId**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:104](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L104)

___

### userId

• **userId**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:103](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L103)
