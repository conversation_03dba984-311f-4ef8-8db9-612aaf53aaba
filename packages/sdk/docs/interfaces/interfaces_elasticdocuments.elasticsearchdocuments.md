[@h1nyc/search-sdk](../README.md) / [interfaces/elasticDocuments](../modules/interfaces_elasticdocuments.md) / ElasticSearchDocuments

# Interface: ElasticSearchDocuments

[interfaces/elasticDocuments](../modules/interfaces_elasticdocuments.md).ElasticSearchDocuments

## Hierarchy

* **ElasticSearchDocuments**

## Table of contents

### Properties

- [doc](interfaces_elasticdocuments.elasticsearchdocuments.md#doc)
- [type](interfaces_elasticdocuments.elasticsearchdocuments.md#type)

## Properties

### doc

• **doc**: *any*

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:74](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L74)

___

### type

• **type**: [*GenericSearchEnum*](../enums/interfaces_elasticdocuments.genericsearchenum.md)

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:73](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L73)
