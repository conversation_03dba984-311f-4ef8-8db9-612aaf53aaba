[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / ResultCardData

# Interface: ResultCardData

[interfaces](../modules/interfaces.md).ResultCardData

## Hierarchy

* **ResultCardData**

## Table of contents

### Properties

- [affiliations](interfaces.resultcarddata.md#affiliations)
- [citationCount](interfaces.resultcarddata.md#citationcount)
- [citationCountAvg](interfaces.resultcarddata.md#citationcountavg)
- [congresses](interfaces.resultcarddata.md#congresses)
- [congressesDates](interfaces.resultcarddata.md#congressesdates)
- [countClinicalTrials](interfaces.resultcarddata.md#countclinicaltrials)
- [countPublications](interfaces.resultcarddata.md#countpublications)
- [diagnosesCount](interfaces.resultcarddata.md#diagnosescount)
- [firstName](interfaces.resultcarddata.md#firstname)
- [grantDates](interfaces.resultcarddata.md#grantdates)
- [grants](interfaces.resultcarddata.md#grants)
- [infoRequestsResolved](interfaces.resultcarddata.md#inforequestsresolved)
- [lastName](interfaces.resultcarddata.md#lastname)
- [middleName](interfaces.resultcarddata.md#middlename)
- [paymentDates](interfaces.resultcarddata.md#paymentdates)
- [personId](interfaces.resultcarddata.md#personid)
- [proceduresCount](interfaces.resultcarddata.md#procedurescount)
- [publicationDates](interfaces.resultcarddata.md#publicationdates)
- [referralsReceivedCount](interfaces.resultcarddata.md#referralsreceivedcount)
- [referralsSentCount](interfaces.resultcarddata.md#referralssentcount)
- [score](interfaces.resultcarddata.md#score)
- [socialMediaMentionsTotal](interfaces.resultcarddata.md#socialmediamentionstotal)
- [specialty](interfaces.resultcarddata.md#specialty)
- [sumGrants](interfaces.resultcarddata.md#sumgrants)
- [sumPayments](interfaces.resultcarddata.md#sumpayments)
- [tags](interfaces.resultcarddata.md#tags)
- [trendHistory](interfaces.resultcarddata.md#trendhistory)
- [trendPresent](interfaces.resultcarddata.md#trendpresent)
- [trialDates](interfaces.resultcarddata.md#trialdates)

## Properties

### affiliations

• **affiliations**: [*ResultCardAffiliation*](interfaces_kolresultinterfaces.resultcardaffiliation.md)[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:65](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L65)

___

### citationCount

• **citationCount**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:86](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L86)

___

### citationCountAvg

• **citationCountAvg**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:87](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L87)

___

### congresses

• **congresses**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:77](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L77)

___

### congressesDates

• **congressesDates**: *number*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:82](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L82)

___

### countClinicalTrials

• **countClinicalTrials**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:70](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L70)

___

### countPublications

• **countPublications**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:69](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L69)

___

### diagnosesCount

• `Optional` **diagnosesCount**: *undefined* \| *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:75](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L75)

___

### firstName

• **firstName**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:62](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L62)

___

### grantDates

• `Optional` **grantDates**: *undefined* \| *number*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:81](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L81)

___

### grants

• **grants**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:78](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L78)

___

### infoRequestsResolved

• **infoRequestsResolved**: *null* \| *boolean*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:88](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L88)

___

### lastName

• **lastName**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:63](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L63)

___

### middleName

• `Optional` **middleName**: *undefined* \| *null* \| *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:64](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L64)

___

### paymentDates

• **paymentDates**: *number*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:83](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L83)

___

### personId

• **personId**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:61](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L61)

___

### proceduresCount

• `Optional` **proceduresCount**: *undefined* \| *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:76](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L76)

___

### publicationDates

• **publicationDates**: *number*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:84](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L84)

___

### referralsReceivedCount

• **referralsReceivedCount**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:72](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L72)

___

### referralsSentCount

• **referralsSentCount**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:73](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L73)

___

### score

• **score**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:66](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L66)

___

### socialMediaMentionsTotal

• **socialMediaMentionsTotal**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:71](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L71)

___

### specialty

• **specialty**: *string*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:80](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L80)

___

### sumGrants

• **sumGrants**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:79](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L79)

___

### sumPayments

• **sumPayments**: *number*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:74](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L74)

___

### tags

• **tags**: [*TagInterface*](interfaces_kolresultinterfaces.taginterface.md)[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:89](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L89)

___

### trendHistory

• **trendHistory**: *number*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:67](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L67)

___

### trendPresent

• **trendPresent**: *string*

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:68](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L68)

___

### trialDates

• **trialDates**: *number*[]

Defined in: [packages/sdk/src/interfaces/KOLResultInterfaces.ts:85](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/KOLResultInterfaces.ts#L85)
