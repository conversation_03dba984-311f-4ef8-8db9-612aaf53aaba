[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / SortOptions

# Interface: SortOptions<E\>

[interfaces](../modules/interfaces.md).SortOptions

## Type parameters

Name |
------ |
`E` |

## Hierarchy

* **SortOptions**

## Table of contents

### Properties

- [direction](interfaces.sortoptions.md#direction)
- [sortBy](interfaces.sortoptions.md#sortby)

## Properties

### direction

• `Optional` **direction**: *undefined* \| [*Asc*](../enums/interfaces_sortoptions.sortdirection.md#asc) \| [*Desc*](../enums/interfaces_sortoptions.sortdirection.md#desc)

Defined in: [packages/sdk/src/interfaces/SortOptions.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/SortOptions.ts#L8)

___

### sortBy

• `Optional` **sortBy**: *undefined* \| E

Defined in: [packages/sdk/src/interfaces/SortOptions.ts:7](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/SortOptions.ts#L7)
