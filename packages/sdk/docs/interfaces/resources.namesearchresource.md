[@h1nyc/search-sdk](../README.md) / [resources](../modules/resources.md) / NameSearchResource

# Interface: NameSearchResource

[resources](../modules/resources.md).NameSearchResource

## Hierarchy

* **NameSearchResource**

## Table of contents

### Methods

- [isReady](resources.namesearchresource.md#isready)
- [runNameSearch](resources.namesearchresource.md#runnamesearch)

## Methods

### isReady

▸ **isReady**(): *Promise*<*boolean*\>

Ready check

**Returns:** *Promise*<*boolean*\>

Defined in: [packages/sdk/src/resources/NameSearchResource.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/NameSearchResource.ts#L8)

___

### runNameSearch

▸ **runNameSearch**(`claimsEnabled`: *boolean*, `profileEnabled`: *boolean*, `referralsEnabled`: *boolean*, `from`: *number*, `pageSize`: *number*, `fsi`: [*FilterSearchInterface*](interfaces_filterinterfaces.filtersearchinterface.md), `peopleIndex`: *string*, `tagIds`: *string*[], `isExcel`: *boolean*): *Promise*<[*PersonSearchResponse*](../modules/interfaces_elasticdocuments.md#personsearchresponse)\>

Run a name search query against Elasticsearch to get person results

#### Parameters:

Name | Type | Description |
------ | ------ | ------ |
`claimsEnabled` | *boolean* | boolean indicating if claims are enabled   |
`profileEnabled` | *boolean* | boolean indicating if profile is enabled   |
`referralsEnabled` | *boolean* | boolean indicating if referrals are enabled   |
`from` | *number* | integer indicating start value for pagination   |
`pageSize` | *number* | integer indicating number of results to return in a page   |
`fsi` | [*FilterSearchInterface*](interfaces_filterinterfaces.filtersearchinterface.md) | FilterSearchInterface class to provide filter values   |
`peopleIndex` | *string* | string indicating the people index name   |
`tagIds` | *string*[] | list of strings for tag ids to look for, used for excel export   |
`isExcel` | *boolean* | boolean indicating that request is for excel export    |

**Returns:** *Promise*<[*PersonSearchResponse*](../modules/interfaces_elasticdocuments.md#personsearchresponse)\>

Defined in: [packages/sdk/src/resources/NameSearchResource.ts:22](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/NameSearchResource.ts#L22)
