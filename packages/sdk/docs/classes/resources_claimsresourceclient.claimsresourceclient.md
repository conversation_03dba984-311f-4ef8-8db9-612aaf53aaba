[@h1nyc/search-sdk](../README.md) / [resources/ClaimsResourceClient](../modules/resources_claimsresourceclient.md) / ClaimsResourceClient

# Class: ClaimsResourceClient

[resources/ClaimsResourceClient](../modules/resources_claimsresourceclient.md).ClaimsResourceClient

## Hierarchy

* *RpcResourceClient*

  ↳ **ClaimsResourceClient**

## Implements

* [*ClaimsResource*](../interfaces/resources_claimsresource.claimsresource.md)

## Table of contents

### Constructors

- [constructor](resources_claimsresourceclient.claimsresourceclient.md#constructor)

### Properties

- [rpcClient](resources_claimsresourceclient.claimsresourceclient.md#rpcclient)

### Methods

- [getDiagnoses](resources_claimsresourceclient.claimsresourceclient.md#getdiagnoses)
- [getProcedures](resources_claimsresourceclient.claimsresourceclient.md#getprocedures)
- [isReady](resources_claimsresourceclient.claimsresourceclient.md#isready)

## Constructors

### constructor

\+ **new ClaimsResourceClient**(`signingSecret`: *string*, `redisOptions?`: RedisOptions): [*ClaimsResourceClient*](resources_claimsresourceclient.claimsresourceclient.md)

#### Parameters:

Name | Type |
------ | ------ |
`signingSecret` | *string* |
`redisOptions?` | RedisOptions |

**Returns:** [*ClaimsResourceClient*](resources_claimsresourceclient.claimsresourceclient.md)

Defined in: [packages/sdk/src/resources/ClaimsResourceClient.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResourceClient.ts#L13)

## Properties

### rpcClient

• **rpcClient**: *default*

Defined in: node_modules/@h1nyc/systems-rpc/dist/RpcResourceClient.d.ts:3

## Methods

### getDiagnoses

▸ **getDiagnoses**(`personId`: *string*, `terms?`: *string*[], `page?`: [*PaginationOptions*](../interfaces/interfaces_pagination.paginationoptions.md), `sort?`: [*DiagnosesSortOptions*](../modules/resources_claimsresource.md#diagnosessortoptions)): *Promise*<[*PaginationResponse*](../interfaces/interfaces_pagination.paginationresponse.md)<[*ClaimsDiagnosis*](../interfaces/interfaces_claims.claimsdiagnosis.md)\>\>

#### Parameters:

Name | Type |
------ | ------ |
`personId` | *string* |
`terms?` | *string*[] |
`page?` | [*PaginationOptions*](../interfaces/interfaces_pagination.paginationoptions.md) |
`sort?` | [*DiagnosesSortOptions*](../modules/resources_claimsresource.md#diagnosessortoptions) |

**Returns:** *Promise*<[*PaginationResponse*](../interfaces/interfaces_pagination.paginationresponse.md)<[*ClaimsDiagnosis*](../interfaces/interfaces_claims.claimsdiagnosis.md)\>\>

Implementation of: [ClaimsResource](../interfaces/resources_claimsresource.claimsresource.md)

Defined in: [packages/sdk/src/resources/ClaimsResourceClient.ts:22](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResourceClient.ts#L22)

___

### getProcedures

▸ **getProcedures**(`personId`: *string*, `terms?`: *string*[], `page?`: [*PaginationOptions*](../interfaces/interfaces_pagination.paginationoptions.md), `sort?`: [*ProceduresSortOptions*](../modules/resources_claimsresource.md#proceduressortoptions)): *Promise*<[*PaginationResponse*](../interfaces/interfaces_pagination.paginationresponse.md)<[*ClaimsProcedure*](../interfaces/interfaces_claims.claimsprocedure.md)\>\>

#### Parameters:

Name | Type |
------ | ------ |
`personId` | *string* |
`terms?` | *string*[] |
`page?` | [*PaginationOptions*](../interfaces/interfaces_pagination.paginationoptions.md) |
`sort?` | [*ProceduresSortOptions*](../modules/resources_claimsresource.md#proceduressortoptions) |

**Returns:** *Promise*<[*PaginationResponse*](../interfaces/interfaces_pagination.paginationresponse.md)<[*ClaimsProcedure*](../interfaces/interfaces_claims.claimsprocedure.md)\>\>

Implementation of: [ClaimsResource](../interfaces/resources_claimsresource.claimsresource.md)

Defined in: [packages/sdk/src/resources/ClaimsResourceClient.ts:34](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResourceClient.ts#L34)

___

### isReady

▸ **isReady**(): *Promise*<*boolean*\>

**Returns:** *Promise*<*boolean*\>

Implementation of: [ClaimsResource](../interfaces/resources_claimsresource.claimsresource.md)

Defined in: [packages/sdk/src/resources/ClaimsResourceClient.ts:18](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResourceClient.ts#L18)
