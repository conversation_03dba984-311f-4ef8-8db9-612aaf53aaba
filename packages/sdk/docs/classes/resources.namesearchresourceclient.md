[@h1nyc/search-sdk](../README.md) / [resources](../modules/resources.md) / NameSearchResourceClient

# Class: NameSearchResourceClient

[resources](../modules/resources.md).NameSearchResourceClient

## Hierarchy

* *RpcResourceClient*

  ↳ **NameSearchResourceClient**

## Implements

* [*NameSearchResource*](../interfaces/resources_namesearchresource.namesearchresource.md)

## Table of contents

### Constructors

- [constructor](resources.namesearchresourceclient.md#constructor)

### Properties

- [rpcClient](resources.namesearchresourceclient.md#rpcclient)

### Methods

- [isReady](resources.namesearchresourceclient.md#isready)
- [runNameSearch](resources.namesearchresourceclient.md#runnamesearch)

## Constructors

### constructor

\+ **new NameSearchResourceClient**(`signingSecret`: *string*, `redisOptions?`: RedisOptions): [*NameSearchResourceClient*](resources_namesearchresourceclient.namesearchresourceclient.md)

#### Parameters:

Name | Type |
------ | ------ |
`signingSecret` | *string* |
`redisOptions?` | RedisOptions |

**Returns:** [*NameSearchResourceClient*](resources_namesearchresourceclient.namesearchresourceclient.md)

Defined in: [packages/sdk/src/resources/NameSearchResourceClient.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/NameSearchResourceClient.ts#L9)

## Properties

### rpcClient

• **rpcClient**: *default*

Defined in: node_modules/@h1nyc/systems-rpc/dist/RpcResourceClient.d.ts:3

## Methods

### isReady

▸ **isReady**(): *Promise*<*boolean*\>

**Returns:** *Promise*<*boolean*\>

Implementation of: [NameSearchResource](../interfaces/resources_namesearchresource.namesearchresource.md)

Defined in: [packages/sdk/src/resources/NameSearchResourceClient.ts:14](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/NameSearchResourceClient.ts#L14)

___

### runNameSearch

▸ **runNameSearch**(`claimsEnabled`: *boolean*, `profileEnabled`: *boolean*, `referralsEnabled`: *boolean*, `from`: *number*, `pageSize`: *number*, `fsi`: [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md), `peopleIndex`: *string*, `tagIds`: *string*[], `isExcel`: *boolean*): *Promise*<[*PersonSearchResponse*](../modules/interfaces_elasticdocuments.md#personsearchresponse)\>

#### Parameters:

Name | Type |
------ | ------ |
`claimsEnabled` | *boolean* |
`profileEnabled` | *boolean* |
`referralsEnabled` | *boolean* |
`from` | *number* |
`pageSize` | *number* |
`fsi` | [*FilterSearchInterface*](../interfaces/interfaces_filterinterfaces.filtersearchinterface.md) |
`peopleIndex` | *string* |
`tagIds` | *string*[] |
`isExcel` | *boolean* |

**Returns:** *Promise*<[*PersonSearchResponse*](../modules/interfaces_elasticdocuments.md#personsearchresponse)\>

Implementation of: [NameSearchResource](../interfaces/resources_namesearchresource.namesearchresource.md)

Defined in: [packages/sdk/src/resources/NameSearchResourceClient.ts:18](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/NameSearchResourceClient.ts#L18)
