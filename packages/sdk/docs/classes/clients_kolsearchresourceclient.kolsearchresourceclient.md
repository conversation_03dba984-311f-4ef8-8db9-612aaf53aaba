[@h1nyc/search-sdk](../README.md) / [clients/KolSearchResourceClient](../modules/clients_kolsearchresourceclient.md) / KolSearchResourceClient

# Class: KolSearchResourceClient

[clients/KolSearchResourceClient](../modules/clients_kolsearchresourceclient.md).KolSearchResourceClient

## Hierarchy

* *RpcResourceClient*

  ↳ **KolSearchResourceClient**

## Implements

* [*KolSearchResource*](../interfaces/resources_kolsearchresource.kolsearchresource.md)

## Table of contents

### Constructors

- [constructor](clients_kolsearchresourceclient.kolsearchresourceclient.md#constructor)

### Properties

- [rpcClient](clients_kolsearchresourceclient.kolsearchresourceclient.md#rpcclient)

### Methods

- [isReady](clients_kolsearchresourceclient.kolsearchresourceclient.md#isready)
- [keywordSearch](clients_kolsearchresourceclient.kolsearchresourceclient.md#keywordsearch)

## Constructors

### constructor

\+ **new KolSearchResourceClient**(`signingSecret`: *string*, `redisOptions?`: RedisOptions): [*KolSearchResourceClient*](clients_kolsearchresourceclient.kolsearchresourceclient.md)

#### Parameters:

Name | Type |
------ | ------ |
`signingSecret` | *string* |
`redisOptions?` | RedisOptions |

**Returns:** [*KolSearchResourceClient*](clients_kolsearchresourceclient.kolsearchresourceclient.md)

Defined in: [packages/sdk/src/clients/KolSearchResourceClient.ts:7](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/clients/KolSearchResourceClient.ts#L7)

## Properties

### rpcClient

• **rpcClient**: *default*

Defined in: node_modules/@h1nyc/systems-rpc/dist/RpcResourceClient.d.ts:3

## Methods

### isReady

▸ **isReady**(): *Promise*<*boolean*\>

**Returns:** *Promise*<*boolean*\>

Implementation of: [KolSearchResource](../interfaces/resources_kolsearchresource.kolsearchresource.md)

Defined in: [packages/sdk/src/clients/KolSearchResourceClient.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/clients/KolSearchResourceClient.ts#L12)
