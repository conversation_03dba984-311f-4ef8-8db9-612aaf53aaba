[@h1nyc/search-sdk](../README.md) / [flow/types](../modules/flow_types.md) / Metrics

# Enumeration: Metrics

[flow/types](../modules/flow_types.md).Metrics

## Table of contents

### Enumeration members

- [collaboratorCount](flow_types.metrics.md#collaboratorcount)
- [congress](flow_types.metrics.md#congress)
- [diagnoses](flow_types.metrics.md#diagnoses)
- [diagnoses.internalCount](flow_types.metrics.md#diagnoses.internalcount)
- [payments](flow_types.metrics.md#payments)
- [payments.amount](flow_types.metrics.md#payments.amount)
- [procedures](flow_types.metrics.md#procedures)
- [procedures.internalCount](flow_types.metrics.md#procedures.internalcount)
- [profile](flow_types.metrics.md#profile)
- [publications](flow_types.metrics.md#publications)
- [publications.citationCount](flow_types.metrics.md#publications.citationcount)
- [publications.microBloggingCount](flow_types.metrics.md#publications.microbloggingcount)
- [trials](flow_types.metrics.md#trials)

## Enumeration members

### collaboratorCount

• **collaboratorCount**: = "collaboratorCount"

Defined in: [packages/sdk/src/flow/types.ts:8](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L8)

___

### congress

• **congress**: = "congress"

Defined in: [packages/sdk/src/flow/types.ts:4](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L4)

___

### diagnoses

• **diagnoses**: = "diagnoses"

Defined in: [packages/sdk/src/flow/types.ts:11](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L11)

___

### diagnoses.internalCount

• **diagnoses.internalCount**: = "diagnoses.internalCount"

Defined in: [packages/sdk/src/flow/types.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L12)

___

### payments

• **payments**: = "payments"

Defined in: [packages/sdk/src/flow/types.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L5)

___

### payments.amount

• **payments.amount**: = "payments.amount"

Defined in: [packages/sdk/src/flow/types.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L6)

___

### procedures

• **procedures**: = "procedures"

Defined in: [packages/sdk/src/flow/types.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L9)

___

### procedures.internalCount

• **procedures.internalCount**: = "procedures.internalCount"

Defined in: [packages/sdk/src/flow/types.ts:10](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L10)

___

### profile

• **profile**: = "profile"

Defined in: [packages/sdk/src/flow/types.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L13)

___

### publications

• **publications**: = "publications"

Defined in: [packages/sdk/src/flow/types.ts:1](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L1)

___

### publications.citationCount

• **publications.citationCount**: = "publications.citationCount"

Defined in: [packages/sdk/src/flow/types.ts:2](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L2)

___

### publications.microBloggingCount

• **publications.microBloggingCount**: = "publications.microBloggingCount"

Defined in: [packages/sdk/src/flow/types.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L3)

___

### trials

• **trials**: = "trials"

Defined in: [packages/sdk/src/flow/types.ts:7](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L7)
