[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / InputState

# Enumeration: InputState

[index](../modules/index.md).InputState

## Table of contents

### Enumeration members

- [CLICKED](index.inputstate.md#clicked)
- [ENTER](index.inputstate.md#enter)
- [REST](index.inputstate.md#rest)
- [TYPE](index.inputstate.md#type)

## Enumeration members

### CLICKED

• **CLICKED**: = 1

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:11](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L11)

___

### ENTER

• **ENTER**: = 3

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L13)

___

### REST

• **REST**: = 0

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:10](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L10)

___

### TYPE

• **TYPE**: = 2

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L12)
