[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / IndexTypes

# Enumeration: IndexTypes

[interfaces](../modules/interfaces.md).IndexTypes

## Table of contents

### Enumeration members

- [claims](interfaces.indextypes.md#claims)
- [congress](interfaces.indextypes.md#congress)
- [grant](interfaces.indextypes.md#grant)
- [payment](interfaces.indextypes.md#payment)
- [person](interfaces.indextypes.md#person)
- [profile](interfaces.indextypes.md#profile)
- [publication](interfaces.indextypes.md#publication)
- [referrals](interfaces.indextypes.md#referrals)
- [tag](interfaces.indextypes.md#tag)
- [trial](interfaces.indextypes.md#trial)

## Enumeration members

### claims

• **claims**: = 7

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:332](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L332)

___

### congress

• **congress**: = 3

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:328](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L328)

___

### grant

• **grant**: = 4

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:329](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L329)

___

### payment

• **payment**: = 5

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:330](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L330)

___

### person

• **person**: = 0

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:325](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L325)

___

### profile

• **profile**: = 8

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:333](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L333)

___

### publication

• **publication**: = 1

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:326](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L326)

___

### referrals

• **referrals**: = 9

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:334](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L334)

___

### tag

• **tag**: = 6

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:331](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L331)

___

### trial

• **trial**: = 2

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:327](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L327)
