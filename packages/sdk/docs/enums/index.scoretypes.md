[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / ScoreTypes

# Enumeration: ScoreTypes

[index](../modules/index.md).ScoreTypes

## Table of contents

### Enumeration members

- [avg](index.scoretypes.md#avg)
- [sum](index.scoretypes.md#sum)

## Enumeration members

### avg

• **avg**: = "avg"

Defined in: [packages/sdk/src/flow/types.ts:18](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L18)

___

### sum

• **sum**: = "sum"

Defined in: [packages/sdk/src/flow/types.ts:17](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L17)
