[@h1nyc/search-sdk](../README.md) / [interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md) / FilterTypes

# Enumeration: FilterTypes

[interfaces/filterInterfaces](../modules/interfaces_filterinterfaces.md).FilterTypes

## Table of contents

### Enumeration members

- [BOOL](interfaces_filterinterfaces.filtertypes.md#bool)
- [DATERANGE](interfaces_filterinterfaces.filtertypes.md#daterange)
- [MIN](interfaces_filterinterfaces.filtertypes.md#min)
- [MONEYRANGE](interfaces_filterinterfaces.filtertypes.md#moneyrange)
- [MULTI](interfaces_filterinterfaces.filtertypes.md#multi)
- [SINGLE](interfaces_filterinterfaces.filtertypes.md#single)

## Enumeration members

### BOOL

• **BOOL**: = 4

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L6)

___

### DATERANGE

• **DATERANGE**: = 0

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:2](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L2)

___

### MIN

• **MIN**: = 5

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:7](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L7)

___

### MONEYRANGE

• **MONEYRANGE**: = 1

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L3)

___

### MULTI

• **MULTI**: = 2

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:4](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L4)

___

### SINGLE

• **SINGLE**: = 3

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L5)
