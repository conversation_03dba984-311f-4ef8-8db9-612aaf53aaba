[@h1nyc/search-sdk](../README.md) / [flow/types](../modules/flow_types.md) / ScoreTypes

# Enumeration: ScoreTypes

[flow/types](../modules/flow_types.md).ScoreTypes

## Table of contents

### Enumeration members

- [avg](flow_types.scoretypes.md#avg)
- [sum](flow_types.scoretypes.md#sum)

## Enumeration members

### avg

• **avg**: = "avg"

Defined in: [packages/sdk/src/flow/types.ts:18](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L18)

___

### sum

• **sum**: = "sum"

Defined in: [packages/sdk/src/flow/types.ts:17](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/flow/types.ts#L17)
