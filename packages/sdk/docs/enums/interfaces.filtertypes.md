[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / FilterTypes

# Enumeration: FilterTypes

[interfaces](../modules/interfaces.md).FilterTypes

## Table of contents

### Enumeration members

- [<PERSON><PERSON><PERSON>](interfaces.filtertypes.md#bool)
- [DATERANGE](interfaces.filtertypes.md#daterange)
- [MIN](interfaces.filtertypes.md#min)
- [MONEYRANGE](interfaces.filtertypes.md#moneyrange)
- [MULTI](interfaces.filtertypes.md#multi)
- [SINGLE](interfaces.filtertypes.md#single)

## Enumeration members

### BOOL

• **BOOL**: = 4

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:6](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L6)

___

### DATERANGE

• **DATERANGE**: = 0

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:2](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L2)

___

### MIN

• **MIN**: = 5

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:7](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L7)

___

### MONEYRANGE

• **MONEYRANGE**: = 1

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:3](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L3)

___

### MULTI

• **MULTI**: = 2

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:4](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L4)

___

### SINGLE

• **SINGLE**: = 3

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:5](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L5)
