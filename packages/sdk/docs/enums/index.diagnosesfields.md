[@h1nyc/search-sdk](../README.md) / [index](../modules/index.md) / DiagnosesFields

# Enumeration: DiagnosesFields

[index](../modules/index.md).DiagnosesFields

## Table of contents

### Enumeration members

- [CodeScheme](index.diagnosesfields.md#codescheme)
- [Count](index.diagnosesfields.md#count)
- [Description](index.diagnosesfields.md#description)
- [DiagnosisCode](index.diagnosesfields.md#diagnosiscode)
- [InternalCount](index.diagnosesfields.md#internalcount)
- [PercentOfClaims](index.diagnosesfields.md#percentofclaims)

## Enumeration members

### CodeScheme

• **CodeScheme**: = "DRG\_diagnoses.codeScheme.keyword"

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:14](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L14)

___

### Count

• **Count**: = "DRG\_diagnoses.count"

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:12](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L12)

___

### Description

• **Description**: = "DRG\_diagnoses.description.keyword"

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:9](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L9)

___

### DiagnosisCode

• **DiagnosisCode**: = "DRG\_diagnoses.diagnosisCode"

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:10](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L10)

___

### InternalCount

• **InternalCount**: = "DRG\_diagnoses.internalCount"

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:13](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L13)

___

### PercentOfClaims

• **PercentOfClaims**: = "DRG\_diagnoses.pctOfClaims"

Defined in: [packages/sdk/src/resources/ClaimsResource.ts:11](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/resources/ClaimsResource.ts#L11)
