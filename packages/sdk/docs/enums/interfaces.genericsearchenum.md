[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / GenericSearchEnum

# Enumeration: GenericSearchEnum

[interfaces](../modules/interfaces.md).GenericSearchEnum

## Table of contents

### Enumeration members

- [AFFILIATION](interfaces.genericsearchenum.md#affiliation)
- [CLAIM](interfaces.genericsearchenum.md#claim)
- [CLINICALTRIAL](interfaces.genericsearchenum.md#clinicaltrial)
- [CONGRESS](interfaces.genericsearchenum.md#congress)
- [DIAGNOSES](interfaces.genericsearchenum.md#diagnoses)
- [GENERIC](interfaces.genericsearchenum.md#generic)
- [GRANTS](interfaces.genericsearchenum.md#grants)
- [PAYMENTS](interfaces.genericsearchenum.md#payments)
- [PERSONS](interfaces.genericsearchenum.md#persons)
- [PROCEDURES](interfaces.genericsearchenum.md#procedures)
- [PUBLICATION](interfaces.genericsearchenum.md#publication)
- [TAG](interfaces.genericsearchenum.md#tag)

## Enumeration members

### AFFILIATION

• **AFFILIATION**: = 0

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:59](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L59)

___

### CLAIM

• **CLAIM**: = 9

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:68](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L68)

___

### CLINICALTRIAL

• **CLINICALTRIAL**: = 1

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:60](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L60)

___

### CONGRESS

• **CONGRESS**: = 2

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:61](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L61)

___

### DIAGNOSES

• **DIAGNOSES**: = 10

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:69](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L69)

___

### GENERIC

• **GENERIC**: = 7

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:66](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L66)

___

### GRANTS

• **GRANTS**: = 4

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:63](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L63)

___

### PAYMENTS

• **PAYMENTS**: = 5

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:64](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L64)

___

### PERSONS

• **PERSONS**: = 6

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:65](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L65)

___

### PROCEDURES

• **PROCEDURES**: = 11

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:70](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L70)

___

### PUBLICATION

• **PUBLICATION**: = 3

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:62](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L62)

___

### TAG

• **TAG**: = 8

Defined in: [packages/sdk/src/interfaces/elasticDocuments.ts:67](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/elasticDocuments.ts#L67)
