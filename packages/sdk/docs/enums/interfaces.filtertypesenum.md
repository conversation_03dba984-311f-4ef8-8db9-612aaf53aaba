[@h1nyc/search-sdk](../README.md) / [interfaces](../modules/interfaces.md) / FilterTypesEnum

# Enumeration: FilterTypesEnum

[interfaces](../modules/interfaces.md).FilterTypesEnum

## Table of contents

### Enumeration members

- [clinicalCurrentPhase](interfaces.filtertypesenum.md#clinicalcurrentphase)
- [clinicalCurrentStatus](interfaces.filtertypesenum.md#clinicalcurrentstatus)
- [clinicalFunderType](interfaces.filtertypesenum.md#clinicalfundertype)
- [clinicalPastStages](interfaces.filtertypesenum.md#clinicalpaststages)
- [clinicalPastStatus](interfaces.filtertypesenum.md#clinicalpaststatus)
- [clinicalPrimaryCompletionDate](interfaces.filtertypesenum.md#clinicalprimarycompletiondate)
- [clinicalPrinicipleInvestigator](interfaces.filtertypesenum.md#clinicalprinicipleinvestigator)
- [clinicalStudyInterventional](interfaces.filtertypesenum.md#clinicalstudyinterventional)
- [clinicalStudyObservational](interfaces.filtertypesenum.md#clinicalstudyobservational)
- [clinicalStudyStartDate](interfaces.filtertypesenum.md#clinicalstudystartdate)
- [clinincalSponsor](interfaces.filtertypesenum.md#clinincalsponsor)
- [congressMinCount](interfaces.filtertypesenum.md#congressmincount)
- [congressName](interfaces.filtertypesenum.md#congressname)
- [congressOrganizerName](interfaces.filtertypesenum.md#congressorganizername)
- [congressSessionType](interfaces.filtertypesenum.md#congresssessiontype)
- [congressType](interfaces.filtertypesenum.md#congresstype)
- [diagnosesICD](interfaces.filtertypesenum.md#diagnosesicd)
- [diagnosesICDMinCount](interfaces.filtertypesenum.md#diagnosesicdmincount)
- [grantFunder](interfaces.filtertypesenum.md#grantfunder)
- [grantMinAmount](interfaces.filtertypesenum.md#grantminamount)
- [institution](interfaces.filtertypesenum.md#institution)
- [institutionType](interfaces.filtertypesenum.md#institutiontype)
- [kolType](interfaces.filtertypesenum.md#koltype)
- [locationCity](interfaces.filtertypesenum.md#locationcity)
- [locationCountry](interfaces.filtertypesenum.md#locationcountry)
- [locationRegion](interfaces.filtertypesenum.md#locationregion)
- [locationState](interfaces.filtertypesenum.md#locationstate)
- [locationZipCode](interfaces.filtertypesenum.md#locationzipcode)
- [minReferralsReceived](interfaces.filtertypesenum.md#minreferralsreceived)
- [minReferralsSent](interfaces.filtertypesenum.md#minreferralssent)
- [npi](interfaces.filtertypesenum.md#npi)
- [pastAndPresentWorkInstitutions](interfaces.filtertypesenum.md#pastandpresentworkinstitutions)
- [paymentCompany](interfaces.filtertypesenum.md#paymentcompany)
- [paymentDate](interfaces.filtertypesenum.md#paymentdate)
- [paymentDrugOrDevice](interfaces.filtertypesenum.md#paymentdrugordevice)
- [paymentMinAmount](interfaces.filtertypesenum.md#paymentminamount)
- [paymentNature](interfaces.filtertypesenum.md#paymentnature)
- [paymentSize](interfaces.filtertypesenum.md#paymentsize)
- [presentWorkInstitutions](interfaces.filtertypesenum.md#presentworkinstitutions)
- [proceduresCPT](interfaces.filtertypesenum.md#procedurescpt)
- [proceduresCPTMinCount](interfaces.filtertypesenum.md#procedurescptmincount)
- [proceduresHCPC](interfaces.filtertypesenum.md#procedureshcpc)
- [proceduresHCPCMinCount](interfaces.filtertypesenum.md#procedureshcpcmincount)
- [profileRole](interfaces.filtertypesenum.md#profilerole)
- [publicationCitationCount](interfaces.filtertypesenum.md#publicationcitationcount)
- [publicationJournal](interfaces.filtertypesenum.md#publicationjournal)
- [publicationMinCount](interfaces.filtertypesenum.md#publicationmincount)
- [publicationPmid](interfaces.filtertypesenum.md#publicationpmid)
- [publicationPublisher](interfaces.filtertypesenum.md#publicationpublisher)
- [publicationPublisherType](interfaces.filtertypesenum.md#publicationpublishertype)
- [publicationType](interfaces.filtertypesenum.md#publicationtype)
- [publicationYear](interfaces.filtertypesenum.md#publicationyear)
- [publicationsSocialMediaCount](interfaces.filtertypesenum.md#publicationssocialmediacount)
- [referralsServiceLine](interfaces.filtertypesenum.md#referralsserviceline)
- [studentInstitutions](interfaces.filtertypesenum.md#studentinstitutions)
- [tagName](interfaces.filtertypesenum.md#tagname)
- [therapeuticArea](interfaces.filtertypesenum.md#therapeuticarea)
- [title](interfaces.filtertypesenum.md#title)
- [trialFunderType](interfaces.filtertypesenum.md#trialfundertype)
- [trialMinCount](interfaces.filtertypesenum.md#trialmincount)
- [trialPhase](interfaces.filtertypesenum.md#trialphase)
- [trialSponsor](interfaces.filtertypesenum.md#trialsponsor)
- [trialStatus](interfaces.filtertypesenum.md#trialstatus)
- [trialStudyType](interfaces.filtertypesenum.md#trialstudytype)

## Enumeration members

### clinicalCurrentPhase

• **clinicalCurrentPhase**: = 0

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:17](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L17)

___

### clinicalCurrentStatus

• **clinicalCurrentStatus**: = 2

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:19](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L19)

___

### clinicalFunderType

• **clinicalFunderType**: = 6

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:23](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L23)

___

### clinicalPastStages

• **clinicalPastStages**: = 1

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:18](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L18)

___

### clinicalPastStatus

• **clinicalPastStatus**: = 3

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:20](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L20)

___

### clinicalPrimaryCompletionDate

• **clinicalPrimaryCompletionDate**: = 5

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:22](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L22)

___

### clinicalPrinicipleInvestigator

• **clinicalPrinicipleInvestigator**: = 29

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:46](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L46)

___

### clinicalStudyInterventional

• **clinicalStudyInterventional**: = 27

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:44](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L44)

___

### clinicalStudyObservational

• **clinicalStudyObservational**: = 28

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:45](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L45)

___

### clinicalStudyStartDate

• **clinicalStudyStartDate**: = 4

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:21](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L21)

___

### clinincalSponsor

• **clinincalSponsor**: = 7

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:24](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L24)

___

### congressMinCount

• **congressMinCount**: = 41

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:58](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L58)

___

### congressName

• **congressName**: = 42

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:59](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L59)

___

### congressOrganizerName

• **congressOrganizerName**: = 44

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:61](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L61)

___

### congressSessionType

• **congressSessionType**: = 45

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:62](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L62)

___

### congressType

• **congressType**: = 43

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:60](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L60)

___

### diagnosesICD

• **diagnosesICD**: = 50

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:67](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L67)

___

### diagnosesICDMinCount

• **diagnosesICDMinCount**: = 53

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:70](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L70)

___

### grantFunder

• **grantFunder**: = 47

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:64](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L64)

___

### grantMinAmount

• **grantMinAmount**: = 46

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:63](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L63)

___

### institution

• **institution**: = 8

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:25](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L25)

___

### institutionType

• **institutionType**: = 9

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:26](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L26)

___

### kolType

• **kolType**: = 34

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:51](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L51)

___

### locationCity

• **locationCity**: = 12

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:29](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L29)

___

### locationCountry

• **locationCountry**: = 33

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:50](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L50)

___

### locationRegion

• **locationRegion**: = 11

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:28](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L28)

___

### locationState

• **locationState**: = 30

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:47](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L47)

___

### locationZipCode

• **locationZipCode**: = 57

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:74](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L74)

___

### minReferralsReceived

• **minReferralsReceived**: = 62

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:79](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L79)

___

### minReferralsSent

• **minReferralsSent**: = 63

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:80](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L80)

___

### npi

• **npi**: = 49

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:66](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L66)

___

### pastAndPresentWorkInstitutions

• **pastAndPresentWorkInstitutions**: = 59

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:76](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L76)

___

### paymentCompany

• **paymentCompany**: = 15

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:32](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L32)

___

### paymentDate

• **paymentDate**: = 17

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:34](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L34)

___

### paymentDrugOrDevice

• **paymentDrugOrDevice**: = 19

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:36](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L36)

___

### paymentMinAmount

• **paymentMinAmount**: = 18

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:35](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L35)

___

### paymentNature

• **paymentNature**: = 14

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:31](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L31)

___

### paymentSize

• **paymentSize**: = 16

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:33](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L33)

___

### presentWorkInstitutions

• **presentWorkInstitutions**: = 58

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:75](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L75)

___

### proceduresCPT

• **proceduresCPT**: = 51

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:68](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L68)

___

### proceduresCPTMinCount

• **proceduresCPTMinCount**: = 54

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:71](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L71)

___

### proceduresHCPC

• **proceduresHCPC**: = 52

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:69](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L69)

___

### proceduresHCPCMinCount

• **proceduresHCPCMinCount**: = 55

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:72](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L72)

___

### profileRole

• **profileRole**: = 56

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:73](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L73)

___

### publicationCitationCount

• **publicationCitationCount**: = 24

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:41](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L41)

___

### publicationJournal

• **publicationJournal**: = 21

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:38](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L38)

___

### publicationMinCount

• **publicationMinCount**: = 32

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:49](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L49)

___

### publicationPmid

• **publicationPmid**: = 22

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:39](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L39)

___

### publicationPublisher

• **publicationPublisher**: = 26

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:43](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L43)

___

### publicationPublisherType

• **publicationPublisherType**: = 31

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:48](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L48)

___

### publicationType

• **publicationType**: = 23

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:40](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L40)

___

### publicationYear

• **publicationYear**: = 20

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:37](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L37)

___

### publicationsSocialMediaCount

• **publicationsSocialMediaCount**: = 25

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:42](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L42)

___

### referralsServiceLine

• **referralsServiceLine**: = 61

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:78](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L78)

___

### studentInstitutions

• **studentInstitutions**: = 60

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:77](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L77)

___

### tagName

• **tagName**: = 48

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:65](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L65)

___

### therapeuticArea

• **therapeuticArea**: = 10

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:27](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L27)

___

### title

• **title**: = 13

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:30](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L30)

___

### trialFunderType

• **trialFunderType**: = 39

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:56](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L56)

___

### trialMinCount

• **trialMinCount**: = 35

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:52](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L52)

___

### trialPhase

• **trialPhase**: = 36

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:53](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L53)

___

### trialSponsor

• **trialSponsor**: = 40

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:57](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L57)

___

### trialStatus

• **trialStatus**: = 37

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:54](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L54)

___

### trialStudyType

• **trialStudyType**: = 38

Defined in: [packages/sdk/src/interfaces/filterInterfaces.ts:55](https://github.com/shore-group/h1-search/blob/0f7bebc/packages/sdk/src/interfaces/filterInterfaces.ts#L55)
