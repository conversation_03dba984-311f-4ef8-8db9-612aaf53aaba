syntax = "proto3";

service DrugSearchService {
  rpc searchDrugValues(DrugValueSearchInput) returns (DrugValues) {}
  rpc searchGenericDrugs(GenericDrugSearchInput) returns (GenericDrugs) {}
  rpc autocompleteGenericDrugs(AutocompleteGenericDrugInput) returns (AutocompleteGenericDrugs) {}
}

message DrugValueSearchInput {
  string searchText = 1;
  uint32 size = 2;
}

enum GrpcDrugTypeEnum {
  ACTIVE_INGREDIENT = 0;
  DRUG_CLASS = 1;
  GENERIC_NAME = 2;
  BRAND_NAME = 3;
}

message DrugValue {
  string value = 1;
  GrpcDrugTypeEnum type = 2;
}

message DrugValues {
  repeated DrugValue values = 1;
}

message GenericDrugSearchInput {
  string value = 1;
  GrpcDrugTypeEnum type = 2;
  uint32 size = 3;
  uint32 from = 4;
}

message AutocompleteGenericDrugInput {
  string searchText = 1;
  uint32 size = 2;
}

message GenericDrug {
  string genericName = 1;
  repeated string brandName = 2;
  repeated string drugClass = 3;
  repeated string activeIngredient = 4;
  int64 patientCount = 5;
  string indicationsAndUsage = 6;
}

message GenericDrugs {
  uint32 total = 1;
  repeated GenericDrug genericDrugs = 2;
}

message AutocompleteGenericDrug {
  string genericName = 1;
  string indicationsAndUsage = 6;
}

message AutocompleteGenericDrugs {
  repeated AutocompleteGenericDrug genericDrugs = 1;
}
