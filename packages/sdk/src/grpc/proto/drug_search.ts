/* eslint-disable */
import Long from "long";
import type { CallContext, CallOptions } from "nice-grpc-common";
import _m0 from "protobufjs/minimal";

export const protobufPackage = "";

export enum GrpcDrugTypeEnum {
  ACTIVE_INGREDIENT = 0,
  DRUG_CLASS = 1,
  GENERIC_NAME = 2,
  BRAND_NAME = 3,
  UNRECOGNIZED = -1,
}

export function grpcDrugTypeEnumFromJSON(object: any): GrpcDrugTypeEnum {
  switch (object) {
    case 0:
    case "ACTIVE_INGREDIENT":
      return GrpcDrugTypeEnum.ACTIVE_INGREDIENT;
    case 1:
    case "DRUG_CLASS":
      return GrpcDrugTypeEnum.DRUG_CLASS;
    case 2:
    case "GENERIC_NAME":
      return GrpcDrugTypeEnum.GENERIC_NAME;
    case 3:
    case "BRAND_NAME":
      return GrpcDrugTypeEnum.BRAND_NAME;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GrpcDrugTypeEnum.UNRECOGNIZED;
  }
}

export function grpcDrugTypeEnumToJSON(object: GrpcDrugTypeEnum): string {
  switch (object) {
    case GrpcDrugTypeEnum.ACTIVE_INGREDIENT:
      return "ACTIVE_INGREDIENT";
    case GrpcDrugTypeEnum.DRUG_CLASS:
      return "DRUG_CLASS";
    case GrpcDrugTypeEnum.GENERIC_NAME:
      return "GENERIC_NAME";
    case GrpcDrugTypeEnum.BRAND_NAME:
      return "BRAND_NAME";
    case GrpcDrugTypeEnum.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface DrugValueSearchInput {
  searchText: string;
  size: number;
}

export interface DrugValue {
  value: string;
  type: GrpcDrugTypeEnum;
}

export interface DrugValues {
  values: DrugValue[];
}

export interface GenericDrugSearchInput {
  value: string;
  type: GrpcDrugTypeEnum;
  size: number;
  from: number;
}

export interface AutocompleteGenericDrugInput {
  searchText: string;
  size: number;
}

export interface GenericDrug {
  genericName: string;
  brandName: string[];
  drugClass: string[];
  activeIngredient: string[];
  patientCount: number;
  indicationsAndUsage: string;
}

export interface GenericDrugs {
  total: number;
  genericDrugs: GenericDrug[];
}

export interface AutocompleteGenericDrug {
  genericName: string;
  indicationsAndUsage: string;
}

export interface AutocompleteGenericDrugs {
  genericDrugs: AutocompleteGenericDrug[];
}

function createBaseDrugValueSearchInput(): DrugValueSearchInput {
  return { searchText: "", size: 0 };
}

export const DrugValueSearchInput = {
  encode(message: DrugValueSearchInput, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.searchText !== "") {
      writer.uint32(10).string(message.searchText);
    }
    if (message.size !== 0) {
      writer.uint32(16).uint32(message.size);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DrugValueSearchInput {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDrugValueSearchInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.searchText = reader.string();
          break;
        case 2:
          message.size = reader.uint32();
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): DrugValueSearchInput {
    return {
      searchText: isSet(object.searchText) ? String(object.searchText) : "",
      size: isSet(object.size) ? Number(object.size) : 0,
    };
  },

  toJSON(message: DrugValueSearchInput): unknown {
    const obj: any = {};
    message.searchText !== undefined && (obj.searchText = message.searchText);
    message.size !== undefined && (obj.size = Math.round(message.size));
    return obj;
  },

  create(base?: DeepPartial<DrugValueSearchInput>): DrugValueSearchInput {
    return DrugValueSearchInput.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<DrugValueSearchInput>): DrugValueSearchInput {
    const message = createBaseDrugValueSearchInput();
    message.searchText = object.searchText ?? "";
    message.size = object.size ?? 0;
    return message;
  },
};

function createBaseDrugValue(): DrugValue {
  return { value: "", type: 0 };
}

export const DrugValue = {
  encode(message: DrugValue, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.value !== "") {
      writer.uint32(10).string(message.value);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DrugValue {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDrugValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.value = reader.string();
          break;
        case 2:
          message.type = reader.int32() as any;
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): DrugValue {
    return {
      value: isSet(object.value) ? String(object.value) : "",
      type: isSet(object.type) ? grpcDrugTypeEnumFromJSON(object.type) : 0,
    };
  },

  toJSON(message: DrugValue): unknown {
    const obj: any = {};
    message.value !== undefined && (obj.value = message.value);
    message.type !== undefined && (obj.type = grpcDrugTypeEnumToJSON(message.type));
    return obj;
  },

  create(base?: DeepPartial<DrugValue>): DrugValue {
    return DrugValue.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<DrugValue>): DrugValue {
    const message = createBaseDrugValue();
    message.value = object.value ?? "";
    message.type = object.type ?? 0;
    return message;
  },
};

function createBaseDrugValues(): DrugValues {
  return { values: [] };
}

export const DrugValues = {
  encode(message: DrugValues, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.values) {
      DrugValue.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DrugValues {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDrugValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.values.push(DrugValue.decode(reader, reader.uint32()));
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): DrugValues {
    return { values: Array.isArray(object?.values) ? object.values.map((e: any) => DrugValue.fromJSON(e)) : [] };
  },

  toJSON(message: DrugValues): unknown {
    const obj: any = {};
    if (message.values) {
      obj.values = message.values.map((e) => e ? DrugValue.toJSON(e) : undefined);
    } else {
      obj.values = [];
    }
    return obj;
  },

  create(base?: DeepPartial<DrugValues>): DrugValues {
    return DrugValues.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<DrugValues>): DrugValues {
    const message = createBaseDrugValues();
    message.values = object.values?.map((e) => DrugValue.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGenericDrugSearchInput(): GenericDrugSearchInput {
  return { value: "", type: 0, size: 0, from: 0 };
}

export const GenericDrugSearchInput = {
  encode(message: GenericDrugSearchInput, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.value !== "") {
      writer.uint32(10).string(message.value);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.size !== 0) {
      writer.uint32(24).uint32(message.size);
    }
    if (message.from !== 0) {
      writer.uint32(32).uint32(message.from);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GenericDrugSearchInput {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenericDrugSearchInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.value = reader.string();
          break;
        case 2:
          message.type = reader.int32() as any;
          break;
        case 3:
          message.size = reader.uint32();
          break;
        case 4:
          message.from = reader.uint32();
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): GenericDrugSearchInput {
    return {
      value: isSet(object.value) ? String(object.value) : "",
      type: isSet(object.type) ? grpcDrugTypeEnumFromJSON(object.type) : 0,
      size: isSet(object.size) ? Number(object.size) : 0,
      from: isSet(object.from) ? Number(object.from) : 0,
    };
  },

  toJSON(message: GenericDrugSearchInput): unknown {
    const obj: any = {};
    message.value !== undefined && (obj.value = message.value);
    message.type !== undefined && (obj.type = grpcDrugTypeEnumToJSON(message.type));
    message.size !== undefined && (obj.size = Math.round(message.size));
    message.from !== undefined && (obj.from = Math.round(message.from));
    return obj;
  },

  create(base?: DeepPartial<GenericDrugSearchInput>): GenericDrugSearchInput {
    return GenericDrugSearchInput.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<GenericDrugSearchInput>): GenericDrugSearchInput {
    const message = createBaseGenericDrugSearchInput();
    message.value = object.value ?? "";
    message.type = object.type ?? 0;
    message.size = object.size ?? 0;
    message.from = object.from ?? 0;
    return message;
  },
};

function createBaseAutocompleteGenericDrugInput(): AutocompleteGenericDrugInput {
  return { searchText: "", size: 0 };
}

export const AutocompleteGenericDrugInput = {
  encode(message: AutocompleteGenericDrugInput, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.searchText !== "") {
      writer.uint32(10).string(message.searchText);
    }
    if (message.size !== 0) {
      writer.uint32(16).uint32(message.size);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AutocompleteGenericDrugInput {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAutocompleteGenericDrugInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.searchText = reader.string();
          break;
        case 2:
          message.size = reader.uint32();
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): AutocompleteGenericDrugInput {
    return {
      searchText: isSet(object.searchText) ? String(object.searchText) : "",
      size: isSet(object.size) ? Number(object.size) : 0,
    };
  },

  toJSON(message: AutocompleteGenericDrugInput): unknown {
    const obj: any = {};
    message.searchText !== undefined && (obj.searchText = message.searchText);
    message.size !== undefined && (obj.size = Math.round(message.size));
    return obj;
  },

  create(base?: DeepPartial<AutocompleteGenericDrugInput>): AutocompleteGenericDrugInput {
    return AutocompleteGenericDrugInput.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<AutocompleteGenericDrugInput>): AutocompleteGenericDrugInput {
    const message = createBaseAutocompleteGenericDrugInput();
    message.searchText = object.searchText ?? "";
    message.size = object.size ?? 0;
    return message;
  },
};

function createBaseGenericDrug(): GenericDrug {
  return {
    genericName: "",
    brandName: [],
    drugClass: [],
    activeIngredient: [],
    patientCount: 0,
    indicationsAndUsage: "",
  };
}

export const GenericDrug = {
  encode(message: GenericDrug, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.genericName !== "") {
      writer.uint32(10).string(message.genericName);
    }
    for (const v of message.brandName) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.drugClass) {
      writer.uint32(26).string(v!);
    }
    for (const v of message.activeIngredient) {
      writer.uint32(34).string(v!);
    }
    if (message.patientCount !== 0) {
      writer.uint32(40).int64(message.patientCount);
    }
    if (message.indicationsAndUsage !== "") {
      writer.uint32(50).string(message.indicationsAndUsage);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GenericDrug {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenericDrug();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.genericName = reader.string();
          break;
        case 2:
          message.brandName.push(reader.string());
          break;
        case 3:
          message.drugClass.push(reader.string());
          break;
        case 4:
          message.activeIngredient.push(reader.string());
          break;
        case 5:
          message.patientCount = longToNumber(reader.int64() as Long);
          break;
        case 6:
          message.indicationsAndUsage = reader.string();
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): GenericDrug {
    return {
      genericName: isSet(object.genericName) ? String(object.genericName) : "",
      brandName: Array.isArray(object?.brandName) ? object.brandName.map((e: any) => String(e)) : [],
      drugClass: Array.isArray(object?.drugClass) ? object.drugClass.map((e: any) => String(e)) : [],
      activeIngredient: Array.isArray(object?.activeIngredient)
        ? object.activeIngredient.map((e: any) => String(e))
        : [],
      patientCount: isSet(object.patientCount) ? Number(object.patientCount) : 0,
      indicationsAndUsage: isSet(object.indicationsAndUsage) ? String(object.indicationsAndUsage) : "",
    };
  },

  toJSON(message: GenericDrug): unknown {
    const obj: any = {};
    message.genericName !== undefined && (obj.genericName = message.genericName);
    if (message.brandName) {
      obj.brandName = message.brandName.map((e) => e);
    } else {
      obj.brandName = [];
    }
    if (message.drugClass) {
      obj.drugClass = message.drugClass.map((e) => e);
    } else {
      obj.drugClass = [];
    }
    if (message.activeIngredient) {
      obj.activeIngredient = message.activeIngredient.map((e) => e);
    } else {
      obj.activeIngredient = [];
    }
    message.patientCount !== undefined && (obj.patientCount = Math.round(message.patientCount));
    message.indicationsAndUsage !== undefined && (obj.indicationsAndUsage = message.indicationsAndUsage);
    return obj;
  },

  create(base?: DeepPartial<GenericDrug>): GenericDrug {
    return GenericDrug.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<GenericDrug>): GenericDrug {
    const message = createBaseGenericDrug();
    message.genericName = object.genericName ?? "";
    message.brandName = object.brandName?.map((e) => e) || [];
    message.drugClass = object.drugClass?.map((e) => e) || [];
    message.activeIngredient = object.activeIngredient?.map((e) => e) || [];
    message.patientCount = object.patientCount ?? 0;
    message.indicationsAndUsage = object.indicationsAndUsage ?? "";
    return message;
  },
};

function createBaseGenericDrugs(): GenericDrugs {
  return { total: 0, genericDrugs: [] };
}

export const GenericDrugs = {
  encode(message: GenericDrugs, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.total !== 0) {
      writer.uint32(8).uint32(message.total);
    }
    for (const v of message.genericDrugs) {
      GenericDrug.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GenericDrugs {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenericDrugs();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.total = reader.uint32();
          break;
        case 2:
          message.genericDrugs.push(GenericDrug.decode(reader, reader.uint32()));
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): GenericDrugs {
    return {
      total: isSet(object.total) ? Number(object.total) : 0,
      genericDrugs: Array.isArray(object?.genericDrugs)
        ? object.genericDrugs.map((e: any) => GenericDrug.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GenericDrugs): unknown {
    const obj: any = {};
    message.total !== undefined && (obj.total = Math.round(message.total));
    if (message.genericDrugs) {
      obj.genericDrugs = message.genericDrugs.map((e) => e ? GenericDrug.toJSON(e) : undefined);
    } else {
      obj.genericDrugs = [];
    }
    return obj;
  },

  create(base?: DeepPartial<GenericDrugs>): GenericDrugs {
    return GenericDrugs.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<GenericDrugs>): GenericDrugs {
    const message = createBaseGenericDrugs();
    message.total = object.total ?? 0;
    message.genericDrugs = object.genericDrugs?.map((e) => GenericDrug.fromPartial(e)) || [];
    return message;
  },
};

function createBaseAutocompleteGenericDrug(): AutocompleteGenericDrug {
  return { genericName: "", indicationsAndUsage: "" };
}

export const AutocompleteGenericDrug = {
  encode(message: AutocompleteGenericDrug, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.genericName !== "") {
      writer.uint32(10).string(message.genericName);
    }
    if (message.indicationsAndUsage !== "") {
      writer.uint32(50).string(message.indicationsAndUsage);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AutocompleteGenericDrug {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAutocompleteGenericDrug();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.genericName = reader.string();
          break;
        case 6:
          message.indicationsAndUsage = reader.string();
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): AutocompleteGenericDrug {
    return {
      genericName: isSet(object.genericName) ? String(object.genericName) : "",
      indicationsAndUsage: isSet(object.indicationsAndUsage) ? String(object.indicationsAndUsage) : "",
    };
  },

  toJSON(message: AutocompleteGenericDrug): unknown {
    const obj: any = {};
    message.genericName !== undefined && (obj.genericName = message.genericName);
    message.indicationsAndUsage !== undefined && (obj.indicationsAndUsage = message.indicationsAndUsage);
    return obj;
  },

  create(base?: DeepPartial<AutocompleteGenericDrug>): AutocompleteGenericDrug {
    return AutocompleteGenericDrug.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<AutocompleteGenericDrug>): AutocompleteGenericDrug {
    const message = createBaseAutocompleteGenericDrug();
    message.genericName = object.genericName ?? "";
    message.indicationsAndUsage = object.indicationsAndUsage ?? "";
    return message;
  },
};

function createBaseAutocompleteGenericDrugs(): AutocompleteGenericDrugs {
  return { genericDrugs: [] };
}

export const AutocompleteGenericDrugs = {
  encode(message: AutocompleteGenericDrugs, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.genericDrugs) {
      AutocompleteGenericDrug.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AutocompleteGenericDrugs {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAutocompleteGenericDrugs();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.genericDrugs.push(AutocompleteGenericDrug.decode(reader, reader.uint32()));
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): AutocompleteGenericDrugs {
    return {
      genericDrugs: Array.isArray(object?.genericDrugs)
        ? object.genericDrugs.map((e: any) => AutocompleteGenericDrug.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AutocompleteGenericDrugs): unknown {
    const obj: any = {};
    if (message.genericDrugs) {
      obj.genericDrugs = message.genericDrugs.map((e) => e ? AutocompleteGenericDrug.toJSON(e) : undefined);
    } else {
      obj.genericDrugs = [];
    }
    return obj;
  },

  create(base?: DeepPartial<AutocompleteGenericDrugs>): AutocompleteGenericDrugs {
    return AutocompleteGenericDrugs.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<AutocompleteGenericDrugs>): AutocompleteGenericDrugs {
    const message = createBaseAutocompleteGenericDrugs();
    message.genericDrugs = object.genericDrugs?.map((e) => AutocompleteGenericDrug.fromPartial(e)) || [];
    return message;
  },
};

export type DrugSearchServiceDefinition = typeof DrugSearchServiceDefinition;
export const DrugSearchServiceDefinition = {
  name: "DrugSearchService",
  fullName: "DrugSearchService",
  methods: {
    searchDrugValues: {
      name: "searchDrugValues",
      requestType: DrugValueSearchInput,
      requestStream: false,
      responseType: DrugValues,
      responseStream: false,
      options: {},
    },
    searchGenericDrugs: {
      name: "searchGenericDrugs",
      requestType: GenericDrugSearchInput,
      requestStream: false,
      responseType: GenericDrugs,
      responseStream: false,
      options: {},
    },
    autocompleteGenericDrugs: {
      name: "autocompleteGenericDrugs",
      requestType: AutocompleteGenericDrugInput,
      requestStream: false,
      responseType: AutocompleteGenericDrugs,
      responseStream: false,
      options: {},
    },
  },
} as const;

export interface DrugSearchServiceImplementation<CallContextExt = {}> {
  searchDrugValues(
    request: DrugValueSearchInput,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<DrugValues>>;
  searchGenericDrugs(
    request: GenericDrugSearchInput,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<GenericDrugs>>;
  autocompleteGenericDrugs(
    request: AutocompleteGenericDrugInput,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<AutocompleteGenericDrugs>>;
}

export interface DrugSearchServiceClient<CallOptionsExt = {}> {
  searchDrugValues(
    request: DeepPartial<DrugValueSearchInput>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<DrugValues>;
  searchGenericDrugs(
    request: DeepPartial<GenericDrugSearchInput>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<GenericDrugs>;
  autocompleteGenericDrugs(
    request: DeepPartial<AutocompleteGenericDrugInput>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<AutocompleteGenericDrugs>;
}

declare var self: any | undefined;
declare var window: any | undefined;
declare var global: any | undefined;
var tsProtoGlobalThis: any = (() => {
  if (typeof globalThis !== "undefined") {
    return globalThis;
  }
  if (typeof self !== "undefined") {
    return self;
  }
  if (typeof window !== "undefined") {
    return window;
  }
  if (typeof global !== "undefined") {
    return global;
  }
  throw "Unable to locate global object";
})();

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function longToNumber(long: Long): number {
  if (long.gt(Number.MAX_SAFE_INTEGER)) {
    throw new tsProtoGlobalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
