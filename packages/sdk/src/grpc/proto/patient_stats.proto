syntax = "proto3";

service PatientStatsService {
  rpc heatmap(PatientStatsRpcInput) returns (PatientDiversityStatsForHeatmap) {}
  rpc stats(PatientStatsRpcInput) returns (PatientDiversityStats) {}
}

message PatientStatsRpcInput {
  repeated string indications = 1;
  repeated string icdCodes = 2;
  repeated string cities = 3;
  repeated string states = 4;
  string level = 5;
}

message Stat {
  string key = 1;
  uint64 value = 2;
  optional float percent = 3;
}

message LocationStat {
  string location_name = 1;
  repeated Stat race = 2;
}

message LocationStatMinified {
  string location_name = 1;
  repeated uint64 values = 2;
}

message PatientDiversityStatsForHeatmap {
  repeated LocationStatMinified diversityStats = 1;
}

message PatientDiversityStats {
  repeated Stat gender = 1;
  repeated Stat age = 2;
  repeated Stat race = 3;
  uint64 patientCount = 4;
}
