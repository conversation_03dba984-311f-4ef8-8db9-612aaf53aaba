/* eslint-disable */
import Long from "long";
import type { CallContext, CallOptions } from "nice-grpc-common";
import _m0 from "protobufjs/minimal";

export const protobufPackage = "";

export interface PatientStatsRpcInput {
  indications: string[];
  icdCodes: string[];
  cities: string[];
  states: string[];
  level: string;
}

export interface Stat {
  key: string;
  value: number;
  percent?: number | undefined;
}

export interface LocationStat {
  locationName: string;
  race: Stat[];
}

export interface LocationStatMinified {
  locationName: string;
  values: number[];
}

export interface PatientDiversityStatsForHeatmap {
  diversityStats: LocationStatMinified[];
}

export interface PatientDiversityStats {
  gender: Stat[];
  age: Stat[];
  race: Stat[];
  patientCount: number;
}

function createBasePatientStatsRpcInput(): PatientStatsRpcInput {
  return { indications: [], icdCodes: [], cities: [], states: [], level: "" };
}

export const PatientStatsRpcInput = {
  encode(message: PatientStatsRpcInput, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.indications) {
      writer.uint32(10).string(v!);
    }
    for (const v of message.icdCodes) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.cities) {
      writer.uint32(26).string(v!);
    }
    for (const v of message.states) {
      writer.uint32(34).string(v!);
    }
    if (message.level !== "") {
      writer.uint32(42).string(message.level);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PatientStatsRpcInput {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePatientStatsRpcInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.indications.push(reader.string());
          break;
        case 2:
          message.icdCodes.push(reader.string());
          break;
        case 3:
          message.cities.push(reader.string());
          break;
        case 4:
          message.states.push(reader.string());
          break;
        case 5:
          message.level = reader.string();
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): PatientStatsRpcInput {
    return {
      indications: Array.isArray(object?.indications) ? object.indications.map((e: any) => String(e)) : [],
      icdCodes: Array.isArray(object?.icdCodes) ? object.icdCodes.map((e: any) => String(e)) : [],
      cities: Array.isArray(object?.cities) ? object.cities.map((e: any) => String(e)) : [],
      states: Array.isArray(object?.states) ? object.states.map((e: any) => String(e)) : [],
      level: isSet(object.level) ? String(object.level) : "",
    };
  },

  toJSON(message: PatientStatsRpcInput): unknown {
    const obj: any = {};
    if (message.indications) {
      obj.indications = message.indications.map((e) => e);
    } else {
      obj.indications = [];
    }
    if (message.icdCodes) {
      obj.icdCodes = message.icdCodes.map((e) => e);
    } else {
      obj.icdCodes = [];
    }
    if (message.cities) {
      obj.cities = message.cities.map((e) => e);
    } else {
      obj.cities = [];
    }
    if (message.states) {
      obj.states = message.states.map((e) => e);
    } else {
      obj.states = [];
    }
    message.level !== undefined && (obj.level = message.level);
    return obj;
  },

  create(base?: DeepPartial<PatientStatsRpcInput>): PatientStatsRpcInput {
    return PatientStatsRpcInput.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<PatientStatsRpcInput>): PatientStatsRpcInput {
    const message = createBasePatientStatsRpcInput();
    message.indications = object.indications?.map((e) => e) || [];
    message.icdCodes = object.icdCodes?.map((e) => e) || [];
    message.cities = object.cities?.map((e) => e) || [];
    message.states = object.states?.map((e) => e) || [];
    message.level = object.level ?? "";
    return message;
  },
};

function createBaseStat(): Stat {
  return { key: "", value: 0, percent: undefined };
}

export const Stat = {
  encode(message: Stat, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(16).uint64(message.value);
    }
    if (message.percent !== undefined) {
      writer.uint32(29).float(message.percent);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Stat {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStat();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.key = reader.string();
          break;
        case 2:
          message.value = longToNumber(reader.uint64() as Long);
          break;
        case 3:
          message.percent = reader.float();
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): Stat {
    return {
      key: isSet(object.key) ? String(object.key) : "",
      value: isSet(object.value) ? Number(object.value) : 0,
      percent: isSet(object.percent) ? Number(object.percent) : undefined,
    };
  },

  toJSON(message: Stat): unknown {
    const obj: any = {};
    message.key !== undefined && (obj.key = message.key);
    message.value !== undefined && (obj.value = Math.round(message.value));
    message.percent !== undefined && (obj.percent = message.percent);
    return obj;
  },

  create(base?: DeepPartial<Stat>): Stat {
    return Stat.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<Stat>): Stat {
    const message = createBaseStat();
    message.key = object.key ?? "";
    message.value = object.value ?? 0;
    message.percent = object.percent ?? undefined;
    return message;
  },
};

function createBaseLocationStat(): LocationStat {
  return { locationName: "", race: [] };
}

export const LocationStat = {
  encode(message: LocationStat, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.locationName !== "") {
      writer.uint32(10).string(message.locationName);
    }
    for (const v of message.race) {
      Stat.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LocationStat {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLocationStat();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.locationName = reader.string();
          break;
        case 2:
          message.race.push(Stat.decode(reader, reader.uint32()));
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): LocationStat {
    return {
      locationName: isSet(object.locationName) ? String(object.locationName) : "",
      race: Array.isArray(object?.race) ? object.race.map((e: any) => Stat.fromJSON(e)) : [],
    };
  },

  toJSON(message: LocationStat): unknown {
    const obj: any = {};
    message.locationName !== undefined && (obj.locationName = message.locationName);
    if (message.race) {
      obj.race = message.race.map((e) => e ? Stat.toJSON(e) : undefined);
    } else {
      obj.race = [];
    }
    return obj;
  },

  create(base?: DeepPartial<LocationStat>): LocationStat {
    return LocationStat.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<LocationStat>): LocationStat {
    const message = createBaseLocationStat();
    message.locationName = object.locationName ?? "";
    message.race = object.race?.map((e) => Stat.fromPartial(e)) || [];
    return message;
  },
};

function createBaseLocationStatMinified(): LocationStatMinified {
  return { locationName: "", values: [] };
}

export const LocationStatMinified = {
  encode(message: LocationStatMinified, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.locationName !== "") {
      writer.uint32(10).string(message.locationName);
    }
    writer.uint32(18).fork();
    for (const v of message.values) {
      writer.uint64(v);
    }
    writer.ldelim();
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LocationStatMinified {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLocationStatMinified();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.locationName = reader.string();
          break;
        case 2:
          if ((tag & 7) === 2) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(longToNumber(reader.uint64() as Long));
            }
          } else {
            message.values.push(longToNumber(reader.uint64() as Long));
          }
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): LocationStatMinified {
    return {
      locationName: isSet(object.locationName) ? String(object.locationName) : "",
      values: Array.isArray(object?.values) ? object.values.map((e: any) => Number(e)) : [],
    };
  },

  toJSON(message: LocationStatMinified): unknown {
    const obj: any = {};
    message.locationName !== undefined && (obj.locationName = message.locationName);
    if (message.values) {
      obj.values = message.values.map((e) => Math.round(e));
    } else {
      obj.values = [];
    }
    return obj;
  },

  create(base?: DeepPartial<LocationStatMinified>): LocationStatMinified {
    return LocationStatMinified.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<LocationStatMinified>): LocationStatMinified {
    const message = createBaseLocationStatMinified();
    message.locationName = object.locationName ?? "";
    message.values = object.values?.map((e) => e) || [];
    return message;
  },
};

function createBasePatientDiversityStatsForHeatmap(): PatientDiversityStatsForHeatmap {
  return { diversityStats: [] };
}

export const PatientDiversityStatsForHeatmap = {
  encode(message: PatientDiversityStatsForHeatmap, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.diversityStats) {
      LocationStatMinified.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PatientDiversityStatsForHeatmap {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePatientDiversityStatsForHeatmap();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.diversityStats.push(LocationStatMinified.decode(reader, reader.uint32()));
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): PatientDiversityStatsForHeatmap {
    return {
      diversityStats: Array.isArray(object?.diversityStats)
        ? object.diversityStats.map((e: any) => LocationStatMinified.fromJSON(e))
        : [],
    };
  },

  toJSON(message: PatientDiversityStatsForHeatmap): unknown {
    const obj: any = {};
    if (message.diversityStats) {
      obj.diversityStats = message.diversityStats.map((e) => e ? LocationStatMinified.toJSON(e) : undefined);
    } else {
      obj.diversityStats = [];
    }
    return obj;
  },

  create(base?: DeepPartial<PatientDiversityStatsForHeatmap>): PatientDiversityStatsForHeatmap {
    return PatientDiversityStatsForHeatmap.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<PatientDiversityStatsForHeatmap>): PatientDiversityStatsForHeatmap {
    const message = createBasePatientDiversityStatsForHeatmap();
    message.diversityStats = object.diversityStats?.map((e) => LocationStatMinified.fromPartial(e)) || [];
    return message;
  },
};

function createBasePatientDiversityStats(): PatientDiversityStats {
  return { gender: [], age: [], race: [], patientCount: 0 };
}

export const PatientDiversityStats = {
  encode(message: PatientDiversityStats, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.gender) {
      Stat.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.age) {
      Stat.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.race) {
      Stat.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    if (message.patientCount !== 0) {
      writer.uint32(32).uint64(message.patientCount);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PatientDiversityStats {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePatientDiversityStats();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.gender.push(Stat.decode(reader, reader.uint32()));
          break;
        case 2:
          message.age.push(Stat.decode(reader, reader.uint32()));
          break;
        case 3:
          message.race.push(Stat.decode(reader, reader.uint32()));
          break;
        case 4:
          message.patientCount = longToNumber(reader.uint64() as Long);
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): PatientDiversityStats {
    return {
      gender: Array.isArray(object?.gender) ? object.gender.map((e: any) => Stat.fromJSON(e)) : [],
      age: Array.isArray(object?.age) ? object.age.map((e: any) => Stat.fromJSON(e)) : [],
      race: Array.isArray(object?.race) ? object.race.map((e: any) => Stat.fromJSON(e)) : [],
      patientCount: isSet(object.patientCount) ? Number(object.patientCount) : 0,
    };
  },

  toJSON(message: PatientDiversityStats): unknown {
    const obj: any = {};
    if (message.gender) {
      obj.gender = message.gender.map((e) => e ? Stat.toJSON(e) : undefined);
    } else {
      obj.gender = [];
    }
    if (message.age) {
      obj.age = message.age.map((e) => e ? Stat.toJSON(e) : undefined);
    } else {
      obj.age = [];
    }
    if (message.race) {
      obj.race = message.race.map((e) => e ? Stat.toJSON(e) : undefined);
    } else {
      obj.race = [];
    }
    message.patientCount !== undefined && (obj.patientCount = Math.round(message.patientCount));
    return obj;
  },

  create(base?: DeepPartial<PatientDiversityStats>): PatientDiversityStats {
    return PatientDiversityStats.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<PatientDiversityStats>): PatientDiversityStats {
    const message = createBasePatientDiversityStats();
    message.gender = object.gender?.map((e) => Stat.fromPartial(e)) || [];
    message.age = object.age?.map((e) => Stat.fromPartial(e)) || [];
    message.race = object.race?.map((e) => Stat.fromPartial(e)) || [];
    message.patientCount = object.patientCount ?? 0;
    return message;
  },
};

export type PatientStatsServiceDefinition = typeof PatientStatsServiceDefinition;
export const PatientStatsServiceDefinition = {
  name: "PatientStatsService",
  fullName: "PatientStatsService",
  methods: {
    heatmap: {
      name: "heatmap",
      requestType: PatientStatsRpcInput,
      requestStream: false,
      responseType: PatientDiversityStatsForHeatmap,
      responseStream: false,
      options: {},
    },
    stats: {
      name: "stats",
      requestType: PatientStatsRpcInput,
      requestStream: false,
      responseType: PatientDiversityStats,
      responseStream: false,
      options: {},
    },
  },
} as const;

export interface PatientStatsServiceImplementation<CallContextExt = {}> {
  heatmap(
    request: PatientStatsRpcInput,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<PatientDiversityStatsForHeatmap>>;
  stats(
    request: PatientStatsRpcInput,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<PatientDiversityStats>>;
}

export interface PatientStatsServiceClient<CallOptionsExt = {}> {
  heatmap(
    request: DeepPartial<PatientStatsRpcInput>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<PatientDiversityStatsForHeatmap>;
  stats(
    request: DeepPartial<PatientStatsRpcInput>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<PatientDiversityStats>;
}

declare var self: any | undefined;
declare var window: any | undefined;
declare var global: any | undefined;
var tsProtoGlobalThis: any = (() => {
  if (typeof globalThis !== "undefined") {
    return globalThis;
  }
  if (typeof self !== "undefined") {
    return self;
  }
  if (typeof window !== "undefined") {
    return window;
  }
  if (typeof global !== "undefined") {
    return global;
  }
  throw "Unable to locate global object";
})();

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function longToNumber(long: Long): number {
  if (long.gt(Number.MAX_SAFE_INTEGER)) {
    throw new tsProtoGlobalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
