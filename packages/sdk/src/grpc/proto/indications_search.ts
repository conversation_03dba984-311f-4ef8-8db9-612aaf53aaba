/* eslint-disable */
import Long from "long";
import type { CallContext, CallOptions } from "nice-grpc-common";
import _m0 from "protobufjs/minimal";

export const protobufPackage = "";

export interface IndicationSearchRootRpcInput {
  size?: number | undefined;
  indicationSource: string[];
  institutionId?: string | undefined;
}

export interface IndicationSearchByQueryRpcInput {
  query: string;
  size: number;
  rootId?: string | undefined;
  indicationSource: string[];
  indicationType: string[];
  sortBy?: string | undefined;
  userId?: string | undefined;
  projectId: string;
  institutionId?: string | undefined;
  h1Ids: string[];
  includeDuplicates?: boolean | undefined;
  language?: string;
}

export interface IndicationGetSubTreesRpcInput {
  rootId: string;
  indicationSource: string[];
  sortBy: string;
  institutionId?: string | undefined;
}

export interface IndicationSearchTreesByQueryRpcInput {
  query: string;
  indicationSource: string[];
  size?: number | undefined;
  userId?: string | undefined;
  projectId: string;
  sortBy: string;
  indicationType: string[];
  institutionId?: string | undefined;
  h1Ids: string[];
}

export interface Indication {
  id: string;
  h1Id: string;
  indicationName: string;
  indicationType: string;
  matchedSynonyms: string[];
  displayName?: string | undefined;
  description?: string | undefined;
  hcpCommunitySize?: number | undefined;
  claimCount?: number | undefined;
  patientCount?: number | undefined;
  match?: boolean | undefined;
  children: Indication[];
  icdCodes: Icd[];
  parentH1Ids: string[];
}

export interface Icd {
  icdCode: string;
  description: string;
  patientCount: number;
}

export interface Indications {
  indications: Indication[];
}

function createBaseIndicationSearchRootRpcInput(): IndicationSearchRootRpcInput {
  return { size: undefined, indicationSource: [], institutionId: undefined };
}

export const IndicationSearchRootRpcInput = {
  encode(message: IndicationSearchRootRpcInput, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.size !== undefined) {
      writer.uint32(8).int32(message.size);
    }
    for (const v of message.indicationSource) {
      writer.uint32(18).string(v!);
    }
    if (message.institutionId !== undefined) {
      writer.uint32(26).string(message.institutionId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IndicationSearchRootRpcInput {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIndicationSearchRootRpcInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.size = reader.int32();
          break;
        case 2:
          message.indicationSource.push(reader.string());
          break;
        case 3:
          message.institutionId = reader.string();
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): IndicationSearchRootRpcInput {
    return {
      size: isSet(object.size) ? Number(object.size) : undefined,
      indicationSource: Array.isArray(object?.indicationSource)
        ? object.indicationSource.map((e: any) => String(e))
        : [],
      institutionId: isSet(object.institutionId) ? String(object.institutionId) : undefined,
    };
  },

  toJSON(message: IndicationSearchRootRpcInput): unknown {
    const obj: any = {};
    message.size !== undefined && (obj.size = Math.round(message.size));
    if (message.indicationSource) {
      obj.indicationSource = message.indicationSource.map((e) => e);
    } else {
      obj.indicationSource = [];
    }
    message.institutionId !== undefined && (obj.institutionId = message.institutionId);
    return obj;
  },

  create(base?: DeepPartial<IndicationSearchRootRpcInput>): IndicationSearchRootRpcInput {
    return IndicationSearchRootRpcInput.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<IndicationSearchRootRpcInput>): IndicationSearchRootRpcInput {
    const message = createBaseIndicationSearchRootRpcInput();
    message.size = object.size ?? undefined;
    message.indicationSource = object.indicationSource?.map((e) => e) || [];
    message.institutionId = object.institutionId ?? undefined;
    return message;
  },
};

function createBaseIndicationSearchByQueryRpcInput(): IndicationSearchByQueryRpcInput {
  return {
    query: "",
    size: 0,
    rootId: undefined,
    indicationSource: [],
    indicationType: [],
    sortBy: undefined,
    userId: undefined,
    projectId: "",
    institutionId: undefined,
    h1Ids: [],
    includeDuplicates: undefined,
  };
}

export const IndicationSearchByQueryRpcInput = {
  encode(message: IndicationSearchByQueryRpcInput, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.query !== "") {
      writer.uint32(10).string(message.query);
    }
    if (message.size !== 0) {
      writer.uint32(16).int32(message.size);
    }
    if (message.rootId !== undefined) {
      writer.uint32(26).string(message.rootId);
    }
    for (const v of message.indicationSource) {
      writer.uint32(34).string(v!);
    }
    for (const v of message.indicationType) {
      writer.uint32(42).string(v!);
    }
    if (message.sortBy !== undefined) {
      writer.uint32(50).string(message.sortBy);
    }
    if (message.userId !== undefined) {
      writer.uint32(58).string(message.userId);
    }
    if (message.projectId !== "") {
      writer.uint32(66).string(message.projectId);
    }
    if (message.institutionId !== undefined) {
      writer.uint32(74).string(message.institutionId);
    }
    for (const v of message.h1Ids) {
      writer.uint32(82).string(v!);
    }
    if (message.includeDuplicates !== undefined) {
      writer.uint32(88).bool(message.includeDuplicates);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IndicationSearchByQueryRpcInput {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIndicationSearchByQueryRpcInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.query = reader.string();
          break;
        case 2:
          message.size = reader.int32();
          break;
        case 3:
          message.rootId = reader.string();
          break;
        case 4:
          message.indicationSource.push(reader.string());
          break;
        case 5:
          message.indicationType.push(reader.string());
          break;
        case 6:
          message.sortBy = reader.string();
          break;
        case 7:
          message.userId = reader.string();
          break;
        case 8:
          message.projectId = reader.string();
          break;
        case 9:
          message.institutionId = reader.string();
          break;
        case 10:
          message.h1Ids.push(reader.string());
          break;
        case 11:
          message.includeDuplicates = reader.bool();
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): IndicationSearchByQueryRpcInput {
    return {
      query: isSet(object.query) ? String(object.query) : "",
      size: isSet(object.size) ? Number(object.size) : 0,
      rootId: isSet(object.rootId) ? String(object.rootId) : undefined,
      indicationSource: Array.isArray(object?.indicationSource)
        ? object.indicationSource.map((e: any) => String(e))
        : [],
      indicationType: Array.isArray(object?.indicationType) ? object.indicationType.map((e: any) => String(e)) : [],
      sortBy: isSet(object.sortBy) ? String(object.sortBy) : undefined,
      userId: isSet(object.userId) ? String(object.userId) : undefined,
      projectId: isSet(object.projectId) ? String(object.projectId) : "",
      institutionId: isSet(object.institutionId) ? String(object.institutionId) : undefined,
      h1Ids: Array.isArray(object?.h1Ids) ? object.h1Ids.map((e: any) => String(e)) : [],
      includeDuplicates: isSet(object.includeDuplicates) ? Boolean(object.includeDuplicates) : undefined,
    };
  },

  toJSON(message: IndicationSearchByQueryRpcInput): unknown {
    const obj: any = {};
    message.query !== undefined && (obj.query = message.query);
    message.size !== undefined && (obj.size = Math.round(message.size));
    message.rootId !== undefined && (obj.rootId = message.rootId);
    if (message.indicationSource) {
      obj.indicationSource = message.indicationSource.map((e) => e);
    } else {
      obj.indicationSource = [];
    }
    if (message.indicationType) {
      obj.indicationType = message.indicationType.map((e) => e);
    } else {
      obj.indicationType = [];
    }
    message.sortBy !== undefined && (obj.sortBy = message.sortBy);
    message.userId !== undefined && (obj.userId = message.userId);
    message.projectId !== undefined && (obj.projectId = message.projectId);
    message.institutionId !== undefined && (obj.institutionId = message.institutionId);
    if (message.h1Ids) {
      obj.h1Ids = message.h1Ids.map((e) => e);
    } else {
      obj.h1Ids = [];
    }
    message.includeDuplicates !== undefined && (obj.includeDuplicates = message.includeDuplicates);
    return obj;
  },

  create(base?: DeepPartial<IndicationSearchByQueryRpcInput>): IndicationSearchByQueryRpcInput {
    return IndicationSearchByQueryRpcInput.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<IndicationSearchByQueryRpcInput>): IndicationSearchByQueryRpcInput {
    const message = createBaseIndicationSearchByQueryRpcInput();
    message.query = object.query ?? "";
    message.size = object.size ?? 0;
    message.rootId = object.rootId ?? undefined;
    message.indicationSource = object.indicationSource?.map((e) => e) || [];
    message.indicationType = object.indicationType?.map((e) => e) || [];
    message.sortBy = object.sortBy ?? undefined;
    message.userId = object.userId ?? undefined;
    message.projectId = object.projectId ?? "";
    message.institutionId = object.institutionId ?? undefined;
    message.h1Ids = object.h1Ids?.map((e) => e) || [];
    message.includeDuplicates = object.includeDuplicates ?? undefined;
    return message;
  },
};

function createBaseIndicationGetSubTreesRpcInput(): IndicationGetSubTreesRpcInput {
  return { rootId: "", indicationSource: [], sortBy: "", institutionId: undefined };
}

export const IndicationGetSubTreesRpcInput = {
  encode(message: IndicationGetSubTreesRpcInput, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.rootId !== "") {
      writer.uint32(10).string(message.rootId);
    }
    for (const v of message.indicationSource) {
      writer.uint32(18).string(v!);
    }
    if (message.sortBy !== "") {
      writer.uint32(26).string(message.sortBy);
    }
    if (message.institutionId !== undefined) {
      writer.uint32(34).string(message.institutionId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IndicationGetSubTreesRpcInput {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIndicationGetSubTreesRpcInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.rootId = reader.string();
          break;
        case 2:
          message.indicationSource.push(reader.string());
          break;
        case 3:
          message.sortBy = reader.string();
          break;
        case 4:
          message.institutionId = reader.string();
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): IndicationGetSubTreesRpcInput {
    return {
      rootId: isSet(object.rootId) ? String(object.rootId) : "",
      indicationSource: Array.isArray(object?.indicationSource)
        ? object.indicationSource.map((e: any) => String(e))
        : [],
      sortBy: isSet(object.sortBy) ? String(object.sortBy) : "",
      institutionId: isSet(object.institutionId) ? String(object.institutionId) : undefined,
    };
  },

  toJSON(message: IndicationGetSubTreesRpcInput): unknown {
    const obj: any = {};
    message.rootId !== undefined && (obj.rootId = message.rootId);
    if (message.indicationSource) {
      obj.indicationSource = message.indicationSource.map((e) => e);
    } else {
      obj.indicationSource = [];
    }
    message.sortBy !== undefined && (obj.sortBy = message.sortBy);
    message.institutionId !== undefined && (obj.institutionId = message.institutionId);
    return obj;
  },

  create(base?: DeepPartial<IndicationGetSubTreesRpcInput>): IndicationGetSubTreesRpcInput {
    return IndicationGetSubTreesRpcInput.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<IndicationGetSubTreesRpcInput>): IndicationGetSubTreesRpcInput {
    const message = createBaseIndicationGetSubTreesRpcInput();
    message.rootId = object.rootId ?? "";
    message.indicationSource = object.indicationSource?.map((e) => e) || [];
    message.sortBy = object.sortBy ?? "";
    message.institutionId = object.institutionId ?? undefined;
    return message;
  },
};

function createBaseIndicationSearchTreesByQueryRpcInput(): IndicationSearchTreesByQueryRpcInput {
  return {
    query: "",
    indicationSource: [],
    size: undefined,
    userId: undefined,
    projectId: "",
    sortBy: "",
    indicationType: [],
    institutionId: undefined,
    h1Ids: [],
  };
}

export const IndicationSearchTreesByQueryRpcInput = {
  encode(message: IndicationSearchTreesByQueryRpcInput, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.query !== "") {
      writer.uint32(10).string(message.query);
    }
    for (const v of message.indicationSource) {
      writer.uint32(18).string(v!);
    }
    if (message.size !== undefined) {
      writer.uint32(24).uint32(message.size);
    }
    if (message.userId !== undefined) {
      writer.uint32(34).string(message.userId);
    }
    if (message.projectId !== "") {
      writer.uint32(42).string(message.projectId);
    }
    if (message.sortBy !== "") {
      writer.uint32(50).string(message.sortBy);
    }
    for (const v of message.indicationType) {
      writer.uint32(58).string(v!);
    }
    if (message.institutionId !== undefined) {
      writer.uint32(66).string(message.institutionId);
    }
    for (const v of message.h1Ids) {
      writer.uint32(74).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): IndicationSearchTreesByQueryRpcInput {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIndicationSearchTreesByQueryRpcInput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.query = reader.string();
          break;
        case 2:
          message.indicationSource.push(reader.string());
          break;
        case 3:
          message.size = reader.uint32();
          break;
        case 4:
          message.userId = reader.string();
          break;
        case 5:
          message.projectId = reader.string();
          break;
        case 6:
          message.sortBy = reader.string();
          break;
        case 7:
          message.indicationType.push(reader.string());
          break;
        case 8:
          message.institutionId = reader.string();
          break;
        case 9:
          message.h1Ids.push(reader.string());
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): IndicationSearchTreesByQueryRpcInput {
    return {
      query: isSet(object.query) ? String(object.query) : "",
      indicationSource: Array.isArray(object?.indicationSource)
        ? object.indicationSource.map((e: any) => String(e))
        : [],
      size: isSet(object.size) ? Number(object.size) : undefined,
      userId: isSet(object.userId) ? String(object.userId) : undefined,
      projectId: isSet(object.projectId) ? String(object.projectId) : "",
      sortBy: isSet(object.sortBy) ? String(object.sortBy) : "",
      indicationType: Array.isArray(object?.indicationType) ? object.indicationType.map((e: any) => String(e)) : [],
      institutionId: isSet(object.institutionId) ? String(object.institutionId) : undefined,
      h1Ids: Array.isArray(object?.h1Ids) ? object.h1Ids.map((e: any) => String(e)) : [],
    };
  },

  toJSON(message: IndicationSearchTreesByQueryRpcInput): unknown {
    const obj: any = {};
    message.query !== undefined && (obj.query = message.query);
    if (message.indicationSource) {
      obj.indicationSource = message.indicationSource.map((e) => e);
    } else {
      obj.indicationSource = [];
    }
    message.size !== undefined && (obj.size = Math.round(message.size));
    message.userId !== undefined && (obj.userId = message.userId);
    message.projectId !== undefined && (obj.projectId = message.projectId);
    message.sortBy !== undefined && (obj.sortBy = message.sortBy);
    if (message.indicationType) {
      obj.indicationType = message.indicationType.map((e) => e);
    } else {
      obj.indicationType = [];
    }
    message.institutionId !== undefined && (obj.institutionId = message.institutionId);
    if (message.h1Ids) {
      obj.h1Ids = message.h1Ids.map((e) => e);
    } else {
      obj.h1Ids = [];
    }
    return obj;
  },

  create(base?: DeepPartial<IndicationSearchTreesByQueryRpcInput>): IndicationSearchTreesByQueryRpcInput {
    return IndicationSearchTreesByQueryRpcInput.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<IndicationSearchTreesByQueryRpcInput>): IndicationSearchTreesByQueryRpcInput {
    const message = createBaseIndicationSearchTreesByQueryRpcInput();
    message.query = object.query ?? "";
    message.indicationSource = object.indicationSource?.map((e) => e) || [];
    message.size = object.size ?? undefined;
    message.userId = object.userId ?? undefined;
    message.projectId = object.projectId ?? "";
    message.sortBy = object.sortBy ?? "";
    message.indicationType = object.indicationType?.map((e) => e) || [];
    message.institutionId = object.institutionId ?? undefined;
    message.h1Ids = object.h1Ids?.map((e) => e) || [];
    return message;
  },
};

function createBaseIndication(): Indication {
  return {
    id: "",
    h1Id: "",
    indicationName: "",
    indicationType: "",
    matchedSynonyms: [],
    displayName: undefined,
    description: undefined,
    hcpCommunitySize: undefined,
    claimCount: undefined,
    patientCount: undefined,
    match: undefined,
    children: [],
    icdCodes: [],
    parentH1Ids: [],
  };
}

export const Indication = {
  encode(message: Indication, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.h1Id !== "") {
      writer.uint32(18).string(message.h1Id);
    }
    if (message.indicationName !== "") {
      writer.uint32(26).string(message.indicationName);
    }
    if (message.indicationType !== "") {
      writer.uint32(34).string(message.indicationType);
    }
    for (const v of message.matchedSynonyms) {
      writer.uint32(42).string(v!);
    }
    if (message.displayName !== undefined) {
      writer.uint32(50).string(message.displayName);
    }
    if (message.description !== undefined) {
      writer.uint32(58).string(message.description);
    }
    if (message.hcpCommunitySize !== undefined) {
      writer.uint32(64).int64(message.hcpCommunitySize);
    }
    if (message.claimCount !== undefined) {
      writer.uint32(72).int64(message.claimCount);
    }
    if (message.patientCount !== undefined) {
      writer.uint32(80).int64(message.patientCount);
    }
    if (message.match !== undefined) {
      writer.uint32(88).bool(message.match);
    }
    for (const v of message.children) {
      Indication.encode(v!, writer.uint32(98).fork()).ldelim();
    }
    for (const v of message.icdCodes) {
      Icd.encode(v!, writer.uint32(106).fork()).ldelim();
    }
    for (const v of message.parentH1Ids) {
      writer.uint32(114).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Indication {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIndication();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.id = reader.string();
          break;
        case 2:
          message.h1Id = reader.string();
          break;
        case 3:
          message.indicationName = reader.string();
          break;
        case 4:
          message.indicationType = reader.string();
          break;
        case 5:
          message.matchedSynonyms.push(reader.string());
          break;
        case 6:
          message.displayName = reader.string();
          break;
        case 7:
          message.description = reader.string();
          break;
        case 8:
          message.hcpCommunitySize = longToNumber(reader.int64() as Long);
          break;
        case 9:
          message.claimCount = longToNumber(reader.int64() as Long);
          break;
        case 10:
          message.patientCount = longToNumber(reader.int64() as Long);
          break;
        case 11:
          message.match = reader.bool();
          break;
        case 12:
          message.children.push(Indication.decode(reader, reader.uint32()));
          break;
        case 13:
          message.icdCodes.push(Icd.decode(reader, reader.uint32()));
          break;
        case 14:
          message.parentH1Ids.push(reader.string());
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): Indication {
    return {
      id: isSet(object.id) ? String(object.id) : "",
      h1Id: isSet(object.h1Id) ? String(object.h1Id) : "",
      indicationName: isSet(object.indicationName) ? String(object.indicationName) : "",
      indicationType: isSet(object.indicationType) ? String(object.indicationType) : "",
      matchedSynonyms: Array.isArray(object?.matchedSynonyms) ? object.matchedSynonyms.map((e: any) => String(e)) : [],
      displayName: isSet(object.displayName) ? String(object.displayName) : undefined,
      description: isSet(object.description) ? String(object.description) : undefined,
      hcpCommunitySize: isSet(object.hcpCommunitySize) ? Number(object.hcpCommunitySize) : undefined,
      claimCount: isSet(object.claimCount) ? Number(object.claimCount) : undefined,
      patientCount: isSet(object.patientCount) ? Number(object.patientCount) : undefined,
      match: isSet(object.match) ? Boolean(object.match) : undefined,
      children: Array.isArray(object?.children) ? object.children.map((e: any) => Indication.fromJSON(e)) : [],
      icdCodes: Array.isArray(object?.icdCodes) ? object.icdCodes.map((e: any) => Icd.fromJSON(e)) : [],
      parentH1Ids: Array.isArray(object?.parentH1Ids) ? object.parentH1Ids.map((e: any) => String(e)) : [],
    };
  },

  toJSON(message: Indication): unknown {
    const obj: any = {};
    message.id !== undefined && (obj.id = message.id);
    message.h1Id !== undefined && (obj.h1Id = message.h1Id);
    message.indicationName !== undefined && (obj.indicationName = message.indicationName);
    message.indicationType !== undefined && (obj.indicationType = message.indicationType);
    if (message.matchedSynonyms) {
      obj.matchedSynonyms = message.matchedSynonyms.map((e) => e);
    } else {
      obj.matchedSynonyms = [];
    }
    message.displayName !== undefined && (obj.displayName = message.displayName);
    message.description !== undefined && (obj.description = message.description);
    message.hcpCommunitySize !== undefined && (obj.hcpCommunitySize = Math.round(message.hcpCommunitySize));
    message.claimCount !== undefined && (obj.claimCount = Math.round(message.claimCount));
    message.patientCount !== undefined && (obj.patientCount = Math.round(message.patientCount));
    message.match !== undefined && (obj.match = message.match);
    if (message.children) {
      obj.children = message.children.map((e) => e ? Indication.toJSON(e) : undefined);
    } else {
      obj.children = [];
    }
    if (message.icdCodes) {
      obj.icdCodes = message.icdCodes.map((e) => e ? Icd.toJSON(e) : undefined);
    } else {
      obj.icdCodes = [];
    }
    if (message.parentH1Ids) {
      obj.parentH1Ids = message.parentH1Ids.map((e) => e);
    } else {
      obj.parentH1Ids = [];
    }
    return obj;
  },

  create(base?: DeepPartial<Indication>): Indication {
    return Indication.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<Indication>): Indication {
    const message = createBaseIndication();
    message.id = object.id ?? "";
    message.h1Id = object.h1Id ?? "";
    message.indicationName = object.indicationName ?? "";
    message.indicationType = object.indicationType ?? "";
    message.matchedSynonyms = object.matchedSynonyms?.map((e) => e) || [];
    message.displayName = object.displayName ?? undefined;
    message.description = object.description ?? undefined;
    message.hcpCommunitySize = object.hcpCommunitySize ?? undefined;
    message.claimCount = object.claimCount ?? undefined;
    message.patientCount = object.patientCount ?? undefined;
    message.match = object.match ?? undefined;
    message.children = object.children?.map((e) => Indication.fromPartial(e)) || [];
    message.icdCodes = object.icdCodes?.map((e) => Icd.fromPartial(e)) || [];
    message.parentH1Ids = object.parentH1Ids?.map((e) => e) || [];
    return message;
  },
};

function createBaseIcd(): Icd {
  return { icdCode: "", description: "", patientCount: 0 };
}

export const Icd = {
  encode(message: Icd, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.icdCode !== "") {
      writer.uint32(10).string(message.icdCode);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.patientCount !== 0) {
      writer.uint32(24).uint64(message.patientCount);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Icd {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIcd();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.icdCode = reader.string();
          break;
        case 2:
          message.description = reader.string();
          break;
        case 3:
          message.patientCount = longToNumber(reader.uint64() as Long);
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): Icd {
    return {
      icdCode: isSet(object.icdCode) ? String(object.icdCode) : "",
      description: isSet(object.description) ? String(object.description) : "",
      patientCount: isSet(object.patientCount) ? Number(object.patientCount) : 0,
    };
  },

  toJSON(message: Icd): unknown {
    const obj: any = {};
    message.icdCode !== undefined && (obj.icdCode = message.icdCode);
    message.description !== undefined && (obj.description = message.description);
    message.patientCount !== undefined && (obj.patientCount = Math.round(message.patientCount));
    return obj;
  },

  create(base?: DeepPartial<Icd>): Icd {
    return Icd.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<Icd>): Icd {
    const message = createBaseIcd();
    message.icdCode = object.icdCode ?? "";
    message.description = object.description ?? "";
    message.patientCount = object.patientCount ?? 0;
    return message;
  },
};

function createBaseIndications(): Indications {
  return { indications: [] };
}

export const Indications = {
  encode(message: Indications, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.indications) {
      Indication.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Indications {
    const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIndications();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          message.indications.push(Indication.decode(reader, reader.uint32()));
          break;
        default:
          reader.skipType(tag & 7);
          break;
      }
    }
    return message;
  },

  fromJSON(object: any): Indications {
    return {
      indications: Array.isArray(object?.indications) ? object.indications.map((e: any) => Indication.fromJSON(e)) : [],
    };
  },

  toJSON(message: Indications): unknown {
    const obj: any = {};
    if (message.indications) {
      obj.indications = message.indications.map((e) => e ? Indication.toJSON(e) : undefined);
    } else {
      obj.indications = [];
    }
    return obj;
  },

  create(base?: DeepPartial<Indications>): Indications {
    return Indications.fromPartial(base ?? {});
  },

  fromPartial(object: DeepPartial<Indications>): Indications {
    const message = createBaseIndications();
    message.indications = object.indications?.map((e) => Indication.fromPartial(e)) || [];
    return message;
  },
};

export type IndicationsSearchServiceDefinition = typeof IndicationsSearchServiceDefinition;
export const IndicationsSearchServiceDefinition = {
  name: "IndicationsSearchService",
  fullName: "IndicationsSearchService",
  methods: {
    searchRootIndications: {
      name: "searchRootIndications",
      requestType: IndicationSearchRootRpcInput,
      requestStream: false,
      responseType: Indications,
      responseStream: false,
      options: {},
    },
    searchByQuery: {
      name: "searchByQuery",
      requestType: IndicationSearchByQueryRpcInput,
      requestStream: false,
      responseType: Indications,
      responseStream: false,
      options: {},
    },
    getSubTrees: {
      name: "getSubTrees",
      requestType: IndicationGetSubTreesRpcInput,
      requestStream: false,
      responseType: Indications,
      responseStream: false,
      options: {},
    },
    searchIndicationTreesByQuery: {
      name: "searchIndicationTreesByQuery",
      requestType: IndicationSearchTreesByQueryRpcInput,
      requestStream: false,
      responseType: Indications,
      responseStream: false,
      options: {},
    },
  },
} as const;

export interface IndicationsSearchServiceImplementation<CallContextExt = {}> {
  searchRootIndications(
    request: IndicationSearchRootRpcInput,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<Indications>>;
  searchByQuery(
    request: IndicationSearchByQueryRpcInput,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<Indications>>;
  getSubTrees(
    request: IndicationGetSubTreesRpcInput,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<Indications>>;
  searchIndicationTreesByQuery(
    request: IndicationSearchTreesByQueryRpcInput,
    context: CallContext & CallContextExt,
  ): Promise<DeepPartial<Indications>>;
}

export interface IndicationsSearchServiceClient<CallOptionsExt = {}> {
  searchRootIndications(
    request: DeepPartial<IndicationSearchRootRpcInput>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<Indications>;
  searchByQuery(
    request: DeepPartial<IndicationSearchByQueryRpcInput>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<Indications>;
  getSubTrees(
    request: DeepPartial<IndicationGetSubTreesRpcInput>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<Indications>;
  searchIndicationTreesByQuery(
    request: DeepPartial<IndicationSearchTreesByQueryRpcInput>,
    options?: CallOptions & CallOptionsExt,
  ): Promise<Indications>;
}

declare var self: any | undefined;
declare var window: any | undefined;
declare var global: any | undefined;
var tsProtoGlobalThis: any = (() => {
  if (typeof globalThis !== "undefined") {
    return globalThis;
  }
  if (typeof self !== "undefined") {
    return self;
  }
  if (typeof window !== "undefined") {
    return window;
  }
  if (typeof global !== "undefined") {
    return global;
  }
  throw "Unable to locate global object";
})();

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function longToNumber(long: Long): number {
  if (long.gt(Number.MAX_SAFE_INTEGER)) {
    throw new tsProtoGlobalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
