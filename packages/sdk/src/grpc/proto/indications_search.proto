syntax = "proto3";

service IndicationsSearchService {
  rpc searchRootIndications(IndicationSearchRootRpcInput) returns (Indications) {}
  rpc searchByQuery(IndicationSearchByQueryRpcInput) returns (Indications) {}
  rpc getSubTrees(IndicationGetSubTreesRpcInput) returns (Indications) {}
  rpc searchIndicationTreesByQuery(IndicationSearchTreesByQueryRpcInput) returns (Indications) {}
}

message IndicationSearchRootRpcInput {
  optional int32 size = 1;
  repeated string indicationSource = 2;
  optional string institutionId = 3;
}

message IndicationSearchByQueryRpcInput {
  string query = 1;
  int32 size = 2;
  optional string rootId = 3;
  repeated string indicationSource = 4;
  repeated string indicationType = 5;
  optional string sortBy = 6;
  optional string userId = 7;
  string projectId = 8;
  optional string institutionId = 9;
  repeated string h1Ids = 10;
  optional bool includeDuplicates = 11;
}

message IndicationGetSubTreesRpcInput {
  string rootId = 1;
  repeated string indicationSource = 2;
  string sortBy = 3;
  optional string institutionId = 4;
}
message IndicationSearchTreesByQueryRpcInput {
  string query = 1;
  repeated string indicationSource = 2;
  optional uint32 size = 3;
  optional string userId = 4;
  string projectId = 5;
  string sortBy = 6;
  repeated string indicationType = 7;
  optional string institutionId = 8;
  repeated string h1Ids = 9;
}

message Indication {
  string id = 1;
  string h1Id = 2;
  string indicationName = 3;
  string indicationType = 4;
  repeated string matchedSynonyms = 5;
  optional string displayName = 6;
  optional string description = 7;
  optional int64 hcpCommunitySize = 8;
  optional int64 claimCount = 9;
  optional int64 patientCount = 10;
  optional bool match = 11;
  repeated  Indication children = 12;
  repeated Icd icdCodes = 13;
  repeated string parentH1Ids = 14;
}

message Icd {
  string icdCode = 1;
  string description = 2;
  uint64 patientCount = 3;
}

message Indications {
  repeated Indication indications = 1;
}