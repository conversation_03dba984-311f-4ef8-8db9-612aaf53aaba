import { createChannel, createClient } from "nice-grpc";
import { CompatServiceDefinition } from "nice-grpc/lib/service-definitions";

export abstract class AbstractGrpcClient<GrpcClientType> {
  client: GrpcClientType;

  constructor(
    host: string,
    port: string,
    serviceDefinition: CompatServiceDefinition
  ) {
    const channel = createChannel(`${host}:${port}`);

    this.client = createClient(serviceDefinition, channel) as GrpcClientType;
  }
}
