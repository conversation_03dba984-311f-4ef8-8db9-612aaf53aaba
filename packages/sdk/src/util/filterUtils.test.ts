import { createEmptyFilters } from "./filterUtils";
import { DefaultScoreWeights } from "../interfaces/filterInterfaces";

describe("createEmptyFilters", () => {
  it("should return empty filters", () => {
    const emptyFilters = createEmptyFilters("1");
    expect(emptyFilters).toEqual(
      expect.objectContaining({
        userId: "",
        projectId: "1",
        query: [],
        sortBy: DefaultScoreWeights,
        pageSize: 10000,
        from: 0,
        filters: {
          dateRangePicker: { min: 0, max: null, active: false },
          designations: {
            values: []
          },
          npi: {
            values: []
          },
          institutionType: {
            values: []
          },
          specialty: {
            values: []
          },
          country: {
            values: []
          },
          state: {
            values: []
          },
          city: {
            values: []
          },
          zipCode: {
            values: []
          },
          institution: {
            values: []
          },
          presentWorkInstitutions: {
            values: []
          },
          pastAndPresentWorkInstitutions: {
            values: []
          },
          studentInstitutions: {
            values: []
          },
          graduationYearRange: { min: null, max: null },
          publications: {
            minCount: { value: null },
            socialMediaMinCount: {
              value: null
            },
            journal: {
              values: []
            },
            type: {
              values: []
            }
          },
          tags: {
            name: {
              values: []
            },
            publicTagIds: [],
            privateTagIds: [],
            programmaticTagIds: []
          },
          claims: {
            diagnosesICDMinCount: {
              value: null
            },
            diagnosesICD: {
              values: []
            },
            genericNames: {
              values: []
            },
            drugClasses: {
              values: []
            },
            brandNames: {
              values: []
            },
            brandNameOrGeneric: { values: [] },
            prescriptionsMinCount: {
              value: null
            },
            proceduresCPTMinCount: {
              value: null
            },
            proceduresCPT: {
              values: []
            },
            proceduresHCPCMinCount: {
              value: null
            },
            proceduresHCPC: {
              values: []
            },
            timeFrame: {
              value: null
            }
          },
          trials: {
            minCount: { value: null },
            maxCount: { value: null },
            status: {
              values: []
            },
            phase: {
              values: []
            },
            studyType: {
              values: []
            },
            funderType: {
              values: []
            },
            sponsor: {
              values: []
            },
            sponsorType: {
              values: []
            },
            id: {
              values: []
            },
            timeFrame: {
              min: 0,
              max: null,
              key: "max"
            },
            biomarkers: {
              values: []
            }
          },
          congresses: {
            minCount: { value: null },
            name: {
              values: []
            },
            type: {
              values: []
            },
            organizerName: {
              values: []
            },
            sessionType: {
              values: []
            }
          },
          engagements: {
            minCount: { value: null }
          },
          grants: {
            minAmount: { value: null },
            funder: {
              values: []
            }
          },
          payments: {
            minAmount: { value: null },
            company: {
              values: []
            },
            drugOrDevice: {
              values: []
            },
            fundingType: {
              values: []
            },
            category: {
              values: []
            }
          },
          referrals: {
            serviceLine: {
              values: []
            },
            minReferralsReceived: {
              value: null
            },
            minReferralsSent: {
              value: null
            }
          },
          patientsDiversity: {
            ageRange: {
              values: []
            },
            sex: {
              values: []
            },
            race: {
              values: []
            },
            raceMix: {
              thresholdValue: []
            }
          },
          providerDiversity: {
            languagesSpoken: {
              values: []
            },
            sex: {
              values: []
            },
            race: {
              values: []
            }
          },
          hasLinkedin: {
            value: null
          },
          hasTwitter: {
            value: null
          },
          hasCTMSData: {
            value: null
          }
        },
        activeFilters: {
          people: false,
          trials: false,
          pubs: false,
          payments: false,
          congress: false,
          tags: false,
          patientsDiversity: false,
          providerDiversity: false,
          engagements: false,
          claims: false,
          referrals: false
        }
      })
    );
  });

  it("should return empty filters with different project conf", () => {
    const emptyFilters = createEmptyFilters("3");
    expect(emptyFilters).toEqual(
      expect.objectContaining({
        userId: "",
        projectId: "3",
        query: [],
        sortBy: DefaultScoreWeights,
        pageSize: 10000,
        from: 0,
        filters: {
          dateRangePicker: { min: 0, max: null, active: false },
          designations: {
            values: []
          },
          npi: {
            values: []
          },
          institutionType: {
            values: []
          },
          specialty: {
            values: []
          },
          country: {
            values: []
          },
          state: {
            values: []
          },
          city: {
            values: []
          },
          zipCode: {
            values: []
          },
          institution: {
            values: []
          },
          presentWorkInstitutions: {
            values: []
          },
          pastAndPresentWorkInstitutions: {
            values: []
          },
          studentInstitutions: {
            values: []
          },
          graduationYearRange: { min: null, max: null },
          publications: {
            minCount: { value: null },
            socialMediaMinCount: {
              value: null
            },
            journal: {
              values: []
            },
            type: {
              values: []
            }
          },
          tags: {
            name: {
              values: []
            },
            publicTagIds: [],
            privateTagIds: [],
            programmaticTagIds: []
          },
          claims: {
            diagnosesICDMinCount: {
              value: null
            },
            diagnosesICD: {
              values: []
            },
            genericNames: {
              values: []
            },
            drugClasses: {
              values: []
            },
            brandNames: {
              values: []
            },
            brandNameOrGeneric: { values: [] },
            prescriptionsMinCount: {
              value: null
            },
            proceduresCPTMinCount: {
              value: null
            },
            proceduresCPT: {
              values: []
            },
            proceduresHCPCMinCount: {
              value: null
            },
            proceduresHCPC: {
              values: []
            },
            timeFrame: {
              value: null
            }
          },
          trials: {
            minCount: { value: null },
            maxCount: { value: null },
            status: {
              values: []
            },
            phase: {
              values: []
            },
            studyType: {
              values: []
            },
            funderType: {
              values: []
            },
            sponsor: {
              values: []
            },
            sponsorType: {
              values: []
            },
            id: {
              values: []
            },
            timeFrame: {
              min: 0,
              max: null,
              key: "max"
            },
            biomarkers: {
              values: []
            }
          },
          congresses: {
            minCount: { value: null },
            name: {
              values: []
            },
            type: {
              values: []
            },
            organizerName: {
              values: []
            },
            sessionType: {
              values: []
            }
          },
          engagements: {
            minCount: { value: null }
          },
          grants: {
            minAmount: { value: null },
            funder: {
              values: []
            }
          },
          payments: {
            minAmount: { value: null },
            company: {
              values: []
            },
            drugOrDevice: {
              values: []
            },
            fundingType: {
              values: []
            },
            category: {
              values: []
            }
          },
          referrals: {
            serviceLine: {
              values: []
            },
            minReferralsReceived: {
              value: null
            },
            minReferralsSent: {
              value: null
            }
          },
          patientsDiversity: {
            ageRange: {
              values: []
            },
            sex: {
              values: []
            },
            race: {
              values: []
            },
            raceMix: {
              thresholdValue: []
            }
          },
          providerDiversity: {
            languagesSpoken: {
              values: []
            },
            sex: {
              values: []
            },
            race: {
              values: []
            }
          },
          hasLinkedin: {
            value: null
          },
          hasTwitter: {
            value: null
          },
          hasCTMSData: {
            value: null
          }
        },
        activeFilters: {
          people: false,
          trials: false,
          pubs: false,
          payments: false,
          congress: false,
          tags: false,
          patientsDiversity: false,
          providerDiversity: false,
          engagements: false,
          claims: false,
          referrals: false
        }
      })
    );
  });

  it("should return empty filters with Claims v2", () => {
    const emptyFilters = createEmptyFilters("1");
    expect(emptyFilters).toEqual(
      expect.objectContaining({
        userId: "",
        projectId: "1",
        query: [],
        sortBy: {
          publication: 0.3,
          citation: 0.1,
          trial: 0.3,
          payment: 0.1,
          congress: 0.2,
          grant: 0.1,
          microBloggingCount: 0.2,
          diagnoses: 0.1,
          referralsReceived: 0.1,
          referralsSent: 0.1,
          procedures: 0.2,
          prescriptions: 0.2,
          patientsDiversityRank: 0.1,
          h1DefaultRank: 0.1,
          twitterFollowersCount: 0,
          twitterTweetCount: 0,
          digitalRelevance: 0,
          age: 0
        },
        pageSize: 10000,
        from: 0,
        filters: {
          dateRangePicker: { min: 0, max: null, active: false },
          designations: {
            values: []
          },
          npi: {
            values: []
          },
          institutionType: {
            values: []
          },
          specialty: {
            values: []
          },
          country: {
            values: []
          },
          state: {
            values: []
          },
          city: {
            values: []
          },
          zipCode: {
            values: []
          },
          institution: {
            values: []
          },
          presentWorkInstitutions: {
            values: []
          },
          pastAndPresentWorkInstitutions: {
            values: []
          },
          studentInstitutions: {
            values: []
          },
          graduationYearRange: { min: null, max: null },
          publications: {
            minCount: { value: null },
            socialMediaMinCount: {
              value: null
            },
            journal: {
              values: []
            },
            type: {
              values: []
            }
          },
          tags: {
            name: {
              values: []
            },
            publicTagIds: [],
            privateTagIds: [],
            programmaticTagIds: []
          },
          claims: {
            diagnosesICDMinCount: {
              value: null
            },
            diagnosesICD: {
              values: []
            },
            genericNames: {
              values: []
            },
            drugClasses: {
              values: []
            },
            brandNames: {
              values: []
            },
            brandNameOrGeneric: { values: [] },
            prescriptionsMinCount: {
              value: null
            },
            proceduresCPTMinCount: {
              value: null
            },
            proceduresCPT: {
              values: []
            },
            proceduresHCPCMinCount: {
              value: null
            },
            proceduresHCPC: {
              values: []
            },
            timeFrame: {
              value: null
            }
          },
          trials: {
            minCount: { value: null },
            maxCount: { value: null },
            status: {
              values: []
            },
            phase: {
              values: []
            },
            studyType: {
              values: []
            },
            id: {
              values: []
            },
            funderType: {
              values: []
            },
            sponsor: {
              values: []
            },
            sponsorType: {
              values: []
            },
            timeFrame: {
              min: 0,
              max: null,
              key: "max"
            },
            biomarkers: {
              values: []
            }
          },
          congresses: {
            minCount: { value: null },
            name: {
              values: []
            },
            type: {
              values: []
            },
            organizerName: {
              values: []
            },
            sessionType: {
              values: []
            }
          },
          engagements: {
            minCount: { value: null }
          },
          grants: {
            minAmount: { value: null },
            funder: {
              values: []
            }
          },
          payments: {
            minAmount: { value: null },
            company: {
              values: []
            },
            drugOrDevice: {
              values: []
            },
            fundingType: {
              values: []
            },
            category: {
              values: []
            }
          },
          referrals: {
            serviceLine: {
              values: []
            },
            minReferralsReceived: {
              value: null
            },
            minReferralsSent: {
              value: null
            }
          },
          patientsDiversity: {
            ageRange: {
              values: []
            },
            sex: {
              values: []
            },
            race: {
              values: []
            },
            raceMix: {
              thresholdValue: []
            }
          },
          providerDiversity: {
            languagesSpoken: {
              values: []
            },
            sex: {
              values: []
            },
            race: {
              values: []
            }
          },
          hasLinkedin: {
            value: null
          },
          hasTwitter: {
            value: null
          },
          hasCTMSData: {
            value: null
          }
        },
        activeFilters: {
          people: false,
          trials: false,
          pubs: false,
          payments: false,
          congress: false,
          tags: false,
          patientsDiversity: false,
          providerDiversity: false,
          engagements: false,
          claims: false,
          referrals: false
        }
      })
    );
  });
});
