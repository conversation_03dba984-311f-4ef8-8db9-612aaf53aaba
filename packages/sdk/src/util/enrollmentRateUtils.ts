import { EnrollmentHistory } from "../interfaces";
import { maxBy, minBy } from "lodash";
import { getTimeFromDateString } from "./clinicalTrialsUtils";

/*
 * This function estimates the enrollment rate based on the enrollment history.
 * It filters out the records with enrollment_type other than "actual" and finds the earliest and latest records.
 * If no such record is found, it returns null for both earliest and latest.
 *
 * Example of enrollment rates to display:
 *
 * > - If trial only has an estimate enrollment we should use the most recent estimate so it matches the trial registry
   >  - We need to match the text in the profile to the search card AKA- `Patients: 600 Estimated`
   > - If trial has an actual enrollment, then we use new logic where we should use the most recent actual enrollment and then initial estimated enrollment
   >  - We will change text to say `Patients: 610 Enrolled (700 Initial Estimate)`
   > - If trial has an actual but no estimation, then we should show `Patients: 610 Enrolled (No Initial Estimate)`
 */
export const getEstimatedEnrollment = (
  enrollmentHistory: EnrollmentHistory[] | undefined
): { earliest: EnrollmentHistory | null; latest: EnrollmentHistory | null } => {
  const nonActualEnrollmentRecords = enrollmentHistory?.filter(
    (record) =>
      record?.enrollment_type?.toLowerCase() !== "actual" &&
      record.enrollment &&
      record.effective_date
  );

  if (!nonActualEnrollmentRecords?.length)
    return { earliest: null, latest: null };

  const earliestNonActualEnrollmentRecord = minBy(
    nonActualEnrollmentRecords,
    (record) => getTimeFromDateString(record.effective_date)
  );

  const latestNonActualEnrollmentRecord = maxBy(
    nonActualEnrollmentRecords,
    (record) => getTimeFromDateString(record.effective_date)
  );

  return {
    earliest: earliestNonActualEnrollmentRecord || null,
    latest: latestNonActualEnrollmentRecord || null
  };
};

/*
 * This function retrieves the latest actual enrollment record from the enrollment history.
 * It filters the records to include only those with enrollment_type "actual" and finds the latest record.
 * If no such record is found, it returns null.
 */
export const getLatestActualEnrollment = (
  enrollmentHistory: EnrollmentHistory[] | undefined
) => {
  const actualEnrollmentRecords = enrollmentHistory?.filter(
    (record: EnrollmentHistory) =>
      record?.enrollment_type?.toLowerCase() === "actual"
  );

  const latestActualEnrollmentRecord = maxBy(
    actualEnrollmentRecords,
    (record) => getTimeFromDateString(record.effective_date)
  );

  return latestActualEnrollmentRecord ? latestActualEnrollmentRecord : null;
};
