import {
  DiagnosesFields,
  DiagnosesSortFields,
  ProceduresFields,
  ProcedureSortFields
} from "../resources/ClaimsResource";

export const mapDiagnosesFieldToSort = (
  field: DiagnosesFields | undefined
): DiagnosesSortFields | undefined => {
  if (!field) {
    return;
  }

  switch (field) {
    case DiagnosesFields.Count:
    case DiagnosesFields.Count1Year:
    case DiagnosesFields.Count2Year:
    case DiagnosesFields.Count5Year:
    case DiagnosesFields.InternalCount:
    case DiagnosesFields.InternalCount1Year:
    case DiagnosesFields.InternalCount2Year:
    case DiagnosesFields.InternalCount5Year:
      return DiagnosesSortFields.Count;
    case DiagnosesFields.PercentOfClaims:
    case DiagnosesFields.PercentOfClaims1Year:
    case DiagnosesFields.PercentOfClaims2Year:
    case DiagnosesFields.PercentOfClaims5Year:
      return DiagnosesSortFields.PercentOfClaims;
    case DiagnosesFields.Description:
      return DiagnosesSortFields.Description;
    case DiagnosesFields.DiagnosisCode:
      return DiagnosesSortFields.DiagnosisCode;
    case DiagnosesFields.CodeScheme:
      return DiagnosesSortFields.CodeScheme;
    default:
      return DiagnosesSortFields.Count;
  }
};

export const mapProceduresFieldToSort = (
  field: ProceduresFields | undefined
): ProcedureSortFields | undefined => {
  if (!field) {
    return;
  }

  switch (field) {
    case ProceduresFields.Count:
    case ProceduresFields.Count1Year:
    case ProceduresFields.Count2Year:
    case ProceduresFields.Count5Year:
    case ProceduresFields.InternalCount:
    case ProceduresFields.InternalCount1Year:
    case ProceduresFields.InternalCount2Year:
    case ProceduresFields.InternalCount5Year:
      return ProcedureSortFields.Count;
    case ProceduresFields.PercentOfClaims:
    case ProceduresFields.PercentOfClaims1Year:
    case ProceduresFields.PercentOfClaims2Year:
    case ProceduresFields.PercentOfClaims5Year:
      return ProcedureSortFields.PercentOfClaims;
    case ProceduresFields.Description:
      return ProcedureSortFields.Description;
    case ProceduresFields.ProcedureCode:
      return ProcedureSortFields.ProcedureCode;
    case ProceduresFields.CodeScheme:
      return ProcedureSortFields.CodeScheme;
    default:
      return ProcedureSortFields.Count;
  }
};
