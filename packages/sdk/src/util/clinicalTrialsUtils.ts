import { differenceInMonths, format, parse } from "date-fns";

const DATE_FORMAT = "yyyy-MM-dd";

export const getEndDateForDuration = (date: string | undefined): string =>
  date || format(new Date(), DATE_FORMAT);

export const getTimeFromDateString = (
  dateString: string | undefined
): number | undefined => {
  if (!dateString) return undefined;
  return parse(dateString, DATE_FORMAT, new Date()).getTime();
};

export const getDateFromDateString = (
  dateString: string | undefined
): Date | undefined => {
  if (!dateString) return undefined;
  return parse(dateString, "yyyy-MM-dd", new Date());
};

export const isActiveEndDate = (endDate: string): boolean => {
  const currentTime = new Date().getTime();
  return (
    !endDate || (getTimeFromDateString(endDate) || currentTime) > currentTime
  );
};

export const getMonthsBetween = (
  startDate: string | undefined,
  endDate: string | undefined
): number | undefined => {
  const startDateDate = getDateFromDateString(startDate);
  const endDateDate = getDateFromDateString(endDate);

  if (!startDateDate || !endDateDate) return undefined;
  return differenceInMonths(endDateDate, startDateDate);
};
