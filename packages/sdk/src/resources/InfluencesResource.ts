import { CitationProfileResponse } from "../interfaces/Influences";

export interface InfluencesResource {
  isReady(): Promise<boolean>;

  /**
   * Search for the influences of a given person and project
   *
   * @param personId The id of the person
   * @param projectId Id of the project
   * @param dateRange Id of the project
   */
  citationProfile(
    personId: string,
    projectId: string,
    dateRange: {
      min: number;
      max?: number | null;
    },
    terms?: string[],
    langCode?: string
  ): Promise<CitationProfileResponse>;
}
