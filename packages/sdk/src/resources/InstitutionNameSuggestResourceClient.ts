import { RpcResourceClient } from "@h1nyc/systems-rpc";
import { RedisOptions } from "ioredis";
import { RPC_NAMESPACE_INSTITUTION_NAME_SUGGEST } from "../constants";
import {
  InstitutionNameSuggestInput,
  InstitutionNameSuggestResponse
} from "../interfaces";
import { InstitutionNameSuggestResource } from "./InstitutionNameSuggestResource";
export class InstitutionNameSuggestResourceClient
  extends RpcResourceClient
  implements InstitutionNameSuggestResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_INSTITUTION_NAME_SUGGEST, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", []);
  }

  async institutionNameSuggestSearch(
    input: InstitutionNameSuggestInput
  ): Promise<InstitutionNameSuggestResponse> {
    return this.rpcClient.sendRequest<InstitutionNameSuggestResponse>(
      "institutionNameSuggestSearch",
      [input]
    );
  }
}
