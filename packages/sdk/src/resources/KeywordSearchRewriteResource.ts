import { Entity } from "@h1nyc/account-sdk";
import {
  KeywordSearchInput,
  HcpDiversityStatsInput,
  PatientDiversityStatsResponse,
  PersonSearchResponse
} from "../interfaces";
import { CompletionSuggesterInput } from "../interfaces/CompletionSuggesterInput";
import { Dictionary } from "lodash";

export interface KeywordSearchRewriteResource {
  isReady(): Promise<boolean>;

  keywordSearchRewrite(
    input: KeywordSearchInput
  ): Promise<PersonSearchResponse>;

  patientCount(input: KeywordSearchInput): Promise<number>;

  bulkSearchRewrite(input: KeywordSearchInput): Promise<string[]>;

  bulkEntitySearch(input: KeywordSearchInput): Promise<Entity[]>;

  keywordCompletion(input: CompletionSuggesterInput): Promise<string[]>;

  hcpDiversityStats(
    input: HcpDiversityStatsInput
  ): Promise<PatientDiversityStatsResponse>;

  hcpCountForIndications(
    input: KeywordSearchInput,
    indications: string[]
  ): Promise<Dictionary<number>>;
}
