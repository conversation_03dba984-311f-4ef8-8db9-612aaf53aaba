import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { RPC_NAMESPACE_KOL_AUTOCOMPLETE } from "../constants";
import { KeywordAutocompleteResource } from ".";
import {
  KeywordFilterAggregation,
  KeywordFilterAutocompleteInput
} from "../interfaces";

export class KeywordAutocompleteResourceClient
  extends RpcResourceClient
  implements KeywordAutocompleteResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_KOL_AUTOCOMPLETE, redisOptions);
  }

  isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", undefined);
  }

  keywordAutocomplete(
    input: KeywordFilterAutocompleteInput
  ): Promise<Array<KeywordFilterAggregation>> {
    return this.rpcClient.sendRequest("keywordAutocomplete", [input]);
  }

  nameAutocomplete(
    input: KeywordFilterAutocompleteInput
  ): Promise<Array<KeywordFilterAggregation>> {
    return this.rpcClient.sendRequest("nameAutocomplete", [input]);
  }
}
