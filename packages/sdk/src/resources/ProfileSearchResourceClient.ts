import { RpcResourceClient } from "@h1nyc/systems-rpc";
import { UserToken } from "@h1nyc/account-sdk";
import { RedisOptions } from "ioredis";
import { RPC_NAMESPACE_PROFILE_SEARCH } from "../constants";
import { GenericSearchResultInterface, HCPLocation } from "../interfaces";
import {
  AllIdsForPerson,
  CongressIdsForPerson,
  GetPaginatedTrialsForPersonInput,
  GetPaginatedTrialsForPersonResponse,
  GetTweetsForPersonInput,
  GetTweetsForPersonResponse,
  MetricSummaryOptions,
  PersonAiResponse,
  ProfileAllMetrics,
  ProfileCongressMetrics,
  ProfileFilterValue,
  ProfilePublicationMetrics,
  ProfileSearchResource,
  ProfileSortedIdsParams,
  ProfileTermsAndFilters,
  ProfileTrialMetrics,
  PublicationIdsForPerson,
  TrialIdsForPerson
} from "./ProfileSearchResource";

export class ProfileSearchResourceClient
  extends RpcResourceClient
  implements ProfileSearchResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_PROFILE_SEARCH, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", []);
  }

  async getPublicationIds(
    personId: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[],
    usersLanguage?: string
  ): Promise<string[]> {
    return this.rpcClient.sendRequest<string[]>("getPublicationIds", [
      personId,
      terms,
      filterValues,
      usersLanguage
    ]);
  }

  async getPublicationIdsForPeople(
    peopleIds: string[],
    terms?: string[],
    filterValues?: ProfileFilterValue[],
    usersLanguage?: string
  ): Promise<PublicationIdsForPerson[]> {
    return this.rpcClient.sendRequest<PublicationIdsForPerson[]>(
      "getPublicationIdsForPeople",
      [peopleIds, terms, filterValues, usersLanguage]
    );
  }

  async getPublicationMetrics(
    personId: string,
    projectId?: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[],
    usersLanguage?: string
  ): Promise<ProfilePublicationMetrics | null> {
    return this.rpcClient.sendRequest<ProfilePublicationMetrics | null>(
      "getPublicationMetrics",
      [personId, projectId, terms, filterValues, usersLanguage]
    );
  }

  async profileSearch(
    personId: string,
    queries: string[],
    projectId: string
  ): Promise<GenericSearchResultInterface[]> {
    return this.rpcClient.sendRequest<GenericSearchResultInterface[]>(
      "profileSearch",
      [personId, queries, projectId]
    );
  }

  async getPublicationMetricsForPeople(
    peopleIds: string[],
    projectId?: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[],
    usersLanguage?: string
  ): Promise<ProfilePublicationMetrics[]> {
    return this.rpcClient.sendRequest<ProfilePublicationMetrics[]>(
      "getPublicationMetricsForPeople",
      [peopleIds, projectId, terms, filterValues, usersLanguage]
    );
  }

  async getTrialIdsForPeople(
    peopleIds: string[],
    terms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<TrialIdsForPerson[]> {
    return this.rpcClient.sendRequest<TrialIdsForPerson[]>(
      "getTrialIdsForPeople",
      [peopleIds, terms, filterValues]
    );
  }

  async getTrialMetricsForPeople(
    peopleIds: string[],
    projectId?: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<ProfileTrialMetrics[]> {
    return this.rpcClient.sendRequest<ProfileTrialMetrics[]>(
      "getTrialMetricsForPeople",
      [peopleIds, projectId, terms, filterValues]
    );
  }

  async getCongressIdsForPeople(
    peopleIds: string[],
    terms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<CongressIdsForPerson[]> {
    return this.rpcClient.sendRequest<CongressIdsForPerson[]>(
      "getCongressIdsForPeople",
      [peopleIds, terms, filterValues]
    );
  }

  async getCongressMetricsForPeople(
    peopleIds: string[],
    projectId?: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<ProfileCongressMetrics[]> {
    return this.rpcClient.sendRequest<ProfileCongressMetrics[]>(
      "getCongressMetricsForPeople",
      [peopleIds, projectId, terms, filterValues]
    );
  }

  async getAllMetricsForPeople(
    peopleIds: string[],
    user: UserToken,
    termsAndFilters?: ProfileTermsAndFilters,
    usersLanguage?: string
  ): Promise<ProfileAllMetrics[]> {
    return this.rpcClient.sendRequest<ProfileAllMetrics[]>(
      "getAllMetricsForPeople",
      [peopleIds, user, termsAndFilters, usersLanguage]
    );
  }

  async getMetricSummaryForPeople(
    opts: MetricSummaryOptions
  ): Promise<ProfileAllMetrics[]> {
    return this.rpcClient.sendRequest<ProfileAllMetrics[]>(
      "getMetricSummaryForPeople",
      [opts]
    );
  }

  async getSortedIdsForPeople(
    peopleIds: string[],
    searchParams?: ProfileSortedIdsParams,
    usersLanguage?: string
  ): Promise<AllIdsForPerson[]> {
    return this.rpcClient.sendRequest<AllIdsForPerson[]>(
      "getSortedIdsForPeople",
      [peopleIds, searchParams, usersLanguage]
    );
  }

  async getAIResponseForPerson(
    personId: string,
    query: string
  ): Promise<PersonAiResponse> {
    return this.rpcClient.sendRequest<PersonAiResponse>(
      "getAIResponseForPerson",
      [personId, query]
    );
  }

  /**
   * Gets HCP Tweets for a given HCP.
   * @param input input used to fetch tweets for an HCP.
   * @param input.personId required string HCP personId to fetch tweets for.
   * @param input.projectId required string projectID to fetch HCP tweets in.
   * @param input.suppliedTerms optional string array of keyword search terms.
   * @param input.userLanguage optional string user language. (Try to provide this if possible). Will be looked up if not provided
   * @param input.page pagination info for request.
   * @param input.page.limit limits the number of returned HCP tweets.
   * @param input.page.offset offset for the HCP tweets. Used in pagination.
   * @returns paginated array of tweets for the given personId and input
   */
  getTweetsForPerson(
    input: GetTweetsForPersonInput
  ): Promise<GetTweetsForPersonResponse> {
    return this.rpcClient.sendRequest<GetTweetsForPersonResponse>(
      "getTweetsForPerson",
      [input]
    );
  }

  async getPaginatedTrialsForPerson(
    input: GetPaginatedTrialsForPersonInput
  ): Promise<GetPaginatedTrialsForPersonResponse> {
    return this.rpcClient.sendRequest<GetPaginatedTrialsForPersonResponse>(
      "getPaginatedTrialsForPerson",
      [input]
    );
  }

  async getLocationsForPerson(
    personId: string,
    projectId: string
  ): Promise<HCPLocation[]> {
    return this.rpcClient.sendRequest<HCPLocation[]>("getLocationsForPerson", [
      personId,
      projectId
    ]);
  }
}
