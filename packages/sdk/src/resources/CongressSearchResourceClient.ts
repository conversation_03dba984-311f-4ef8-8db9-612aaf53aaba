import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { RPC_NAMESPACE_CONGRESS_SEARCH } from "../constants";
import {
  CongressSearchFilterAggregation,
  CongressSearchFilterAutocompleteInput,
  CongressSearchInput,
  CongressSearchLocationFilterAggregation,
  CongressSearchResponse
} from "../interfaces";
import { CongressSearchResource } from "./CongressSearchResource";

export class CongressSearchResourceClient
  extends RpcResourceClient
  implements CongressSearchResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_CONGRESS_SEARCH, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return true;
  }

  search(input: CongressSearchInput): Promise<CongressSearchResponse> {
    return this.rpcClient.sendRequest<CongressSearchResponse>("search", [
      input
    ]);
  }

  autocompleteCountries(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]> {
    return this.rpcClient.sendRequest<
      CongressSearchLocationFilterAggregation[]
    >("autocompleteCountries", [input]);
  }

  autocompleteRegions(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]> {
    return this.rpcClient.sendRequest<
      CongressSearchLocationFilterAggregation[]
    >("autocompleteRegions", [input]);
  }

  autocompleteCities(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]> {
    return this.rpcClient.sendRequest<
      CongressSearchLocationFilterAggregation[]
    >("autocompleteCities", [input]);
  }

  autocompletePostalCodes(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]> {
    return this.rpcClient.sendRequest<
      CongressSearchLocationFilterAggregation[]
    >("autocompletePostalCodes", [input]);
  }

  autocompleteSeriesNames(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]> {
    return this.rpcClient.sendRequest<CongressSearchFilterAggregation[]>(
      "autocompleteSeriesNames",
      [input]
    );
  }

  autocompleteSocietyNames(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]> {
    return this.rpcClient.sendRequest<CongressSearchFilterAggregation[]>(
      "autocompleteSocietyNames",
      [input]
    );
  }

  autocompleteCongressTypes(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]> {
    return this.rpcClient.sendRequest<CongressSearchFilterAggregation[]>(
      "autocompleteCongressTypes",
      [input]
    );
  }

  congressCountForIndications(
    input: CongressSearchInput,
    indications: string[]
  ): Promise<Record<string, number>> {
    return this.rpcClient.sendRequest<Record<string, number>>(
      "congressCountForIndications",
      [input, indications]
    );
  }
}
