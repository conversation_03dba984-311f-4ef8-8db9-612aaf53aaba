import { GenericSearchResultInterface, HCPLocation } from "../interfaces";
import { SortOptions } from "..";
import { UserToken } from "@h1nyc/account-sdk";

export enum ProfileFilterValueType {
  PublicationType = "PublicationType",
  PublicationJournal = "PublicationJournal",
  PublicationDate = "PublicationDate",
  PublicationIsFirstOrder = "PublicationIsFirstOrder",
  PublicationIsLastOrder = "PublicationIsLastOrder",
  TrialDate = "TrialDate",
  TrialStatus = "TrialStatus",
  TrialKeyword = "TrialKeyword",
  TrialHCPRole = "TrialHCPRole",
  TrialPhase = "TrialPhase",
  TrialSponsor = "TrialSponsor",
  TrialSponsorType = "TrialSponsorType",
  TrialStudyType = "TrialStudyType",
  TrialId = "TrialId",
  Indication = "Indication",
  Biomarker = "Biomarker",
  CongressDate = "CongressDate",
  PaymentDate = "PaymentDate",
  PublicationIsGuideline = "PublicationIsGuideline"
}

export interface PersonAiResponse {
  aiResponse: string;
}

export interface TermFilterValue {
  value: string;
}

export interface TermsFilterValue {
  value: string[];
}

export interface DateFilterValue {
  value: {
    min?: number;
    max?: number;
  };
}
export interface BooleanFilterValue {
  value: boolean;
}

export interface PublicationTypeFilterValue extends TermFilterValue {
  type: ProfileFilterValueType.PublicationType;
}

export interface PublicationJournalFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.PublicationJournal;
}

export interface PublicationDateFilterValue extends DateFilterValue {
  type: ProfileFilterValueType.PublicationDate;
}
export interface PublicationIsGuidelineFilterValue extends BooleanFilterValue {
  type: ProfileFilterValueType.PublicationIsGuideline;
}

export interface TrialStatusFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.TrialStatus;
}

export interface TrialKeywordFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.TrialKeyword;
}
export interface TrialHCPRoleFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.TrialHCPRole;
}
export interface TrialPhaseFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.TrialPhase;
}
export interface TrialSponsorFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.TrialSponsor;
}
export interface TrialSponsorTypeFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.TrialSponsorType;
}
export interface TrialStudyTypeFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.TrialStudyType;
}

export interface TrialIdFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.TrialId;
}
export interface IndicationFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.Indication;
}
export interface BiomarkerFilterValue extends TermsFilterValue {
  type: ProfileFilterValueType.Biomarker;
}

export interface TrialDateFilterValue extends DateFilterValue {
  type: ProfileFilterValueType.TrialDate;
}

export interface CongressDateFilterValue extends DateFilterValue {
  type: ProfileFilterValueType.CongressDate;
}

export interface PaymentDateFilterValue extends DateFilterValue {
  type: ProfileFilterValueType.PaymentDate;
}

export interface PublicationsIsFirstOrderFilterValue
  extends BooleanFilterValue {
  type: ProfileFilterValueType.PublicationIsFirstOrder;
}

export interface PublicationsIsLastOrderFilterValue extends BooleanFilterValue {
  type: ProfileFilterValueType.PublicationIsLastOrder;
}

export type ProfileFilterValue =
  | PublicationTypeFilterValue
  | PublicationJournalFilterValue
  | PublicationDateFilterValue
  | TrialDateFilterValue
  | TrialKeywordFilterValue
  | TrialHCPRoleFilterValue
  | TrialPhaseFilterValue
  | TrialSponsorFilterValue
  | TrialSponsorTypeFilterValue
  | TrialStudyTypeFilterValue
  | TrialIdFilterValue
  | TrialStatusFilterValue
  | IndicationFilterValue
  | BiomarkerFilterValue
  | CongressDateFilterValue
  | PaymentDateFilterValue
  | PublicationsIsFirstOrderFilterValue
  | PublicationsIsLastOrderFilterValue
  | PublicationIsGuidelineFilterValue;

export interface ProfileFilters {
  publicationFiltersValues?: ProfileFilterValue[];
  trialFiltersValues?: ProfileFilterValue[];
  congressFiltersValues?: ProfileFilterValue[];
  paymentsFilterValues?: ProfileFilterValue[];
}

export interface ProfileTermsAndFilters {
  terms?: string[];
  filters?: ProfileFilters;
}

export interface ProfileSortedIdsParams {
  size: number;
  termsAndFilters: ProfileTermsAndFilters;
}

export interface ProfilePublicationMetrics {
  personId: string;
  total: number;
  totalByYear: {
    year: number;
    total: number;
  }[];
  citationSum: number;
  microBloggingSum: number;
  guidelineSum: number;
}

export interface ProfileTrialMetrics {
  personId: string;
  total: number;
  totalByYear: {
    year: number;
    total: number;
  }[];
  inProgressCount: number;
  completedCount: number;
  terminatedCount: number;
  topConditions: string[];
  topInterventions: string[];
  trialIds: string[];
}

export interface ProfilePaymentMetrics {
  personId: string;
  total: number;
  totalPaymentsAmount: number;
}

export interface ProfileCongressMetrics {
  personId: string;
  total: number;
  postersTotal: number;
  sessionsTotal: number;
  topCongresses: {
    name: string;
    presentations: number;
  }[];
}

export interface ProfileDiagnosisMetrics {
  personId: string;
  total: number;
  internalCountSum: number;
}

export interface ProfileProcedureMetrics {
  personId: string;
  total: number;
  internalCountSum: number;
}

export interface ProfileAllMetrics {
  publicationMetrics: ProfilePublicationMetrics | null;
  trialMetrics: ProfileTrialMetrics | null;
  congressMetrics: ProfileCongressMetrics | null;
  diagnosisMetrics: ProfileDiagnosisMetrics | null;
  procedureMetrics: ProfileProcedureMetrics | null;
  paymentMetrics: ProfilePaymentMetrics | null;
}

export interface AllIdsForPerson {
  personId: string;
  publicationIds: string[];
  trialIds: string[];
  congressIds: string[];
}

export interface PublicationIdsForPerson {
  personId: string;
  publicationIds: string[];
}

export interface TrialIdsForPerson {
  personId: string;
  trialIds: string[];
}

export interface CongressIdsForPerson {
  personId: string;
  congressIds: string[];
}

export interface PaymentIdsForPerson {
  personId: string;
  paymentIds: string[];
}

export enum PublicationFields {
  datePublished = "publications.datePublished"
}

export enum TrialFields {
  startDate = "trials.startDate"
}

export enum CongressFields {
  masterInitialDate = "congress.masterInitialDate"
}

export type PublicationSortOptions = SortOptions<PublicationFields>;
export type TrialSortOptions = SortOptions<TrialFields>;
export type CongressSortOptions = SortOptions<CongressFields>;

export enum ClaimsType {
  LexisNexis = "LexisNexis",
  DRG = "DRG"
}

export interface MetricSummaryOptions {
  peopleIds: string[];
  projectId?: string;
  /**
   * @deprecated claimsType defaults to DRG now. This will be ignored.
   */
  claimsType?: ClaimsType;
  termsAndFilters?: ProfileTermsAndFilters;
  languageCode?: string;
}

export interface HCPTweet {
  id: string;
  text: string;
  isRetweet: boolean;
  isQuote: boolean;
  isReply: boolean;
  date: Date;
}

export interface GetTweetsForPersonInput {
  personId: string;
  projectId: string;
  suppliedTerms?: string[];
  userLanguage?: string;
  page: {
    limit: number;
    offset: number;
  };
}

export interface GetTweetsForPersonResponse {
  total: number;
  personId: string;
  tweets: HCPTweet[];
}

export interface GetPaginatedTrialsForPersonInput {
  personId: string;
  projectId: string;
  terms?: string[];
  filters?: ProfileFilterValue[];
  page: {
    limit: number;
    offset: number;
  };
}

export interface GetPaginatedTrialsForPersonResponse {
  h1Trials: { id: string; affiliatedBy: string[] }[];
  ctmsTrials: { id: string; affiliatedBy: string[] }[];
  totalCount: number;
}

export interface ProfileSearchResource {
  isReady(): Promise<boolean>;

  /**
   * Get a list publication ids for a person that match the search terms.
   *
   * @param personId The id of the person
   * @param terms Search terms
   * @param filterValues Filters
   *
   * @deprecated Use getPublicationIdsForPeople instead
   */
  getPublicationIds(
    personId: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[],
    usersLanguage?: string
  ): Promise<string[]>;

  /**
   * Get the publication ids for a group of people that match the search criteria.
   *
   * @param peopleIds The ids of the group of people
   * @param terms Search terms
   * @param filterValues Filters
   */
  getPublicationIdsForPeople(
    peopleIds: string[],
    terms?: string[],
    filterValues?: ProfileFilterValue[],
    usersLanguage?: string
  ): Promise<PublicationIdsForPerson[]>;

  /**
   * Get the aggregated publication metrics for a person.
   *
   * @param personId The id of the person
   * @param terms Search terms
   * @param filterValues Filters
   *
   * @deprecated Use getPublicationMetricsForPeople instead
   */
  getPublicationMetrics(
    personId: string,
    projectId?: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[],
    usersLanguage?: string
  ): Promise<ProfilePublicationMetrics | null>;

  /**
   * Search for the sub documents in the profile, given person and project
   *
   * @param personId The id of the person
   * @param queries terms in the query
   * @param projectId Id of the project
   */
  profileSearch(
    personId: string,
    queries: string[],
    projectId: string
  ): Promise<GenericSearchResultInterface[]>;

  /**
   * Get the aggregated publication metrics for a group of people.
   *
   * @param peopleIds The ids of the group of people
   * @param terms Search terms
   * @param filterValues Filters
   */
  getPublicationMetricsForPeople(
    peopleIds: string[],
    projectId?: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[],
    usersLanguage?: string
  ): Promise<ProfilePublicationMetrics[]>;

  /**
   * Get the trial ids for a group of people that match the search criteria.
   *
   * @param peopleIds The ids of the group of people
   * @param terms Search terms
   * @param filterValues Filters
   */
  getTrialIdsForPeople(
    peopleIds: string[],
    terms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<TrialIdsForPerson[]>;

  /**
   * Get the paginated trial ids for a person
   *
   * @param input.personId The id of the person;
   * @param input.projectId The current project Id
   * @param input.terms Array of search terms
   * @param input.filters Filters
   * @param input.page Pagination object
   */
  getPaginatedTrialsForPerson(
    input: GetPaginatedTrialsForPersonInput
  ): Promise<GetPaginatedTrialsForPersonResponse>;

  /**
   * Get the aggregated trial metrics for a group of people.
   *
   * @param peopleIds The ids of the group of people
   * @param terms Search terms
   * @param filterValues Filters
   */
  getTrialMetricsForPeople(
    peopleIds: string[],
    projectId?: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<ProfileTrialMetrics[]>;

  /**
   * Get the congress ids for a group of people that match the search criteria.
   *
   * @param peopleIds The ids of the group of people
   * @param terms Search terms
   * @param filterValues Search filters
   */
  getCongressIdsForPeople(
    peopleIds: string[],
    terms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<CongressIdsForPerson[]>;

  /**
   * Get the aggregated congress metrics for a group of people.
   *
   * @param peopleIds The ids of the group of people
   * @param terms Search terms
   * @param filterValues Filters
   *
   */
  getCongressMetricsForPeople(
    peopleIds: string[],
    projectId?: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<ProfileCongressMetrics[]>;

  /**
   * Get the aggregated metrics for all types for a group of people.
   *
   * @param peopleIds The ids of the group of people
   * @param termsAndFilters the terms and filters for all types of documents searched (COngress, Publication, Trial, Payments)
   */
  getAllMetricsForPeople(
    peopleIds: string[],
    user: UserToken,
    termsAndFilters?: ProfileTermsAndFilters,
    usersLanguage?: string
  ): Promise<ProfileAllMetrics[]>;

  /**
   * Get the aggregated metrics for a group of people.
   * @param opts Options
   */
  getMetricSummaryForPeople(
    opts: MetricSummaryOptions
  ): Promise<ProfileAllMetrics[]>;

  /**
   * Get the all ids (Congress, Publications, Clinical Trials) for a group of people that match the search criteria.
   *
   * @param peopleIds The ids of the group of people
   * @param searchParams params to search including size (number of results) and filters and terms (Congress, Publication, Trial)
   */
  getSortedIdsForPeople(
    peopleIds: string[],
    searchParams?: ProfileSortedIdsParams,
    usersLanguage?: string
  ): Promise<AllIdsForPerson[]>;

  /**
   * Gets the AI response for a given HCP.
   * @param personId the HCP ID
   * @param query  the query to get the AI response for
   */
  getAIResponseForPerson(
    personId: string,
    query: string
  ): Promise<PersonAiResponse>;

  /**
   * Gets HCP Tweets for a given HCP.
   * @param input input used to fetch tweets for an HCP.
   * @param input.personId required string HCP personId to fetch tweets for.
   * @param input.projectId required string projectID to fetch HCP tweets in.
   * @param input.suppliedTerms optional string array of keyword search terms.
   * @param input.userLanguage optional string user language. (Try to provide this if possible). Will be looked up if not provided
   * @param input.page pagination info for request.
   * @param input.page.limit limits the number of returned HCP tweets.
   * @param input.page.offset offset for the HCP tweets. Used in pagination.
   * @returns paginated array of tweets for the given personId and input
   */
  getTweetsForPerson(
    input: GetTweetsForPersonInput
  ): Promise<GetTweetsForPersonResponse>;

  /**
   * Gets the locations for a given HCP.
   * @param personId the HCP ID
   * @param projectId the ID of the project the HCP is in
   */
  getLocationsForPerson(
    personId: string,
    projectId: string
  ): Promise<HCPLocation[]>;
}
