import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { CTMSResource } from "./CTMSResource";
import { RPC_NAMESPACE_CTMS } from "../constants";
import {
  CTMSFacilityAggregationResponse,
  CTMSFacilityAggregationInput,
  CTMSFacilityInitialFilterOptions,
  CTMSInvestigatorAggregationResponse,
  CTMSInvestigatorAggregationInput,
  CTMSInvestigatorInitialFilterOptions
} from "../interfaces";

export class CTMSResourceClient
  extends RpcResourceClient
  implements CTMSResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_CTMS, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return true;
  }

  ctmsFacilityAggregation(
    input: CTMSFacilityAggregationInput
  ): Promise<CTMSFacilityAggregationResponse> {
    return this.rpcClient.sendRequest<CTMSFacilityAggregationResponse>(
      "ctmsFacilityAggregation",
      [input]
    );
  }

  ctmsFacilityInitialFilterOptions(
    institutionId: string
  ): Promise<CTMSFacilityInitialFilterOptions> {
    return this.rpcClient.sendRequest<CTMSFacilityInitialFilterOptions>(
      "ctmsFacilityInitialFilterOptions",
      [institutionId]
    );
  }

  hasCTMSData(institutionId: string): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("hasCTMSData", [institutionId]);
  }

  getAllCTMSFacilityIds(): Promise<string[]> {
    return this.rpcClient.sendRequest("getAllCTMSFacilityIds", []);
  }

  getAllCTMSPersonIds(): Promise<string[]> {
    return this.rpcClient.sendRequest("getAllCTMSPersonIds", []);
  }

  ctmsInvestigatorAggregation(
    input: CTMSInvestigatorAggregationInput
  ): Promise<CTMSInvestigatorAggregationResponse> {
    return this.rpcClient.sendRequest<CTMSInvestigatorAggregationResponse>(
      "ctmsInvestigatorAggregation",
      [input]
    );
  }

  ctmsInvestigatorInitialFilterOptions(
    personId: string
  ): Promise<CTMSInvestigatorInitialFilterOptions> {
    return this.rpcClient.sendRequest<CTMSInvestigatorInitialFilterOptions>(
      "ctmsInvestigatorInitialFilterOptions",
      [personId]
    );
  }
  personHasCTMSData(personId: string): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("personHasCTMSData", [personId]);
  }
}
