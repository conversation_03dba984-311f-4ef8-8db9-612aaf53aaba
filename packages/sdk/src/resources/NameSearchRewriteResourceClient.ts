import { RpcResourceClient } from "@h1nyc/systems-rpc";
import { RedisOptions } from "ioredis";
import { RPC_NAMESPACE_NAME_SEARCH_REWRITE } from "../constants";
import { FOPersonSearchResponse, PersonSearchResponse } from "../interfaces";
import { NameSearchInput } from "../interfaces/NameSearchInput";
import { NameSearchRewriteResource } from "./NameSearchRewriteResource";

export class NameSearchRewriteResourceClient
  extends RpcResourceClient
  implements NameSearchRewriteResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_NAME_SEARCH_REWRITE, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", []);
  }

  async runNameSearchRewrite(
    input: NameSearchInput
  ): Promise<PersonSearchResponse> {
    return this.rpcClient.sendRequest<PersonSearchResponse>(
      "runNameSearchRewrite",
      [input]
    );
  }

  async runFONameSearch(
    input: NameSearchInput
  ): Promise<FOPersonSearchResponse> {
    return this.rpcClient.sendRequest<FOPersonSearchResponse>(
      "runFONameSearch",
      [input]
    );
  }
}
