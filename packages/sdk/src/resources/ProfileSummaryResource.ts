export interface ProfileSummaryMetrics {
  personId: string;
  yearsActive: number;
  publications: {
    total: number;
    last12Months: number;
  };
  trials: {
    total: number;
    inProgress: number;
  };
  congresses: {
    total: number;
  };
  payments: {
    total: number;
    amount: number;
    topCompanyDistributions: {
      companyName: string;
      percentage: number;
    }[];
  };
}

export interface ProfileTopMeshTerms {
  personId: string;
  totalPublications: number;
  publicationsByYear: {
    year: number;
    total: number;
  }[];
  topKeyWords: {
    name: string;
    total: number;
    keywordsByYear: {
      year: number;
      total: number;
    }[];
  }[];
}

export interface ProfileSummaryResource {
  isReady(): Promise<boolean>;

  getMetricsForPeople(
    peopleIds: string[],
    projectId?: string
  ): Promise<ProfileSummaryMetrics[]>;

  getTopMeshTermsForPeople(
    peopleIds: string[],
    size: number,
    startDate: Date,
    endDate: Date,
    projectId?: string
  ): Promise<ProfileTopMeshTerms[]>;
}
