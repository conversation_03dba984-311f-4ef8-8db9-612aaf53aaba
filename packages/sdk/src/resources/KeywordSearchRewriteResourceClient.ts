import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { RPC_NAMESPACE_KOL_SEARCH_REWRITE } from "../constants";
import { KeywordSearchRewriteResource } from ".";
import {
  KeywordSearchInput,
  HcpDiversityStatsInput,
  PatientDiversityStatsResponse,
  PersonSearchResponse
} from "../interfaces";
import { Entity } from "@h1nyc/account-sdk";
import { CompletionSuggesterInput } from "../interfaces/CompletionSuggesterInput";
import { Dictionary } from "lodash";

export class KeywordSearchRewriteResourceClient
  extends RpcResourceClient
  implements KeywordSearchRewriteResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_KOL_SEARCH_REWRITE, redisOptions);
  }

  isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", undefined);
  }

  keywordSearchRewrite(
    input: KeywordSearchInput
  ): Promise<PersonSearchResponse> {
    return this.rpcClient.sendRequest("keywordSearchRewrite", [input]);
  }

  patientCount(input: KeywordSearchInput): Promise<number> {
    return this.rpcClient.sendRequest("patientCount", [input]);
  }

  bulkSearchRewrite(input: KeywordSearchInput): Promise<Array<string>> {
    return this.rpcClient.sendRequest("bulkSearchRewrite", [input]);
  }

  bulkEntitySearch(input: KeywordSearchInput): Promise<Entity[]> {
    return this.rpcClient.sendRequest("bulkEntitySearch", [input]);
  }

  keywordCompletion(input: CompletionSuggesterInput): Promise<string[]> {
    return this.rpcClient.sendRequest("keywordCompletion", [input]);
  }

  hcpDiversityStats(
    input: HcpDiversityStatsInput
  ): Promise<PatientDiversityStatsResponse> {
    return this.rpcClient.sendRequest("hcpDiversityStats", [input]);
  }

  hcpCountForIndications(
    input: KeywordSearchInput,
    indications: string[]
  ): Promise<Dictionary<number>> {
    return this.rpcClient.sendRequest("hcpCountForIndications", [
      input,
      indications
    ]);
  }
}
