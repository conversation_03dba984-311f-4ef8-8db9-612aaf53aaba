import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { DiversityResource } from "./DiversityResource";
import { RPC_NAMESPACE_DIVERSITY } from "../constants";
import { ProviderExportDiversityInfo } from "../interfaces/Diversity";

export class DiversityResourceClient
  extends RpcResourceClient
  implements DiversityResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_DIVERSITY, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return true;
  }

  /**
   * Search for diversity fields for specific people.
   *
   * @param personIds The id of the person
   *
   */
  getDiversity(personIds: string[]): Promise<ProviderExportDiversityInfo[]> {
    return this.rpcClient.sendRequest<ProviderExportDiversityInfo[]>(
      "getDiversity",
      [personIds]
    );
  }
}
