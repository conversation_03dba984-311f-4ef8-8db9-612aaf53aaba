import { faker } from "@faker-js/faker";
import { CongressEventPageSearchResourceClient } from "./CongressEventPageSearchResourceClient";
import {
  SessionsSearchByCongressInput,
  SessionsSearchByCongressResponse
} from "../interfaces";

describe("CongressEventPageSearchResourceClient", () => {
  describe("isReady", () => {
    it("should return true", async () => {
      const congressSearchResourceClient =
        new CongressEventPageSearchResourceClient(faker.datatype.string());

      expect(congressSearchResourceClient.isReady()).resolves.toEqual(true);
    });
  });

  describe("searchSessionsByCongress", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient =
        new CongressEventPageSearchResourceClient(faker.datatype.string());

      const expectedResponse: SessionsSearchByCongressResponse = {
        total: faker.datatype.number(),
        sessions: [
          {
            id: faker.datatype.number().toString(),
            name: faker.datatype.string(),
            sessionType: faker.datatype.string(),
            description: faker.datatype.string(),
            speakers: [
              {
                id: faker.datatype.number().toString(),
                name: faker.name.fullName(),
                role: faker.datatype.string()
              }
            ],
            subsessions: [
              {
                id: faker.datatype.number().toString(),
                name: faker.datatype.string(),
                sessionType: faker.datatype.string(),
                description: faker.datatype.string(),
                speakers: [
                  {
                    id: faker.datatype.number().toString(),
                    name: faker.name.fullName(),
                    role: faker.datatype.string()
                  }
                ],
                translations: [
                  {
                    name: faker.datatype.string(),
                    description: faker.datatype.string(),
                    languageCode: faker.datatype.string(),
                    sessionType: faker.datatype.string()
                  }
                ]
              }
            ],
            translations: [
              {
                name: faker.datatype.string(),
                description: faker.datatype.string(),
                languageCode: faker.datatype.string(),
                sessionType: faker.datatype.string()
              }
            ]
          }
        ]
      };

      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input: SessionsSearchByCongressInput = {
        congressId: faker.datatype.number().toString(),
        query: faker.datatype.string(),
        filters: { dateRange: { min: faker.datatype.number() } },
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const actualResponse =
        await congressSearchResourceClient.searchSessionsByCongress(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("searchSessionsByCongress", [
        input
      ]);
    });
  });

  describe("getSpeakerIdsByRole", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient =
        new CongressEventPageSearchResourceClient(faker.datatype.string());
      const expectedResponse = [faker.datatype.string()];
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input = {
        congressId: faker.datatype.number().toString(),
        roles: [faker.datatype.string()]
      };

      const actualResponse =
        await congressSearchResourceClient.getSpeakerIdsByRole(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("getSpeakerIdsByRole", [
        input
      ]);
    });
  });

  describe("getIndicationsForCongress", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient =
        new CongressEventPageSearchResourceClient(faker.datatype.string());
      const expectedResponse = [faker.datatype.string()];
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const congressId = faker.datatype.number().toString();

      const actualResponse =
        await congressSearchResourceClient.getIndicationsForCongress(
          congressId
        );

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("getIndicationsForCongress", [
        congressId
      ]);
    });
  });

  describe("autocompleteSessionTypes", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient =
        new CongressEventPageSearchResourceClient(faker.datatype.string());
      const expectedResponse = [
        {
          id: faker.datatype.string(),
          label: faker.datatype.string(),
          count: faker.datatype.number()
        }
      ];
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input = {
        globalQuery: faker.datatype.string(),
        filterQuery: faker.datatype.string(),
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string(),
        congressId: faker.datatype.string()
      };

      const actualResponse =
        await congressSearchResourceClient.autocompleteSessionTypes(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompleteSessionTypes", [
        input
      ]);
    });
  });
});
