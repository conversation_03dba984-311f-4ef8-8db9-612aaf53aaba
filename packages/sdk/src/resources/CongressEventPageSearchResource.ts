import {
  CongressSearchFilterAggregation,
  GetSpeakerIdsByRoleInput,
  SessionsSearchByCongressInput,
  SessionsSearchByCongressResponse,
  SessionsSearchFilterAutocompleteInput
} from "../interfaces";

export interface CongressEventPageSearchResource {
  isReady(): Promise<boolean>;

  searchSessionsByCongress(
    input: SessionsSearchByCongressInput
  ): Promise<SessionsSearchByCongressResponse>;

  getSpeakerIdsByRole(input: GetSpeakerIdsByRoleInput): Promise<string[]>;

  getIndicationsForCongress(congressId: string): Promise<string[]>;

  autocompleteSessionTypes(
    input: SessionsSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]>;
}
