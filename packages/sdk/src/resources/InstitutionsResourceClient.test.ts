import { faker } from "@faker-js/faker";
import { InstitutionsResourceClient } from "./InstitutionsResourceClient";
import {
  Institution,
  InstitutionFilterAggregation,
  InstitutionsResponse,
  InstitutionsSearchInput,
  InstitutionsAutocompleteInput,
  TrialsSearchByInstitutionInput,
  TrialsSearchByInstitutionResponse
} from "../interfaces/Institutions";
import { INSTITUTION_ACCESS_LEVEL } from "@h1nyc/account-sdk";

// TODO: this _really_ should be extracted to a test utility instead of having 3 copies strewn about
function createFakeInstitution(
  overrides: Partial<Institution> = {}
): Institution {
  const institution = {
    id: faker.datatype.number(),
    name: faker.datatype.string(),
    institutionType: faker.datatype.string(),
    website: faker.internet.url(),
    beds: faker.datatype.number(),
    region: faker.datatype.string(),
    linkedinUrl: faker.internet.url(),
    twitterUrl: faker.internet.url(),
    totalTags: faker.datatype.number(),
    people: {
      matched: faker.datatype.number(),
      total: faker.datatype.number()
    },
    publications: {
      matched: faker.datatype.number(),
      total: faker.datatype.number()
    },
    trials: {
      matched: faker.datatype.number(),
      total: faker.datatype.number()
    },
    procedures: {
      matched: faker.datatype.number(),
      total: faker.datatype.number()
    },
    diagnoses: {
      matched: faker.datatype.number(),
      total: faker.datatype.number()
    },
    prescriptions: {
      matched: faker.datatype.number(),
      total: faker.datatype.number()
    },
    congresses: {
      matched: faker.datatype.number(),
      total: faker.datatype.number()
    },
    payments: {
      matched: faker.datatype.number(),
      total: faker.datatype.number()
    },
    address: {
      street1: faker.address.streetAddress(),
      street2: faker.address.secondaryAddress(),
      country: faker.address.country(),
      city: faker.address.city(),
      region: faker.address.state(),
      regionCode: faker.address.stateAbbr(),
      postalCode: faker.address.zipCode()
    },
    location: {
      lat: faker.datatype.number(),
      lon: faker.datatype.number()
    }
  };

  return { ...institution, ...overrides };
}

describe("InstitutionsResourceClient", () => {
  describe("isReady", () => {
    it("should return true", async () => {
      const institutionsResourceClient = new InstitutionsResourceClient(
        faker.datatype.string()
      );

      expect(institutionsResourceClient.isReady()).resolves.toEqual(true);
    });
  });

  describe("autocompleteInstitutionCountries", () => {
    it("should return the results of sendRequest", async () => {
      const institutionsResourceClient = new InstitutionsResourceClient(
        faker.datatype.string()
      );

      const institutionFilters: InstitutionFilterAggregation[] = [
        {
          id: faker.address.countryCode(),
          count: faker.datatype.number()
        },
        {
          id: faker.address.countryCode(),
          count: faker.datatype.number()
        }
      ];

      const input: InstitutionsAutocompleteInput = {
        query: faker.datatype.string(),
        prefix: faker.datatype.string(),
        filters: {
          countries: [faker.address.countryCode(), faker.address.countryCode()],
          regions: [faker.address.stateAbbr(), faker.address.stateAbbr()],
          cities: [faker.address.city(), faker.address.city()],
          postalCodes: [faker.address.zipCode(), faker.address.zipCode()]
        },
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        accessLevel: faker.helpers.arrayElement(
          Object.values(INSTITUTION_ACCESS_LEVEL)
        )
      };

      const sendRequestSpy = jest
        .spyOn(institutionsResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(institutionFilters);

      const actualInstitutionFilters =
        await institutionsResourceClient.autocompleteCountries(input);

      expect(actualInstitutionFilters).toEqual(institutionFilters);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompleteCountries", [
        input
      ]);
    });
  });

  describe("autocompleteInstitutionRegions", () => {
    it("should return the results of sendRequest", async () => {
      const institutionsResourceClient = new InstitutionsResourceClient(
        faker.datatype.string()
      );

      const institutionFilters: InstitutionFilterAggregation[] = [
        {
          id: faker.address.stateAbbr(),
          count: faker.datatype.number()
        },
        {
          id: faker.address.stateAbbr(),
          count: faker.datatype.number()
        }
      ];

      const input: InstitutionsAutocompleteInput = {
        query: faker.datatype.string(),
        prefix: faker.datatype.string(),
        filters: {
          countries: [faker.address.countryCode(), faker.address.countryCode()],
          regions: [faker.address.stateAbbr(), faker.address.stateAbbr()],
          cities: [faker.address.city(), faker.address.city()],
          postalCodes: [faker.address.zipCode(), faker.address.zipCode()]
        },
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        accessLevel: faker.helpers.arrayElement(
          Object.values(INSTITUTION_ACCESS_LEVEL)
        )
      };

      const sendRequestSpy = jest
        .spyOn(institutionsResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(institutionFilters);

      const actualInstitutionFilters =
        await institutionsResourceClient.autocompleteRegions(input);

      expect(actualInstitutionFilters).toEqual(institutionFilters);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompleteRegions", [
        input
      ]);
    });
  });

  describe("autocompleteInstitutionCities", () => {
    it("should return the results of sendRequest", async () => {
      const institutionsResourceClient = new InstitutionsResourceClient(
        faker.datatype.string()
      );

      const institutionFilters: InstitutionFilterAggregation[] = [
        {
          id: faker.address.cityName(),
          count: faker.datatype.number()
        },
        {
          id: faker.address.cityName(),
          count: faker.datatype.number()
        }
      ];

      const input: InstitutionsAutocompleteInput = {
        query: faker.datatype.string(),
        prefix: faker.datatype.string(),
        filters: {
          countries: [faker.address.countryCode(), faker.address.countryCode()],
          regions: [faker.address.stateAbbr(), faker.address.stateAbbr()],
          cities: [faker.address.city(), faker.address.city()],
          postalCodes: [faker.address.zipCode(), faker.address.zipCode()]
        },
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        accessLevel: faker.helpers.arrayElement(
          Object.values(INSTITUTION_ACCESS_LEVEL)
        )
      };

      const sendRequestSpy = jest
        .spyOn(institutionsResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(institutionFilters);

      const actualInstitutionFilters =
        await institutionsResourceClient.autocompleteCities(input);

      expect(actualInstitutionFilters).toEqual(institutionFilters);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompleteCities", [
        input
      ]);
    });
  });

  describe("autocompleteInstitutionPostalCodes", () => {
    it("should return the results of sendRequest", async () => {
      const institutionsResourceClient = new InstitutionsResourceClient(
        faker.datatype.string()
      );

      const institutionFilters: InstitutionFilterAggregation[] = [
        {
          id: faker.address.zipCode(),
          count: faker.datatype.number()
        },
        {
          id: faker.address.zipCode(),
          count: faker.datatype.number()
        }
      ];

      const input: InstitutionsAutocompleteInput = {
        query: faker.datatype.string(),
        prefix: faker.datatype.string(),
        filters: {
          countries: [faker.address.countryCode(), faker.address.countryCode()],
          regions: [faker.address.stateAbbr(), faker.address.stateAbbr()],
          cities: [faker.address.city(), faker.address.city()],
          postalCodes: [faker.address.zipCode(), faker.address.zipCode()]
        },
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        accessLevel: faker.helpers.arrayElement(
          Object.values(INSTITUTION_ACCESS_LEVEL)
        )
      };

      const sendRequestSpy = jest
        .spyOn(institutionsResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(institutionFilters);

      const actualInstitutionFilters =
        await institutionsResourceClient.autocompletePostalCodes(input);

      expect(actualInstitutionFilters).toEqual(institutionFilters);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompletePostalCodes", [
        input
      ]);
    });
  });

  describe("search", () => {
    it("should return the results of sendRequest", async () => {
      const institutionsResourceClient = new InstitutionsResourceClient(
        faker.datatype.string()
      );

      const institutionsResponse: InstitutionsResponse = {
        total: faker.datatype.number(),
        institutions: [createFakeInstitution(), createFakeInstitution()],
        countryAggregations: [],
        regionAggregations: [],
        cityAggregations: [],
        postalCodeAggregations: [],
        synonyms: [],
        icdCodeSynonyms: [],
        isNavigationalQuery: true
      };

      const sendRequestSpy = jest
        .spyOn(institutionsResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(institutionsResponse);

      const input: InstitutionsSearchInput = {
        query: faker.datatype.string(),
        paging: {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        },
        projectId: faker.datatype.string(),
        accessLevel: faker.helpers.arrayElement(
          Object.values(INSTITUTION_ACCESS_LEVEL)
        )
      };

      const actualInstitutionsResponse =
        await institutionsResourceClient.search(input);

      expect(actualInstitutionsResponse).toEqual(institutionsResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("search", [input]);
    });
  });

  describe("searchTrialsByInstitution", () => {
    it("should return the results of sendRequest", async () => {
      const institutionsResourceClient = new InstitutionsResourceClient(
        faker.datatype.string()
      );

      const expectedTrialsSearchByInstitutionResponse: TrialsSearchByInstitutionResponse =
        {
          institutionId: faker.datatype.number(),
          matchedTrialIds: [],
          matchedCtmsTrialIds: [],
          totalMatchedTrials: faker.datatype.number(),
          totalTrials: faker.datatype.number(),
          totalMatchedPersons: faker.datatype.number(),
          totalPersons: faker.datatype.number(),
          matchedTrials: []
        };

      const sendRequestSpy = jest
        .spyOn(institutionsResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedTrialsSearchByInstitutionResponse);

      const input: TrialsSearchByInstitutionInput = {
        institutionId: faker.datatype.number(),
        query: faker.datatype.string(),
        trialsFilters: { dateRange: { min: faker.datatype.number() } }
      };

      const actualTrialsSearchByInstitutionResponse =
        await institutionsResourceClient.searchTrialsByInstitution(input);

      expect(actualTrialsSearchByInstitutionResponse).toEqual(
        expectedTrialsSearchByInstitutionResponse
      );
      expect(sendRequestSpy).toHaveBeenCalledWith("searchTrialsByInstitution", [
        input
      ]);
    });
  });
});
