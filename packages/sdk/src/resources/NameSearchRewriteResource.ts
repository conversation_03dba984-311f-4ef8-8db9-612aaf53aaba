import {
  FOPersonSearchResponse,
  PersonSearchResponse
} from "../interfaces/elasticDocuments";

import { NameSearchInput } from "../interfaces/NameSearchInput";

export interface NameSearchRewriteResource {
  /**
   * Ready check
   */
  isReady(): Promise<boolean>;

  runNameSearchRewrite(input: NameSearchInput): Promise<PersonSearchResponse>;

  runFONameSearch(input: NameSearchInput): Promise<FOPersonSearchResponse>;
}
