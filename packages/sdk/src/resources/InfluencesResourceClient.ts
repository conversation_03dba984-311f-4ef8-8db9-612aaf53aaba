import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { InfluencesResource } from ".";
import { RPC_NAMESPACE_INFLUENCES } from "../constants";
import { CitationProfileResponse } from "../interfaces/Influences";

export class InfluencesResourceClient
  extends RpcResourceClient
  implements InfluencesResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_INFLUENCES, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return true;
  }

  citationProfile(
    personId: string,
    projectId: string,
    dateRange: {
      min: number;
      max?: number | null;
    },
    terms?: string[],
    langCode?: string
  ): Promise<CitationProfileResponse> {
    return this.rpcClient.sendRequest<CitationProfileResponse>(
      "citationProfile",
      [personId, projectId, dateRange, terms, langCode]
    );
  }
}
