import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { NetworkResource } from ".";
import { RPC_NAMESPACE_NETWORK } from "../constants";
import {
  NetworkResponse,
  NetworkRequest,
  NetworkCollaborator,
  NetworkCollaboratorRequest,
  NetworkFilterAggregation,
  NetworkFilterAutocompleteRequest,
  BulkNetworkRequest,
  BulkNetworkResponse
} from "../interfaces/Network";

export class NetworkResourceClient
  extends RpcResourceClient
  implements NetworkResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_NETWORK, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return true;
  }

  findNetwork(request: NetworkRequest): Promise<NetworkResponse> {
    return this.rpcClient.sendRequest<NetworkResponse>("findNetwork", [
      request
    ]);
  }

  findBulkNetwork(request: BulkNetworkRequest): Promise<BulkNetworkResponse[]> {
    return this.rpcClient.sendRequest<BulkNetworkResponse[]>(
      "findBulkNetwork",
      [request]
    );
  }

  findCollaborator(
    request: NetworkCollaboratorRequest
  ): Promise<NetworkCollaborator> {
    return this.rpcClient.sendRequest<NetworkCollaborator>("findCollaborator", [
      request
    ]);
  }

  filterAutocomplete(
    request: NetworkFilterAutocompleteRequest
  ): Promise<NetworkFilterAggregation[]> {
    return this.rpcClient.sendRequest<NetworkFilterAggregation[]>(
      "filterAutocomplete",
      [request]
    );
  }
}
