import { RpcClient } from "@h1nyc/systems-rpc";
import { faker } from "@faker-js/faker";
import { DiversityResourceClient } from "./DiversityResourceClient";

let client: DiversityResourceClient;
let rpcClient: RpcClient;

beforeAll(() => {
  client = new DiversityResourceClient("testing");
  rpcClient = client.rpcClient;
});

describe("isReady", () => {
  it("should return true", async () => {
    const diversityResourceClient = new DiversityResourceClient(
      faker.datatype.string()
    );

    expect(diversityResourceClient.isReady()).resolves.toEqual(true);
  });
});

describe("getDiversity", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const personIdOne = faker.random.alphaNumeric();
    const personIdTwo = faker.random.alphaNumeric();
    const personIds = [personIdOne, personIdTwo];

    client.getDiversity(personIds);

    expect(sendRequestSpy).toHaveBeenCalledWith("getDiversity", [personIds]);
  });
});
