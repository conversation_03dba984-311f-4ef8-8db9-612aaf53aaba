import {
  ClaimsCcsrDiagnosis,
  ClaimsCcsrProcedure,
  ClaimsDiagnosis,
  ClaimsPrescription,
  ClaimsProcedure
} from "../interfaces/Claims";
import {
  PaginationOptions,
  PaginationResponse
} from "../interfaces/pagination";
import { SortOptions } from "../interfaces/SortOptions";

export enum DiagnosesSortFields {
  Description = "Description",
  DiagnosisCode = "DiagnosisCode",
  PercentOfClaims = "PercentOfClaims",
  Count = "Count",
  CodeScheme = "CodeScheme"
}

export enum DiagnosesCcsrSortFields {
  Description = "Description",
  PercentOfClaims = "PercentOfClaims",
  HcpQuartile = "HcpQuartile"
}

export enum ProceduresCcsrSortFields {
  Description = "Description",
  PercentOfClaims = "PercentOfClaims",
  HcpQuartile = "HcpQuartile"
}

export enum ProcedureSortFields {
  Description = "Description",
  ProcedureCode = "ProcedureCode",
  PercentOfClaims = "PercentOfClaims",
  Count = "Count",
  CodeScheme = "CodeScheme"
}

export enum PrescriptionsSortFields {
  GenericName = "GenericName",
  BrandName = "BrandName",
  PrescriptionsCount = "PrescriptionsCount",
  PatientCount = "PatientCount",
  Rank = "Rank",
  Quartile = "Quartile",
  Percentage = "Percentage"
}

export enum DiagnosesFields {
  Description = "DRG_diagnoses.description_eng.keyword",
  DiagnosisCode = "DRG_diagnoses.diagnosisCode_eng",
  PercentOfClaims = "DRG_diagnoses.pctOfClaims",
  PercentOfUniqueClaims = "DRG_diagnoses.pctOfUniqueClaims",
  Count = "DRG_diagnoses.internalCount",
  InternalCount = "DRG_diagnoses.internalCount",
  UniqueCount = "DRG_diagnoses.internaUniqueCount",
  InternalUniqueCount = "DRG_diagnoses.internalUniqueCount",
  PercentOfClaims5Year = "DRG_diagnoses.pctOfClaims_5_year",
  PercentOfUniqueClaims5Year = "DRG_diagnoses.pctOfUniqueClaims_5_year",
  Count5Year = "DRG_diagnoses.internalCount_5_year",
  InternalUniqueCount5Year = "DRG_diagnoses.internalUniqueCount_5_year",
  UniqueCount5Year = "DRG_diagnoses.internalUniqueCount_5_year",
  InternalCount5Year = "DRG_diagnoses.internalCount_5_year",
  PercentOfClaims2Year = "DRG_diagnoses.pctOfClaims_2_year",
  PercentOfUniqueClaims2Year = "DRG_diagnoses.pctOfUniqueClaims_2_year",
  Count2Year = "DRG_diagnoses.internalCount_2_year",
  InternalCount2Year = "DRG_diagnoses.internalCount_2_year",
  UniqueCount2Year = "DRG_diagnoses.internalUniqueCount_2_year",
  InternalUniqueCount2Year = "DRG_diagnoses.internalUniqueCount_2_year",
  PercentOfClaims1Year = "DRG_diagnoses.pctOfClaims_1_year",
  PercentOfUniqueClaims1Year = "DRG_diagnoses.pctOfUniqueClaims_1_year",
  Count1Year = "DRG_diagnoses.internalCount_1_year",
  InternalCount1Year = "DRG_diagnoses.internalCount_1_year",
  UniqueCount1Year = "DRG_diagnoses.internalUniqueCount_1_year",
  InternalUniqueCount1Year = "DRG_diagnoses.internalUniqueCount_1_year",
  CodeScheme = "DRG_diagnoses.codeScheme.keyword"
}
export enum DiagnosesCareClustersFields {
  Description = "ccsr.description_eng",
  PercentOfUniqueClaims = "ccsr.pctOfCcsrPatients",
  PercentOfUniqueClaims5Year = "ccsr.pctOfCcsrPatients_5_year",
  PercentOfUniqueClaims2Year = "ccsr.pctOfCcsrPatients_2_year",
  PercentOfUniqueClaims1Year = "ccsr.pctOfCcsrPatients_1_year",
  HcpQuartile = "ccsr.hcp_quartile",
  HcpQuartile5Year = "ccsr.hcp_quartile_5_year",
  HcpQuartile2Year = "ccsr.hcp_quartile_2_year",
  HcpQuartile1Year = "ccsr.hcp_quartile_1_year",
  InternalCount = "ccsr.internalCount",
  InternalUniqueCount = "ccsr.internalUniqueCount",
  InternalCount5Year = "ccsr.internalCount_5_year",
  InternalUniqueCount5Year = "ccsr.internalUniqueCount_5_year",
  InternalCount2Year = "ccsr.internalCount_2_year",
  InternalUniqueCount2Year = "ccsr.internalUniqueCount_2_year",
  InternalCount1Year = "ccsr.internalCount_1_year",
  InternalUniqueCount1Year = "ccsr.internalUniqueCount_1_year"
}

export enum ProceduresCareClustersFields {
  Description = "ccsr_px.description_eng",
  PercentOfUniqueClaims = "ccsr_px.pctOfCcsrPatients",
  PercentOfUniqueClaims5Year = "ccsr_px.pctOfCcsrPatients_5_year",
  PercentOfUniqueClaims2Year = "ccsr_px.pctOfCcsrPatients_2_year",
  PercentOfUniqueClaims1Year = "ccsr_px.pctOfCcsrPatients_1_year",
  HcpQuartile = "ccsr_px.hcp_quartile",
  HcpQuartile5Year = "ccsr_px.hcp_quartile_5_year",
  HcpQuartile2Year = "ccsr_px.hcp_quartile_2_year",
  HcpQuartile1Year = "ccsr_px.hcp_quartile_1_year",
  InternalCount = "ccsr_px.internalCount",
  InternalUniqueCount = "ccsr_px.internalUniqueCount",
  InternalCount5Year = "ccsr_px.internalCount_5_year",
  InternalUniqueCount5Year = "ccsr_px.internalUniqueCount_5_year",
  InternalCount2Year = "ccsr_px.internalCount_2_year",
  InternalUniqueCount2Year = "ccsr_px.internalUniqueCount_2_year",
  InternalCount1Year = "ccsr_px.internalCount_1_year",
  InternalUniqueCount1Year = "ccsr_px.internalUniqueCount_1_year"
}

export enum ProceduresFields {
  Description = "DRG_procedures.description_eng.keyword",
  ProcedureCode = "DRG_procedures.procedureCode_eng.keyword",
  PercentOfClaims = "DRG_procedures.percentage",
  PercentOfUniqueClaims = "DRG_procedures.uniquePercentage",
  Count = "DRG_procedures.internalCount",
  InternalCount = "DRG_procedures.internalCount",
  UniqueCount = "DRG_procedures.internaUniqueCount",
  InternalUniqueCount = "DRG_procedures.internalUniqueCount",
  PercentOfClaims5Year = "DRG_procedures.percentage_5_year",
  PercentOfUniqueClaims5Year = "DRG_procedures.uniquePercentage_5_year",
  Count5Year = "DRG_procedures.internalCount_5_year",
  InternalCount5Year = "DRG_procedures.internalCount_5_year",
  InternalUniqueCount5Year = "DRG_procedures.internalUniqueCount_5_year",
  UniqueCount5Year = "DRG_procedures.internalUniqueCount_5_year",
  PercentOfClaims2Year = "DRG_procedures.percentage_2_year",
  PercentOfUniqueClaims2Year = "DRG_procedures.uniquePercentage_2_year",
  Count2Year = "DRG_procedures.internalCount_2_year",
  InternalCount2Year = "DRG_procedures.internalCount_2_year",
  UniqueCount2Year = "DRG_procedures.internalUniqueCount_2_year",
  InternalUniqueCount2Year = "DRG_procedures.internalUniqueCount_2_year",
  PercentOfClaims1Year = "DRG_procedures.percentage_1_year",
  PercentOfUniqueClaims1Year = "DRG_procedures.uniquePercentage_1_year",
  Count1Year = "DRG_procedures.internalCount_1_year",
  InternalCount1Year = "DRG_procedures.internalCount_1_year",
  UniqueCount1Year = "DRG_procedures.internalUniqueCount_1_year",
  InternalUniqueCount1Year = "DRG_procedures.internalUniqueCount_1_year",
  CodeScheme = "DRG_procedures.codeScheme"
}

export enum PrescriptionsFields {
  GenericName = "prescriptions.generic_name",
  BrandName = "prescriptions.brand_name",
  DrugClass = "prescriptions.drug_class",
  NumPrescriptions = "prescriptions.num_prescriptions",
  NumPrescriptions1Year = "prescriptions.num_prescriptions_1_year",
  NumPrescriptions2Year = "prescriptions.num_prescriptions_2_year",
  NumPrescriptions5Year = "prescriptions.num_prescriptions_5_year",
  TotalNumPrescriptions = "num_prescriptions",
  TotalNumPrescriptions1Year = "num_prescriptions_1_year",
  TotalNumPrescriptions2Year = "num_prescriptions_2_year",
  TotalNumPrescriptions5Year = "num_prescriptions_5_year",
  PatientCount = "prescriptions.patient_count",
  PatientCount1Year = "prescriptions.patient_count_1_year",
  PatientCount2Year = "prescriptions.patient_count_2_year",
  PatientCount5Year = "prescriptions.patient_count_5_year",
  TotalPatientCount = "prescriptions_patient_count",
  TotalPatientCount1Year = "prescriptions_patient_count_1_year",
  TotalPatientCount2Year = "prescriptions_patient_count_2_year",
  TotalPatientCount5Year = "prescriptions_patient_count_5_year",
  Rank = "prescriptions.rank_max_prescriptions",
  Rank5Year = "prescriptions.rank_5_year_prescriptions",
  Rank2Year = "prescriptions.rank_2_year_prescriptions",
  Rank1Year = "prescriptions.rank_1_year_prescriptions",
  Quartile = "prescriptions.hcp_quartile",
  Quartile1Year = "prescriptions.hcp_quartile_1_year",
  Quartile2Year = "prescriptions.hcp_quartile_2_year",
  Quartile5Year = "prescriptions.hcp_quartile_5_year",
  Percentage = "prescriptions.pctOfUniquePatients",
  Percentage1Year = "prescriptions.pctOfUniquePatients_1_year",
  Percentage2Year = "prescriptions.pctOfUniquePatients_2_year",
  Percentage5Year = "prescriptions.pctOfUniquePatients_5_year"
}

export enum ClaimsFilterRange {
  max = "max",
  fiveYear = "5 year",
  twoYear = "2 year",
  oneYear = "1 year"
}

export interface ClaimsFilterValue {
  filterRange: ClaimsFilterRange;
}

export type DiagnosesSortOptions = SortOptions<DiagnosesSortFields>;
export type DiagnosesCcsrSortOptions = SortOptions<DiagnosesCcsrSortFields>;
export type ProceduresSortOptions = SortOptions<ProcedureSortFields>;
export type ProceduresCcsrSortOptions = SortOptions<ProceduresCcsrSortFields>;
export type PrescriptionsSortOptions = SortOptions<PrescriptionsSortFields>;

export type DiagnosesSortOptionsDeprecated = SortOptions<DiagnosesFields>;
export type ProceduresSortOptionsDeprecated = SortOptions<ProceduresFields>;

export interface ClaimsTotals {
  diagnoses: number;
  procedures: number;
  prescriptions: number;
  prescriptionPatients: number;
  diagnosesPatients: number;
  proceduresPatients: number;
  diagnosesEncounters: number;
  proceduresEncounters: number;
}
export interface UniquePatientCountTotal {
  personId: string;
  diagnosesPatients: string;
  proceduresPatients: string;
}
export type ClaimsResourceOptions = {
  shouldUseUniquePatientCount: boolean;
  disableUniquePatientCountForOnlyProcedures?: boolean;
};

export type CcsrDiagnosesResponse = PaginationResponse<ClaimsCcsrDiagnosis>;
export type CcsrProceduresResponse = PaginationResponse<ClaimsCcsrProcedure>;

export interface ClaimsResource {
  isReady(): Promise<boolean>;

  /**
   * Search for diagnosis claims for a given person.
   *
   * @param personId The id of the person
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   *
   * @deprecated Use getFilteredDiagnoses instead
   */
  getDiagnoses(
    personId: string,
    terms?: string[],
    page?: PaginationOptions,
    sort?: DiagnosesSortOptionsDeprecated
  ): Promise<PaginationResponse<ClaimsDiagnosis>>;

  /**
   * Search for procedure claims for a given person.
   *
   * @param personId The id of the person
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   *
   * @deprecated Use getFilteredProcedures instead
   */
  getProcedures(
    personId: string,
    terms?: string[],
    page?: PaginationOptions,
    sort?: ProceduresSortOptionsDeprecated
  ): Promise<PaginationResponse<ClaimsProcedure>>;

  /**
   * Search for diagnosis claims for a given person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to procedures search
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   */
  getFilteredDiagnoses(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: DiagnosesSortOptions,
    options?: ClaimsResourceOptions,
    ccsrToExpand?: string
  ): Promise<PaginationResponse<ClaimsDiagnosis>>;

  /**
   * Search for diagnosis care clusters for a given person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to diagnoses search
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   */
  getFilteredDiagnosisCareClusters(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: DiagnosesCcsrSortOptions,
    options?: ClaimsResourceOptions
  ): Promise<CcsrDiagnosesResponse>;

  /**
   * Search for procedure care clusters for a given person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to procedures search
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   */
  getFilteredProcedureCareClusters(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: ProceduresCcsrSortOptions,
    options?: ClaimsResourceOptions
  ): Promise<CcsrProceduresResponse>;

  /**
   * Search for procedure claims for a given person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to procedures search
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   */
  getFilteredProcedures(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: ProceduresSortOptions,
    options?: ClaimsResourceOptions
  ): Promise<PaginationResponse<ClaimsProcedure>>;

  /**
   * Search for prescriptions claims for a given person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to prescriptions search
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   */
  getFilteredPrescriptions(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: PrescriptionsSortOptions
  ): Promise<PaginationResponse<ClaimsPrescription>>;
  /**
   * Get the diagnosis and procedure totals for a person.
   *
   * @param personId The id of the person
   *
   * @deprecated Use getFilteredClaimsTotals instead
   */
  getClaimsTotals(personId: string): Promise<ClaimsTotals>;

  /**
   * Get the unique patient counts for diagnoses and procedures for each person ID.
   *
   * @param personIds The ids of the HCPs
   */
  getUniquePatientCountTotals(
    personIds: string[]
  ): Promise<UniquePatientCountTotal[]>;

  /**
   * Get the diagnosis and procedure totals for a person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to procedures search
   */
  getFilteredClaimsTotals(
    personId: string,
    filters: ClaimsFilterValue,
    options?: ClaimsResourceOptions
  ): Promise<ClaimsTotals>;
}
