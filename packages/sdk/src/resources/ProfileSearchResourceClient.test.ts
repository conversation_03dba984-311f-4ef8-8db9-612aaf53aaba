import { UserToken } from "@h1nyc/account-sdk";
import { RpcClient } from "@h1nyc/systems-rpc";
import { faker } from "@faker-js/faker";
import { ClaimsType, ProfileSortedIdsParams, ProfileTermsAndFilters } from ".";
import {
  ProfileFilterValue,
  ProfileFilterValueType
} from "./ProfileSearchResource";
import { ProfileSearchResourceClient } from "./ProfileSearchResourceClient";

let client: ProfileSearchResourceClient;
let rpcClient: RpcClient;

beforeAll(() => {
  client = new ProfileSearchResourceClient("testing");
  rpcClient = client.rpcClient;
});

describe("getPublicationIds", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const personId = faker.random.alphaNumeric();
    const terms = [faker.random.alpha(), faker.random.alpha()];
    const filterValues = [
      {
        type: ProfileFilterValueType.PublicationType,
        value: faker.random.alphaNumeric()
      }
    ] as ProfileFilterValue[];
    const usersLanguage = "japanese";

    client.getPublicationIds(personId, terms, filterValues, usersLanguage);

    expect(sendRequestSpy).toHaveBeenCalledWith("getPublicationIds", [
      personId,
      terms,
      filterValues,
      usersLanguage
    ]);
  });
});

describe("getPublicationIdsForPeople", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const peopleIds = [faker.random.alphaNumeric()];
    const terms = [faker.random.alpha(), faker.random.alpha()];
    const filterValues = [
      {
        type: ProfileFilterValueType.PublicationType,
        value: faker.random.alphaNumeric()
      }
    ] as ProfileFilterValue[];
    const usersLanguage = "chinese";

    client.getPublicationIdsForPeople(
      peopleIds,
      terms,
      filterValues,
      usersLanguage
    );

    expect(sendRequestSpy).toHaveBeenCalledWith("getPublicationIdsForPeople", [
      peopleIds,
      terms,
      filterValues,
      usersLanguage
    ]);
  });
});

describe("getPublicationMetrics", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const personId = faker.random.alphaNumeric();
    const terms = [faker.random.alpha(), faker.random.alpha()];
    const filterValues = [
      {
        type: ProfileFilterValueType.PublicationType,
        value: faker.random.alphaNumeric()
      }
    ] as ProfileFilterValue[];
    const usersLanguage = "english";
    const projectId = faker.datatype.uuid();
    client.getPublicationMetrics(
      personId,
      projectId,
      terms,
      filterValues,
      usersLanguage
    );

    expect(sendRequestSpy).toHaveBeenCalledWith("getPublicationMetrics", [
      personId,
      projectId,
      terms,
      filterValues,
      usersLanguage
    ]);
  });
});

describe("profileSearch", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const personId = faker.random.alphaNumeric();
    const queries = [faker.random.alpha(), faker.random.alpha()];
    const projectId = "1";

    client.profileSearch(personId, queries, projectId);

    expect(sendRequestSpy).toHaveBeenCalledWith("profileSearch", [
      personId,
      queries,
      projectId
    ]);
  });
});

describe("getPublicationMetricsForPeople", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const peopleIds = [faker.random.alphaNumeric()];
    const terms = [faker.random.alpha(), faker.random.alpha()];
    const filterValues = [
      {
        type: ProfileFilterValueType.PublicationType,
        value: faker.random.alphaNumeric()
      }
    ] as ProfileFilterValue[];

    const projectId = faker.datatype.uuid();

    client.getPublicationMetricsForPeople(
      peopleIds,
      projectId,
      terms,
      filterValues
    );

    expect(sendRequestSpy).toHaveBeenCalledWith(
      "getPublicationMetricsForPeople",
      [peopleIds, projectId, terms, filterValues, undefined]
    );
  });
});

describe("getTrialIdsForPeople", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const peopleIds = [faker.random.alphaNumeric()];
    const terms = [faker.random.alpha(), faker.random.alpha()];
    const filterValues = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: faker.date.past(5)
      }
    ] as ProfileFilterValue[];

    client.getTrialIdsForPeople(peopleIds, terms, filterValues);

    expect(sendRequestSpy).toHaveBeenCalledWith("getTrialIdsForPeople", [
      peopleIds,
      terms,
      filterValues
    ]);
  });
});

describe("getTrialMetricsForPeople", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const peopleIds = [faker.random.alphaNumeric()];
    const terms = [faker.random.alpha(), faker.random.alpha()];
    const filterValues = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: faker.date.past(5)
      }
    ] as ProfileFilterValue[];

    const projectId = faker.datatype.uuid();
    client.getTrialMetricsForPeople(peopleIds, projectId, terms, filterValues);

    expect(sendRequestSpy).toHaveBeenCalledWith("getTrialMetricsForPeople", [
      peopleIds,
      projectId,
      terms,
      filterValues
    ]);
  });
});

describe("getCongressIdsForPeople", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const peopleIds = [faker.random.alphaNumeric()];
    const terms = [faker.random.alpha(), faker.random.alpha()];
    const filterValues = [
      {
        type: ProfileFilterValueType.CongressDate,
        value: faker.date.past(5)
      }
    ] as ProfileFilterValue[];

    client.getCongressIdsForPeople(peopleIds, terms, filterValues);

    expect(sendRequestSpy).toHaveBeenCalledWith("getCongressIdsForPeople", [
      peopleIds,
      terms,
      filterValues
    ]);
  });
});

describe("getCongressMetricsForPeople", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const peopleIds = [faker.random.alphaNumeric()];
    const terms = [faker.random.alpha(), faker.random.alpha()];
    const filterValues = [
      {
        type: ProfileFilterValueType.CongressDate,
        value: faker.date.past(5)
      }
    ] as ProfileFilterValue[];

    const projectId = faker.datatype.uuid();
    client.getCongressMetricsForPeople(
      peopleIds,
      projectId,
      terms,
      filterValues
    );

    expect(sendRequestSpy).toHaveBeenCalledWith("getCongressMetricsForPeople", [
      peopleIds,
      projectId,
      terms,
      filterValues
    ]);
  });
});

describe("getAllMetricsForPeople", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const peopleIds = [faker.random.alphaNumeric()];
    const user = {
      userId: faker.datatype.uuid(),
      projectFeatures: {}
    } as UserToken;
    const termsAndFilters = {
      terms: [faker.random.alpha(), faker.random.alpha()],
      filters: [
        {
          type: ProfileFilterValueType.CongressDate,
          value: faker.date.past(5)
        }
      ]
    } as ProfileTermsAndFilters;
    const languageCode = "cmn";

    client.getAllMetricsForPeople(
      peopleIds,
      user,
      termsAndFilters,
      languageCode
    );

    expect(sendRequestSpy).toHaveBeenCalledWith("getAllMetricsForPeople", [
      peopleIds,
      user,
      termsAndFilters,
      "cmn"
    ]);
  });
});

describe("getMetricSummaryForPeople", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const peopleIds = [faker.random.alphaNumeric()];
    const claimsType = ClaimsType.DRG;
    const termsAndFilters = {
      terms: [faker.random.alpha(), faker.random.alpha()],
      filters: [
        {
          type: ProfileFilterValueType.CongressDate,
          value: faker.date.past(5)
        }
      ]
    } as ProfileTermsAndFilters;
    const languageCode = "cmn";
    const projectId = faker.datatype.uuid();

    const opts = {
      peopleIds,
      projectId,
      claimsType,
      termsAndFilters,
      languageCode
    };

    client.getMetricSummaryForPeople(opts);

    expect(sendRequestSpy).toHaveBeenCalledWith("getMetricSummaryForPeople", [
      opts
    ]);
  });
});

describe("getSortedIdsForPeople", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const peopleIds = [faker.random.alphaNumeric()];
    const searchParams = {
      size: 1,
      termsAndFilters: {
        terms: [faker.random.alpha(), faker.random.alpha()],
        filters: [
          {
            type: ProfileFilterValueType.CongressDate,
            value: faker.date.past(5)
          }
        ]
      }
    } as ProfileSortedIdsParams;

    client.getSortedIdsForPeople(peopleIds, searchParams);

    expect(sendRequestSpy).toHaveBeenCalledWith("getSortedIdsForPeople", [
      peopleIds,
      searchParams,
      undefined
    ]);
  });
});

describe("getTweetsForPerson", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const input = {
      personId: faker.datatype.number({ min: 1 }).toString(),
      projectId: faker.datatype.number({ min: 1 }).toString(),
      suppliedTerms: [faker.random.alpha(), faker.random.alpha()],
      userLanguage: "japanese",
      page: {
        limit: faker.datatype.number({ min: 1, max: 10 }),
        offset: 0
      }
    };

    client.getTweetsForPerson(input);

    expect(sendRequestSpy).toHaveBeenCalledWith("getTweetsForPerson", [input]);
  });
});

describe("getLocationsForPerson", () => {
  it("should call sendRequest with args", async () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const personId = faker.datatype.number({ min: 1 }).toString();
    const projectId = faker.datatype.number({ min: 1 }).toString();

    await client.getLocationsForPerson(personId, projectId);

    expect(sendRequestSpy).toHaveBeenCalledWith("getLocationsForPerson", [
      personId,
      projectId
    ]);
  });
});
