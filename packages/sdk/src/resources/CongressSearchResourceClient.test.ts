import { faker } from "@faker-js/faker";
import { CongressSearchResourceClient } from "./CongressSearchResourceClient";
import {
  CongressSearchResult,
  CongressSearchInput,
  CongressSearchResponse,
  CongressSearchFilterAutocompleteInput
} from "../interfaces";

function createFakeCongress(
  overrides: Partial<CongressSearchResult> = {}
): CongressSearchResult {
  const congress: CongressSearchResult = {
    id: faker.datatype.number().toString(),
    name: faker.datatype.string(),
    seriesId: faker.datatype.number().toString(),
    seriesName: faker.datatype.string(),
    type: faker.datatype.string(),
    addresses: [
      {
        street1: faker.address.streetAddress(),
        street2: faker.address.secondaryAddress(),
        country: faker.address.country(),
        city: faker.address.city(),
        region: faker.address.state(),
        regionCode: faker.address.stateAbbr(),
        postalCode: faker.address.zipCode()
      }
    ],
    speakers: [
      {
        id: faker.datatype.number().toString(),
        name: faker.name.fullName(),
        role: faker.name.jobTitle()
      }
    ],
    translations: [
      {
        name: faker.datatype.string(),
        description: faker.datatype.string(),
        society: faker.datatype.string(),
        languageCode: faker.datatype.string()
      }
    ],
    isFollowing: faker.datatype.boolean(),
    startDate: faker.date.past().getSeconds(),
    endDate: faker.date.future().getSeconds()
  };

  return { ...congress, ...overrides };
}

describe("CongressSearchResourceClient", () => {
  describe("isReady", () => {
    it("should return true", async () => {
      const congressSearchResourceClient = new CongressSearchResourceClient(
        faker.datatype.string()
      );

      expect(congressSearchResourceClient.isReady()).resolves.toEqual(true);
    });
  });

  describe("search", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient = new CongressSearchResourceClient(
        faker.datatype.string()
      );

      const expectedResponse: CongressSearchResponse = {
        total: faker.datatype.number(),
        congresses: [createFakeCongress(), createFakeCongress()]
      };

      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input: CongressSearchInput = {
        query: faker.datatype.string(),
        paging: {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        },
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const actualResponse = await congressSearchResourceClient.search(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("search", [input]);
    });
  });

  describe("autocompleteCountries", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient = new CongressSearchResourceClient(
        faker.datatype.string()
      );

      const expectedResponse = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input: CongressSearchFilterAutocompleteInput = {
        globalQuery: faker.datatype.string(),
        filterQuery: faker.datatype.string(),
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const actualResponse =
        await congressSearchResourceClient.autocompleteCountries(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompleteCountries", [
        input
      ]);
    });
  });

  describe("autocompleteRegions", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient = new CongressSearchResourceClient(
        faker.datatype.string()
      );

      const expectedResponse = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input: CongressSearchFilterAutocompleteInput = {
        globalQuery: faker.datatype.string(),
        filterQuery: faker.datatype.string(),
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const actualResponse =
        await congressSearchResourceClient.autocompleteRegions(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompleteRegions", [
        input
      ]);
    });
  });

  describe("autocompleteCities", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient = new CongressSearchResourceClient(
        faker.datatype.string()
      );

      const expectedResponse = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input: CongressSearchFilterAutocompleteInput = {
        globalQuery: faker.datatype.string(),
        filterQuery: faker.datatype.string(),
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const actualResponse =
        await congressSearchResourceClient.autocompleteCities(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompleteCities", [
        input
      ]);
    });
  });

  describe("autocompletePostalCodes", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient = new CongressSearchResourceClient(
        faker.datatype.string()
      );

      const expectedResponse = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input: CongressSearchFilterAutocompleteInput = {
        globalQuery: faker.datatype.string(),
        filterQuery: faker.datatype.string(),
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const actualResponse =
        await congressSearchResourceClient.autocompletePostalCodes(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompletePostalCodes", [
        input
      ]);
    });
  });

  describe("autocompleteSeriesNames", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient = new CongressSearchResourceClient(
        faker.datatype.string()
      );

      const expectedResponse = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input: CongressSearchFilterAutocompleteInput = {
        globalQuery: faker.datatype.string(),
        filterQuery: faker.datatype.string(),
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const actualResponse =
        await congressSearchResourceClient.autocompleteSeriesNames(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompleteSeriesNames", [
        input
      ]);
    });
  });

  describe("autocompleteSocietyNames", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient = new CongressSearchResourceClient(
        faker.datatype.string()
      );

      const expectedResponse = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input: CongressSearchFilterAutocompleteInput = {
        globalQuery: faker.datatype.string(),
        filterQuery: faker.datatype.string(),
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const actualResponse =
        await congressSearchResourceClient.autocompleteSocietyNames(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompleteSocietyNames", [
        input
      ]);
    });
  });

  describe("autocompleteCongressTypes", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient = new CongressSearchResourceClient(
        faker.datatype.string()
      );

      const expectedResponse = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input: CongressSearchFilterAutocompleteInput = {
        globalQuery: faker.datatype.string(),
        filterQuery: faker.datatype.string(),
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const actualResponse =
        await congressSearchResourceClient.autocompleteCongressTypes(input);

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith("autocompleteCongressTypes", [
        input
      ]);
    });
  });

  describe("congressCountForIndications", () => {
    it("should return the results of sendRequest", async () => {
      const congressSearchResourceClient = new CongressSearchResourceClient(
        faker.datatype.string()
      );

      const expectedResponse = {
        [faker.datatype.string()]: faker.datatype.number(),
        [faker.datatype.string()]: faker.datatype.number()
      };
      const sendRequestSpy = jest
        .spyOn(congressSearchResourceClient.rpcClient, "sendRequest")
        .mockResolvedValue(expectedResponse);

      const input: CongressSearchInput = {
        query: faker.datatype.string(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const actualResponse =
        await congressSearchResourceClient.congressCountForIndications(
          input,
          Object.keys(expectedResponse)
        );

      expect(actualResponse).toEqual(expectedResponse);
      expect(sendRequestSpy).toHaveBeenCalledWith(
        "congressCountForIndications",
        [input, Object.keys(expectedResponse)]
      );
    });
  });
});
