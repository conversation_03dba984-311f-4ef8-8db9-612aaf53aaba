import { RpcResourceClient } from "@h1nyc/systems-rpc";
import { RedisOptions } from "ioredis";
import { RPC_NAMESPACE_NAME_SUGGEST } from "../constants";
import { PersonSearchResponse } from "../interfaces";
import { NameSuggestResource } from "./NameSuggestResource";
import { KolNameSuggestionInput } from "../types";
export class NameSuggestResourceClient
  extends RpcResourceClient
  implements NameSuggestResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_NAME_SUGGEST, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", []);
  }

  async nameSuggestSearch(
    input: KolNameSuggestionInput,
    peopleIndex: string,
    userId: string,
    projectId: string,
    multiLangEnabled?: boolean,
    translateTaiwan?: boolean
  ): Promise<any> {
    return this.rpcClient.sendRequest<PersonSearchResponse>(
      "nameSuggestSearch",
      [input, peopleIndex, userId, projectId, multiLangEnabled, translateTaiwan]
    );
  }
}
