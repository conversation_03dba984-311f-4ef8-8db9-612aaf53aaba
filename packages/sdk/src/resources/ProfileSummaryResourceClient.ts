import { RpcResourceClient } from "@h1nyc/systems-rpc";
import { RedisOptions } from "ioredis";
import { RPC_NAMESPACE_PROFILE_SUMMARY } from "../constants";
import {
  ProfileSummaryMetrics,
  ProfileSummaryResource,
  ProfileTopMeshTerms
} from "./ProfileSummaryResource";

export class ProfileSummaryResourceClient
  extends RpcResourceClient
  implements ProfileSummaryResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_PROFILE_SUMMARY, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", []);
  }

  getMetricsForPeople(
    peopleIds: string[],
    projectId?: string
  ): Promise<ProfileSummaryMetrics[]> {
    return this.rpcClient.sendRequest<ProfileSummaryMetrics[]>(
      "getMetricsForPeople",
      [peopleIds, projectId]
    );
  }

  getTopMeshTermsForPeople(
    peopleIds: string[],
    size: number,
    startDate: Date,
    endDate: Date,
    projectId?: string
  ): Promise<ProfileTopMeshTerms[]> {
    return this.rpcClient.sendRequest<ProfileTopMeshTerms[]>(
      "getTopMeshTermsForPeople",
      [peopleIds, size, startDate, endDate, projectId]
    );
  }
}
