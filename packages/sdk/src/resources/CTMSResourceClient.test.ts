import { Rpc<PERSON>lient } from "@h1nyc/systems-rpc";
import { faker } from "@faker-js/faker";
import { CTMSResourceClient } from "./CTMSResourceClient";

let client: CTMSResourceClient;
let rpcClient: RpcClient;

beforeAll(() => {
  client = new CTMSResourceClient("testing");
  rpcClient = client.rpcClient;
});

describe("isReady", () => {
  it("should return true", async () => {
    const ctmsResourceClient = new CTMSResourceClient(faker.datatype.string());

    expect(ctmsResourceClient.isReady()).resolves.toEqual(true);
  });
});

describe("ctmsFacilityAggregation", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const institutionId = faker.random.alphaNumeric();
    const input = {
      institutionId,
      filters: {
        phase: [faker.datatype.string(), faker.datatype.string()],
        indication: [faker.datatype.string()],
        therapeutic_area: [faker.datatype.string(), faker.datatype.string()]
      }
    };

    client.ctmsFacilityAggregation(input);

    expect(sendRequestSpy).toHaveBeenCalledWith("ctmsFacilityAggregation", [
      input
    ]);
  });
});
describe("ctmsFacilityInitialOptions", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const institutionId = faker.random.alphaNumeric();

    client.ctmsFacilityInitialFilterOptions(institutionId);

    expect(sendRequestSpy).toHaveBeenCalledWith(
      "ctmsFacilityInitialFilterOptions",
      [institutionId]
    );
  });
});
describe("getAllCTMSFacilityIds", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    client.getAllCTMSFacilityIds();

    expect(sendRequestSpy).toHaveBeenCalledWith("getAllCTMSFacilityIds", []);
  });
});

describe("getAllCTMSPersonIds", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    client.getAllCTMSPersonIds();

    expect(sendRequestSpy).toHaveBeenCalledWith("getAllCTMSPersonIds", []);
  });
});
describe("ctmsInvestigatorAggregation", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const personId = faker.random.alphaNumeric();
    const input = {
      personId,
      filters: {
        phase: [faker.datatype.string(), faker.datatype.string()],
        indications: [faker.datatype.string()],
        therapeutic_area: [faker.datatype.string(), faker.datatype.string()]
      }
    };
    client.ctmsInvestigatorAggregation(input);

    expect(sendRequestSpy).toHaveBeenCalledWith("ctmsInvestigatorAggregation", [
      input
    ]);
  });
});
describe("ctmsInvestigatorInitialOptions", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const personId = faker.random.alphaNumeric();

    client.ctmsInvestigatorInitialFilterOptions(personId);

    expect(sendRequestSpy).toHaveBeenCalledWith(
      "ctmsInvestigatorInitialFilterOptions",
      [personId]
    );
  });
});

describe("personHasCTMSData", () => {
  it("should call sendRequest with args", () => {
    const sendRequestSpy = jest.spyOn(rpcClient, "sendRequest");
    const personId = faker.random.alphaNumeric();

    client.personHasCTMSData(personId);

    expect(sendRequestSpy).toHaveBeenCalledWith("personHasCTMSData", [
      personId
    ]);
  });
});
