import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { CollaboratorsResource } from ".";
import { RPC_NAMESPACE_COLLABORATORS } from "../constants";
import {
  CollaboratorsResponse,
  HasCollaboratorsResponse
} from "../interfaces/Collaborators";

export class CollaboratorsResourceClient
  extends RpcResourceClient
  implements CollaboratorsResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_COLLABORATORS, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return true;
  }

  hasCollaborators(
    personId: string,
    projectId: string
  ): Promise<HasCollaboratorsResponse> {
    return this.rpcClient.sendRequest<HasCollaboratorsResponse>(
      "hasCollaborators",
      [personId, projectId]
    );
  }

  /**
   * Search for the number of collaborators for a given person and project
   *
   * @param personId The id of the person
   * @param projectId Id of the project
   */
  findCollaborators(
    personId: string,
    dateRange: {
      min?: number | null;
      max?: number | null;
    },
    page: {
      limit: number;
      offset: number;
    },
    terms?: string[],
    projectId?: string
  ): Promise<CollaboratorsResponse> {
    return this.rpcClient.sendRequest<CollaboratorsResponse>(
      "findCollaborators",
      [personId, dateRange, page, terms, projectId]
    );
  }
}
