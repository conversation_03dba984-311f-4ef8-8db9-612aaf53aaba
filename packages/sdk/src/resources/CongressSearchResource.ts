import { Dictionary } from "lodash";
import {
  CongressSearchFilterAggregation,
  CongressSearchFilterAutocompleteInput,
  CongressSearchInput,
  CongressSearchLocationFilterAggregation,
  CongressSearchResponse
} from "../interfaces";

export interface CongressSearchResource {
  isReady(): Promise<boolean>;

  search(input: CongressSearchInput): Promise<CongressSearchResponse>;

  autocompleteCountries(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]>;

  autocompleteRegions(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]>;

  autocompleteCities(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]>;

  autocompletePostalCodes(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchLocationFilterAggregation[]>;

  autocompleteSeriesNames(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]>;

  autocompleteSocietyNames(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]>;

  autocompleteCongressTypes(
    input: CongressSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]>;

  congressCountForIndications(
    input: CongressSearchInput,
    indications: string[]
  ): Promise<Dictionary<number>>;
}
