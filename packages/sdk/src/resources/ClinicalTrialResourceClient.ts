import { RpcResourceClient } from "@h1nyc/systems-rpc";
import { RedisOptions } from "ioredis";
import { RPC_NAMESPACE_CLINICAL_TRIALS } from "../constants";
import {
  ClinicalTrialsSearchResponse,
  ClinicalTrialInput,
  ClinicalTrialDocument,
  ClinicalTrialDocumentV2,
  ClinicalTrialDocumentV3,
  ClinicalTrialDocumentV4
} from "../interfaces";
import { ClinicalTrialResource } from "./ClinicalTrialResource";

/**
 * @deprecated Please use ClinicalTrialSearchResourceClient instead.
 */
export class ClinicalTrialResourceClient
  extends RpcResourceClient
  implements ClinicalTrialResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_CLINICAL_TRIALS, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", []);
  }

  async searchTrials(
    input: ClinicalTrialInput
  ): Promise<ClinicalTrialsSearchResponse> {
    return this.rpcClient.sendRequest<ClinicalTrialsSearchResponse>(
      "searchTrials",
      [input]
    );
  }

  async trialDocument(trialId: string): Promise<ClinicalTrialDocument | null> {
    console.log(`calling RPC with trial ${trialId}`);
    return this.rpcClient.sendRequest<ClinicalTrialDocument | null>(
      "trialDocument",
      [trialId]
    );
  }

  async trialDocumentV2(
    nctId: string
  ): Promise<ClinicalTrialDocumentV2 | null> {
    return this.rpcClient.sendRequest<ClinicalTrialDocumentV2 | null>(
      "trialDocumentV2",
      [nctId]
    );
  }

  async trialDocumentV3(
    nctId: string
  ): Promise<ClinicalTrialDocumentV3 | null> {
    return this.rpcClient.sendRequest<ClinicalTrialDocumentV3 | null>(
      "trialDocumentV3",
      [nctId]
    );
  }

  async trialDocumentV4(
    nctId: string
  ): Promise<ClinicalTrialDocumentV4 | null> {
    return this.rpcClient.sendRequest<ClinicalTrialDocumentV4 | null>(
      "trialDocumentV4",
      [nctId]
    );
  }
}
