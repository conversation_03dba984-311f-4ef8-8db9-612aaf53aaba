import {
  IndicationsGetSubTreesInput,
  SearchIndicationsByQueryInput,
  SearchIndicationTreesByQueryInput,
  SearchRootIndicationsInput
} from "../interfaces";
import { IndicationNode } from "../interfaces/IndicationsTree";

export interface IndicationsTreeSearchResource {
  searchRootIndications(
    input: SearchRootIndicationsInput
  ): Promise<IndicationNode[]>;
  searchByQuery(
    input: SearchIndicationsByQueryInput
  ): Promise<IndicationNode[]>;
  getSubTrees(input: IndicationsGetSubTreesInput): Promise<IndicationNode[]>;
  searchIndicationTreesByQuery(
    input: SearchIndicationTreesByQueryInput
  ): Promise<IndicationNode[]>;
}
