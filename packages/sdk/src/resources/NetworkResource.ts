import {
  NetworkResponse,
  NetworkRequest,
  NetworkCollaborator,
  NetworkCollaboratorRequest,
  NetworkFilterAutocompleteRequest,
  NetworkFilterAggregation,
  BulkNetworkRequest,
  BulkNetworkResponse
} from "../interfaces/Network";

export interface NetworkResource {
  isReady(): Promise<boolean>;

  findNetwork(request: NetworkRequest): Promise<NetworkResponse>;

  findBulkNetwork(request: BulkNetworkRequest): Promise<BulkNetworkResponse[]>;

  findCollaborator(
    request: NetworkCollaboratorRequest
  ): Promise<NetworkCollaborator>;

  filterAutocomplete(
    request: NetworkFilterAutocompleteRequest
  ): Promise<Array<NetworkFilterAggregation>>;
}
