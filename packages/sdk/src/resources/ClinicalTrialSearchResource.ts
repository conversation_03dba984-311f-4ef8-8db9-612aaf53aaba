import { Entity } from "@h1nyc/account-sdk";
import {
  ClinicalTrialDocument,
  ClinicalTrialDocumentV2,
  ClinicalTrialDocumentV3,
  ClinicalTrialDocumentV4,
  ClinicalTrialDocumentWithCTMS,
  ClinicalTrialFilterAutocompleteInput,
  ClinicalTrialInput,
  ClinicalTrialsSearchResponse,
  IndicationsType,
  KeywordFilterAggregation,
  ClinicalTrialHistogramInput,
  ClinicalTrialHistogramResponse,
  ClinicalTrialCountryBreakdownInput,
  ClinicalTrialCountryBreakdownResponse,
  ClinicalTrialRegionBreakdownInput,
  ClinicalTrialRegionBreakdownResponse
} from "../interfaces";

export interface ClinicalTrialSearchResource {
  /**
   * Ready check
   */
  isReady(): Promise<boolean>;

  searchTrials(
    input: ClinicalTrialInput
  ): Promise<ClinicalTrialsSearchResponse>;

  trialCountForIndications(
    searchInput: Readonly<ClinicalTrialInput>,
    indications: string[],
    type: IndicationsType
  ): Promise<_.Dictionary<number>>;

  searchBulkTrials(input: ClinicalTrialInput): Promise<Array<Entity>>;

  autocompleteForTrialFilters(
    input: ClinicalTrialFilterAutocompleteInput
  ): Promise<Array<KeywordFilterAggregation>>;

  trialDocument(trialId: string): Promise<ClinicalTrialDocument | null>;

  trialDocumentV2(nctId: string): Promise<ClinicalTrialDocumentV2 | null>;

  trialDocumentV3(nctId: string): Promise<ClinicalTrialDocumentV3 | null>;

  trialDocumentV4(nctId: string): Promise<ClinicalTrialDocumentV4 | null>;

  trialDocumentWithCTMS(
    trialId: string
  ): Promise<ClinicalTrialDocumentWithCTMS | null>;

  /**
   * Generate histogram data for trial metrics
   */
  generateTrialHistogram(
    input: ClinicalTrialHistogramInput
  ): Promise<ClinicalTrialHistogramResponse>;

  /**
   * Generate country breakdown data for trial metrics
   */
  generateTrialCountryBreakdown(
    input: ClinicalTrialCountryBreakdownInput
  ): Promise<ClinicalTrialCountryBreakdownResponse>;

  /**
   * Generate region breakdown data for trial metrics within a specific country
   */
  generateTrialRegionBreakdown(
    input: ClinicalTrialRegionBreakdownInput
  ): Promise<ClinicalTrialRegionBreakdownResponse>;
}
