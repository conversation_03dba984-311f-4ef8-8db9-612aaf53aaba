import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { ClaimsResource, DiagnosesSortOptions } from ".";
import { RPC_NAMESPACE_CLAIMS_SEARCH } from "../constants";
import {
  ClaimsDiagnosis,
  ClaimsPrescription,
  ClaimsProcedure
} from "../interfaces/Claims";
import {
  PaginationOptions,
  PaginationResponse
} from "../interfaces/pagination";
import {
  CcsrDiagnosesResponse,
  CcsrProceduresResponse,
  ClaimsFilterValue,
  ClaimsResourceOptions,
  ClaimsTotals,
  DiagnosesCcsrSortOptions,
  DiagnosesSortOptionsDeprecated,
  PrescriptionsSortOptions,
  ProceduresCcsrSortOptions,
  ProceduresSortOptions,
  ProceduresSortOptionsDeprecated,
  UniquePatientCountTotal
} from "./ClaimsResource";

export class ClaimsResourceClient
  extends RpcResourceClient
  implements ClaimsResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_CLAIMS_SEARCH, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return true;
  }

  /**
   * Search for diagnosis claims for a given person.
   *
   * @param personId The id of the person
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   *
   * @deprecated Use getFilteredDiagnoses instead
   */
  getDiagnoses(
    personId: string,
    terms?: string[],
    page?: PaginationOptions,
    sort?: DiagnosesSortOptionsDeprecated
  ): Promise<PaginationResponse<ClaimsDiagnosis>> {
    return this.rpcClient.sendRequest<PaginationResponse<ClaimsDiagnosis>>(
      "getDiagnoses",
      [personId, terms, page, sort]
    );
  }

  /**
   * Search for procedure claims for a given person.
   *
   * @param personId The id of the person
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   *
   * @deprecated Use getFilteredProcedures instead
   */
  getProcedures(
    personId: string,
    terms?: string[],
    page?: PaginationOptions,
    sort?: ProceduresSortOptionsDeprecated
  ): Promise<PaginationResponse<ClaimsProcedure>> {
    return this.rpcClient.sendRequest<PaginationResponse<ClaimsProcedure>>(
      "getProcedures",
      [personId, terms, page, sort]
    );
  }

  /**
   * Search for diagnosis claims for a given person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to procedures search
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   */
  getFilteredDiagnoses(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: DiagnosesSortOptions,
    options?: ClaimsResourceOptions,
    ccsrToExpand?: string
  ): Promise<PaginationResponse<ClaimsDiagnosis>> {
    return this.rpcClient.sendRequest<PaginationResponse<ClaimsDiagnosis>>(
      "getFilteredDiagnoses",
      [personId, filters, terms, page, sort, options, ccsrToExpand]
    );
  }

  /**
   * Search for diagnosis care cluster for a given person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to diagnoses search
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   */
  getFilteredDiagnosisCareClusters(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: DiagnosesCcsrSortOptions,
    options?: ClaimsResourceOptions
  ): Promise<CcsrDiagnosesResponse> {
    return this.rpcClient.sendRequest<CcsrDiagnosesResponse>(
      "getFilteredDiagnosisCareClusters",
      [personId, filters, terms, page, sort, options]
    );
  }

  /**
   * Get the unique patient counts for diagnoses and procedures for each person ID.
   *
   * @param personIds The ids of the HCPs
   */
  getUniquePatientCountTotals(
    personIds: string[]
  ): Promise<UniquePatientCountTotal[]> {
    return this.rpcClient.sendRequest<UniquePatientCountTotal[]>(
      "getUniquePatientCountTotals",
      [personIds]
    );
  }

  /**
   * Search for procedure care clusters for a given person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to procedures search
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   */
  getFilteredProcedureCareClusters(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: ProceduresCcsrSortOptions,
    options?: ClaimsResourceOptions
  ): Promise<CcsrProceduresResponse> {
    return this.rpcClient.sendRequest<CcsrProceduresResponse>(
      "getFilteredProcedureCareClusters",
      [personId, filters, terms, page, sort, options]
    );
  }

  /**
   * Search for procedure claims for a given person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to procedures search
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   */
  getFilteredProcedures(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: ProceduresSortOptions,
    options?: ClaimsResourceOptions
  ): Promise<PaginationResponse<ClaimsProcedure>> {
    return this.rpcClient.sendRequest<PaginationResponse<ClaimsProcedure>>(
      "getFilteredProcedures",
      [personId, filters, terms, page, sort, options]
    );
  }

  /**
   * Search for procedure claims for a given person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to procedures search
   * @param terms Search terms
   * @param page Pagination options
   * @param sort Sort options
   */
  getFilteredPrescriptions(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: PrescriptionsSortOptions
  ): Promise<PaginationResponse<ClaimsPrescription>> {
    return this.rpcClient.sendRequest<PaginationResponse<ClaimsPrescription>>(
      "getFilteredPrescriptions",
      [personId, filters, terms, page, sort]
    );
  }

  /**
   * Get the diagnosis and procedure totals for a person.
   *
   * @param personId The id of the person
   *
   * @deprecated Use getFilteredClaimsTotals instead
   */
  getClaimsTotals(personId: string): Promise<ClaimsTotals> {
    return this.rpcClient.sendRequest<ClaimsTotals>("getClaimsTotals", [
      personId
    ]);
  }

  /**
   * Get the diagnosis and procedure totals for a person.
   *
   * @param personId The id of the person
   * @param filters The filters to apply to procedures search
   */
  getFilteredClaimsTotals(
    personId: string,
    filters: ClaimsFilterValue,
    options?: ClaimsResourceOptions
  ): Promise<ClaimsTotals> {
    return this.rpcClient.sendRequest<ClaimsTotals>("getFilteredClaimsTotals", [
      personId,
      filters,
      options
    ]);
  }
}
