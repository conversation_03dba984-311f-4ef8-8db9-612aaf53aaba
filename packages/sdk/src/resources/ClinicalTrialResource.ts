import {
  ClinicalTrialDocument,
  ClinicalTrialDocumentV2,
  ClinicalTrialDocumentV3,
  ClinicalTrialDocumentV4,
  ClinicalTrialInput,
  ClinicalTrialsSearchResponse
} from "../interfaces";

/**
 * @deprecated Please use ClinicalTrialSearchResource instead.
 */
export interface ClinicalTrialResource {
  /**
   * Ready check
   */
  isReady(): Promise<boolean>;

  searchTrials(
    input: ClinicalTrialInput
  ): Promise<ClinicalTrialsSearchResponse>;

  trialDocument(trialId: string): Promise<ClinicalTrialDocument | null>;

  trialDocumentV2(nctId: string): Promise<ClinicalTrialDocumentV2 | null>;

  trialDocumentV3(nctId: string): Promise<ClinicalTrialDocumentV3 | null>;

  trialDocumentV4(nctId: string): Promise<ClinicalTrialDocumentV4 | null>;
}
