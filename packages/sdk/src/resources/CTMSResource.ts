import {
  CTMSFacilityAggregationResponse,
  CTMSFacilityAggregationInput,
  CTMSFacilityInitialFilterOptions,
  CTMSInvestigatorAggregationResponse,
  CTMSInvestigatorAggregationInput,
  CTMSInvestigatorInitialFilterOptions
} from "../interfaces";

export interface CTMSResource {
  isReady(): Promise<boolean>;

  ctmsFacilityAggregation(
    input: CTMSFacilityAggregationInput
  ): Promise<CTMSFacilityAggregationResponse>;

  ctmsFacilityInitialFilterOptions(
    institutionId: string
  ): Promise<CTMSFacilityInitialFilterOptions>;

  hasCTMSData(institutionId: string): Promise<boolean>;

  getAllCTMSFacilityIds(): Promise<string[]>;

  getAllCTMSPersonIds(): Promise<string[]>;

  ctmsInvestigatorAggregation(
    input: CTMSInvestigatorAggregationInput
  ): Promise<CTMSInvestigatorAggregationResponse>;

  ctmsInvestigatorInitialFilterOptions(
    personId: string
  ): Promise<CTMSInvestigatorInitialFilterOptions>;

  personHasCTMSData(personId: string): Promise<boolean>;
}
