import { RpcResourceClient, RedisOptions } from "@h1nyc/systems-rpc";
import { RPC_NAMESPACE_CONGRESS_EVENT_PAGE_SEARCH } from "../constants";
import {
  CongressSearchFilterAggregation,
  GetSpeakerIdsByRoleInput,
  SessionsSearchByCongressInput,
  SessionsSearchByCongressResponse,
  SessionsSearchFilterAutocompleteInput
} from "../interfaces";
import { CongressEventPageSearchResource } from "./CongressEventPageSearchResource";

export class CongressEventPageSearchResourceClient
  extends RpcResourceClient
  implements CongressEventPageSearchResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(
      signingSecret,
      RPC_NAMESPACE_CONGRESS_EVENT_PAGE_SEARCH,
      redisOptions
    );
  }

  async isReady(): Promise<boolean> {
    return true;
  }

  searchSessionsByCongress(
    input: SessionsSearchByCongressInput
  ): Promise<SessionsSearchByCongressResponse> {
    return this.rpcClient.sendRequest<SessionsSearchByCongressResponse>(
      "searchSessionsByCongress",
      [input]
    );
  }

  getSpeakerIdsByRole(input: GetSpeakerIdsByRoleInput): Promise<string[]> {
    return this.rpcClient.sendRequest<string[]>("getSpeakerIdsByRole", [input]);
  }

  getIndicationsForCongress(congressId: string): Promise<string[]> {
    return this.rpcClient.sendRequest<string[]>("getIndicationsForCongress", [
      congressId
    ]);
  }

  autocompleteSessionTypes(
    input: SessionsSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]> {
    return this.rpcClient.sendRequest<CongressSearchFilterAggregation[]>(
      "autocompleteSessionTypes",
      [input]
    );
  }
}
