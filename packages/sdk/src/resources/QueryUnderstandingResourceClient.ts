import { RedisOptions, RpcResourceClient } from "@h1nyc/systems-rpc";
import { RPC_NAMESPACE_QUERY_UNDERSTANDING } from "../constants";
import { SearchTypes } from "../interfaces/filterInterfaces";
import { QueryUnderstandingResource } from "./QueryUnderstandingResource";

export class QueryUnderstandingResourceClient
  extends RpcResourceClient
  implements QueryUnderstandingResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_QUERY_UNDERSTANDING, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", []);
  }

  async getKeywordSynonyms(keyword: string): Promise<string[]> {
    return this.rpcClient.sendRequest<string[]>("getKeywordSynonyms", [
      keyword
    ]);
  }

  async getSearchTypes(
    query: string,
    projectId: string,
    userId?: string,
    language?: string
  ): Promise<SearchTypes[]> {
    return this.rpcClient.sendRequest<SearchTypes[]>("getSearchTypes", [
      query,
      projectId,
      userId,
      language
    ]);
  }
}
