import { RedisOptions, RpcResourceClient } from "@h1nyc/systems-rpc";
import { RPC_NAMESPACE_REFERRAL_AUTOSUGGEST } from "../constants";
import {
  ReferralAutosuggestResource,
  ReferralType
} from "./ReferralAutosuggestResource";

export class ReferralAutosuggestResourceClient
  extends RpcResourceClient
  implements ReferralAutosuggestResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_REFERRAL_AUTOSUGGEST, redisOptions);
  }

  async isReady(): Promise<boolean> {
    return this.rpcClient.sendRequest<boolean>("isReady", []);
  }

  async serviceLineSuggest(
    personId: string,
    projectId: string,
    query: string,
    referralType: ReferralType
  ): Promise<string[]> {
    return this.rpcClient.sendRequest<string[]>("serviceLineSuggest", [
      personId,
      projectId,
      query,
      referralType
    ]);
  }

  async specialtySuggest(
    peopleIds: string[],
    projectId: string,
    query: string
  ): Promise<string[]> {
    return this.rpcClient.sendRequest<string[]>("specialtySuggest", [
      peopleIds,
      projectId,
      query
    ]);
  }
}
