import { RedisOptions, RpcResourceClient } from "@h1nyc/systems-rpc";
import { RPC_NAMESPACE_INDICATIONS_TREE_SEARCH } from "../constants";
import {
  SearchIndicationsByQueryInput,
  IndicationNode,
  SearchRootIndicationsInput,
  IndicationsGetSubTreesInput,
  SearchIndicationTreesByQueryInput
} from "../interfaces";
import { IndicationsTreeSearchResource } from "./IndicationsTreeSearchResource";

export class IndicationsTreeSearchResourceClient
  extends RpcResourceClient
  implements IndicationsTreeSearchResource
{
  constructor(signingSecret: string, redisOptions?: RedisOptions) {
    super(signingSecret, RPC_NAMESPACE_INDICATIONS_TREE_SEARCH, redisOptions);
  }
  searchRootIndications(
    input: SearchRootIndicationsInput
  ): Promise<IndicationNode[]> {
    return this.rpcClient.sendRequest("searchRootIndications", [input]);
  }

  searchByQuery(
    input: SearchIndicationsByQueryInput
  ): Promise<IndicationNode[]> {
    return this.rpcClient.sendRequest("searchByQuery", [input]);
  }

  getSubTrees(input: IndicationsGetSubTreesInput): Promise<IndicationNode[]> {
    return this.rpcClient.sendRequest("getSubTrees", [input]);
  }

  searchIndicationTreesByQuery(
    input: SearchIndicationTreesByQueryInput
  ): Promise<IndicationNode[]> {
    return this.rpcClient.sendRequest("searchIndicationTreesByQuery", [input]);
  }
}
