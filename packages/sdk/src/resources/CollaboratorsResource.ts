import {
  CollaboratorsResponse,
  HasCollaboratorsResponse
} from "../interfaces/Collaborators";

export interface CollaboratorsResource {
  isReady(): Promise<boolean>;

  /**
   * Search for the existence of collaborators for a given person and project
   *
   * @param personId The id of the person
   * @param projectId Id of the project
   */
  hasCollaborators(
    personId: string,
    projectId: string
  ): Promise<HasCollaboratorsResponse>;

  /**
   * Search for the number of collaborators for a given person and possible project
   *
   * @param personId The id of the person
   * @param projectId Id of the project
   */
  findCollaborators(
    personId: string,
    dateRange: {
      min?: number | null;
      max?: number | null;
    },
    page: {
      limit: number;
      offset: number;
    },
    terms?: string[],
    projectId?: string
  ): Promise<CollaboratorsResponse>;
}
