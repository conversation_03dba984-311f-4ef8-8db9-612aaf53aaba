export enum ReferralType {
  Received = "Received",
  Sent = "Sent"
}

export interface ReferralAutosuggestResource {
  isReady(): Promise<boolean>;

  serviceLineSuggest(
    personId: string,
    projectId: string,
    query: string,
    referralType: ReferralType
  ): Promise<string[]>;

  specialtySuggest(
    peopleIds: string[],
    projectId: string,
    query: string
  ): Promise<string[]>;
}
