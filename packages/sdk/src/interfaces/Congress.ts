export interface CongressSearchInput {
  query?: string;
  filters?: CongressSearchFilters;
  paging?: {
    limit: number;
    offset: number;
  };
  projectId: string;
  userId: string;
  sortOrder?: CongressSearchSortOrder;
}

export interface CongressSearchFilters {
  location?: {
    regions?: string[];
    excludeRegions?: string[];
    countries?: string[];
    excludeCountries?: string[];
    cities?: string[];
    excludeCities?: string[];
    postalCodes?: string[];
    excludePostalCodes?: string[];
    isVirtual?: boolean;
  };
  indication?: {
    indications?: string[];
  };
  dateRange?: {
    min: number;
    max?: number;
  };
  series?: {
    seriesNames?: string[];
  };
  society?: {
    societyNames?: string[];
  };
  type?: {
    congressTypes?: string[];
  };
  tags?: {
    peopleTags?: string[];
    exclusionPeopleTags?: string[];
    intersectPeopleTags?: boolean;
  };
  following?: {
    isFollowing?: boolean;
    isNotFollowing?: boolean;
  };
}

export interface CongressSearchResponse {
  total: number;
  congresses: CongressSearchResult[];
}

export interface CongressSearchResult {
  id: string;
  seriesId: string;
  name: string;
  seriesName: string;
  type: string;
  speakers: Speaker[];
  addresses: Address[];
  translations: Translation[];
  isFollowing: boolean;
  startDate: number;
  endDate: number;
  society?: string;
}

interface Speaker {
  id?: string;
  name: string;
  role: string;
}

interface Address {
  street1?: string;
  street2?: string;
  street3?: string;
  city?: string;
  region?: string;
  regionCode?: string;
  country?: string;
  postalCode?: string;
  county?: string;
  district?: string;
  languageCode?: string;
}

interface Translation {
  name: string;
  description: string;
  society: string;
  languageCode: string;
}

interface SessionTranslation {
  name: string;
  description?: string;
  sessionType?: string;
  languageCode: string;
}

export interface SessionsSearchByCongressInput {
  congressId: string;
  query?: string;
  filters?: SessionsSearchFilters;
  paging?: {
    limit: number;
    offset: number;
  };
  projectId: string;
  userId: string;
}

export interface SessionsSearchFilters {
  dateRange?: {
    min: number;
    max?: number;
  };
  type?: {
    sessionTypes: string[];
  };
}

export interface SessionsSearchByCongressResponse {
  total: number;
  sessions: Session[];
}

export interface Session {
  id: string;
  name: string;
  description?: string;
  sessionType?: string;
  startDate?: number;
  endDate?: number;
  speakers: Speaker[];
  subsessions?: Session[];
  translations: SessionTranslation[];
}

export interface CongressSearchFilterAutocompleteInput {
  globalQuery?: string;
  filterQuery?: string;
  filters?: CongressSearchFilters;
  count: number;
  projectId: string;
  userId: string;
}

export interface CongressSearchFilterAggregation {
  id: string;
  label: string;
  count: number;
}

export interface CongressSearchLocationFilterAggregation
  extends CongressSearchFilterAggregation {
  regionsIn?: string[];
  locationsInRegion?: CongressSearchFilterAggregation[];
}

export interface GetSpeakerIdsByRoleInput {
  congressId: string;
  roles: string[];
}

export interface SessionsSearchFilterAutocompleteInput {
  globalQuery?: string;
  filterQuery?: string;
  filters?: SessionsSearchFilters;
  congressId: string;
  count: number;
  projectId: string;
  userId: string;
}

export enum CongressSearchSortOrder {
  H1_RANKING = "H1_RANKING",
  CHRONOLOGICAL = "CHRONOLOGICAL",
  REVERSE_CHRONOLOGICAL = "REVERSE_CHRONOLOGICAL"
}
