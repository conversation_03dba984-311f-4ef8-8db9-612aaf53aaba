// generated using https://app.quicktype.io/
// based off json schema: teams/data-platform-ct/multi-env/glue/schemas/ctms/topic/facility-aggregation.json
import { AggregationStats, CTMSFilters } from "./CTMSFacilityAggregation";

export interface CTMSInvestigatorAggregation {
  avg_patient_recruitment_performance_pct?: number;
  avg_patient_recruitment_period?: number;
  avg_patient_retention_pct?: number;
  avg_protocol_violations_per_participant?: number;
  avg_recruitment_period_early_pct?: number;
  avg_recruitment_period_late_pct?: number;
  avg_recruitment_period_on_time_pct?: number;
  effective_date: string;
  first_name?: string;
  h1_person_id?: string;
  last_name?: string;
  person_hash: string;
  times_used_as_trial?: number;
  indication?: string;
  phase?: string;
  therapeutic_area?: string;
  version: any;
}
export interface CTMSInvestigatorAggregationResponse {
  avg_patient_recruitment_performance_pct?: number;
  avg_patient_recruitment_period?: number;
  avg_patient_retention_pct?: number;
  avg_protocol_violations_per_participant?: number;
  avg_recruitment_period_early_pct?: number;
  avg_recruitment_period_late_pct?: number;
  avg_recruitment_period_on_time_pct?: number;
  first_name?: string;
  h1_person_id?: string;
  last_name?: string;
  times_used_as_trial?: number;
  indication?: AggregationStats[];
  phase?: AggregationStats[];
  therapeutic_area?: AggregationStats[];
}

export interface CTMSInvestigatorAggregationInput {
  personId: string;
  filters?: CTMSFilters;
}
export interface CTMSInvestigatorInitialFilterOptions {
  indication?: AggregationStats[];
  phase?: AggregationStats[];
  therapeutic_area?: AggregationStats[];
}
