export interface IndicationNode {
  id: string;
  h1Id: string;
  parentH1Ids: string[];
  indicationName: string;
  indicationType: IndicationType;
  matchedSynonyms: string[];
  displayName?: string;
  description?: string;
  hcpCommunitySize?: number;
  claimCount?: number;
  patientCount?: number;
  icdCodes: IcdNode[];
  match?: boolean;
  children: IndicationNode[];
}

export interface IcdNode {
  icdCode: string;
  patientCount: number;
  description: string;
}

export enum IndicationSource {
  ALL = "ALL",
  CLAIMS = "CLAIMS",
  CLAIMS_UK = "CLAIMS_UK",
  CLAIMS_BRAZIL = "CLAIMS_BRAZIL"
}

export enum IndicationType {
  L1 = "L1",
  L2 = "L2",
  L3 = "L3",
  ICD = "ICD"
}

export enum IndicationSortBy {
  HCP_COMMUNITY_SIZE = "hcpCommunitySize",
  PATIENT_COUNT = "patientCount"
}
