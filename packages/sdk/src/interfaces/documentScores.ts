export interface DocumentScoreRange {
  min: number;
  max: number;
}

export interface ScoredDocumentResult {
  value: number;
  normalizedValue: number;
  minValue: number;
  maxValue: number;
  percentile: number;
}
export const ZERO_SCORED_DOCUMENT_RESULT: Readonly<ScoredDocumentResult> = {
  minValue: 0,
  maxValue: 0,
  normalizedValue: 0,
  percentile: 0,
  value: 0
};

export interface ScoredDocumentData {
  personId: string;
  normalizedRange: DocumentScoreRange;
  publications: ScoredDocumentResult;
  citations: ScoredDocumentResult;
  trials: ScoredDocumentResult;
  payments: ScoredDocumentResult;
  paymentsCount: ScoredDocumentResult;
  grants: ScoredDocumentResult;
  grantsCount: ScoredDocumentResult;
  congresses: ScoredDocumentResult;
  collaborators: ScoredDocumentResult;
  socialMediaMentions: ScoredDocumentResult;
  procedures: ScoredDocumentResult;
  diagnoses: ScoredDocumentResult;
  prescriptions?: ScoredDocumentResult;
  referralsReceived?: ScoredDocumentResult;
  referralsSent?: ScoredDocumentResult;
  h1Score: number;
}

export interface DocumentRanges {
  publicationCount: DocumentScoreRange;
  citationCount: DocumentScoreRange;
  trialCount: DocumentScoreRange;
  paymentCount: DocumentScoreRange;
  paymentSum: DocumentScoreRange;
  grantCount: DocumentScoreRange;
  grantSum: DocumentScoreRange;
  congressCount: DocumentScoreRange;
  totalCollaborators: DocumentScoreRange;
  socialMediaMentions: DocumentScoreRange;
  procedures: DocumentScoreRange;
  diagnoses: DocumentScoreRange;
  referralsReceived: DocumentScoreRange;
  referralsSent: DocumentScoreRange;
}
