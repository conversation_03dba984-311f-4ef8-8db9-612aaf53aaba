export interface ClaimsDiagnosis {
  description: string;
  diagnosisCode: string;
  percentOfClaims: number;
  percentage: number;
  count: string;
  internalCount: number;
  codeScheme: string;
}

export interface ClaimsCcsrDiagnosis {
  description: string;
  percentOfClaims: number;
  hcpQuartile: number;
  count: number;
}

export interface ClaimsCcsrProcedure {
  description: string;
  percentOfClaims: number;
  hcpQuartile: number;
  count: number;
}

export interface ClaimsProcedure {
  description: string;
  procedureCode: string;
  percentOfClaims: number;
  percentage: number;
  count: string;
  internalCount: number;
  codeScheme: string;
}

export interface ClaimsPrescription {
  genericName: string;
  brandName: string;
  drugClass: string[];
  numPrescriptions: string;
  patientCount: number;
  rank: number;
  quartile: number;
  percentage: number;
}
