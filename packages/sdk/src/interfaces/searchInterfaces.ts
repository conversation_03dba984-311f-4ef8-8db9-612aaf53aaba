import { LatLonGeoLocation } from "@elastic/elasticsearch/lib/api/types";

interface ShardsNameSearch {
  total: number;
  successful: number;
  skipped: number;
  failed: number;
}

type Location = {
  city_eng: string;
  state_eng: string;
  zipCode5_eng: string;
  state_cmn: string;
  state_jpn: string;
  languageCode: string;
  token: string;
  zipCode5_cmn: string;
  zipCode5_jpn: string;
  country_eng: string;
  zipCodeFull: string;
  country_cmn: string;
  stateCode: string;
  city_cmn: string;
  country_jpn: string;
  city_jpn: string;
};

export interface TrialPerson {
  fullName: string;
  id: string;
}

export interface Trial {
  interventions: string[];
  officialTitle?: string;
  officialTitle_eng: string;
  officialTitle_cmn: string;
  officialTitle_jpn: string;
  briefTitle?: string;
  briefTitle_eng: string;
  briefTitle_cmn: string;
  briefTitle_jpn: string;
  completionDate: number;
  persons: TrialPerson[];
  keywords: string[];
  startDate: number;
  id: string;
  conditions: string[];
  primaryCompletionDate: number;
  summary: string;
  type?: string;
  status?: string;
  status_eng: string;
  status_cmn: string;
  status_jpn: string;
  phase?: string;
  phase_eng: string;
  phase_cmn: string;
  phase_jpn: string;
  eligibilityCriteria: string;
  languageCode: string;
  documentType?: DocumentType;
}

export interface CongressPersons {
  fullName: string;
  id: string;
}

export interface Congress {
  id: string;
  organizer: string;
  type?: string;
  name?: string;
  name_eng: string;
  name_jpn: string;
  name_cmn: string;
  presentedDate?: any;
  endDate: number;
  keywords: any[];
  persons: CongressPersons[];
  title?: string;
  title_eng: string;
  title_cmn: string;
  title_jpn: string;
  languageCode: string;
  documentType?: DocumentType;
}

interface Payment {
  natureOfPayment: string;
  id: string;
  paymentDate: number;
  payerCompany: string;
  associatedDrug: string;
  amount: number;
}

export interface PublicationPersons {
  fullName: string;
  id: string;
}

export interface Publication {
  title?: string;
  title_eng: string;
  title_jpn: string;
  title_cmn: string;
  journalName?: string;
  journalName_eng: string;
  journalName_jpn: string;
  journalName_cmn: string;
  type: string[];
  persons: PublicationPersons[];
  keywords: string[];
  citationCount: number;
  id: string;
  PMID: string;
  substances: any[];
  abstract: string;
  datePublished: number;
  languageCode: string;
  documentType?: DocumentType;
}

export enum DocumentType {
  PUBLICATION = "PUBLICATION",
  CLINICALTRIAL = "CLINICALTRIAL",
  CONGRESS = "CONGRESS"
}

export type ActivityDocument = Publication | Trial | Congress;

export interface RecentActivity {
  document: ActivityDocument;
}

interface Referral {
  serviceLine: string;
  serviceCodeType: string;
  count: number;
  serviceDescription: string;
}

interface Source {
  locations: Location[];
  trials: Trial[];
  congress: Congress[];
  microBloggingTotal: number;
  congressCount: number;
  payments: Payment[];
  titles: any[];
  institutions: string[];
  designations?: string[];
  affiliations?: any[];
  presentWorkInstitutions: string[];
  pastAndPresentWorkInstitutions: string[];
  studentInstitutions: string[];
  graduationYear: number;
  trialCount: number;
  totalWorks: number;
  projectIds: string[];
  publications: Publication[];
  schools: any[];
  collaboratorCount: number;
  firstName: string;
  name: string;
  lastName: string;
  id: string;
  h1dn_id: string;
  specialty: string[];
  middleName: string;
  publicationCount: number;
  presentWorkInstitutionCount: number;
  paymentTotal: number;
  DRG_diagnosesCount: number;
  DRG_proceduresCount: number;
  referralsSent?: Referral[];
  referralsReceived?: Referral[];
  referralsSentCount: number;
  referralsReceivedCount: number;
  firstName_eng: string;
  name_eng: string;
  lastName_eng: string;
  specialty_eng: string[];
  middleName_eng: string;
  titles_eng: any[];
  institutions_eng: string[];
  presentWorkInstitutions_eng: string[];
  pastAndPresentWorkInstitutions_eng: string[];
  studentInstitutions_eng: string[];
  firstName_cmn: string;
  lastName_cmn: string;
  middleName_cmn: string;
  specialty_cmn: string[];
}

export interface SearchHit {
  _index: string;
  _type: string;
  _id: string;
  _score: number;
  _source: Source;
}

interface Total {
  value: number;
  relation: string;
}
interface Hits {
  total: Total;
  max_score: number;
  hits: SearchHit[];
}

interface AggregationFilterCount {
  [k: string]: {
    buckets: Array<{
      key: string;
      doc_count: number;
    }>;
  };
}
export interface RootSearchObject {
  took: number;
  timed_out: boolean;
  _shards: ShardsNameSearch;
  hits: Hits;
  aggregations: AggregationFilterCount;
}
export interface GeoClusteringAggregation {
  centroid: LatLonGeoLocation;
  geohash_grids: string[];
  doc_count: number;
}
