// generated using https://app.quicktype.io/
// based off json schema: teams/data-platform-ct/multi-env/glue/schemas/ctms/topic/facility-aggregation.json
import { CTMSInvestigatorAggregation } from "./CTMSInvestigatorAggregation";
export interface AggregationStats {
  value: string;
  count: number;
}
export interface CTMSFacilityAggregation {
  activation?: number;
  avg_patient_recruitment_performance_pct?: number;
  avg_patient_recruitment_period?: number;
  avg_patient_retention_pct?: number;
  avg_protocol_violations_per_participant?: number;
  avg_recruitment_period_early_pct?: number;
  avg_recruitment_period_late_pct?: number;
  avg_recruitment_period_on_time_pct?: number;
  effective_date: string;
  h1_organization_id: string;
  indication?: string;
  investigator?: Investigator[];
  phase?: string;
  therapeutic_area?: string;
  times_used_as_trial?: number;
  version: any;
}

export interface Investigator {
  first_name?: string;
  h1_person_id?: string;
  last_name?: string;
  person_hash: string;
  times_used_as_trial?: number;
}

export interface CTMSFilters {
  therapeutic_area?: string[] | null;
  phase?: string[] | null;
  indication?: string[] | null;
  dateRange?: {
    min: number;
    max?: number | undefined;
  } | null;
}
export interface CTMSFacilityAggregationInput {
  institutionId: string;
  filters?: CTMSFilters;
}

export interface CTMSFacilityAggregationResponse {
  activation?: number;
  avg_patient_recruitment_performance_pct?: number;
  avg_patient_recruitment_period?: number;
  avg_patient_retention_pct?: number;
  avg_protocol_violations_per_participant?: number;
  avg_recruitment_period_early_pct?: number;
  avg_recruitment_period_late_pct?: number;
  avg_recruitment_period_on_time_pct?: number;
  times_used_as_trial?: number;
  indication?: AggregationStats[];
  investigatorAggregations?: Investigator[];
  phase?: AggregationStats[];
  therapeutic_area?: AggregationStats[];
  investigators?: AggregationStats[];
  h1LinkedInvestigators?: CTMSInvestigatorAggregation[];
}

export interface CTMSFacilityInitialFilterOptions {
  indication?: AggregationStats[];
  phase?: AggregationStats[];
  therapeutic_area?: AggregationStats[];
}
