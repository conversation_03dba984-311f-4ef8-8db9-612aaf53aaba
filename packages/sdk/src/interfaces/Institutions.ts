import {
  GeoBounds,
  LatLonGeoLocation
} from "@elastic/elasticsearch/lib/api/types";
import { INSTITUTION_ACCESS_LEVEL } from "@h1nyc/account-sdk";
import { EntitySortOptions } from "@h1nyc/account-sdk/dist/interfaces/Sort";
import {
  GeoDistance,
  GeoShape,
  GeoChoroplethFilter,
  SearchTypes
} from "./filterInterfaces";
import { GeoClusteringAggregation } from "./searchInterfaces";
import { Apps } from "./Apps";
import { InclusionExclusionRule, RuleOrRuleGroup } from "./rulesAndGroup";

export interface InstitutionClaims {
  code: string | null;
  description: string | null;
  count: number | null;
}
export interface PatientsDiversityRaceInfo
  extends PatientsSpainDiversityRaceInfo {
  americanIndianOrAlaskaNative?: number;

  asianPacificIslander?: number;

  blackNonHispanic?: number;

  hispanic?: number;

  whiteNonHispanic?: number;

  notIdentified?: number;

  indigenousBrazil?: number;

  mixedBrazil?: number;

  asian?: number;

  pacificIslander?: number;

  nonWhite?: number;

  // UK races
  // asian?: number;
  black?: number;
  mixed?: number;
  white?: number;
  other?: number;
  unknown?: number;
}

export interface PatientsUkDiversityRaceInfo {
  asian?: number;
  black?: number;
  mixed?: number;
  white?: number;
  other?: number;
  unknown?: number;
}

export interface PatientsFranceDiversityRaceInfo {
  franceNative?: number;
  europeanUnion?: number;
  otherEuropean?: number;
  middleEastAndNorthAfrica?: number;
  centralAndSouthAfrica?: number;
  oceania?: number;
  southAmerica?: number;
  southeastAsia?: number;
  northAmerica?: number;
  eastAsia?: number;
  southAsia?: number;
  centralAmerica?: number;
}

export interface PatientsSpainDiversityRaceInfo
  extends PatientsFranceDiversityRaceInfo {
  spain?: number;
  centralAsia?: number;
  other?: number;
  unknown?: number;
}

export interface PatientsDiversityAgeInfo {
  range?: string | null;
  count?: number | null;
}

export interface PatientsDiversitySexInfo {
  sex?: string | null;
  count?: number | null;
}

export interface EnrollmentRatesByIndication {
  indication?: string;
  enrollmentRate?: number;
  trialCount?: number;
}

export interface ESEnrollmentRatesByIndication {
  indication?: string;
  enrollment_rate?: number;
  total_trials?: number;
}

export interface Institution {
  id: number;
  name: string;
  nameTranslations?: {
    name: string;
    languageCode: string;
  }[];
  institutionType?: string | null;
  website?: string | null;
  beds?: number | null;
  region: string;
  linkedinUrl?: string | null;
  twitterUrl?: string | null;
  people: InstitutionPeopleAggregation;
  procedures: InstitutionAggregation;
  diagnoses: InstitutionAggregation;
  prescriptions: InstitutionAggregation;
  publications: InstitutionAggregation;
  trials: InstitutionAggregation;
  congresses: InstitutionAggregation;
  payments: InstitutionAggregation;
  trialOngoingCount?: number;
  trialActivelyRecruitingCount?: number;
  address: {
    street1?: string | null;
    street2?: string | null;
    street3?: string | null;
    country?: string | null;
    countryCode?: string | null;
    city?: string | null;
    region?: string | null;
    regionCode?: string | null;
    postalCode?: string | null;
  };
  addressTranslations?: {
    id: number;
    street1?: string;
    street2?: string;
    street3?: string;
    postal_code?: string;
    city?: string;
    region?: string;
    region_code?: string;
    country?: string;
    country_code?: string;
    languageCode: string;
  }[];
  location?: LatLonGeoLocation | null;
  lat_long_precision?: number | null;
  hasCTMSData?: boolean;
  matchedDiagnosesDetails?: InstitutionClaims[];
  matchedProceduresDetails?: InstitutionClaims[];
  patientsDiversity?:
    | PatientsDiversityRaceInfo
    | PatientsUkDiversityRaceInfo
    | PatientsFranceDiversityRaceInfo;
  patientsAge?: PatientsDiversityAgeInfo[];
  patientsSex?: PatientsDiversitySexInfo[];
  patientMatchesForIndication?: number | null;
  nciDesignatedCancerCenter?: boolean;
  nationalComprehensiveCancerNetworkMember?: boolean;
  teachingHospital?: boolean;
  communityHospital?: boolean;
  researchFacility?: boolean;
  cro?: boolean;
  flags?: {
    nciDesignatedCancerCenter: boolean;
    nationalComprehensiveCancerNetworkMember: boolean;
    teachingHospital: boolean;
    nihCommunitySite: boolean;
    minorityCommunitySite: boolean;
    researchFacility: boolean;
    cro: boolean;
    partOfSystem: {
      partOfSystem: boolean;
      healthSystem: boolean;
      universitySystem: boolean;
    };
    privatePractice: {
      privatePractice: boolean;
      large: boolean;
      small: boolean;
    };
    government: {
      government: boolean;
      military: boolean;
      veteransAffairs: boolean;
    };
  };
  orgTypes?: string;
  orgTypesLevel2?: string;
  orgTypesLevel3?: string;
  trialEnrollmentRate?: number;
  enrollmentRatesByIndications?: EnrollmentRatesByIndication[] | undefined;
  matchedEnrollmentRatesByIndications?:
    | EnrollmentRatesByIndication[]
    | undefined;
  totalTags: number;
  ultimateParentId?: string;
  ultimateParentName?: string;
  childAffiliationCount?: number;
}

export interface InstitutionAggregation {
  matched?: number | null;
  total: number;
}

export interface InstitutionPeopleAggregation extends InstitutionAggregation {
  affiliatedMatched?: number | null;
}

export interface InstitutionFilterAggregation {
  id: string;
  count: number;
  ccsrDescriptions?: string[];
  /**
   * @deprecated use ccsrSize instead
   */
  ccsrIcdSize?: string;
  ccsrSize?: string;
}

export interface InstitutionLocationFilterAggregation
  extends InstitutionFilterAggregation {
  regionsIn?: string[];
  locationsInRegion?: InstitutionFilterAggregation[];
}

export interface InstitutionsResponse {
  total: number;
  institutions: Institution[];
  totalInstitutionsInArea?: number;
  countryAggregations: InstitutionLocationFilterAggregation[];
  regionAggregations: InstitutionLocationFilterAggregation[];
  cityAggregations: InstitutionLocationFilterAggregation[];
  postalCodeAggregations: InstitutionFilterAggregation[];
  geoLocationAggregations?: InstitutionFilterAggregation[];
  geoDistanceAggregations?: InstitutionFilterAggregation[];
  geoClusteringAggregations?: GeoClusteringAggregation[];
  isNavigationalQuery: boolean;
  synonyms: string[] | null;
  icdCodeSynonyms: string[];
  geoClaimsAggregations?: InstitutionFilterAggregation[];
  orgTypesAggregations?: InstitutionFilterAggregation[];
  diversityHeatmap?: HeatmapAggregation[]; // code-based (subIcbCode)
  namedLocationDiversityHeatmap?: HeatmapLocationAggregation[]; // name-based (e.g., "Normandy")
  diversityDashboard?: DiversityDashboardAggregation;
  childrenMatchedCount?: number;
}

export interface InstitutionSearchFilter {
  regions?: string[] | null;
  excludeRegions?: string[] | null;
  countries?: string[] | null;
  excludeCountries?: string[] | null;
  cities?: string[] | null;
  excludeCities?: string[] | null;
  postalCodes?: string[] | null;
  excludePostalCodes?: string[] | null;
  trialsFilters?: InstitutionTrialsFiltersInput | null;
  tags?: string[] | null;
  intersectTags?: boolean | null;
  excludeTags?: string[] | null;
  iolIds?: string[] | null;
  ultimateParentId?: string | null;
  affiliatedPeopleIolsIds?: string[] | null;
  precision?: number | null;
  geoBoundingBox?: GeoBounds | null;
  geoDistance?: GeoDistance | null;
  geoShape?: GeoShape | null;
  zoom?: number | null;
  computeResultGeoClusters?: boolean | null;
  geoStatsRegionLevel?: GeoChoroplethFilter | null;
  geoHashGrids?: string[] | null;
  heatMapZoomLevel?: HeatMapZoomLevel | null;
  heatMapZoomLevel1?: string[] | null;
  heatMapZoomLevel2?: string[] | null;
  heatMapZoomLevel3?: string[] | null;
  orgTypes?: string[] | null;
  indications?: string[] | null;
  hasCTMSData?: boolean | null;
  claimsFilters?: ClaimFiltersInput | null;
  exclusionClaims?: ExclusionClaimFiltersInput | null;
  diversityFilters?: DiversityFiltersInput | null;
  patientClaimsFilter?: RuleOrRuleGroup | null;
  patientClaimsFilterV2?: InclusionExclusionRule | null;
  excludeAcademicTypes?: AcademicTypesEnum[] | null;
  includeAcademicTypes?: AcademicTypesEnum[] | null;
  trialEnrollmentRate?: {
    min?: number | undefined;
    max?: number | undefined;
  } | null;
  isPartOfSystem?: boolean | null;
}

export interface InstitutionsSearchInput {
  query?: string;
  filters?: InstitutionSearchFilter;
  paging?: {
    limit: number;
    offset: number;
  };
  projectId: string;
  accessLevel: INSTITUTION_ACCESS_LEVEL;
  uniqueIols?: number;
  sortBy?: InstitutionSortOptions;
  exportsSelections?: {
    matchedClaimsDetails: boolean;
  };
  consistentLocationFilter?: boolean;
  userId?: string;
  app?: Apps;
  searchType?: SearchTypes;
  showAllTypesOfInstitutions?: boolean;
}

export interface InstitutionTrialsFiltersInput {
  id?: string[] | null;
  minCount?: number | null;
  maxCount?: number | null;
  status?: string[] | null;
  phase?: string[] | null;
  studyType?: string[] | null;
  sponsor?: string[] | null;
  sponsorType?: string[] | null;
  conditions?: string[] | null;
  biomarkers?: string[] | null;
  dateRange?: {
    min: number;
    max?: number | undefined;
  } | null;
  startDateTimeFrame?: {
    min: number;
    max?: number | undefined;
  } | null;
}

export interface ClaimFiltersInput {
  diagnosesICDMinCount?: number | null;
  diagnosesICDMaxCount?: number | null;
  diagnosesICD?: string[] | null;
  proceduresCPTMinCount?: number | null;
  proceduresCPTMaxCount?: number | null;
  proceduresCPT?: string[] | null;
  timeFrame?: number | null;
  showUniquePatients?: boolean | null;
  genericNames?: string[] | null;
  prescriptionsMinCount?: number | null;
  prescriptionsMaxCount?: number | null;
  ccsr?: string[] | null;
  ccsr_px?: string[] | null;
}

export interface ExclusionClaimFiltersInput {
  diagnosesICD?: string[] | null;
  proceduresCPT?: string[] | null;
  genericNames?: string[] | null;
  ccsr?: string[] | null;
  ccsr_px?: string[] | null;
}

export enum PatientRaceUk {
  ASIAN = "Asian",
  BLACK = "Black Non-Hispanic",
  MIXED = "Mixed or Multiple",
  WHITE = "White Non-Hispanic",
  OTHER = "Other Ethnic Group",
  UNKNOWN = "Unknown"
}

export enum PatientNationalityFrance {
  FRANCE_NATIVE = "France - Native",
  EUROPEAN_UNION = "European Union",
  OTHER_EUROPEAN = "Other European",
  MIDDLE_EAST_NORTH_AFRICA = "Middle East/North Africa",
  CENTRAL_SOUTH_AFRICA = "Central/South Africa",
  OCEANIA = "Oceania",
  SOUTHEAST_ASIA = "Southeast Asia",
  EAST_ASIA = "East Asia",
  SOUTH_ASIA = "South Asia",
  NORTH_AMERICA = "North America",
  SOUTH_AMERICA = "Sourth America",
  CENTRAL_AMERICA = "Central America"
}

export enum PatientNationalitySpain {
  SPAIN = "Spain",
  EUROPEAN_UNION = "European Union",
  OTHER_EUROPEAN = "Other European",
  MIDDLE_EAST_NORTH_AFRICA = "Middle East/North Africa",
  CENTRAL_SOUTH_AFRICA = "Central/South Africa",
  OCEANIA = "Oceania",
  SOUTHEAST_ASIA = "Southeast Asia",
  EAST_ASIA = "East Asia",
  SOUTH_ASIA = "South Asia",
  CENTRAL_ASIA = "Central Asia",
  NORTH_AMERICA = "North America",
  SOUTH_AMERICA = "Sourth America",
  CENTRAL_AMERICA = "Central America",
  OTHER = "Other",
  UNKNOWN = "Unknown"
}

export interface DiversityFiltersInput {
  race?: string[] | null;
  ageRange?: string[] | null;
  sex?: string[] | null;
  raceMix?: MinMaxFilter[] | null;
  nationality?: string[] | null;
  ethnicity?: string[] | null;
}

export interface MinMaxFilter {
  key: string;
  min: number | undefined;
  max: number | undefined;
}

export interface InstitutionsAutocompleteInput {
  query?: string;
  prefix: string;
  filters?: {
    regions?: string[] | null;
    countries?: string[] | null;
    cities?: string[] | null;
    postalCodes?: string[] | null;
    trialsFilters?: InstitutionTrialsFiltersInput | null;
    orgTypes?: string[] | null;
    claimsFilters?: ClaimFiltersInput | null;
    exclusionClaims?: ExclusionClaimFiltersInput | null;
    diversityFilters?: DiversityFiltersInput | null;
    indications?: string[] | null;
    trialEnrollmentRate?: {
      min?: number | undefined;
      max?: number | undefined;
    } | null;
  };
  count: number;
  projectId: string;
  accessLevel: INSTITUTION_ACCESS_LEVEL;
  userId?: string;
  app?: Apps;
  consistentLocationFilter?: boolean;
  searchType?: SearchTypes;
  showAllTypesOfInstitutions?: boolean;
  ccsrToExpand?: string;
  /**
   * @deprecated use ccsrOffset instead
   */
  ccsrIcdOffset?: number;
  /**
   * @deprecated use ccsrSize instead
   */
  ccsrIcdSize?: number;
  ccsrOffset?: number;
  ccsrSize?: number;
}

export interface TrialsSearchByInstitutionInput {
  institutionId: number;
  h1dnInstitutionId?: string;
  query?: string;
  trialsFilters?: InstitutionTrialsFiltersInput | null;
  paging?: {
    limit: number;
    offset: number;
  };
  projectId?: string;
  indications?: string[];
}

export interface MatchedInstitutionTrials {
  trialId: string;
  affiliationType?: string;
}
export interface TrialsSearchByInstitutionResponse {
  institutionId: number;
  h1dnInstitutionId?: string;
  matchedTrialIds: string[];
  matchedCtmsTrialIds: string[];
  totalMatchedTrials: number;
  totalTrials: number;
  totalMatchedPersons: number;
  totalPersons: number;
  ongoingTrialsCount?: number;
  matchedTrials: MatchedInstitutionTrials[];
}

// sort institution results by the following options
export enum InstitutionSortOptions {
  TRIALS = "TRIALS",
  DIVERSITY = "DIVERSITY",
  UK_DIVERSITY = "UK_DIVERSITY",
  FRANCE_DIVERSITY = "FRANCE_DIVERSITY",
  SPAIN_DIVERSITY = "SPAIN_DIVERSITY",
  AGE = "AGE",
  TRIAL_PERFORMANCE = "TRIAL_PERFORMANCE",
  PATIENT_COUNT = "PATIENT_COUNT",
  DIAGNOSES = "DIAGNOSES",
  PROCEDURES = "PROCEDURES",
  PRESCRIPTIONS = "PRESCRIPTIONS",
  H1_DEFAULT = "H1_DEFAULT"
}

export enum AcademicTypesEnum {
  nationalComprehensiveCancerNetworkMember = "nationalComprehensiveCancerNetworkMember",
  communityHospital = "communityHospital",
  hospital = "hospital",
  teachingHospital = "teachingHospital",
  privatePractice = "privatePractice",
  privatePracticeLarge = "privatePracticeLarge",
  privatePracticeSmall = "privatePracticeSmall",
  cancerCenter = "cancerCenter",
  nciDesignatedCancerCenter = "nciDesignatedCancerCenter",
  communitySite = "communitySite",
  nihCommunitySite = "nihCommunitySite",
  minorityCommunitySite = "minorityCommunitySite",
  researchFacility = "researchFacility",
  contractResearchOrganization = "contractResearchOrganization", //CRO
  government = "government",
  militaryGovernment = "militaryGovernment",
  veteransAffairGovernment = "veteransAffairGovernment"
}
export interface IolClaim {
  code: string;
  description: string;
  genericName?: string;
  claimsCount: number;
  rank: number;
  totalInCountry: number;
  iolId: string;
  regionRank?: number;
  totalInRegion?: number;
}
export declare type IolClaimSortOptions = Pick<
  IolClaim,
  "claimsCount" | "code" | "description" | "rank" | "regionRank" | "genericName"
>;
interface MinMaxFloatFilter {
  min?: number | null;
  max?: number | null;
}
export interface IolClaimsFilters {
  codes?: string[] | null;
  schemes?: string[] | null;
  descriptions?: string[] | null;
  genericNames?: string[] | null;
  count?: MinMaxFloatFilter | null;
  type: IolClaimType;
  rank?: MinMaxFloatFilter | null;
  keywords?: string[] | null;
  regionRank?: MinMaxFloatFilter | null;
}

export enum IolClaimType {
  Diagnosis = "Diagnosis",
  Procedure = "Procedure",
  Prescriptions = "Prescriptions"
}

export interface ClaimsPage {
  offset: number;
  limit: number;
}
export interface IolClaimsInput {
  iolId: string;
  sort: EntitySortOptions<IolClaimSortOptions>;
  page: ClaimsPage;
  filters: IolClaimsFilters;
  useExactFilters?: boolean;
  userId?: string;
  projectId?: string;
  showUniquePatients?: boolean;
}

export interface IolClaimsResponse {
  type: string;
  count: number;
  items: IolClaim[];
}

export interface PeopleSearchInclusionExclusionAggregationResponse {
  aggregations: {
    filtered_institutions: {
      doc_count: number;
      filtered_ids: {
        doc_count: number;
        by_institution: {
          buckets: Array<AggregationBucket>;
        };
      };
    };
  };
}
export interface AggregationBucket {
  key: string;
  doc_count: number;
  to_parent: {
    doc_count: number;
  };
}

export interface DiversityHeatmapAggregationResponse {
  heatmap: {
    buckets: SubIcbAggregation[];
  };
}

export interface SubIcbAggregation {
  key: string;
  doc_count: number;
  nested_agg: {
    doc_count: number;
    filtered_matching: RaceEncounterCountAggregation;
  };
}

export interface RaceEncounterCountAggregation {
  doc_count: number;
  patientCountUnknown: PatientCount;
  patientCountBlack: PatientCount;
  patientCountAsian: PatientCount;
  patientCountWhite: PatientCount;
  patientCountOther: PatientCount;
  patientCountMixed: PatientCount;
}

export interface DiversityCategoryAggregation {
  category: string;
  count: number;
  percentage: number;
}

export interface DiversityDashboardAggregation {
  age: DiversityCategoryAggregation[];
  gender: DiversityCategoryAggregation[];
  education: DiversityCategoryAggregation[];
  nationality: DiversityCategoryAggregation[];
}

/**
 * @deprecated use NamedLocationDiversityHeatmapAggregationResponse
 */
export interface FranceDiversityHeatmapAggregationResponse {
  france_heatmap: {
    buckets: FranceHeatmapLocationAggregation[];
  };
}

export interface FranceHeatmapLocationAggregation {
  key: string;
  doc_count: number;
  nested_agg: {
    doc_count: number;
    filtered_matching: FranceNationalityEncounterCountAggregation;
  };
}

export interface FranceNationalityEncounterCountAggregation {
  doc_count: number;
  patientCountOceania: PatientCount;
  patientCountEastAsia: PatientCount;
  patientCountSouthAsia: PatientCount;
  patientCountFranceNative: PatientCount;
  patientCountSouthAmerica: PatientCount;
  patientCountNorthAmerica: PatientCount;
  patientCountEuropeanUnion: PatientCount;
  patientCountOtherEuropean: PatientCount;
  patientCountSoutheastAsia: PatientCount;
  patientCountCentralAmerica: PatientCount;
  patientCountCentralAndSouthAfrica: PatientCount;
  patientCountMiddleEastAndNorthAfrica: PatientCount;
}

export interface NamedLocationDiversityHeatmapAggregationResponse {
  france_heatmap?: {
    buckets: FranceHeatmapLocationAggregation[];
  };
  spain_heatmap?: {
    buckets: SpainHeatmapLocationAggregation[];
  };
}

export interface SpainHeatmapLocationAggregation {
  key: string;
  doc_count: number;
  nested_agg: {
    doc_count: number;
    filtered_matching: SpainNationalityEncounterCountAggregation;
  };
}

export interface SpainNationalityEncounterCountAggregation {
  doc_count: number;
  patientCountSpain: PatientCount;
  patientCountOther: PatientCount;
  patientCountUnknown: PatientCount;
  patientCountOceania: PatientCount;
  patientCountEastAsia: PatientCount;
  patientCountSouthAsia: PatientCount;
  patientCountCentralAsia: PatientCount;
  patientCountSouthAmerica: PatientCount;
  patientCountNorthAmerica: PatientCount;
  patientCountEuropeanUnion: PatientCount;
  patientCountOtherEuropean: PatientCount;
  patientCountSoutheastAsia: PatientCount;
  patientCountCentralAmerica: PatientCount;
  patientCountCentralAndSouthAfrica: PatientCount;
  patientCountMiddleEastAndNorthAfrica: PatientCount;
}

interface PatientCount {
  value: number;
}

export interface HeatmapAggregation {
  subIcbCode: string;
  count: number;
}

export interface HeatmapLocationAggregation {
  namedLocation: string;
  count: number;
}

export interface DiversityDashboardAggregationBucket {
  key: string;
  count: {
    value: number;
  };
}

export interface EuropeanDiversityDashboardAggregationResponse {
  location_filtered: {
    nationality_counts: {
      by_nationality: {
        buckets: DiversityDashboardAggregationBucket[];
      };
      total_nationality_count: {
        value: number;
      };
    };
    age_counts: {
      by_age: {
        buckets: DiversityDashboardAggregationBucket[];
      };
      total_age_count: {
        value: number;
      };
    };
    gender_counts: {
      by_gender: {
        buckets: DiversityDashboardAggregationBucket[];
      };
      total_gender_count: {
        value: number;
      };
    };
    education_counts: {
      by_education: {
        buckets: DiversityDashboardAggregationBucket[];
      };
      total_education_count: {
        value: number;
      };
    };
  };
}

export interface DiversityDashboardAggregationResponse {
  france_diversity_dashboard?: EuropeanDiversityDashboardAggregationResponse;
  spain_diversity_dashboard?: EuropeanDiversityDashboardAggregationResponse;
}

export interface ChildrenMatchedCountAggregation {
  doc_count: number;
  matching_children_count: {
    value: number;
  };
}

export interface TaggedInstitutionsSearchInput
  extends Omit<
    InstitutionsSearchInput,
    "userId" | "accessLevel" | "uniqueIols" | "exportsSelections"
  > {
  userId: string;
  tagIds?: string[];
  limit?: number;
  offset?: number;
}

export interface TaggedInstitution {
  id: string;
  groupH1dnOrganizationId?: string;
  masterOrganizationId?: number;
  name: string;
  location: LatLonGeoLocation;
  tagIds?: string[];
  address: {
    street1?: string | null;
    street2?: string | null;
    street3?: string | null;
    country?: string | null;
    countryCode?: string | null;
    city?: string | null;
    region?: string | null;
    regionCode?: string | null;
    postalCode?: string | null;
  };
  region: string;
  patientsDiversity?: PatientsDiversityRaceInfo | PatientsUkDiversityRaceInfo;
  patientsAge?: PatientsDiversityAgeInfo[];
  patientsSex?: PatientsDiversitySexInfo[];
  lat_long_precision?: number;
}

export interface TaggedInstitutionsSearchResponse {
  total: number;
  institutions: TaggedInstitution[];
}

export interface InstitutionNameSuggestInput
  extends Omit<
    InstitutionsSearchInput,
    "userId" | "accessLevel" | "uniqueIols" | "exportsSelections"
  > {
  userId: string;
}

export interface InstitutionNameSuggestResponse {
  total: number;
  institutions: Array<InstitutionNameSuggestResult>;
}

export interface InstitutionNameSuggestResult {
  id: string;
  institutionId?: number;
  groupH1dnOrganizationId?: string;
  masterOrganizationId?: number;
  name: string;
  address: {
    country?: string;
    countryCode?: string;
    city?: string;
    region?: string;
    regionCode?: string;
  };
  personCount: number;
  trialCount: number;
  website?: string;
}

export enum HeatMapZoomLevel {
  Level1Geography = "level1Geography",
  Level2Geography = "level2Geography",
  Level3Geography = "level2Geography"
}
