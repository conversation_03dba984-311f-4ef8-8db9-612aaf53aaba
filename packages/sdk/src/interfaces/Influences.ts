export interface CitationProfileSharedMetric {
  name: string;
  count: number;
}

export interface CitationProfileCardMetric {
  count: number;
  name: string;
  path: string;
}

export interface CitationProfilePublication {
  id: string;
  citationCount: number;
  date: string;
  journal: string;
  socialMediaCount: number;
  title: string;
}

export interface CitationProfile {
  id: string;
  fullName: string;
  citationCount: number;
  matchedPubs: number;
  publications: CitationProfilePublication[];
  sharedMetrics: CitationProfileSharedMetric[];
  cardMetrics: CitationProfileCardMetric[];
}

export interface CitationProfileResponse {
  cited: CitationProfile[];
  citedBy: CitationProfile[];
  fullName: string;
  fullName_cmn: string;
  fullName_jpn: string;
}
