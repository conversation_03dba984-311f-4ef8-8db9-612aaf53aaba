import {
  ScoredDocumentData,
  DocumentRanges,
  DocumentScoreRange
} from "./documentScores";
import { PaginationResponse } from "./pagination";
import { FOResultCardData, ResultCardData } from "./KOLResultInterfaces";
import { QueryIntentFilterValues } from "./QueryIntentFilterInterface";
import { AggregationsAggregate } from "@elastic/elasticsearch/lib/api/types";

type Without<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

export enum GenericSearchEnum {
  AFFILIATION,
  CLINICALTRIAL,
  CONGRESS,
  PUBLICATION,
  GRANTS,
  PAYMENTS,
  PERSONS,
  GENERIC,
  TAG,
  CLAIM,
  DIAGNOSES,
  PROCEDURES
}

// @tagsPerformanceIssue
interface InjectedTagAssignmentUser {
  __typename: "User";
  id: string;
  firstName: string;
  lastName: string;
}

// @tagsPerformanceIssue
interface TagAssignmentTag {
  __typename: "Tag";
  id: string;
  name: string;
  description: string | null;
  type: "public" | "private";
  createdAt: string;
  color: string | null;
  createdBy: InjectedTagAssignmentUser;
}

// @tagsPerformanceIssue
interface InjectedTagAssignment {
  __typename: "TagAssignment";
  id: string;
  assignedBy: InjectedTagAssignmentUser;
  assignedAt: string;
  tag: TagAssignmentTag;
}
export type PublicationHighlightSegments = {
  publicationAbstract?: string;
  keywords?: string;
  title?: string;
};
export type PublicationsHighlights =
  | {
      publicationsId: string;
      highlight: PublicationHighlightSegments;
    }
  | undefined;

export type TrialHighlightSegments = {
  officialTitle?: string;
  briefTitle?: string;
  conditions?: string;
  interventions?: string;
  keywords?: string;
  summary?: string;
};
export type TrialHighlights =
  | {
      trialsId: string;
      highlight: TrialHighlightSegments;
    }
  | undefined;

export type CongressHighlightSegments = {
  keywords?: string;
  title?: string;
};

export type CongressHighlights =
  | {
      congressId: string;
      highlight: CongressHighlightSegments;
    }
  | undefined;

export type ClaimsHighlightSegments = {
  codeAndDescription?: string;
};

export type ClaimsHighlights = {
  highlight: ClaimsHighlightSegments;
};

export type AffiliationHighlightSegments = {
  institution?: {
    institutionId: string;
    address?: {
      city?: string;
      region?: string;
      country?: string;
    };
  };
};

export declare type AffiliationHighlights =
  | {
      affiliationId: string;
      highlight: AffiliationHighlightSegments;
    }
  | undefined;

export type Highlights = {
  publicationsHighlights?: PublicationsHighlights[];
  trialsHighlights?: TrialHighlights[];
  congressHighlights?: CongressHighlights[];
  claimsDiagnosesHighlights?: ClaimsHighlights[];
  claimsProceduresHighlights?: ClaimsHighlights[];
  affiliationHighlights?: AffiliationHighlights[];
};

export type PersonCard = Without<
  ResultCardData,
  "trendHistory" | "trendPresent"
> & {
  totalWorks: number;
  scores: ScoredDocumentData;
} & {
  // @tagsPerformanceIssue
  publicTagAssignments?: InjectedTagAssignment[];
  privateTagAssignments?: InjectedTagAssignment[];
  programmaticTags?: Array<{
    __typename: "ProgrammaticTag";
    id: string;
    name: string;
  }>;
} & Highlights;

export interface AggregationFilterCount {
  field: string;
  buckets: Array<{
    key: string;
    doc_count: number;
  }>;
}

export type PersonSearchDebug = {
  risingStar?: {
    overallRisingScore?: number;
    avgIndicationRisingScore?: number;
  };
};

export type PersonSearchResponse = PaginationResponse<PersonCard> & {
  ranges: DocumentRanges;
  normalizedRange: DocumentScoreRange;
  filterCounts: AggregationFilterCount[];
  synonyms?: string[];
  aggregations?: Record<string, AggregationsAggregate>;
  queryIntent?: QueryIntent[];
  spellSuggesterResult?: string | undefined;
  suggestedFilters?: QueryIntentFilterValues;
  icdCodeSynonyms?: string[];
  totalResultsOutsideUserSlice?: number;
  debugData?: PersonSearchDebug;
};

export type FOPersonSearchResponse = PaginationResponse<FOResultCardData>;

export enum QueryIntent {
  PERSON = "PERSON",
  DISEASE = "DISEASE",
  SPECIALIST = "SPECIALIST",
  INSTITUTION = "INSTITUTION",
  CONFERENCE = "CONFERENCE",
  NATURAL_LANGUAGE = "NATURAL_LANGUAGE"
}
