import { AppName } from "@h1nyc/account-sdk";
import { FilterInterface } from "./filterInterfaces";

export interface NameSearchInput {
  page: {
    from: number;
    size: number;
  };
  projectId: string;
  query?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  email?: string;
  suppliedFilters: FilterInterface;
  language?: string;
  projectFeatures: {
    claims: boolean;
    referrals: boolean;
    engagementsV2: boolean;
    searchMultiLanguage: boolean;
    translateTaiwan: boolean;
  };
  userId: string;
  appName?: AppName;
  hcpId?: string;
  useCongressContributorRanking?: boolean;
}
