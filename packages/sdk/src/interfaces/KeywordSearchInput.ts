import {
  FilterInterface,
  WeightedSortBy,
  HCPSearchScoringFunctions,
  HighlightCountPerAsset,
  SearchSliceOptionsEnum
} from "./filterInterfaces";
import { Apps } from "./Apps";

export interface KeywordSearchInput {
  page: {
    from: number;
    size: number;
  };
  projectId: string;
  query?: string;
  suppliedFilters: FilterInterface;
  sortBy: WeightedSortBy;
  language?: string;
  projectFeatures: {
    advancedOperators: boolean;
    claims: boolean;
    referrals: boolean;
    engagementsV2: boolean;
    translateTaiwan: boolean;
  };
  userId?: string;
  app?: Apps;
  scoringFunction?: HCPSearchScoringFunctions | null;
  disableQueryIntentFiltering?: boolean | null;
  numberOfHighlights?: HighlightCountPerAsset;
  sliceOption?: SearchSliceOptionsEnum;
}
