import { ClinicalTrialDocumentBase, GroupedByItem } from "../clinicalTrials";
import {
  FacilityV4,
  IdentifierV4,
  PersonHistoryV4,
  PersonV4,
  RoleHistoryV4,
  StatusHistoryV4,
  StudyV4
} from "./ClinicalTrialsSchemaV4";

export interface ClinicalTrialDocumentWithCTMS
  extends ClinicalTrialDocumentBase {
  h1_clinical_trial_id?: string;
  h1dn_clinical_trial_id?: string;
  project_id?: string;
  is_deprecated?: boolean;
  survivor_h1_clinical_trial_id?: string;
  version?: string;
  effective_date?: string;
  facility?: FacilityV5[];
  identifiers?: IdentifierV4[];
  person?: PersonV5[];
  study?: StudyV4;
  ctms?: CTMS[];
  hasCTMSData?: boolean;
  generic_drug_names?: string[];
  inclusion_generic_drug_names?: string[];
  exclusion_generic_drug_names?: string[];
  biomarker_inclusion?: string[];
  biomarker_exclusion?: string[];
  indication_inclusions?: string[];
  indication_exclusions?: string[];
  indications?: string[];
  biomarker?: string[];
}

export interface PersonHistoryV5
  extends Omit<PersonHistoryV4, "facility_person_hash" | "study_person_hash"> {
  facility_person_hash?: string;
  study_person_hash?: string;
  h1_person_id?: number;
}

export interface StatusHistoryV5
  extends Omit<StatusHistoryV4, "facility_hash"> {
  facility_hash?: string;
}

export interface FacilityV5
  extends Omit<
    FacilityV4,
    | "study_organization_hash"
    | "personHistory"
    | "statusHistory"
    | "external_uuid"
  > {
  external_uuid?: string;
  h1dn_organization_id?: string;
  group_h1dn_organization_id?: string;
  personHistory?: PersonHistoryV5[];
  statusHistory?: StatusHistoryV5[];
  study_organization_hash?: string;
  associatedBy?: AssociatedBy[];
}

export interface RoleHistoryV5
  extends Omit<RoleHistoryV4, "study_person_role_hash"> {
  study_person_role_hash?: string;
}

export interface TrialPublication {
  id: string;
  title: string;
  pmid?: string;
}

export enum AssociatedBy {
  PUBLICATION = "PUBLICATION",
  TRIAL = "TRIAL",
  PAYMENT = "PAYMENTS"
}

export interface PersonV5
  extends Omit<
    PersonV4,
    "external_uuid" | "study_person_hash" | "roleHistory"
  > {
  external_uuid?: string;
  h1dn_person_id?: string;
  group_h1dn_person_id?: string;
  roleHistory?: RoleHistoryV5[];
  study_person_hash?: string;
  associatedBy?: AssociatedBy[];
  related_publications?: TrialPublication[];
}

export interface Registry {
  id: string;
  name: string;
}

export interface CTMS {
  h1dn_clinical_trial_id: string;
  name: string;
  project_id: string;
  sites: CTMSSite[];
  status?: string;
  registry?: Registry;
}

export interface CTMSSite {
  account_name?: string;
  account_number?: string;
  activation_date?: string;
  actual_completed_count?: number;
  actual_date_of_fsfv?: Date;
  actual_date_of_fslv?: Date;
  actual_date_of_lsfv?: Date;
  actual_date_of_lslv?: Date;
  address?: CTMSAddress;
  client_id: string;
  closed_date?: Date;
  country_status?: string;
  enrolled_count?: number;
  h1dn_id: string;
  h1_organization_id?: string;
  group_h1dn_organization_id?: string;
  investigator?: CTMSInvestigator;
  name?: string;
  planned_activation_date?: Date;
  planned_closed_date?: Date;
  planned_date_of_fsfv?: Date;
  planned_date_of_fslv?: Date;
  planned_date_of_lsfv?: Date;
  planned_date_of_lslv?: Date;
  pre_screen_failure_count?: number;
  pre_screened_count?: number;
  primary_contact_name?: string;
  project_id: string;
  randomised_count?: number;
  run_in_count?: number;
  run_in_failure_count?: number;
  screen_failure_count?: number;
  screened_count?: number;
  status?: string;
  withdrawn_count?: number;
}

export interface CTMSAddress {
  country?: string;
  line1?: string;
  locality?: string;
}

export interface CTMSInvestigator {
  first_name: string;
  full_name: string;
  h1dn_id: string;
  h1_person_id?: string;
  group_h1dn_person_id?: string;
  last_name: string;
}

export interface IndicationCountsAggregation {
  indication_counts: {
    buckets: Array<GroupedByItem<string>>;
  };
}
