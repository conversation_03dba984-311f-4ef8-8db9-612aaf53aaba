// // generated using https://app.quicktype.io/
// based off json schema: https://github.com/h1insights/data-platform-infra/blob/main/teams/data-platform-ct/multi-env/glue/schemas/aact/topic/latest.v2.json

import {
  ClinicalTrialDocumentBase,
  EnrollmentHistory
} from "../clinicalTrials";

export interface ClinicalTrialDocumentV4 extends ClinicalTrialDocumentBase {
  h1_clinical_trial_id: string;
  is_deprecated: any;
  survivor_h1_clinical_trial_id?: string;
  version: any;
  effective_date?: string;
  facility?: FacilityV4[];
  identifiers?: IdentifierV4[];
  person?: PersonV4[];
  study?: StudyV4;
  generic_drug_names?: string[];
  inclusion_generic_drug_names?: string[];
  exclusion_generic_drug_names?: string[];
  biomarker_inclusion?: string[];
  biomarker_exclusion?: string[];
  indication_inclusions?: string[];
  indication_exclusions?: string[];
  indications?: string[];
  biomarker?: string[];
}

export interface FacilityV4 {
  city?: string[];
  collection_source_id?: number;
  country?: string[];
  external_uuid: string;
  h1_clinical_trial_id?: string;
  h1_organization_id?: number;
  name?: string;
  personHistory?: PersonHistoryV4[];
  state?: string[];
  statusHistory?: StatusHistoryV4[];
  study_organization_hash: string;
  zip?: string;
}

export interface PersonHistoryV4 {
  collection_source_id?: number;
  end_date?: string;
  facility_person_hash: string;
  h1_clinical_trial_id?: string;
  hash_or_id?: string;
  role?: string;
  start_date?: string;
  study_organization_hash?: string;
  study_person_hash: string;
}

export interface StatusHistoryV4 {
  collection_source_id?: number;
  end_date?: string;
  facility_hash: string;
  h1_clinical_trial_id?: string;
  hash_or_id?: string;
  start_date?: string;
  status?: string;
  study_organization_hash?: string;
}

export interface IdentifierV4 {
  collection_source_id: number;
  external_uuid: string;
  h1_clinical_trial_id?: string;
}

export interface PersonV4 {
  collection_source_id?: number;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  h1_person_id?: number;
  name?: string;
  roleHistory?: RoleHistoryV4[];
  study_person_hash: string;
}

export interface RoleHistoryV4 {
  affiliation?: string;
  collection_source_id?: number;
  email?: string;
  end_date?: string;
  h1_clinical_trial_id?: string;
  hash_or_id?: string;
  phone?: string;
  role?: string;
  start_date?: string;
  study_person_hash?: string;
  study_person_role_hash: string;
}

export interface StudyV4 {
  acronym?: string;
  application_review?: ApplicationReviewV4;
  baseline_population?: string;
  biospec_description?: string;
  biospec_retention?: string;
  brief_summaries?: string[];
  brief_title?: string;
  browse_conditions?: string[];
  browse_interventions?: string[];
  collection_source_id?: number;
  completion_date?: string;
  completion_date_type?: string;
  completion_month_year?: string;
  condition?: ConditionV4[];
  created_at?: string;
  design?: DesignV4[];
  designGroup?: DesignGroupV4[];
  designOutcome?: DesignOutcomeV4[];
  detailed_descriptions?: string[];
  disposition_first_posted_date?: string;
  disposition_first_posted_date_type?: string;
  disposition_first_submitted_date?: string;
  disposition_first_submitted_qc_date?: string;
  eea_member_state?: EeaMemberStateV4[];
  effective_date?: string;
  eligibility?: EligibilityV4[];
  enrollment?: number;
  enrollment_eea?: string;
  enrollment_global?: string;
  enrollment_member_state?: string;
  enrollment_type?: string;
  estimated_enrollment?: EnrollmentHistory;
  actual_enrollment?: EnrollmentHistory;
  enrollment_history?: EnrollmentHistory[];
  ethics_committee?: EthicsCommitteeV4;
  expanded_access_type_individual?: boolean;
  expanded_access_type_intermediate?: boolean;
  expanded_access_type_treatment?: boolean;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  has_dmc?: boolean;
  has_expanded_access?: boolean;
  idInformation?: IDInformationV4[];
  independent_data_monitoring_committee?: boolean;
  intervention?: StudyInterventionV4[];
  ipd_access_criteria?: string;
  ipd_time_frame?: string;
  ipd_url?: string;
  is_fda_regulated_device?: boolean;
  is_fda_regulated_drug?: boolean;
  is_ppsd?: boolean;
  is_unapproved_device?: boolean;
  is_us_export?: boolean;
  keywords?: string[];
  last_known_status?: string;
  last_update_posted_date_type?: string;
  last_update_submitted_date?: string;
  last_update_submitted_qc_date?: string;
  latest?: LatestV4[];
  limitations_and_caveats?: string;
  link?: LinkV4[];
  multistate_target_duration_years?: number;
  nlm_download_date_description?: string;
  number_of_arms?: number;
  number_of_groups?: number;
  official_title?: string;
  outcome?: OutcomeV4[];
  overall_status?: string;
  participantFlow?: ParticipantFlowV4[];
  pediatric_investigation_plan?: boolean;
  pediatric_investigation_plan_decision_number?: string;
  pendingResult?: PendingResultV4[];
  phase?: string;
  plan_to_share_ipd?: string;
  plan_to_share_ipd_description?: string;
  primary_completion_date?: string;
  primary_completion_date_type?: string;
  primary_completion_month_year?: string;
  reference?: ReferenceV4[];
  resultAgreement?: ResultAgreementV4[];
  resultGroup?: ResultGroupV4[];
  results_first_posted_date?: string;
  results_first_posted_date_type?: string;
  results_first_submitted_date?: string;
  results_first_submitted_qc_date?: string;
  site_country_list?: string[];
  source?: string;
  sponsor?: SponsorV4[];
  sponsor_contact?: SponsorContactV4[];
  start_date?: string;
  start_date_type?: string;
  start_month_year?: string;
  study_first_posted_date?: string;
  study_first_posted_date_type?: string;
  study_first_submitted_date?: string;
  study_first_submitted_qc_date?: string;
  study_meddra?: StudyMeddraV4;
  study_subtype?: string;
  study_subtype_other?: string;
  study_type?: string;
  substudy?: boolean;
  substudy_details?: string;
  target_duration?: string;
  target_duration_global_days?: number;
  target_duration_global_days_study_enrollment_eea?: string;
  target_duration_global_months?: number;
  target_duration_global_years?: number;
  trial_end_definition?: string;
  updated_at?: string;
  verification_date?: string;
  verification_month_year?: string;
  why_stopped?: string;
  studyDirectors?: string[];
  patients_per_site_per_month?: number;
  enrollment_duration_in_months?: number;
  start_date_history?: {
    effective_date: string;
    start_date?: string;
  }[];
  primary_completion_date_history?: {
    effective_date: string;
    primary_completion_date?: string;
  }[];
  status_history?: {
    effective_date: string;
    status?: string;
  }[];
  completion_date_history?: {
    effective_date: string;
    completion_date?: string;
  }[];
}

export interface ApplicationReviewV4 {
  application_review_hash: string;
  ec_opinion?: string;
  ec_opinion_date?: string;
  ec_opinion_reason?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  nca_decision?: string;
  nca_decision_date?: string;
}

export interface ConditionV4 {
  condition_hash: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  name?: string;
  rare_disease?: boolean;
  therapy_area?: string;
}

export interface DesignV4 {
  allocation?: string;
  caregiver_masked?: boolean;
  collection_source_id?: number;
  controlled?: boolean;
  design_group_other_specification?: string;
  design_hash: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  intervention_model?: string;
  intervention_model_description?: string;
  intervention_model_otherdesc?: string;
  investigator_masked?: boolean;
  masking?: string;
  masking_description?: string;
  observational_model?: string;
  outcomes_assessor_masked?: boolean;
  primary_purpose?: string[];
  primary_purpose_otherdesc?: string;
  subject_masked?: boolean;
  time_perspective?: string;
}

export interface DesignGroupV4 {
  collection_source_id?: number;
  description?: string;
  design_group_hash: string;
  effective_date?: string;
  external_uuid: string;
  group_type?: string;
  h1_clinical_trial_id?: string;
  intervention?: DesignGroupInterventionV4[];
  title?: string;
}

export interface DesignGroupInterventionV4 {
  collection_source_id?: number;
  design_group_hash?: string;
  design_group_intervention_hash: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  intervention_hash: string;
}

export interface DesignOutcomeV4 {
  collection_source_id?: number;
  description?: string;
  design_outcome_hash: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  measure?: string;
  outcome_type?: string;
  population?: string;
  time_frame?: string;
}

export interface EeaMemberStateV4 {
  completion_date?: string;
  country?: string;
  number_of_sites?: number;
  overall_status?: string;
  planned_population?: number;
}

export interface EligibilityV4 {
  collection_source_id?: number;
  criteria?: string;
  effective_date?: string;
  eligibility_hash: string;
  exclusion_criteria_parsed?: string;
  external_uuid: string;
  gender?: string;
  gender_based?: boolean;
  gender_description?: string;
  h1_clinical_trial_id?: string;
  healthy_volunteers?: string;
  inclusion_criteria_parsed?: string;
  maximum_age?: string;
  minimum_age?: string;
  patients?: string;
  population?: string[];
  sampling_method?: string;
}

export interface EthicsCommitteeV4 {
  authorised_country?: string;
  effective_date?: string;
  ethics_committee_hash: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
}

export interface IDInformationV4 {
  collection_source_id?: number;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  id_information_hash: string;
  id_type?: string;
  id_value?: string;
}

export interface StudyInterventionV4 {
  active_substance_cas_number?: string;
  active_substance_concentration_number?: string;
  active_substance_concentration_type?: string;
  active_substance_concentration_unit?: string;
  active_substance_current_sponsor_code?: string;
  active_substance_inn?: string;
  active_substance_origin?: string;
  active_substance_other_name?: string;
  cat_classification_issued?: boolean;
  cat_classification_value?: string;
  category?: string;
  collection_source_id?: number;
  description?: string;
  effective_date?: string;
  external_uuid: string;
  formulation?: string;
  h1_clinical_trial_id?: string;
  intervention_hash: string;
  intervention_other_names?: string[];
  intervention_othertype_specification?: string;
  intervention_type?: string;
  marketing_authorisation?: boolean;
  marketing_authorisation_country?: string;
  marketing_authorisation_holder?: string;
  max_route_of_administration?: string;
  name?: string;
  orphan_drug?: boolean;
  orphan_drug_number?: string;
  pediatric_formulation?: boolean;
  placebo_form?: string;
  placebo_roa?: string;
  route_of_administration?: string;
}

export interface LatestV4 {
  effective_date: string;
  h1_clinical_trial_id?: string;
}

export interface LinkV4 {
  collection_source_id?: number;
  description?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  link_hash: string;
  url?: string;
}

export interface OutcomeV4 {
  analysis?: AnalysisV4[];
  anticipated_posting_date?: string;
  anticipated_posting_month_year?: string;
  collection_source_id?: number;
  count?: CountV4[];
  description?: string;
  dispersion_type?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  measurement?: MeasurementV4[];
  outcome_hash: string;
  outcome_type?: string;
  param_type?: string;
  population?: string;
  time_frame?: string;
  title?: string;
  units?: string;
  units_analyzed?: string;
}

export interface AnalysisV4 {
  ci_lower_limit?: number;
  ci_n_sides?: string;
  ci_percent?: number;
  ci_upper_limit?: number;
  ci_upper_limit_na_comment?: string;
  collection_source_id?: number;
  dispersion_type?: string;
  dispersion_value?: number;
  effective_date?: string;
  estimate_description?: string;
  external_uuid: string;
  group?: GroupV4[];
  groups_description?: string;
  h1_clinical_trial_id?: string;
  method?: string;
  method_description?: string;
  non_inferiority_description?: string;
  non_inferiority_type?: string;
  other_analysis_description?: string;
  outcome_analysis_hash: string;
  outcome_hash?: string;
  p_value?: number;
  p_value_description?: string;
  p_value_modifier?: number;
  param_type?: string;
  param_value?: number;
}

export interface GroupV4 {
  collection_source_id?: number;
  ctgov_group_code?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  outcome_analysis_group_hash: string;
  outcome_analysis_hash?: string;
  result_group_hash: string;
}

export interface CountV4 {
  collection_source_id?: number;
  count?: number;
  ctgov_group_code?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  outcome_count_hash: string;
  outcome_hash?: string;
  result_group_hash: string;
  scope?: string;
  units?: string;
}

export interface MeasurementV4 {
  category?: string;
  classification?: string;
  collection_source_id?: number;
  ctgov_group_code?: string;
  description?: string;
  dispersion_lower_limit?: number;
  dispersion_type?: string;
  dispersion_upper_limit?: number;
  dispersion_value?: string;
  dispersion_value_num?: number;
  effective_date?: string;
  explanation_of_na?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  outcome_hash?: string;
  outcome_measurement_hash: string;
  param_type?: string;
  param_value?: string;
  param_value_num?: number;
  result_group_hash: string;
  title?: string;
  units?: string;
}

export interface ParticipantFlowV4 {
  collection_source_id?: number;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  participant_flow_hash: string;
  pre_assignment_details?: string;
  recruitment_details?: string;
}

export interface PendingResultV4 {
  collection_source_id?: number;
  effective_date?: string;
  event?: string;
  event_date?: string;
  event_date_description?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  pending_result_hash: string;
}

export interface ReferenceV4 {
  citation?: string;
  collection_source_id?: number;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  pmid?: number;
  reference_type?: string;
  study_reference_hash: string;
}

export interface ResultAgreementV4 {
  agreement?: string;
  collection_source_id?: number;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  other_details?: string;
  pi_employee?: string;
  restriction_type?: string;
  restrictive_agreement?: string;
  result_agreement_hash: string;
}

export interface ResultGroupV4 {
  baselineCount?: BaselineCountV4[];
  baselineMeasurement?: BaselineMeasurementV4[];
  collection_source_id?: number;
  ctgov_group_code?: string;
  description?: string;
  dropWithdrawal?: DropWithdrawalV4[];
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  milestone?: MilestoneV4[];
  result_group_hash: string;
  result_type?: string;
  title?: string;
}

export interface BaselineCountV4 {
  baseline_count_hash: string;
  collection_source_id?: number;
  count?: number;
  ctgov_group_code?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  result_group_hash?: string;
  scope?: string;
  units?: string;
}

export interface BaselineMeasurementV4 {
  baseline_measurement_hash: string;
  category?: string;
  classification?: string;
  collection_source_id?: number;
  ctgov_group_code?: string;
  description?: string;
  dispersion_lower_limit?: number;
  dispersion_type?: string;
  dispersion_upper_limit?: number;
  dispersion_value?: string;
  dispersion_value_num?: number;
  effective_date?: string;
  explanation_of_na?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  param_type?: string;
  param_value?: string;
  param_value_num?: number;
  result_group_hash?: string;
  title?: string;
  units?: string;
}

export interface DropWithdrawalV4 {
  collection_source_id?: number;
  count?: number;
  ctgov_group_code?: string;
  drop_withdrawal_hash: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  period?: string;
  reason?: string;
  result_group_hash?: string;
}

export interface MilestoneV4 {
  collection_source_id?: number;
  count?: number;
  ctgov_group_code?: string;
  description?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  milestone_hash: string;
  period?: string;
  result_group_hash?: string;
  title?: string;
}

export interface SponsorV4 {
  agency_class?: string;
  collection_source_id?: number;
  country?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  is_organization?: boolean;
  is_responsible_party?: boolean;
  lead_or_collaborator?: string;
  name?: string;
  sponsor_hash: string;
  status?: string;
  study_organization_hash?: string;
  study_person_hash?: string;
}

export interface SponsorContactV4 {
  address?: string;
  affiliation?: string;
  city?: string;
  country?: string;
  email?: string;
  external_uuid: string;
  fax?: string;
  h1_clinical_trial_id?: string;
  name?: string;
  phone?: string;
  sponsor_contact_hash: string;
  zip?: string;
}

export interface StudyMeddraV4 {
  classification_code?: string;
  effective_date?: string;
  external_uuid: string;
  h1_clinical_trial_id?: string;
  level?: string;
  study_meddra_hash: string;
  system_organ_class?: string;
  term?: string;
  version?: string;
}
