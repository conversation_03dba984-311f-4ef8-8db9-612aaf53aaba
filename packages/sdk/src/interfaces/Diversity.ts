export interface Diversity {
  race: string[];
  sex: string[];
}

export interface ProviderExportDiversityInfo {
  id: string;
  firstName: string;
  lastName: string;
  patientDiversity: PatientDiversity;
  providerDiversity: ProviderDiversity;
  patientsDiversityCount: number;
}

export interface PatientDiversity {
  races?: PatientRace[];
  age?: PatientAge[];
  sex?: PatientSex[];
}

export interface PatientSex {
  value: string;
  count: number;
  percent?: number;
}

export interface PatientAge {
  range: string;
  count: number;
  percent?: number;
}

export interface PatientRace {
  race: string;
  count: number;
  percent?: number;
}

export interface ProviderDiversity {
  races?: string[];
  sex?: string[];
  languagesSpoken?: string[];
}
export interface ProviderDiversitySearchResponse {
  races_eng?: string[];
  sex?: string[];
  languagesSpoken?: string[];
}

export interface PatientDiversitySearchResponse {
  races_eng?: PatientRace[];
  age?: PatientAge[];
  sex?: PatientSex[];
}

export enum DiversityRankingEnum {
  HIGH = "high",
  MEDIUM = "medium",
  LOW = "low"
}
