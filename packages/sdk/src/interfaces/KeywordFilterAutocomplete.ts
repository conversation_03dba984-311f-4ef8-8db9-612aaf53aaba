import { Apps } from "./Apps";
import {
  FilterInterface,
  SearchSliceOptionsEnum,
  WeightedSortBy
} from "./filterInterfaces";

export interface KeywordFilterAutocompleteInput {
  projectId: string;
  userId?: string;
  query?: string;
  filterField?: KeywordFilterAutocompleteFilterField;
  filterValue?: string;
  suppliedFilters: FilterInterface;
  designations?: string[];
  sortBy?: WeightedSortBy;
  language?: string;
  projectFeatures: {
    engagementsV2: boolean;
    searchMultiLanguage?: boolean;
  };
  sliceOption?: SearchSliceOptionsEnum;
  ccsrToExpand?: string;
  ccsrIcdOffset?: number;
  ccsrIcdSize?: number;
  app?: Apps;
}

interface Aggregation {
  id: string;
  count: number;
}

export interface KeywordFilterAggregation extends Aggregation {
  uniqueId?: string;
  ccsr?: string[];
  ccsrIcdSize?: string;
  locationsInRegion?: Aggregation[];
  regionsIn?: string[];
}

export enum KeywordFilterAutocompleteFilterField {
  CITY = 12,
  COUNTRY = 33,
  REGION = 30,
  POSTAL_CODE = 56,
  SPECIALTY = 10,
  DESIGNATION = 70,
  AFFILIATION = 57,
  MEDICAL_SCHOOL = 59,
  PUBLICATION_TYPE = 23,
  JOURNAL_NAME = 21,
  TRIAL_STATUS = 37,
  TRIAL_PHASE = 36,
  TRIAL_STUDY_TYPE = 38,
  TRIAL_SPONSOR_TYPE = 77,
  TRIAL_ID = 84,
  TRIAL_SPONSOR = 40,
  CONGRESS_NAME = 42,
  CONGRESS_ORGANIZER = 44,
  CONGRESS_CONTRIBUTOR_ROLE = 93,
  PAYMENTS_COMPANY = 15,
  PAYMENTS_DRUG_OR_DEVICE = 19,
  PAYMENTS_TYPE_OF_FUNDING = 14,
  PAYMENTS_CATEGORY = 81,
  DIAGNOSES = 50,
  PROCEDURES = 51,
  DRG_DIAGNOSES = 64,
  DRG_PROCEDURES = 65,
  REFERRALS_SERVICE_LINE = 60,
  DIVERSITY_PATIENT_DIVERSITY = 76,
  DIVERSITY_PATIENT_ETHNICITY = 94,
  DIVERSITY_PATIENT_AGE = 71,
  DIVERSITY_PATIENT_SEX = 73,
  DIVERSITY_PROVIDER_RACE = 75,
  DIVERSITY_PROVIDER_LANGUAGES = 74,
  DIVERSITY_PROVIDER_SEX = 80,
  SOCIETY_AFFILIATIONS = 82,
  GENERIC_NAME = 83,
  BRAND_NAME = 85,
  DRUG_CLASS = 86,
  TRIAL_ROLES = 88,
  BIOMARKERS = 87,
  CCSR = 89,
  CCSR_DIAGNOSES = 90,
  CCSR_PX = 91,
  CCSR_PROCEDURES = 92
}
