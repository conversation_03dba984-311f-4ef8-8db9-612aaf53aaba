import { InclusionExclusionRule } from "./rulesAndGroup";

export interface BaseDiversityStatsInput {
  query?: string;
  patientClaimsFilterV2?: InclusionExclusionRule;
}

export interface HcpDiversityStatsInput extends BaseDiversityStatsInput {
  hcpId: string;
}

export interface InstitutionDiversityStatsInput
  extends BaseDiversityStatsInput {
  masterOrganizationId: string;
}

export interface PatientDiversityStatsResponse {
  sex: DiversityStats[];
  race: DiversityStats[];
  education: DiversityStats[];
  age: DiversityStats[];
  income: DiversityStats[];
}

export interface DiversityStats {
  value: string;
  count: number;
  percentage: number;
}
