export interface NetworkRequest {
  readonly suppliedFilters: NetworkFilters;
  readonly projectFeatures: {
    advancedOperators: boolean;
  };
  readonly personId: string;
  readonly dateRange?: {
    min?: number | null;
    max?: number | null;
  };
  readonly page: {
    limit: number;
    offset: number;
  };
  readonly language: string;
  readonly projectId?: string | null;
  readonly sortBy?: NetworkSortBy | null;
  readonly userId?: string | null;
}

export interface BulkNetworkRequest extends Omit<NetworkRequest, "personId"> {
  readonly personIds: string[];
}

export interface NetworkCollaboratorRequest
  extends Omit<NetworkRequest, "page"> {
  readonly collaboratorId: string;
  readonly page: SharedWorksPage;
}

export interface NetworkFilterAutocompleteRequest
  extends Omit<NetworkRequest, "page"> {
  readonly filterField: NetworkFilterAutocompleteField;
  readonly filterValue: string;
}

export interface NetworkFilters {
  readonly collaborationType?: CollaborationTypeFilter;
  readonly collaboratorLocation?: LocationFilter;
  readonly keyword?: string;
  /**
   *  @deprecated use collaboratorLocation
   */
  readonly location?: string;
}

export interface CollaborationTypeFilter {
  readonly trials?: boolean;
  readonly congresses?: boolean;
  readonly publications?: boolean;
}

export interface LocationFilter {
  readonly country?: string[];
  readonly city?: string[];
  readonly region?: string[];
  readonly postalCode?: string[];
}

export interface NetworkResponse {
  totalCollaborators: number;
  totalPublicationCollaborators: number;
  totalCongressCollaborators: number;
  totalTrialCollaborators: number;
  collaborators: NetworkCollaborator[];
}

export interface BulkNetworkResponse {
  totalCollaborators: number;
  totalPublicationCollaborators: number;
  totalCongressCollaborators: number;
  totalTrialCollaborators: number;
}

export interface NetworkCollaborator {
  id: string;
  personId: string;
  totalSharedWorks: number;
  totalSharedPublications: number;
  totalSharedCongresses: number;
  totalSharedTrials: number;
  sharedAffiliations: NetworkAffiliation[];
  locations?: NetworkLocation[];
  sharedWorks?: Array<SharedTrial | SharedCongress | SharedPublication>;
  trialOffset?: number;
  congressOffset?: number;
  publicationOffset?: number;
}

export interface NetworkAffiliation {
  id: string;
  name: string;
  isCurrent: boolean;
  affiliationType: string | null;
  ultimateParentId: string | null;
}

export interface NetworkLocation {
  country: string | null;
  countryCode: string | null;
  city: string | null;
  region: string | null;
  regionCode: string | null;
  postalCode: string | null;
}

export interface SharedWork {
  id: string;
  title: string[];
  date: string[];
  persons: string[];
}

export interface SharedCongress extends SharedWork {
  name: string[];
  organizer: string[];
  type: string[];
}

export interface SharedPublication extends SharedWork {
  name: string[];
  type: string[];
}

export interface SharedTrial extends SharedWork {
  phase: string[];
  status: string[];
  sponsor: string[];
}

export interface SharedWorksPage {
  limit: number;
  trialOffset: number;
  congressOffset: number;
  publicationOffset: number;
}

export interface NetworkFilterAggregation {
  id: string;
  count: number;
}

export enum NetworkFilterAutocompleteField {
  CITY,
  COUNTRY,
  REGION,
  POSTAL_CODE
}

export enum NetworkSortBy {
  TOTAL_SHARED_WORKS,
  TRIALS,
  CONGRESSES,
  PUBLICATIONS,
  AFFILIATIONS,
  NAME
}
