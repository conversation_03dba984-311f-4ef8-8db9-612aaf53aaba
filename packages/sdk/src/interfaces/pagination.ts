import { TrialsAggregations } from "./clinicalTrials";
export interface Pagination {
  /**
   * The first index of the result set
   */
  from: number;
  /**
   * The page size
   */
  pageSize: number;
  /**
   * The total number of results
   */
  total: number;
}

export interface PaginationResponse<T> extends Pagination {
  results: T[];
  resultIds?: string[];
}

export interface TrialsPaginatedResponse<T> extends PaginationResponse<T> {
  aggregations?: TrialsAggregations;
  resultIds?: string[];
}

export interface PaginationOptions {
  limit?: number;
  offset?: number;
}
