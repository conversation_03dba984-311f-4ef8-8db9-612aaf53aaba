export const RPC_NAMESPACE_KOL_SEARCH = "searchKol";
export const RPC_NAMESPACE_KOL_AUTOCOMPLETE = "autocompleteKol";
export const RPC_NAMESPACE_KOL_SEARCH_REWRITE = "searchKolRewrite";
export const RPC_NAMESPACE_CLAIMS_SEARCH = "searchClaims";
export const RPC_NAMESPACE_PROFILE_SEARCH = "searchProfile";
export const RPC_NAMESPACE_PROFILE_SUMMARY = "summaryProfile";
export const RPC_NAMESPACE_INITIAL_OPTIONS = "searchInitialOptions";
export const RPC_NAMESPACE_NAME_SUGGEST = "searchNameSuggest";
export const RPC_NAMESPACE_COLLABORATORS = "searchCollaborators";
export const RPC_NAMESPACE_INFLUENCES = "searchInfluences";
export const RPC_NAMESPACE_CLINICAL_TRIALS = "searchClinicalTrials";
export const RPC_NAMESPACE_REFERRAL_AUTOSUGGEST = "searchReferralAutosuggest";
export const RPC_NAMESPACE_QUERY_PARSER = "searchQueryParser";
export const RPC_NAMESPACE_INSTITUTIONS = "searchInstitutions";
export const RPC_NAMESPACE_DIVERSITY = "searchDiversity";
export const RPC_NAMESPACE_CTMS = "searchCTMS";
export const RPC_NAMESPACE_NETWORK = "searchNetwork";
export const RPC_NAMESPACE_QUERY_UNDERSTANDING = "queryUnderstanding";
export const RPC_NAMESPACE_INDICATIONS_TREE_SEARCH = "indicationsTreeSearch";
export const RPC_NAMESPACE_NAME_SEARCH_REWRITE = "searchNameSearchRewrite";
export const RPC_NAMESPACE_INSTITUTION_NAME_SUGGEST =
  "searchInstitutionNameSuggest";
export const RPC_NAMESPACE_CONGRESS_SEARCH = "searchCongress";
export const RPC_NAMESPACE_CONGRESS_EVENT_PAGE_SEARCH =
  "searchCongressEventPage";
