type DateSearchFiltersInput = {
  min: number;
  max?: number | null;
};

type GraduationYearRangeFiltersInput = {
  min?: number | null;
  max?: number | null;
};

type ClaimFiltersInput = {
  diagnosesICDMinCount?: number | null;
  diagnosesICD?: string[] | null;
  proceduresCPTMinCount?: number | null;
  proceduresCPT?: string[] | null;
  proceduresHCPCMinCount?: number | null;
  proceduresHCPC?: string[] | null;
};

type TagFiltersInput = {
  privateTagIds?: string[] | null;
  publicTagIds?: string[] | null;
  programmaticTagIds?: string[] | null;
};

type PublicationFiltersInput = {
  socialMediaMinCount?: number | null;
  minCount?: number | null;
  journal?: string[] | null;
  type?: string[] | null;
};

type TrialFiltersInput = {
  minCount?: number | null;
  status?: string[] | null;
  phase?: string[] | null;
  studyType?: string[] | null;
  sponsor?: string[] | null;
  sponsorType?: string[] | null;
};

type CongressFiltersInput = {
  minCount?: number | null;
  name?: string[] | null;
  organizerName?: string[] | null;
};

type GrantFiltersInput = {
  minAmount?: number | null;
  funder?: string[] | null;
};

type PaymentFiltersInput = {
  minAmount?: number | null;
  company?: string[] | null;
  drugOrDevice?: string[] | null;
  fundingType?: string[] | null;
  category?: string[] | null;
};

type ReferralFiltersInput = {
  serviceLine?: string[] | null;
  minReferralsReceived?: number | null;
  minReferralsSent?: number | null;
};

type EngagementFiltersInput = {
  minCount?: number | null;
};

type SocialMediaFiltersInput = {
  hasLinkedin: boolean | null;
  hasTwitter: boolean | null;
};

type ProfileFiltersInput = {
  role?: string[] | null;
};

type SearchFiltersInput = {
  date: DateSearchFiltersInput;
  specialty?: string[] | null;
  state?: string[] | null;
  country?: string[] | null;
  city?: string[] | null;
  zipCode?: string[] | null;
  institution?: string[] | null;
  presentWorkInstitutions?: string[] | null;
  pastAndPresentWorkInstitutions?: string[] | null;
  studentInstitutions?: string[] | null;
  graduationYearRange?: GraduationYearRangeFiltersInput | null;
  npi?: string[] | null;
  tags?: TagFiltersInput | null;
  publications?: PublicationFiltersInput | null;
  profile?: ProfileFiltersInput | null;
  trials?: TrialFiltersInput | null;
  congresses?: CongressFiltersInput | null;
  claims?: ClaimFiltersInput | null;
  grants?: GrantFiltersInput | null;
  payments?: PaymentFiltersInput | null;
  referrals?: ReferralFiltersInput | null;
  engagements?: EngagementFiltersInput | null;
  socialMedia?: SocialMediaFiltersInput | null;
  hasCTMSData?: boolean | null;
};

type KolSearchSortWeightsInput = {
  publication: number;
  citation: number;
  microBloggingCount: number;
  trial: number;
  payment: number;
  congress: number;
  grant: number;
  diagnoses: number;
  procedures: number;
  referralsReceived: number;
  referralsSent: number;
  patientsDiversityRank: number;
};

export type KolNameSuggestionInput = {
  query?: string[] | null;
  filters?: SearchFiltersInput;
  sortBy?: KolSearchSortWeightsInput;
  limit: number;
  offset: number;
  language?: string;
};
