# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.30.14](https://github.com/shore-group/h1-search/compare/v1.30.13...v1.30.14) (2025-09-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.30.13](https://github.com/shore-group/h1-search/compare/v1.30.12...v1.30.13) (2025-09-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.30.12](https://github.com/shore-group/h1-search/compare/v1.30.11...v1.30.12) (2025-09-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.30.11](https://github.com/shore-group/h1-search/compare/v1.30.10...v1.30.11) (2025-09-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.30.10](https://github.com/shore-group/h1-search/compare/v1.30.9...v1.30.10) (2025-09-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.30.9](https://github.com/shore-group/h1-search/compare/v1.30.8...v1.30.9) (2025-09-22)

### Bug Fixes

- **global feasibility:** aggregation updates and field mapping ([#1936](https://github.com/shore-group/h1-search/issues/1936)) ([6350b26](https://github.com/shore-group/h1-search/commit/6350b268b6eed053d7e3af0c046ed513e93615e3))

## [1.30.8](https://github.com/shore-group/h1-search/compare/v1.30.7...v1.30.8) (2025-09-22)

### Reverts

- Revert "add: feature natural language search (#1933)" (#1940) ([5ea3e8d](https://github.com/shore-group/h1-search/commit/5ea3e8da4742c0042b8e40c8f962fb6c1eb59c11)), closes [#1933](https://github.com/shore-group/h1-search/issues/1933) [#1940](https://github.com/shore-group/h1-search/issues/1940)

## [1.30.7](https://github.com/shore-group/h1-search/compare/v1.30.6...v1.30.7) (2025-09-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.30.6](https://github.com/shore-group/h1-search/compare/v1.30.5...v1.30.6) (2025-09-17)

### Bug Fixes

- **global feasibility:** country size ([#1921](https://github.com/shore-group/h1-search/issues/1921)) ([cf463f2](https://github.com/shore-group/h1-search/commit/cf463f2e1d747ab7ab54ca329730d22fe7c18870))

## [1.30.5](https://github.com/shore-group/h1-search/compare/v1.30.4...v1.30.5) (2025-09-16)

### Reverts

- Revert "add: feature natural language search (#1914)" (#1932) ([c3de3f6](https://github.com/shore-group/h1-search/commit/c3de3f6b50ba043a125367b5fb2abbd0535d477c)), closes [#1914](https://github.com/shore-group/h1-search/issues/1914) [#1932](https://github.com/shore-group/h1-search/issues/1932)

## [1.30.4](https://github.com/shore-group/h1-search/compare/v1.30.3...v1.30.4) (2025-09-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.30.3](https://github.com/shore-group/h1-search/compare/v1.30.2...v1.30.3) (2025-09-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.30.2](https://github.com/shore-group/h1-search/compare/v1.30.1...v1.30.2) (2025-09-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.30.1](https://github.com/shore-group/h1-search/compare/v1.30.0...v1.30.1) (2025-09-11)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.30.0](https://github.com/shore-group/h1-search/compare/v1.29.9...v1.30.0) (2025-09-09)

### Features

- Add language support to indication search inputs when enableMultiLanguageTranslations flag enabled ([#1916](https://github.com/shore-group/h1-search/issues/1916)) ([67f42fe](https://github.com/shore-group/h1-search/commit/67f42fe16e4ca4c2bd331ebf0ab724ccf9417f67))

## [1.29.9](https://github.com/shore-group/h1-search/compare/v1.29.8...v1.29.9) (2025-09-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.29.8](https://github.com/shore-group/h1-search/compare/v1.29.7...v1.29.8) (2025-09-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.29.7](https://github.com/shore-group/h1-search/compare/v1.29.6...v1.29.7) (2025-09-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.29.6](https://github.com/shore-group/h1-search/compare/v1.29.5...v1.29.6) (2025-09-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.29.5](https://github.com/shore-group/h1-search/compare/v1.29.4...v1.29.5) (2025-09-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.29.4](https://github.com/shore-group/h1-search/compare/v1.29.3...v1.29.4) (2025-08-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.29.3](https://github.com/shore-group/h1-search/compare/v1.29.2...v1.29.3) (2025-08-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.29.2](https://github.com/shore-group/h1-search/compare/v1.29.1...v1.29.2) (2025-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.29.1](https://github.com/shore-group/h1-search/compare/v1.29.0...v1.29.1) (2025-08-26)

### Bug Fixes

- **global feasibility:** people aggs query ([#1912](https://github.com/shore-group/h1-search/issues/1912)) ([c272109](https://github.com/shore-group/h1-search/commit/c272109aee053eacce5b27e447fce536faa78a83))

# [1.29.0](https://github.com/shore-group/h1-search/compare/v1.28.0...v1.29.0) (2025-08-22)

### Features

- **global feasibility:** region aggregations ([#1896](https://github.com/shore-group/h1-search/issues/1896)) ([d291f3d](https://github.com/shore-group/h1-search/commit/d291f3dc7dd2c30c522b60f3f3630019bea0be22))

# [1.28.0](https://github.com/shore-group/h1-search/compare/v1.27.22...v1.28.0) (2025-08-22)

### Features

- **global feasibility:** country aggregation breakdown ([#1895](https://github.com/shore-group/h1-search/issues/1895)) ([7bd47a9](https://github.com/shore-group/h1-search/commit/7bd47a97feb072416b5812d136a84b63b3965d3f))

## [1.27.22](https://github.com/shore-group/h1-search/compare/v1.27.21...v1.27.22) (2025-08-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.21](https://github.com/shore-group/h1-search/compare/v1.27.20...v1.27.21) (2025-08-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.20](https://github.com/shore-group/h1-search/compare/v1.27.19...v1.27.20) (2025-08-21)

### Bug Fixes

- country region autocomplete fix ([#1905](https://github.com/shore-group/h1-search/issues/1905)) ([dbdfdae](https://github.com/shore-group/h1-search/commit/dbdfdaebfacb3fe697752690f8e833bd9fcb2bf0))

## [1.27.19](https://github.com/shore-group/h1-search/compare/v1.27.18...v1.27.19) (2025-08-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.18](https://github.com/shore-group/h1-search/compare/v1.27.17...v1.27.18) (2025-08-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.17](https://github.com/shore-group/h1-search/compare/v1.27.16...v1.27.17) (2025-08-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.16](https://github.com/shore-group/h1-search/compare/v1.27.15...v1.27.16) (2025-08-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.15](https://github.com/shore-group/h1-search/compare/v1.27.14...v1.27.15) (2025-08-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.14](https://github.com/shore-group/h1-search/compare/v1.27.13...v1.27.14) (2025-08-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.13](https://github.com/shore-group/h1-search/compare/v1.27.12...v1.27.13) (2025-08-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.12](https://github.com/shore-group/h1-search/compare/v1.27.11...v1.27.12) (2025-08-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.11](https://github.com/shore-group/h1-search/compare/v1.27.10...v1.27.11) (2025-08-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.10](https://github.com/shore-group/h1-search/compare/v1.27.9...v1.27.10) (2025-08-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.9](https://github.com/shore-group/h1-search/compare/v1.27.8...v1.27.9) (2025-08-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.8](https://github.com/shore-group/h1-search/compare/v1.27.7...v1.27.8) (2025-08-12)

### Bug Fixes

- removed path prefix ([#1892](https://github.com/shore-group/h1-search/issues/1892)) ([5ed1dff](https://github.com/shore-group/h1-search/commit/5ed1dff75586bfd73152b949c2f42f004c99f770))

## [1.27.7](https://github.com/shore-group/h1-search/compare/v1.27.6...v1.27.7) (2025-08-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.6](https://github.com/shore-group/h1-search/compare/v1.27.5...v1.27.6) (2025-08-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.5](https://github.com/shore-group/h1-search/compare/v1.27.4...v1.27.5) (2025-08-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.4](https://github.com/shore-group/h1-search/compare/v1.27.3...v1.27.4) (2025-08-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.3](https://github.com/shore-group/h1-search/compare/v1.27.2...v1.27.3) (2025-08-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.27.2](https://github.com/shore-group/h1-search/compare/v1.27.1...v1.27.2) (2025-08-05)

### Bug Fixes

- prescrtion-indication behind a ff ([#1885](https://github.com/shore-group/h1-search/issues/1885)) ([bb22ede](https://github.com/shore-group/h1-search/commit/bb22ede69a276804402555049c2520d4203bec54))

## [1.27.1](https://github.com/shore-group/h1-search/compare/v1.27.0...v1.27.1) (2025-08-04)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.27.0](https://github.com/shore-group/h1-search/compare/v1.26.10...v1.27.0) (2025-07-31)

### Features

- **my projects:** trial Intelligence graphs remove outliers ([#1882](https://github.com/shore-group/h1-search/issues/1882)) ([b7ebfad](https://github.com/shore-group/h1-search/commit/b7ebfad6d5ee4d0c117bae6eeccb2fb52b8cc351))

## [1.26.10](https://github.com/shore-group/h1-search/compare/v1.26.9...v1.26.10) (2025-07-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.26.9](https://github.com/shore-group/h1-search/compare/v1.26.8...v1.26.9) (2025-07-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.26.8](https://github.com/shore-group/h1-search/compare/v1.26.7...v1.26.8) (2025-07-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.26.7](https://github.com/shore-group/h1-search/compare/v1.26.6...v1.26.7) (2025-07-17)

### Bug Fixes

- scheme and description missing from the exports ([#1874](https://github.com/shore-group/h1-search/issues/1874)) ([feefb5b](https://github.com/shore-group/h1-search/commit/feefb5b470f94cbd1ffb0e8b7f5fef1daa330e14))

## [1.26.6](https://github.com/shore-group/h1-search/compare/v1.26.5...v1.26.6) (2025-07-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.26.5](https://github.com/shore-group/h1-search/compare/v1.26.4...v1.26.5) (2025-07-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.26.4](https://github.com/shore-group/h1-search/compare/v1.26.3...v1.26.4) (2025-07-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.26.3](https://github.com/shore-group/h1-search/compare/v1.26.2...v1.26.3) (2025-07-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.26.2](https://github.com/shore-group/h1-search/compare/v1.26.1...v1.26.2) (2025-07-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.26.1](https://github.com/shore-group/h1-search/compare/v1.26.0...v1.26.1) (2025-07-02)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.26.0](https://github.com/shore-group/h1-search/compare/v1.25.57...v1.26.0) (2025-07-02)

### Features

- **trial intelligence:** add support for histogram collection ([#1859](https://github.com/shore-group/h1-search/issues/1859)) ([93d5dc0](https://github.com/shore-group/h1-search/commit/93d5dc061480d0e57d8bbca192e79ac9b25beb08))

## [1.25.57](https://github.com/shore-group/h1-search/compare/v1.25.56...v1.25.57) (2025-07-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.56](https://github.com/shore-group/h1-search/compare/v1.25.55...v1.25.56) (2025-06-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.55](https://github.com/shore-group/h1-search/compare/v1.25.54...v1.25.55) (2025-06-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.54](https://github.com/shore-group/h1-search/compare/v1.25.53...v1.25.54) (2025-06-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.53](https://github.com/shore-group/h1-search/compare/v1.25.52...v1.25.53) (2025-06-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.52](https://github.com/shore-group/h1-search/compare/v1.25.51...v1.25.52) (2025-06-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.51](https://github.com/shore-group/h1-search/compare/v1.25.50...v1.25.51) (2025-06-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.50](https://github.com/shore-group/h1-search/compare/v1.25.49...v1.25.50) (2025-06-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.49](https://github.com/shore-group/h1-search/compare/v1.25.48...v1.25.49) (2025-06-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.48](https://github.com/shore-group/h1-search/compare/v1.25.47...v1.25.48) (2025-05-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.47](https://github.com/shore-group/h1-search/compare/v1.25.46...v1.25.47) (2025-05-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.46](https://github.com/shore-group/h1-search/compare/v1.25.45...v1.25.46) (2025-05-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.45](https://github.com/shore-group/h1-search/compare/v1.25.44...v1.25.45) (2025-05-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.44](https://github.com/shore-group/h1-search/compare/v1.25.43...v1.25.44) (2025-05-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.43](https://github.com/shore-group/h1-search/compare/v1.25.42...v1.25.43) (2025-05-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.42](https://github.com/shore-group/h1-search/compare/v1.25.41...v1.25.42) (2025-05-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.41](https://github.com/shore-group/h1-search/compare/v1.25.40...v1.25.41) (2025-05-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.40](https://github.com/shore-group/h1-search/compare/v1.25.39...v1.25.40) (2025-05-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.39](https://github.com/shore-group/h1-search/compare/v1.25.38...v1.25.39) (2025-05-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.38](https://github.com/shore-group/h1-search/compare/v1.25.37...v1.25.38) (2025-05-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.37](https://github.com/shore-group/h1-search/compare/v1.25.36...v1.25.37) (2025-05-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.36](https://github.com/shore-group/h1-search/compare/v1.25.35...v1.25.36) (2025-05-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.35](https://github.com/shore-group/h1-search/compare/v1.25.34...v1.25.35) (2025-05-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.34](https://github.com/shore-group/h1-search/compare/v1.25.33...v1.25.34) (2025-05-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.33](https://github.com/shore-group/h1-search/compare/v1.25.32...v1.25.33) (2025-05-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.32](https://github.com/shore-group/h1-search/compare/v1.25.31...v1.25.32) (2025-05-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.31](https://github.com/shore-group/h1-search/compare/v1.25.30...v1.25.31) (2025-05-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.30](https://github.com/shore-group/h1-search/compare/v1.25.29...v1.25.30) (2025-05-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.29](https://github.com/shore-group/h1-search/compare/v1.25.28...v1.25.29) (2025-05-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.28](https://github.com/shore-group/h1-search/compare/v1.25.27...v1.25.28) (2025-05-09)

### Bug Fixes

- mcp env issue ([#1823](https://github.com/shore-group/h1-search/issues/1823)) ([1ec3f8b](https://github.com/shore-group/h1-search/commit/1ec3f8b56331b196ca81c4545f572fa39b0ecf3f))

## [1.25.27](https://github.com/shore-group/h1-search/compare/v1.25.26...v1.25.27) (2025-05-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.26](https://github.com/shore-group/h1-search/compare/v1.25.25...v1.25.26) (2025-05-09)

### Bug Fixes

- env fix ([#1822](https://github.com/shore-group/h1-search/issues/1822)) ([3e2ccd2](https://github.com/shore-group/h1-search/commit/3e2ccd2e6aad2896bc7ccfd25f589bc561465499))

## [1.25.25](https://github.com/shore-group/h1-search/compare/v1.25.24...v1.25.25) (2025-05-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.24](https://github.com/shore-group/h1-search/compare/v1.25.23...v1.25.24) (2025-05-09)

### Bug Fixes

- mcp client issue ([#1818](https://github.com/shore-group/h1-search/issues/1818)) ([efdf6ed](https://github.com/shore-group/h1-search/commit/efdf6ed3373bb8b7062c2ae6ec947776928d86c2))

## [1.25.23](https://github.com/shore-group/h1-search/compare/v1.25.22...v1.25.23) (2025-05-09)

### Bug Fixes

- trial inner hits bug ([#1816](https://github.com/shore-group/h1-search/issues/1816)) ([f102ebf](https://github.com/shore-group/h1-search/commit/f102ebf1d06a3cfe3839d53bd8af10fbc65591f8))

## [1.25.22](https://github.com/shore-group/h1-search/compare/v1.25.21...v1.25.22) (2025-05-09)

### Bug Fixes

- ensure nested filters return an empty array when undefined ([#1815](https://github.com/shore-group/h1-search/issues/1815)) ([41302a8](https://github.com/shore-group/h1-search/commit/41302a842e27581d02ca414434408ff22e4540c8))

## [1.25.21](https://github.com/shore-group/h1-search/compare/v1.25.20...v1.25.21) (2025-05-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.20](https://github.com/shore-group/h1-search/compare/v1.25.19...v1.25.20) (2025-05-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.19](https://github.com/shore-group/h1-search/compare/v1.25.18...v1.25.19) (2025-05-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.18](https://github.com/shore-group/h1-search/compare/v1.25.17...v1.25.18) (2025-05-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.17](https://github.com/shore-group/h1-search/compare/v1.25.16...v1.25.17) (2025-05-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.16](https://github.com/shore-group/h1-search/compare/v1.25.15...v1.25.16) (2025-05-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.15](https://github.com/shore-group/h1-search/compare/v1.25.14...v1.25.15) (2025-05-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.14](https://github.com/shore-group/h1-search/compare/v1.25.13...v1.25.14) (2025-05-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.13](https://github.com/shore-group/h1-search/compare/v1.25.12...v1.25.13) (2025-05-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.12](https://github.com/shore-group/h1-search/compare/v1.25.11...v1.25.12) (2025-05-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.11](https://github.com/shore-group/h1-search/compare/v1.25.10...v1.25.11) (2025-05-05)

### Reverts

- Revert Node Version Upgrade (#1805) ([e440121](https://github.com/shore-group/h1-search/commit/e440121154acd912cd4bf50a00a5d419bdd63661)), closes [#1805](https://github.com/shore-group/h1-search/issues/1805) [#1799](https://github.com/shore-group/h1-search/issues/1799)

## [1.25.10](https://github.com/shore-group/h1-search/compare/v1.25.9...v1.25.10) (2025-05-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.9](https://github.com/shore-group/h1-search/compare/v1.25.8...v1.25.9) (2025-05-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.8](https://github.com/shore-group/h1-search/compare/v1.25.7...v1.25.8) (2025-05-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.7](https://github.com/shore-group/h1-search/compare/v1.25.6...v1.25.7) (2025-05-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.6](https://github.com/shore-group/h1-search/compare/v1.25.5...v1.25.6) (2025-04-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.5](https://github.com/shore-group/h1-search/compare/v1.25.4...v1.25.5) (2025-04-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.4](https://github.com/shore-group/h1-search/compare/v1.25.3...v1.25.4) (2025-04-28)

### Bug Fixes

- add both operator condition ([#1798](https://github.com/shore-group/h1-search/issues/1798)) ([67991ab](https://github.com/shore-group/h1-search/commit/67991ab336df2c2f2d76f088ef9412661217dc7f))

## [1.25.3](https://github.com/shore-group/h1-search/compare/v1.25.2...v1.25.3) (2025-04-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.2](https://github.com/shore-group/h1-search/compare/v1.25.1...v1.25.2) (2025-04-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.25.1](https://github.com/shore-group/h1-search/compare/v1.25.0...v1.25.1) (2025-04-25)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.25.0](https://github.com/shore-group/h1-search/compare/v1.24.14...v1.25.0) (2025-04-24)

### Features

- **org hierarchy:** search children aggregations ([#1794](https://github.com/shore-group/h1-search/issues/1794)) ([3eadd06](https://github.com/shore-group/h1-search/commit/3eadd06ac2168576129317b43e3934129d592b79))

## [1.24.14](https://github.com/shore-group/h1-search/compare/v1.24.13...v1.24.14) (2025-04-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.13](https://github.com/shore-group/h1-search/compare/v1.24.12...v1.24.13) (2025-04-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.12](https://github.com/shore-group/h1-search/compare/v1.24.11...v1.24.12) (2025-04-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.11](https://github.com/shore-group/h1-search/compare/v1.24.10...v1.24.11) (2025-04-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.10](https://github.com/shore-group/h1-search/compare/v1.24.9...v1.24.10) (2025-04-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.9](https://github.com/shore-group/h1-search/compare/v1.24.8...v1.24.9) (2025-04-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.8](https://github.com/shore-group/h1-search/compare/v1.24.7...v1.24.8) (2025-04-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.7](https://github.com/shore-group/h1-search/compare/v1.24.6...v1.24.7) (2025-04-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.6](https://github.com/shore-group/h1-search/compare/v1.24.5...v1.24.6) (2025-04-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.5](https://github.com/shore-group/h1-search/compare/v1.24.4...v1.24.5) (2025-04-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.4](https://github.com/shore-group/h1-search/compare/v1.24.3...v1.24.4) (2025-04-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.3](https://github.com/shore-group/h1-search/compare/v1.24.2...v1.24.3) (2025-04-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.2](https://github.com/shore-group/h1-search/compare/v1.24.1...v1.24.2) (2025-04-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.24.1](https://github.com/shore-group/h1-search/compare/v1.24.0...v1.24.1) (2025-04-14)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.24.0](https://github.com/shore-group/h1-search/compare/v1.23.101...v1.24.0) (2025-04-14)

### Features

- **org hierarchy:** update iol types and search response ([#1776](https://github.com/shore-group/h1-search/issues/1776)) ([042ba9f](https://github.com/shore-group/h1-search/commit/042ba9f6f89fb18bcdb10647328b8cabd78d03f0))

## [1.23.101](https://github.com/shore-group/h1-search/compare/v1.23.100...v1.23.101) (2025-04-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.100](https://github.com/shore-group/h1-search/compare/v1.23.99...v1.23.100) (2025-04-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.99](https://github.com/shore-group/h1-search/compare/v1.23.98...v1.23.99) (2025-04-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.98](https://github.com/shore-group/h1-search/compare/v1.23.97...v1.23.98) (2025-04-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.97](https://github.com/shore-group/h1-search/compare/v1.23.96...v1.23.97) (2025-04-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.96](https://github.com/shore-group/h1-search/compare/v1.23.95...v1.23.96) (2025-04-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.95](https://github.com/shore-group/h1-search/compare/v1.23.94...v1.23.95) (2025-04-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.94](https://github.com/shore-group/h1-search/compare/v1.23.93...v1.23.94) (2025-04-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.93](https://github.com/shore-group/h1-search/compare/v1.23.92...v1.23.93) (2025-04-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.92](https://github.com/shore-group/h1-search/compare/v1.23.91...v1.23.92) (2025-04-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.91](https://github.com/shore-group/h1-search/compare/v1.23.90...v1.23.91) (2025-04-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.90](https://github.com/shore-group/h1-search/compare/v1.23.89...v1.23.90) (2025-04-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.89](https://github.com/shore-group/h1-search/compare/v1.23.88...v1.23.89) (2025-04-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.88](https://github.com/shore-group/h1-search/compare/v1.23.87...v1.23.88) (2025-04-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.87](https://github.com/shore-group/h1-search/compare/v1.23.86...v1.23.87) (2025-04-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.86](https://github.com/shore-group/h1-search/compare/v1.23.85...v1.23.86) (2025-04-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.85](https://github.com/shore-group/h1-search/compare/v1.23.84...v1.23.85) (2025-04-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.84](https://github.com/shore-group/h1-search/compare/v1.23.83...v1.23.84) (2025-04-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.83](https://github.com/shore-group/h1-search/compare/v1.23.82...v1.23.83) (2025-04-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.82](https://github.com/shore-group/h1-search/compare/v1.23.81...v1.23.82) (2025-04-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.81](https://github.com/shore-group/h1-search/compare/v1.23.80...v1.23.81) (2025-04-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.80](https://github.com/shore-group/h1-search/compare/v1.23.79...v1.23.80) (2025-04-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.79](https://github.com/shore-group/h1-search/compare/v1.23.78...v1.23.79) (2025-04-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.78](https://github.com/shore-group/h1-search/compare/v1.23.77...v1.23.78) (2025-04-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.77](https://github.com/shore-group/h1-search/compare/v1.23.76...v1.23.77) (2025-04-02)

### Bug Fixes

- trial split counts ([#1755](https://github.com/shore-group/h1-search/issues/1755)) ([05bb232](https://github.com/shore-group/h1-search/commit/05bb232c3a41093b2caf35f6c2dea06f520d5cbf))

## [1.23.76](https://github.com/shore-group/h1-search/compare/v1.23.75...v1.23.76) (2025-04-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.75](https://github.com/shore-group/h1-search/compare/v1.23.74...v1.23.75) (2025-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.74](https://github.com/shore-group/h1-search/compare/v1.23.73...v1.23.74) (2025-03-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.73](https://github.com/shore-group/h1-search/compare/v1.23.72...v1.23.73) (2025-03-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.72](https://github.com/shore-group/h1-search/compare/v1.23.71...v1.23.72) (2025-03-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.71](https://github.com/shore-group/h1-search/compare/v1.23.70...v1.23.71) (2025-03-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.70](https://github.com/shore-group/h1-search/compare/v1.23.69...v1.23.70) (2025-03-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.69](https://github.com/shore-group/h1-search/compare/v1.23.68...v1.23.69) (2025-03-24)

### Bug Fixes

- enablePenalizeAutoCreatedIOLs flag causing queries to break ([#1744](https://github.com/shore-group/h1-search/issues/1744)) ([47ff0e2](https://github.com/shore-group/h1-search/commit/47ff0e25c07f8b34e4133c0ec44f56b0c7994221))

## [1.23.68](https://github.com/shore-group/h1-search/compare/v1.23.67...v1.23.68) (2025-03-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.67](https://github.com/shore-group/h1-search/compare/v1.23.66...v1.23.67) (2025-03-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.66](https://github.com/shore-group/h1-search/compare/v1.23.65...v1.23.66) (2025-03-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.65](https://github.com/shore-group/h1-search/compare/v1.23.64...v1.23.65) (2025-03-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.64](https://github.com/shore-group/h1-search/compare/v1.23.63...v1.23.64) (2025-03-20)

### Bug Fixes

- change to or for claims filters or patient filters ([#1738](https://github.com/shore-group/h1-search/issues/1738)) ([ec8c67b](https://github.com/shore-group/h1-search/commit/ec8c67b7dcfb60b773ebbdf1bab910aea065ce4d))

## [1.23.63](https://github.com/shore-group/h1-search/compare/v1.23.62...v1.23.63) (2025-03-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.62](https://github.com/shore-group/h1-search/compare/v1.23.61...v1.23.62) (2025-03-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.61](https://github.com/shore-group/h1-search/compare/v1.23.60...v1.23.61) (2025-03-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.60](https://github.com/shore-group/h1-search/compare/v1.23.59...v1.23.60) (2025-03-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.59](https://github.com/shore-group/h1-search/compare/v1.23.58...v1.23.59) (2025-03-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.58](https://github.com/shore-group/h1-search/compare/v1.23.57...v1.23.58) (2025-03-18)

### Bug Fixes

- fixed patient counts in sorts ([#1730](https://github.com/shore-group/h1-search/issues/1730)) ([fc61c39](https://github.com/shore-group/h1-search/commit/fc61c396e1dfcda73f2faab65cc8ec8d849523d5))

## [1.23.57](https://github.com/shore-group/h1-search/compare/v1.23.56...v1.23.57) (2025-03-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.56](https://github.com/shore-group/h1-search/compare/v1.23.55...v1.23.56) (2025-03-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.55](https://github.com/shore-group/h1-search/compare/v1.23.54...v1.23.55) (2025-03-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.54](https://github.com/shore-group/h1-search/compare/v1.23.53...v1.23.54) (2025-03-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.53](https://github.com/shore-group/h1-search/compare/v1.23.52...v1.23.53) (2025-03-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.52](https://github.com/shore-group/h1-search/compare/v1.23.51...v1.23.52) (2025-03-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.51](https://github.com/shore-group/h1-search/compare/v1.23.50...v1.23.51) (2025-03-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.50](https://github.com/shore-group/h1-search/compare/v1.23.49...v1.23.50) (2025-03-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.49](https://github.com/shore-group/h1-search/compare/v1.23.48...v1.23.49) (2025-03-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.48](https://github.com/shore-group/h1-search/compare/v1.23.47...v1.23.48) (2025-03-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.47](https://github.com/shore-group/h1-search/compare/v1.23.46...v1.23.47) (2025-03-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.46](https://github.com/shore-group/h1-search/compare/v1.23.45...v1.23.46) (2025-03-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.45](https://github.com/shore-group/h1-search/compare/v1.23.44...v1.23.45) (2025-03-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.44](https://github.com/shore-group/h1-search/compare/v1.23.43...v1.23.44) (2025-03-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.43](https://github.com/shore-group/h1-search/compare/v1.23.42...v1.23.43) (2025-03-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.42](https://github.com/shore-group/h1-search/compare/v1.23.41...v1.23.42) (2025-03-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.41](https://github.com/shore-group/h1-search/compare/v1.23.40...v1.23.41) (2025-03-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.40](https://github.com/shore-group/h1-search/compare/v1.23.39...v1.23.40) (2025-03-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.39](https://github.com/shore-group/h1-search/compare/v1.23.38...v1.23.39) (2025-03-04)

### Bug Fixes

- **ProfileTrialMetrics:** Add Terminated of Withdrawn ([#1707](https://github.com/shore-group/h1-search/issues/1707)) ([6e2ad86](https://github.com/shore-group/h1-search/commit/6e2ad8617382bbb26add8d632c360c52ed695a9c))

## [1.23.38](https://github.com/shore-group/h1-search/compare/v1.23.37...v1.23.38) (2025-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.37](https://github.com/shore-group/h1-search/compare/v1.23.36...v1.23.37) (2025-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.36](https://github.com/shore-group/h1-search/compare/v1.23.35...v1.23.36) (2025-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.35](https://github.com/shore-group/h1-search/compare/v1.23.34...v1.23.35) (2025-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.34](https://github.com/shore-group/h1-search/compare/v1.23.33...v1.23.34) (2025-02-28)

### Bug Fixes

- use of term clause instead of match ([#1699](https://github.com/shore-group/h1-search/issues/1699)) ([a0949a9](https://github.com/shore-group/h1-search/commit/a0949a9e1515b082aaa978b9250b05952e7f4a63))

## [1.23.33](https://github.com/shore-group/h1-search/compare/v1.23.32...v1.23.33) (2025-02-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.32](https://github.com/shore-group/h1-search/compare/v1.23.31...v1.23.32) (2025-02-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.31](https://github.com/shore-group/h1-search/compare/v1.23.30...v1.23.31) (2025-02-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.30](https://github.com/shore-group/h1-search/compare/v1.23.29...v1.23.30) (2025-02-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.29](https://github.com/shore-group/h1-search/compare/v1.23.28...v1.23.29) (2025-02-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.28](https://github.com/shore-group/h1-search/compare/v1.23.27...v1.23.28) (2025-02-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.27](https://github.com/shore-group/h1-search/compare/v1.23.26...v1.23.27) (2025-02-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.26](https://github.com/shore-group/h1-search/compare/v1.23.25...v1.23.26) (2025-02-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.25](https://github.com/shore-group/h1-search/compare/v1.23.24...v1.23.25) (2025-02-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.24](https://github.com/shore-group/h1-search/compare/v1.23.23...v1.23.24) (2025-02-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.23](https://github.com/shore-group/h1-search/compare/v1.23.22...v1.23.23) (2025-02-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.22](https://github.com/shore-group/h1-search/compare/v1.23.21...v1.23.22) (2025-02-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.21](https://github.com/shore-group/h1-search/compare/v1.23.20...v1.23.21) (2025-02-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.20](https://github.com/shore-group/h1-search/compare/v1.23.19...v1.23.20) (2025-02-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.19](https://github.com/shore-group/h1-search/compare/v1.23.18...v1.23.19) (2025-02-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.18](https://github.com/shore-group/h1-search/compare/v1.23.17...v1.23.18) (2025-02-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.17](https://github.com/shore-group/h1-search/compare/v1.23.16...v1.23.17) (2025-02-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.16](https://github.com/shore-group/h1-search/compare/v1.23.15...v1.23.16) (2025-02-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.15](https://github.com/shore-group/h1-search/compare/v1.23.14...v1.23.15) (2025-02-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.14](https://github.com/shore-group/h1-search/compare/v1.23.13...v1.23.14) (2025-02-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.13](https://github.com/shore-group/h1-search/compare/v1.23.12...v1.23.13) (2025-02-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.12](https://github.com/shore-group/h1-search/compare/v1.23.11...v1.23.12) (2025-02-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.11](https://github.com/shore-group/h1-search/compare/v1.23.10...v1.23.11) (2025-02-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.10](https://github.com/shore-group/h1-search/compare/v1.23.9...v1.23.10) (2025-02-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.9](https://github.com/shore-group/h1-search/compare/v1.23.8...v1.23.9) (2025-02-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.8](https://github.com/shore-group/h1-search/compare/v1.23.7...v1.23.8) (2025-02-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.7](https://github.com/shore-group/h1-search/compare/v1.23.6...v1.23.7) (2025-02-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.6](https://github.com/shore-group/h1-search/compare/v1.23.5...v1.23.6) (2025-02-19)

### Reverts

- Revert "matched indications (#1668)" (#1670) ([ef0f5ec](https://github.com/shore-group/h1-search/commit/ef0f5ec0e405dc74227f88a9ceaa65a97da51334)), closes [#1668](https://github.com/shore-group/h1-search/issues/1668) [#1670](https://github.com/shore-group/h1-search/issues/1670)

## [1.23.5](https://github.com/shore-group/h1-search/compare/v1.23.4...v1.23.5) (2025-02-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.4](https://github.com/shore-group/h1-search/compare/v1.23.3...v1.23.4) (2025-02-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.3](https://github.com/shore-group/h1-search/compare/v1.23.2...v1.23.3) (2025-02-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.2](https://github.com/shore-group/h1-search/compare/v1.23.1...v1.23.2) (2025-02-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.23.1](https://github.com/shore-group/h1-search/compare/v1.23.0...v1.23.1) (2025-02-13)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.23.0](https://github.com/shore-group/h1-search/compare/v1.22.57...v1.23.0) (2025-02-13)

### Features

- **trial indications:** add trials indications search support ([#1635](https://github.com/shore-group/h1-search/issues/1635)) ([4f31b02](https://github.com/shore-group/h1-search/commit/4f31b021e3010241a200f316824b4ffae379f664))

## [1.22.57](https://github.com/shore-group/h1-search/compare/v1.22.56...v1.22.57) (2025-02-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.56](https://github.com/shore-group/h1-search/compare/v1.22.55...v1.22.56) (2025-02-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.55](https://github.com/shore-group/h1-search/compare/v1.22.54...v1.22.55) (2025-02-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.54](https://github.com/shore-group/h1-search/compare/v1.22.53...v1.22.54) (2025-02-07)

### Bug Fixes

- **matched claims:** matched claims export ([#1648](https://github.com/shore-group/h1-search/issues/1648)) ([5ff70fc](https://github.com/shore-group/h1-search/commit/5ff70fc306c9526c368ce5a9b77ed1b8f500407a))

## [1.22.53](https://github.com/shore-group/h1-search/compare/v1.22.52...v1.22.53) (2025-02-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.52](https://github.com/shore-group/h1-search/compare/v1.22.51...v1.22.52) (2025-02-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.51](https://github.com/shore-group/h1-search/compare/v1.22.50...v1.22.51) (2025-02-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.50](https://github.com/shore-group/h1-search/compare/v1.22.49...v1.22.50) (2025-02-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.49](https://github.com/shore-group/h1-search/compare/v1.22.48...v1.22.49) (2025-02-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.48](https://github.com/shore-group/h1-search/compare/v1.22.47...v1.22.48) (2025-02-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.47](https://github.com/shore-group/h1-search/compare/v1.22.46...v1.22.47) (2025-02-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.46](https://github.com/shore-group/h1-search/compare/v1.22.45...v1.22.46) (2025-02-04)

### Bug Fixes

- **remove aggs:** remove old aggs ([#1645](https://github.com/shore-group/h1-search/issues/1645)) ([2be94e3](https://github.com/shore-group/h1-search/commit/2be94e38c15103e002f60292bafd093eefe3afc7))

## [1.22.45](https://github.com/shore-group/h1-search/compare/v1.22.44...v1.22.45) (2025-02-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.44](https://github.com/shore-group/h1-search/compare/v1.22.43...v1.22.44) (2025-02-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.43](https://github.com/shore-group/h1-search/compare/v1.22.42...v1.22.43) (2025-02-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.42](https://github.com/shore-group/h1-search/compare/v1.22.41...v1.22.42) (2025-01-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.41](https://github.com/shore-group/h1-search/compare/v1.22.40...v1.22.41) (2025-01-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.40](https://github.com/shore-group/h1-search/compare/v1.22.39...v1.22.40) (2025-01-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.39](https://github.com/shore-group/h1-search/compare/v1.22.38...v1.22.39) (2025-01-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.38](https://github.com/shore-group/h1-search/compare/v1.22.37...v1.22.38) (2025-01-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.37](https://github.com/shore-group/h1-search/compare/v1.22.36...v1.22.37) (2025-01-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.36](https://github.com/shore-group/h1-search/compare/v1.22.35...v1.22.36) (2025-01-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.35](https://github.com/shore-group/h1-search/compare/v1.22.34...v1.22.35) (2025-01-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.34](https://github.com/shore-group/h1-search/compare/v1.22.33...v1.22.34) (2025-01-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.33](https://github.com/shore-group/h1-search/compare/v1.22.32...v1.22.33) (2025-01-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.32](https://github.com/shore-group/h1-search/compare/v1.22.31...v1.22.32) (2025-01-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.31](https://github.com/shore-group/h1-search/compare/v1.22.30...v1.22.31) (2025-01-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.30](https://github.com/shore-group/h1-search/compare/v1.22.29...v1.22.30) (2025-01-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.29](https://github.com/shore-group/h1-search/compare/v1.22.28...v1.22.29) (2025-01-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.28](https://github.com/shore-group/h1-search/compare/v1.22.27...v1.22.28) (2025-01-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.27](https://github.com/shore-group/h1-search/compare/v1.22.26...v1.22.27) (2025-01-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.26](https://github.com/shore-group/h1-search/compare/v1.22.25...v1.22.26) (2025-01-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.25](https://github.com/shore-group/h1-search/compare/v1.22.24...v1.22.25) (2025-01-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.24](https://github.com/shore-group/h1-search/compare/v1.22.23...v1.22.24) (2025-01-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.23](https://github.com/shore-group/h1-search/compare/v1.22.22...v1.22.23) (2025-01-09)

### Bug Fixes

- **trials autocomplete:** sponsors autocomplete counts ([#1601](https://github.com/shore-group/h1-search/issues/1601)) ([4ecd025](https://github.com/shore-group/h1-search/commit/4ecd02540916d7ca3537fd5af814acda5ebb3b73))

## [1.22.22](https://github.com/shore-group/h1-search/compare/v1.22.21...v1.22.22) (2025-01-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.21](https://github.com/shore-group/h1-search/compare/v1.22.20...v1.22.21) (2025-01-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.20](https://github.com/shore-group/h1-search/compare/v1.22.19...v1.22.20) (2025-01-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.19](https://github.com/shore-group/h1-search/compare/v1.22.18...v1.22.19) (2025-01-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.18](https://github.com/shore-group/h1-search/compare/v1.22.17...v1.22.18) (2024-12-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.17](https://github.com/shore-group/h1-search/compare/v1.22.16...v1.22.17) (2024-12-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.16](https://github.com/shore-group/h1-search/compare/v1.22.15...v1.22.16) (2024-12-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.15](https://github.com/shore-group/h1-search/compare/v1.22.14...v1.22.15) (2024-12-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.14](https://github.com/shore-group/h1-search/compare/v1.22.13...v1.22.14) (2024-12-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.13](https://github.com/shore-group/h1-search/compare/v1.22.12...v1.22.13) (2024-12-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.12](https://github.com/shore-group/h1-search/compare/v1.22.11...v1.22.12) (2024-12-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.11](https://github.com/shore-group/h1-search/compare/v1.22.10...v1.22.11) (2024-12-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.10](https://github.com/shore-group/h1-search/compare/v1.22.9...v1.22.10) (2024-12-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.9](https://github.com/shore-group/h1-search/compare/v1.22.8...v1.22.9) (2024-12-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.8](https://github.com/shore-group/h1-search/compare/v1.22.7...v1.22.8) (2024-12-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.7](https://github.com/shore-group/h1-search/compare/v1.22.6...v1.22.7) (2024-12-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.6](https://github.com/shore-group/h1-search/compare/v1.22.5...v1.22.6) (2024-12-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.5](https://github.com/shore-group/h1-search/compare/v1.22.4...v1.22.5) (2024-12-05)

### Bug Fixes

- **institution types:** or together iol types ([#1584](https://github.com/shore-group/h1-search/issues/1584)) ([baa56bc](https://github.com/shore-group/h1-search/commit/baa56bc05dcefa6074686af01e48a614f4f5c62d))

## [1.22.4](https://github.com/shore-group/h1-search/compare/v1.22.3...v1.22.4) (2024-12-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.3](https://github.com/shore-group/h1-search/compare/v1.22.2...v1.22.3) (2024-12-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.2](https://github.com/shore-group/h1-search/compare/v1.22.1...v1.22.2) (2024-12-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.22.1](https://github.com/shore-group/h1-search/compare/v1.22.0...v1.22.1) (2024-12-02)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.22.0](https://github.com/shore-group/h1-search/compare/v1.21.30...v1.22.0) (2024-12-02)

### Features

- **trial role filters:** people trial role filters ([#1564](https://github.com/shore-group/h1-search/issues/1564)) ([869baf3](https://github.com/shore-group/h1-search/commit/869baf3d90ba295ac1c6b3a7e4bd19a1188398bb))

## [1.21.30](https://github.com/shore-group/h1-search/compare/v1.21.29...v1.21.30) (2024-12-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.29](https://github.com/shore-group/h1-search/compare/v1.21.28...v1.21.29) (2024-11-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.28](https://github.com/shore-group/h1-search/compare/v1.21.27...v1.21.28) (2024-11-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.27](https://github.com/shore-group/h1-search/compare/v1.21.26...v1.21.27) (2024-11-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.26](https://github.com/shore-group/h1-search/compare/v1.21.25...v1.21.26) (2024-11-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.25](https://github.com/shore-group/h1-search/compare/v1.21.24...v1.21.25) (2024-11-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.24](https://github.com/shore-group/h1-search/compare/v1.21.23...v1.21.24) (2024-11-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.23](https://github.com/shore-group/h1-search/compare/v1.21.22...v1.21.23) (2024-11-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.22](https://github.com/shore-group/h1-search/compare/v1.21.21...v1.21.22) (2024-11-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.21](https://github.com/shore-group/h1-search/compare/v1.21.20...v1.21.21) (2024-11-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.20](https://github.com/shore-group/h1-search/compare/v1.21.19...v1.21.20) (2024-11-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.19](https://github.com/shore-group/h1-search/compare/v1.21.18...v1.21.19) (2024-11-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.18](https://github.com/shore-group/h1-search/compare/v1.21.17...v1.21.18) (2024-11-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.17](https://github.com/shore-group/h1-search/compare/v1.21.16...v1.21.17) (2024-11-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.16](https://github.com/shore-group/h1-search/compare/v1.21.15...v1.21.16) (2024-11-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.15](https://github.com/shore-group/h1-search/compare/v1.21.14...v1.21.15) (2024-11-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.14](https://github.com/shore-group/h1-search/compare/v1.21.13...v1.21.14) (2024-11-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.13](https://github.com/shore-group/h1-search/compare/v1.21.12...v1.21.13) (2024-11-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.12](https://github.com/shore-group/h1-search/compare/v1.21.11...v1.21.12) (2024-11-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.11](https://github.com/shore-group/h1-search/compare/v1.21.10...v1.21.11) (2024-11-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.10](https://github.com/shore-group/h1-search/compare/v1.21.9...v1.21.10) (2024-11-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.9](https://github.com/shore-group/h1-search/compare/v1.21.8...v1.21.9) (2024-11-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.8](https://github.com/shore-group/h1-search/compare/v1.21.7...v1.21.8) (2024-10-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.7](https://github.com/shore-group/h1-search/compare/v1.21.6...v1.21.7) (2024-10-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.6](https://github.com/shore-group/h1-search/compare/v1.21.5...v1.21.6) (2024-10-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.5](https://github.com/shore-group/h1-search/compare/v1.21.4...v1.21.5) (2024-10-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.4](https://github.com/shore-group/h1-search/compare/v1.21.3...v1.21.4) (2024-10-29)

### Bug Fixes

- **trials contacts:** fix contacts list to only include contacts ([#1532](https://github.com/shore-group/h1-search/issues/1532)) ([a88448d](https://github.com/shore-group/h1-search/commit/a88448d47ee7b2c47eb74647eb525237461a4436))

## [1.21.3](https://github.com/shore-group/h1-search/compare/v1.21.2...v1.21.3) (2024-10-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.2](https://github.com/shore-group/h1-search/compare/v1.21.1...v1.21.2) (2024-10-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.21.1](https://github.com/shore-group/h1-search/compare/v1.21.0...v1.21.1) (2024-10-28)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.21.0](https://github.com/shore-group/h1-search/compare/v1.20.33...v1.21.0) (2024-10-28)

### Features

- **trial sources:** add ISRCTN trials source ([#1529](https://github.com/shore-group/h1-search/issues/1529)) ([7c438c6](https://github.com/shore-group/h1-search/commit/7c438c6e94bb368bea5c846a312cee1fbaac662b))

## [1.20.33](https://github.com/shore-group/h1-search/compare/v1.20.32...v1.20.33) (2024-10-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.32](https://github.com/shore-group/h1-search/compare/v1.20.31...v1.20.32) (2024-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.31](https://github.com/shore-group/h1-search/compare/v1.20.30...v1.20.31) (2024-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.30](https://github.com/shore-group/h1-search/compare/v1.20.29...v1.20.30) (2024-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.29](https://github.com/shore-group/h1-search/compare/v1.20.28...v1.20.29) (2024-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.28](https://github.com/shore-group/h1-search/compare/v1.20.27...v1.20.28) (2024-10-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.27](https://github.com/shore-group/h1-search/compare/v1.20.26...v1.20.27) (2024-10-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.26](https://github.com/shore-group/h1-search/compare/v1.20.25...v1.20.26) (2024-10-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.25](https://github.com/shore-group/h1-search/compare/v1.20.24...v1.20.25) (2024-10-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.24](https://github.com/shore-group/h1-search/compare/v1.20.23...v1.20.24) (2024-10-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.23](https://github.com/shore-group/h1-search/compare/v1.20.22...v1.20.23) (2024-10-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.22](https://github.com/shore-group/h1-search/compare/v1.20.21...v1.20.22) (2024-10-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.21](https://github.com/shore-group/h1-search/compare/v1.20.20...v1.20.21) (2024-10-11)

### Bug Fixes

- **matched counts:** set up claims code filters in matched count calculations ([#1506](https://github.com/shore-group/h1-search/issues/1506)) ([90250d2](https://github.com/shore-group/h1-search/commit/90250d2c70aace1deb402feb47ec5e815bf51818))

## [1.20.20](https://github.com/shore-group/h1-search/compare/v1.20.19...v1.20.20) (2024-10-04)

### Bug Fixes

- **trial contacts:** Show and tag inactive contacts ([#1501](https://github.com/shore-group/h1-search/issues/1501)) ([be93854](https://github.com/shore-group/h1-search/commit/be9385493c6a7019ab52ee77286fa9a6f371d8a8))

## [1.20.19](https://github.com/shore-group/h1-search/compare/v1.20.18...v1.20.19) (2024-10-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.18](https://github.com/shore-group/h1-search/compare/v1.20.17...v1.20.18) (2024-10-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.17](https://github.com/shore-group/h1-search/compare/v1.20.16...v1.20.17) (2024-10-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.16](https://github.com/shore-group/h1-search/compare/v1.20.15...v1.20.16) (2024-10-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.15](https://github.com/shore-group/h1-search/compare/v1.20.14...v1.20.15) (2024-10-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.14](https://github.com/shore-group/h1-search/compare/v1.20.13...v1.20.14) (2024-09-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.13](https://github.com/shore-group/h1-search/compare/v1.20.12...v1.20.13) (2024-09-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.12](https://github.com/shore-group/h1-search/compare/v1.20.11...v1.20.12) (2024-09-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.11](https://github.com/shore-group/h1-search/compare/v1.20.10...v1.20.11) (2024-09-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.10](https://github.com/shore-group/h1-search/compare/v1.20.9...v1.20.10) (2024-09-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.9](https://github.com/shore-group/h1-search/compare/v1.20.8...v1.20.9) (2024-09-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.8](https://github.com/shore-group/h1-search/compare/v1.20.7...v1.20.8) (2024-09-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.7](https://github.com/shore-group/h1-search/compare/v1.20.6...v1.20.7) (2024-09-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.6](https://github.com/shore-group/h1-search/compare/v1.20.5...v1.20.6) (2024-09-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.5](https://github.com/shore-group/h1-search/compare/v1.20.4...v1.20.5) (2024-09-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.4](https://github.com/shore-group/h1-search/compare/v1.20.3...v1.20.4) (2024-09-11)

### Bug Fixes

- **site rescue:** updates for search when no ie ([#1478](https://github.com/shore-group/h1-search/issues/1478)) ([177db31](https://github.com/shore-group/h1-search/commit/177db31a430bd5e5b856be1863ee5173ec9accff))

## [1.20.3](https://github.com/shore-group/h1-search/compare/v1.20.2...v1.20.3) (2024-09-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.2](https://github.com/shore-group/h1-search/compare/v1.20.1...v1.20.2) (2024-09-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.20.1](https://github.com/shore-group/h1-search/compare/v1.20.0...v1.20.1) (2024-09-10)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.20.0](https://github.com/shore-group/h1-search/compare/v1.19.101...v1.20.0) (2024-09-10)

### Features

- **site rescue:** co affiliated hcps ([#1474](https://github.com/shore-group/h1-search/issues/1474)) ([da1b3b1](https://github.com/shore-group/h1-search/commit/da1b3b115375695cb9cf609ba48ed869f5959048))

## [1.19.101](https://github.com/shore-group/h1-search/compare/v1.19.100...v1.19.101) (2024-09-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.100](https://github.com/shore-group/h1-search/compare/v1.19.99...v1.19.100) (2024-09-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.99](https://github.com/shore-group/h1-search/compare/v1.19.98...v1.19.99) (2024-09-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.98](https://github.com/shore-group/h1-search/compare/v1.19.97...v1.19.98) (2024-09-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.97](https://github.com/shore-group/h1-search/compare/v1.19.96...v1.19.97) (2024-09-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.96](https://github.com/shore-group/h1-search/compare/v1.19.95...v1.19.96) (2024-09-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.95](https://github.com/shore-group/h1-search/compare/v1.19.94...v1.19.95) (2024-09-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.94](https://github.com/shore-group/h1-search/compare/v1.19.93...v1.19.94) (2024-08-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.93](https://github.com/shore-group/h1-search/compare/v1.19.92...v1.19.93) (2024-08-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.92](https://github.com/shore-group/h1-search/compare/v1.19.91...v1.19.92) (2024-08-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.91](https://github.com/shore-group/h1-search/compare/v1.19.90...v1.19.91) (2024-08-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.90](https://github.com/shore-group/h1-search/compare/v1.19.89...v1.19.90) (2024-08-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.89](https://github.com/shore-group/h1-search/compare/v1.19.88...v1.19.89) (2024-08-27)

### Bug Fixes

- **site rescue:** lat lon switch ([#1458](https://github.com/shore-group/h1-search/issues/1458)) ([463351a](https://github.com/shore-group/h1-search/commit/463351aa453c93d04fb13634882cae88df0f3cfe))

## [1.19.88](https://github.com/shore-group/h1-search/compare/v1.19.87...v1.19.88) (2024-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.87](https://github.com/shore-group/h1-search/compare/v1.19.86...v1.19.87) (2024-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.86](https://github.com/shore-group/h1-search/compare/v1.19.85...v1.19.86) (2024-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.85](https://github.com/shore-group/h1-search/compare/v1.19.84...v1.19.85) (2024-08-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.84](https://github.com/shore-group/h1-search/compare/v1.19.83...v1.19.84) (2024-08-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.83](https://github.com/shore-group/h1-search/compare/v1.19.82...v1.19.83) (2024-08-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.82](https://github.com/shore-group/h1-search/compare/v1.19.81...v1.19.82) (2024-08-14)

### Reverts

- Revert "Use totalPatientDocs when unique patient toggle is ON (#1449)" (#1450) ([ccd3060](https://github.com/shore-group/h1-search/commit/ccd3060f412e6ef2fb0d88852a2d57819cbd3d3e)), closes [#1449](https://github.com/shore-group/h1-search/issues/1449) [#1450](https://github.com/shore-group/h1-search/issues/1450)

## [1.19.81](https://github.com/shore-group/h1-search/compare/v1.19.80...v1.19.81) (2024-08-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.80](https://github.com/shore-group/h1-search/compare/v1.19.79...v1.19.80) (2024-08-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.79](https://github.com/shore-group/h1-search/compare/v1.19.78...v1.19.79) (2024-08-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.78](https://github.com/shore-group/h1-search/compare/v1.19.77...v1.19.78) (2024-08-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.77](https://github.com/shore-group/h1-search/compare/v1.19.75...v1.19.77) (2024-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.76](https://github.com/shore-group/h1-search/compare/v1.19.75...v1.19.76) (2024-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.75](https://github.com/shore-group/h1-search/compare/v1.19.74...v1.19.75) (2024-07-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.74](https://github.com/shore-group/h1-search/compare/v1.19.73...v1.19.74) (2024-07-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.73](https://github.com/shore-group/h1-search/compare/v1.19.72...v1.19.73) (2024-07-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.72](https://github.com/shore-group/h1-search/compare/v1.19.71...v1.19.72) (2024-07-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.71](https://github.com/shore-group/h1-search/compare/v1.19.70...v1.19.71) (2024-07-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.70](https://github.com/shore-group/h1-search/compare/v1.19.69...v1.19.70) (2024-07-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.69](https://github.com/shore-group/h1-search/compare/v1.19.68...v1.19.69) (2024-07-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.68](https://github.com/shore-group/h1-search/compare/v1.19.67...v1.19.68) (2024-07-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.67](https://github.com/shore-group/h1-search/compare/v1.19.66...v1.19.67) (2024-07-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.66](https://github.com/shore-group/h1-search/compare/v1.19.65...v1.19.66) (2024-07-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.65](https://github.com/shore-group/h1-search/compare/v1.19.64...v1.19.65) (2024-07-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.64](https://github.com/shore-group/h1-search/compare/v1.19.63...v1.19.64) (2024-07-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.63](https://github.com/shore-group/h1-search/compare/v1.19.62...v1.19.63) (2024-07-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.62](https://github.com/shore-group/h1-search/compare/v1.19.61...v1.19.62) (2024-07-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.61](https://github.com/shore-group/h1-search/compare/v1.19.60...v1.19.61) (2024-07-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.60](https://github.com/shore-group/h1-search/compare/v1.19.59...v1.19.60) (2024-07-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.59](https://github.com/shore-group/h1-search/compare/v1.19.58...v1.19.59) (2024-07-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.58](https://github.com/shore-group/h1-search/compare/v1.19.57...v1.19.58) (2024-07-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.57](https://github.com/shore-group/h1-search/compare/v1.19.56...v1.19.57) (2024-07-10)

### Bug Fixes

- **prescriptions sort:** default prescriptions sort ([#1408](https://github.com/shore-group/h1-search/issues/1408)) ([4ee4d9d](https://github.com/shore-group/h1-search/commit/4ee4d9d12a8fba5358acddb8c3a583d751544a51))

## [1.19.56](https://github.com/shore-group/h1-search/compare/v1.19.55...v1.19.56) (2024-07-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.55](https://github.com/shore-group/h1-search/compare/v1.19.54...v1.19.55) (2024-07-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.54](https://github.com/shore-group/h1-search/compare/v1.19.53...v1.19.54) (2024-07-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.53](https://github.com/shore-group/h1-search/compare/v1.19.52...v1.19.53) (2024-07-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.52](https://github.com/shore-group/h1-search/compare/v1.19.51...v1.19.52) (2024-07-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.51](https://github.com/shore-group/h1-search/compare/v1.19.50...v1.19.51) (2024-07-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.50](https://github.com/shore-group/h1-search/compare/v1.19.49...v1.19.50) (2024-07-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.49](https://github.com/shore-group/h1-search/compare/v1.19.48...v1.19.49) (2024-06-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.48](https://github.com/shore-group/h1-search/compare/v1.19.47...v1.19.48) (2024-06-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.47](https://github.com/shore-group/h1-search/compare/v1.19.46...v1.19.47) (2024-06-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.46](https://github.com/shore-group/h1-search/compare/v1.19.45...v1.19.46) (2024-06-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.45](https://github.com/shore-group/h1-search/compare/v1.19.44...v1.19.45) (2024-06-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.44](https://github.com/shore-group/h1-search/compare/v1.19.43...v1.19.44) (2024-06-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.43](https://github.com/shore-group/h1-search/compare/v1.19.42...v1.19.43) (2024-06-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.42](https://github.com/shore-group/h1-search/compare/v1.19.41...v1.19.42) (2024-06-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.41](https://github.com/shore-group/h1-search/compare/v1.19.40...v1.19.41) (2024-06-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.40](https://github.com/shore-group/h1-search/compare/v1.19.39...v1.19.40) (2024-06-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.39](https://github.com/shore-group/h1-search/compare/v1.19.38...v1.19.39) (2024-06-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.38](https://github.com/shore-group/h1-search/compare/v1.19.37...v1.19.38) (2024-06-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.37](https://github.com/shore-group/h1-search/compare/v1.19.36...v1.19.37) (2024-06-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.36](https://github.com/shore-group/h1-search/compare/v1.19.35...v1.19.36) (2024-06-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.35](https://github.com/shore-group/h1-search/compare/v1.19.34...v1.19.35) (2024-06-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.34](https://github.com/shore-group/h1-search/compare/v1.19.33...v1.19.34) (2024-06-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.33](https://github.com/shore-group/h1-search/compare/v1.19.32...v1.19.33) (2024-06-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.32](https://github.com/shore-group/h1-search/compare/v1.19.31...v1.19.32) (2024-06-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.31](https://github.com/shore-group/h1-search/compare/v1.19.30...v1.19.31) (2024-06-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.30](https://github.com/shore-group/h1-search/compare/v1.19.29...v1.19.30) (2024-06-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.29](https://github.com/shore-group/h1-search/compare/v1.19.28...v1.19.29) (2024-06-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.28](https://github.com/shore-group/h1-search/compare/v1.19.27...v1.19.28) (2024-06-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.27](https://github.com/shore-group/h1-search/compare/v1.19.26...v1.19.27) (2024-06-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.26](https://github.com/shore-group/h1-search/compare/v1.19.25...v1.19.26) (2024-06-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.25](https://github.com/shore-group/h1-search/compare/v1.19.24...v1.19.25) (2024-06-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.24](https://github.com/shore-group/h1-search/compare/v1.19.23...v1.19.24) (2024-06-05)

### Reverts

- Revert "[SEARCH-770] Institution Diversity Ranking for Patient population (#1…" (#1361) ([68531cd](https://github.com/shore-group/h1-search/commit/68531cda30e939c6f70853f75c24a6614162a7d7)), closes [#1](https://github.com/shore-group/h1-search/issues/1) [#1361](https://github.com/shore-group/h1-search/issues/1361)
- Revert "[SEARCH-770] fix patient count for diversity ranking (#1359)" (#1360) ([35fb406](https://github.com/shore-group/h1-search/commit/35fb40605c6aae87468ebe9df45a162405b43bf2)), closes [#1359](https://github.com/shore-group/h1-search/issues/1359) [#1360](https://github.com/shore-group/h1-search/issues/1360)

## [1.19.23](https://github.com/shore-group/h1-search/compare/v1.19.22...v1.19.23) (2024-06-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.22](https://github.com/shore-group/h1-search/compare/v1.19.21...v1.19.22) (2024-06-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.21](https://github.com/shore-group/h1-search/compare/v1.19.20...v1.19.21) (2024-06-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.20](https://github.com/shore-group/h1-search/compare/v1.19.19...v1.19.20) (2024-06-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.19](https://github.com/shore-group/h1-search/compare/v1.19.18...v1.19.19) (2024-06-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.18](https://github.com/shore-group/h1-search/compare/v1.19.17...v1.19.18) (2024-05-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.17](https://github.com/shore-group/h1-search/compare/v1.19.16...v1.19.17) (2024-05-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.16](https://github.com/shore-group/h1-search/compare/v1.19.15...v1.19.16) (2024-05-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.15](https://github.com/shore-group/h1-search/compare/v1.19.14...v1.19.15) (2024-05-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.14](https://github.com/shore-group/h1-search/compare/v1.19.13...v1.19.14) (2024-05-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.13](https://github.com/shore-group/h1-search/compare/v1.19.12...v1.19.13) (2024-05-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.12](https://github.com/shore-group/h1-search/compare/v1.19.11...v1.19.12) (2024-05-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.11](https://github.com/shore-group/h1-search/compare/v1.19.10...v1.19.11) (2024-05-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.10](https://github.com/shore-group/h1-search/compare/v1.19.9...v1.19.10) (2024-05-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.9](https://github.com/shore-group/h1-search/compare/v1.19.8...v1.19.9) (2024-05-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.8](https://github.com/shore-group/h1-search/compare/v1.19.7...v1.19.8) (2024-05-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.7](https://github.com/shore-group/h1-search/compare/v1.19.6...v1.19.7) (2024-05-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.6](https://github.com/shore-group/h1-search/compare/v1.19.5...v1.19.6) (2024-05-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.5](https://github.com/shore-group/h1-search/compare/v1.19.4...v1.19.5) (2024-05-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.4](https://github.com/shore-group/h1-search/compare/v1.19.3...v1.19.4) (2024-05-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.3](https://github.com/shore-group/h1-search/compare/v1.19.2...v1.19.3) (2024-05-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.2](https://github.com/shore-group/h1-search/compare/v1.19.1...v1.19.2) (2024-05-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.19.1](https://github.com/shore-group/h1-search/compare/v1.19.0...v1.19.1) (2024-05-06)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.19.0](https://github.com/shore-group/h1-search/compare/v1.18.2...v1.19.0) (2024-05-06)

### Features

- **CTIS:** Add CTIS mappings ([#1329](https://github.com/shore-group/h1-search/issues/1329)) ([49f8c1a](https://github.com/shore-group/h1-search/commit/49f8c1a3beb2ad4fd3fde2a3595a3f693fb1b043))

## [1.18.2](https://github.com/shore-group/h1-search/compare/v1.18.1...v1.18.2) (2024-05-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.18.1](https://github.com/shore-group/h1-search/compare/v1.18.0...v1.18.1) (2024-05-02)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.18.0](https://github.com/shore-group/h1-search/compare/v1.17.6...v1.18.0) (2024-05-01)

### Features

- **trial affiliated by:** track affiliatedBy ([#1325](https://github.com/shore-group/h1-search/issues/1325)) ([1c4299f](https://github.com/shore-group/h1-search/commit/1c4299f5a57856f34d79e42620dd989980218aae))

## [1.17.6](https://github.com/shore-group/h1-search/compare/v1.17.5...v1.17.6) (2024-04-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.17.5](https://github.com/shore-group/h1-search/compare/v1.17.4...v1.17.5) (2024-04-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.17.4](https://github.com/shore-group/h1-search/compare/v1.17.3...v1.17.4) (2024-04-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.17.3](https://github.com/shore-group/h1-search/compare/v1.17.2...v1.17.3) (2024-04-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.17.2](https://github.com/shore-group/h1-search/compare/v1.17.1...v1.17.2) (2024-04-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.17.1](https://github.com/shore-group/h1-search/compare/v1.17.0...v1.17.1) (2024-04-08)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.17.0](https://github.com/shore-group/h1-search/compare/v1.16.59...v1.17.0) (2024-04-08)

### Features

- **person trials:** add publication affiliation tracking and pull out ctms trials ([#1319](https://github.com/shore-group/h1-search/issues/1319)) ([5972fa0](https://github.com/shore-group/h1-search/commit/5972fa02e13db446f014369d86414bd12c1e9cfd))

## [1.16.59](https://github.com/shore-group/h1-search/compare/v1.16.58...v1.16.59) (2024-04-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.58](https://github.com/shore-group/h1-search/compare/v1.16.57...v1.16.58) (2024-04-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.57](https://github.com/shore-group/h1-search/compare/v1.16.56...v1.16.57) (2024-04-04)

### Reverts

- Revert "search in diagnoses fields even though patient claims filter is applied" ([4e58425](https://github.com/shore-group/h1-search/commit/4e58425721013c0c678d33041be29df9cf2c7660))

## [1.16.56](https://github.com/shore-group/h1-search/compare/v1.16.55...v1.16.56) (2024-04-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.55](https://github.com/shore-group/h1-search/compare/v1.16.54...v1.16.55) (2024-04-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.54](https://github.com/shore-group/h1-search/compare/v1.16.53...v1.16.54) (2024-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.53](https://github.com/shore-group/h1-search/compare/v1.16.52...v1.16.53) (2024-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.52](https://github.com/shore-group/h1-search/compare/v1.16.51...v1.16.52) (2024-03-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.51](https://github.com/shore-group/h1-search/compare/v1.16.50...v1.16.51) (2024-03-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.50](https://github.com/shore-group/h1-search/compare/v1.16.49...v1.16.50) (2024-03-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.49](https://github.com/shore-group/h1-search/compare/v1.16.48...v1.16.49) (2024-03-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.48](https://github.com/shore-group/h1-search/compare/v1.16.47...v1.16.48) (2024-03-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.47](https://github.com/shore-group/h1-search/compare/v1.16.46...v1.16.47) (2024-03-21)

### Bug Fixes

- **trials aggregations:** remove recruiting trials from certain aggs ([#1309](https://github.com/shore-group/h1-search/issues/1309)) ([a37a373](https://github.com/shore-group/h1-search/commit/a37a3734aac19efd3458df9424b845e6d4229d0d))

## [1.16.46](https://github.com/shore-group/h1-search/compare/v1.16.45...v1.16.46) (2024-03-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.45](https://github.com/shore-group/h1-search/compare/v1.16.44...v1.16.45) (2024-03-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.44](https://github.com/shore-group/h1-search/compare/v1.16.43...v1.16.44) (2024-03-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.43](https://github.com/shore-group/h1-search/compare/v1.16.42...v1.16.43) (2024-03-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.42](https://github.com/shore-group/h1-search/compare/v1.16.41...v1.16.42) (2024-03-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.41](https://github.com/shore-group/h1-search/compare/v1.16.40...v1.16.41) (2024-03-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.40](https://github.com/shore-group/h1-search/compare/v1.16.39...v1.16.40) (2024-03-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.39](https://github.com/shore-group/h1-search/compare/v1.16.38...v1.16.39) (2024-03-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.38](https://github.com/shore-group/h1-search/compare/v1.16.37...v1.16.38) (2024-03-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.37](https://github.com/shore-group/h1-search/compare/v1.16.36...v1.16.37) (2024-03-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.36](https://github.com/shore-group/h1-search/compare/v1.16.35...v1.16.36) (2024-03-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.35](https://github.com/shore-group/h1-search/compare/v1.16.34...v1.16.35) (2024-03-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.34](https://github.com/shore-group/h1-search/compare/v1.16.33...v1.16.34) (2024-03-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.33](https://github.com/shore-group/h1-search/compare/v1.16.32...v1.16.33) (2024-03-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.32](https://github.com/shore-group/h1-search/compare/v1.16.31...v1.16.32) (2024-03-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.31](https://github.com/shore-group/h1-search/compare/v1.16.30...v1.16.31) (2024-02-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.30](https://github.com/shore-group/h1-search/compare/v1.16.29...v1.16.30) (2024-02-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.29](https://github.com/shore-group/h1-search/compare/v1.16.28...v1.16.29) (2024-02-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.28](https://github.com/shore-group/h1-search/compare/v1.16.27...v1.16.28) (2024-02-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.27](https://github.com/shore-group/h1-search/compare/v1.16.26...v1.16.27) (2024-02-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.26](https://github.com/shore-group/h1-search/compare/v1.16.25...v1.16.26) (2024-02-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.25](https://github.com/shore-group/h1-search/compare/v1.16.24...v1.16.25) (2024-02-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.24](https://github.com/shore-group/h1-search/compare/v1.16.23...v1.16.24) (2024-02-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.23](https://github.com/shore-group/h1-search/compare/v1.16.22...v1.16.23) (2024-02-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.22](https://github.com/shore-group/h1-search/compare/v1.16.21...v1.16.22) (2024-02-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.21](https://github.com/shore-group/h1-search/compare/v1.16.20...v1.16.21) (2024-02-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.20](https://github.com/shore-group/h1-search/compare/v1.16.19...v1.16.20) (2024-02-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.19](https://github.com/shore-group/h1-search/compare/v1.16.18...v1.16.19) (2024-02-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.18](https://github.com/shore-group/h1-search/compare/v1.16.17...v1.16.18) (2024-02-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.17](https://github.com/shore-group/h1-search/compare/v1.16.16...v1.16.17) (2024-02-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.16](https://github.com/shore-group/h1-search/compare/v1.16.15...v1.16.16) (2024-02-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.15](https://github.com/shore-group/h1-search/compare/v1.16.14...v1.16.15) (2024-02-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.14](https://github.com/shore-group/h1-search/compare/v1.16.13...v1.16.14) (2024-02-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.13](https://github.com/shore-group/h1-search/compare/v1.16.12...v1.16.13) (2024-02-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.12](https://github.com/shore-group/h1-search/compare/v1.16.11...v1.16.12) (2024-02-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.11](https://github.com/shore-group/h1-search/compare/v1.16.10...v1.16.11) (2024-02-09)

### Reverts

- Revert "Feat/update aggs median trails duration (#1261)" (#1266) ([659ec24](https://github.com/shore-group/h1-search/commit/659ec2419c23497c70024081de614df05bb9130d)), closes [#1261](https://github.com/shore-group/h1-search/issues/1261) [#1266](https://github.com/shore-group/h1-search/issues/1266)

## [1.16.10](https://github.com/shore-group/h1-search/compare/v1.16.9...v1.16.10) (2024-02-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.9](https://github.com/shore-group/h1-search/compare/v1.16.8...v1.16.9) (2024-02-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.8](https://github.com/shore-group/h1-search/compare/v1.16.7...v1.16.8) (2024-02-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.7](https://github.com/shore-group/h1-search/compare/v1.16.6...v1.16.7) (2024-02-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.6](https://github.com/shore-group/h1-search/compare/v1.16.5...v1.16.6) (2024-02-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.5](https://github.com/shore-group/h1-search/compare/v1.16.4...v1.16.5) (2024-02-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.4](https://github.com/shore-group/h1-search/compare/v1.16.3...v1.16.4) (2024-02-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.3](https://github.com/shore-group/h1-search/compare/v1.16.2...v1.16.3) (2024-01-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.2](https://github.com/shore-group/h1-search/compare/v1.16.1...v1.16.2) (2024-01-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.16.1](https://github.com/shore-group/h1-search/compare/v1.16.0...v1.16.1) (2024-01-30)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.16.0](https://github.com/shore-group/h1-search/compare/v1.15.11...v1.16.0) (2024-01-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.15.11](https://github.com/shore-group/h1-search/compare/v1.15.10...v1.15.11) (2024-01-22)

### Bug Fixes

- **iol autocomplete:** autocomplete name search bugfix ([#1244](https://github.com/shore-group/h1-search/issues/1244)) ([564cf0f](https://github.com/shore-group/h1-search/commit/564cf0fe3ae7900b91d919cb3af68e9869639135))

## [1.15.10](https://github.com/shore-group/h1-search/compare/v1.15.9...v1.15.10) (2024-01-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.15.9](https://github.com/shore-group/h1-search/compare/v1.15.8...v1.15.9) (2024-01-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.15.8](https://github.com/shore-group/h1-search/compare/v1.15.7...v1.15.8) (2024-01-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.15.7](https://github.com/shore-group/h1-search/compare/v1.15.6...v1.15.7) (2024-01-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.15.6](https://github.com/shore-group/h1-search/compare/v1.15.5...v1.15.6) (2024-01-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.15.5](https://github.com/shore-group/h1-search/compare/v1.15.4...v1.15.5) (2024-01-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.15.4](https://github.com/shore-group/h1-search/compare/v1.15.3...v1.15.4) (2024-01-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.15.3](https://github.com/shore-group/h1-search/compare/v1.15.2...v1.15.3) (2024-01-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.15.2](https://github.com/shore-group/h1-search/compare/v1.15.1...v1.15.2) (2024-01-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.15.1](https://github.com/shore-group/h1-search/compare/v1.15.0...v1.15.1) (2024-01-08)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.15.0](https://github.com/shore-group/h1-search/compare/v1.14.50...v1.15.0) (2023-12-21)

### Features

- **median values:** add support for median aggregations ([#1241](https://github.com/shore-group/h1-search/issues/1241)) ([046831b](https://github.com/shore-group/h1-search/commit/046831bdf220648688021ffbedb0ed2100a3c8b7))

## [1.14.50](https://github.com/shore-group/h1-search/compare/v1.14.49...v1.14.50) (2023-12-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.49](https://github.com/shore-group/h1-search/compare/v1.14.48...v1.14.49) (2023-12-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.48](https://github.com/shore-group/h1-search/compare/v1.14.47...v1.14.48) (2023-12-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.47](https://github.com/shore-group/h1-search/compare/v1.14.46...v1.14.47) (2023-12-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.46](https://github.com/shore-group/h1-search/compare/v1.14.45...v1.14.46) (2023-12-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.45](https://github.com/shore-group/h1-search/compare/v1.14.44...v1.14.45) (2023-12-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.44](https://github.com/shore-group/h1-search/compare/v1.14.43...v1.14.44) (2023-12-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.43](https://github.com/shore-group/h1-search/compare/v1.14.42...v1.14.43) (2023-12-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.42](https://github.com/shore-group/h1-search/compare/v1.14.41...v1.14.42) (2023-12-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.41](https://github.com/shore-group/h1-search/compare/v1.14.40...v1.14.41) (2023-12-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.40](https://github.com/shore-group/h1-search/compare/v1.14.39...v1.14.40) (2023-12-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.39](https://github.com/shore-group/h1-search/compare/v1.14.38...v1.14.39) (2023-12-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.38](https://github.com/shore-group/h1-search/compare/v1.14.37...v1.14.38) (2023-12-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.37](https://github.com/shore-group/h1-search/compare/v1.14.36...v1.14.37) (2023-11-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.36](https://github.com/shore-group/h1-search/compare/v1.14.35...v1.14.36) (2023-11-29)

### Reverts

- Revert "add rpc method to get tagged institutions to display on the map" ([ce7ee0d](https://github.com/shore-group/h1-search/commit/ce7ee0d58efb5e89fd2dcfcdd8af4d255d2e0d08))

## [1.14.35](https://github.com/shore-group/h1-search/compare/v1.14.34...v1.14.35) (2023-11-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.34](https://github.com/shore-group/h1-search/compare/v1.14.33...v1.14.34) (2023-11-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.33](https://github.com/shore-group/h1-search/compare/v1.14.32...v1.14.33) (2023-11-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.32](https://github.com/shore-group/h1-search/compare/v1.14.31...v1.14.32) (2023-11-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.31](https://github.com/shore-group/h1-search/compare/v1.14.30...v1.14.31) (2023-11-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.30](https://github.com/shore-group/h1-search/compare/v1.14.29...v1.14.30) (2023-11-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.29](https://github.com/shore-group/h1-search/compare/v1.14.28...v1.14.29) (2023-11-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.28](https://github.com/shore-group/h1-search/compare/v1.14.27...v1.14.28) (2023-11-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.27](https://github.com/shore-group/h1-search/compare/v1.14.26...v1.14.27) (2023-11-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.26](https://github.com/shore-group/h1-search/compare/v1.14.25...v1.14.26) (2023-11-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.25](https://github.com/shore-group/h1-search/compare/v1.14.24...v1.14.25) (2023-11-17)

### Reverts

- Revert "[SEARCH-699] Full Slice Search - remove project id 1 filter (#1210)" (#1212) ([cf04b19](https://github.com/shore-group/h1-search/commit/cf04b19fdd101924aa3e0da81c73999f7869fb3f)), closes [#1210](https://github.com/shore-group/h1-search/issues/1210) [#1212](https://github.com/shore-group/h1-search/issues/1212)

## [1.14.24](https://github.com/shore-group/h1-search/compare/v1.14.23...v1.14.24) (2023-11-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.23](https://github.com/shore-group/h1-search/compare/v1.14.22...v1.14.23) (2023-11-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.22](https://github.com/shore-group/h1-search/compare/v1.14.21...v1.14.22) (2023-11-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.21](https://github.com/shore-group/h1-search/compare/v1.14.20...v1.14.21) (2023-11-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.20](https://github.com/shore-group/h1-search/compare/v1.14.19...v1.14.20) (2023-11-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.19](https://github.com/shore-group/h1-search/compare/v1.14.18...v1.14.19) (2023-11-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.18](https://github.com/shore-group/h1-search/compare/v1.14.17...v1.14.18) (2023-11-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.17](https://github.com/shore-group/h1-search/compare/v1.14.16...v1.14.17) (2023-11-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.16](https://github.com/shore-group/h1-search/compare/v1.14.15...v1.14.16) (2023-11-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.15](https://github.com/shore-group/h1-search/compare/v1.14.14...v1.14.15) (2023-11-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.14](https://github.com/shore-group/h1-search/compare/v1.14.13...v1.14.14) (2023-11-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.13](https://github.com/shore-group/h1-search/compare/v1.14.12...v1.14.13) (2023-11-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.12](https://github.com/shore-group/h1-search/compare/v1.14.11...v1.14.12) (2023-11-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.11](https://github.com/shore-group/h1-search/compare/v1.14.10...v1.14.11) (2023-10-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.10](https://github.com/shore-group/h1-search/compare/v1.14.9...v1.14.10) (2023-10-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.9](https://github.com/shore-group/h1-search/compare/v1.14.8...v1.14.9) (2023-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.8](https://github.com/shore-group/h1-search/compare/v1.14.7...v1.14.8) (2023-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.7](https://github.com/shore-group/h1-search/compare/v1.14.6...v1.14.7) (2023-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.6](https://github.com/shore-group/h1-search/compare/v1.14.5...v1.14.6) (2023-10-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.5](https://github.com/shore-group/h1-search/compare/v1.14.4...v1.14.5) (2023-10-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.4](https://github.com/shore-group/h1-search/compare/v1.14.3...v1.14.4) (2023-10-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.3](https://github.com/shore-group/h1-search/compare/v1.14.2...v1.14.3) (2023-10-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.2](https://github.com/shore-group/h1-search/compare/v1.14.1...v1.14.2) (2023-10-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.14.1](https://github.com/shore-group/h1-search/compare/v1.14.0...v1.14.1) (2023-10-13)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.14.0](https://github.com/shore-group/h1-search/compare/v1.13.94...v1.14.0) (2023-10-12)

### Features

- **AuthorOrder:** Switch Author Order to Or condition ([#1176](https://github.com/shore-group/h1-search/issues/1176)) ([c311bf7](https://github.com/shore-group/h1-search/commit/c311bf7604ecaa696eab02a1d5f4af187206a4cc))
- **PublicationDate Filter:** Add publication date to keyword filters ([#1177](https://github.com/shore-group/h1-search/issues/1177)) ([ab2ee45](https://github.com/shore-group/h1-search/commit/ab2ee45d77689371e87d12b16665a32ab5e33d1f))

## [1.13.94](https://github.com/shore-group/h1-search/compare/v1.13.93...v1.13.94) (2023-10-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.93](https://github.com/shore-group/h1-search/compare/v1.13.92...v1.13.93) (2023-10-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.92](https://github.com/shore-group/h1-search/compare/v1.13.91...v1.13.92) (2023-10-11)

### Bug Fixes

- **IsIndustry/Active:** Pass the false value to query ([#1173](https://github.com/shore-group/h1-search/issues/1173)) ([503c0be](https://github.com/shore-group/h1-search/commit/503c0be636c056490ad2364944dc46324f430607))

## [1.13.91](https://github.com/shore-group/h1-search/compare/v1.13.90...v1.13.91) (2023-10-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.90](https://github.com/shore-group/h1-search/compare/v1.13.89...v1.13.90) (2023-10-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.89](https://github.com/shore-group/h1-search/compare/v1.13.88...v1.13.89) (2023-10-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.88](https://github.com/shore-group/h1-search/compare/v1.13.87...v1.13.88) (2023-10-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.87](https://github.com/shore-group/h1-search/compare/v1.13.86...v1.13.87) (2023-10-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.86](https://github.com/shore-group/h1-search/compare/v1.13.85...v1.13.86) (2023-10-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.85](https://github.com/shore-group/h1-search/compare/v1.13.84...v1.13.85) (2023-10-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.84](https://github.com/shore-group/h1-search/compare/v1.13.83...v1.13.84) (2023-10-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.83](https://github.com/shore-group/h1-search/compare/v1.13.82...v1.13.83) (2023-10-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.82](https://github.com/shore-group/h1-search/compare/v1.13.81...v1.13.82) (2023-10-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.81](https://github.com/shore-group/h1-search/compare/v1.13.80...v1.13.81) (2023-09-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.80](https://github.com/shore-group/h1-search/compare/v1.13.79...v1.13.80) (2023-09-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.79](https://github.com/shore-group/h1-search/compare/v1.13.78...v1.13.79) (2023-09-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.78](https://github.com/shore-group/h1-search/compare/v1.13.77...v1.13.78) (2023-09-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.77](https://github.com/shore-group/h1-search/compare/v1.13.76...v1.13.77) (2023-09-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.76](https://github.com/shore-group/h1-search/compare/v1.13.75...v1.13.76) (2023-09-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.75](https://github.com/shore-group/h1-search/compare/v1.13.74...v1.13.75) (2023-09-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.74](https://github.com/shore-group/h1-search/compare/v1.13.73...v1.13.74) (2023-09-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.73](https://github.com/shore-group/h1-search/compare/v1.13.72...v1.13.73) (2023-09-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.72](https://github.com/shore-group/h1-search/compare/v1.13.71...v1.13.72) (2023-09-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.71](https://github.com/shore-group/h1-search/compare/v1.13.70...v1.13.71) (2023-09-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.70](https://github.com/shore-group/h1-search/compare/v1.13.69...v1.13.70) (2023-09-11)

### Reverts

- Revert "Remove \_id access in scripts and use mappingID instead (#1142)" (#1144) ([90c9520](https://github.com/shore-group/h1-search/commit/90c95204067dd621132dfcaa144ecffc14ca2dee)), closes [#1142](https://github.com/shore-group/h1-search/issues/1142) [#1144](https://github.com/shore-group/h1-search/issues/1144)

## [1.13.69](https://github.com/shore-group/h1-search/compare/v1.13.68...v1.13.69) (2023-09-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.68](https://github.com/shore-group/h1-search/compare/v1.13.67...v1.13.68) (2023-09-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.67](https://github.com/shore-group/h1-search/compare/v1.13.66...v1.13.67) (2023-09-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.66](https://github.com/shore-group/h1-search/compare/v1.13.65...v1.13.66) (2023-09-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.65](https://github.com/shore-group/h1-search/compare/v1.13.64...v1.13.65) (2023-09-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.64](https://github.com/shore-group/h1-search/compare/v1.13.63...v1.13.64) (2023-09-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.63](https://github.com/shore-group/h1-search/compare/v1.13.62...v1.13.63) (2023-09-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.62](https://github.com/shore-group/h1-search/compare/v1.13.61...v1.13.62) (2023-09-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.61](https://github.com/shore-group/h1-search/compare/v1.13.60...v1.13.61) (2023-09-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.60](https://github.com/shore-group/h1-search/compare/v1.13.59...v1.13.60) (2023-08-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.59](https://github.com/shore-group/h1-search/compare/v1.13.58...v1.13.59) (2023-08-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.58](https://github.com/shore-group/h1-search/compare/v1.13.57...v1.13.58) (2023-08-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.57](https://github.com/shore-group/h1-search/compare/v1.13.56...v1.13.57) (2023-08-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.56](https://github.com/shore-group/h1-search/compare/v1.13.55...v1.13.56) (2023-08-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.55](https://github.com/shore-group/h1-search/compare/v1.13.54...v1.13.55) (2023-08-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.54](https://github.com/shore-group/h1-search/compare/v1.13.53...v1.13.54) (2023-08-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.53](https://github.com/shore-group/h1-search/compare/v1.13.52...v1.13.53) (2023-08-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.52](https://github.com/shore-group/h1-search/compare/v1.13.51...v1.13.52) (2023-08-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.51](https://github.com/shore-group/h1-search/compare/v1.13.50...v1.13.51) (2023-08-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.50](https://github.com/shore-group/h1-search/compare/v1.13.49...v1.13.50) (2023-08-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.49](https://github.com/shore-group/h1-search/compare/v1.13.48...v1.13.49) (2023-08-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.48](https://github.com/shore-group/h1-search/compare/v1.13.47...v1.13.48) (2023-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.47](https://github.com/shore-group/h1-search/compare/v1.13.46...v1.13.47) (2023-07-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.46](https://github.com/shore-group/h1-search/compare/v1.13.45...v1.13.46) (2023-07-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.45](https://github.com/shore-group/h1-search/compare/v1.13.44...v1.13.45) (2023-07-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.44](https://github.com/shore-group/h1-search/compare/v1.13.43...v1.13.44) (2023-07-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.43](https://github.com/shore-group/h1-search/compare/v1.13.42...v1.13.43) (2023-07-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.42](https://github.com/shore-group/h1-search/compare/v1.13.41...v1.13.42) (2023-07-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.41](https://github.com/shore-group/h1-search/compare/v1.13.40...v1.13.41) (2023-07-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.40](https://github.com/shore-group/h1-search/compare/v1.13.39...v1.13.40) (2023-07-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.39](https://github.com/shore-group/h1-search/compare/v1.13.38...v1.13.39) (2023-07-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.38](https://github.com/shore-group/h1-search/compare/v1.13.37...v1.13.38) (2023-07-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.37](https://github.com/shore-group/h1-search/compare/v1.13.36...v1.13.37) (2023-07-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.36](https://github.com/shore-group/h1-search/compare/v1.13.35...v1.13.36) (2023-07-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.35](https://github.com/shore-group/h1-search/compare/v1.13.34...v1.13.35) (2023-07-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.34](https://github.com/shore-group/h1-search/compare/v1.13.33...v1.13.34) (2023-06-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.33](https://github.com/shore-group/h1-search/compare/v1.13.32...v1.13.33) (2023-06-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.32](https://github.com/shore-group/h1-search/compare/v1.13.31...v1.13.32) (2023-06-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.31](https://github.com/shore-group/h1-search/compare/v1.13.30...v1.13.31) (2023-06-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.30](https://github.com/shore-group/h1-search/compare/v1.13.29...v1.13.30) (2023-06-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.29](https://github.com/shore-group/h1-search/compare/v1.13.28...v1.13.29) (2023-06-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.28](https://github.com/shore-group/h1-search/compare/v1.13.27...v1.13.28) (2023-06-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.27](https://github.com/shore-group/h1-search/compare/v1.13.26...v1.13.27) (2023-06-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.26](https://github.com/shore-group/h1-search/compare/v1.13.25...v1.13.26) (2023-06-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.25](https://github.com/shore-group/h1-search/compare/v1.13.24...v1.13.25) (2023-06-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.24](https://github.com/shore-group/h1-search/compare/v1.13.23...v1.13.24) (2023-06-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.23](https://github.com/shore-group/h1-search/compare/v1.13.22...v1.13.23) (2023-06-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.22](https://github.com/shore-group/h1-search/compare/v1.13.21...v1.13.22) (2023-06-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.21](https://github.com/shore-group/h1-search/compare/v1.13.20...v1.13.21) (2023-06-14)

### Reverts

- Revert "SEARCH-528: Using new QUS endpoint for indication synonyms and icd code resolution (#1075)" (#1081) ([d16fbef](https://github.com/shore-group/h1-search/commit/d16fbef4721510e52448828b700f2fe9e230d04c)), closes [#1075](https://github.com/shore-group/h1-search/issues/1075) [#1081](https://github.com/shore-group/h1-search/issues/1081)

## [1.13.20](https://github.com/shore-group/h1-search/compare/v1.13.19...v1.13.20) (2023-06-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.19](https://github.com/shore-group/h1-search/compare/v1.13.18...v1.13.19) (2023-06-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.18](https://github.com/shore-group/h1-search/compare/v1.13.17...v1.13.18) (2023-06-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.17](https://github.com/shore-group/h1-search/compare/v1.13.16...v1.13.17) (2023-06-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.16](https://github.com/shore-group/h1-search/compare/v1.13.15...v1.13.16) (2023-06-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.15](https://github.com/shore-group/h1-search/compare/v1.13.14...v1.13.15) (2023-06-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.14](https://github.com/shore-group/h1-search/compare/v1.13.13...v1.13.14) (2023-06-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.13](https://github.com/shore-group/h1-search/compare/v1.13.12...v1.13.13) (2023-06-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.12](https://github.com/shore-group/h1-search/compare/v1.13.11...v1.13.12) (2023-06-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.11](https://github.com/shore-group/h1-search/compare/v1.13.10...v1.13.11) (2023-06-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.10](https://github.com/shore-group/h1-search/compare/v1.13.9...v1.13.10) (2023-06-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.9](https://github.com/shore-group/h1-search/compare/v1.13.8...v1.13.9) (2023-05-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.8](https://github.com/shore-group/h1-search/compare/v1.13.7...v1.13.8) (2023-05-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.7](https://github.com/shore-group/h1-search/compare/v1.13.6...v1.13.7) (2023-05-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.6](https://github.com/shore-group/h1-search/compare/v1.13.5...v1.13.6) (2023-05-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.5](https://github.com/shore-group/h1-search/compare/v1.13.4...v1.13.5) (2023-05-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.4](https://github.com/shore-group/h1-search/compare/v1.13.3...v1.13.4) (2023-05-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.3](https://github.com/shore-group/h1-search/compare/v1.13.2...v1.13.3) (2023-05-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.2](https://github.com/shore-group/h1-search/compare/v1.13.1...v1.13.2) (2023-05-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.13.1](https://github.com/shore-group/h1-search/compare/v1.13.0...v1.13.1) (2023-05-24)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.13.0](https://github.com/shore-group/h1-search/compare/v1.12.6...v1.13.0) (2023-05-24)

### Features

- **dl map:** add location to iol resp ([#1057](https://github.com/shore-group/h1-search/issues/1057)) ([f062535](https://github.com/shore-group/h1-search/commit/f062535cb6da057694472d09af8815da1f33762f))

## [1.12.6](https://github.com/shore-group/h1-search/compare/v1.12.5...v1.12.6) (2023-05-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.12.5](https://github.com/shore-group/h1-search/compare/v1.12.4...v1.12.5) (2023-05-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.12.4](https://github.com/shore-group/h1-search/compare/v1.12.3...v1.12.4) (2023-05-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.12.3](https://github.com/shore-group/h1-search/compare/v1.12.2...v1.12.3) (2023-05-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.12.2](https://github.com/shore-group/h1-search/compare/v1.12.1...v1.12.2) (2023-05-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.12.1](https://github.com/shore-group/h1-search/compare/v1.12.0...v1.12.1) (2023-05-18)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.12.0](https://github.com/shore-group/h1-search/compare/v1.11.22...v1.12.0) (2023-05-17)

### Features

- **Affiliations:** Add Tie Breaker Sorting for name ([#1044](https://github.com/shore-group/h1-search/issues/1044)) ([4ea0917](https://github.com/shore-group/h1-search/commit/4ea0917bd171d907e80f68dfd5c854a75b571904))

## [1.11.22](https://github.com/shore-group/h1-search/compare/v1.11.21...v1.11.22) (2023-05-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.21](https://github.com/shore-group/h1-search/compare/v1.11.20...v1.11.21) (2023-05-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.20](https://github.com/shore-group/h1-search/compare/v1.11.19...v1.11.20) (2023-05-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.19](https://github.com/shore-group/h1-search/compare/v1.11.18...v1.11.19) (2023-05-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.18](https://github.com/shore-group/h1-search/compare/v1.11.17...v1.11.18) (2023-05-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.17](https://github.com/shore-group/h1-search/compare/v1.11.16...v1.11.17) (2023-05-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.16](https://github.com/shore-group/h1-search/compare/v1.11.15...v1.11.16) (2023-05-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.15](https://github.com/shore-group/h1-search/compare/v1.11.14...v1.11.15) (2023-05-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.14](https://github.com/shore-group/h1-search/compare/v1.11.13...v1.11.14) (2023-05-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.13](https://github.com/shore-group/h1-search/compare/v1.11.12...v1.11.13) (2023-05-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.12](https://github.com/shore-group/h1-search/compare/v1.11.11...v1.11.12) (2023-05-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.11](https://github.com/shore-group/h1-search/compare/v1.11.10...v1.11.11) (2023-05-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.10](https://github.com/shore-group/h1-search/compare/v1.11.9...v1.11.10) (2023-05-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.9](https://github.com/shore-group/h1-search/compare/v1.11.8...v1.11.9) (2023-05-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.8](https://github.com/shore-group/h1-search/compare/v1.11.7...v1.11.8) (2023-05-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.7](https://github.com/shore-group/h1-search/compare/v1.11.6...v1.11.7) (2023-05-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.6](https://github.com/shore-group/h1-search/compare/v1.11.5...v1.11.6) (2023-05-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.5](https://github.com/shore-group/h1-search/compare/v1.11.4...v1.11.5) (2023-05-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.4](https://github.com/shore-group/h1-search/compare/v1.11.3...v1.11.4) (2023-05-05)

### Bug Fixes

- **iol claims:** fix broken filter query ([#1021](https://github.com/shore-group/h1-search/issues/1021)) ([e3dc3f3](https://github.com/shore-group/h1-search/commit/e3dc3f33c935e9bbde628c927369bf9c62f574c2))

## [1.11.3](https://github.com/shore-group/h1-search/compare/v1.11.2...v1.11.3) (2023-05-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.2](https://github.com/shore-group/h1-search/compare/v1.11.1...v1.11.2) (2023-05-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.11.1](https://github.com/shore-group/h1-search/compare/v1.11.0...v1.11.1) (2023-05-04)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.11.0](https://github.com/shore-group/h1-search/compare/v1.10.43...v1.11.0) (2023-05-03)

### Features

- **iol matched counts:** Factor in claims filters ([#1009](https://github.com/shore-group/h1-search/issues/1009)) ([c97d899](https://github.com/shore-group/h1-search/commit/c97d899f042ef398548eb0447bedaa3379ad7da8))

## [1.10.43](https://github.com/shore-group/h1-search/compare/v1.10.42...v1.10.43) (2023-05-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.42](https://github.com/shore-group/h1-search/compare/v1.10.41...v1.10.42) (2023-05-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.41](https://github.com/shore-group/h1-search/compare/v1.10.40...v1.10.41) (2023-05-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.40](https://github.com/shore-group/h1-search/compare/v1.10.39...v1.10.40) (2023-05-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.39](https://github.com/shore-group/h1-search/compare/v1.10.38...v1.10.39) (2023-05-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.38](https://github.com/shore-group/h1-search/compare/v1.10.37...v1.10.38) (2023-05-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.37](https://github.com/shore-group/h1-search/compare/v1.10.36...v1.10.37) (2023-05-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.36](https://github.com/shore-group/h1-search/compare/v1.10.35...v1.10.36) (2023-05-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.35](https://github.com/shore-group/h1-search/compare/v1.10.34...v1.10.35) (2023-05-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.34](https://github.com/shore-group/h1-search/compare/v1.10.33...v1.10.34) (2023-05-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.33](https://github.com/shore-group/h1-search/compare/v1.10.32...v1.10.33) (2023-04-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.32](https://github.com/shore-group/h1-search/compare/v1.10.31...v1.10.32) (2023-04-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.31](https://github.com/shore-group/h1-search/compare/v1.10.30...v1.10.31) (2023-04-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.30](https://github.com/shore-group/h1-search/compare/v1.10.29...v1.10.30) (2023-04-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.29](https://github.com/shore-group/h1-search/compare/v1.10.28...v1.10.29) (2023-04-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.28](https://github.com/shore-group/h1-search/compare/v1.10.27...v1.10.28) (2023-04-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.27](https://github.com/shore-group/h1-search/compare/v1.10.26...v1.10.27) (2023-04-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.26](https://github.com/shore-group/h1-search/compare/v1.10.25...v1.10.26) (2023-04-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.25](https://github.com/shore-group/h1-search/compare/v1.10.24...v1.10.25) (2023-04-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.24](https://github.com/shore-group/h1-search/compare/v1.10.23...v1.10.24) (2023-04-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.23](https://github.com/shore-group/h1-search/compare/v1.10.22...v1.10.23) (2023-04-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.22](https://github.com/shore-group/h1-search/compare/v1.10.21...v1.10.22) (2023-04-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.21](https://github.com/shore-group/h1-search/compare/v1.10.20...v1.10.21) (2023-04-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.20](https://github.com/shore-group/h1-search/compare/v1.10.19...v1.10.20) (2023-04-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.19](https://github.com/shore-group/h1-search/compare/v1.10.18...v1.10.19) (2023-04-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.18](https://github.com/shore-group/h1-search/compare/v1.10.17...v1.10.18) (2023-04-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.17](https://github.com/shore-group/h1-search/compare/v1.10.16...v1.10.17) (2023-04-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.16](https://github.com/shore-group/h1-search/compare/v1.10.15...v1.10.16) (2023-04-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.15](https://github.com/shore-group/h1-search/compare/v1.10.14...v1.10.15) (2023-04-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.14](https://github.com/shore-group/h1-search/compare/v1.10.13...v1.10.14) (2023-04-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.13](https://github.com/shore-group/h1-search/compare/v1.10.12...v1.10.13) (2023-04-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.12](https://github.com/shore-group/h1-search/compare/v1.10.11...v1.10.12) (2023-04-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.11](https://github.com/shore-group/h1-search/compare/v1.10.10...v1.10.11) (2023-04-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.10](https://github.com/shore-group/h1-search/compare/v1.10.9...v1.10.10) (2023-04-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.9](https://github.com/shore-group/h1-search/compare/v1.10.8...v1.10.9) (2023-04-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.8](https://github.com/shore-group/h1-search/compare/v1.10.7...v1.10.8) (2023-04-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.7](https://github.com/shore-group/h1-search/compare/v1.10.6...v1.10.7) (2023-04-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.6](https://github.com/shore-group/h1-search/compare/v1.10.5...v1.10.6) (2023-04-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.5](https://github.com/shore-group/h1-search/compare/v1.10.4...v1.10.5) (2023-04-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.4](https://github.com/shore-group/h1-search/compare/v1.10.3...v1.10.4) (2023-04-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.3](https://github.com/shore-group/h1-search/compare/v1.10.2...v1.10.3) (2023-04-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.2](https://github.com/shore-group/h1-search/compare/v1.10.1...v1.10.2) (2023-04-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.10.1](https://github.com/shore-group/h1-search/compare/v1.10.0...v1.10.1) (2023-04-06)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.10.0](https://github.com/shore-group/h1-search/compare/v1.9.209...v1.10.0) (2023-04-04)

### Features

- **tl search:** add study directors ([#934](https://github.com/shore-group/h1-search/issues/934)) ([89647ef](https://github.com/shore-group/h1-search/commit/89647ef06a8d16ac5d98b7bcd10b9748767c7780))

## [1.9.209](https://github.com/shore-group/h1-search/compare/v1.9.208...v1.9.209) (2023-04-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.208](https://github.com/shore-group/h1-search/compare/v1.9.207...v1.9.208) (2023-04-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.207](https://github.com/shore-group/h1-search/compare/v1.9.206...v1.9.207) (2023-04-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.206](https://github.com/shore-group/h1-search/compare/v1.9.205...v1.9.206) (2023-04-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.205](https://github.com/shore-group/h1-search/compare/v1.9.204...v1.9.205) (2023-04-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.204](https://github.com/shore-group/h1-search/compare/v1.9.203...v1.9.204) (2023-04-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.203](https://github.com/shore-group/h1-search/compare/v1.9.202...v1.9.203) (2023-04-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.202](https://github.com/shore-group/h1-search/compare/v1.9.201...v1.9.202) (2023-03-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.201](https://github.com/shore-group/h1-search/compare/v1.9.200...v1.9.201) (2023-03-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.200](https://github.com/shore-group/h1-search/compare/v1.9.199...v1.9.200) (2023-03-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.199](https://github.com/shore-group/h1-search/compare/v1.9.198...v1.9.199) (2023-03-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.198](https://github.com/shore-group/h1-search/compare/v1.9.197...v1.9.198) (2023-03-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.197](https://github.com/shore-group/h1-search/compare/v1.9.196...v1.9.197) (2023-03-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.196](https://github.com/shore-group/h1-search/compare/v1.9.195...v1.9.196) (2023-03-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.195](https://github.com/shore-group/h1-search/compare/v1.9.194...v1.9.195) (2023-03-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.194](https://github.com/shore-group/h1-search/compare/v1.9.193...v1.9.194) (2023-03-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.193](https://github.com/shore-group/h1-search/compare/v1.9.192...v1.9.193) (2023-03-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.192](https://github.com/shore-group/h1-search/compare/v1.9.191...v1.9.192) (2023-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.191](https://github.com/shore-group/h1-search/compare/v1.9.190...v1.9.191) (2023-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.190](https://github.com/shore-group/h1-search/compare/v1.9.189...v1.9.190) (2023-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.189](https://github.com/shore-group/h1-search/compare/v1.9.188...v1.9.189) (2023-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.188](https://github.com/shore-group/h1-search/compare/v1.9.187...v1.9.188) (2023-03-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.187](https://github.com/shore-group/h1-search/compare/v1.9.186...v1.9.187) (2023-03-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.186](https://github.com/shore-group/h1-search/compare/v1.9.185...v1.9.186) (2023-03-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.185](https://github.com/shore-group/h1-search/compare/v1.9.184...v1.9.185) (2023-03-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.184](https://github.com/shore-group/h1-search/compare/v1.9.183...v1.9.184) (2023-03-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.183](https://github.com/shore-group/h1-search/compare/v1.9.182...v1.9.183) (2023-03-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.182](https://github.com/shore-group/h1-search/compare/v1.9.181...v1.9.182) (2023-03-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.181](https://github.com/shore-group/h1-search/compare/v1.9.180...v1.9.181) (2023-03-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.180](https://github.com/shore-group/h1-search/compare/v1.9.179...v1.9.180) (2023-03-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.179](https://github.com/shore-group/h1-search/compare/v1.9.178...v1.9.179) (2023-03-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.178](https://github.com/shore-group/h1-search/compare/v1.9.177...v1.9.178) (2023-03-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.177](https://github.com/shore-group/h1-search/compare/v1.9.176...v1.9.177) (2023-03-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.176](https://github.com/shore-group/h1-search/compare/v1.9.175...v1.9.176) (2023-03-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.175](https://github.com/shore-group/h1-search/compare/v1.9.174...v1.9.175) (2023-03-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.174](https://github.com/shore-group/h1-search/compare/v1.9.173...v1.9.174) (2023-03-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.173](https://github.com/shore-group/h1-search/compare/v1.9.172...v1.9.173) (2023-03-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.172](https://github.com/shore-group/h1-search/compare/v1.9.171...v1.9.172) (2023-03-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.171](https://github.com/shore-group/h1-search/compare/v1.9.170...v1.9.171) (2023-03-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.170](https://github.com/shore-group/h1-search/compare/v1.9.169...v1.9.170) (2023-03-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.169](https://github.com/shore-group/h1-search/compare/v1.9.168...v1.9.169) (2023-03-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.168](https://github.com/shore-group/h1-search/compare/v1.9.167...v1.9.168) (2023-03-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.167](https://github.com/shore-group/h1-search/compare/v1.9.166...v1.9.167) (2023-03-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.166](https://github.com/shore-group/h1-search/compare/v1.9.165...v1.9.166) (2023-03-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.165](https://github.com/shore-group/h1-search/compare/v1.9.164...v1.9.165) (2023-03-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.164](https://github.com/shore-group/h1-search/compare/v1.9.163...v1.9.164) (2023-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.163](https://github.com/shore-group/h1-search/compare/v1.9.162...v1.9.163) (2023-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.162](https://github.com/shore-group/h1-search/compare/v1.9.161...v1.9.162) (2023-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.161](https://github.com/shore-group/h1-search/compare/v1.9.160...v1.9.161) (2023-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.160](https://github.com/shore-group/h1-search/compare/v1.9.159...v1.9.160) (2023-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.159](https://github.com/shore-group/h1-search/compare/v1.9.158...v1.9.159) (2023-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.158](https://github.com/shore-group/h1-search/compare/v1.9.157...v1.9.158) (2023-03-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.157](https://github.com/shore-group/h1-search/compare/v1.9.156...v1.9.157) (2023-03-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.156](https://github.com/shore-group/h1-search/compare/v1.9.155...v1.9.156) (2023-03-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.155](https://github.com/shore-group/h1-search/compare/v1.9.154...v1.9.155) (2023-03-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.154](https://github.com/shore-group/h1-search/compare/v1.9.153...v1.9.154) (2023-03-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.153](https://github.com/shore-group/h1-search/compare/v1.9.152...v1.9.153) (2023-03-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.152](https://github.com/shore-group/h1-search/compare/v1.9.151...v1.9.152) (2023-03-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.151](https://github.com/shore-group/h1-search/compare/v1.9.150...v1.9.151) (2023-03-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.150](https://github.com/shore-group/h1-search/compare/v1.9.149...v1.9.150) (2023-02-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.149](https://github.com/shore-group/h1-search/compare/v1.9.148...v1.9.149) (2023-02-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.148](https://github.com/shore-group/h1-search/compare/v1.9.147...v1.9.148) (2023-02-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.147](https://github.com/shore-group/h1-search/compare/v1.9.146...v1.9.147) (2023-02-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.146](https://github.com/shore-group/h1-search/compare/v1.9.145...v1.9.146) (2023-02-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.145](https://github.com/shore-group/h1-search/compare/v1.9.144...v1.9.145) (2023-02-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.144](https://github.com/shore-group/h1-search/compare/v1.9.143...v1.9.144) (2023-02-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.143](https://github.com/shore-group/h1-search/compare/v1.9.142...v1.9.143) (2023-02-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.142](https://github.com/shore-group/h1-search/compare/v1.9.141...v1.9.142) (2023-02-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.141](https://github.com/shore-group/h1-search/compare/v1.9.140...v1.9.141) (2023-02-21)

### Reverts

- Revert "Don't remove suggestion that match user query (#850)" (#875) ([fb8b1f0](https://github.com/shore-group/h1-search/commit/fb8b1f059902069f584e4490d109cae062ac0cde)), closes [#850](https://github.com/shore-group/h1-search/issues/850) [#875](https://github.com/shore-group/h1-search/issues/875)

## [1.9.140](https://github.com/shore-group/h1-search/compare/v1.9.139...v1.9.140) (2023-02-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.139](https://github.com/shore-group/h1-search/compare/v1.9.138...v1.9.139) (2023-02-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.138](https://github.com/shore-group/h1-search/compare/v1.9.137...v1.9.138) (2023-02-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.137](https://github.com/shore-group/h1-search/compare/v1.9.136...v1.9.137) (2023-02-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.136](https://github.com/shore-group/h1-search/compare/v1.9.135...v1.9.136) (2023-02-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.135](https://github.com/shore-group/h1-search/compare/v1.9.134...v1.9.135) (2023-02-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.134](https://github.com/shore-group/h1-search/compare/v1.9.133...v1.9.134) (2023-02-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.133](https://github.com/shore-group/h1-search/compare/v1.9.132...v1.9.133) (2023-02-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.132](https://github.com/shore-group/h1-search/compare/v1.9.131...v1.9.132) (2023-02-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.131](https://github.com/shore-group/h1-search/compare/v1.9.130...v1.9.131) (2023-02-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.130](https://github.com/shore-group/h1-search/compare/v1.9.129...v1.9.130) (2023-02-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.129](https://github.com/shore-group/h1-search/compare/v1.9.128...v1.9.129) (2023-02-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.128](https://github.com/shore-group/h1-search/compare/v1.9.127...v1.9.128) (2023-02-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.127](https://github.com/shore-group/h1-search/compare/v1.9.126...v1.9.127) (2023-02-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.126](https://github.com/shore-group/h1-search/compare/v1.9.125...v1.9.126) (2023-02-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.125](https://github.com/shore-group/h1-search/compare/v1.9.124...v1.9.125) (2023-02-07)

### Reverts

- Revert "Upgrade node version to 16.16.0(LTS) (#829)" (#847) ([95e5103](https://github.com/shore-group/h1-search/commit/95e510310dea0015df2f50c54371971395a0a5a7)), closes [#829](https://github.com/shore-group/h1-search/issues/829) [#847](https://github.com/shore-group/h1-search/issues/847)

## [1.9.124](https://github.com/shore-group/h1-search/compare/v1.9.123...v1.9.124) (2023-02-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.123](https://github.com/shore-group/h1-search/compare/v1.9.122...v1.9.123) (2023-02-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.122](https://github.com/shore-group/h1-search/compare/v1.9.121...v1.9.122) (2023-02-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.121](https://github.com/shore-group/h1-search/compare/v1.9.120...v1.9.121) (2023-02-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.120](https://github.com/shore-group/h1-search/compare/v1.9.119...v1.9.120) (2023-02-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.119](https://github.com/shore-group/h1-search/compare/v1.9.118...v1.9.119) (2023-02-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.118](https://github.com/shore-group/h1-search/compare/v1.9.117...v1.9.118) (2023-02-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.117](https://github.com/shore-group/h1-search/compare/v1.9.116...v1.9.117) (2023-02-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.116](https://github.com/shore-group/h1-search/compare/v1.9.115...v1.9.116) (2023-02-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.115](https://github.com/shore-group/h1-search/compare/v1.9.114...v1.9.115) (2023-02-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.114](https://github.com/shore-group/h1-search/compare/v1.9.113...v1.9.114) (2023-01-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.113](https://github.com/shore-group/h1-search/compare/v1.9.112...v1.9.113) (2023-01-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.112](https://github.com/shore-group/h1-search/compare/v1.9.111...v1.9.112) (2023-01-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.111](https://github.com/shore-group/h1-search/compare/v1.9.110...v1.9.111) (2023-01-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.110](https://github.com/shore-group/h1-search/compare/v1.9.109...v1.9.110) (2023-01-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.109](https://github.com/shore-group/h1-search/compare/v1.9.108...v1.9.109) (2023-01-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.108](https://github.com/shore-group/h1-search/compare/v1.9.107...v1.9.108) (2023-01-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.107](https://github.com/shore-group/h1-search/compare/v1.9.106...v1.9.107) (2023-01-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.106](https://github.com/shore-group/h1-search/compare/v1.9.105...v1.9.106) (2023-01-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.105](https://github.com/shore-group/h1-search/compare/v1.9.104...v1.9.105) (2023-01-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.104](https://github.com/shore-group/h1-search/compare/v1.9.103...v1.9.104) (2023-01-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.103](https://github.com/shore-group/h1-search/compare/v1.9.102...v1.9.103) (2023-01-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.102](https://github.com/shore-group/h1-search/compare/v1.9.101...v1.9.102) (2023-01-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.101](https://github.com/shore-group/h1-search/compare/v1.9.100...v1.9.101) (2023-01-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.100](https://github.com/shore-group/h1-search/compare/v1.9.99...v1.9.100) (2023-01-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.99](https://github.com/shore-group/h1-search/compare/v1.9.98...v1.9.99) (2023-01-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.98](https://github.com/shore-group/h1-search/compare/v1.9.97...v1.9.98) (2023-01-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.97](https://github.com/shore-group/h1-search/compare/v1.9.96...v1.9.97) (2023-01-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.96](https://github.com/shore-group/h1-search/compare/v1.9.95...v1.9.96) (2023-01-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.95](https://github.com/shore-group/h1-search/compare/v1.9.94...v1.9.95) (2023-01-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.94](https://github.com/shore-group/h1-search/compare/v1.9.93...v1.9.94) (2023-01-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.93](https://github.com/shore-group/h1-search/compare/v1.9.92...v1.9.93) (2023-01-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.92](https://github.com/shore-group/h1-search/compare/v1.9.91...v1.9.92) (2023-01-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.91](https://github.com/shore-group/h1-search/compare/v1.9.90...v1.9.91) (2023-01-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.90](https://github.com/shore-group/h1-search/compare/v1.9.89...v1.9.90) (2023-01-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.89](https://github.com/shore-group/h1-search/compare/v1.9.88...v1.9.89) (2023-01-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.88](https://github.com/shore-group/h1-search/compare/v1.9.87...v1.9.88) (2023-01-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.87](https://github.com/shore-group/h1-search/compare/v1.9.86...v1.9.87) (2023-01-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.86](https://github.com/shore-group/h1-search/compare/v1.9.85...v1.9.86) (2023-01-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.85](https://github.com/shore-group/h1-search/compare/v1.9.84...v1.9.85) (2023-01-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.84](https://github.com/shore-group/h1-search/compare/v1.9.83...v1.9.84) (2023-01-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.83](https://github.com/shore-group/h1-search/compare/v1.9.82...v1.9.83) (2023-01-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.82](https://github.com/shore-group/h1-search/compare/v1.9.81...v1.9.82) (2023-01-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.81](https://github.com/shore-group/h1-search/compare/v1.9.80...v1.9.81) (2023-01-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.80](https://github.com/shore-group/h1-search/compare/v1.9.79...v1.9.80) (2023-01-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.79](https://github.com/shore-group/h1-search/compare/v1.9.78...v1.9.79) (2023-01-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.78](https://github.com/shore-group/h1-search/compare/v1.9.77...v1.9.78) (2023-01-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.77](https://github.com/shore-group/h1-search/compare/v1.9.76...v1.9.77) (2023-01-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.76](https://github.com/shore-group/h1-search/compare/v1.9.75...v1.9.76) (2023-01-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.75](https://github.com/shore-group/h1-search/compare/v1.9.74...v1.9.75) (2023-01-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.74](https://github.com/shore-group/h1-search/compare/v1.9.73...v1.9.74) (2023-01-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.73](https://github.com/shore-group/h1-search/compare/v1.9.72...v1.9.73) (2022-12-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.72](https://github.com/shore-group/h1-search/compare/v1.9.71...v1.9.72) (2022-12-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.71](https://github.com/shore-group/h1-search/compare/v1.9.70...v1.9.71) (2022-12-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.70](https://github.com/shore-group/h1-search/compare/v1.9.69...v1.9.70) (2022-12-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.69](https://github.com/shore-group/h1-search/compare/v1.9.68...v1.9.69) (2022-12-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.68](https://github.com/shore-group/h1-search/compare/v1.9.67...v1.9.68) (2022-12-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.67](https://github.com/shore-group/h1-search/compare/v1.9.66...v1.9.67) (2022-12-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.66](https://github.com/shore-group/h1-search/compare/v1.9.65...v1.9.66) (2022-12-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.65](https://github.com/shore-group/h1-search/compare/v1.9.64...v1.9.65) (2022-12-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.64](https://github.com/shore-group/h1-search/compare/v1.9.63...v1.9.64) (2022-12-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.63](https://github.com/shore-group/h1-search/compare/v1.9.62...v1.9.63) (2022-12-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.62](https://github.com/shore-group/h1-search/compare/v1.9.61...v1.9.62) (2022-12-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.61](https://github.com/shore-group/h1-search/compare/v1.9.60...v1.9.61) (2022-12-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.60](https://github.com/shore-group/h1-search/compare/v1.9.59...v1.9.60) (2022-12-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.59](https://github.com/shore-group/h1-search/compare/v1.9.58...v1.9.59) (2022-12-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.58](https://github.com/shore-group/h1-search/compare/v1.9.57...v1.9.58) (2022-12-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.57](https://github.com/shore-group/h1-search/compare/v1.9.56...v1.9.57) (2022-12-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.56](https://github.com/shore-group/h1-search/compare/v1.9.55...v1.9.56) (2022-12-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.55](https://github.com/shore-group/h1-search/compare/v1.9.54...v1.9.55) (2022-12-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.54](https://github.com/shore-group/h1-search/compare/v1.9.53...v1.9.54) (2022-12-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.53](https://github.com/shore-group/h1-search/compare/v1.9.52...v1.9.53) (2022-12-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.52](https://github.com/shore-group/h1-search/compare/v1.9.51...v1.9.52) (2022-12-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.51](https://github.com/shore-group/h1-search/compare/v1.9.50...v1.9.51) (2022-12-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.50](https://github.com/shore-group/h1-search/compare/v1.9.49...v1.9.50) (2022-12-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.49](https://github.com/shore-group/h1-search/compare/v1.9.48...v1.9.49) (2022-12-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.48](https://github.com/shore-group/h1-search/compare/v1.9.47...v1.9.48) (2022-12-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.47](https://github.com/shore-group/h1-search/compare/v1.9.46...v1.9.47) (2022-12-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.46](https://github.com/shore-group/h1-search/compare/v1.9.45...v1.9.46) (2022-12-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.45](https://github.com/shore-group/h1-search/compare/v1.9.44...v1.9.45) (2022-12-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.44](https://github.com/shore-group/h1-search/compare/v1.9.43...v1.9.44) (2022-12-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.43](https://github.com/shore-group/h1-search/compare/v1.9.42...v1.9.43) (2022-12-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.42](https://github.com/shore-group/h1-search/compare/v1.9.41...v1.9.42) (2022-11-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.41](https://github.com/shore-group/h1-search/compare/v1.9.40...v1.9.41) (2022-11-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.40](https://github.com/shore-group/h1-search/compare/v1.9.39...v1.9.40) (2022-11-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.39](https://github.com/shore-group/h1-search/compare/v1.9.38...v1.9.39) (2022-11-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.38](https://github.com/shore-group/h1-search/compare/v1.9.37...v1.9.38) (2022-11-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.37](https://github.com/shore-group/h1-search/compare/v1.9.36...v1.9.37) (2022-11-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.36](https://github.com/shore-group/h1-search/compare/v1.9.35...v1.9.36) (2022-11-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.35](https://github.com/shore-group/h1-search/compare/v1.9.34...v1.9.35) (2022-11-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.34](https://github.com/shore-group/h1-search/compare/v1.9.33...v1.9.34) (2022-11-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.33](https://github.com/shore-group/h1-search/compare/v1.9.32...v1.9.33) (2022-11-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.32](https://github.com/shore-group/h1-search/compare/v1.9.31...v1.9.32) (2022-11-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.31](https://github.com/shore-group/h1-search/compare/v1.9.30...v1.9.31) (2022-11-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.30](https://github.com/shore-group/h1-search/compare/v1.9.29...v1.9.30) (2022-11-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.29](https://github.com/shore-group/h1-search/compare/v1.9.28...v1.9.29) (2022-11-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.28](https://github.com/shore-group/h1-search/compare/v1.9.27...v1.9.28) (2022-11-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.27](https://github.com/shore-group/h1-search/compare/v1.9.26...v1.9.27) (2022-11-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.26](https://github.com/shore-group/h1-search/compare/v1.9.25...v1.9.26) (2022-11-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.25](https://github.com/shore-group/h1-search/compare/v1.9.24...v1.9.25) (2022-11-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.24](https://github.com/shore-group/h1-search/compare/v1.9.23...v1.9.24) (2022-11-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.23](https://github.com/shore-group/h1-search/compare/v1.9.22...v1.9.23) (2022-11-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.22](https://github.com/shore-group/h1-search/compare/v1.9.21...v1.9.22) (2022-11-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.21](https://github.com/shore-group/h1-search/compare/v1.9.20...v1.9.21) (2022-11-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.20](https://github.com/shore-group/h1-search/compare/v1.9.19...v1.9.20) (2022-11-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.19](https://github.com/shore-group/h1-search/compare/v1.9.18...v1.9.19) (2022-11-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.18](https://github.com/shore-group/h1-search/compare/v1.9.17...v1.9.18) (2022-11-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.17](https://github.com/shore-group/h1-search/compare/v1.9.16...v1.9.17) (2022-11-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.16](https://github.com/shore-group/h1-search/compare/v1.9.15...v1.9.16) (2022-11-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.15](https://github.com/shore-group/h1-search/compare/v1.9.14...v1.9.15) (2022-11-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.14](https://github.com/shore-group/h1-search/compare/v1.9.13...v1.9.14) (2022-11-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.13](https://github.com/shore-group/h1-search/compare/v1.9.12...v1.9.13) (2022-11-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.12](https://github.com/shore-group/h1-search/compare/v1.9.11...v1.9.12) (2022-11-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.11](https://github.com/shore-group/h1-search/compare/v1.9.10...v1.9.11) (2022-11-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.10](https://github.com/shore-group/h1-search/compare/v1.9.9...v1.9.10) (2022-11-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.9](https://github.com/shore-group/h1-search/compare/v1.9.8...v1.9.9) (2022-11-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.8](https://github.com/shore-group/h1-search/compare/v1.9.7...v1.9.8) (2022-11-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.7](https://github.com/shore-group/h1-search/compare/v1.9.6...v1.9.7) (2022-11-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.6](https://github.com/shore-group/h1-search/compare/v1.9.5...v1.9.6) (2022-11-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.5](https://github.com/shore-group/h1-search/compare/v1.9.4...v1.9.5) (2022-11-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.4](https://github.com/shore-group/h1-search/compare/v1.9.3...v1.9.4) (2022-11-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.3](https://github.com/shore-group/h1-search/compare/v1.9.2...v1.9.3) (2022-11-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.2](https://github.com/shore-group/h1-search/compare/v1.9.1...v1.9.2) (2022-11-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.9.1](https://github.com/shore-group/h1-search/compare/v1.9.0...v1.9.1) (2022-10-31)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.9.0](https://github.com/shore-group/h1-search/compare/v1.8.82...v1.9.0) (2022-10-31)

### Features

- **KeywordSearch:** refactor handling of digitalRelevance sort ([#691](https://github.com/shore-group/h1-search/issues/691)) ([ba6f494](https://github.com/shore-group/h1-search/commit/ba6f4946fcc248f21f41d3f68e200e0fdc133aa2))

## [1.8.82](https://github.com/shore-group/h1-search/compare/v1.8.81...v1.8.82) (2022-10-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.81](https://github.com/shore-group/h1-search/compare/v1.8.80...v1.8.81) (2022-10-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.80](https://github.com/shore-group/h1-search/compare/v1.8.79...v1.8.80) (2022-10-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.79](https://github.com/shore-group/h1-search/compare/v1.8.78...v1.8.79) (2022-10-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.78](https://github.com/shore-group/h1-search/compare/v1.8.77...v1.8.78) (2022-10-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.77](https://github.com/shore-group/h1-search/compare/v1.8.76...v1.8.77) (2022-10-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.76](https://github.com/shore-group/h1-search/compare/v1.8.75...v1.8.76) (2022-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.75](https://github.com/shore-group/h1-search/compare/v1.8.74...v1.8.75) (2022-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.74](https://github.com/shore-group/h1-search/compare/v1.8.73...v1.8.74) (2022-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.73](https://github.com/shore-group/h1-search/compare/v1.8.72...v1.8.73) (2022-10-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.72](https://github.com/shore-group/h1-search/compare/v1.8.71...v1.8.72) (2022-10-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.71](https://github.com/shore-group/h1-search/compare/v1.8.70...v1.8.71) (2022-10-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.70](https://github.com/shore-group/h1-search/compare/v1.8.69...v1.8.70) (2022-10-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.69](https://github.com/shore-group/h1-search/compare/v1.8.68...v1.8.69) (2022-10-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.68](https://github.com/shore-group/h1-search/compare/v1.8.67...v1.8.68) (2022-10-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.67](https://github.com/shore-group/h1-search/compare/v1.8.66...v1.8.67) (2022-10-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.66](https://github.com/shore-group/h1-search/compare/v1.8.65...v1.8.66) (2022-10-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.65](https://github.com/shore-group/h1-search/compare/v1.8.64...v1.8.65) (2022-10-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.64](https://github.com/shore-group/h1-search/compare/v1.8.63...v1.8.64) (2022-10-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.63](https://github.com/shore-group/h1-search/compare/v1.8.62...v1.8.63) (2022-10-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.62](https://github.com/shore-group/h1-search/compare/v1.8.61...v1.8.62) (2022-10-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.61](https://github.com/shore-group/h1-search/compare/v1.8.60...v1.8.61) (2022-10-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.60](https://github.com/shore-group/h1-search/compare/v1.8.59...v1.8.60) (2022-10-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.59](https://github.com/shore-group/h1-search/compare/v1.8.58...v1.8.59) (2022-10-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.58](https://github.com/shore-group/h1-search/compare/v1.8.57...v1.8.58) (2022-10-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.57](https://github.com/shore-group/h1-search/compare/v1.8.56...v1.8.57) (2022-10-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.56](https://github.com/shore-group/h1-search/compare/v1.8.55...v1.8.56) (2022-10-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.55](https://github.com/shore-group/h1-search/compare/v1.8.54...v1.8.55) (2022-10-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.54](https://github.com/shore-group/h1-search/compare/v1.8.53...v1.8.54) (2022-10-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.53](https://github.com/shore-group/h1-search/compare/v1.8.52...v1.8.53) (2022-10-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.52](https://github.com/shore-group/h1-search/compare/v1.8.51...v1.8.52) (2022-10-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.51](https://github.com/shore-group/h1-search/compare/v1.8.50...v1.8.51) (2022-10-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.50](https://github.com/shore-group/h1-search/compare/v1.8.49...v1.8.50) (2022-10-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.49](https://github.com/shore-group/h1-search/compare/v1.8.48...v1.8.49) (2022-10-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.48](https://github.com/shore-group/h1-search/compare/v1.8.47...v1.8.48) (2022-10-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.47](https://github.com/shore-group/h1-search/compare/v1.8.46...v1.8.47) (2022-10-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.46](https://github.com/shore-group/h1-search/compare/v1.8.45...v1.8.46) (2022-10-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.45](https://github.com/shore-group/h1-search/compare/v1.8.44...v1.8.45) (2022-10-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.44](https://github.com/shore-group/h1-search/compare/v1.8.43...v1.8.44) (2022-10-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.43](https://github.com/shore-group/h1-search/compare/v1.8.42...v1.8.43) (2022-10-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.42](https://github.com/shore-group/h1-search/compare/v1.8.41...v1.8.42) (2022-10-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.41](https://github.com/shore-group/h1-search/compare/v1.8.40...v1.8.41) (2022-10-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.40](https://github.com/shore-group/h1-search/compare/v1.8.39...v1.8.40) (2022-10-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.39](https://github.com/shore-group/h1-search/compare/v1.8.38...v1.8.39) (2022-10-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.38](https://github.com/shore-group/h1-search/compare/v1.8.37...v1.8.38) (2022-10-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.37](https://github.com/shore-group/h1-search/compare/v1.8.36...v1.8.37) (2022-10-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.36](https://github.com/shore-group/h1-search/compare/v1.8.35...v1.8.36) (2022-10-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.35](https://github.com/shore-group/h1-search/compare/v1.8.34...v1.8.35) (2022-10-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.34](https://github.com/shore-group/h1-search/compare/v1.8.33...v1.8.34) (2022-10-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.33](https://github.com/shore-group/h1-search/compare/v1.8.32...v1.8.33) (2022-09-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.32](https://github.com/shore-group/h1-search/compare/v1.8.31...v1.8.32) (2022-09-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.31](https://github.com/shore-group/h1-search/compare/v1.8.30...v1.8.31) (2022-09-30)

### Reverts

- Revert "Added basic sorts on twitter Followers and tweets including addition … (#617)" (#629) ([8a19038](https://github.com/shore-group/h1-search/commit/8a19038ec74b4c6b34b4f39189e49a345d15ab9e)), closes [#617](https://github.com/shore-group/h1-search/issues/617) [#629](https://github.com/shore-group/h1-search/issues/629)

## [1.8.30](https://github.com/shore-group/h1-search/compare/v1.8.29...v1.8.30) (2022-09-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.29](https://github.com/shore-group/h1-search/compare/v1.8.28...v1.8.29) (2022-09-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.28](https://github.com/shore-group/h1-search/compare/v1.8.27...v1.8.28) (2022-09-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.27](https://github.com/shore-group/h1-search/compare/v1.8.26...v1.8.27) (2022-09-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.26](https://github.com/shore-group/h1-search/compare/v1.8.25...v1.8.26) (2022-09-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.25](https://github.com/shore-group/h1-search/compare/v1.8.24...v1.8.25) (2022-09-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.24](https://github.com/shore-group/h1-search/compare/v1.8.23...v1.8.24) (2022-09-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.23](https://github.com/shore-group/h1-search/compare/v1.8.22...v1.8.23) (2022-09-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.22](https://github.com/shore-group/h1-search/compare/v1.8.21...v1.8.22) (2022-09-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.21](https://github.com/shore-group/h1-search/compare/v1.8.20...v1.8.21) (2022-09-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.20](https://github.com/shore-group/h1-search/compare/v1.8.19...v1.8.20) (2022-09-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.19](https://github.com/shore-group/h1-search/compare/v1.8.18...v1.8.19) (2022-09-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.18](https://github.com/shore-group/h1-search/compare/v1.8.17...v1.8.18) (2022-09-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.17](https://github.com/shore-group/h1-search/compare/v1.8.16...v1.8.17) (2022-09-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.16](https://github.com/shore-group/h1-search/compare/v1.8.15...v1.8.16) (2022-09-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.15](https://github.com/shore-group/h1-search/compare/v1.8.14...v1.8.15) (2022-09-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.14](https://github.com/shore-group/h1-search/compare/v1.8.13...v1.8.14) (2022-09-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.13](https://github.com/shore-group/h1-search/compare/v1.8.12...v1.8.13) (2022-09-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.12](https://github.com/shore-group/h1-search/compare/v1.8.11...v1.8.12) (2022-09-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.11](https://github.com/shore-group/h1-search/compare/v1.8.10...v1.8.11) (2022-09-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.10](https://github.com/shore-group/h1-search/compare/v1.8.9...v1.8.10) (2022-09-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.9](https://github.com/shore-group/h1-search/compare/v1.8.8...v1.8.9) (2022-09-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.8](https://github.com/shore-group/h1-search/compare/v1.8.7...v1.8.8) (2022-09-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.7](https://github.com/shore-group/h1-search/compare/v1.8.6...v1.8.7) (2022-09-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.6](https://github.com/shore-group/h1-search/compare/v1.8.5...v1.8.6) (2022-09-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.5](https://github.com/shore-group/h1-search/compare/v1.8.4...v1.8.5) (2022-09-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.4](https://github.com/shore-group/h1-search/compare/v1.8.3...v1.8.4) (2022-09-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.3](https://github.com/shore-group/h1-search/compare/v1.8.2...v1.8.3) (2022-09-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.2](https://github.com/shore-group/h1-search/compare/v1.8.1...v1.8.2) (2022-09-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.8.1](https://github.com/shore-group/h1-search/compare/v1.8.0...v1.8.1) (2022-09-06)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.8.0](https://github.com/shore-group/h1-search/compare/v1.7.7...v1.8.0) (2022-09-01)

### Features

- **h1dn tags:** Create new bulk search method to handle h1nd profiles ([#576](https://github.com/shore-group/h1-search/issues/576)) ([44f2d4a](https://github.com/shore-group/h1-search/commit/44f2d4ab094c7ff3111f4df5960faf64acd33f90))

## [1.7.7](https://github.com/shore-group/h1-search/compare/v1.7.6...v1.7.7) (2022-09-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.7.6](https://github.com/shore-group/h1-search/compare/v1.7.5...v1.7.6) (2022-08-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.7.5](https://github.com/shore-group/h1-search/compare/v1.7.4...v1.7.5) (2022-08-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.7.4](https://github.com/shore-group/h1-search/compare/v1.7.3...v1.7.4) (2022-08-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.7.3](https://github.com/shore-group/h1-search/compare/v1.7.2...v1.7.3) (2022-08-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.7.2](https://github.com/shore-group/h1-search/compare/v1.7.1...v1.7.2) (2022-08-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.7.1](https://github.com/shore-group/h1-search/compare/v1.7.0...v1.7.1) (2022-08-30)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.7.0](https://github.com/shore-group/h1-search/compare/v1.6.108...v1.7.0) (2022-08-30)

### Features

- **tagsv3|h1dn:** Update tag filtering to work with new tagging system. ([#524](https://github.com/shore-group/h1-search/issues/524)) ([c6bcadd](https://github.com/shore-group/h1-search/commit/c6bcadd263d83f9ec76a785f6093409ae192960a))

## [1.6.108](https://github.com/shore-group/h1-search/compare/v1.6.107...v1.6.108) (2022-08-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.107](https://github.com/shore-group/h1-search/compare/v1.6.106...v1.6.107) (2022-08-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.106](https://github.com/shore-group/h1-search/compare/v1.6.105...v1.6.106) (2022-08-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.105](https://github.com/shore-group/h1-search/compare/v1.6.104...v1.6.105) (2022-08-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.104](https://github.com/shore-group/h1-search/compare/v1.6.103...v1.6.104) (2022-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.103](https://github.com/shore-group/h1-search/compare/v1.6.102...v1.6.103) (2022-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.102](https://github.com/shore-group/h1-search/compare/v1.6.101...v1.6.102) (2022-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.101](https://github.com/shore-group/h1-search/compare/v1.6.100...v1.6.101) (2022-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.100](https://github.com/shore-group/h1-search/compare/v1.6.99...v1.6.100) (2022-08-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.99](https://github.com/shore-group/h1-search/compare/v1.6.98...v1.6.99) (2022-08-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.98](https://github.com/shore-group/h1-search/compare/v1.6.97...v1.6.98) (2022-08-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.97](https://github.com/shore-group/h1-search/compare/v1.6.96...v1.6.97) (2022-08-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.96](https://github.com/shore-group/h1-search/compare/v1.6.95...v1.6.96) (2022-08-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.95](https://github.com/shore-group/h1-search/compare/v1.6.94...v1.6.95) (2022-08-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.94](https://github.com/shore-group/h1-search/compare/v1.6.93...v1.6.94) (2022-08-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.93](https://github.com/shore-group/h1-search/compare/v1.6.92...v1.6.93) (2022-08-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.92](https://github.com/shore-group/h1-search/compare/v1.6.91...v1.6.92) (2022-08-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.91](https://github.com/shore-group/h1-search/compare/v1.6.90...v1.6.91) (2022-08-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.90](https://github.com/shore-group/h1-search/compare/v1.6.89...v1.6.90) (2022-08-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.89](https://github.com/shore-group/h1-search/compare/v1.6.88...v1.6.89) (2022-08-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.88](https://github.com/shore-group/h1-search/compare/v1.6.87...v1.6.88) (2022-08-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.87](https://github.com/shore-group/h1-search/compare/v1.6.86...v1.6.87) (2022-08-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.86](https://github.com/shore-group/h1-search/compare/v1.6.85...v1.6.86) (2022-08-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.85](https://github.com/shore-group/h1-search/compare/v1.6.84...v1.6.85) (2022-08-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.84](https://github.com/shore-group/h1-search/compare/v1.6.83...v1.6.84) (2022-08-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.83](https://github.com/shore-group/h1-search/compare/v1.6.82...v1.6.83) (2022-08-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.82](https://github.com/shore-group/h1-search/compare/v1.6.81...v1.6.82) (2022-08-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.81](https://github.com/shore-group/h1-search/compare/v1.6.80...v1.6.81) (2022-08-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.80](https://github.com/shore-group/h1-search/compare/v1.6.79...v1.6.80) (2022-08-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.79](https://github.com/shore-group/h1-search/compare/v1.6.78...v1.6.79) (2022-08-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.78](https://github.com/shore-group/h1-search/compare/v1.6.77...v1.6.78) (2022-08-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.77](https://github.com/shore-group/h1-search/compare/v1.6.76...v1.6.77) (2022-08-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.76](https://github.com/shore-group/h1-search/compare/v1.6.75...v1.6.76) (2022-08-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.75](https://github.com/shore-group/h1-search/compare/v1.6.74...v1.6.75) (2022-08-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.74](https://github.com/shore-group/h1-search/compare/v1.6.73...v1.6.74) (2022-08-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.73](https://github.com/shore-group/h1-search/compare/v1.6.72...v1.6.73) (2022-08-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.72](https://github.com/shore-group/h1-search/compare/v1.6.71...v1.6.72) (2022-08-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.71](https://github.com/shore-group/h1-search/compare/v1.6.70...v1.6.71) (2022-08-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.70](https://github.com/shore-group/h1-search/compare/v1.6.69...v1.6.70) (2022-08-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.69](https://github.com/shore-group/h1-search/compare/v1.6.68...v1.6.69) (2022-08-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.68](https://github.com/shore-group/h1-search/compare/v1.6.67...v1.6.68) (2022-08-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.67](https://github.com/shore-group/h1-search/compare/v1.6.66...v1.6.67) (2022-08-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.66](https://github.com/shore-group/h1-search/compare/v1.6.65...v1.6.66) (2022-08-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.65](https://github.com/shore-group/h1-search/compare/v1.6.64...v1.6.65) (2022-08-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.64](https://github.com/shore-group/h1-search/compare/v1.6.63...v1.6.64) (2022-08-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.63](https://github.com/shore-group/h1-search/compare/v1.6.62...v1.6.63) (2022-08-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.62](https://github.com/shore-group/h1-search/compare/v1.6.61...v1.6.62) (2022-08-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.61](https://github.com/shore-group/h1-search/compare/v1.6.60...v1.6.61) (2022-08-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.60](https://github.com/shore-group/h1-search/compare/v1.6.59...v1.6.60) (2022-08-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.59](https://github.com/shore-group/h1-search/compare/v1.6.58...v1.6.59) (2022-08-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.58](https://github.com/shore-group/h1-search/compare/v1.6.57...v1.6.58) (2022-08-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.57](https://github.com/shore-group/h1-search/compare/v1.6.56...v1.6.57) (2022-08-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.56](https://github.com/shore-group/h1-search/compare/v1.6.55...v1.6.56) (2022-08-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.55](https://github.com/shore-group/h1-search/compare/v1.6.54...v1.6.55) (2022-08-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.54](https://github.com/shore-group/h1-search/compare/v1.6.53...v1.6.54) (2022-08-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.53](https://github.com/shore-group/h1-search/compare/v1.6.52...v1.6.53) (2022-08-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.52](https://github.com/shore-group/h1-search/compare/v1.6.51...v1.6.52) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.51](https://github.com/shore-group/h1-search/compare/v1.6.50...v1.6.51) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.50](https://github.com/shore-group/h1-search/compare/v1.6.49...v1.6.50) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.49](https://github.com/shore-group/h1-search/compare/v1.6.48...v1.6.49) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.48](https://github.com/shore-group/h1-search/compare/v1.6.47...v1.6.48) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.47](https://github.com/shore-group/h1-search/compare/v1.6.46...v1.6.47) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.46](https://github.com/shore-group/h1-search/compare/v1.6.45...v1.6.46) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.45](https://github.com/shore-group/h1-search/compare/v1.6.44...v1.6.45) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.44](https://github.com/shore-group/h1-search/compare/v1.6.43...v1.6.44) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.43](https://github.com/shore-group/h1-search/compare/v1.6.42...v1.6.43) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.42](https://github.com/shore-group/h1-search/compare/v1.6.41...v1.6.42) (2022-08-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.41](https://github.com/shore-group/h1-search/compare/v1.6.40...v1.6.41) (2022-07-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.40](https://github.com/shore-group/h1-search/compare/v1.6.39...v1.6.40) (2022-07-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.39](https://github.com/shore-group/h1-search/compare/v1.6.38...v1.6.39) (2022-07-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.38](https://github.com/shore-group/h1-search/compare/v1.6.37...v1.6.38) (2022-07-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.37](https://github.com/shore-group/h1-search/compare/v1.6.36...v1.6.37) (2022-07-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.36](https://github.com/shore-group/h1-search/compare/v1.6.35...v1.6.36) (2022-07-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.35](https://github.com/shore-group/h1-search/compare/v1.6.34...v1.6.35) (2022-07-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.34](https://github.com/shore-group/h1-search/compare/v1.6.33...v1.6.34) (2022-07-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.33](https://github.com/shore-group/h1-search/compare/v1.6.32...v1.6.33) (2022-07-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.32](https://github.com/shore-group/h1-search/compare/v1.6.31...v1.6.32) (2022-07-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.31](https://github.com/shore-group/h1-search/compare/v1.6.30...v1.6.31) (2022-07-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.30](https://github.com/shore-group/h1-search/compare/v1.6.29...v1.6.30) (2022-07-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.29](https://github.com/shore-group/h1-search/compare/v1.6.28...v1.6.29) (2022-07-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.28](https://github.com/shore-group/h1-search/compare/v1.6.27...v1.6.28) (2022-07-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.27](https://github.com/shore-group/h1-search/compare/v1.6.26...v1.6.27) (2022-07-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.26](https://github.com/shore-group/h1-search/compare/v1.6.25...v1.6.26) (2022-07-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.25](https://github.com/shore-group/h1-search/compare/v1.6.24...v1.6.25) (2022-07-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.24](https://github.com/shore-group/h1-search/compare/v1.6.23...v1.6.24) (2022-07-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.23](https://github.com/shore-group/h1-search/compare/v1.6.22...v1.6.23) (2022-07-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.22](https://github.com/shore-group/h1-search/compare/v1.6.21...v1.6.22) (2022-07-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.21](https://github.com/shore-group/h1-search/compare/v1.6.20...v1.6.21) (2022-07-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.20](https://github.com/shore-group/h1-search/compare/v1.6.19...v1.6.20) (2022-07-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.19](https://github.com/shore-group/h1-search/compare/v1.6.18...v1.6.19) (2022-07-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.18](https://github.com/shore-group/h1-search/compare/v1.6.17...v1.6.18) (2022-07-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.17](https://github.com/shore-group/h1-search/compare/v1.6.16...v1.6.17) (2022-07-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.16](https://github.com/shore-group/h1-search/compare/v1.6.15...v1.6.16) (2022-07-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.15](https://github.com/shore-group/h1-search/compare/v1.6.14...v1.6.15) (2022-07-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.14](https://github.com/shore-group/h1-search/compare/v1.6.13...v1.6.14) (2022-07-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.13](https://github.com/shore-group/h1-search/compare/v1.6.12...v1.6.13) (2022-07-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.12](https://github.com/shore-group/h1-search/compare/v1.6.11...v1.6.12) (2022-07-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.11](https://github.com/shore-group/h1-search/compare/v1.6.10...v1.6.11) (2022-07-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.10](https://github.com/shore-group/h1-search/compare/v1.6.9...v1.6.10) (2022-07-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.9](https://github.com/shore-group/h1-search/compare/v1.6.8...v1.6.9) (2022-07-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.8](https://github.com/shore-group/h1-search/compare/v1.6.7...v1.6.8) (2022-07-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.7](https://github.com/shore-group/h1-search/compare/v1.6.6...v1.6.7) (2022-07-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.6](https://github.com/shore-group/h1-search/compare/v1.6.5...v1.6.6) (2022-07-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.5](https://github.com/shore-group/h1-search/compare/v1.6.4...v1.6.5) (2022-06-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.4](https://github.com/shore-group/h1-search/compare/v1.6.3...v1.6.4) (2022-06-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.3](https://github.com/shore-group/h1-search/compare/v1.6.2...v1.6.3) (2022-06-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.2](https://github.com/shore-group/h1-search/compare/v1.6.1...v1.6.2) (2022-06-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.6.1](https://github.com/shore-group/h1-search/compare/v1.6.0...v1.6.1) (2022-06-29)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.6.0](https://github.com/shore-group/h1-search/compare/v1.5.32...v1.6.0) (2022-06-29)

### Features

- **Trials v3:** update facility count agg for nested type ([#426](https://github.com/shore-group/h1-search/issues/426)) ([7b9f0ff](https://github.com/shore-group/h1-search/commit/7b9f0ff8063e96f43478983244d68283343ceb84))

## [1.5.32](https://github.com/shore-group/h1-search/compare/v1.5.31...v1.5.32) (2022-06-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.31](https://github.com/shore-group/h1-search/compare/v1.5.30...v1.5.31) (2022-06-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.30](https://github.com/shore-group/h1-search/compare/v1.5.29...v1.5.30) (2022-06-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.29](https://github.com/shore-group/h1-search/compare/v1.5.28...v1.5.29) (2022-06-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.28](https://github.com/shore-group/h1-search/compare/v1.5.27...v1.5.28) (2022-06-17)

### Bug Fixes

- **Trial Aggs:** Fix aggregations for v3 mapper ([#420](https://github.com/shore-group/h1-search/issues/420)) ([5f118a1](https://github.com/shore-group/h1-search/commit/5f118a1b0b1ee3addd66c8648e4e0b21bc8d13a2))

## [1.5.27](https://github.com/shore-group/h1-search/compare/v1.5.26...v1.5.27) (2022-06-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.26](https://github.com/shore-group/h1-search/compare/v1.5.25...v1.5.26) (2022-06-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.25](https://github.com/shore-group/h1-search/compare/v1.5.24...v1.5.25) (2022-06-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.24](https://github.com/shore-group/h1-search/compare/v1.5.23...v1.5.24) (2022-06-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.23](https://github.com/shore-group/h1-search/compare/v1.5.22...v1.5.23) (2022-06-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.22](https://github.com/shore-group/h1-search/compare/v1.5.21...v1.5.22) (2022-06-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.21](https://github.com/shore-group/h1-search/compare/v1.5.20...v1.5.21) (2022-06-15)

### Reverts

- Revert "fix(Trials Aggs): filter outliers from enrollment aggregation (#391)" ([b26b0fa](https://github.com/shore-group/h1-search/commit/b26b0faacdd6d240a2de8bc3172a68d61ac25f0a)), closes [#391](https://github.com/shore-group/h1-search/issues/391)

## [1.5.20](https://github.com/shore-group/h1-search/compare/v1.5.19...v1.5.20) (2022-06-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.19](https://github.com/shore-group/h1-search/compare/v1.5.18...v1.5.19) (2022-06-14)

### Bug Fixes

- **Trials Aggs:** filter outliers from enrollment aggregation ([#391](https://github.com/shore-group/h1-search/issues/391)) ([4cebf99](https://github.com/shore-group/h1-search/commit/4cebf99e2225925195188e21ec4659ff9842ccf3))

## [1.5.18](https://github.com/shore-group/h1-search/compare/v1.5.17...v1.5.18) (2022-06-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.17](https://github.com/shore-group/h1-search/compare/v1.5.16...v1.5.17) (2022-06-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.16](https://github.com/shore-group/h1-search/compare/v1.5.15...v1.5.16) (2022-06-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.15](https://github.com/shore-group/h1-search/compare/v1.5.14...v1.5.15) (2022-06-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.14](https://github.com/shore-group/h1-search/compare/v1.5.13...v1.5.14) (2022-06-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.13](https://github.com/shore-group/h1-search/compare/v1.5.12...v1.5.13) (2022-06-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.12](https://github.com/shore-group/h1-search/compare/v1.5.11...v1.5.12) (2022-06-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.11](https://github.com/shore-group/h1-search/compare/v1.5.10...v1.5.11) (2022-06-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.10](https://github.com/shore-group/h1-search/compare/v1.5.9...v1.5.10) (2022-06-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.9](https://github.com/shore-group/h1-search/compare/v1.5.8...v1.5.9) (2022-06-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.8](https://github.com/shore-group/h1-search/compare/v1.5.7...v1.5.8) (2022-06-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.7](https://github.com/shore-group/h1-search/compare/v1.5.6...v1.5.7) (2022-06-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.6](https://github.com/shore-group/h1-search/compare/v1.5.5...v1.5.6) (2022-06-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.5](https://github.com/shore-group/h1-search/compare/v1.5.4...v1.5.5) (2022-06-02)

### Bug Fixes

- **trail aggs:** fix avgInvestigatorCount aggregation ([#382](https://github.com/shore-group/h1-search/issues/382)) ([e18b474](https://github.com/shore-group/h1-search/commit/e18b4749458cf823c72dc9fca1875049ad5bea84))

## [1.5.4](https://github.com/shore-group/h1-search/compare/v1.5.3...v1.5.4) (2022-06-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.3](https://github.com/shore-group/h1-search/compare/v1.5.2...v1.5.3) (2022-06-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.2](https://github.com/shore-group/h1-search/compare/v1.5.1...v1.5.2) (2022-06-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.5.1](https://github.com/shore-group/h1-search/compare/v1.5.0...v1.5.1) (2022-05-31)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.5.0](https://github.com/shore-group/h1-search/compare/v1.4.8...v1.5.0) (2022-05-31)

### Features

- **Trail Aggregations:** add new enrollment total aggregation and new derived investigator count ([#369](https://github.com/shore-group/h1-search/issues/369)) ([7a4fcb3](https://github.com/shore-group/h1-search/commit/7a4fcb342051d19bb1eab5644cb4b1e60406fa64))

## [1.4.8](https://github.com/shore-group/h1-search/compare/v1.4.7...v1.4.8) (2022-05-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.4.7](https://github.com/shore-group/h1-search/compare/v1.4.6...v1.4.7) (2022-05-27)

### Reverts

- Revert "Search by Institution ID, Adds personIds & facilities to search response (#368)" (#373) ([a7b1333](https://github.com/shore-group/h1-search/commit/a7b1333c3f0a7d608dca5eea8d313e6931d7be57)), closes [#368](https://github.com/shore-group/h1-search/issues/368) [#373](https://github.com/shore-group/h1-search/issues/373)

## [1.4.6](https://github.com/shore-group/h1-search/compare/v1.4.5...v1.4.6) (2022-05-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.4.5](https://github.com/shore-group/h1-search/compare/v1.4.4...v1.4.5) (2022-05-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.4.4](https://github.com/shore-group/h1-search/compare/v1.4.3...v1.4.4) (2022-05-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.4.3](https://github.com/shore-group/h1-search/compare/v1.4.2...v1.4.3) (2022-05-25)

### Bug Fixes

- **QueryBuilder:** add missing aggregations to test ([#366](https://github.com/shore-group/h1-search/issues/366)) ([219ae56](https://github.com/shore-group/h1-search/commit/219ae5666c7d85d5b328d114f615f59d701a6229))

## [1.4.2](https://github.com/shore-group/h1-search/compare/v1.4.1...v1.4.2) (2022-05-23)

### Bug Fixes

- **trial aggregation:** change status agg group name and remove console log ([#365](https://github.com/shore-group/h1-search/issues/365)) ([eb4e7f0](https://github.com/shore-group/h1-search/commit/eb4e7f0ed08bb59dca48faa3cc2ec070b9235555))

## [1.4.1](https://github.com/shore-group/h1-search/compare/v1.4.0...v1.4.1) (2022-05-23)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.4.0](https://github.com/shore-group/h1-search/compare/v1.3.0...v1.4.0) (2022-05-23)

### Features

- **trial aggs:** add group by person role ([#359](https://github.com/shore-group/h1-search/issues/359)) ([8725300](https://github.com/shore-group/h1-search/commit/8725300b77546db7a36ed8c4346cac8e63962dc2))

# [1.3.0](https://github.com/shore-group/h1-search/compare/v1.2.1...v1.3.0) (2022-05-23)

### Features

- **trial aggs:** add group by country aggregation ([#360](https://github.com/shore-group/h1-search/issues/360)) ([40e3bd0](https://github.com/shore-group/h1-search/commit/40e3bd0c74f12418374a4b08cfcb3da5551af3f6))

## [1.2.1](https://github.com/shore-group/h1-search/compare/v1.2.0...v1.2.1) (2022-05-23)

**Note:** Version bump only for package @h1eng/search-resource-server

# [1.2.0](https://github.com/shore-group/h1-search/compare/v1.1.0...v1.2.0) (2022-05-23)

### Features

- **trial aggs:** add group by phase and status ([#361](https://github.com/shore-group/h1-search/issues/361)) ([22dd62e](https://github.com/shore-group/h1-search/commit/22dd62ecb36f1b3288a5c2c142a43ce5eb3ff01c))

# [1.1.0](https://github.com/shore-group/h1-search/compare/v1.0.164...v1.1.0) (2022-05-20)

### Features

- **trial aggs:** add facility count aggregation ([#358](https://github.com/shore-group/h1-search/issues/358)) ([092baf8](https://github.com/shore-group/h1-search/commit/092baf8fb310e107db849e8f6c311b7ece7d63ea))

## [1.0.164](https://github.com/shore-group/h1-search/compare/v1.0.163...v1.0.164) (2022-05-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.163](https://github.com/shore-group/h1-search/compare/v1.0.162...v1.0.163) (2022-05-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.162](https://github.com/shore-group/h1-search/compare/v1.0.161...v1.0.162) (2022-05-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.161](https://github.com/shore-group/h1-search/compare/v1.0.160...v1.0.161) (2022-05-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.160](https://github.com/shore-group/h1-search/compare/v1.0.159...v1.0.160) (2022-05-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.159](https://github.com/shore-group/h1-search/compare/v1.0.158...v1.0.159) (2022-05-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.158](https://github.com/shore-group/h1-search/compare/v1.0.157...v1.0.158) (2022-05-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.157](https://github.com/shore-group/h1-search/compare/v1.0.156...v1.0.157) (2022-05-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.156](https://github.com/shore-group/h1-search/compare/v1.0.155...v1.0.156) (2022-05-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.155](https://github.com/shore-group/h1-search/compare/v1.0.154...v1.0.155) (2022-05-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.154](https://github.com/shore-group/h1-search/compare/v1.0.153...v1.0.154) (2022-05-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.153](https://github.com/shore-group/h1-search/compare/v1.0.152...v1.0.153) (2022-05-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.152](https://github.com/shore-group/h1-search/compare/v1.0.151...v1.0.152) (2022-05-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.151](https://github.com/shore-group/h1-search/compare/v1.0.150...v1.0.151) (2022-05-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.150](https://github.com/shore-group/h1-search/compare/v1.0.149...v1.0.150) (2022-05-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.149](https://github.com/shore-group/h1-search/compare/v1.0.148...v1.0.149) (2022-05-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.148](https://github.com/shore-group/h1-search/compare/v1.0.147...v1.0.148) (2022-04-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.147](https://github.com/shore-group/h1-search/compare/v1.0.146...v1.0.147) (2022-04-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.146](https://github.com/shore-group/h1-search/compare/v1.0.145...v1.0.146) (2022-04-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.145](https://github.com/shore-group/h1-search/compare/v1.0.144...v1.0.145) (2022-04-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.144](https://github.com/shore-group/h1-search/compare/v1.0.143...v1.0.144) (2022-04-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.143](https://github.com/shore-group/h1-search/compare/v1.0.142...v1.0.143) (2022-04-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.142](https://github.com/shore-group/h1-search/compare/v1.0.141...v1.0.142) (2022-04-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.141](https://github.com/shore-group/h1-search/compare/v1.0.140...v1.0.141) (2022-04-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.140](https://github.com/shore-group/h1-search/compare/v1.0.139...v1.0.140) (2022-04-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.139](https://github.com/shore-group/h1-search/compare/v1.0.138...v1.0.139) (2022-04-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.138](https://github.com/shore-group/h1-search/compare/v1.0.137...v1.0.138) (2022-04-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.137](https://github.com/shore-group/h1-search/compare/v1.0.136...v1.0.137) (2022-04-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.136](https://github.com/shore-group/h1-search/compare/v1.0.135...v1.0.136) (2022-04-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.135](https://github.com/shore-group/h1-search/compare/v1.0.134...v1.0.135) (2022-04-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.134](https://github.com/shore-group/h1-search/compare/v1.0.133...v1.0.134) (2022-04-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.133](https://github.com/shore-group/h1-search/compare/v1.0.132...v1.0.133) (2022-04-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.132](https://github.com/shore-group/h1-search/compare/v1.0.131...v1.0.132) (2022-04-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.131](https://github.com/shore-group/h1-search/compare/v1.0.130...v1.0.131) (2022-04-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.130](https://github.com/shore-group/h1-search/compare/v1.0.129...v1.0.130) (2022-03-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.129](https://github.com/shore-group/h1-search/compare/v1.0.128...v1.0.129) (2022-03-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.128](https://github.com/shore-group/h1-search/compare/v1.0.127...v1.0.128) (2022-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.127](https://github.com/shore-group/h1-search/compare/v1.0.126...v1.0.127) (2022-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.126](https://github.com/shore-group/h1-search/compare/v1.0.125...v1.0.126) (2022-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.125](https://github.com/shore-group/h1-search/compare/v1.0.124...v1.0.125) (2022-03-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.124](https://github.com/shore-group/h1-search/compare/v1.0.123...v1.0.124) (2022-03-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.123](https://github.com/shore-group/h1-search/compare/v1.0.122...v1.0.123) (2022-03-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.122](https://github.com/shore-group/h1-search/compare/v1.0.121...v1.0.122) (2022-03-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.121](https://github.com/shore-group/h1-search/compare/v1.0.120...v1.0.121) (2022-03-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.120](https://github.com/shore-group/h1-search/compare/v1.0.119...v1.0.120) (2022-03-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.119](https://github.com/shore-group/h1-search/compare/v1.0.118...v1.0.119) (2022-03-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.118](https://github.com/shore-group/h1-search/compare/v1.0.117...v1.0.118) (2022-03-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.117](https://github.com/shore-group/h1-search/compare/v1.0.116...v1.0.117) (2022-03-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.116](https://github.com/shore-group/h1-search/compare/v1.0.115...v1.0.116) (2022-03-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.115](https://github.com/shore-group/h1-search/compare/v1.0.114...v1.0.115) (2022-03-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.114](https://github.com/shore-group/h1-search/compare/v1.0.113...v1.0.114) (2022-02-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.113](https://github.com/shore-group/h1-search/compare/v1.0.112...v1.0.113) (2022-02-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.112](https://github.com/shore-group/h1-search/compare/v1.0.111...v1.0.112) (2022-02-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.111](https://github.com/shore-group/h1-search/compare/v1.0.110...v1.0.111) (2022-02-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.110](https://github.com/shore-group/h1-search/compare/v1.0.109...v1.0.110) (2022-02-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.109](https://github.com/shore-group/h1-search/compare/v1.0.108...v1.0.109) (2022-02-16)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.108](https://github.com/shore-group/h1-search/compare/v1.0.107...v1.0.108) (2022-02-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.107](https://github.com/shore-group/h1-search/compare/v1.0.106...v1.0.107) (2022-02-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.106](https://github.com/shore-group/h1-search/compare/v1.0.105...v1.0.106) (2022-02-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.105](https://github.com/shore-group/h1-search/compare/v1.0.104...v1.0.105) (2022-02-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.104](https://github.com/shore-group/h1-search/compare/v1.0.103...v1.0.104) (2022-02-11)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.103](https://github.com/shore-group/h1-search/compare/v1.0.102...v1.0.103) (2022-02-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.102](https://github.com/shore-group/h1-search/compare/v1.0.101...v1.0.102) (2022-02-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.101](https://github.com/shore-group/h1-search/compare/v1.0.100...v1.0.101) (2022-02-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.100](https://github.com/shore-group/h1-search/compare/v1.0.99...v1.0.100) (2022-02-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.99](https://github.com/shore-group/h1-search/compare/v1.0.98...v1.0.99) (2022-02-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.98](https://github.com/shore-group/h1-search/compare/v1.0.97...v1.0.98) (2022-02-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.97](https://github.com/shore-group/h1-search/compare/v1.0.96...v1.0.97) (2022-02-02)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.96](https://github.com/shore-group/h1-search/compare/v1.0.95...v1.0.96) (2022-01-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.95](https://github.com/shore-group/h1-search/compare/v1.0.94...v1.0.95) (2022-01-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.94](https://github.com/shore-group/h1-search/compare/v1.0.93...v1.0.94) (2022-01-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.93](https://github.com/shore-group/h1-search/compare/v1.0.92...v1.0.93) (2022-01-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.92](https://github.com/shore-group/h1-search/compare/v1.0.91...v1.0.92) (2022-01-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.91](https://github.com/shore-group/h1-search/compare/v1.0.90...v1.0.91) (2022-01-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.90](https://github.com/shore-group/h1-search/compare/v1.0.89...v1.0.90) (2022-01-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.89](https://github.com/shore-group/h1-search/compare/v1.0.88...v1.0.89) (2022-01-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.88](https://github.com/shore-group/h1-search/compare/v1.0.87...v1.0.88) (2022-01-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.87](https://github.com/shore-group/h1-search/compare/v1.0.86...v1.0.87) (2022-01-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.86](https://github.com/shore-group/h1-search/compare/v1.0.85...v1.0.86) (2022-01-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.85](https://github.com/shore-group/h1-search/compare/v1.0.84...v1.0.85) (2022-01-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.84](https://github.com/shore-group/h1-search/compare/v1.0.83...v1.0.84) (2022-01-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.83](https://github.com/shore-group/h1-search/compare/v1.0.82...v1.0.83) (2022-01-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.82](https://github.com/shore-group/h1-search/compare/v1.0.81...v1.0.82) (2022-01-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.81](https://github.com/shore-group/h1-search/compare/v1.0.80...v1.0.81) (2022-01-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.80](https://github.com/shore-group/h1-search/compare/v1.0.79...v1.0.80) (2021-12-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.79](https://github.com/shore-group/h1-search/compare/v1.0.78...v1.0.79) (2021-12-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.78](https://github.com/shore-group/h1-search/compare/v1.0.77...v1.0.78) (2021-12-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.77](https://github.com/shore-group/h1-search/compare/v1.0.76...v1.0.77) (2021-12-10)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.76](https://github.com/shore-group/h1-search/compare/v1.0.75...v1.0.76) (2021-12-06)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.75](https://github.com/shore-group/h1-search/compare/v1.0.74...v1.0.75) (2021-12-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.74](https://github.com/shore-group/h1-search/compare/v1.0.73...v1.0.74) (2021-11-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.73](https://github.com/shore-group/h1-search/compare/v1.0.72...v1.0.73) (2021-11-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.72](https://github.com/shore-group/h1-search/compare/v1.0.71...v1.0.72) (2021-11-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.71](https://github.com/shore-group/h1-search/compare/v1.0.70...v1.0.71) (2021-11-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.70](https://github.com/shore-group/h1-search/compare/v1.0.69...v1.0.70) (2021-11-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.69](https://github.com/shore-group/h1-search/compare/v1.0.68...v1.0.69) (2021-11-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.68](https://github.com/shore-group/h1-search/compare/v1.0.67...v1.0.68) (2021-11-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.67](https://github.com/shore-group/h1-search/compare/v1.0.66...v1.0.67) (2021-11-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.66](https://github.com/shore-group/h1-search/compare/v1.0.65...v1.0.66) (2021-11-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.65](https://github.com/shore-group/h1-search/compare/v1.0.64...v1.0.65) (2021-11-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.64](https://github.com/shore-group/h1-search/compare/v1.0.63...v1.0.64) (2021-11-18)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.63](https://github.com/shore-group/h1-search/compare/v1.0.62...v1.0.63) (2021-11-17)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.62](https://github.com/shore-group/h1-search/compare/v1.0.61...v1.0.62) (2021-11-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.61](https://github.com/shore-group/h1-search/compare/v1.0.60...v1.0.61) (2021-11-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.60](https://github.com/shore-group/h1-search/compare/v1.0.59...v1.0.60) (2021-11-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.59](https://github.com/shore-group/h1-search/compare/v1.0.58...v1.0.59) (2021-11-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.58](https://github.com/shore-group/h1-search/compare/v1.0.57...v1.0.58) (2021-11-07)

### Reverts

- Revert "[HPES-1478]calculate trials stats in two decimal values (#231)" (#236) ([c15f063](https://github.com/shore-group/h1-search/commit/c15f0635bd3724aa2864aebc7f59db057208788c)), closes [#231](https://github.com/shore-group/h1-search/issues/231) [#236](https://github.com/shore-group/h1-search/issues/236)

## [1.0.57](https://github.com/shore-group/h1-search/compare/v1.0.56...v1.0.57) (2021-11-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.56](https://github.com/shore-group/h1-search/compare/v1.0.55...v1.0.56) (2021-11-05)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.55](https://github.com/shore-group/h1-search/compare/v1.0.54...v1.0.55) (2021-10-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.54](https://github.com/shore-group/h1-search/compare/v1.0.53...v1.0.54) (2021-10-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.53](https://github.com/shore-group/h1-search/compare/v1.0.52...v1.0.53) (2021-10-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.52](https://github.com/shore-group/h1-search/compare/v1.0.51...v1.0.52) (2021-10-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.51](https://github.com/shore-group/h1-search/compare/v1.0.50...v1.0.51) (2021-10-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.50](https://github.com/shore-group/h1-search/compare/v1.0.49...v1.0.50) (2021-10-19)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.49](https://github.com/shore-group/h1-search/compare/v1.0.48...v1.0.49) (2021-10-15)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.48](https://github.com/shore-group/h1-search/compare/v1.0.47...v1.0.48) (2021-10-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.47](https://github.com/shore-group/h1-search/compare/v1.0.46...v1.0.47) (2021-10-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.46](https://github.com/shore-group/h1-search/compare/v1.0.45...v1.0.46) (2021-10-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.45](https://github.com/shore-group/h1-search/compare/v1.0.44...v1.0.45) (2021-10-12)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.44](https://github.com/shore-group/h1-search/compare/v1.0.43...v1.0.44) (2021-10-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.43](https://github.com/shore-group/h1-search/compare/v1.0.42...v1.0.43) (2021-10-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.42](https://github.com/shore-group/h1-search/compare/v1.0.41...v1.0.42) (2021-10-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.41](https://github.com/shore-group/h1-search/compare/v1.0.40...v1.0.41) (2021-10-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.40](https://github.com/shore-group/h1-search/compare/v1.0.39...v1.0.40) (2021-10-07)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.39](https://github.com/shore-group/h1-search/compare/v1.0.38...v1.0.39) (2021-10-04)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.38](https://github.com/shore-group/h1-search/compare/v1.0.37...v1.0.38) (2021-09-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.37](https://github.com/shore-group/h1-search/compare/v1.0.36...v1.0.37) (2021-09-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.36](https://github.com/shore-group/h1-search/compare/v1.0.35...v1.0.36) (2021-09-29)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.35](https://github.com/shore-group/h1-search/compare/v1.0.34...v1.0.35) (2021-09-28)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.34](https://github.com/shore-group/h1-search/compare/v1.0.33...v1.0.34) (2021-09-27)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.33](https://github.com/shore-group/h1-search/compare/v1.0.32...v1.0.33) (2021-09-24)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.32](https://github.com/shore-group/h1-search/compare/v1.0.31...v1.0.32) (2021-09-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.31](https://github.com/shore-group/h1-search/compare/v1.0.30...v1.0.31) (2021-09-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.30](https://github.com/shore-group/h1-search/compare/v1.0.29...v1.0.30) (2021-09-23)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.29](https://github.com/shore-group/h1-search/compare/v1.0.28...v1.0.29) (2021-09-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.28](https://github.com/shore-group/h1-search/compare/v1.0.27...v1.0.28) (2021-09-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.27](https://github.com/shore-group/h1-search/compare/v1.0.26...v1.0.27) (2021-09-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.26](https://github.com/shore-group/h1-search/compare/v1.0.25...v1.0.26) (2021-09-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.25](https://github.com/shore-group/h1-search/compare/v1.0.24...v1.0.25) (2021-09-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.24](https://github.com/shore-group/h1-search/compare/v1.0.23...v1.0.24) (2021-09-22)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.23](https://github.com/shore-group/h1-search/compare/v1.0.22...v1.0.23) (2021-09-21)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.22](https://github.com/shore-group/h1-search/compare/v1.0.21...v1.0.22) (2021-09-20)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.21](https://github.com/shore-group/h1-search/compare/v1.0.20...v1.0.21) (2021-09-14)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.20](https://github.com/shore-group/h1-search/compare/v1.0.19...v1.0.20) (2021-09-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.19](https://github.com/shore-group/h1-search/compare/v1.0.18...v1.0.19) (2021-09-13)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.18](https://github.com/shore-group/h1-search/compare/v1.0.17...v1.0.18) (2021-09-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.17](https://github.com/shore-group/h1-search/compare/v1.0.16...v1.0.17) (2021-09-09)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.16](https://github.com/shore-group/h1-search/compare/v1.0.15...v1.0.16) (2021-09-08)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.15](https://github.com/shore-group/h1-search/compare/v1.0.14...v1.0.15) (2021-09-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.14](https://github.com/shore-group/h1-search/compare/v1.0.13...v1.0.14) (2021-09-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.13](https://github.com/shore-group/h1-search/compare/v1.0.12...v1.0.13) (2021-09-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.12](https://github.com/shore-group/h1-search/compare/v1.0.11...v1.0.12) (2021-09-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.11](https://github.com/shore-group/h1-search/compare/v1.0.10...v1.0.11) (2021-09-03)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.10](https://github.com/shore-group/h1-search/compare/v1.0.9...v1.0.10) (2021-09-01)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.9](https://github.com/shore-group/h1-search/compare/v1.0.8...v1.0.9) (2021-08-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.8](https://github.com/shore-group/h1-search/compare/v1.0.7...v1.0.8) (2021-08-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.7](https://github.com/shore-group/h1-search/compare/v1.0.6...v1.0.7) (2021-08-31)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.6](https://github.com/shore-group/h1-search/compare/v1.0.5...v1.0.6) (2021-08-30)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.5](https://github.com/shore-group/h1-search/compare/v1.0.4...v1.0.5) (2021-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.4](https://github.com/shore-group/h1-search/compare/v1.0.3...v1.0.4) (2021-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.3](https://github.com/shore-group/h1-search/compare/v1.0.2...v1.0.3) (2021-08-26)

**Note:** Version bump only for package @h1eng/search-resource-server

## [1.0.2](https://github.com/shore-group/h1-search/compare/v1.0.1...v1.0.2) (2021-08-25)

**Note:** Version bump only for package @h1eng/search-resource-server

## 1.0.1 (2021-08-25)

### Bug Fixes

- HPE-1127 Use the filter path if present, otherwise use the path, when determining if filters contain selected values ([02233eb](https://github.com/shore-group/h1-search/commit/02233eb53932b042a9d9880d73415e346ff1b3f9))
- HPE-1127 Use the location token during autocomplete for the city filter ([f68ef9d](https://github.com/shore-group/h1-search/commit/f68ef9dffc4cacbb952a1d0a2a771a7c28059f44))
- HPE-1151 Intercept the input queries to use the new search framework for autocomplete searches in the filters ([f172090](https://github.com/shore-group/h1-search/commit/f1720900ae092f3a51535fbbc43b13be5e6f1587))
- HPE-1160 Calculate the value for the minimum_should_match field ([dd9af37](https://github.com/shore-group/h1-search/commit/dd9af3765a21092c00b5303745a5a60d8caf0db0))
- HPE-1236 Added the initialOptions RPC call so that clients (mainly the UI) can request the initial filter options from the search domain (which makes an ES query and returns the results in GQL objects). ([1807974](https://github.com/shore-group/h1-search/commit/1807974122e91016eb362d4cbc8acf31bf6a0a71))
- HPE-1237 Implement name suggest search (cloned from h1 right now, but will be the main eventually when all searches point to the new search domain) ([8a38f44](https://github.com/shore-group/h1-search/commit/8a38f448e45ca4a01f07e76eb35f7af383e7c91b))
- HPE-1446 Added collaborator service and implemented hasCollaborators ([2ecbdbc](https://github.com/shore-group/h1-search/commit/2ecbdbcaea843ef468f870e351cc87956c54868b))
- HPE-1570 Retrieve the tag names from the account service and pass it into the language recognition function ([b9fc344](https://github.com/shore-group/h1-search/commit/b9fc3448be60406fec43e57f05706c450d726f29))
- HPE-1618 Keep date for range search in milliseconds (do not convert it to seconds) ([#100](https://github.com/shore-group/h1-search/issues/100)) ([1f79557](https://github.com/shore-group/h1-search/commit/1f7955745cb7ca2cb86f51edbb754cb73cfa9d30))
- HPE-1718 Use filters to search ES trials ([023f4a8](https://github.com/shore-group/h1-search/commit/023f4a8f9ee269e53009b845e4cf66be48fac315))
- HPE-1969 Map intervention other names (formerly generic names) to the appropriate ES field ([2cd1960](https://github.com/shore-group/h1-search/commit/2cd196050e2db71837ddcd4de6a47535dd51c6a8))
- HPE-1969 Only look in the interventionName field when filtering by intervention names ([768698e](https://github.com/shore-group/h1-search/commit/768698e1937dedf0b5afb310dc3a93a5a06a2ae5))
- HPE-1975 Implement script filtering and script sorting for study type column in clinical trials ([53c375a](https://github.com/shore-group/h1-search/commit/53c375a75f08b31ea86386fc46981f7eafa48c89))
- HPE-1982 Adjust to return flattened document data for tabular view consumption ([1abcf2a](https://github.com/shore-group/h1-search/commit/1abcf2ae2ae62af3afdc7d31464ffa5f0e1d66ce))
- HPE-1982 This adds the ability to query a full clinical trial document by id from ES ([e714a96](https://github.com/shore-group/h1-search/commit/e714a967a4363466b59d4b6439c3851692e94a58))
- HPE-1983 Add a clause in the shoulds for 'null' when one of the study phase value options is 'Not Applicable' ([bf8d14b](https://github.com/shore-group/h1-search/commit/bf8d14b1a893a065afb9ed29f0a0093470b78558))
- HPE-2011 Fix filter search for sponsor so text is case insensitive. Also fixed for allocation, interventions model, sites, states, and cities ([b447685](https://github.com/shore-group/h1-search/commit/b447685e8c23e93647ff41ba197bc6b2fc53a0d7))
- HPE-686 Remove profile feature check, profile role filter values and inclusion in searches ([b4ed325](https://github.com/shore-group/h1-search/commit/b4ed325ec741ff8ba2d152c70ca03d6ca49ad051))
- HPE-930 Adjust the KOL Search input and add the autocomplete aggregation to the name query, if needed. ([d93d1d3](https://github.com/shore-group/h1-search/commit/d93d1d3d63ecb820a55dce1dfaaba2bdb61af1cd))
