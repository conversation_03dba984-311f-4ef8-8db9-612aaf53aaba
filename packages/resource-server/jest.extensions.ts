import _ from "lodash";

function termQuery(
  actual: unknown,
  path: string,
  value: string | number | boolean
) {
  if (!_.isPlainObject(actual)) {
    throw new Error("Actual value must be an object");
  }

  const pass = _.get(actual, ["term", path]) === value;

  return {
    pass,
    message: pass
      ? () =>
          `expect ${JSON.stringify(
            actual
          )} not to be a TermQuery with key '${path}' and value '${value}'`
      : () =>
          `expect ${JSON.stringify(
            actual
          )} to be a TermQuery with key '${path}' and value '${value}'`
  };
}

expect.extend({
  termQuery,
  termsQuery(
    actual: unknown,
    path: string,
    expectedValues: Readonly<Array<string>>
  ) {
    if (!_.isPlainObject(actual)) {
      throw new Error("Actual value must be an object");
    }

    const actualValues = _.get(actual, ["terms", path]);

    const pass = _.xor(actualValues, expectedValues).length === 0;

    return {
      pass,
      message: pass
        ? () =>
            `expect ${JSON.stringify(
              actual
            )} not to be a TermsQuery with key '${path}' and values '${expectedValues}'`
        : () =>
            `expect ${JSON.stringify(
              actual
            )} to be a TermsQuery with key '${path}' and values '${expectedValues}'`
    };
  },
  fieldExists(actual: unknown, path: string) {
    if (!_.isPlainObject(actual)) {
      throw new Error("Actual value must be an object");
    }

    const pass = _.get(actual, ["exists", "field"]) === path;

    return {
      pass,
      message: pass
        ? () =>
            `expect ${JSON.stringify(
              actual
            )} not to be an ExistsQuery with key '${path}'`
        : () =>
            `expect ${JSON.stringify(
              actual
            )} to be an ExistsQuery with key '${path}'`
    };
  },
  positiveRange(actual: unknown, path: string) {
    if (!_.isPlainObject(actual)) {
      throw new Error("Actual value must be an object");
    }

    const passGreaterThan = _.get(actual, ["range", path, "gt"]) === 0;
    const passGreaterThanOrEqualTo = _.get(actual, ["range", path, "gte"]) > 0;

    const containsLessThan = _.has(actual, ["range", path, "lt"]);
    const containsLessThanOrEqualTo = _.has(actual, ["range", path, "lte"]);

    const pass =
      (passGreaterThan || passGreaterThanOrEqualTo) &&
      !containsLessThan &&
      !containsLessThanOrEqualTo;

    return {
      pass,
      message: pass
        ? () =>
            `expect ${JSON.stringify(
              actual
            )} not to be a RangeQuery with key '${path}' and either non-negative 'gt' or positive 'gte' specified`
        : () =>
            `expect ${JSON.stringify(
              actual
            )} to be a RangeQuery with key '${path}' and either non-negative 'gt' or positive 'gte' specified`
    };
  },
  containsOnly(
    actual: unknown,
    expectedValues: Readonly<Array<string | number>>
  ) {
    if (!_.isArray(actual)) {
      throw new Error("Actual value must be an array");
    }

    const pass = _.xor(actual, expectedValues).length === 0;

    return {
      pass,
      message: pass
        ? () =>
            `expect ${JSON.stringify(
              actual
            )} not to be an array with values '${expectedValues}'`
        : () =>
            `expect ${JSON.stringify(
              actual
            )} to be an array with values '${expectedValues}'`
    };
  },
  projectIdFilter(actual: unknown, value: string) {
    return termQuery(actual, "projectIds", value);
  }
});
