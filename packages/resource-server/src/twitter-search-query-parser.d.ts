declare module "twitter-search-query-parser" {
  type ITree = TerminalClause | ArrayClause | SingleChildClause;

  type TerminalClause = ["Text" | "Exactly", string];
  type ArrayClause = ["And" | "Or", ITree[]];
  type SingleChildClause = ["Group" | "Including" | "Excluding", ITree];

  export function parse(query: string): ITree;

  export function simplify(
    query: ITree,
    disallow: { disallow: string[] }
  ): ITree;
}
