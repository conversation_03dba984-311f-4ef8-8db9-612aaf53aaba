import { faker } from "@faker-js/faker";
import _ from "lodash";

const MAX_NUMBER_OF_ELEMENTS = 100;

const NON_PLAIN_OBJECTS: Readonly<Array<any>> = [
  undefined,
  null,
  faker.datatype.boolean(),
  faker.datatype.number(),
  new Number(faker.datatype.number()),
  faker.datatype.string(),
  new String(faker.datatype.string()),
  [],
  new RegExp(faker.random.word()),
  () => faker.datatype.string()
];

const value = typeof ({} as any); // this unfortunately cannot be inlined

const createListOfUniqueWords = createListOfUniqueValues.bind(null, "string");
const createListOfUniqueNumbers = createListOfUniqueValues.bind(null, "number");

function createListOfUniqueValues(
  datatype: keyof typeof faker.datatype & typeof value
) {
  const atLeast1Element = _.random(MAX_NUMBER_OF_ELEMENTS) + 1;

  return _(_.range(0, atLeast1Element))
    .map(() => faker.datatype[datatype]())
    .uniq()
    .value() as Array<typeof datatype>;
}

describe("termQuery", () => {
  it("should throw an error when actual is a non-plain object", () => {
    const path = faker.random.alphaNumeric();
    const value = faker.datatype.string();

    for (const actual of NON_PLAIN_OBJECTS) {
      try {
        // symmetric
        expect(actual).termQuery(path, value);
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an object");
      }

      try {
        // asymmetric
        expect([actual]).toEqual(
          expect.arrayContaining([expect.termQuery(path, value)])
        );
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an object");
      }
    }
  });

  it("should fail when paths are not equal", () => {
    const actualPath = faker.random.word();
    const expectedPath = faker.random.word() + faker.random.alphaNumeric();

    const value = faker.datatype.string();

    const actual = {
      term: {
        [actualPath]: value
      }
    };

    // symmetric
    expect(actual).not.termQuery(expectedPath, value);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.termQuery(expectedPath, value)])
    );
  });

  it("should fail when values are not equal", () => {
    const path = faker.random.word();

    const actualValue = faker.random.word();
    const expectedValue = _.shuffle(actualValue).join();

    const actual = {
      term: {
        [path]: actualValue
      }
    };

    // symmetric
    expect(actual).not.termQuery(path, expectedValue);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.termQuery(path, expectedValue)])
    );
  });

  it("should pass when both paths and values are equal", () => {
    const path = faker.random.word();

    const values = [
      faker.datatype.string(),
      faker.datatype.boolean(),
      faker.datatype.number()
    ];

    for (const value of values) {
      const actual = {
        term: {
          [path]: value
        }
      };

      // symmetric
      expect(actual).termQuery(path, value);

      // asymmetric
      expect([actual]).toEqual(
        expect.arrayContaining([expect.termQuery(path, value)])
      );
    }
  });
});

describe("termsQuery", () => {
  it("should throw an error when actual is a non-plain object", () => {
    const path = faker.random.alphaNumeric();

    const values = [faker.datatype.string()];

    for (const actual of NON_PLAIN_OBJECTS) {
      try {
        // symmetric
        expect(actual).termsQuery(path, values);
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an object");
      }

      try {
        // asymmetric
        expect([actual]).toEqual(
          expect.arrayContaining([expect.termsQuery(path, values)])
        );
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an object");
      }
    }
  });

  it("should fail when paths are not equal", () => {
    const path = faker.random.word();

    const values = [faker.datatype.string()];

    const actual = {
      terms: {
        [path + faker.random.alphaNumeric()]: values
      }
    };

    // symmetric
    expect(actual).not.termsQuery(path, values);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.termsQuery(path, values)])
    );
  });

  it("should fail when values arrays are equal length but elements are not equal", () => {
    const path = faker.random.word();

    const value = faker.datatype.string();
    const actualValues = [value];
    const expectedValues = [value + faker.random.alphaNumeric()];

    const actual = {
      terms: {
        [path]: actualValues
      }
    };

    // symmetric
    expect(actual).not.termsQuery(path, expectedValues);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.termsQuery(path, expectedValues)])
    );
  });

  it("should fail when expected values is a proper subset of actual values", () => {
    const path = faker.random.word();

    const actualValues = createListOfUniqueWords();
    const expectedValues = [...actualValues];
    expectedValues.splice(_.random(expectedValues.length - 1), 1);

    const actual = {
      terms: {
        [path]: actualValues
      }
    };

    // symmetric
    expect(actual).not.termsQuery(path, expectedValues);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.termsQuery(path, expectedValues)])
    );
  });

  it("should fail when actual values is a proper subset of expected values", () => {
    const path = faker.random.word();

    const expectedValues = createListOfUniqueWords();
    const actualValues = [...expectedValues];
    actualValues.splice(_.random(actualValues.length - 1), 1);

    const actual = {
      terms: {
        [path]: actualValues
      }
    };

    // symmetric
    expect(actual).not.termsQuery(path, expectedValues);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.termsQuery(path, expectedValues)])
    );
  });

  it("should pass when paths are equal and values are equal in order", () => {
    const path = faker.random.word();

    const values = createListOfUniqueWords();

    const actual = {
      terms: {
        [path]: values
      }
    };

    // symmetric
    expect(actual).termsQuery(path, values);

    // asymmetric
    expect([actual]).toEqual(
      expect.arrayContaining([expect.termsQuery(path, values)])
    );
  });

  it("should pass when paths are equal and values are set-equivalent", () => {
    const path = faker.random.word();

    const actualValues = createListOfUniqueWords();
    const expectedValues = _.shuffle(actualValues);

    const actual = {
      terms: {
        [path]: actualValues
      }
    };

    // symmetric
    expect(actual).termsQuery(path, expectedValues);

    // asymmetric
    expect([actual]).toEqual(
      expect.arrayContaining([expect.termsQuery(path, expectedValues)])
    );
  });

  it("should pass when paths are equal and values are empty arrays", () => {
    const path = faker.random.word();

    const values: Array<string> = [];

    const actual = {
      terms: {
        [path]: values
      }
    };

    // symmetric
    expect(actual).termsQuery(path, values);

    // asymmetric
    expect([actual]).toEqual(
      expect.arrayContaining([expect.termsQuery(path, values)])
    );
  });

  it("should pass when paths are equal and actual values contains duplicates of expected values", () => {
    const path = faker.random.word();

    const expectedValues = createListOfUniqueWords();

    // create a copy of expected values with a random element duplicated onto the end for actual values
    const actualValues = [...expectedValues];
    const indexToCopy = _.random(actualValues.length - 1);
    actualValues.push(actualValues[indexToCopy]);

    const actual = {
      terms: {
        [path]: actualValues
      }
    };

    // symmetric
    expect(actual).termsQuery(path, expectedValues);

    // asymmetric
    expect([actual]).toEqual(
      expect.arrayContaining([expect.termsQuery(path, expectedValues)])
    );
  });

  it("should pass when paths are equal and expected values contains duplicates of actual values", () => {
    const path = faker.random.word();

    const actualValues = createListOfUniqueWords();

    // create a copy of actual values with a random element duplicated onto the end for expected values
    const expectedValues = [...actualValues];
    const indexToCopy = _.random(expectedValues.length - 1);
    expectedValues.push(expectedValues[indexToCopy]);

    const actual = {
      terms: {
        [path]: actualValues
      }
    };

    // symmetric
    expect(actual).termsQuery(path, expectedValues);

    // asymmetric
    expect([actual]).toEqual(
      expect.arrayContaining([expect.termsQuery(path, expectedValues)])
    );
  });
});

describe("fieldExists", () => {
  it("should throw an error when actual is a non-plain object", () => {
    const path = faker.random.word();

    for (const actual of NON_PLAIN_OBJECTS) {
      try {
        // symmetric
        expect(actual).fieldExists(path);
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an object");
      }

      try {
        // asymmetric
        expect([actual]).toEqual(
          expect.arrayContaining([expect.fieldExists(path)])
        );
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an object");
      }
    }
  });

  it("should fail when paths are not equal", () => {
    const actualPath = faker.random.word();
    const expectedPath = actualPath + faker.random.alphaNumeric();

    const actual = {
      exists: {
        field: actualPath
      }
    };

    // symmetric
    expect(actual).not.fieldExists(expectedPath);

    // asymmetric
    expect(actual).toEqual(
      expect.not.arrayContaining([expect.fieldExists(expectedPath)])
    );
  });

  it("should pass when paths are equal", () => {
    const path = faker.random.word();

    const actual = {
      exists: {
        field: path
      }
    };

    // symmetric
    expect(actual).fieldExists(path);

    // asymmetric
    expect([actual]).toEqual(
      expect.arrayContaining([expect.fieldExists(path)])
    );
  });
});

describe("positiveRange", () => {
  it("should throw an error when actual is a non-plain object", () => {
    const path = faker.random.alphaNumeric();

    for (const actual of NON_PLAIN_OBJECTS) {
      try {
        // symmetric
        expect(actual).positiveRange(path);
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an object");
      }

      try {
        // asymmetric
        expect([actual]).toEqual(
          expect.arrayContaining([expect.positiveRange(path)])
        );
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an object");
      }
    }
  });

  it("should fail when actual does not contain 'range' property", () => {
    const actualPath = faker.random.word();
    const expectedPath = faker.random.word() + faker.random.alphaNumeric();

    const actual = {
      term: {
        [actualPath]: faker.datatype.string()
      }
    };

    // symmetric
    expect(actual).not.positiveRange(expectedPath);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.positiveRange(expectedPath)])
    );
  });

  it("should fail when paths are not equal", () => {
    const actualPath = faker.random.word();
    const expectedPath = faker.random.word() + faker.random.alphaNumeric();

    const actual = {
      range: {
        [actualPath]: {
          gt: 0
        }
      }
    };

    // symmetric
    expect(actual).not.positiveRange(expectedPath);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.positiveRange(expectedPath)])
    );
  });

  it("should fail when range.path does not contain 'gt' property", () => {
    const actualPath = faker.random.word();
    const expectedPath = faker.random.word() + faker.random.alphaNumeric();

    const actual = {
      range: {
        [actualPath]: {}
      }
    };

    // symmetric
    expect(actual).not.positiveRange(expectedPath);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.positiveRange(expectedPath)])
    );
  });

  it("should fail when range.path.gt does not equal 0", () => {
    const actualPath = faker.random.word();
    const expectedPath = faker.random.word() + faker.random.alphaNumeric();

    const actual = {
      range: {
        [actualPath]: {
          gt: faker.datatype.number() + 1
        }
      }
    };

    // symmetric
    expect(actual).not.positiveRange(expectedPath);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.positiveRange(expectedPath)])
    );
  });

  it("should fail when range.path.gte is equal to 0", () => {
    const actualPath = faker.random.word();
    const expectedPath = faker.random.word() + faker.random.alphaNumeric();

    const actual = {
      range: {
        [actualPath]: {
          gte: 0
        }
      }
    };

    // symmetric
    expect(actual).not.positiveRange(expectedPath);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.positiveRange(expectedPath)])
    );
  });

  it("should fail when range.path.gte is negative", () => {
    const actualPath = faker.random.word();
    const expectedPath = faker.random.word() + faker.random.alphaNumeric();

    const actual = {
      range: {
        [actualPath]: {
          gte: 0 - faker.datatype.number()
        }
      }
    };

    // symmetric
    expect(actual).not.positiveRange(expectedPath);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.positiveRange(expectedPath)])
    );
  });

  it("should fail when both paths are equal, 'gt' is equal to 0, but 'lt' exists", () => {
    const path = faker.random.word();

    const actual = {
      range: {
        [path]: {
          gt: 0,
          lt: faker.datatype.number()
        }
      }
    };

    // symmetric
    expect(actual).not.positiveRange(path);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.positiveRange(path)])
    );
  });

  it("should fail when both paths are equal, 'gt' is equal to 0, but 'lte' exists", () => {
    const path = faker.random.word();

    const actual = {
      range: {
        [path]: {
          gt: 0,
          lte: faker.datatype.number()
        }
      }
    };

    // symmetric
    expect(actual).not.positiveRange(path);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.positiveRange(path)])
    );
  });

  it("should fail when both paths are equal, 'gte' is positive, but 'lt' exists", () => {
    const path = faker.random.word();

    const actual = {
      range: {
        [path]: {
          gte: Number.MIN_VALUE + faker.datatype.number(),
          lt: faker.datatype.number()
        }
      }
    };

    // symmetric
    expect(actual).not.positiveRange(path);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.positiveRange(path)])
    );
  });

  it("should fail when both paths are equal, 'gt' is equal to 0, but 'lte' exists", () => {
    const path = faker.random.word();

    const actual = {
      range: {
        [path]: {
          gte: Number.MIN_VALUE + faker.datatype.number(),
          lte: faker.datatype.number()
        }
      }
    };

    // symmetric
    expect(actual).not.positiveRange(path);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.positiveRange(path)])
    );
  });

  it("should pass when both paths are equal and 'gt' is equal to 0", () => {
    const path = faker.random.word();

    const actual = {
      range: {
        [path]: {
          gt: 0
        }
      }
    };

    // symmetric
    expect(actual).positiveRange(path);

    // asymmetric
    expect([actual]).toEqual(
      expect.arrayContaining([expect.positiveRange(path)])
    );
  });

  it("should pass when both paths are equal and 'gte' is not 0 or negative", () => {
    const path = faker.random.word();

    const actual = {
      range: {
        [path]: {
          gte: 1 + faker.datatype.number()
        }
      }
    };

    // symmetric
    expect(actual).positiveRange(path);

    // asymmetric
    expect([actual]).toEqual(
      expect.arrayContaining([expect.positiveRange(path)])
    );
  });

  it("should pass when both paths are equal and 'gte' is slightly greater than 0", () => {
    const path = faker.random.word();

    const actual = {
      range: {
        [path]: {
          gte: Number.MIN_VALUE
        }
      }
    };

    // symmetric
    expect(actual).positiveRange(path);

    // asymmetric
    expect([actual]).toEqual(
      expect.arrayContaining([expect.positiveRange(path)])
    );
  });
});

describe("containsOnly", () => {
  it("should throw an error when actual is not an array", () => {
    const values = [faker.datatype.string()];

    const NON_ARRAY_OBJECTS = [
      undefined,
      null,
      faker.datatype.boolean(),
      faker.datatype.number(),
      new Number(faker.datatype.number()),
      faker.datatype.string(),
      new String(faker.datatype.string()),
      {},
      new RegExp(faker.random.word()),
      () => faker.datatype.string()
    ];

    for (const actual of NON_ARRAY_OBJECTS) {
      try {
        // symmetric
        expect(actual).containsOnly(values);
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an array");
      }

      try {
        // asymmetric
        expect([actual]).toEqual(
          expect.arrayContaining([expect.containsOnly(values)])
        );
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an array");
      }
    }
  });

  it("should fail when values arrays are equal length but elements are not equal", () => {
    const randomWord = faker.datatype.string();
    const randomNumber = faker.datatype.number() + 1; // could be 0 so make at least 1

    for (const value of [randomWord, randomNumber]) {
      const actualValues = [value];
      const expectedValues = [];

      if (typeof value === "string") {
        expectedValues.push(value + value);
      } else {
        expectedValues.push(value * 2);
      }

      // symmetric
      expect(actualValues).not.containsOnly(expectedValues);

      // asymmetric
      expect([actualValues]).toEqual(
        expect.not.arrayContaining([expect.containsOnly(expectedValues)])
      );
    }
  });

  it("should fail when expected values is a proper subset of actual values", () => {
    const randomWords = createListOfUniqueWords();
    const randomNumbers = createListOfUniqueNumbers();

    for (const actualValues of [randomWords, randomNumbers]) {
      const expectedValues = [...actualValues];

      // remove a random item
      expectedValues.splice(_.random(expectedValues.length - 1), 1);

      // symmetric
      expect(actualValues).not.containsOnly(expectedValues);

      // asymmetric
      expect([actualValues]).toEqual(
        expect.not.arrayContaining([expect.containsOnly(expectedValues)])
      );
    }
  });

  it("should fail when actual values is a proper subset of expected values", () => {
    const randomWords = createListOfUniqueWords();
    const randomNumbers = createListOfUniqueNumbers();

    for (const expectedValues of [randomWords, randomNumbers]) {
      const actualValues = [...expectedValues];
      // remove a random item
      actualValues.splice(_.random(actualValues.length - 1), 1);

      // symmetric
      expect(actualValues).not.containsOnly(expectedValues);

      // asymmetric
      expect([actualValues]).toEqual(
        expect.not.arrayContaining([expect.containsOnly(expectedValues)])
      );
    }
  });

  it("should fail when values are stringified versions of same numbers", () => {
    const randomNumbers = createListOfUniqueNumbers();

    const randomNumbersAsStrings = [...randomNumbers].map(String);

    // symmetric
    expect(randomNumbersAsStrings).not.containsOnly(randomNumbers);
    expect(randomNumbers).not.containsOnly(randomNumbersAsStrings);

    // asymmetric
    expect([randomNumbersAsStrings]).toEqual(
      expect.not.arrayContaining([expect.containsOnly(randomNumbers)])
    );
    expect([randomNumbers]).toEqual(
      expect.not.arrayContaining([expect.containsOnly(randomNumbersAsStrings)])
    );
  });

  it("should pass when paths are equal and values are equal in order", () => {
    const randomWords = createListOfUniqueWords();
    const randomNumbers = createListOfUniqueNumbers();

    for (const values of [randomWords, randomNumbers]) {
      // symmetric
      expect(values).containsOnly(values);

      // asymmetric
      expect([values]).toEqual(
        expect.arrayContaining([expect.containsOnly(values)])
      );
    }
  });

  it("should pass when paths are equal and values are set-equivalent", () => {
    const randomWords = createListOfUniqueWords();
    const randomNumbers = createListOfUniqueNumbers();

    for (const actualValues of [randomWords, randomNumbers]) {
      const expectedValues = _.shuffle(actualValues) as Array<string | number>;

      // symmetric
      expect(actualValues).containsOnly(expectedValues);

      // asymmetric
      expect([actualValues]).toEqual(
        expect.arrayContaining([expect.containsOnly(expectedValues)])
      );
    }
  });

  it("should pass when paths are equal and values are empty arrays", () => {
    const values: Array<string | number> = [];

    // symmetric
    expect(values).containsOnly(values);

    // asymmetric
    expect([values]).toEqual(
      expect.arrayContaining([expect.containsOnly(values)])
    );
  });

  it("should pass when paths are equal and expected/actual values contains duplicates of the other", () => {
    const randomWords = createListOfUniqueWords();
    const randomNumbers = createListOfUniqueNumbers();

    for (const expectedValues of [randomWords, randomNumbers]) {
      // create a copy of expected values with a random element duplicated onto the end for actual values
      const actualValues = [...expectedValues];
      const indexToCopy = _.random(actualValues.length - 1);
      actualValues.push(actualValues[indexToCopy]);

      // symmetric
      expect(actualValues).containsOnly(expectedValues);
      expect(expectedValues).containsOnly(actualValues);

      // asymmetric
      expect([actualValues]).toEqual(
        expect.arrayContaining([expect.containsOnly(expectedValues)])
      );
      expect([expectedValues]).toEqual(
        expect.arrayContaining([expect.containsOnly(actualValues)])
      );
    }
  });

  it("should pass when numbers and their string forms are both represented", () => {
    const randomNumber1 = faker.datatype.number();
    const randomNumber2 = faker.datatype.number();

    const values = [
      randomNumber1,
      String(randomNumber1),
      randomNumber2,
      String(randomNumber2)
    ];

    // symmetric
    expect(_.shuffle(values)).containsOnly(_.shuffle(values));

    // asymmetric
    expect([values]).toEqual(
      expect.arrayContaining([expect.containsOnly(values)])
    );
  });
});

describe("projectIdFilter", () => {
  it("should throw an error when actual is a non-plain object", () => {
    const value = faker.datatype.string();

    for (const actual of NON_PLAIN_OBJECTS) {
      try {
        // symmetric
        expect(actual).projectIdFilter(value);
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an object");
      }

      try {
        // asymmetric
        expect([actual]).toEqual(
          expect.arrayContaining([expect.projectIdFilter(value)])
        );
        fail("an error should have been thrown");
      } catch (err: any) {
        expect(err.message).toEqual("Actual value must be an object");
      }
    }
  });

  it("should fail when values are not equal", () => {
    const actualValue = faker.random.word();
    const expectedValue = _.shuffle(actualValue).join();

    const actual = {
      term: {
        projectIds: actualValue
      }
    };

    // symmetric
    expect(actual).not.projectIdFilter(expectedValue);

    // asymmetric
    expect([actual]).toEqual(
      expect.not.arrayContaining([expect.projectIdFilter(expectedValue)])
    );
  });

  it("should pass when values are equal", () => {
    const value = faker.datatype.string();

    const actual = {
      term: {
        projectIds: value
      }
    };

    // symmetric
    expect(actual).projectIdFilter(value);

    // asymmetric
    expect([actual]).toEqual(
      expect.arrayContaining([expect.projectIdFilter(value)])
    );
  });
});
