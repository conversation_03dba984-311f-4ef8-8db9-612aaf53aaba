import Container from "typedi";
// import request from "supertest";
// import { createHealthProbe } from "./app";
import { HealthService } from "./services/HealthService";

afterEach(() => {
  Container.reset();
});

describe("Check node version", () => {
  it("should use node v16", () => {
    expect(process.version).toContain("v20.");
  });
});

//TODO: Figure out why these tests frequently fail in CircleCI
describe.skip("app", () => {
  // const app = createHealthProbe();

  it("should respond with a 200", async () => {
    const mockHealthService = {
      check: jest.fn()
    };

    Container.set(HealthService, mockHealthService);

    // await request(app).get("/_health").expect(200, { ok: true });
  });

  it("should respond with a 503 when healthService.check throws an error object", async () => {
    const mockHealthService = {
      check: jest
        .fn()
        .mockRejectedValue(
          new Error("healthService.check threw an error for some reason")
        )
    };

    Container.set(HealthService, mockHealthService);

    // await request(app)
    //   .get("/_health")
    //   .expect(503, {
    //     errors: [
    //       { message: "healthService.check threw an error for some reason" }
    //     ]
    //   });
  });

  it("should respond with a 503 when healthService.check throws an non-error object", async () => {
    const mockHealthService = {
      check: jest
        .fn()
        .mockRejectedValue("healthService.check threw an error for some reason")
    };

    Container.set(HealthService, mockHealthService);

    // await request(app)
    //   .get("/_health")
    //   .expect(503, {
    //     errors: [
    //       { message: '"healthService.check threw an error for some reason"' }
    //     ]
    //   });
  });
});
