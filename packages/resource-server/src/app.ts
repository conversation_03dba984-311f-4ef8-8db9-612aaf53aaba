import express from "express";
import Container from "typedi";
import { createLogger } from "./lib/Logger";
import { HealthService } from "./services/HealthService";

type ErrorWithMessage = {
  message: string;
};

function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === "object" &&
    error !== null &&
    "message" in error &&
    typeof (error as Record<string, unknown>).message === "string"
  );
}

function toErrorWithMessage(maybeError: unknown): ErrorWithMessage {
  if (isErrorWithMessage(maybeError)) return maybeError;

  try {
    return new Error(JSON.stringify(maybeError));
  } catch {
    // fallback in case there's an error stringifying the maybeError
    // like with circular references for example.
    return new Error(String(maybeError));
  }
}

export const createHealthProbe = (): express.Express => {
  const app = express();
  const logger = createLogger("HealthProbe");

  app.get("/_health", async (_, res) => {
    try {
      const healthService = Container.get(HealthService);
      await healthService.check();
      res.send({ ok: true });
    } catch (err) {
      const errorWithMessage = toErrorWithMessage(err);

      logger.error(
        errorWithMessage,
        `Health probe failed with message: ${errorWithMessage.message}`
      );
      const errors = [{ message: errorWithMessage.message }];
      res.status(503).send({ errors });
    }
  });

  return app;
};
