// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@grpc/grpc-js');
var query_understanding_service_pb = require('./query_understanding_service_pb.js');
var synonym_pb = require('./synonym_pb.js');
var query_intent_pb = require('./query_intent_pb.js');

function serialize_QueryUnderstandingServiceRequest(arg) {
  if (!(arg instanceof query_understanding_service_pb.QueryUnderstandingServiceRequest)) {
    throw new Error('Expected argument of type QueryUnderstandingServiceRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_QueryUnderstandingServiceRequest(buffer_arg) {
  return query_understanding_service_pb.QueryUnderstandingServiceRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_QueryUnderstandingServiceRequestForIndications(arg) {
  if (!(arg instanceof query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications)) {
    throw new Error('Expected argument of type QueryUnderstandingServiceRequestForIndications');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_QueryUnderstandingServiceRequestForIndications(buffer_arg) {
  return query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_QueryUnderstandingServiceResponse(arg) {
  if (!(arg instanceof query_understanding_service_pb.QueryUnderstandingServiceResponse)) {
    throw new Error('Expected argument of type QueryUnderstandingServiceResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_QueryUnderstandingServiceResponse(buffer_arg) {
  return query_understanding_service_pb.QueryUnderstandingServiceResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_QueryUnderstandingServiceResponseForIndications(arg) {
  if (!(arg instanceof query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications)) {
    throw new Error('Expected argument of type QueryUnderstandingServiceResponseForIndications');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_QueryUnderstandingServiceResponseForIndications(buffer_arg) {
  return query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications.deserializeBinary(new Uint8Array(buffer_arg));
}


// The Query understanding service definition
var QueryUnderstandingService = exports.QueryUnderstandingService = {
  getQueryUnderstanding: {
    path: '/QueryUnderstanding/GetQueryUnderstanding',
    requestStream: false,
    responseStream: false,
    requestType: query_understanding_service_pb.QueryUnderstandingServiceRequest,
    responseType: query_understanding_service_pb.QueryUnderstandingServiceResponse,
    requestSerialize: serialize_QueryUnderstandingServiceRequest,
    requestDeserialize: deserialize_QueryUnderstandingServiceRequest,
    responseSerialize: serialize_QueryUnderstandingServiceResponse,
    responseDeserialize: deserialize_QueryUnderstandingServiceResponse,
  },
  getSynonyms: {
    path: '/QueryUnderstanding/GetSynonyms',
    requestStream: false,
    responseStream: false,
    requestType: query_understanding_service_pb.QueryUnderstandingServiceRequest,
    responseType: query_understanding_service_pb.QueryUnderstandingServiceResponse,
    requestSerialize: serialize_QueryUnderstandingServiceRequest,
    requestDeserialize: deserialize_QueryUnderstandingServiceRequest,
    responseSerialize: serialize_QueryUnderstandingServiceResponse,
    responseDeserialize: deserialize_QueryUnderstandingServiceResponse,
  },
  getIndicationSynonymsAndIcdCodes: {
    path: '/QueryUnderstanding/GetIndicationSynonymsAndIcdCodes',
    requestStream: false,
    responseStream: false,
    requestType: query_understanding_service_pb.QueryUnderstandingServiceRequestForIndications,
    responseType: query_understanding_service_pb.QueryUnderstandingServiceResponseForIndications,
    requestSerialize: serialize_QueryUnderstandingServiceRequestForIndications,
    requestDeserialize: deserialize_QueryUnderstandingServiceRequestForIndications,
    responseSerialize: serialize_QueryUnderstandingServiceResponseForIndications,
    responseDeserialize: deserialize_QueryUnderstandingServiceResponseForIndications,
  },
  getEntities: {
    path: '/QueryUnderstanding/GetEntities',
    requestStream: false,
    responseStream: false,
    requestType: query_understanding_service_pb.QueryUnderstandingServiceRequest,
    responseType: query_understanding_service_pb.QueryUnderstandingServiceResponse,
    requestSerialize: serialize_QueryUnderstandingServiceRequest,
    requestDeserialize: deserialize_QueryUnderstandingServiceRequest,
    responseSerialize: serialize_QueryUnderstandingServiceResponse,
    responseDeserialize: deserialize_QueryUnderstandingServiceResponse,
  },
};

exports.QueryUnderstandingClient = grpc.makeGenericClientConstructor(QueryUnderstandingService);
