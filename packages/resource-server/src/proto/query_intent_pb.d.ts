// package:
// file: query_intent.proto

/* tslint:disable */
/* eslint-disable */

import * as jspb from "google-protobuf";

export class QueryIntent extends jspb.Message { 

    hasInstitutionIntent(): boolean;
    clearInstitutionIntent(): void;
    getInstitutionIntent(): QueryIntent.Intent | undefined;
    setInstitutionIntent(value?: QueryIntent.Intent): QueryIntent;

    hasDiseaseIntent(): boolean;
    clearDiseaseIntent(): void;
    getDiseaseIntent(): QueryIntent.Intent | undefined;
    setDiseaseIntent(value?: QueryIntent.Intent): QueryIntent;

    hasPersonNameIntent(): boolean;
    clearPersonNameIntent(): void;
    getPersonNameIntent(): QueryIntent.Intent | undefined;
    setPersonNameIntent(value?: QueryIntent.Intent): QueryIntent;

    hasSpecialistIntent(): boolean;
    clearSpecialistIntent(): void;
    getSpecialistIntent(): QueryIntent.Intent | undefined;
    setSpecialistIntent(value?: QueryIntent.Intent): QueryIntent;

    hasNpiIntent(): boolean;
    clearNpiIntent(): void;
    getNpiIntent(): QueryIntent.Intent | undefined;
    setNpiIntent(value?: QueryIntent.Intent): QueryIntent;

    hasConferenceIntent(): boolean;
    clearConferenceIntent(): void;
    getConferenceIntent(): QueryIntent.Intent | undefined;
    setConferenceIntent(value?: QueryIntent.Intent): QueryIntent;

    hasLlmIntent(): boolean;
    clearLlmIntent(): void;
    getLlmIntent(): QueryIntent.Intent | undefined;
    setLlmIntent(value?: QueryIntent.Intent): QueryIntent;
    clearEntitiesList(): void;
    getEntitiesList(): Array<QueryIntent.Span>;
    setEntitiesList(value: Array<QueryIntent.Span>): QueryIntent;
    addEntities(value?: QueryIntent.Span, index?: number): QueryIntent.Span;

    serializeBinary(): Uint8Array;
    toObject(includeInstance?: boolean): QueryIntent.AsObject;
    static toObject(includeInstance: boolean, msg: QueryIntent): QueryIntent.AsObject;
    static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
    static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
    static serializeBinaryToWriter(message: QueryIntent, writer: jspb.BinaryWriter): void;
    static deserializeBinary(bytes: Uint8Array): QueryIntent;
    static deserializeBinaryFromReader(message: QueryIntent, reader: jspb.BinaryReader): QueryIntent;
}

export namespace QueryIntent {
    export type AsObject = {
        institutionIntent?: QueryIntent.Intent.AsObject,
        diseaseIntent?: QueryIntent.Intent.AsObject,
        personNameIntent?: QueryIntent.Intent.AsObject,
        specialistIntent?: QueryIntent.Intent.AsObject,
        npiIntent?: QueryIntent.Intent.AsObject,
        conferenceIntent?: QueryIntent.Intent.AsObject,
        llmIntent?: QueryIntent.Intent.AsObject,
        entitiesList: Array<QueryIntent.Span.AsObject>,
    }


    export class Intent extends jspb.Message { 
        getIntentType(): QueryIntent.IntentType;
        setIntentType(value: QueryIntent.IntentType): Intent;
        getScore(): number;
        setScore(value: number): Intent;
        getSynonyms(): string;
        setSynonyms(value: string): Intent;
        clearSynonymListList(): void;
        getSynonymListList(): Array<string>;
        setSynonymListList(value: Array<string>): Intent;
        addSynonymList(value: string, index?: number): string;
        clearIcdCodesListList(): void;
        getIcdCodesListList(): Array<string>;
        setIcdCodesListList(value: Array<string>): Intent;
        addIcdCodesList(value: string, index?: number): string;

        serializeBinary(): Uint8Array;
        toObject(includeInstance?: boolean): Intent.AsObject;
        static toObject(includeInstance: boolean, msg: Intent): Intent.AsObject;
        static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
        static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
        static serializeBinaryToWriter(message: Intent, writer: jspb.BinaryWriter): void;
        static deserializeBinary(bytes: Uint8Array): Intent;
        static deserializeBinaryFromReader(message: Intent, reader: jspb.BinaryReader): Intent;
    }

    export namespace Intent {
        export type AsObject = {
            intentType: QueryIntent.IntentType,
            score: number,
            synonyms: string,
            synonymListList: Array<string>,
            icdCodesListList: Array<string>,
        }
    }

    export class Span extends jspb.Message { 
        getEntity(): string;
        setEntity(value: string): Span;
        clearLabelsList(): void;
        getLabelsList(): Array<QueryIntent.Intent>;
        setLabelsList(value: Array<QueryIntent.Intent>): Span;
        addLabels(value?: QueryIntent.Intent, index?: number): QueryIntent.Intent;

        serializeBinary(): Uint8Array;
        toObject(includeInstance?: boolean): Span.AsObject;
        static toObject(includeInstance: boolean, msg: Span): Span.AsObject;
        static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
        static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
        static serializeBinaryToWriter(message: Span, writer: jspb.BinaryWriter): void;
        static deserializeBinary(bytes: Uint8Array): Span;
        static deserializeBinaryFromReader(message: Span, reader: jspb.BinaryReader): Span;
    }

    export namespace Span {
        export type AsObject = {
            entity: string,
            labelsList: Array<QueryIntent.Intent.AsObject>,
        }
    }


    export enum IntentType {
    INSTITUTION = 0,
    PERSON_NAME = 1,
    DISEASE = 2,
    SPECIALIST = 3,
    NPI_NUMBER = 4,
    CONFERENCE_ORG = 5,
    CONFERENCE_SERIES = 6,
    CONFERENCE_NAME = 7,
    LOCATION = 8,
    INDICATION = 9,
    UNKNOWN = 10,
    LLM_INTENT = 11,
    }

}
