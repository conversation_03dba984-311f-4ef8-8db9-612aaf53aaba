// source: synonym.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

goog.exportSymbol('proto.PhrasalSynonyms', null, global);
goog.exportSymbol('proto.PhrasalSynonyms.TokenizedPhrasePair', null, global);
goog.exportSymbol('proto.Synonym', null, global);
goog.exportSymbol('proto.Synonym.ContextSyn', null, global);
goog.exportSymbol('proto.Synonym.SynScore', null, global);
goog.exportSymbol('proto.Synonyms', null, global);
goog.exportSymbol('proto.TokenToPhrasePairInvertedIndex', null, global);
goog.exportSymbol('proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Synonym = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.Synonym.repeatedFields_, null);
};
goog.inherits(proto.Synonym, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Synonym.displayName = 'proto.Synonym';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Synonym.ContextSyn = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Synonym.ContextSyn, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Synonym.ContextSyn.displayName = 'proto.Synonym.ContextSyn';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Synonym.SynScore = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.Synonym.SynScore, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Synonym.SynScore.displayName = 'proto.Synonym.SynScore';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.Synonyms = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.Synonyms.repeatedFields_, null);
};
goog.inherits(proto.Synonyms, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.Synonyms.displayName = 'proto.Synonyms';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.TokenToPhrasePairInvertedIndex = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.TokenToPhrasePairInvertedIndex.repeatedFields_, null);
};
goog.inherits(proto.TokenToPhrasePairInvertedIndex, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.TokenToPhrasePairInvertedIndex.displayName = 'proto.TokenToPhrasePairInvertedIndex';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.repeatedFields_, null);
};
goog.inherits(proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.displayName = 'proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.PhrasalSynonyms = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.PhrasalSynonyms.repeatedFields_, null);
};
goog.inherits(proto.PhrasalSynonyms, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.PhrasalSynonyms.displayName = 'proto.PhrasalSynonyms';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.PhrasalSynonyms.TokenizedPhrasePair = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.PhrasalSynonyms.TokenizedPhrasePair.repeatedFields_, null);
};
goog.inherits(proto.PhrasalSynonyms.TokenizedPhrasePair, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.PhrasalSynonyms.TokenizedPhrasePair.displayName = 'proto.PhrasalSynonyms.TokenizedPhrasePair';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.Synonym.repeatedFields_ = [2,3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Synonym.prototype.toObject = function(opt_includeInstance) {
  return proto.Synonym.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Synonym} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Synonym.toObject = function(includeInstance, msg) {
  var f, obj = {
    orig: jspb.Message.getFieldWithDefault(msg, 1, ""),
    contextSynList: jspb.Message.toObjectList(msg.getContextSynList(),
    proto.Synonym.ContextSyn.toObject, includeInstance),
    contextFreeSynList: jspb.Message.toObjectList(msg.getContextFreeSynList(),
    proto.Synonym.SynScore.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Synonym}
 */
proto.Synonym.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Synonym;
  return proto.Synonym.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Synonym} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Synonym}
 */
proto.Synonym.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrig(value);
      break;
    case 2:
      var value = new proto.Synonym.ContextSyn;
      reader.readMessage(value,proto.Synonym.ContextSyn.deserializeBinaryFromReader);
      msg.addContextSyn(value);
      break;
    case 3:
      var value = new proto.Synonym.SynScore;
      reader.readMessage(value,proto.Synonym.SynScore.deserializeBinaryFromReader);
      msg.addContextFreeSyn(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Synonym.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Synonym.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Synonym} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Synonym.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOrig();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getContextSynList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.Synonym.ContextSyn.serializeBinaryToWriter
    );
  }
  f = message.getContextFreeSynList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.Synonym.SynScore.serializeBinaryToWriter
    );
  }
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Synonym.ContextSyn.prototype.toObject = function(opt_includeInstance) {
  return proto.Synonym.ContextSyn.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Synonym.ContextSyn} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Synonym.ContextSyn.toObject = function(includeInstance, msg) {
  var f, obj = {
    leftContext: jspb.Message.getFieldWithDefault(msg, 1, ""),
    rightContext: jspb.Message.getFieldWithDefault(msg, 2, ""),
    syn: jspb.Message.getFieldWithDefault(msg, 3, ""),
    score: jspb.Message.getFloatingPointFieldWithDefault(msg, 4, 0.0),
    debug: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Synonym.ContextSyn}
 */
proto.Synonym.ContextSyn.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Synonym.ContextSyn;
  return proto.Synonym.ContextSyn.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Synonym.ContextSyn} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Synonym.ContextSyn}
 */
proto.Synonym.ContextSyn.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setLeftContext(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setRightContext(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSyn(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setScore(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setDebug(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Synonym.ContextSyn.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Synonym.ContextSyn.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Synonym.ContextSyn} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Synonym.ContextSyn.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeFloat(
      4,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional string left_context = 1;
 * @return {string}
 */
proto.Synonym.ContextSyn.prototype.getLeftContext = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Synonym.ContextSyn} returns this
 */
proto.Synonym.ContextSyn.prototype.setLeftContext = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Synonym.ContextSyn} returns this
 */
proto.Synonym.ContextSyn.prototype.clearLeftContext = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Synonym.ContextSyn.prototype.hasLeftContext = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string right_context = 2;
 * @return {string}
 */
proto.Synonym.ContextSyn.prototype.getRightContext = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.Synonym.ContextSyn} returns this
 */
proto.Synonym.ContextSyn.prototype.setRightContext = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Synonym.ContextSyn} returns this
 */
proto.Synonym.ContextSyn.prototype.clearRightContext = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Synonym.ContextSyn.prototype.hasRightContext = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string syn = 3;
 * @return {string}
 */
proto.Synonym.ContextSyn.prototype.getSyn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.Synonym.ContextSyn} returns this
 */
proto.Synonym.ContextSyn.prototype.setSyn = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Synonym.ContextSyn} returns this
 */
proto.Synonym.ContextSyn.prototype.clearSyn = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Synonym.ContextSyn.prototype.hasSyn = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional float score = 4;
 * @return {number}
 */
proto.Synonym.ContextSyn.prototype.getScore = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 4, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.Synonym.ContextSyn} returns this
 */
proto.Synonym.ContextSyn.prototype.setScore = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Synonym.ContextSyn} returns this
 */
proto.Synonym.ContextSyn.prototype.clearScore = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Synonym.ContextSyn.prototype.hasScore = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string debug = 5;
 * @return {string}
 */
proto.Synonym.ContextSyn.prototype.getDebug = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.Synonym.ContextSyn} returns this
 */
proto.Synonym.ContextSyn.prototype.setDebug = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Synonym.ContextSyn} returns this
 */
proto.Synonym.ContextSyn.prototype.clearDebug = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Synonym.ContextSyn.prototype.hasDebug = function() {
  return jspb.Message.getField(this, 5) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Synonym.SynScore.prototype.toObject = function(opt_includeInstance) {
  return proto.Synonym.SynScore.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Synonym.SynScore} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Synonym.SynScore.toObject = function(includeInstance, msg) {
  var f, obj = {
    syn: jspb.Message.getFieldWithDefault(msg, 1, ""),
    score: jspb.Message.getFloatingPointFieldWithDefault(msg, 2, 0.0),
    debug: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Synonym.SynScore}
 */
proto.Synonym.SynScore.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Synonym.SynScore;
  return proto.Synonym.SynScore.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Synonym.SynScore} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Synonym.SynScore}
 */
proto.Synonym.SynScore.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSyn(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setScore(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDebug(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Synonym.SynScore.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Synonym.SynScore.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Synonym.SynScore} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Synonym.SynScore.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeFloat(
      2,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string syn = 1;
 * @return {string}
 */
proto.Synonym.SynScore.prototype.getSyn = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Synonym.SynScore} returns this
 */
proto.Synonym.SynScore.prototype.setSyn = function(value) {
  return jspb.Message.setField(this, 1, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Synonym.SynScore} returns this
 */
proto.Synonym.SynScore.prototype.clearSyn = function() {
  return jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Synonym.SynScore.prototype.hasSyn = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional float score = 2;
 * @return {number}
 */
proto.Synonym.SynScore.prototype.getScore = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 2, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.Synonym.SynScore} returns this
 */
proto.Synonym.SynScore.prototype.setScore = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Synonym.SynScore} returns this
 */
proto.Synonym.SynScore.prototype.clearScore = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Synonym.SynScore.prototype.hasScore = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional string debug = 3;
 * @return {string}
 */
proto.Synonym.SynScore.prototype.getDebug = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.Synonym.SynScore} returns this
 */
proto.Synonym.SynScore.prototype.setDebug = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.Synonym.SynScore} returns this
 */
proto.Synonym.SynScore.prototype.clearDebug = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.Synonym.SynScore.prototype.hasDebug = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string orig = 1;
 * @return {string}
 */
proto.Synonym.prototype.getOrig = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.Synonym} returns this
 */
proto.Synonym.prototype.setOrig = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated ContextSyn context_syn = 2;
 * @return {!Array<!proto.Synonym.ContextSyn>}
 */
proto.Synonym.prototype.getContextSynList = function() {
  return /** @type{!Array<!proto.Synonym.ContextSyn>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.Synonym.ContextSyn, 2));
};


/**
 * @param {!Array<!proto.Synonym.ContextSyn>} value
 * @return {!proto.Synonym} returns this
*/
proto.Synonym.prototype.setContextSynList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.Synonym.ContextSyn=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Synonym.ContextSyn}
 */
proto.Synonym.prototype.addContextSyn = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.Synonym.ContextSyn, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.Synonym} returns this
 */
proto.Synonym.prototype.clearContextSynList = function() {
  return this.setContextSynList([]);
};


/**
 * repeated SynScore context_free_syn = 3;
 * @return {!Array<!proto.Synonym.SynScore>}
 */
proto.Synonym.prototype.getContextFreeSynList = function() {
  return /** @type{!Array<!proto.Synonym.SynScore>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.Synonym.SynScore, 3));
};


/**
 * @param {!Array<!proto.Synonym.SynScore>} value
 * @return {!proto.Synonym} returns this
*/
proto.Synonym.prototype.setContextFreeSynList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.Synonym.SynScore=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Synonym.SynScore}
 */
proto.Synonym.prototype.addContextFreeSyn = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.Synonym.SynScore, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.Synonym} returns this
 */
proto.Synonym.prototype.clearContextFreeSynList = function() {
  return this.setContextFreeSynList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.Synonyms.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.Synonyms.prototype.toObject = function(opt_includeInstance) {
  return proto.Synonyms.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.Synonyms} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Synonyms.toObject = function(includeInstance, msg) {
  var f, obj = {
    synonymInfoList: jspb.Message.toObjectList(msg.getSynonymInfoList(),
    proto.Synonym.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.Synonyms}
 */
proto.Synonyms.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.Synonyms;
  return proto.Synonyms.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.Synonyms} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.Synonyms}
 */
proto.Synonyms.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.Synonym;
      reader.readMessage(value,proto.Synonym.deserializeBinaryFromReader);
      msg.addSynonymInfo(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.Synonyms.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.Synonyms.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.Synonyms} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.Synonyms.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSynonymInfoList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.Synonym.serializeBinaryToWriter
    );
  }
};


/**
 * repeated Synonym synonym_info = 1;
 * @return {!Array<!proto.Synonym>}
 */
proto.Synonyms.prototype.getSynonymInfoList = function() {
  return /** @type{!Array<!proto.Synonym>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.Synonym, 1));
};


/**
 * @param {!Array<!proto.Synonym>} value
 * @return {!proto.Synonyms} returns this
*/
proto.Synonyms.prototype.setSynonymInfoList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.Synonym=} opt_value
 * @param {number=} opt_index
 * @return {!proto.Synonym}
 */
proto.Synonyms.prototype.addSynonymInfo = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.Synonym, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.Synonyms} returns this
 */
proto.Synonyms.prototype.clearSynonymInfoList = function() {
  return this.setSynonymInfoList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.TokenToPhrasePairInvertedIndex.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.TokenToPhrasePairInvertedIndex.prototype.toObject = function(opt_includeInstance) {
  return proto.TokenToPhrasePairInvertedIndex.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.TokenToPhrasePairInvertedIndex} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.TokenToPhrasePairInvertedIndex.toObject = function(includeInstance, msg) {
  var f, obj = {
    indexListList: jspb.Message.toObjectList(msg.getIndexListList(),
    proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.TokenToPhrasePairInvertedIndex}
 */
proto.TokenToPhrasePairInvertedIndex.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.TokenToPhrasePairInvertedIndex;
  return proto.TokenToPhrasePairInvertedIndex.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.TokenToPhrasePairInvertedIndex} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.TokenToPhrasePairInvertedIndex}
 */
proto.TokenToPhrasePairInvertedIndex.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex;
      reader.readMessage(value,proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.deserializeBinaryFromReader);
      msg.addIndexList(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.TokenToPhrasePairInvertedIndex.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.TokenToPhrasePairInvertedIndex.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.TokenToPhrasePairInvertedIndex} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.TokenToPhrasePairInvertedIndex.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIndexListList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.serializeBinaryToWriter
    );
  }
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.prototype.toObject = function(opt_includeInstance) {
  return proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.toObject = function(includeInstance, msg) {
  var f, obj = {
    token: jspb.Message.getFieldWithDefault(msg, 1, ""),
    indexList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex}
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex;
  return proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex}
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setToken(value);
      break;
    case 2:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedInt64() : [reader.readInt64()]);
      for (var i = 0; i < values.length; i++) {
        msg.addIndex(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getToken();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIndexList();
  if (f.length > 0) {
    writer.writePackedInt64(
      2,
      f
    );
  }
};


/**
 * optional string token = 1;
 * @return {string}
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.prototype.getToken = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex} returns this
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.prototype.setToken = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated int64 index = 2;
 * @return {!Array<number>}
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.prototype.getIndexList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex} returns this
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.prototype.setIndexList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex} returns this
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.prototype.addIndex = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex} returns this
 */
proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex.prototype.clearIndexList = function() {
  return this.setIndexList([]);
};


/**
 * repeated TokenToPhrasePairIndex index_list = 1;
 * @return {!Array<!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex>}
 */
proto.TokenToPhrasePairInvertedIndex.prototype.getIndexListList = function() {
  return /** @type{!Array<!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex, 1));
};


/**
 * @param {!Array<!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex>} value
 * @return {!proto.TokenToPhrasePairInvertedIndex} returns this
*/
proto.TokenToPhrasePairInvertedIndex.prototype.setIndexListList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex=} opt_value
 * @param {number=} opt_index
 * @return {!proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex}
 */
proto.TokenToPhrasePairInvertedIndex.prototype.addIndexList = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.TokenToPhrasePairInvertedIndex.TokenToPhrasePairIndex, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.TokenToPhrasePairInvertedIndex} returns this
 */
proto.TokenToPhrasePairInvertedIndex.prototype.clearIndexListList = function() {
  return this.setIndexListList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.PhrasalSynonyms.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.PhrasalSynonyms.prototype.toObject = function(opt_includeInstance) {
  return proto.PhrasalSynonyms.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.PhrasalSynonyms} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.PhrasalSynonyms.toObject = function(includeInstance, msg) {
  var f, obj = {
    phrasePairListList: jspb.Message.toObjectList(msg.getPhrasePairListList(),
    proto.PhrasalSynonyms.TokenizedPhrasePair.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.PhrasalSynonyms}
 */
proto.PhrasalSynonyms.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.PhrasalSynonyms;
  return proto.PhrasalSynonyms.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.PhrasalSynonyms} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.PhrasalSynonyms}
 */
proto.PhrasalSynonyms.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.PhrasalSynonyms.TokenizedPhrasePair;
      reader.readMessage(value,proto.PhrasalSynonyms.TokenizedPhrasePair.deserializeBinaryFromReader);
      msg.addPhrasePairList(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.PhrasalSynonyms.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.PhrasalSynonyms.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.PhrasalSynonyms} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.PhrasalSynonyms.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPhrasePairListList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.PhrasalSynonyms.TokenizedPhrasePair.serializeBinaryToWriter
    );
  }
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.repeatedFields_ = [1,2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.prototype.toObject = function(opt_includeInstance) {
  return proto.PhrasalSynonyms.TokenizedPhrasePair.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.PhrasalSynonyms.TokenizedPhrasePair} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.toObject = function(includeInstance, msg) {
  var f, obj = {
    originalWordTokensList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f,
    synonymWordTokensList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.PhrasalSynonyms.TokenizedPhrasePair}
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.PhrasalSynonyms.TokenizedPhrasePair;
  return proto.PhrasalSynonyms.TokenizedPhrasePair.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.PhrasalSynonyms.TokenizedPhrasePair} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.PhrasalSynonyms.TokenizedPhrasePair}
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addOriginalWordTokens(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.addSynonymWordTokens(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.PhrasalSynonyms.TokenizedPhrasePair.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.PhrasalSynonyms.TokenizedPhrasePair} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOriginalWordTokensList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
  f = message.getSynonymWordTokensList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      2,
      f
    );
  }
};


/**
 * repeated string original_word_tokens = 1;
 * @return {!Array<string>}
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.prototype.getOriginalWordTokensList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.PhrasalSynonyms.TokenizedPhrasePair} returns this
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.prototype.setOriginalWordTokensList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.PhrasalSynonyms.TokenizedPhrasePair} returns this
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.prototype.addOriginalWordTokens = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.PhrasalSynonyms.TokenizedPhrasePair} returns this
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.prototype.clearOriginalWordTokensList = function() {
  return this.setOriginalWordTokensList([]);
};


/**
 * repeated string synonym_word_tokens = 2;
 * @return {!Array<string>}
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.prototype.getSynonymWordTokensList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.PhrasalSynonyms.TokenizedPhrasePair} returns this
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.prototype.setSynonymWordTokensList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.PhrasalSynonyms.TokenizedPhrasePair} returns this
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.prototype.addSynonymWordTokens = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.PhrasalSynonyms.TokenizedPhrasePair} returns this
 */
proto.PhrasalSynonyms.TokenizedPhrasePair.prototype.clearSynonymWordTokensList = function() {
  return this.setSynonymWordTokensList([]);
};


/**
 * repeated TokenizedPhrasePair phrase_pair_list = 1;
 * @return {!Array<!proto.PhrasalSynonyms.TokenizedPhrasePair>}
 */
proto.PhrasalSynonyms.prototype.getPhrasePairListList = function() {
  return /** @type{!Array<!proto.PhrasalSynonyms.TokenizedPhrasePair>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.PhrasalSynonyms.TokenizedPhrasePair, 1));
};


/**
 * @param {!Array<!proto.PhrasalSynonyms.TokenizedPhrasePair>} value
 * @return {!proto.PhrasalSynonyms} returns this
*/
proto.PhrasalSynonyms.prototype.setPhrasePairListList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.PhrasalSynonyms.TokenizedPhrasePair=} opt_value
 * @param {number=} opt_index
 * @return {!proto.PhrasalSynonyms.TokenizedPhrasePair}
 */
proto.PhrasalSynonyms.prototype.addPhrasePairList = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.PhrasalSynonyms.TokenizedPhrasePair, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.PhrasalSynonyms} returns this
 */
proto.PhrasalSynonyms.prototype.clearPhrasePairListList = function() {
  return this.setPhrasePairListList([]);
};


goog.object.extend(exports, proto);
