// source: query_intent.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

goog.exportSymbol('proto.QueryIntent', null, global);
goog.exportSymbol('proto.QueryIntent.Intent', null, global);
goog.exportSymbol('proto.QueryIntent.IntentType', null, global);
goog.exportSymbol('proto.QueryIntent.Span', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryIntent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.QueryIntent.repeatedFields_, null);
};
goog.inherits(proto.QueryIntent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryIntent.displayName = 'proto.QueryIntent';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryIntent.Intent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.QueryIntent.Intent.repeatedFields_, null);
};
goog.inherits(proto.QueryIntent.Intent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryIntent.Intent.displayName = 'proto.QueryIntent.Intent';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.QueryIntent.Span = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.QueryIntent.Span.repeatedFields_, null);
};
goog.inherits(proto.QueryIntent.Span, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.QueryIntent.Span.displayName = 'proto.QueryIntent.Span';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.QueryIntent.repeatedFields_ = [8];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryIntent.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryIntent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryIntent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryIntent.toObject = function(includeInstance, msg) {
  var f, obj = {
    institutionIntent: (f = msg.getInstitutionIntent()) && proto.QueryIntent.Intent.toObject(includeInstance, f),
    diseaseIntent: (f = msg.getDiseaseIntent()) && proto.QueryIntent.Intent.toObject(includeInstance, f),
    personNameIntent: (f = msg.getPersonNameIntent()) && proto.QueryIntent.Intent.toObject(includeInstance, f),
    specialistIntent: (f = msg.getSpecialistIntent()) && proto.QueryIntent.Intent.toObject(includeInstance, f),
    npiIntent: (f = msg.getNpiIntent()) && proto.QueryIntent.Intent.toObject(includeInstance, f),
    conferenceIntent: (f = msg.getConferenceIntent()) && proto.QueryIntent.Intent.toObject(includeInstance, f),
    llmIntent: (f = msg.getLlmIntent()) && proto.QueryIntent.Intent.toObject(includeInstance, f),
    entitiesList: jspb.Message.toObjectList(msg.getEntitiesList(),
    proto.QueryIntent.Span.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryIntent}
 */
proto.QueryIntent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryIntent;
  return proto.QueryIntent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryIntent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryIntent}
 */
proto.QueryIntent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.QueryIntent.Intent;
      reader.readMessage(value,proto.QueryIntent.Intent.deserializeBinaryFromReader);
      msg.setInstitutionIntent(value);
      break;
    case 2:
      var value = new proto.QueryIntent.Intent;
      reader.readMessage(value,proto.QueryIntent.Intent.deserializeBinaryFromReader);
      msg.setDiseaseIntent(value);
      break;
    case 3:
      var value = new proto.QueryIntent.Intent;
      reader.readMessage(value,proto.QueryIntent.Intent.deserializeBinaryFromReader);
      msg.setPersonNameIntent(value);
      break;
    case 4:
      var value = new proto.QueryIntent.Intent;
      reader.readMessage(value,proto.QueryIntent.Intent.deserializeBinaryFromReader);
      msg.setSpecialistIntent(value);
      break;
    case 5:
      var value = new proto.QueryIntent.Intent;
      reader.readMessage(value,proto.QueryIntent.Intent.deserializeBinaryFromReader);
      msg.setNpiIntent(value);
      break;
    case 6:
      var value = new proto.QueryIntent.Intent;
      reader.readMessage(value,proto.QueryIntent.Intent.deserializeBinaryFromReader);
      msg.setConferenceIntent(value);
      break;
    case 7:
      var value = new proto.QueryIntent.Intent;
      reader.readMessage(value,proto.QueryIntent.Intent.deserializeBinaryFromReader);
      msg.setLlmIntent(value);
      break;
    case 8:
      var value = new proto.QueryIntent.Span;
      reader.readMessage(value,proto.QueryIntent.Span.deserializeBinaryFromReader);
      msg.addEntities(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryIntent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryIntent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryIntent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryIntent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getInstitutionIntent();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.QueryIntent.Intent.serializeBinaryToWriter
    );
  }
  f = message.getDiseaseIntent();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.QueryIntent.Intent.serializeBinaryToWriter
    );
  }
  f = message.getPersonNameIntent();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.QueryIntent.Intent.serializeBinaryToWriter
    );
  }
  f = message.getSpecialistIntent();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.QueryIntent.Intent.serializeBinaryToWriter
    );
  }
  f = message.getNpiIntent();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      proto.QueryIntent.Intent.serializeBinaryToWriter
    );
  }
  f = message.getConferenceIntent();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      proto.QueryIntent.Intent.serializeBinaryToWriter
    );
  }
  f = message.getLlmIntent();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      proto.QueryIntent.Intent.serializeBinaryToWriter
    );
  }
  f = message.getEntitiesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      8,
      f,
      proto.QueryIntent.Span.serializeBinaryToWriter
    );
  }
};


/**
 * @enum {number}
 */
proto.QueryIntent.IntentType = {
  INSTITUTION: 0,
  PERSON_NAME: 1,
  DISEASE: 2,
  SPECIALIST: 3,
  NPI_NUMBER: 4,
  CONFERENCE_ORG: 5,
  CONFERENCE_SERIES: 6,
  CONFERENCE_NAME: 7,
  LOCATION: 8,
  INDICATION: 9,
  UNKNOWN: 10,
  LLM_INTENT: 11
};


/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.QueryIntent.Intent.repeatedFields_ = [4,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryIntent.Intent.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryIntent.Intent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryIntent.Intent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryIntent.Intent.toObject = function(includeInstance, msg) {
  var f, obj = {
    intentType: jspb.Message.getFieldWithDefault(msg, 1, 0),
    score: jspb.Message.getFloatingPointFieldWithDefault(msg, 2, 0.0),
    synonyms: jspb.Message.getFieldWithDefault(msg, 3, ""),
    synonymListList: (f = jspb.Message.getRepeatedField(msg, 4)) == null ? undefined : f,
    icdCodesListList: (f = jspb.Message.getRepeatedField(msg, 5)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryIntent.Intent}
 */
proto.QueryIntent.Intent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryIntent.Intent;
  return proto.QueryIntent.Intent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryIntent.Intent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryIntent.Intent}
 */
proto.QueryIntent.Intent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.QueryIntent.IntentType} */ (reader.readEnum());
      msg.setIntentType(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setScore(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSynonyms(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.addSynonymList(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.addIcdCodesList(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryIntent.Intent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryIntent.Intent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryIntent.Intent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryIntent.Intent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIntentType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getScore();
  if (f !== 0.0) {
    writer.writeFloat(
      2,
      f
    );
  }
  f = message.getSynonyms();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getSynonymListList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      4,
      f
    );
  }
  f = message.getIcdCodesListList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      5,
      f
    );
  }
};


/**
 * optional IntentType intent_type = 1;
 * @return {!proto.QueryIntent.IntentType}
 */
proto.QueryIntent.Intent.prototype.getIntentType = function() {
  return /** @type {!proto.QueryIntent.IntentType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.QueryIntent.IntentType} value
 * @return {!proto.QueryIntent.Intent} returns this
 */
proto.QueryIntent.Intent.prototype.setIntentType = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional float score = 2;
 * @return {number}
 */
proto.QueryIntent.Intent.prototype.getScore = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 2, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.QueryIntent.Intent} returns this
 */
proto.QueryIntent.Intent.prototype.setScore = function(value) {
  return jspb.Message.setProto3FloatField(this, 2, value);
};


/**
 * optional string synonyms = 3;
 * @return {string}
 */
proto.QueryIntent.Intent.prototype.getSynonyms = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryIntent.Intent} returns this
 */
proto.QueryIntent.Intent.prototype.setSynonyms = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * repeated string synonym_list = 4;
 * @return {!Array<string>}
 */
proto.QueryIntent.Intent.prototype.getSynonymListList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 4));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.QueryIntent.Intent} returns this
 */
proto.QueryIntent.Intent.prototype.setSynonymListList = function(value) {
  return jspb.Message.setField(this, 4, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.QueryIntent.Intent} returns this
 */
proto.QueryIntent.Intent.prototype.addSynonymList = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 4, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryIntent.Intent} returns this
 */
proto.QueryIntent.Intent.prototype.clearSynonymListList = function() {
  return this.setSynonymListList([]);
};


/**
 * repeated string icd_codes_list = 5;
 * @return {!Array<string>}
 */
proto.QueryIntent.Intent.prototype.getIcdCodesListList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 5));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.QueryIntent.Intent} returns this
 */
proto.QueryIntent.Intent.prototype.setIcdCodesListList = function(value) {
  return jspb.Message.setField(this, 5, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.QueryIntent.Intent} returns this
 */
proto.QueryIntent.Intent.prototype.addIcdCodesList = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 5, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryIntent.Intent} returns this
 */
proto.QueryIntent.Intent.prototype.clearIcdCodesListList = function() {
  return this.setIcdCodesListList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.QueryIntent.Span.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.QueryIntent.Span.prototype.toObject = function(opt_includeInstance) {
  return proto.QueryIntent.Span.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.QueryIntent.Span} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryIntent.Span.toObject = function(includeInstance, msg) {
  var f, obj = {
    entity: jspb.Message.getFieldWithDefault(msg, 1, ""),
    labelsList: jspb.Message.toObjectList(msg.getLabelsList(),
    proto.QueryIntent.Intent.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.QueryIntent.Span}
 */
proto.QueryIntent.Span.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.QueryIntent.Span;
  return proto.QueryIntent.Span.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.QueryIntent.Span} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.QueryIntent.Span}
 */
proto.QueryIntent.Span.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setEntity(value);
      break;
    case 2:
      var value = new proto.QueryIntent.Intent;
      reader.readMessage(value,proto.QueryIntent.Intent.deserializeBinaryFromReader);
      msg.addLabels(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.QueryIntent.Span.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.QueryIntent.Span.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.QueryIntent.Span} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.QueryIntent.Span.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEntity();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLabelsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.QueryIntent.Intent.serializeBinaryToWriter
    );
  }
};


/**
 * optional string entity = 1;
 * @return {string}
 */
proto.QueryIntent.Span.prototype.getEntity = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.QueryIntent.Span} returns this
 */
proto.QueryIntent.Span.prototype.setEntity = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated Intent labels = 2;
 * @return {!Array<!proto.QueryIntent.Intent>}
 */
proto.QueryIntent.Span.prototype.getLabelsList = function() {
  return /** @type{!Array<!proto.QueryIntent.Intent>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.QueryIntent.Intent, 2));
};


/**
 * @param {!Array<!proto.QueryIntent.Intent>} value
 * @return {!proto.QueryIntent.Span} returns this
*/
proto.QueryIntent.Span.prototype.setLabelsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.QueryIntent.Intent=} opt_value
 * @param {number=} opt_index
 * @return {!proto.QueryIntent.Intent}
 */
proto.QueryIntent.Span.prototype.addLabels = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.QueryIntent.Intent, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryIntent.Span} returns this
 */
proto.QueryIntent.Span.prototype.clearLabelsList = function() {
  return this.setLabelsList([]);
};


/**
 * optional Intent institution_intent = 1;
 * @return {?proto.QueryIntent.Intent}
 */
proto.QueryIntent.prototype.getInstitutionIntent = function() {
  return /** @type{?proto.QueryIntent.Intent} */ (
    jspb.Message.getWrapperField(this, proto.QueryIntent.Intent, 1));
};


/**
 * @param {?proto.QueryIntent.Intent|undefined} value
 * @return {!proto.QueryIntent} returns this
*/
proto.QueryIntent.prototype.setInstitutionIntent = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryIntent} returns this
 */
proto.QueryIntent.prototype.clearInstitutionIntent = function() {
  return this.setInstitutionIntent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryIntent.prototype.hasInstitutionIntent = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional Intent disease_intent = 2;
 * @return {?proto.QueryIntent.Intent}
 */
proto.QueryIntent.prototype.getDiseaseIntent = function() {
  return /** @type{?proto.QueryIntent.Intent} */ (
    jspb.Message.getWrapperField(this, proto.QueryIntent.Intent, 2));
};


/**
 * @param {?proto.QueryIntent.Intent|undefined} value
 * @return {!proto.QueryIntent} returns this
*/
proto.QueryIntent.prototype.setDiseaseIntent = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryIntent} returns this
 */
proto.QueryIntent.prototype.clearDiseaseIntent = function() {
  return this.setDiseaseIntent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryIntent.prototype.hasDiseaseIntent = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional Intent person_name_intent = 3;
 * @return {?proto.QueryIntent.Intent}
 */
proto.QueryIntent.prototype.getPersonNameIntent = function() {
  return /** @type{?proto.QueryIntent.Intent} */ (
    jspb.Message.getWrapperField(this, proto.QueryIntent.Intent, 3));
};


/**
 * @param {?proto.QueryIntent.Intent|undefined} value
 * @return {!proto.QueryIntent} returns this
*/
proto.QueryIntent.prototype.setPersonNameIntent = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryIntent} returns this
 */
proto.QueryIntent.prototype.clearPersonNameIntent = function() {
  return this.setPersonNameIntent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryIntent.prototype.hasPersonNameIntent = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional Intent specialist_intent = 4;
 * @return {?proto.QueryIntent.Intent}
 */
proto.QueryIntent.prototype.getSpecialistIntent = function() {
  return /** @type{?proto.QueryIntent.Intent} */ (
    jspb.Message.getWrapperField(this, proto.QueryIntent.Intent, 4));
};


/**
 * @param {?proto.QueryIntent.Intent|undefined} value
 * @return {!proto.QueryIntent} returns this
*/
proto.QueryIntent.prototype.setSpecialistIntent = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryIntent} returns this
 */
proto.QueryIntent.prototype.clearSpecialistIntent = function() {
  return this.setSpecialistIntent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryIntent.prototype.hasSpecialistIntent = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional Intent npi_intent = 5;
 * @return {?proto.QueryIntent.Intent}
 */
proto.QueryIntent.prototype.getNpiIntent = function() {
  return /** @type{?proto.QueryIntent.Intent} */ (
    jspb.Message.getWrapperField(this, proto.QueryIntent.Intent, 5));
};


/**
 * @param {?proto.QueryIntent.Intent|undefined} value
 * @return {!proto.QueryIntent} returns this
*/
proto.QueryIntent.prototype.setNpiIntent = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryIntent} returns this
 */
proto.QueryIntent.prototype.clearNpiIntent = function() {
  return this.setNpiIntent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryIntent.prototype.hasNpiIntent = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional Intent conference_intent = 6;
 * @return {?proto.QueryIntent.Intent}
 */
proto.QueryIntent.prototype.getConferenceIntent = function() {
  return /** @type{?proto.QueryIntent.Intent} */ (
    jspb.Message.getWrapperField(this, proto.QueryIntent.Intent, 6));
};


/**
 * @param {?proto.QueryIntent.Intent|undefined} value
 * @return {!proto.QueryIntent} returns this
*/
proto.QueryIntent.prototype.setConferenceIntent = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryIntent} returns this
 */
proto.QueryIntent.prototype.clearConferenceIntent = function() {
  return this.setConferenceIntent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryIntent.prototype.hasConferenceIntent = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional Intent llm_intent = 7;
 * @return {?proto.QueryIntent.Intent}
 */
proto.QueryIntent.prototype.getLlmIntent = function() {
  return /** @type{?proto.QueryIntent.Intent} */ (
    jspb.Message.getWrapperField(this, proto.QueryIntent.Intent, 7));
};


/**
 * @param {?proto.QueryIntent.Intent|undefined} value
 * @return {!proto.QueryIntent} returns this
*/
proto.QueryIntent.prototype.setLlmIntent = function(value) {
  return jspb.Message.setWrapperField(this, 7, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.QueryIntent} returns this
 */
proto.QueryIntent.prototype.clearLlmIntent = function() {
  return this.setLlmIntent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.QueryIntent.prototype.hasLlmIntent = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * repeated Span entities = 8;
 * @return {!Array<!proto.QueryIntent.Span>}
 */
proto.QueryIntent.prototype.getEntitiesList = function() {
  return /** @type{!Array<!proto.QueryIntent.Span>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.QueryIntent.Span, 8));
};


/**
 * @param {!Array<!proto.QueryIntent.Span>} value
 * @return {!proto.QueryIntent} returns this
*/
proto.QueryIntent.prototype.setEntitiesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 8, value);
};


/**
 * @param {!proto.QueryIntent.Span=} opt_value
 * @param {number=} opt_index
 * @return {!proto.QueryIntent.Span}
 */
proto.QueryIntent.prototype.addEntities = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 8, opt_value, proto.QueryIntent.Span, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.QueryIntent} returns this
 */
proto.QueryIntent.prototype.clearEntitiesList = function() {
  return this.setEntitiesList([]);
};


goog.object.extend(exports, proto);
