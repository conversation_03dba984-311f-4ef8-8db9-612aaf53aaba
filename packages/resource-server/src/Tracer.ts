import "reflect-metadata";
import { getTracer } from "@h1nyc/systems-apm";
export { Span } from "dd-trace";

const tracer = getTracer({
  service: "h1-search/resource-server",
  disabledPlugins: ["fs"],
  logInjection: true,
  analytics: true
});

export default tracer;

type SpanArgMap = Record<string, number>;

const SPAN_ARG_METADATA = "tracer:span-arg";

/**
 *
 * @returns
 */
export function SpanArg(): ParameterDecorator {
  return (target, key, index) => {
    const spanArgMap: SpanArgMap =
      Reflect.getMetadata(SPAN_ARG_METADATA, target) || {};

    spanArgMap[key.toString()] = index;

    Reflect.defineMetadata(SPAN_ARG_METADATA, spanArgMap, target);
  };
}

/**
 * Decorate a method with a tracer.
 *
 * @param traceName The name of the trace
 */
export function Trace(traceName: string): MethodDecorator {
  return (target, propertyKey, descriptor: PropertyDescriptor) => {
    const method = descriptor.value;
    const spanArgMap = Reflect.getMetadata(SPAN_ARG_METADATA, target) || {};
    const spanArgIndex = spanArgMap[propertyKey];

    descriptor.value = function (...args: any[]) {
      let newArgs: any[] = [];

      if (spanArgIndex !== undefined) {
        const maxLength = Math.max(method.length, spanArgIndex + 1);

        for (let i = 0; i < maxLength; i++) {
          const val = i < args.length ? args[i] : undefined;
          newArgs.push(val);
        }
      } else {
        newArgs = args;
      }

      return tracer.trace(traceName, (span) => {
        if (spanArgIndex !== undefined) {
          newArgs.splice(spanArgIndex, 0, span);
        }

        const inputArg = newArgs.find((arg) => arg?.userId);
        if (inputArg?.userId) {
          span?.setTag("user.id", inputArg.userId);
        }

        return method.apply(this, newArgs);
      });
    };

    return descriptor;
  };
}
