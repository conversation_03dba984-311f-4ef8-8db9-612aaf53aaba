const mockSpan = {
  tracer: {},
  setTag: jest.fn(),
  finish: jest.fn()
};

jest.mock("@h1nyc/systems-apm", () => ({
  getTracer() {
    return {
      trace(traceName: string, fn: (span: any) => any) {
        expect(traceName).toBeDefined();
        return fn(mockSpan);
      }
    };
  }
}));

jest.mock("./Tracer", () => jest.requireActual("./Tracer"));

import { faker } from "@faker-js/faker";
import { Trace, Span, SpanArg } from "./Tracer";

class TestClass {
  @Trace("my-test-trace")
  testSpanFirst(
    @SpanArg() span: Span,
    test1: string,
    test2: string,
    test3?: string,
    test4?: string
  ) {
    expect(span).toHaveProperty("tracer");
    expect(test1).toBeTruthy();
    expect(test2).toBeTruthy();
    expect(test3).toBeTruthy();
    expect(test4).toBeUndefined();
  }

  @Trace("my-test-trace")
  testSpanLast(
    test1: string,
    test2: string,
    test3?: string,
    test4?: string,
    @SpanArg() span?: Span
  ) {
    expect(test1).toBeTruthy();
    expect(test2).toBeTruthy();
    expect(test3).toBeTruthy();
    expect(test4).toBeUndefined();
    expect(span).toHaveProperty("tracer");
  }

  @Trace("my-test-trace")
  testSpanMiddle(
    test1: string,
    test2: string,
    @SpanArg() span: Span,
    test3?: string,
    test4?: string
  ) {
    expect(test1).toBeTruthy();
    expect(test2).toBeTruthy();
    expect(span).toHaveProperty("tracer");
    expect(test3).toBeTruthy();
    expect(test4).toBeUndefined();
  }

  @Trace("my-test-trace")
  testNoSpan(test1: string, test2: string, test3?: string, test4?: string) {
    expect(test1).toBeTruthy();
    expect(test2).toBeTruthy();
    expect(test3).toBeTruthy();
    expect(test4).toBeUndefined();
  }

  @Trace("my-test-trace")
  testSpanUserIdTag(input: { userId: string }) {
    expect(input).toHaveProperty("userId");
    expect(mockSpan.setTag).toHaveBeenCalledWith("user.id", input.userId);
  }
}

describe("test tracer passing span as last arg", () => {
  const testC = new TestClass();
  const callMethod = (name: string, ...args: any[]) => {
    // @ts-ignore
    testC[name](...args);
  };

  it("should inject span as first argument", () => {
    callMethod("testSpanFirst", "a", "b", "c");
  });

  it("should inject span as last argument", () => {
    callMethod("testSpanLast", "a", "b", "c");
  });

  it("should inject span in middle of argument list", () => {
    callMethod("testSpanMiddle", "a", "b", "c");
  });

  it("should not modify arguments list if no span decorator", () => {
    callMethod("testNoSpan", "a", "b", "c");
  });

  it("should set span tag for user id", () => {
    callMethod("testSpanUserIdTag", { userId: faker.datatype.uuid() });
  });
});
