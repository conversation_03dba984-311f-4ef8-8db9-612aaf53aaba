import {
  QueryDslQueryContainer,
  AggregationsAggregationContainer,
  AggregationsScriptedMetricAggregation,
  AggregationsAggregateOrder
} from "@elastic/elasticsearch/lib/api/types";
import {
  KeywordFilterAutocompleteInput,
  KeywordFilterAutocompleteFilterField,
  Apps
} from "@h1nyc/search-sdk";
import _ from "lodash";
import { Service } from "typedi";
import {
  AutocompleteFeatureFlags,
  AGGREGATIONS_THAT_NEED_NESTED_FILTERING
} from "./KeywordAutocompleteResourceService";
import {
  ClaimType,
  LanguageDetector,
  ParsedQueryTree
} from "./KeywordSearchResourceServiceRewrite";
import { ENGLISH, Language, CHINESE, JAPANESE } from "./LanguageDetectService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import {
  findNestedClaimsFilters,
  findNestedFilters
} from "../util/QueryParsingUtils";
import { FilterInterface } from "@h1nyc/search-sdk";
import { buildTermQuery } from "../util/QueryBuildingUtils";
import { KeywordFilterClauseBuilderService } from "./KeywordFilterClauseBuilderService";

type ClaimsPath = "DRG_diagnoses" | "DRG_procedures";
type AssetPath =
  | "payments"
  | "congress"
  | "trials"
  | "publications"
  | "affiliations"
  | "prescriptions"
  | ClaimsPath
  | "ccsr"
  | "ccsr_px";
type MinFilterField =
  | "payments.amount"
  | "publications.microBloggingCount"
  | "DRG_diagnoses.internalCount"
  | "DRG_procedures.internalCount"
  | "DRG_diagnoses.internalUniqueCount"
  | "DRG_procedures.internalUniqueCount"
  | string;

type QueryContainer = QueryDslQueryContainer[] | undefined;

interface MinValues {
  minAmount?: number;
  minCount?: number;
}

interface MaxValues {
  maxCount?: number;
  maxAmount?: number;
}

const USE_ENGLISH_FIELDS: LanguageDetector = () => ENGLISH;
const ZERO_LENGTH_FILTER_VALUE = "";
const REGEXP_SPECIAL_CHARACTERS = new Set([
  ".",
  "+",
  "*",
  "?",
  "^",
  "$",
  "(",
  ")",
  "[",
  "]",
  "{",
  "}",
  "|",
  "&",
  "\\"
]);
const REGEX_ONE_OR_TWO_CHARACTERS = ".{1,2}";

const ONLY_INCLUDE_US_ADDRESSES = {
  include: "us\\|.*"
};

const MIN_CONDITION_ONLY_COUNT = "total >= params.minCount";
const MAX_CONDITION_ONLY_COUNT = "total <= params.maxCount";
const MIN_CONDITION_ONLY_AMOUNT = "total >= params.minAmount";
const MAX_CONDITION_ONLY_AMOUNT = "total <= params.maxAmount";
const MIN_CONDITION_BOTH_COUNT = "totals[0] >= params.minCount";
const MAX_CONDITION_BOTH_COUNT = "totals[0] <= params.maxCount";
const MIN_CONDITION_BOTH_AMOUNT = "totals[1] >= params.minAmount";
const MAX_CONDITION_BOTH_AMOUNT = "totals[1] <= params.maxAmount";

const NESTED_AGGREGATIONS_AVAILABLE_IN_CHINESE = new Map<
  KeywordFilterAutocompleteFilterField,
  { path: AssetPath; field: string }
>([
  [
    KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
    {
      path: "publications",
      field: "publications.journalName_cmn"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
    {
      path: "publications",
      field: "publications.type_cmn"
    }
  ]
]);

const NESTED_AGGREGATIONS_AVAILABLE_IN_JAPANESE = new Map<
  KeywordFilterAutocompleteFilterField,
  { path: AssetPath; field: string }
>([
  [
    KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
    {
      path: "publications",
      field: "publications.journalName_jpn"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
    {
      path: "publications",
      field: "publications.type_jpn"
    }
  ]
]);

const AGGREGATIONS_THAT_CANT_BE_PREFIX_PHRASE_MATCHED: {
  [index: number]: string;
} = {
  [KeywordFilterAutocompleteFilterField.DESIGNATION]: "designations",
  [KeywordFilterAutocompleteFilterField.SPECIALTY]: "specialty_eng",
  [KeywordFilterAutocompleteFilterField.REFERRALS_SERVICE_LINE]:
    "referralsServiceLine_eng",
  [KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL]:
    "studentInstitutionNames_eng",
  [KeywordFilterAutocompleteFilterField.AFFILIATION]:
    "presentWorkInstitutionNames_multi",
  [KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_AGE]:
    "patientsDiversityAgeRanges_eng",
  [KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_DIVERSITY]:
    "patientsDiversityRaces_eng",
  [KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_ETHNICITY]:
    "patientsDiversityEthnicities",
  [KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_SEX]:
    "patientsDiversitySex_eng",
  [KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_LANGUAGES]:
    "providerDiversityLanguagesSpoken_eng",
  [KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_RACE]:
    "providerDiversityRaces_eng",
  [KeywordFilterAutocompleteFilterField.DIVERSITY_PROVIDER_SEX]:
    "providerDiversitySex_eng",
  [KeywordFilterAutocompleteFilterField.COUNTRY]: "country_multi",
  [KeywordFilterAutocompleteFilterField.REGION]: "state_multi",
  [KeywordFilterAutocompleteFilterField.CITY]: "locationToken_multi",
  [KeywordFilterAutocompleteFilterField.POSTAL_CODE]: "zipCode5"
};

const AGGREGATIONS_AVAILABLE_IN_CHINESE = new Map<
  KeywordFilterAutocompleteFilterField,
  string
>([
  [KeywordFilterAutocompleteFilterField.COUNTRY, "country_cmn"],
  [KeywordFilterAutocompleteFilterField.REGION, "state_cmn"],
  [KeywordFilterAutocompleteFilterField.CITY, "city_cmn"],
  [
    KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL,
    "studentInstitutionNames_cmn"
  ],
  [KeywordFilterAutocompleteFilterField.SPECIALTY, "specialty_cmn"]
]);

const AGGREGATIONS_AVAILABLE_IN_JAPANESE = new Map<
  KeywordFilterAutocompleteFilterField,
  string
>([
  [KeywordFilterAutocompleteFilterField.COUNTRY, "country_jpn"],
  [KeywordFilterAutocompleteFilterField.REGION, "state_jpn"],
  [KeywordFilterAutocompleteFilterField.CITY, "city_jpn"],
  [
    KeywordFilterAutocompleteFilterField.MEDICAL_SCHOOL,
    "studentInstitutionNames_jpn"
  ],
  [KeywordFilterAutocompleteFilterField.SPECIALTY, "specialty_jpn"]
]);

const HCPU_ADDRESSES = "addressesForHCPU";
const TL_ADDRESSES = "addressesForTL";
const HCPU_ADDRESSES_FILTERS = `${HCPU_ADDRESSES}.filters`;
const TL_ADDRESSES_FILTERS = `${TL_ADDRESSES}.filters`;

const GEO_LOCATION_ROLLUP_AGGREGATION_FIELDS_FOR_HCPU = new Map<
  KeywordFilterAutocompleteFilterField,
  {
    filterLocationField: string;
    filterRegionField: string;
    addressLocationField: string;
    addressRegionField: string;
  }
>([
  [
    KeywordFilterAutocompleteFilterField.COUNTRY,
    {
      filterLocationField: `${HCPU_ADDRESSES_FILTERS}.country`,
      filterRegionField: `${HCPU_ADDRESSES_FILTERS}.country_level_regions`,
      addressLocationField: `${HCPU_ADDRESSES}.country`,
      addressRegionField: `${HCPU_ADDRESSES}.country_level_regions`
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.REGION,
    {
      filterLocationField: `${HCPU_ADDRESSES_FILTERS}.region`,
      filterRegionField: `${HCPU_ADDRESSES_FILTERS}.state_level_regions`,
      addressLocationField: `${HCPU_ADDRESSES}.region`,
      addressRegionField: `${HCPU_ADDRESSES}.state_level_regions`
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.CITY,
    {
      filterLocationField: `${HCPU_ADDRESSES_FILTERS}.city`,
      filterRegionField: `${HCPU_ADDRESSES_FILTERS}.city_level_regions`,
      addressLocationField: `${HCPU_ADDRESSES}.city`,
      addressRegionField: `${HCPU_ADDRESSES}.city_level_regions`
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.POSTAL_CODE,
    {
      filterLocationField: `${HCPU_ADDRESSES_FILTERS}.postal_code`,
      filterRegionField: `${HCPU_ADDRESSES_FILTERS}.city_level_regions`,
      addressLocationField: `${HCPU_ADDRESSES}.postal_code`,
      addressRegionField: `${HCPU_ADDRESSES}.city_level_regions`
    }
  ]
]);

const GEO_LOCATION_ROLLUP_AGGREGATION_FIELDS_FOR_TL = new Map<
  KeywordFilterAutocompleteFilterField,
  {
    filterLocationField: string;
    filterRegionField: string;
    addressLocationField: string;
    addressRegionField: string;
  }
>([
  [
    KeywordFilterAutocompleteFilterField.COUNTRY,
    {
      filterLocationField: `${TL_ADDRESSES_FILTERS}.country`,
      filterRegionField: `${TL_ADDRESSES_FILTERS}.country_level_regions`,
      addressLocationField: `${TL_ADDRESSES}.country`,
      addressRegionField: `${TL_ADDRESSES}.country_level_regions`
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.REGION,
    {
      filterLocationField: `${TL_ADDRESSES_FILTERS}.region`,
      filterRegionField: `${TL_ADDRESSES_FILTERS}.state_level_regions`,
      addressLocationField: `${TL_ADDRESSES}.region`,
      addressRegionField: `${TL_ADDRESSES}.state_level_regions`
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.CITY,
    {
      filterLocationField: `${TL_ADDRESSES_FILTERS}.city`,
      filterRegionField: `${TL_ADDRESSES_FILTERS}.city_level_regions`,
      addressLocationField: `${TL_ADDRESSES}.city`,
      addressRegionField: `${TL_ADDRESSES}.city_level_regions`
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.POSTAL_CODE,
    {
      filterLocationField: `${TL_ADDRESSES_FILTERS}.postal_code`,
      filterRegionField: `${TL_ADDRESSES_FILTERS}.city_level_regions`,
      addressLocationField: `${TL_ADDRESSES}.postal_code`,
      addressRegionField: `${TL_ADDRESSES}.city_level_regions`
    }
  ]
]);

const SORT_BY_MATCHING_DOC_COUNT: AggregationsAggregateOrder = {
  ["matching.doc_count"]: "desc"
};

export function expandFilterValueToRegex(filterValue: string): string {
  return filterValue
    .trim()
    .split(" ")
    .map((word) => {
      return word
        .split("")
        .map((char) => {
          const lowerCased = char.toLowerCase();
          const upperCased = char.toUpperCase();
          const hasCaseDifference = lowerCased !== upperCased;

          if (hasCaseDifference) {
            return `[${lowerCased}${upperCased}]`;
          }

          if (REGEXP_SPECIAL_CHARACTERS.has(char)) {
            return `\\${char}`;
          }

          return char;
        })
        .join("");
    })
    .join(REGEX_ONE_OR_TWO_CHARACTERS);
}

function toFieldRangeGreaterThanValueFilter(
  fieldName: string,
  value: number
): QueryDslQueryContainer {
  return {
    range: {
      [fieldName]: {
        gte: value
      }
    }
  };
}

function toFieldRangeLesserThanValueFilter(
  fieldName: string,
  value: number
): QueryDslQueryContainer {
  return {
    range: {
      [fieldName]: {
        lte: value
      }
    }
  };
}

const CONGRESS_ID_AGGREGATION = {
  terms: {
    field: "congress.id",
    size: 1
  }
};

const MATCH_ALL = {
  match_all: {}
};

const LOCATIONS_IN_REGION_AGGREGATION_SIZE = 200;

@Service()
export class AggregationContainerBuilderService {
  constructor(
    private parsedQueryTreeToElasticsearchQueriesService: ParsedQueryTreeToElasticsearchQueriesService,
    private keywordFilterClauseBuilderService: KeywordFilterClauseBuilderService
  ) {}
  private buildParsedPaymentsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      ["payments.associatedDrugOrDevice", "payments.payerCompany"]
    );
  }

  private buildParsedCongressQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      [
        "congress.keywords_eng",
        "congress.title_eng",
        "congress.organizer_eng.search",
        "congress.name_eng.search",
        "congress.role.search"
      ]
    );
  }

  private buildParsedPublicationsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>,
    languageDetector: LanguageDetector
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      [
        "publications.publicationAbstract",
        "publications.keywords",
        "publications.title"
      ],
      languageDetector
    );
  }

  private buildParsedTrialsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      [
        "trials.officialTitle",
        "trials.briefTitle",
        "trials.conditions",
        "trials.interventions",
        "trials.keywords",
        "trials.summary"
      ],
      ENGLISH
    );
  }

  private buildParsedClaimsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>,
    claimsType: ClaimType
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      [`DRG_${claimsType}.codeAndDescription`],
      ENGLISH
    );
  }

  private buildParsedPrescriptionsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      [`prescriptions.generic_name.text`]
    );
  }

  private buildParsedCcsrQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      ["ccsr.description_eng.text"]
    );
  }

  private buildParsedCcsrPxQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      ["ccsr_px.description_eng.text"]
    );
  }

  private getMinMaxCondition(
    { minCount, minAmount }: MinValues,
    { maxCount, maxAmount }: MaxValues
  ) {
    const stringConditions = [];
    if ((minCount || maxCount) && !(minAmount || maxAmount)) {
      if (minCount) {
        stringConditions.push(MIN_CONDITION_ONLY_COUNT);
      }
      if (maxCount) {
        stringConditions.push(MAX_CONDITION_ONLY_COUNT);
      }
    } else if (!(minCount || maxCount) && (minAmount || maxAmount)) {
      if (minAmount) {
        stringConditions.push(MIN_CONDITION_ONLY_AMOUNT);
      }
      if (maxAmount) {
        stringConditions.push(MAX_CONDITION_ONLY_AMOUNT);
      }
    } else if ((minCount || maxCount) && (minAmount || maxAmount)) {
      if (minAmount) {
        stringConditions.push(MIN_CONDITION_BOTH_AMOUNT);
      }
      if (maxAmount) {
        stringConditions.push(MAX_CONDITION_BOTH_AMOUNT);
      }
      if (minCount) {
        stringConditions.push(MIN_CONDITION_BOTH_COUNT);
      }
      if (maxCount) {
        stringConditions.push(MAX_CONDITION_BOTH_COUNT);
      }
    }

    return stringConditions.join(" && ");
  }

  private buildCountFilterScript(
    minValues: MinValues,
    maxValues: MaxValues = {}
  ): AggregationsScriptedMetricAggregation {
    const condition = this.getMinMaxCondition(minValues, maxValues);

    return {
      params: {
        minCount: minValues.minCount,
        maxCount: maxValues.maxCount
      },
      init_script: "state.personToTotal = new HashMap()",
      map_script:
        "state.personToTotal.put(doc._id.value, 1 + state.personToTotal.getOrDefault(doc._id.value, 0))",
      combine_script: "return state.personToTotal",
      reduce_script: `Map personToTotal = new HashMap(); int personCount = 0; \
        for (state in states) { \
          state.forEach( \
            (personId, nestedDocCount) -> { \
              personToTotal.put(personId, nestedDocCount + personToTotal.getOrDefault(personId, 0)) \
            } \
          ); \
        } \
        for (total in personToTotal.values()) { \
          if (${condition}) { personCount++; } \
        } \
        return personCount`
    };
  }

  private buildAmountAndCountFilterScript(
    amountField: string,
    minValues: MinValues,
    maxValues: MaxValues = {}
  ): AggregationsScriptedMetricAggregation {
    const condition = this.getMinMaxCondition(minValues, maxValues);
    return {
      params: {
        minCount: minValues.minCount,
        minAmount: minValues.minAmount,
        maxCount: maxValues.maxCount,
        maxAmount: maxValues.maxAmount
      },
      init_script: "state.personToTotals = new HashMap()",
      map_script: `if (!state.personToTotals.containsKey(doc._id.value)) { \
                      state.personToTotals.put(doc._id.value, new double[2]); \
                     } \
                     state.personToTotals.get(doc._id.value)[0] += 1; \
                     state.personToTotals.get(doc._id.value)[1] += doc['${amountField}'].getValue();`,
      combine_script: "return state.personToTotals",
      reduce_script: `Map personToTotals = new HashMap(); int personCount = 0; \
                        for (state in states) { \
                          state.forEach( \
                            (personId, counts) -> { \
                              if (!personToTotals.containsKey(personId)) { personToTotals.put(personId, new double[2]); } \
                              personToTotals.get(personId)[0] += counts[0]; \
                              personToTotals.get(personId)[1] += counts[1]; \
                            } \
                          ); \
                        } \
                        for (totals in personToTotals.values()) { \
                          if (${condition}) \
                            {  \
                              personCount++; \
                            } \
                        } \
                        return personCount`
    };
  }

  private getMultiLangField(
    {
      filterField,
      filterValue,
      language: usersPreferredLanguage
    }: KeywordFilterAutocompleteInput,
    detectedLanguage: Language
  ) {
    let field;
    if (
      detectedLanguage === CHINESE &&
      NESTED_AGGREGATIONS_AVAILABLE_IN_CHINESE.has(filterField!)
    ) {
      field = NESTED_AGGREGATIONS_AVAILABLE_IN_CHINESE.get(filterField!)?.field;
    } else if (
      !filterValue &&
      usersPreferredLanguage === CHINESE &&
      NESTED_AGGREGATIONS_AVAILABLE_IN_CHINESE.has(filterField!)
    ) {
      field = NESTED_AGGREGATIONS_AVAILABLE_IN_CHINESE.get(filterField!)?.field;
    } else if (
      detectedLanguage === JAPANESE &&
      NESTED_AGGREGATIONS_AVAILABLE_IN_JAPANESE.has(filterField!)
    ) {
      field = NESTED_AGGREGATIONS_AVAILABLE_IN_JAPANESE.get(
        filterField!
      )?.field;
    } else if (
      !filterValue &&
      usersPreferredLanguage === JAPANESE &&
      NESTED_AGGREGATIONS_AVAILABLE_IN_JAPANESE.has(filterField!)
    ) {
      field = NESTED_AGGREGATIONS_AVAILABLE_IN_JAPANESE.get(
        filterField!
      )?.field;
    } else {
      field = AGGREGATIONS_THAT_NEED_NESTED_FILTERING.get(filterField!)?.field;
    }

    return field;
  }

  private buildAmountFilterScript(
    field: MinFilterField,
    minValues: MinValues,
    maxValues: MaxValues = {}
  ): AggregationsScriptedMetricAggregation {
    const condition = this.getMinMaxCondition(minValues, maxValues);
    return {
      params: {
        minAmount: minValues.minAmount,
        maxAmount: maxValues.maxAmount
      },
      init_script: "state.personToTotal = new HashMap()",
      map_script: `state.personToTotal.put(doc._id.value, doc['${field}'].value + state.personToTotal.getOrDefault(doc._id.value, 0.0))`,
      combine_script: "return state.personToTotal",
      reduce_script: `Map personToTotal = new HashMap(); int personCount = 0; \
        for (state in states) { \
          state.forEach(\
            (personId, amount) -> { \
              personToTotal.put(personId, amount + personToTotal.getOrDefault(personId, 0.0)) \
            } \
          ); \
        } \
        for (total in personToTotal.values()) { \
          if (${condition}) { personCount++; } \
        } \
        return personCount`
    };
  }

  private getMinMaxScriptForAsset(
    path: AssetPath,
    { minCount, minAmount }: MinValues,
    { maxCount, maxAmount }: MaxValues = {}
  ): AggregationsScriptedMetricAggregation {
    const microBloggingCountField = "publications.microBloggingCount";

    switch (path) {
      case "payments":
        if (minAmount || maxAmount) {
          return this.buildAmountFilterScript(
            "payments.amount",
            { minAmount },
            { maxAmount }
          );
        }
        break;
      case "congress":
        if (minCount || maxCount) {
          return this.buildCountFilterScript({ minCount }, { maxCount });
        }
        break;
      case "publications":
        if ((minAmount || maxAmount) && (minCount || maxCount)) {
          return this.buildAmountAndCountFilterScript(
            microBloggingCountField,
            { minAmount, minCount },
            { maxAmount, maxCount }
          );
        } else if (minAmount || maxAmount) {
          return this.buildAmountFilterScript(
            microBloggingCountField,
            { minAmount },
            { maxAmount }
          );
        } else if (minCount || maxCount) {
          return this.buildCountFilterScript({ minCount }, { maxCount });
        }
        break;
      case "trials":
        if (minCount || maxCount) {
          return this.buildCountFilterScript({ minCount }, { maxCount });
        }
        break;
    }

    return {};
  }

  private getParsedQueryForAsset(
    path: Omit<AssetPath, "affiliations">,
    parsedQueryTree: NonNullable<ParsedQueryTree>,
    languageDetector: LanguageDetector
  ) {
    switch (path) {
      case "payments":
        return this.buildParsedPaymentsQuery(parsedQueryTree);
      case "congress":
        return this.buildParsedCongressQuery(parsedQueryTree);
      case "publications":
        return this.buildParsedPublicationsQuery(
          parsedQueryTree,
          languageDetector
        );
      case "trials":
        return this.buildParsedTrialsQuery(parsedQueryTree);
      case "DRG_diagnoses":
        return this.buildParsedClaimsQuery(parsedQueryTree, "diagnoses");
      case "DRG_procedures":
        return this.buildParsedClaimsQuery(parsedQueryTree, "procedures");
      case "prescriptions":
        return this.buildParsedPrescriptionsQuery(parsedQueryTree);
      case "ccsr":
        return this.buildParsedCcsrQuery(parsedQueryTree);
      case "ccsr_px":
        return this.buildParsedCcsrPxQuery(parsedQueryTree);
      default:
        return MATCH_ALL;
    }
  }

  private buildPrefixPhraseMatchedAggregation(
    {
      filterField,
      filterValue,
      language: usersPreferredLanguage
    }: KeywordFilterAutocompleteInput,
    detectedLanguage: Language
  ): Record<string, AggregationsAggregationContainer> {
    let field;

    if (
      detectedLanguage === CHINESE &&
      AGGREGATIONS_AVAILABLE_IN_CHINESE.has(filterField!)
    ) {
      field = AGGREGATIONS_AVAILABLE_IN_CHINESE.get(filterField!)!;
    } else if (
      !filterValue &&
      usersPreferredLanguage === CHINESE &&
      AGGREGATIONS_AVAILABLE_IN_CHINESE.has(filterField!)
    ) {
      field = AGGREGATIONS_AVAILABLE_IN_CHINESE.get(filterField!)!;
    } else if (
      detectedLanguage === JAPANESE &&
      AGGREGATIONS_AVAILABLE_IN_JAPANESE.has(filterField!)
    ) {
      field = AGGREGATIONS_AVAILABLE_IN_JAPANESE.get(filterField!)!;
    } else if (
      !filterValue &&
      usersPreferredLanguage === JAPANESE &&
      AGGREGATIONS_AVAILABLE_IN_JAPANESE.has(filterField!)
    ) {
      field = AGGREGATIONS_AVAILABLE_IN_JAPANESE.get(filterField!)!;
    } else {
      field = AGGREGATIONS_THAT_CANT_BE_PREFIX_PHRASE_MATCHED[filterField!];
    }

    if (filterValue) {
      const filterValueExpandedForRegex = expandFilterValueToRegex(filterValue);

      //if users language is chinese or japanese, we should return the field with different regex, as the word combination could be in the middle of the field.
      //Example: if user types "台灣" in chinese, this should match "中國台灣省".
      if (detectedLanguage === CHINESE || detectedLanguage === JAPANESE) {
        return {
          matching: {
            terms: {
              field,
              include: `.*${filterValueExpandedForRegex}.*`
            }
          }
        };
      }
      return {
        matching: {
          terms: {
            field,
            include: `(.*[ \\-#~.]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
          }
        }
      };
    }

    return {
      matching: {
        terms: {
          field,
          exclude: ZERO_LENGTH_FILTER_VALUE,
          shard_size: 1000
        }
      }
    };
  }

  private async buildNestedClaimsAggregationContainer(
    {
      filterField,
      filterValue = "",
      suppliedFilters,
      ccsrIcdSize
    }: KeywordFilterAutocompleteInput,
    filters: QueryDslQueryContainer[],
    parsedQueryTree: ParsedQueryTree,
    featureFlags: AutocompleteFeatureFlags,
    ccsrClaimCodesToInclude: string[]
  ): Promise<Record<string, AggregationsAggregationContainer>> {
    const path =
      filterField === KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES ||
      filterField === KeywordFilterAutocompleteFilterField.DIAGNOSES ||
      filterField === KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES
        ? "DRG_diagnoses"
        : "DRG_procedures";

    const field = AGGREGATIONS_THAT_NEED_NESTED_FILTERING.get(
      filterField!
    )?.field;

    const assetFilters = findNestedClaimsFilters(filters, path, featureFlags);

    const filterContainer: QueryDslQueryContainer[] = [];
    if (assetFilters.length) {
      filterContainer.push(...assetFilters);
    } else if (
      parsedQueryTree &&
      !(filterField === KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES)
    ) {
      filterContainer.push(
        this.getParsedQueryForAsset(path, parsedQueryTree, USE_ENGLISH_FIELDS)
      );
    }

    const minValues: MinValues = {};
    if (path === "DRG_diagnoses") {
      minValues.minAmount =
        suppliedFilters.claims.diagnosesICDMinCount.value ?? undefined;
    } else {
      minValues.minAmount =
        suppliedFilters.claims.proceduresCPTMinCount.value ?? undefined;
    }

    const maxValues: MaxValues = {};
    if (path === "DRG_diagnoses") {
      maxValues.maxAmount =
        suppliedFilters.claims.diagnosesICDMaxCount?.value ?? undefined;
    } else {
      maxValues.maxAmount =
        suppliedFilters.claims.proceduresCPTMaxCount?.value ?? undefined;
    }
    const claimType = path == "DRG_diagnoses" ? "diagnoses" : "procedures";
    let claimsCountField = this.getInternalCountFieldForClaims(
      claimType,
      featureFlags,
      suppliedFilters
    );
    if (suppliedFilters.claims.timeFrame?.value) {
      claimsCountField =
        claimsCountField + `_${suppliedFilters.claims.timeFrame.value}_year`;
      minValues.minAmount = minValues.minAmount ?? 1;
    }

    if (minValues.minAmount) {
      const rangeFilter = toFieldRangeGreaterThanValueFilter(
        `${path}.${claimsCountField}`,
        minValues.minAmount
      );

      const rangeFilterAlreadyAdded = filterContainer.find((filter) => {
        return _.isEqual(filter, rangeFilter);
      });

      if (!rangeFilterAlreadyAdded) {
        filterContainer.push(
          toFieldRangeGreaterThanValueFilter(
            `${path}.${claimsCountField}`,
            minValues.minAmount
          )
        );
      }
    }

    if (maxValues.maxAmount) {
      const rangeFilter = toFieldRangeLesserThanValueFilter(
        `${path}.${claimsCountField}`,
        maxValues.maxAmount
      );

      const rangeFilterAlreadyAdded = filterContainer.find((filter) => {
        return _.isEqual(filter, rangeFilter);
      });

      if (!rangeFilterAlreadyAdded) {
        filterContainer.push(
          toFieldRangeLesserThanValueFilter(
            `${path}.${claimsCountField}`,
            maxValues.maxAmount
          )
        );
      }
    }

    if (
      filterField === KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES ||
      filterField === KeywordFilterAutocompleteFilterField.CCSR_PROCEDURES
    ) {
      const filtersAgg: Record<string, any> = ccsrClaimCodesToInclude.reduce(
        (acc, code) => {
          acc[code] = {
            bool: {
              filter: [buildTermQuery(field!, code), ...filterContainer]
            }
          };
          return acc;
        },
        {} as Record<string, any>
      );
      return {
        nested: {
          nested: {
            path
          },
          aggs: {
            filtered_matching: {
              filters: {
                filters: filtersAgg
              },
              aggs: {
                matching_desc: {
                  terms: {
                    field: `${path}.codeAndDescription_eng.keyword`,
                    size: ccsrIcdSize
                  }
                }
              }
            }
          }
        }
      };
    }
    return {
      nested: {
        nested: {
          path
        },
        aggs: {
          filtered_matching: {
            filter: {
              bool: {
                filter: filterContainer
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: `${field}.keyword`,
                  exclude: !filterValue ? ZERO_LENGTH_FILTER_VALUE : undefined
                }
              }
            }
          }
        }
      }
    };
  }

  private buildNestedCcsrAggregationContainer(
    {
      filterField,
      filterValue = "",
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    filters: QueryDslQueryContainer[],
    featureFlags: AutocompleteFeatureFlags,
    parsedQueryTree?: ParsedQueryTree
  ): Record<string, AggregationsAggregationContainer> {
    const isDiagnosis =
      filterField == KeywordFilterAutocompleteFilterField.CCSR;

    const { path, field } = AGGREGATIONS_THAT_NEED_NESTED_FILTERING.get(
      filterField!
    )!;

    const assetFilters = findNestedClaimsFilters(
      filters,
      path as ClaimsPath,
      featureFlags
    );

    const filterContainer: QueryDslQueryContainer[] = [];
    if (assetFilters.length) {
      filterContainer.push(...assetFilters);
    }
    if (parsedQueryTree && !filterValue.length) {
      filterContainer.push(
        this.getParsedQueryForAsset(path, parsedQueryTree, USE_ENGLISH_FIELDS)
      );
    }

    const minValues: MinValues = {};
    if (isDiagnosis) {
      minValues.minAmount =
        suppliedFilters.claims.diagnosesICDMinCount.value ?? undefined;
    } else {
      minValues.minAmount =
        suppliedFilters.claims.proceduresCPTMinCount.value ?? undefined;
    }

    const maxValues: MaxValues = {};
    if (isDiagnosis) {
      maxValues.maxAmount =
        suppliedFilters.claims.diagnosesICDMaxCount?.value ?? undefined;
    } else {
      maxValues.maxAmount =
        suppliedFilters.claims.proceduresCPTMaxCount?.value ?? undefined;
    }

    const claimType = isDiagnosis ? "diagnoses" : "procedures";
    let ccsrCountField = this.getInternalCountFieldForClaims(
      claimType,
      featureFlags,
      suppliedFilters
    );
    if (suppliedFilters.claims.timeFrame?.value) {
      ccsrCountField =
        ccsrCountField + `_${suppliedFilters.claims.timeFrame.value}_year`;
      minValues.minAmount = minValues.minAmount ?? 1;
    }

    if (minValues.minAmount) {
      const rangeFilter = toFieldRangeGreaterThanValueFilter(
        `${path}.${ccsrCountField}`,
        minValues.minAmount
      );

      const rangeFilterAlreadyAdded = filterContainer.find((filter) => {
        return _.isEqual(filter, rangeFilter);
      });

      if (!rangeFilterAlreadyAdded) {
        filterContainer.push(
          toFieldRangeGreaterThanValueFilter(
            `${path}.${ccsrCountField}`,
            minValues.minAmount
          )
        );
      }
    }

    if (maxValues.maxAmount) {
      const rangeFilter = toFieldRangeLesserThanValueFilter(
        `${path}.${ccsrCountField}`,
        maxValues.maxAmount
      );

      const rangeFilterAlreadyAdded = filterContainer.find((filter) => {
        return _.isEqual(filter, rangeFilter);
      });

      if (!rangeFilterAlreadyAdded) {
        filterContainer.push(
          toFieldRangeLesserThanValueFilter(
            `${path}.${ccsrCountField}`,
            maxValues.maxAmount
          )
        );
      }
    }
    const sizeParam = isDiagnosis ? "icdSize" : "cptSize";
    return {
      nested: {
        nested: {
          path
        },
        aggs: {
          filtered_matching: {
            filter: {
              bool: {
                filter: filterContainer
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: `${field}`,
                  exclude: !filterValue ? ZERO_LENGTH_FILTER_VALUE : undefined
                },
                aggs: {
                  icd_size: {
                    terms: {
                      field: `${path}.${sizeParam}`
                    }
                  }
                }
              }
            }
          }
        }
      }
    };
  }

  private buildNestedPrescriptionsAggregationContainer(
    {
      filterField,
      filterValue = "",
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    filters: QueryDslQueryContainer[],
    parsedQueryTree: ParsedQueryTree
  ): Record<string, AggregationsAggregationContainer> {
    const path = "prescriptions";

    const field = AGGREGATIONS_THAT_NEED_NESTED_FILTERING.get(
      filterField!
    )?.field;

    const assetFilters = findNestedFilters(filters, path);

    const filterContainer: QueryDslQueryContainer[] = [];
    if (assetFilters.length) {
      filterContainer.push(...assetFilters);
    } else if (parsedQueryTree) {
      filterContainer.push(
        this.getParsedQueryForAsset(path, parsedQueryTree, USE_ENGLISH_FIELDS)
      );
    }

    const minValues: MinValues = {};
    minValues.minAmount =
      suppliedFilters.claims.prescriptionsMinCount?.value ?? undefined;

    const maxValues: MaxValues = {};
    maxValues.maxAmount =
      suppliedFilters.claims.prescriptionsMaxCount?.value ?? undefined;

    let claimsCountField = "num_prescriptions";
    if (suppliedFilters.claims.prescriptionsTimeFrame?.value) {
      claimsCountField =
        claimsCountField +
        `_${suppliedFilters.claims.prescriptionsTimeFrame.value}_year`;
      minValues.minAmount = minValues.minAmount ?? 1;
    }

    if (minValues.minAmount) {
      const rangeFilter = toFieldRangeGreaterThanValueFilter(
        `${path}.${claimsCountField}`,
        minValues.minAmount
      );

      const rangeFilterAlreadyAdded = filterContainer.find((filter) => {
        return _.isEqual(filter, rangeFilter);
      });

      if (!rangeFilterAlreadyAdded) {
        filterContainer.push(
          toFieldRangeGreaterThanValueFilter(
            `${path}.${claimsCountField}`,
            minValues.minAmount
          )
        );
      }
    }

    if (maxValues.maxAmount) {
      const rangeFilter = toFieldRangeLesserThanValueFilter(
        `${path}.${claimsCountField}`,
        maxValues.maxAmount
      );

      const rangeFilterAlreadyAdded = filterContainer.find((filter) => {
        return _.isEqual(filter, rangeFilter);
      });

      if (!rangeFilterAlreadyAdded) {
        filterContainer.push(
          toFieldRangeLesserThanValueFilter(
            `${path}.${claimsCountField}`,
            maxValues.maxAmount
          )
        );
      }
    }

    return {
      nested: {
        nested: {
          path
        },
        aggs: {
          filtered_matching: {
            filter: {
              bool: {
                filter: filterContainer
              }
            },
            aggs: {
              matching: {
                terms: {
                  field,
                  exclude: !filterValue ? ZERO_LENGTH_FILTER_VALUE : undefined
                },
                aggs: {
                  matching: {
                    reverse_nested: {}
                  }
                }
              }
            }
          }
        }
      }
    };
  }

  private buildDesignationsAggregationContainer(
    input: KeywordFilterAutocompleteInput
  ): Record<string, AggregationsAggregationContainer> {
    const matches = new RegExp(`^${input.filterValue}`, "i");
    const designations = input.designations?.filter((designation: string) =>
      matches.test(designation)
    );

    // TODO: this can probably just use the output of buildPrefixPhraseMatchedAggregation with a slightly modified include
    return {
      matching: {
        terms: {
          field:
            AGGREGATIONS_THAT_CANT_BE_PREFIX_PHRASE_MATCHED[input.filterField!],
          include: designations
        }
      }
    };
  }

  private buildNestedPaymentsAggregationContainer =
    this.buildNestedPaymentsOrCongressAggregationContainer.bind(
      this,
      "payments"
    );
  private buildNestedCongressAggregationContainer =
    this.buildNestedPaymentsOrCongressAggregationContainer.bind(
      this,
      "congress"
    );

  private buildNestedPaymentsOrCongressAggregationContainer(
    assetPath: "payments" | "congress",
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    filters: QueryDslQueryContainer[],
    parsedQueryTree: ParsedQueryTree,
    featureFlags: AutocompleteFeatureFlags
  ): Record<string, AggregationsAggregationContainer> {
    const field = AGGREGATIONS_THAT_NEED_NESTED_FILTERING.get(
      filterField!
    )?.field;
    const assetFilters = findNestedFilters(filters, assetPath);

    const filterContainer: QueryDslQueryContainer[] = [];
    if (assetFilters.length) {
      filterContainer.push(...assetFilters);
    } else if (parsedQueryTree) {
      // TODO: Remove this condition once we have the ability to determine whether a query contains drug names
      if (
        assetPath !== "payments" ||
        featureFlags.enableQueryContextualPaymentsFiltering
      ) {
        filterContainer.push(
          this.getParsedQueryForAsset(
            assetPath,
            parsedQueryTree,
            USE_ENGLISH_FIELDS
          )
        );
      }
    }

    const minValues: MinValues = {};
    if (assetPath === "payments") {
      minValues.minAmount =
        suppliedFilters.payments.minAmount.value ?? undefined;
    } else {
      minValues.minCount =
        suppliedFilters.congresses.minCount.value ?? undefined;
    }

    const maxValues: MaxValues = {};
    if (assetPath === "payments") {
      maxValues.maxAmount =
        suppliedFilters.payments.maxAmount?.value ?? undefined;
    } else {
      maxValues.maxCount =
        suppliedFilters.congresses.maxCount?.value ?? undefined;
    }

    let subAggregation: Record<string, AggregationsAggregationContainer>;
    if (minValues.minAmount || maxValues.maxAmount) {
      subAggregation = {
        matching: {
          scripted_metric: this.getMinMaxScriptForAsset(
            assetPath,
            minValues,
            maxValues
          )
        }
      };
    } else {
      subAggregation = {
        matching: {
          reverse_nested: {}
        }
      };
    }

    if (subAggregation && assetPath === "congress") {
      subAggregation.by_congressId = CONGRESS_ID_AGGREGATION;
    }

    const isSubAggregationReverseNested = !(
      minValues.minAmount ||
      minValues.minCount ||
      maxValues.maxAmount ||
      maxValues.maxCount
    );

    return {
      nested: {
        nested: {
          path: assetPath
        },
        aggs: {
          filtered_matching: {
            filter: {
              bool: {
                filter: filterContainer
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: field,
                  exclude: !filterValue ? ZERO_LENGTH_FILTER_VALUE : undefined,
                  order: isSubAggregationReverseNested
                    ? SORT_BY_MATCHING_DOC_COUNT
                    : undefined
                },
                aggs: subAggregation
              }
            }
          }
        }
      }
    };
  }

  private buildNestedPublicationsAggregationContainer(
    input: KeywordFilterAutocompleteInput,
    filters: QueryDslQueryContainer[],
    languageDetector: LanguageDetector,
    parsedQueryTree: ParsedQueryTree
  ): Record<string, AggregationsAggregationContainer> {
    const field = this.getMultiLangField(
      input,
      languageDetector(input.filterValue!)
    );
    const publicationFilters = findNestedFilters(filters, "publications");

    const filterContainer: QueryDslQueryContainer[] = [];
    if (publicationFilters.length) {
      filterContainer.push(...publicationFilters);
    } else if (parsedQueryTree) {
      filterContainer.push(
        this.getParsedQueryForAsset(
          "publications",
          parsedQueryTree,
          languageDetector
        )
      );
    }

    const minCount =
      input.suppliedFilters.publications.minCount.value ?? undefined;
    const maxCount =
      input.suppliedFilters.publications.maxCount?.value ?? undefined;
    const minMicrobloggingCount =
      input.suppliedFilters.publications.socialMediaMinCount.value ?? undefined;
    const maxMicrobloggingCount =
      input.suppliedFilters.publications.socialMediaMaxCount?.value ??
      undefined;

    let subAggregation;
    if (minMicrobloggingCount || maxMicrobloggingCount) {
      subAggregation = {
        scripted_metric: this.getMinMaxScriptForAsset(
          "publications",
          {
            minCount,
            minAmount: minMicrobloggingCount
          },
          {
            maxCount,
            maxAmount: maxMicrobloggingCount
          }
        )
      };
    } else {
      subAggregation = {
        reverse_nested: {}
      };
    }

    const isSubAggregationReverseNested = !(
      minCount ||
      minMicrobloggingCount ||
      maxCount ||
      maxMicrobloggingCount
    );

    return {
      nested: {
        nested: {
          path: "publications"
        },
        aggs: {
          filtered_matching: {
            filter: {
              bool: {
                filter: filterContainer
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: field,
                  exclude: !input.filterValue
                    ? ZERO_LENGTH_FILTER_VALUE
                    : undefined,
                  order: isSubAggregationReverseNested
                    ? SORT_BY_MATCHING_DOC_COUNT
                    : undefined
                },
                aggs: {
                  matching: subAggregation
                }
              }
            }
          }
        }
      }
    };
  }

  private buildNestedTrialsAggregationContainer(
    {
      filterField,
      filterValue,
      suppliedFilters
    }: KeywordFilterAutocompleteInput,
    filters: QueryDslQueryContainer[],
    parsedQueryTree: ParsedQueryTree
  ): Record<string, AggregationsAggregationContainer> {
    const field = AGGREGATIONS_THAT_NEED_NESTED_FILTERING.get(
      filterField!
    )?.field;
    const trialsFilters = findNestedFilters(filters, "trials");

    const filterContainer: QueryDslQueryContainer[] = [];
    if (trialsFilters.length) {
      filterContainer.push(...trialsFilters);
    } else if (parsedQueryTree) {
      filterContainer.push(
        this.getParsedQueryForAsset(
          "trials",
          parsedQueryTree,
          USE_ENGLISH_FIELDS
        )
      );
    }

    const minCount = suppliedFilters.trials.minCount.value ?? undefined;
    const maxCount = suppliedFilters.trials.maxCount?.value ?? undefined;

    const subAggregation = {
      reverse_nested: {}
    };
    const isSubAggregationReverseNested = !(minCount || maxCount);

    let shardSize;

    // trial id is defined as keyword and was returning incorrect count so to solve this issue we are adding shard_size to the terms query
    if (filterField === KeywordFilterAutocompleteFilterField.TRIAL_ID) {
      shardSize = 10000;
    }

    return {
      nested: {
        nested: {
          path: "trials"
        },
        aggs: {
          filtered_matching: {
            filter: {
              bool: {
                filter: filterContainer
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: field,
                  exclude: !filterValue ? ZERO_LENGTH_FILTER_VALUE : undefined,
                  shard_size: shardSize,
                  order: isSubAggregationReverseNested
                    ? SORT_BY_MATCHING_DOC_COUNT
                    : undefined
                },
                aggs: {
                  matching: subAggregation
                }
              }
            }
          }
        }
      }
    };
  }

  private buildNestedAggregationContainer(
    input: KeywordFilterAutocompleteInput,
    filters: QueryDslQueryContainer[],
    path: AssetPath,
    parsedQueryTree: ParsedQueryTree,
    languageDetector: LanguageDetector,
    featureFlags: AutocompleteFeatureFlags,
    ccsrClaimCodesToInclude: string[]
  ) {
    switch (path) {
      case "payments": {
        return this.buildNestedPaymentsAggregationContainer(
          input,
          filters,
          parsedQueryTree,
          featureFlags
        );
      }
      case "congress": {
        return this.buildNestedCongressAggregationContainer(
          input,
          filters,
          parsedQueryTree,
          featureFlags
        );
      }
      case "publications": {
        return this.buildNestedPublicationsAggregationContainer(
          input,
          filters,
          languageDetector,
          parsedQueryTree
        );
      }
      case "trials": {
        return this.buildNestedTrialsAggregationContainer(
          input,
          filters,
          parsedQueryTree
        );
      }
      case "DRG_diagnoses": {
        return this.buildNestedClaimsAggregationContainer(
          input,
          filters,
          parsedQueryTree,
          featureFlags,
          ccsrClaimCodesToInclude
        );
      }
      case "DRG_procedures": {
        return this.buildNestedClaimsAggregationContainer(
          input,
          filters,
          parsedQueryTree,
          featureFlags,
          ccsrClaimCodesToInclude
        );
      }
      case "affiliations": {
        return this.buildNestedAffiliationsAggregationContainer(input, path);
      }
      case "prescriptions": {
        return this.buildNestedPrescriptionsAggregationContainer(
          input,
          filters,
          parsedQueryTree
        );
      }
      case "ccsr": {
        return this.buildNestedCcsrAggregationContainer(
          input,
          filters,
          featureFlags,
          parsedQueryTree
        );
      }
      case "ccsr_px": {
        return this.buildNestedCcsrAggregationContainer(
          input,
          filters,
          featureFlags,
          parsedQueryTree
        );
      }
    }
    return {};
  }

  public buildNestedLocationsAggregationContainer(
    input: KeywordFilterAutocompleteInput
  ): Record<string, AggregationsAggregationContainer> {
    const { filterField, filterValue } = input;

    let fields, nestedPath: string;
    if (input.app === Apps.TRIAL_LANDSCAPE) {
      fields = GEO_LOCATION_ROLLUP_AGGREGATION_FIELDS_FOR_TL.get(filterField!);
      nestedPath = TL_ADDRESSES;
    } else {
      fields = GEO_LOCATION_ROLLUP_AGGREGATION_FIELDS_FOR_HCPU.get(
        filterField!
      );
      nestedPath = HCPU_ADDRESSES;
    }

    if (!fields) {
      return {};
    }

    const {
      filterLocationField,
      filterRegionField,
      addressLocationField,
      addressRegionField
    } = fields;

    const reverseNestedAggregation = {
      matching: {
        reverse_nested: {}
      }
    };

    const isPostalCodeFilter =
      filterField === KeywordFilterAutocompleteFilterField.POSTAL_CODE;

    const termsAggregationParameters = {
      exclude: !filterValue ? ZERO_LENGTH_FILTER_VALUE : undefined,
      order: SORT_BY_MATCHING_DOC_COUNT,
      ...(isPostalCodeFilter ? ONLY_INCLUDE_US_ADDRESSES : {})
    };
    const filters =
      this.keywordFilterClauseBuilderService.buildGeoRollupLocationFilters(
        input
      );

    let phrasePrefix;
    let locationFilters;
    if (filters) {
      const nestedAddressQuery = (filters as QueryDslQueryContainer[]).find(
        (filter) => filter.nested?.path === nestedPath
      );

      const nestedAddressesFilters = nestedAddressQuery?.nested?.query?.bool
        ?.filter as QueryContainer;

      phrasePrefix = nestedAddressesFilters?.find(
        (filter) =>
          (filter.bool?.should as QueryContainer)?.[0].match_phrase_prefix
      );

      if (nestedAddressesFilters) {
        locationFilters = {
          bool: {
            filter: nestedAddressesFilters
          }
        };
      }
    }

    const filterValueExpandedForRegex = filterValue
      ? expandFilterValueToRegex(filterValue)
      : undefined;

    let locationPhrasePrefix, regionPhrasePrefix;
    if (!isPostalCodeFilter && phrasePrefix) {
      locationPhrasePrefix = (
        phrasePrefix?.bool?.should as QueryDslQueryContainer[]
      ).find((should) => should.match_phrase_prefix?.[addressLocationField]);
      regionPhrasePrefix = (
        phrasePrefix?.bool?.should as QueryDslQueryContainer[]
      ).find((should) => should.match_phrase_prefix?.[addressRegionField]);
    }

    let subAggregations: Record<string, AggregationsAggregationContainer> = {
      filtered_matching_locations: {
        filter: {
          bool: {
            filter: locationPhrasePrefix ?? []
          }
        },
        aggs: {
          matching_locations: {
            terms: {
              field: filterLocationField,
              ...termsAggregationParameters
            },
            aggs: {
              ...reverseNestedAggregation,
              regions_in: {
                terms: {
                  field: filterRegionField,
                  exclude: ZERO_LENGTH_FILTER_VALUE
                }
              }
            }
          }
        }
      }
    };

    if (!isPostalCodeFilter) {
      subAggregations = {
        ...subAggregations,
        filtered_matching_regions: {
          filter: {
            bool: {
              filter: regionPhrasePrefix ? [regionPhrasePrefix] : []
            }
          },
          aggs: {
            matching_regions: {
              terms: {
                field: filterRegionField,
                ...termsAggregationParameters,
                include: filterValueExpandedForRegex
                  ? `(.*[ \\-#~.(]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                  : undefined
              },
              aggs: {
                ...reverseNestedAggregation,
                locations_in_region: {
                  terms: {
                    field: filterLocationField,
                    exclude: ZERO_LENGTH_FILTER_VALUE,
                    size: LOCATIONS_IN_REGION_AGGREGATION_SIZE
                  },
                  aggs: reverseNestedAggregation
                }
              }
            }
          }
        }
      };
    }

    return {
      nested: {
        nested: {
          path: nestedPath
        },
        aggs: {
          filtered_matching: {
            filter: locationFilters ?? {
              bool: {
                filter: []
              }
            },
            aggs: subAggregations
          }
        }
      }
    };
  }

  public buildNestedAffiliationsAggregationContainer(
    { filterField, filterValue }: KeywordFilterAutocompleteInput,
    assetPath: AssetPath
  ) {
    const field = AGGREGATIONS_THAT_NEED_NESTED_FILTERING.get(
      filterField!
    )?.field;

    const filterContainer: QueryDslQueryContainer[] = [];
    if (
      filterField == KeywordFilterAutocompleteFilterField.SOCIETY_AFFILIATIONS
    ) {
      filterContainer.push({
        term: {
          ["affiliations.type"]: {
            value: "Society Member"
          }
        }
      });
    }

    const subAggregation: Record<string, AggregationsAggregationContainer> = {
      matching: {
        reverse_nested: {}
      }
    };
    const filterValueExpandedForRegex = filterValue
      ? expandFilterValueToRegex(filterValue)
      : undefined;

    return {
      nested: {
        nested: {
          path: assetPath
        },
        aggs: {
          filtered_matching: {
            filter: {
              bool: {
                filter: filterContainer
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: field,
                  include: filterValue
                    ? `(.*[ \\-#~.(]${filterValueExpandedForRegex}.*)|(${filterValueExpandedForRegex}.*)`
                    : undefined,
                  exclude: !filterValue ? ZERO_LENGTH_FILTER_VALUE : undefined,
                  order: SORT_BY_MATCHING_DOC_COUNT
                },
                aggs: subAggregation
              }
            }
          }
        }
      }
    };
  }

  public async buildAggregationContainer(
    input: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector,
    filters: QueryDslQueryContainer[],
    featureFlags: AutocompleteFeatureFlags,
    ccsrClaimCodesToInclude: string[],
    parsedQueryTree?: ParsedQueryTree
  ): Promise<Record<string, AggregationsAggregationContainer>> {
    if (
      input.filterField === KeywordFilterAutocompleteFilterField.DESIGNATION
    ) {
      return this.buildDesignationsAggregationContainer(input);
    }

    if (input.filterField) {
      if (
        this.isLocationFilterField(input.filterField) &&
        featureFlags.enableLocationFilterRegionRollup
      ) {
        return this.buildNestedLocationsAggregationContainer(input);
      }

      const pathAndField = AGGREGATIONS_THAT_NEED_NESTED_FILTERING.get(
        input.filterField
      );

      if (pathAndField) {
        return await this.buildNestedAggregationContainer(
          input,
          filters,
          pathAndField.path as AssetPath,
          parsedQueryTree,
          languageDetector,
          featureFlags,
          ccsrClaimCodesToInclude
        );
      }
    }

    // TODO: figure out a better way to filter, it physically pains me to do this way
    if (
      input.filterField &&
      AGGREGATIONS_THAT_CANT_BE_PREFIX_PHRASE_MATCHED[input.filterField]
    ) {
      return this.buildPrefixPhraseMatchedAggregation(
        input,
        languageDetector(input.filterValue!)
      );
    }

    throw new Error(`unknown aggregation field: ${input.filterField}`);
  }
  private getInternalCountFieldForClaims(
    claimType: ClaimType,
    featureFlags: AutocompleteFeatureFlags,
    suppliedFilters: FilterInterface
  ) {
    const {
      enableUniquePatientCountForClaims,
      disableUniquePatientCountForOnlyProcedures
    } = featureFlags;
    const uniquePatientToggleFromInput: boolean =
      suppliedFilters.claims?.showUniquePatients?.value ?? false;
    const isProcedureWithUniqueCount =
      claimType === "procedures" && !disableUniquePatientCountForOnlyProcedures;
    const isDiagnosis = claimType === "diagnoses";

    const shouldUseUniquePatientCount =
      uniquePatientToggleFromInput &&
      (isProcedureWithUniqueCount || isDiagnosis) &&
      enableUniquePatientCountForClaims;

    const claimsCountField = shouldUseUniquePatientCount
      ? "internalUniqueCount"
      : "internalCount";
    return claimsCountField;
  }

  private isLocationFilterField(
    filterField: KeywordFilterAutocompleteFilterField
  ) {
    return (
      filterField === KeywordFilterAutocompleteFilterField.COUNTRY ||
      filterField === KeywordFilterAutocompleteFilterField.REGION ||
      filterField === KeywordFilterAutocompleteFilterField.CITY ||
      filterField === KeywordFilterAutocompleteFilterField.POSTAL_CODE
    );
  }
}
