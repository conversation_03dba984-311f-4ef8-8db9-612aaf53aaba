import { UserResourceClient } from "@h1nyc/account-sdk";
import { KolNameSuggestionInput } from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";
import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  CHINESE,
  ENGLISH,
  JAPANESE,
  LanguageDetectService
} from "./LanguageDetectService";
import {
  featureFlagDefaults,
  getNameSearchInput,
  NameSuggestFeatureFlags,
  NameSuggestResourceService
} from "./NameSuggestResourceService";
import { NameSearchResourceServiceRewrite } from "./NameSearchResourceServiceRewrite";
import { generateMockPersonSearchResponse } from "./NameSearchResponseAdapterService.test";
import { FeatureFlagsService, LDUserInput } from "@h1nyc/systems-feature-flags";
import { LDFlagsState } from "launchdarkly-node-server-sdk";
import { AffiliationAdapterService } from "./AffiliationAdapterService";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";
import { getNameSuggestBuilder } from "../lib/NameSuggestBuilder";

export function generateMockLanguage(language: string): any {
  return {
    language,
    title: faker.datatype.string(),
    flagCountryCode: faker.address.countryCode(),
    userLanguageSettings: [],
    projectLanguageSettings: []
  };
}

function getNameSuggestFeatureFlags(
  overrides: Partial<NameSuggestFeatureFlags> = {}
) {
  return Object.entries(featureFlagDefaults).reduce((acc, [key, value]) => {
    if (typeof overrides[key as keyof NameSuggestFeatureFlags] !== undefined) {
      acc[key] = overrides[key as keyof NameSuggestFeatureFlags]!;
    } else {
      acc[key] = value.default;
    }
    return acc;
  }, {} as Record<string, boolean>);
}

function mockFeatureFlagsService(
  overrides: Partial<NameSuggestFeatureFlags> = {}
) {
  const values = Object.entries(featureFlagDefaults).reduce(
    (acc, [key, value]) => {
      if (overrides[key as keyof NameSuggestFeatureFlags]) {
        acc[value.key] = overrides[key as keyof NameSuggestFeatureFlags]!;
      } else {
        acc[value.key] = value.default;
      }
      return acc;
    },
    {} as Record<string, boolean>
  );

  const featureFlagsService = createMockInstance(
    FeatureFlagsService as any
  ) as jest.Mocked<FeatureFlagsService>;

  featureFlagsService.getFlag.mockImplementation(
    (flagName: string): Promise<boolean> => {
      return Promise.resolve(values[flagName]);
    }
  );

  featureFlagsService.getAllFlags.mockImplementation(
    (_user?: LDUserInput): Promise<LDFlagsState> => {
      return Promise.resolve({
        valid: true,
        getFlagValue: jest.fn().mockImplementation((flagName: string) => {
          return values[flagName];
        }),
        getFlagReason: jest.fn(),
        allValues: jest.fn(),
        toJSON: jest.fn()
      });
    }
  );

  return featureFlagsService;
}

describe("NameSuggestResourceService", () => {
  describe("multiLang disabled", () => {
    it("should NOT make a call to UserResourceClient.getUsersPreferredLanguage if language is provided in the input", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const mockElasticsearchResponse = {
        hits: {
          total: {
            value: faker.datatype.number()
          },
          hits: [
            {
              _source: {
                id: faker.datatype.string(),
                name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                designations: [faker.name.suffix(), faker.name.suffix()],
                topL3Indications: [faker.name.jobTitle(), faker.name.jobTitle()]
              }
            },
            {
              _source: {
                id: faker.datatype.string(),
                name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                designations: [faker.name.suffix(), faker.name.suffix()],
                topL3Indications: [faker.name.jobTitle(), faker.name.jobTitle()]
              }
            }
          ]
        }
      };

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.getSignedElasticRequest.mockResolvedValue(
        mockElasticsearchResponse
      );

      const languageDetectService = createMockInstance(LanguageDetectService);
      languageDetectService.determineSupportedLanguageWithUserPreference.mockResolvedValue(
        ENGLISH
      );

      const userResourceClient = createMockInstance(UserResourceClient);
      const usersPreferredLanguage = faker.datatype.string();
      userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
        generateMockLanguage(usersPreferredLanguage)
      );

      const nameSearchResourceServiceRewrite = createMockInstance(
        NameSearchResourceServiceRewrite
      );

      const featureFlagsService = mockFeatureFlagsService();
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const nameSuggestResourceService = new NameSuggestResourceService(
        configService,
        elasticSearchService,
        languageDetectService,
        userResourceClient,
        searchAnalyticsTracerService,
        featureFlagsService,
        nameSearchResourceServiceRewrite,
        affiliationAdapterService
      );

      const input: KolNameSuggestionInput = {
        query: [faker.lorem.word(), faker.lorem.word()],
        limit: faker.datatype.number(),
        offset: faker.datatype.number(),
        language: [JAPANESE, CHINESE, ENGLISH][faker.datatype.number(2)]
      };
      const peopleIndex = faker.datatype.string();
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const multiLangEnabled = false;

      const response = await nameSuggestResourceService.nameSuggestSearch(
        input,
        peopleIndex,
        userId,
        projectId,
        multiLangEnabled
      );

      expect(response).toEqual({
        total: mockElasticsearchResponse.hits.total.value,
        results: [
          {
            personId: mockElasticsearchResponse.hits.hits[0]._source.id,
            name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
            specialties:
              mockElasticsearchResponse.hits.hits[0]._source.specialty_eng,
            designations:
              mockElasticsearchResponse.hits.hits[0]._source.designations,
            h1dnId: null,
            topL3Indications: []
          },
          {
            personId: mockElasticsearchResponse.hits.hits[1]._source.id,
            name: mockElasticsearchResponse.hits.hits[1]._source.name_eng,
            specialties:
              mockElasticsearchResponse.hits.hits[1]._source.specialty_eng,
            designations:
              mockElasticsearchResponse.hits.hits[1]._source.designations,
            h1dnId: null,
            topL3Indications: []
          }
        ]
      });

      expect(
        userResourceClient.getUsersPreferredLanguage
      ).not.toHaveBeenCalled();
      expect(
        languageDetectService.determineSupportedLanguageWithUserPreference
      ).not.toHaveBeenCalled();
      expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalledWith(
        expect.any(Object),
        peopleIndex,
        {
          searchType: "dfs_query_then_fetch"
        }
      );
    });

    describe("user's preferred language is 'eng'", () => {
      it("should use 'eng' fields", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const mockElasticsearchResponse = {
          hits: {
            total: {
              value: faker.datatype.number()
            },
            hits: [
              {
                _source: {
                  id: faker.datatype.string(),
                  h1dn_id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              },
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              }
            ]
          }
        };

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.getSignedElasticRequest.mockResolvedValue(
          mockElasticsearchResponse
        );

        const languageDetectService = createMockInstance(LanguageDetectService);

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage(faker.datatype.string())
        );
        const nameSearchResourceServiceRewrite = createMockInstance(
          NameSearchResourceServiceRewrite
        );
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const featureFlagsService = mockFeatureFlagsService();
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const nameSuggestResourceService = new NameSuggestResourceService(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          searchAnalyticsTracerService,
          featureFlagsService,
          nameSearchResourceServiceRewrite,
          affiliationAdapterService
        );

        const input: KolNameSuggestionInput = {
          query: [faker.lorem.word(), faker.lorem.word()],
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const peopleIndex = faker.datatype.string();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const multiLangEnabled = false;

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(response).toEqual({
          total: mockElasticsearchResponse.hits.total.value,
          results: [
            {
              personId: mockElasticsearchResponse.hits.hits[0]._source.id,
              name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[0]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[0]._source.designations,
              h1dnId: mockElasticsearchResponse.hits.hits[0]._source.h1dn_id,
              topL3Indications: []
            },
            {
              personId: mockElasticsearchResponse.hits.hits[1]._source.id,
              name: mockElasticsearchResponse.hits.hits[1]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[1]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[1]._source.designations,
              h1dnId: null,
              topL3Indications: []
            }
          ]
        });

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(userId, projectId);
        expect(
          languageDetectService.determineSupportedLanguageWithUserPreference
        ).not.toHaveBeenCalled();
        expect(
          elasticSearchService.getSignedElasticRequest
        ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
          searchType: "dfs_query_then_fetch"
        });
      });

      it("should return empty designations and specialties when not available in 'eng'", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const mockElasticsearchResponse = {
          hits: {
            total: {
              value: faker.datatype.number()
            },
            hits: [
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              }
            ]
          }
        };

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.getSignedElasticRequest.mockResolvedValue(
          mockElasticsearchResponse
        );

        const languageDetectService = createMockInstance(LanguageDetectService);

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage(faker.datatype.string())
        );

        const nameSearchResourceServiceRewrite = createMockInstance(
          NameSearchResourceServiceRewrite
        );

        const featureFlagsService = mockFeatureFlagsService();
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const nameSuggestResourceService = new NameSuggestResourceService(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          searchAnalyticsTracerService,
          featureFlagsService,
          nameSearchResourceServiceRewrite,
          affiliationAdapterService
        );

        const input: KolNameSuggestionInput = {
          query: [faker.lorem.word(), faker.lorem.word()],
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const peopleIndex = faker.datatype.string();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const multiLangEnabled = false;

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(response).toEqual({
          total: mockElasticsearchResponse.hits.total.value,
          results: [
            {
              personId: mockElasticsearchResponse.hits.hits[0]._source.id,
              name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
              specialties: [],
              designations: [],
              h1dnId: null,
              topL3Indications: []
            }
          ]
        });

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(userId, projectId);
        expect(
          languageDetectService.determineSupportedLanguageWithUserPreference
        ).not.toHaveBeenCalled();
        expect(
          elasticSearchService.getSignedElasticRequest
        ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
          searchType: "dfs_query_then_fetch"
        });
      });
    });

    describe("user's preferred language is 'jpn'", () => {
      it("should use 'eng' fields anyway", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const mockElasticsearchResponse = {
          hits: {
            total: {
              value: faker.datatype.number()
            },
            hits: [
              {
                _source: {
                  id: faker.datatype.string(),
                  h1dn_id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              },
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              }
            ]
          }
        };

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.getSignedElasticRequest.mockResolvedValue(
          mockElasticsearchResponse
        );

        const languageDetectService = createMockInstance(LanguageDetectService);

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage("jpn")
        );

        const nameSearchResourceServiceRewrite = createMockInstance(
          NameSearchResourceServiceRewrite
        );

        const featureFlagsService = mockFeatureFlagsService();
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const nameSuggestResourceService = new NameSuggestResourceService(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          searchAnalyticsTracerService,
          featureFlagsService,
          nameSearchResourceServiceRewrite,
          affiliationAdapterService
        );

        const input: KolNameSuggestionInput = {
          query: [faker.lorem.word(), faker.lorem.word()],
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const peopleIndex = faker.datatype.string();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const multiLangEnabled = false;

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(response).toEqual({
          total: mockElasticsearchResponse.hits.total.value,
          results: [
            {
              personId: mockElasticsearchResponse.hits.hits[0]._source.id,
              name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[0]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[0]._source.designations,
              h1dnId: mockElasticsearchResponse.hits.hits[0]._source.h1dn_id,
              topL3Indications: []
            },
            {
              personId: mockElasticsearchResponse.hits.hits[1]._source.id,
              name: mockElasticsearchResponse.hits.hits[1]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[1]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[1]._source.designations,
              h1dnId: null,
              topL3Indications: []
            }
          ]
        });

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(userId, projectId);
        expect(
          languageDetectService.determineSupportedLanguageWithUserPreference
        ).not.toHaveBeenCalled();
        expect(
          elasticSearchService.getSignedElasticRequest
        ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
          searchType: "dfs_query_then_fetch"
        });
      });

      it("should return empty designations and specialties when not available in 'eng'", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const mockElasticsearchResponse = {
          hits: {
            total: {
              value: faker.datatype.number()
            },
            hits: [
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              }
            ]
          }
        };

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.getSignedElasticRequest.mockResolvedValue(
          mockElasticsearchResponse
        );

        const languageDetectService = createMockInstance(LanguageDetectService);

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage("jpn")
        );

        const nameSearchResourceServiceRewrite = createMockInstance(
          NameSearchResourceServiceRewrite
        );

        const featureFlagsService = mockFeatureFlagsService();
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const nameSuggestResourceService = new NameSuggestResourceService(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          searchAnalyticsTracerService,
          featureFlagsService,
          nameSearchResourceServiceRewrite,
          affiliationAdapterService
        );

        const input: KolNameSuggestionInput = {
          query: [faker.lorem.word(), faker.lorem.word()],
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const peopleIndex = faker.datatype.string();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const multiLangEnabled = false;

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(response).toEqual({
          total: mockElasticsearchResponse.hits.total.value,
          results: [
            {
              personId: mockElasticsearchResponse.hits.hits[0]._source.id,
              name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
              specialties: [],
              designations: [],
              h1dnId: null,
              topL3Indications: []
            }
          ]
        });

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(userId, projectId);
        expect(
          languageDetectService.determineSupportedLanguageWithUserPreference
        ).not.toHaveBeenCalled();
        expect(
          elasticSearchService.getSignedElasticRequest
        ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
          searchType: "dfs_query_then_fetch"
        });
      });
    });

    describe("user's preferred language is 'cmn'", () => {
      it("should use 'eng' fields anyway", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const mockElasticsearchResponse = {
          hits: {
            total: {
              value: faker.datatype.number()
            },
            hits: [
              {
                _source: {
                  id: faker.datatype.string(),
                  h1dn_id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              },
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              }
            ]
          }
        };

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.getSignedElasticRequest.mockResolvedValue(
          mockElasticsearchResponse
        );

        const languageDetectService = createMockInstance(LanguageDetectService);

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage("cmn")
        );

        const nameSearchResourceServiceRewrite = createMockInstance(
          NameSearchResourceServiceRewrite
        );

        const featureFlagsService = mockFeatureFlagsService();
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const nameSuggestResourceService = new NameSuggestResourceService(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          searchAnalyticsTracerService,
          featureFlagsService,
          nameSearchResourceServiceRewrite,
          affiliationAdapterService
        );

        const input: KolNameSuggestionInput = {
          query: [faker.lorem.word(), faker.lorem.word()],
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const peopleIndex = faker.datatype.string();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const multiLangEnabled = false;

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(response).toEqual({
          total: mockElasticsearchResponse.hits.total.value,
          results: [
            {
              personId: mockElasticsearchResponse.hits.hits[0]._source.id,
              name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[0]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[0]._source.designations,
              h1dnId: mockElasticsearchResponse.hits.hits[0]._source.h1dn_id,
              topL3Indications: []
            },
            {
              personId: mockElasticsearchResponse.hits.hits[1]._source.id,
              name: mockElasticsearchResponse.hits.hits[1]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[1]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[1]._source.designations,
              h1dnId: null,
              topL3Indications: []
            }
          ]
        });

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(userId, projectId);
        expect(
          languageDetectService.determineSupportedLanguageWithUserPreference
        ).not.toHaveBeenCalled();
        expect(
          elasticSearchService.getSignedElasticRequest
        ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
          searchType: "dfs_query_then_fetch"
        });
      });

      it("should return empty designations and specialties when not available in 'eng'", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const mockElasticsearchResponse = {
          hits: {
            total: {
              value: faker.datatype.number()
            },
            hits: [
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              }
            ]
          }
        };

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.getSignedElasticRequest.mockResolvedValue(
          mockElasticsearchResponse
        );

        const languageDetectService = createMockInstance(LanguageDetectService);

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage("cmn")
        );

        const nameSearchResourceServiceRewrite = createMockInstance(
          NameSearchResourceServiceRewrite
        );

        const featureFlagsService = mockFeatureFlagsService();
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const nameSuggestResourceService = new NameSuggestResourceService(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          searchAnalyticsTracerService,
          featureFlagsService,
          nameSearchResourceServiceRewrite,
          affiliationAdapterService
        );
        const input: KolNameSuggestionInput = {
          query: [faker.lorem.word(), faker.lorem.word()],
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const peopleIndex = faker.datatype.string();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const multiLangEnabled = false;

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(response).toEqual({
          total: mockElasticsearchResponse.hits.total.value,
          results: [
            {
              personId: mockElasticsearchResponse.hits.hits[0]._source.id,
              name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
              specialties: [],
              designations: [],
              h1dnId: null,
              topL3Indications: []
            }
          ]
        });

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(userId, projectId);
        expect(
          languageDetectService.determineSupportedLanguageWithUserPreference
        ).not.toHaveBeenCalled();
        expect(
          elasticSearchService.getSignedElasticRequest
        ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
          searchType: "dfs_query_then_fetch"
        });
      });
    });
  });

  describe("multiLang enabled", () => {
    describe("with non-empty input.query ", () => {
      it("should not make a call to UserResourceClient.getUsersPreferredLanguage if language is provided in the input", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const mockElasticsearchResponse = {
          hits: {
            total: {
              value: faker.datatype.number()
            },
            hits: [
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              },
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              }
            ]
          }
        };

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.getSignedElasticRequest.mockResolvedValue(
          mockElasticsearchResponse
        );

        const languageDetectService = createMockInstance(LanguageDetectService);
        languageDetectService.determineSupportedLanguageWithUserPreference.mockResolvedValue(
          ENGLISH
        );

        const userResourceClient = createMockInstance(UserResourceClient);
        const usersPreferredLanguage = faker.datatype.string();
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage(usersPreferredLanguage)
        );

        const nameSearchResourceServiceRewrite = createMockInstance(
          NameSearchResourceServiceRewrite
        );

        const featureFlagsService = mockFeatureFlagsService();
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const nameSuggestResourceService = new NameSuggestResourceService(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          searchAnalyticsTracerService,
          featureFlagsService,
          nameSearchResourceServiceRewrite,
          affiliationAdapterService
        );

        const input: KolNameSuggestionInput = {
          query: [faker.lorem.word(), faker.lorem.word()],
          limit: faker.datatype.number(),
          offset: faker.datatype.number(),
          language: [JAPANESE, CHINESE, ENGLISH][faker.datatype.number(2)]
        };
        const peopleIndex = faker.datatype.string();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const multiLangEnabled = true;

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(response).toEqual({
          total: mockElasticsearchResponse.hits.total.value,
          results: [
            {
              personId: mockElasticsearchResponse.hits.hits[0]._source.id,
              name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[0]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[0]._source.designations,
              h1dnId: null,
              topL3Indications: []
            },
            {
              personId: mockElasticsearchResponse.hits.hits[1]._source.id,
              name: mockElasticsearchResponse.hits.hits[1]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[1]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[1]._source.designations,
              h1dnId: null,
              topL3Indications: []
            }
          ]
        });

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).not.toHaveBeenCalled();
        expect(
          languageDetectService.determineSupportedLanguageWithUserPreference
        ).toHaveBeenCalledWith(input.query!.join(), input.language);
        expect(
          elasticSearchService.getSignedElasticRequest
        ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
          searchType: "dfs_query_then_fetch"
        });
      });

      describe("detected language is 'eng'", () => {
        it("should use 'eng' fields when query's detected language is 'eng'", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const mockElasticsearchResponse = {
            hits: {
              total: {
                value: faker.datatype.number()
              },
              hits: [
                {
                  _source: {
                    id: faker.datatype.string(),
                    name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_eng: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_cmn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_jpn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    designations: [faker.name.suffix(), faker.name.suffix()],
                    topL3Indications: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ]
                  }
                },
                {
                  _source: {
                    id: faker.datatype.string(),
                    name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_eng: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_cmn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_jpn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    designations: [faker.name.suffix(), faker.name.suffix()],
                    topL3Indications: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ]
                  }
                }
              ]
            }
          };

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.getSignedElasticRequest.mockResolvedValue(
            mockElasticsearchResponse
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          languageDetectService.determineSupportedLanguageWithUserPreference.mockResolvedValue(
            ENGLISH
          );

          const userResourceClient = createMockInstance(UserResourceClient);
          const usersPreferredLanguage = faker.datatype.string();
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage(usersPreferredLanguage)
          );

          const nameSearchResourceServiceRewrite = createMockInstance(
            NameSearchResourceServiceRewrite
          );

          const featureFlagsService = mockFeatureFlagsService();
          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );
          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const nameSuggestResourceService = new NameSuggestResourceService(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            searchAnalyticsTracerService,
            featureFlagsService,
            nameSearchResourceServiceRewrite,
            affiliationAdapterService
          );

          const input: KolNameSuggestionInput = {
            query: [faker.lorem.word(), faker.lorem.word()],
            limit: faker.datatype.number(),
            offset: faker.datatype.number()
          };
          const peopleIndex = faker.datatype.string();
          const userId = faker.datatype.string();
          const projectId = faker.datatype.string();
          const multiLangEnabled = true;

          const response = await nameSuggestResourceService.nameSuggestSearch(
            input,
            peopleIndex,
            userId,
            projectId,
            multiLangEnabled
          );

          expect(response).toEqual({
            total: mockElasticsearchResponse.hits.total.value,
            results: [
              {
                personId: mockElasticsearchResponse.hits.hits[0]._source.id,
                name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
                specialties:
                  mockElasticsearchResponse.hits.hits[0]._source.specialty_eng,
                designations:
                  mockElasticsearchResponse.hits.hits[0]._source.designations,
                h1dnId: null,
                topL3Indications: []
              },
              {
                personId: mockElasticsearchResponse.hits.hits[1]._source.id,
                name: mockElasticsearchResponse.hits.hits[1]._source.name_eng,
                specialties:
                  mockElasticsearchResponse.hits.hits[1]._source.specialty_eng,
                designations:
                  mockElasticsearchResponse.hits.hits[1]._source.designations,
                h1dnId: null,
                topL3Indications: []
              }
            ]
          });

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(userId, projectId);
          expect(
            languageDetectService.determineSupportedLanguageWithUserPreference
          ).toHaveBeenCalledWith(input.query!.join(), usersPreferredLanguage);
          expect(
            elasticSearchService.getSignedElasticRequest
          ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
            searchType: "dfs_query_then_fetch"
          });
        });

        it("should return empty designations and specialties when not available in 'eng'", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const mockElasticsearchResponse = {
            hits: {
              total: {
                value: faker.datatype.number()
              },
              hits: [
                {
                  _source: {
                    id: faker.datatype.string(),
                    name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_cmn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_jpn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    topL3Indications: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ]
                  }
                }
              ]
            }
          };

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.getSignedElasticRequest.mockResolvedValue(
            mockElasticsearchResponse
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          languageDetectService.determineSupportedLanguageWithUserPreference.mockResolvedValue(
            ENGLISH
          );

          const userResourceClient = createMockInstance(UserResourceClient);
          const usersPreferredLanguage = faker.datatype.string();
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage(usersPreferredLanguage)
          );

          const nameSearchResourceServiceRewrite = createMockInstance(
            NameSearchResourceServiceRewrite
          );

          const featureFlagsService = mockFeatureFlagsService();
          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );
          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const nameSuggestResourceService = new NameSuggestResourceService(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            searchAnalyticsTracerService,
            featureFlagsService,
            nameSearchResourceServiceRewrite,
            affiliationAdapterService
          );

          const input: KolNameSuggestionInput = {
            query: [faker.lorem.word(), faker.lorem.word()],
            limit: faker.datatype.number(),
            offset: faker.datatype.number()
          };
          const peopleIndex = faker.datatype.string();
          const userId = faker.datatype.string();
          const projectId = faker.datatype.string();
          const multiLangEnabled = true;

          const response = await nameSuggestResourceService.nameSuggestSearch(
            input,
            peopleIndex,
            userId,
            projectId,
            multiLangEnabled
          );

          expect(response).toEqual({
            total: mockElasticsearchResponse.hits.total.value,
            results: [
              {
                personId: mockElasticsearchResponse.hits.hits[0]._source.id,
                name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
                specialties: [],
                designations: [],
                h1dnId: null,
                topL3Indications: []
              }
            ]
          });

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(userId, projectId);
          expect(
            languageDetectService.determineSupportedLanguageWithUserPreference
          ).toHaveBeenCalledWith(input.query!.join(), usersPreferredLanguage);
          expect(
            elasticSearchService.getSignedElasticRequest
          ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
            searchType: "dfs_query_then_fetch"
          });
        });
      });

      describe("detected language is 'jpn'", () => {
        it("should use 'jpn' fields when query's detected language is 'jpn'", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const mockElasticsearchResponse = {
            hits: {
              total: {
                value: faker.datatype.number()
              },
              hits: [
                {
                  _source: {
                    id: faker.datatype.string(),
                    name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_eng: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_cmn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_jpn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    designations: [faker.name.suffix(), faker.name.suffix()],
                    topL3Indications: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ]
                  }
                },
                {
                  _source: {
                    id: faker.datatype.string(),
                    name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_eng: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_cmn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_jpn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    designations: [faker.name.suffix(), faker.name.suffix()],
                    topL3Indications: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ]
                  }
                }
              ]
            }
          };

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.getSignedElasticRequest.mockResolvedValue(
            mockElasticsearchResponse
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          languageDetectService.determineSupportedLanguageWithUserPreference.mockResolvedValue(
            JAPANESE
          );

          const userResourceClient = createMockInstance(UserResourceClient);
          const usersPreferredLanguage = faker.datatype.string();
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage(usersPreferredLanguage)
          );

          const nameSearchResourceServiceRewrite = createMockInstance(
            NameSearchResourceServiceRewrite
          );

          const featureFlagsService = mockFeatureFlagsService();
          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );
          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const nameSuggestResourceService = new NameSuggestResourceService(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            searchAnalyticsTracerService,
            featureFlagsService,
            nameSearchResourceServiceRewrite,
            affiliationAdapterService
          );

          const input: KolNameSuggestionInput = {
            query: [faker.lorem.word(), faker.lorem.word()],
            limit: faker.datatype.number(),
            offset: faker.datatype.number()
          };
          const peopleIndex = faker.datatype.string();
          const userId = faker.datatype.string();
          const projectId = faker.datatype.string();
          const multiLangEnabled = true;

          const response = await nameSuggestResourceService.nameSuggestSearch(
            input,
            peopleIndex,
            userId,
            projectId,
            multiLangEnabled
          );

          expect(response).toEqual({
            total: mockElasticsearchResponse.hits.total.value,
            results: [
              {
                personId: mockElasticsearchResponse.hits.hits[0]._source.id,
                name: mockElasticsearchResponse.hits.hits[0]._source.name_jpn,
                specialties:
                  mockElasticsearchResponse.hits.hits[0]._source.specialty_jpn,
                designations:
                  mockElasticsearchResponse.hits.hits[0]._source.designations,
                h1dnId: null,
                topL3Indications: []
              },
              {
                personId: mockElasticsearchResponse.hits.hits[1]._source.id,
                name: mockElasticsearchResponse.hits.hits[1]._source.name_jpn,
                specialties:
                  mockElasticsearchResponse.hits.hits[1]._source.specialty_jpn,
                designations:
                  mockElasticsearchResponse.hits.hits[1]._source.designations,
                h1dnId: null,
                topL3Indications: []
              }
            ]
          });

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(userId, projectId);
          expect(
            languageDetectService.determineSupportedLanguageWithUserPreference
          ).toHaveBeenCalledWith(input.query!.join(), usersPreferredLanguage);
          expect(
            elasticSearchService.getSignedElasticRequest
          ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
            searchType: "dfs_query_then_fetch"
          });
        });

        it("should return empty designations and specialties when not available in 'jpn'", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const mockElasticsearchResponse = {
            hits: {
              total: {
                value: faker.datatype.number()
              },
              hits: [
                {
                  _source: {
                    id: faker.datatype.string(),
                    name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_eng: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_cmn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    topL3Indications: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ]
                  }
                }
              ]
            }
          };

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.getSignedElasticRequest.mockResolvedValue(
            mockElasticsearchResponse
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          languageDetectService.determineSupportedLanguageWithUserPreference.mockResolvedValue(
            JAPANESE
          );

          const userResourceClient = createMockInstance(UserResourceClient);
          const usersPreferredLanguage = faker.datatype.string();
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage(usersPreferredLanguage)
          );

          const nameSearchResourceServiceRewrite = createMockInstance(
            NameSearchResourceServiceRewrite
          );

          const featureFlagsService = mockFeatureFlagsService();
          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );
          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const nameSuggestResourceService = new NameSuggestResourceService(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            searchAnalyticsTracerService,
            featureFlagsService,
            nameSearchResourceServiceRewrite,
            affiliationAdapterService
          );

          const input: KolNameSuggestionInput = {
            query: [faker.lorem.word(), faker.lorem.word()],
            limit: faker.datatype.number(),
            offset: faker.datatype.number()
          };
          const peopleIndex = faker.datatype.string();
          const userId = faker.datatype.string();
          const projectId = faker.datatype.string();
          const multiLangEnabled = true;

          const response = await nameSuggestResourceService.nameSuggestSearch(
            input,
            peopleIndex,
            userId,
            projectId,
            multiLangEnabled
          );

          expect(response).toEqual({
            total: mockElasticsearchResponse.hits.total.value,
            results: [
              {
                personId: mockElasticsearchResponse.hits.hits[0]._source.id,
                name: mockElasticsearchResponse.hits.hits[0]._source.name_jpn,
                specialties: [],
                designations: [],
                h1dnId: null,
                topL3Indications: []
              }
            ]
          });

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(userId, projectId);
          expect(
            languageDetectService.determineSupportedLanguageWithUserPreference
          ).toHaveBeenCalledWith(input.query!.join(), usersPreferredLanguage);
          expect(
            elasticSearchService.getSignedElasticRequest
          ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
            searchType: "dfs_query_then_fetch"
          });
        });
      });

      describe("detected language is 'cmn'", () => {
        it("should use 'cmn' fields when query's detected language is 'cmn'", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const mockElasticsearchResponse = {
            hits: {
              total: {
                value: faker.datatype.number()
              },
              hits: [
                {
                  _source: {
                    id: faker.datatype.string(),
                    name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_eng: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_cmn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_jpn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    designations: [faker.name.suffix(), faker.name.suffix()],
                    h1dnId: null,
                    topL3Indications: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ]
                  }
                },
                {
                  _source: {
                    id: faker.datatype.string(),
                    name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_eng: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_cmn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_jpn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    designations: [faker.name.suffix(), faker.name.suffix()],
                    h1dnId: null,
                    topL3Indications: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ]
                  }
                }
              ]
            }
          };

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.getSignedElasticRequest.mockResolvedValue(
            mockElasticsearchResponse
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          languageDetectService.determineSupportedLanguageWithUserPreference.mockResolvedValue(
            "cmn"
          );

          const userResourceClient = createMockInstance(UserResourceClient);
          const usersPreferredLanguage = faker.datatype.string();
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage(usersPreferredLanguage)
          );

          const nameSearchResourceServiceRewrite = createMockInstance(
            NameSearchResourceServiceRewrite
          );

          const featureFlagsService = mockFeatureFlagsService();
          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );
          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const nameSuggestResourceService = new NameSuggestResourceService(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            searchAnalyticsTracerService,
            featureFlagsService,
            nameSearchResourceServiceRewrite,
            affiliationAdapterService
          );

          const input: KolNameSuggestionInput = {
            query: [faker.lorem.word(), faker.lorem.word()],
            limit: faker.datatype.number(),
            offset: faker.datatype.number()
          };
          const peopleIndex = faker.datatype.string();
          const userId = faker.datatype.string();
          const projectId = faker.datatype.string();
          const multiLangEnabled = true;

          const response = await nameSuggestResourceService.nameSuggestSearch(
            input,
            peopleIndex,
            userId,
            projectId,
            multiLangEnabled
          );

          expect(response).toEqual({
            total: mockElasticsearchResponse.hits.total.value,
            results: [
              {
                personId: mockElasticsearchResponse.hits.hits[0]._source.id,
                name: mockElasticsearchResponse.hits.hits[0]._source.name_cmn,
                specialties:
                  mockElasticsearchResponse.hits.hits[0]._source.specialty_cmn,
                designations:
                  mockElasticsearchResponse.hits.hits[0]._source.designations,
                h1dnId: null,
                topL3Indications: []
              },
              {
                personId: mockElasticsearchResponse.hits.hits[1]._source.id,
                name: mockElasticsearchResponse.hits.hits[1]._source.name_cmn,
                specialties:
                  mockElasticsearchResponse.hits.hits[1]._source.specialty_cmn,
                designations:
                  mockElasticsearchResponse.hits.hits[1]._source.designations,
                h1dnId: null,
                topL3Indications: []
              }
            ]
          });

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(userId, projectId);
          expect(
            languageDetectService.determineSupportedLanguageWithUserPreference
          ).toHaveBeenCalledWith(input.query!.join(), usersPreferredLanguage);
          expect(
            elasticSearchService.getSignedElasticRequest
          ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
            searchType: "dfs_query_then_fetch"
          });
        });

        it("should return empty designations and specialties when not available in 'cmn'", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const mockElasticsearchResponse = {
            hits: {
              total: {
                value: faker.datatype.number()
              },
              hits: [
                {
                  _source: {
                    id: faker.datatype.string(),
                    name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_eng: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                    specialty_jpn: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ],
                    topL3Indications: [
                      faker.name.jobTitle(),
                      faker.name.jobTitle()
                    ]
                  }
                }
              ]
            }
          };

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.getSignedElasticRequest.mockResolvedValue(
            mockElasticsearchResponse
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          languageDetectService.determineSupportedLanguageWithUserPreference.mockResolvedValue(
            "cmn"
          );

          const userResourceClient = createMockInstance(UserResourceClient);
          const usersPreferredLanguage = faker.datatype.string();
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage(usersPreferredLanguage)
          );

          const nameSearchResourceServiceRewrite = createMockInstance(
            NameSearchResourceServiceRewrite
          );

          const featureFlagsService = mockFeatureFlagsService();
          const affiliationAdapterService = createMockInstance(
            AffiliationAdapterService
          );
          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const nameSuggestResourceService = new NameSuggestResourceService(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            searchAnalyticsTracerService,
            featureFlagsService,
            nameSearchResourceServiceRewrite,
            affiliationAdapterService
          );

          const input: KolNameSuggestionInput = {
            query: [faker.lorem.word(), faker.lorem.word()],
            limit: faker.datatype.number(),
            offset: faker.datatype.number()
          };
          const peopleIndex = faker.datatype.string();
          const userId = faker.datatype.string();
          const projectId = faker.datatype.string();
          const multiLangEnabled = true;

          const response = await nameSuggestResourceService.nameSuggestSearch(
            input,
            peopleIndex,
            userId,
            projectId,
            multiLangEnabled
          );

          expect(response).toEqual({
            total: mockElasticsearchResponse.hits.total.value,
            results: [
              {
                personId: mockElasticsearchResponse.hits.hits[0]._source.id,
                name: mockElasticsearchResponse.hits.hits[0]._source.name_cmn,
                specialties: [],
                designations: [],
                h1dnId: null,
                topL3Indications: []
              }
            ]
          });

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(userId, projectId);
          expect(
            languageDetectService.determineSupportedLanguageWithUserPreference
          ).toHaveBeenCalledWith(input.query!.join(), usersPreferredLanguage);
          expect(
            elasticSearchService.getSignedElasticRequest
          ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
            searchType: "dfs_query_then_fetch"
          });
        });
      });
    });

    // these tests are skipped because, even though there are conditionals that handle the case, an empty
    // input.query array causes the query builders to throw exceptions.
    // TODO: figure out if an empty query array is even applicable to name suggest queries and fix accordingly
    describe.skip("with empty input.query ", () => {
      it("should use 'eng' fields when user's preferred language is 'eng' and not call languageDetectService", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const mockElasticsearchResponse = {
          hits: {
            total: {
              value: faker.datatype.number()
            },
            hits: [
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              },
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              }
            ]
          }
        };

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.getSignedElasticRequest.mockResolvedValue(
          mockElasticsearchResponse
        );

        const languageDetectService = createMockInstance(LanguageDetectService);
        languageDetectService.determineSupportedLanguageWithUserPreference.mockRejectedValue(
          new Error(
            "languageDetectService.determineSupportedLanguageWithUserPreference should not have been called"
          )
        );

        const userResourceClient = createMockInstance(UserResourceClient);
        const usersPreferredLanguage = faker.datatype.string();
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage(usersPreferredLanguage)
        );

        const nameSearchResourceServiceRewrite = createMockInstance(
          NameSearchResourceServiceRewrite
        );

        const featureFlagsService = mockFeatureFlagsService();
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const nameSuggestResourceService = new NameSuggestResourceService(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          searchAnalyticsTracerService,
          featureFlagsService,
          nameSearchResourceServiceRewrite,
          affiliationAdapterService
        );

        const input: KolNameSuggestionInput = {
          query: [],
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const peopleIndex = faker.datatype.string();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const multiLangEnabled = true;

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(response).toEqual({
          total: mockElasticsearchResponse.hits.total.value,
          results: [
            {
              personId: mockElasticsearchResponse.hits.hits[0]._source.id,
              name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[0]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[0]._source.designations,
              topL3Indications: []
            },
            {
              personId: mockElasticsearchResponse.hits.hits[1]._source.id,
              name: mockElasticsearchResponse.hits.hits[1]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[1]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[1]._source.designations,
              topL3Indications: []
            }
          ]
        });

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(userId, projectId);
        expect(
          languageDetectService.determineSupportedLanguageWithUserPreference
        ).not.toHaveBeenCalled();
        expect(
          elasticSearchService.getSignedElasticRequest
        ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
          searchType: "dfs_query_then_fetch"
        });
      });

      it("should use 'cmn' fields when query's detected language is 'cmn'", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const mockElasticsearchResponse = {
          hits: {
            total: {
              value: faker.datatype.number()
            },
            hits: [
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              },
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              }
            ]
          }
        };

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.getSignedElasticRequest.mockResolvedValue(
          mockElasticsearchResponse
        );

        const languageDetectService = createMockInstance(LanguageDetectService);
        languageDetectService.determineSupportedLanguageWithUserPreference.mockRejectedValue(
          new Error(
            "languageDetectService.determineSupportedLanguageWithUserPreference should not have been called"
          )
        );

        const userResourceClient = createMockInstance(UserResourceClient);
        const usersPreferredLanguage = faker.datatype.string();
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage(usersPreferredLanguage)
        );

        const nameSearchResourceServiceRewrite = createMockInstance(
          NameSearchResourceServiceRewrite
        );

        const featureFlagsService = mockFeatureFlagsService();
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const nameSuggestResourceService = new NameSuggestResourceService(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          searchAnalyticsTracerService,
          featureFlagsService,
          nameSearchResourceServiceRewrite,
          affiliationAdapterService
        );

        const input: KolNameSuggestionInput = {
          query: [],
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const peopleIndex = faker.datatype.string();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const multiLangEnabled = true;

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(response).toEqual({
          total: mockElasticsearchResponse.hits.total.value,
          results: [
            {
              personId: mockElasticsearchResponse.hits.hits[0]._source.id,
              name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[0]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[0]._source.designations,
              topL3Indications: []
            },
            {
              personId: mockElasticsearchResponse.hits.hits[1]._source.id,
              name: mockElasticsearchResponse.hits.hits[1]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[1]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[1]._source.designations,
              topL3Indications: []
            }
          ]
        });

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(userId, projectId);
        expect(
          languageDetectService.determineSupportedLanguageWithUserPreference
        ).not.toHaveBeenCalled();
        expect(
          elasticSearchService.getSignedElasticRequest
        ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
          searchType: "dfs_query_then_fetch"
        });
      });

      it("should use 'jpn' fields when query's detected language is 'jpn'", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const mockElasticsearchResponse = {
          hits: {
            total: {
              value: faker.datatype.number()
            },
            hits: [
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              },
              {
                _source: {
                  id: faker.datatype.string(),
                  name_eng: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_eng: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_cmn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_cmn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  name_jpn: `${faker.name.lastName()} ${faker.name.lastName()}`,
                  specialty_jpn: [faker.name.jobTitle(), faker.name.jobTitle()],
                  designations: [faker.name.suffix(), faker.name.suffix()],
                  topL3Indications: [
                    faker.name.jobTitle(),
                    faker.name.jobTitle()
                  ]
                }
              }
            ]
          }
        };

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.getSignedElasticRequest.mockResolvedValue(
          mockElasticsearchResponse
        );

        const languageDetectService = createMockInstance(LanguageDetectService);
        languageDetectService.determineSupportedLanguageWithUserPreference.mockRejectedValue(
          new Error(
            "languageDetectService.determineSupportedLanguageWithUserPreference should not have been called"
          )
        );

        const userResourceClient = createMockInstance(UserResourceClient);
        const usersPreferredLanguage = faker.datatype.string();
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage(usersPreferredLanguage)
        );

        const nameSearchResourceServiceRewrite = createMockInstance(
          NameSearchResourceServiceRewrite
        );

        const featureFlagsService = mockFeatureFlagsService();
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const nameSuggestResourceService = new NameSuggestResourceService(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          searchAnalyticsTracerService,
          featureFlagsService,
          nameSearchResourceServiceRewrite,
          affiliationAdapterService
        );

        const input: KolNameSuggestionInput = {
          query: [],
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const peopleIndex = faker.datatype.string();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const multiLangEnabled = true;

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(response).toEqual({
          total: mockElasticsearchResponse.hits.total.value,
          results: [
            {
              personId: mockElasticsearchResponse.hits.hits[0]._source.id,
              name: mockElasticsearchResponse.hits.hits[0]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[0]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[0]._source.designations,
              topL3Indications: []
            },
            {
              personId: mockElasticsearchResponse.hits.hits[1]._source.id,
              name: mockElasticsearchResponse.hits.hits[1]._source.name_eng,
              specialties:
                mockElasticsearchResponse.hits.hits[1]._source.specialty_eng,
              designations:
                mockElasticsearchResponse.hits.hits[1]._source.designations,
              topL3Indications: []
            }
          ]
        });

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(userId, projectId);
        expect(
          languageDetectService.determineSupportedLanguageWithUserPreference
        ).not.toHaveBeenCalled();
        expect(
          elasticSearchService.getSignedElasticRequest
        ).toHaveBeenCalledWith(expect.any(Object), peopleIndex, {
          searchType: "dfs_query_then_fetch"
        });
      });
    });
  });

  describe("Name Search fall back", () => {
    it("it should call name search when there are no results for Name suggest request and enableNameSearchFallbackForNameSuggest feature flag in enabled", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const mockElasticsearchResponse = {
        hits: {
          total: {
            value: 0
          },
          hits: []
        }
      };

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.getSignedElasticRequest.mockResolvedValue(
        mockElasticsearchResponse
      );

      const languageDetectService = createMockInstance(LanguageDetectService);
      languageDetectService.determineSupportedLanguageWithUserPreference.mockResolvedValue(
        ENGLISH
      );

      const userResourceClient = createMockInstance(UserResourceClient);
      const usersPreferredLanguage = faker.datatype.string();
      userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
        generateMockLanguage(usersPreferredLanguage)
      );

      const nameSearchResourceServiceRewrite = createMockInstance(
        NameSearchResourceServiceRewrite
      );

      const featureFlagsService = mockFeatureFlagsService({
        enableNameSearchFallbackForNameSuggest: true
      });
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const nameSuggestResourceService = new NameSuggestResourceService(
        configService,
        elasticSearchService,
        languageDetectService,
        userResourceClient,
        searchAnalyticsTracerService,
        featureFlagsService,
        nameSearchResourceServiceRewrite,
        affiliationAdapterService
      );

      const input: KolNameSuggestionInput = {
        query: [faker.lorem.word(), faker.lorem.word()],
        limit: faker.datatype.number(),
        offset: faker.datatype.number()
      };
      const peopleIndex = faker.datatype.string();
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const multiLangEnabled = true;

      const nameSearchInput = getNameSearchInput(
        input,
        projectId,
        ENGLISH,
        multiLangEnabled,
        userId
      );
      nameSearchResourceServiceRewrite.runNameSearchRewrite.mockResolvedValue(
        generateMockPersonSearchResponse(nameSearchInput)
      );

      await nameSuggestResourceService.nameSuggestSearch(
        input,
        peopleIndex,
        userId,
        projectId,
        multiLangEnabled
      );

      expect(
        nameSearchResourceServiceRewrite.runNameSearchRewrite
      ).toHaveBeenCalledWith(nameSearchInput);

      expect(userResourceClient.getUsersPreferredLanguage).toHaveBeenCalledWith(
        userId,
        projectId
      );
      expect(
        languageDetectService.determineSupportedLanguageWithUserPreference
      ).toHaveBeenCalledWith(input.query!.join(), usersPreferredLanguage);
      expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalledWith(
        expect.any(Object),
        peopleIndex,
        {
          searchType: "dfs_query_then_fetch"
        }
      );
    });
  });

  describe("Advanced operators", () => {
    it("should return no suggestions when query contains advanced operators", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.getSignedElasticRequest.mockRejectedValue(
        new Error("elasticSearchService.getSignedElasticRequest threw an error")
      );

      const languageDetectService = createMockInstance(LanguageDetectService);
      languageDetectService.determineSupportedLanguageWithUserPreference.mockRejectedValue(
        new Error(
          "languageDetectService.determineSupportedLanguageWithUserPreference threw an error"
        )
      );

      const userResourceClient = createMockInstance(UserResourceClient);
      userResourceClient.getUsersPreferredLanguage.mockRejectedValue(
        new Error("userResourceClient.getUsersPreferredLanguage threw an error")
      );

      const nameSearchResourceServiceRewrite = createMockInstance(
        NameSearchResourceServiceRewrite
      );

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const featureFlagsService = mockFeatureFlagsService();
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const nameSuggestResourceService = new NameSuggestResourceService(
        configService,
        elasticSearchService,
        languageDetectService,
        userResourceClient,
        searchAnalyticsTracerService,
        featureFlagsService,
        nameSearchResourceServiceRewrite,
        affiliationAdapterService
      );

      const peopleIndex = faker.datatype.string();
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const multiLangEnabled = true;

      const queries = [
        `${faker.datatype.string()} AND ${faker.datatype.string()}`,
        `${faker.datatype.string()} OR ${faker.datatype.string()}`,
        `NOT ${faker.datatype.string()}`
      ];

      queries.forEach(async (query) => {
        const input: KolNameSuggestionInput = {
          query: [query],
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };

        const nameSearchInput = getNameSearchInput(
          input,
          projectId,
          ENGLISH,
          multiLangEnabled,
          userId
        );
        nameSearchResourceServiceRewrite.runNameSearchRewrite.mockResolvedValue(
          generateMockPersonSearchResponse(nameSearchInput)
        );

        const response = await nameSuggestResourceService.nameSuggestSearch(
          input,
          peopleIndex,
          userId,
          projectId,
          multiLangEnabled
        );

        expect(
          nameSearchResourceServiceRewrite.runNameSearchRewrite
        ).not.toHaveBeenCalled();

        expect(response).toEqual({
          total: 0,
          results: []
        });
      });
    });
  });

  describe("Limit tokens", () => {
    it("should limit number of query tokens to 10", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const mockElasticsearchResponse = {
        hits: {
          total: {
            value: faker.datatype.number()
          },
          hits: []
        }
      };

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.getSignedElasticRequest.mockResolvedValue(
        mockElasticsearchResponse
      );

      const languageDetectService = createMockInstance(LanguageDetectService);

      const userResourceClient = createMockInstance(UserResourceClient);
      userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
        generateMockLanguage(faker.datatype.string())
      );
      const nameSearchResourceServiceRewrite = createMockInstance(
        NameSearchResourceServiceRewrite
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSuggestResourceService = new NameSuggestResourceService(
        configService,
        elasticSearchService,
        languageDetectService,
        userResourceClient,
        searchAnalyticsTracerService,
        featureFlagsService,
        nameSearchResourceServiceRewrite,
        affiliationAdapterService
      );

      const tokens = [
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word()
      ];

      const input: KolNameSuggestionInput = {
        query: [tokens.join("  \t")],
        limit: faker.datatype.number(),
        offset: faker.datatype.number()
      };
      const peopleIndex = faker.datatype.string();
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const multiLangEnabled = false;

      const featureFlags =
        getNameSuggestFeatureFlags() as NameSuggestFeatureFlags;

      const buildNameSuggestQuery = getNameSuggestBuilder("eng");
      const expectedBody = await buildNameSuggestQuery({
        input: { ...input, query: [tokens.slice(0, 10).join(" ")] },
        projectId,
        featureFlags
      });
      await nameSuggestResourceService.nameSuggestSearch(
        input,
        peopleIndex,
        userId,
        projectId,
        multiLangEnabled
      );

      expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalledWith(
        expectedBody,
        peopleIndex,
        {
          searchType: "dfs_query_then_fetch"
        }
      );
    });
  });

  describe("Sanitize query", () => {
    it("should replace acute accent with single quote", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const mockElasticsearchResponse = {
        hits: {
          total: {
            value: faker.datatype.number()
          },
          hits: []
        }
      };

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.getSignedElasticRequest.mockResolvedValue(
        mockElasticsearchResponse
      );

      const languageDetectService = createMockInstance(LanguageDetectService);

      const userResourceClient = createMockInstance(UserResourceClient);
      userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
        generateMockLanguage(faker.datatype.string())
      );
      const nameSearchResourceServiceRewrite = createMockInstance(
        NameSearchResourceServiceRewrite
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const featureFlagsService = mockFeatureFlagsService();
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSuggestResourceService = new NameSuggestResourceService(
        configService,
        elasticSearchService,
        languageDetectService,
        userResourceClient,
        searchAnalyticsTracerService,
        featureFlagsService,
        nameSearchResourceServiceRewrite,
        affiliationAdapterService
      );

      const tokens = [
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word()
      ];

      const input: KolNameSuggestionInput = {
        query: [tokens.join("´")],
        limit: faker.datatype.number(),
        offset: faker.datatype.number()
      };
      const peopleIndex = faker.datatype.string();
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const multiLangEnabled = false;

      const featureFlags =
        getNameSuggestFeatureFlags() as NameSuggestFeatureFlags;

      const buildNameSuggestQuery = getNameSuggestBuilder("eng");
      const expectedBody = await buildNameSuggestQuery({
        input: { ...input, query: [tokens.join("'")] },
        projectId,
        featureFlags
      });
      await nameSuggestResourceService.nameSuggestSearch(
        input,
        peopleIndex,
        userId,
        projectId,
        multiLangEnabled
      );

      expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalledWith(
        expectedBody,
        peopleIndex,
        {
          searchType: "dfs_query_then_fetch"
        }
      );
    });
  });
});
