import { createMockInstance } from "../util/TestUtils";
import { QueryUnderstandingResourceService } from "./QueryUnderstandingResourceService";
import { QueryParserService } from "./QueryParserService";
import { ConfigService } from "./ConfigService";
import { faker } from "@faker-js/faker";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { SearchTypes } from "@h1nyc/search-sdk";
import { QueryIntent } from "../proto/query_intent_pb";
import { UserResourceClient } from "@h1nyc/account-sdk";
import { CHINESE, ENGLISH, JAPANESE } from "./LanguageDetectService";

describe("getKeywordSynonyms", () => {
  it("calls queryParserService as expected", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );

    const expectedSynonyms: string[] = [
      faker.datatype.string(),
      faker.datatype.string()
    ];
    const keyword = faker.datatype.string();

    jest
      .spyOn(queryParserService, "getKeywordSynonyms")
      .mockResolvedValueOnce(expectedSynonyms);

    const actualSynonyms = await queryUnderstandingService.getKeywordSynonyms(
      keyword
    );

    expect(queryParserService.getKeywordSynonyms).toHaveBeenCalledTimes(1);
    expect(actualSynonyms).toEqual(expectedSynonyms);
  });
});

describe("getSearchType", () => {
  it("search type with empty query intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );

    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();
    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );

    expect(actualSearchType).toEqual([SearchTypes.NAME, SearchTypes.KEYWORD]);
  });

  it("search type with person intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([QueryIntent.IntentType.PERSON_NAME])
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.NAME]);
  });

  it("search type with specialist intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([QueryIntent.IntentType.SPECIALIST])
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
  });

  it("search type with disease intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([QueryIntent.IntentType.DISEASE])
    );
    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
  });

  it("search type with person and disease intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([
        QueryIntent.IntentType.DISEASE,
        QueryIntent.IntentType.PERSON_NAME
      ])
    );
    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.NAME, SearchTypes.KEYWORD]);
  });

  it("search type with institution intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([QueryIntent.IntentType.INSTITUTION])
    );
    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.INSTITUTION]);
  });

  it("search type with conference org intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([QueryIntent.IntentType.CONFERENCE_ORG])
    );
    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
  });

  it("search type with conference name intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([QueryIntent.IntentType.CONFERENCE_NAME])
    );
    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
  });

  it("search type with no intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);
    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.NAME, SearchTypes.KEYWORD]);
  });

  it("should return keyword intent for advanced search queries", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const err = new Error(
      "queryUnderstandingServiceClient.analyze threw an error"
    );
    queryUnderstandingServiceClient.analyze.mockRejectedValue(err);

    const queries = [
      `${faker.datatype.string()} AND ${faker.datatype.string()}`,
      `${faker.datatype.string()} OR ${faker.datatype.string()}`,
      `NOT ${faker.datatype.string()}`
    ];

    queries.forEach(async (query) => {
      const actualSearchType = await queryUnderstandingService.getSearchTypes(
        query,
        faker.datatype.string()
      );
      expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
    });
  });

  it("should call getUsersPreferredLanguage will passed userId and projectId", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);
    userClient.getUsersPreferredLanguage.mockResolvedValueOnce("eng" as any);
    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([QueryIntent.IntentType.PERSON_NAME])
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const userId = faker.datatype.string();
    const projectId = faker.datatype.string();
    await queryUnderstandingService.getSearchTypes(query, projectId, userId);
    expect(userClient.getUsersPreferredLanguage).toHaveBeenCalledWith(
      userId,
      projectId
    );
  });

  it("should NOT call getUsersPreferredLanguage if language is passed", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);
    userClient.getUsersPreferredLanguage.mockResolvedValueOnce("eng" as any);
    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([QueryIntent.IntentType.PERSON_NAME])
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const userId = faker.datatype.string();
    const projectId = faker.datatype.string();
    const language = [ENGLISH, CHINESE, JAPANESE][faker.datatype.number(2)];
    await queryUnderstandingService.getSearchTypes(
      query,
      projectId,
      userId,
      language
    );
    expect(userClient.getUsersPreferredLanguage).not.toHaveBeenCalled();
  });

  it("search type with LLM intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([QueryIntent.IntentType.LLM_INTENT])
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
  });

  it("search type with LLM intent and person intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([
        QueryIntent.IntentType.LLM_INTENT,
        QueryIntent.IntentType.PERSON_NAME
      ])
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
  });

  it("search type with LLM intent and disease intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([
        QueryIntent.IntentType.LLM_INTENT,
        QueryIntent.IntentType.DISEASE
      ])
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
  });

  it("search type with LLM intent, person intent, and disease intent", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([
        QueryIntent.IntentType.LLM_INTENT,
        QueryIntent.IntentType.PERSON_NAME,
        QueryIntent.IntentType.DISEASE
      ])
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
  });

  it("search type with LLM intent and multiple other intents", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    queryUnderstandingServiceResponse.setQueryIntent(
      constructQueryIntent([
        QueryIntent.IntentType.LLM_INTENT,
        QueryIntent.IntentType.INSTITUTION,
        QueryIntent.IntentType.SPECIALIST
      ])
    );

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
  });

  it("search type with LLM intent having low score should not be included", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    // Create LLM intent with low score
    const queryIntent = new QueryIntent();
    const llmIntent = new QueryIntent.Intent();
    llmIntent.setIntentType(QueryIntent.IntentType.LLM_INTENT);
    llmIntent.setScore(0.5); // Low score, should not be included
    queryIntent.setLlmIntent(llmIntent);

    queryUnderstandingServiceResponse.setQueryIntent(queryIntent);

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    // Should return default search types since LLM intent score is too low
    expect(actualSearchType).toEqual([SearchTypes.NAME, SearchTypes.KEYWORD]);
  });

  it("search type with LLM intent having exactly 1.0 score should be included", async () => {
    const configService = createMockInstance(ConfigService);
    const queryParserService = createMockInstance(QueryParserService);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    const userClient = createMockInstance(UserResourceClient);

    const queryUnderstandingService = new QueryUnderstandingResourceService(
      configService,
      queryParserService,
      queryUnderstandingServiceClient,
      userClient
    );
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();

    // Create LLM intent with exact 1.0 score
    const queryIntent = new QueryIntent();
    const llmIntent = new QueryIntent.Intent();
    llmIntent.setIntentType(QueryIntent.IntentType.LLM_INTENT);
    llmIntent.setScore(1.0); // Exact score requirement
    queryIntent.setLlmIntent(llmIntent);

    queryUnderstandingServiceResponse.setQueryIntent(queryIntent);

    queryUnderstandingServiceClient.analyze.mockResolvedValue(
      queryUnderstandingServiceResponse
    );

    const query = faker.datatype.string();
    const actualSearchType = await queryUnderstandingService.getSearchTypes(
      query,
      faker.datatype.string()
    );
    expect(actualSearchType).toEqual([SearchTypes.KEYWORD]);
  });
});

function constructQueryIntent(
  intentTypes: QueryIntent.IntentType[]
): QueryIntent {
  const queryIntent = new QueryIntent();

  if (intentTypes.includes(QueryIntent.IntentType.PERSON_NAME)) {
    const intent = new QueryIntent.Intent();
    intent.setIntentType(QueryIntent.IntentType.PERSON_NAME);
    intent.setScore(1.0);
    queryIntent.setPersonNameIntent(intent);
  }

  if (intentTypes.includes(QueryIntent.IntentType.DISEASE)) {
    const intent = new QueryIntent.Intent();
    intent.setIntentType(QueryIntent.IntentType.DISEASE);
    intent.setScore(1.0);
    queryIntent.setDiseaseIntent(intent);
  }

  if (intentTypes.includes(QueryIntent.IntentType.INSTITUTION)) {
    const intent = new QueryIntent.Intent();
    intent.setIntentType(QueryIntent.IntentType.INSTITUTION);
    intent.setScore(1.0);
    queryIntent.setInstitutionIntent(intent);
  }

  if (intentTypes.includes(QueryIntent.IntentType.CONFERENCE_NAME)) {
    const intent = new QueryIntent.Intent();
    intent.setIntentType(QueryIntent.IntentType.CONFERENCE_NAME);
    intent.setScore(1.0);
    queryIntent.setConferenceIntent(intent);
  }

  if (intentTypes.includes(QueryIntent.IntentType.CONFERENCE_ORG)) {
    const intent = new QueryIntent.Intent();
    intent.setIntentType(QueryIntent.IntentType.CONFERENCE_ORG);
    intent.setScore(1.0);
    queryIntent.setConferenceIntent(intent);
  }

  if (intentTypes.includes(QueryIntent.IntentType.SPECIALIST)) {
    const intent = new QueryIntent.Intent();
    intent.setIntentType(QueryIntent.IntentType.SPECIALIST);
    intent.setScore(1.0);
    queryIntent.setSpecialistIntent(intent);
  }

  if (intentTypes.includes(QueryIntent.IntentType.LLM_INTENT)) {
    const intent = new QueryIntent.Intent();
    intent.setIntentType(QueryIntent.IntentType.LLM_INTENT);
    intent.setScore(1.0);
    queryIntent.setLlmIntent(intent);
  }

  return queryIntent;
}
