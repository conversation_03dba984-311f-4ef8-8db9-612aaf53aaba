import { Service } from "typedi";
import { ConfigService } from "./ConfigService";
import { ElasticCommonSearchService } from "./ElasticCommonSearchService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";

@Service()
export class ElasticSearchCompletionService extends ElasticCommonSearchService {
  constructor(config: ConfigService, featureFlagService: FeatureFlagsService) {
    super(
      {
        ...config.elasticsearchClientOptions,
        requestTimeout: config.elasticCompletionSuggesterRequestTimeout
      },
      {
        ...config.elasticsearchClientOptionsV2,
        requestTimeout: config.elasticCompletionSuggesterRequestTimeout
      },
      featureFlagService,
      config.elasticCompletionSuggesterIndex
    );
  }
}
