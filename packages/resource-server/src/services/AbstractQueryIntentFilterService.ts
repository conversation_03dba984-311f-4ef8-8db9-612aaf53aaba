import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { KeywordSearchInput, QueryIntentFilterValues } from "@h1nyc/search-sdk";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";

export interface QueryIntentFilterResponse {
  suggestedFilterValues?: QueryIntentFilterValues;
}

export interface AbstractQueryUnderstandingFilterService {
  getQueryIntentFilterResponse(
    queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse,
    input: Readonly<KeywordSearchInput>,
    filtersWithoutParsedQuery: QueryDslQueryContainer[]
  ): Promise<QueryIntentFilterResponse>;
}
