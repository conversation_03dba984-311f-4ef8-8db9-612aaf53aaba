import {
  QueryDslQueryContainer,
  AggregationsAggregationContainer,
  SearchRequest,
  AggregationsTermsAggregateBase
} from "@elastic/elasticsearch/lib/api/types";
import { KeywordSearchInput, QueryIntentFilterValues } from "@h1nyc/search-sdk";
import { min, slice } from "lodash";
import { Logger } from "../lib/Logger";
import { Service } from "typedi";
import { createLogger } from "../lib/Logger";
import { QueryIntent } from "../proto/query_intent_pb";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import {
  AbstractQueryUnderstandingFilterService,
  QueryIntentFilterResponse
} from "./AbstractQueryIntentFilterService";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import { DocCountBucket } from "./KeywordAutocompleteResponseAdapterService";
import {
  ALL_ORs,
  OR,
  ALL_ANDs,
  ALL_UNICODE_DOUBLE_QUOTES,
  ASCII_DOUBLE_QUOTES,
  EMPTY_STRING
} from "./KeywordSearchResourceServiceRewrite";
import { ALL_FLAGS_EXCEPT_WHITESPACE } from "./ParsedQueryTreeToElasticsearchQueries";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";

type NestedFilteredOrgAndConfAggregations = Record<
  "nested",
  Record<
    "filtered_matching",
    {
      org_name: {
        buckets: Array<
          {
            range: {
              buckets: Array<{
                key: "upcoming" | "recent";
                conf_name: AggregationsTermsAggregateBase<DocCountBucket>;
              }>;
            };
          } & DocCountBucket
        >;
      };
    }
  >
>;

interface CongressAggregationResponse {
  conferenceNames: string[];
  conferenceOrgNames: string[];
}

type CongressSearchPath = "org" | "name";

const TIME_LIMIT_FOR_UPCOMING_CONGRESS = "10w/d"; // 10 weeks rounded to nearest day
const TIME_LIMIT_FOR_RECENT_CONGRESS = "3y/M"; // 3 years rounded to nearest month
export const CONGRESS_INTENT_SCORE_THRESHOLD = 0.65;
@Service()
export class ConferenceQueryIntentFilterService
  implements AbstractQueryUnderstandingFilterService
{
  private logger: Logger;
  constructor(
    private configService: ConfigService,
    private elasticService: ElasticSearchService,
    private searchAnalyticsTracerService: SearchAnalyticsTracerService
  ) {
    this.logger = createLogger("ConferenceQueryIntentFilterService", "info");
    this.logger.debug("Instantiated ConferenceQueryIntentFilterService");
  }

  private getCongressQueryFilterFromPath(
    path: CongressSearchPath,
    query: string
  ): QueryDslQueryContainer[] {
    const filters: Array<QueryDslQueryContainer> = [];

    if (path === "org") {
      filters.push({
        nested: {
          path: "congress",
          query: {
            bool: {
              filter: [
                {
                  simple_query_string: {
                    query,
                    default_operator: "AND",
                    fields: ["congress.organizer_eng.search"],
                    flags: ALL_FLAGS_EXCEPT_WHITESPACE
                  }
                }
              ]
            }
          }
        }
      });
    } else if (path === "name") {
      filters.push({
        nested: {
          path: "congress",
          query: {
            bool: {
              filter: [
                {
                  simple_query_string: {
                    query,
                    default_operator: "AND",
                    fields: ["congress.name_eng.search"],
                    flags: ALL_FLAGS_EXCEPT_WHITESPACE
                  }
                }
              ]
            }
          }
        }
      });
    }

    return filters;
  }

  private getNamedAggregationForLatestConferenceName(
    filter: QueryDslQueryContainer,
    input: Readonly<KeywordSearchInput>
  ): Record<string, AggregationsAggregationContainer> {
    return {
      nested: {
        nested: {
          path: "congress"
        },
        aggs: {
          filtered_matching: {
            filter: {
              bool: {
                filter: [
                  ...(filter.nested?.query?.bool
                    ?.filter as QueryDslQueryContainer[])
                ]
              }
            },
            aggs: {
              org_name: {
                terms: {
                  field: "congress.organizer_eng",
                  size: 1,
                  exclude: input.suppliedFilters.congresses.organizerName.values
                },
                aggs: {
                  range: {
                    date_range: {
                      field: "congress.endDate",
                      format: "yyyy-MM-dd",
                      ranges: [
                        {
                          from: "now/d", // Current time rounded to nearest day
                          to: `now+${TIME_LIMIT_FOR_UPCOMING_CONGRESS}`,
                          key: "upcoming"
                        },
                        {
                          to: "now-1d/d", // Current time - 1 day rounded to nearest day
                          from: `now-${TIME_LIMIT_FOR_RECENT_CONGRESS}`,
                          key: "recent"
                        }
                      ]
                    },
                    aggs: {
                      conf_name: {
                        terms: {
                          field: "congress.name_eng",
                          size: 2,
                          order: {
                            date: "desc"
                          },
                          exclude: input.suppliedFilters.congresses.name.values
                        },
                        aggs: {
                          date: {
                            min: {
                              field: "congress.endDate"
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    };
  }

  private async getRecentConferenceNameAndOrg(
    path: CongressSearchPath,
    query: string,
    input: Readonly<KeywordSearchInput>,
    otherFilters: QueryDslQueryContainer[],
    score: number
  ): Promise<CongressAggregationResponse> {
    const filters = this.getCongressQueryFilterFromPath(path, query);

    if (filters.length) {
      const aggs = this.getNamedAggregationForLatestConferenceName(
        filters[0],
        input
      );

      const request: SearchRequest = {
        index: this.configService.elasticPeopleIndex,
        _source_includes: [],
        size: 0,
        query: {
          bool: {
            filter: [...filters, ...otherFilters]
          }
        },
        aggs
      };

      const { aggregations } = await this.elasticService.query(request);

      const orgBuckets = (aggregations as NestedFilteredOrgAndConfAggregations)
        .nested.filtered_matching.org_name.buckets;

      if (orgBuckets.length !== 0) {
        const confNameBuckets = orgBuckets[0].range.buckets;

        const upcomingConferenceNames = (
          confNameBuckets[1].conf_name.buckets as DocCountBucket[]
        ).map((bucket) => bucket.key);
        const recentConferenceNames = (
          confNameBuckets[0].conf_name.buckets as DocCountBucket[]
        ).map((bucket) => bucket.key);

        const conferenceNames = slice(
          [...upcomingConferenceNames, ...recentConferenceNames],
          0,
          2
        );

        const conferenceOrgNames = [orgBuckets[0].key];

        this.searchAnalyticsTracerService.sendAnalyticsEvent({
          event: "detected.congress_intent",
          properties: {
            query: input.query,
            score,
            intentType: path,
            upcomingCongressCount: min([2, upcomingConferenceNames.length]),
            totalCongressCount: conferenceNames.length,
            projectId: input.projectId
          },
          timestamp: new Date(),
          userId: input.userId
        });

        this.logger.info({ request }, "congress intent aggregation request");

        return { conferenceNames, conferenceOrgNames };
      }
    }

    this.searchAnalyticsTracerService.sendAnalyticsEvent({
      event: "detected.congress_intent",
      properties: {
        query: input.query,
        score,
        intentType: path,
        upcomingCongressCount: 0,
        totalCongressCount: 0,
        projectId: input.projectId
      },
      timestamp: new Date(),
      userId: input.userId
    });

    return { conferenceNames: [], conferenceOrgNames: [] };
  }

  private buildSuggestedFiltersFromResponse(
    conferences: CongressAggregationResponse
  ): QueryIntentFilterValues {
    const suggestedFilterValues: QueryIntentFilterValues = {
      congresses: {
        name: [],
        organizerName: []
      }
    };

    const conferenceOrgNames = conferences.conferenceOrgNames;
    const conferenceNames = conferences.conferenceNames;

    if (conferenceNames.length) {
      suggestedFilterValues.congresses?.name?.push(...conferenceNames);
    }

    if (conferenceOrgNames.length) {
      suggestedFilterValues.congresses?.organizerName?.push(
        conferenceOrgNames[0]
      );
    }

    return suggestedFilterValues;
  }

  async getQueryIntentFilterResponse(
    queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse,
    input: Readonly<KeywordSearchInput>,
    filtersWithoutParsedQuery: QueryDslQueryContainer[]
  ): Promise<QueryIntentFilterResponse> {
    const isCongressTypeFilterApplied =
      input.suppliedFilters.congresses.organizerName.values.length != 0 ||
      input.suppliedFilters.congresses.name.values.length != 0;

    let suggestedFilterValues: QueryIntentFilterValues = {
      congresses: {
        name: [],
        organizerName: []
      }
    };

    if (isCongressTypeFilterApplied) {
      filtersWithoutParsedQuery = filtersWithoutParsedQuery.filter(
        (filter) =>
          filter.nested?.path !== "congress" &&
          filter.function_score?.query?.nested?.path !== "congress"
      );
    }

    const query =
      queryUnderstandingServiceResponse
        .getQueryIntent()
        ?.getConferenceIntent()
        ?.getSynonyms()
        .replace(ALL_ORs, OR)
        .replace(ALL_ANDs, EMPTY_STRING)
        .replace(ALL_UNICODE_DOUBLE_QUOTES, ASCII_DOUBLE_QUOTES) ||
      input.query!;
    const score = queryUnderstandingServiceResponse
      ?.getQueryIntent()
      ?.getConferenceIntent()
      ?.getScore();
    if (
      hasGoodConfidenceForConferenceIntent(queryUnderstandingServiceResponse)
    ) {
      const conferenceIntentType = queryUnderstandingServiceResponse
        .getQueryIntent()
        ?.getConferenceIntent()
        ?.getIntentType();

      let path: CongressSearchPath;

      switch (conferenceIntentType) {
        case QueryIntent.IntentType.CONFERENCE_ORG:
          path = "org";
          break;
        case QueryIntent.IntentType.CONFERENCE_NAME:
          path = "name";
          break;
        default:
          throw new Error(
            "Unsupported conference type: " + conferenceIntentType
          );
          break;
      }

      const conferencesResponse = await this.getRecentConferenceNameAndOrg(
        path,
        query,
        input,
        filtersWithoutParsedQuery,
        score!
      );

      suggestedFilterValues =
        this.buildSuggestedFiltersFromResponse(conferencesResponse);
    } else {
      this.searchAnalyticsTracerService.sendAnalyticsEvent({
        event: "not_detected.congress_intent",
        properties: {
          query,
          score,
          projectId: input.projectId
        },
        timestamp: new Date(),
        userId: input.userId
      });
    }

    return {
      suggestedFilterValues
    };
  }
}

export function hasGoodConfidenceForConferenceIntent(
  queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse
): boolean {
  const score = queryUnderstandingServiceResponse
    ?.getQueryIntent()
    ?.getConferenceIntent()
    ?.getScore();
  if (score && score >= CONGRESS_INTENT_SCORE_THRESHOLD && score <= 1.0) {
    return true;
  }

  return false;
}
