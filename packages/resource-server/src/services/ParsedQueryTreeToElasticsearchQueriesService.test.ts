import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { faker } from "@faker-js/faker";
import { ITree } from "twitter-search-query-parser";
import { LanguageDetector } from "./KeywordSearchResourceServiceRewrite";
import { CHINESE, ENGLISH, JAPANESE, Language } from "./LanguageDetectService";

const parserService = new ParsedQueryTreeToElasticsearchQueriesService();

function randomLanguage(): Language {
  return faker.helpers.arrayElement([CHINESE, JAPANESE, ENGLISH]);
}

describe("ParsedQueryTreeToElasticsearchQueriesService", () => {
  describe("default '_eng' languageDetector", () => {
    it("should return a simple_query_string on string input", () => {
      const input = faker.random.word();
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(input, fields);

      expect(parsed).toEqual({
        simple_query_string: {
          query: input,
          default_operator: "AND",
          fields,
          flags: "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
        }
      });
    });

    it('should return single "must" clause for a lone "Text" element: a', () => {
      const textValue = faker.random.word();

      const parseTree: ITree = ["And", [["Including", ["Text", textValue]]]];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              simple_query_string: {
                query: `(${textValue})`,
                default_operator: "AND",
                fields,
                flags:
                  "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
              }
            }
          ]
        }
      });
    });

    it('should return a single "must" clause for a lone "Exactly" element: "a"', () => {
      const parseTree: ITree = ["Exactly", faker.random.word()];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        simple_query_string: {
          query: `"${parseTree[1]}"`,
          fields
        }
      });
    });

    it('should return two "must" clauses for an "AND" operator: a AND b', () => {
      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();

      const parseTree: ITree = [
        "And",
        [
          ["Including", ["Text", textValue1]],
          ["Including", ["Text", textValue2]]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              simple_query_string: {
                query: `(${textValue1}) + (${textValue2})`,
                default_operator: "AND",
                fields,
                flags:
                  "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
              }
            }
          ]
        }
      });
    });

    it('should return two "should" clauses for an "OR" operator: a OR b', () => {
      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();

      const parseTree: ITree = [
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", textValue1]],
                ["Including", ["Text", textValue2]]
              ]
            ]
          ]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    simple_query_string: {
                      query: `(${textValue1}) | (${textValue2})`,
                      default_operator: "AND",
                      fields,
                      flags:
                        "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                    }
                  }
                ],
                minimum_should_match: 1
              }
            }
          ]
        }
      });
    });

    it('should return an embedded "must" clause for a group: (a AND b)', () => {
      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();

      const parseTree: ITree = [
        "And",
        [
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  ["Including", ["Text", textValue1]],
                  ["Including", ["Text", textValue2]]
                ]
              ]
            ]
          ]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              bool: {
                must: [
                  {
                    simple_query_string: {
                      query: `(${textValue1}) + (${textValue2})`,
                      default_operator: "AND",
                      fields,
                      flags:
                        "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                    }
                  }
                ]
              }
            }
          ]
        }
      });
    });

    it('should return "should" clauses embedded within a "must" clause for a group: (a OR b)', () => {
      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();

      const parseTree: ITree = [
        "And",
        [
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  [
                    "Including",
                    [
                      "Or",
                      [
                        ["Including", ["Text", textValue1]],
                        ["Including", ["Text", textValue2]]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              bool: {
                must: [
                  {
                    bool: {
                      should: [
                        {
                          simple_query_string: {
                            query: `(${textValue1}) | (${textValue2})`,
                            default_operator: "AND",
                            fields,
                            flags:
                              "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  }
                ]
              }
            }
          ]
        }
      });
    });

    it('should return a single "must_not" clause for a lone NOT element: NOT a', () => {
      const textValue1 = faker.random.word();

      const parseTree: ITree = ["And", [["Excluding", ["Text", textValue1]]]];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must_not: [
            {
              simple_query_string: {
                query: textValue1,
                default_operator: "AND",
                fields,
                flags: "PHRASE"
              }
            }
          ]
        }
      });
    });

    it('should return a combination of "must" and "must_not": a AND NOT b', () => {
      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();

      const parseTree: ITree = [
        "And",
        [
          ["Including", ["Text", textValue1]],
          ["Excluding", ["Text", textValue2]]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              simple_query_string: {
                query: `(${textValue1})`,
                default_operator: "AND",
                fields,
                flags:
                  "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
              }
            }
          ],
          must_not: [
            {
              simple_query_string: {
                query: textValue2,
                default_operator: "AND",
                fields,
                flags: "PHRASE"
              }
            }
          ]
        }
      });
    });

    it('should return two "must" clauses for ANDed groups: (a OR b) AND (c OR d)', () => {
      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();
      const textValue3 = faker.random.word();
      const textValue4 = faker.random.word();

      const parseTree: ITree = [
        "And",
        [
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  [
                    "Including",
                    [
                      "Or",
                      [
                        ["Including", ["Text", textValue1]],
                        ["Including", ["Text", textValue2]]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  [
                    "Including",
                    [
                      "Or",
                      [
                        ["Including", ["Text", textValue3]],
                        ["Including", ["Text", textValue4]]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              bool: {
                must: [
                  {
                    bool: {
                      should: [
                        {
                          simple_query_string: {
                            query: `(${textValue1}) | (${textValue2})`,
                            default_operator: "AND",
                            fields,
                            flags:
                              "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  }
                ]
              }
            },
            {
              bool: {
                must: [
                  {
                    bool: {
                      should: [
                        {
                          simple_query_string: {
                            query: `(${textValue3}) | (${textValue4})`,
                            default_operator: "AND",
                            fields,
                            flags:
                              "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  }
                ]
              }
            }
          ]
        }
      });
    });

    it('should return double-quoted strings for double-quotes inputs: "a" OR b OR "c"', () => {
      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();
      const textValue3 = faker.random.word();

      const parseTree: ITree = [
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Exactly", textValue1]],
                ["Including", ["Text", textValue2]],
                ["Including", ["Exactly", textValue3]]
              ]
            ]
          ]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              bool: {
                should: [
                  {
                    simple_query_string: {
                      query: `("${textValue1}") | (${textValue2}) | ("${textValue3}")`,
                      default_operator: "AND",
                      fields,
                      flags:
                        "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                    }
                  }
                ],
                minimum_should_match: 1
              }
            }
          ]
        }
      });
    });

    it('should return nested "must" clauses for nested groups: ((a OR b))', () => {
      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();

      const parseTree: ITree = [
        "And",
        [
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  [
                    "Including",
                    [
                      "Group",
                      [
                        "And",
                        [
                          [
                            "Including",
                            [
                              "Or",
                              [
                                ["Including", ["Text", textValue1]],
                                ["Including", ["Text", textValue2]]
                              ]
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              bool: {
                must: [
                  {
                    bool: {
                      must: [
                        {
                          bool: {
                            should: [
                              {
                                simple_query_string: {
                                  query: `(${textValue1}) | (${textValue2})`,
                                  default_operator: "AND",
                                  fields,
                                  flags:
                                    "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                }
                              }
                            ],
                            minimum_should_match: 1
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      });
    });

    it("should parse a combination of AND, ORs, NOTs, and groups: a AND (b NOT (c OR d))", () => {
      // this is basically just a query that hopefully captures all edge cases for a complex input

      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();
      const textValue3 = faker.random.word();
      const textValue4 = faker.random.word();

      const parseTree: ITree = [
        "And",
        [
          ["Including", ["Text", textValue1]],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  ["Including", ["Text", textValue2]],
                  [
                    "Excluding",
                    [
                      "Group",
                      [
                        "And",
                        [
                          [
                            "Including",
                            [
                              "Or",
                              [
                                ["Including", ["Text", textValue3]],
                                ["Including", ["Text", textValue4]]
                              ]
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              simple_query_string: {
                query: `(${textValue1})`,
                default_operator: "AND",
                fields,
                flags:
                  "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
              }
            },
            {
              bool: {
                must: [
                  {
                    simple_query_string: {
                      query: `(${textValue2})`,
                      default_operator: "AND",
                      fields,
                      flags:
                        "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                    }
                  }
                ],
                must_not: [
                  {
                    bool: {
                      must: [
                        {
                          bool: {
                            should: [
                              {
                                simple_query_string: {
                                  query: `(${textValue3}) | (${textValue4})`,
                                  default_operator: "AND",
                                  fields,
                                  flags:
                                    "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                }
                              }
                            ],
                            minimum_should_match: 1
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      });
    });
  });

  describe("languageDetector parameter supplied", () => {
    it("should use the LanguageDetector when supplied", () => {
      // this is basically just a query that hopefully captures all edge cases for a complex input

      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();
      const textValue3 = faker.random.word();
      const textValue4 = faker.random.word();

      const detectedLanguageForTextValue1 = randomLanguage();
      const detectedLanguageForTextValue2 = randomLanguage();
      const detectedLanguageForTextValue3 = randomLanguage();
      let detectedLanguageForTextValue4 = randomLanguage();
      while (detectedLanguageForTextValue4 === detectedLanguageForTextValue3) {
        detectedLanguageForTextValue4 = randomLanguage();
      }

      const languageDetector: LanguageDetector = (value: string) => {
        switch (value) {
          case textValue1:
            return detectedLanguageForTextValue1;
          case textValue2:
            return detectedLanguageForTextValue2;
          case textValue3:
            return detectedLanguageForTextValue3;
          case textValue4:
            return detectedLanguageForTextValue4;
          default:
            throw new Error(`Unknown value: ${value}`);
        }
      };

      const parseTree: ITree = [
        "And",
        [
          ["Including", ["Text", textValue1]],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  ["Including", ["Text", textValue2]],
                  [
                    "Excluding",
                    [
                      "Group",
                      [
                        "And",
                        [
                          [
                            "Including",
                            [
                              "Or",
                              [
                                ["Including", ["Text", textValue3]],
                                ["Including", ["Text", textValue4]]
                              ]
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields, languageDetector);

      expect(parsed).toEqual({
        bool: {
          must: [
            {
              simple_query_string: {
                query: `(${textValue1})`,
                default_operator: "AND",
                fields: fields.map(
                  (field) => `${field}_${detectedLanguageForTextValue1}`
                ),
                flags:
                  "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
              }
            },
            {
              bool: {
                must: [
                  {
                    simple_query_string: {
                      query: `(${textValue2})`,
                      default_operator: "AND",
                      fields: fields.map(
                        (field) => `${field}_${detectedLanguageForTextValue2}`
                      ),
                      flags:
                        "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                    }
                  }
                ],
                must_not: [
                  {
                    bool: {
                      must: [
                        {
                          bool: {
                            should: [
                              {
                                simple_query_string: {
                                  query: `(${textValue3})`,
                                  default_operator: "AND",
                                  fields: fields.map(
                                    (field) =>
                                      `${field}_${detectedLanguageForTextValue3}`
                                  ),
                                  flags:
                                    "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                }
                              },
                              {
                                simple_query_string: {
                                  query: `(${textValue4})`,
                                  default_operator: "AND",
                                  fields: fields.map(
                                    (field) =>
                                      `${field}_${detectedLanguageForTextValue4}`
                                  ),
                                  flags:
                                    "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                }
                              }
                            ],
                            minimum_should_match: 1
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ]
        }
      });
    });

    it('"eng" languageDetector supplied should only ever append "eng" ', () => {
      // this is basically just a query that hopefully captures all edge cases for a complex input

      const textValue1 = faker.random.word();
      const textValue2 = faker.random.word();
      const textValue3 = faker.random.word();
      const textValue4 = faker.random.word();

      const parseTree: ITree = [
        "And",
        [
          ["Including", ["Text", textValue1]],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  ["Including", ["Text", textValue2]],
                  [
                    "Excluding",
                    [
                      "Group",
                      [
                        "And",
                        [
                          [
                            "Including",
                            [
                              "Or",
                              [
                                ["Including", ["Text", textValue3]],
                                ["Including", ["Text", textValue4]]
                              ]
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ];
      const fields = [faker.datatype.string(), faker.datatype.string()];

      const parsed = parserService.parse(parseTree, fields, ENGLISH);

      expect(parsed).toEqual({
        bool: {
          must: expect.arrayContaining([
            {
              simple_query_string: {
                query: `(${textValue1})`,
                default_operator: "AND",
                fields: fields.map((field) => `${field}_${ENGLISH}`),
                flags:
                  "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
              }
            },
            {
              bool: {
                must: [
                  {
                    simple_query_string: {
                      query: `(${textValue2})`,
                      default_operator: "AND",
                      fields: fields.map((field) => `${field}_${ENGLISH}`),
                      flags:
                        "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                    }
                  }
                ],
                must_not: [
                  {
                    bool: {
                      must: [
                        {
                          bool: {
                            should: [
                              {
                                simple_query_string: {
                                  query: `(${textValue3}) | (${textValue4})`,
                                  default_operator: "AND",
                                  fields: fields.map(
                                    (field) => `${field}_${ENGLISH}`
                                  ),
                                  flags:
                                    "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
                                }
                              }
                            ],
                            minimum_should_match: 1
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
          ])
        }
      });
    });
  });
});
