import { Service } from "typedi";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";

import { createLogger } from "../lib/Logger";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  SERVER_CONFIG,
  LIST_INDICES_TOOL_CONFIG,
  GET_MAPPING_TOOL_CONFIG,
  ELASTIC_SEARCH_TOOL_CONFIG,
  //   GET_PROFILE_DOCUMENT_TOOL_CONFIG,
  getMappingsHandler,
  getListIndicesHandler,
  elasticSearchHandler
  //   getProfileDocumentHandler
} from "../lib/MCPServer";

@Service()
export class ElasticMCPServerService {
  private readonly logger = createLogger(this);

  constructor(private elasticService: ElasticSearchService) {}

  async createServer(): Promise<McpServer> {
    const server = new McpServer({
      name: SERVER_CONFIG.name,
      version: SERVER_CONFIG.version
    });

    return server;
  }

  async addToolsToServer(server: McpServer) {
    //list_indices
    server.tool(
      LIST_INDICES_TOOL_CONFIG.name,
      LIST_INDICES_TOOL_CONFIG.description,
      LIST_INDICES_TOOL_CONFIG.validator,
      getListIndicesHandler(this.elasticService.client, this.logger)
    );

    //get_mappings
    server.tool(
      GET_MAPPING_TOOL_CONFIG.name,
      GET_MAPPING_TOOL_CONFIG.description,
      GET_MAPPING_TOOL_CONFIG.validator,
      getMappingsHandler(this.elasticService.client, this.logger)
    );

    //search_elasticsearch
    server.tool(
      ELASTIC_SEARCH_TOOL_CONFIG.name,
      ELASTIC_SEARCH_TOOL_CONFIG.description,
      ELASTIC_SEARCH_TOOL_CONFIG.validator,
      elasticSearchHandler(this.elasticService.client, this.logger)
    );

    //get_profile_document
    // server.tool(
    //     GET_PROFILE_DOCUMENT_TOOL_CONFIG.name,
    //     GET_PROFILE_DOCUMENT_TOOL_CONFIG.description,
    //     GET_PROFILE_DOCUMENT_TOOL_CONFIG.validator,
    //     getProfileDocumentHandler(this.elasticService.client, this.logger)
    // )
  }

  async startServer() {
    const server = await this.createServer();
    this.addToolsToServer(server);
    const transport = new StdioServerTransport();
    await server.connect(transport);
    this.logger.info("Elastic Search MCP Server running on stdio");
  }
}
