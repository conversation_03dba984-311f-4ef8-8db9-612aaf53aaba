import { Service } from "typedi";
import { ElasticSearchSpellCheckService } from "./ElasticSearchSpellCheckService";
import { ConfigService } from "./ConfigService";
import { createLogger } from "../lib/Logger";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { Trace } from "../Tracer";
import {
  getEditDistanceMatrix,
  getOperationsFromEditDistanceMatrix
} from "../util/editDistanceUtil";
import {
  SearchPhraseSuggest,
  SearchPhraseSuggestOption,
  SearchRequest,
  SearchResponse
} from "@elastic/elasticsearch/lib/api/types";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";
import { KeywordSearchInput } from "@h1nyc/search-sdk";

const HAS_ADVANCED_OPERATORS = /(\sAND\s|\sOR\s|NOT\s)/;
const HAS_NUMBERS = /.*\d.*/;
const HAS_EMAIL = /[\w.-]+@[\w.-]+/;

@Service()
export class SpellSuggesterService {
  private readonly logger = createLogger(this);
  private spellCheckIndex: string;

  constructor(
    config: ConfigService,
    private elasticSpellCheckService: ElasticSearchSpellCheckService,
    private searchAnalyticsTracerService: SearchAnalyticsTracerService
  ) {
    this.spellCheckIndex = config.elasticSpellCheckIndex;
  }

  private buildSearchAndPhraseSuggestionRequests(query: string): SearchRequest {
    return {
      index: this.spellCheckIndex,
      suggest: {
        suggest_phrase: {
          text: query,
          phrase: {
            max_errors: 0.9,
            field: "trigram",
            gram_size: 3,
            size: 1,
            direct_generator: [
              {
                field: "trigram",
                max_inspections: 10,
                suggest_mode: "missing"
              }
            ],
            collate: {
              query: {
                source: `{"simple_query_string": {"query": "{{suggestion}}","default_operator": "AND","fields": ["trigram"]}}`
              },
              prune: false
            }
          }
        }
      }
    };
  }

  private isValidSpellCheckQuery(
    query: string,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ) {
    if (
      !query ||
      HAS_ADVANCED_OPERATORS.test(query) || // TODO: Spell checker should work for advanced operators
      HAS_NUMBERS.test(query) ||
      HAS_EMAIL.test(query)
    ) {
      return false;
    }

    if (this.hasPersonNameIntent(queryUnderstandingServiceResponse)) {
      return false;
    }
    return true;
  }

  private getSuggestionTextFromResponse(
    phraseSuggesterResponse: Readonly<SearchResponse>
  ) {
    const suggestPhraseObject = phraseSuggesterResponse?.suggest?.[
      "suggest_phrase"
    ] as SearchPhraseSuggest[];
    if (
      phraseSuggesterResponse.suggest &&
      suggestPhraseObject?.length &&
      (suggestPhraseObject[0]?.options as SearchPhraseSuggestOption[]).length
    ) {
      const suggestionObject = (
        suggestPhraseObject[0].options as SearchPhraseSuggestOption[]
      )[0];

      return suggestionObject?.text;
    }

    return undefined;
  }

  private hasPersonNameIntent(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): boolean {
    if (
      queryUnderstandingServiceResponse?.hasQueryIntent &&
      queryUnderstandingServiceResponse.getQueryIntent()?.hasPersonNameIntent &&
      queryUnderstandingServiceResponse
        .getQueryIntent()
        ?.getPersonNameIntent()
        ?.getScore() === 1.0
    ) {
      return true;
    } else {
      return false;
    }
  }

  private addCharacterCasingToSuggestion(
    userQuery: string,
    suggestion: string
  ) {
    const editMatrix = getEditDistanceMatrix(userQuery, suggestion);
    const editOps = getOperationsFromEditDistanceMatrix(editMatrix);

    const queryCharArray = userQuery.split("");
    const suggestionCharArray = suggestion.split("");

    editOps.forEach((editOp) => {
      if (editOp.operationType === "keep") {
        suggestionCharArray[editOp.j] = queryCharArray[editOp.i];
      }
    });

    return suggestionCharArray.join("");
  }

  @Trace("h1-search.search.spell-suggestion")
  async getSpellCheckSuggestion(
    { query = "", userId, projectId }: KeywordSearchInput,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): Promise<string | undefined> {
    const isValid = this.isValidSpellCheckQuery(
      query,
      queryUnderstandingServiceResponse
    );
    if (isValid) {
      const request = this.buildSearchAndPhraseSuggestionRequests(query!);

      let response;
      try {
        response = await this.elasticSpellCheckService.query(request);
      } catch (error) {
        return undefined;
      }

      if (response) {
        let suggestion = this.getSuggestionTextFromResponse(response);

        if (suggestion) {
          suggestion = this.addCharacterCasingToSuggestion(query, suggestion);

          this.searchAnalyticsTracerService.sendAnalyticsEvent({
            event: "search.spellSuggest.suggestion",
            properties: {
              query,
              suggestion,
              projectId
            },
            timestamp: new Date(),
            userId
          });
        }

        this.logger.info({ query, suggestion }, "spell suggester info");
        return suggestion;
      }
    }

    return undefined;
  }
}
