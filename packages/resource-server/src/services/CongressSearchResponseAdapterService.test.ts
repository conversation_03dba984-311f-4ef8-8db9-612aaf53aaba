import {
  CongressSearchResult,
  CongressSearchInput,
  CongressSearchResponse
} from "@h1nyc/search-sdk";
import {
  createMockInstance,
  generateMockElasticsearchCongressHit,
  generateMockElasticsearchResponseWithAggregations
} from "../util/TestUtils";
import { faker } from "@faker-js/faker";
import { SearchTotalHits } from "@elastic/elasticsearch/lib/api/types";
import {
  CongressSearchFilterAutocompleteTypes,
  ElasticsearchCongressDoc
} from "./CongressSearchResourceService";
import { CongressSearchResponseAdapterService } from "./CongressSearchResponseAdapterService";
import _ from "lodash";
import { LocationLabelFormatterService } from "./LocationLabelFormatterService";

function addressNestedDocToAddress(
  address: ElasticsearchCongressDoc["addresses"][number]
) {
  if (isNonEmptyAddress(address)) {
    return {
      street1: address.street1,
      street2: address.street2,
      street3: address.street3,
      city: address.city,
      region: address.region,
      regionCode: address.region_code,
      country: address.country,
      postalCode: address.postal_code,
      county: address.county,
      district: address.district,
      languageCode: address.language_code
    };
  }

  return null;
}

function isNonEmptyAddress(
  address: ElasticsearchCongressDoc["addresses"][number]
) {
  const fields = [
    address.street1,
    address.street2,
    address.street3,
    address.city,
    address.region,
    address.region_code,
    address.country,
    address.postal_code,
    address.county,
    address.district
  ];

  return fields.some((field) => field?.trim().length);
}

function speakerNestedDocToSpeaker(
  speaker: NonNullable<ElasticsearchCongressDoc["speakers"]>[number]
) {
  return {
    id: speaker.h1_person_id,
    name: speaker.name,
    role: speaker.role
  };
}

function translationNestedDocToTranslation(
  translation: ElasticsearchCongressDoc["translations"][number]
) {
  return {
    name: translation.name,
    description: translation.description,
    society: translation.society,
    languageCode: translation.language_code
  };
}

function toCongressSearchResult(
  esHit: ElasticsearchCongressDoc
): CongressSearchResult {
  return {
    id: esHit.h1_conference_id,
    seriesId: esHit.h1_series_id,
    name: esHit.name,
    type: esHit["filters.congress_type"],
    seriesName: esHit.series_name,
    addresses: _.compact(esHit.addresses.map(addressNestedDocToAddress)),
    speakers: esHit.speakers?.map(speakerNestedDocToSpeaker) ?? [],
    translations: esHit.translations.map(translationNestedDocToTranslation),
    isFollowing: false,
    startDate: esHit["filters.start_date"],
    endDate: esHit["filters.end_date"],
    society: esHit.society
  };
}

function extractTotalValue(total: number | SearchTotalHits | undefined) {
  if (total === undefined) {
    return 0;
  }

  if (typeof total === "number") {
    return total;
  }

  return total.value;
}

describe("CongressSearchResponseAdapterService", () => {
  describe("adaptToCongressSearchResponse", () => {
    it("congresses should be parsed from the elasticsearch response", async () => {
      const input: CongressSearchInput = {
        projectId: faker.datatype.string(),
        userId: faker.datatype.string(),
        query: faker.datatype.string()
      };

      const congress1 = generateMockElasticsearchCongressHit();
      const congress2 = generateMockElasticsearchCongressHit();

      const elasticsearchResponse =
        generateMockElasticsearchResponseWithAggregations([
          congress1,
          congress2
        ]);

      const expectedResponse: CongressSearchResponse = {
        total: extractTotalValue(elasticsearchResponse.hits.total ?? 0),
        congresses: [congress1, congress2].map(toCongressSearchResult)
      };

      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );

      const responseAdapter = new CongressSearchResponseAdapterService(
        locationLabelFormatterService
      );

      const actualResponse = responseAdapter.adaptToCongressSearchResponse(
        input,
        elasticsearchResponse,
        new Set([congress1.h1_series_id])
      );

      expect(actualResponse).toEqual({
        ...expectedResponse,
        congresses: [
          {
            ...expectedResponse.congresses[0],
            isFollowing: true
          },
          expectedResponse.congresses[1]
        ]
      });
    });
  });

  describe("adaptToCongressSearchFilterAggregations", () => {
    it("should return an empty array if the aggregation is undefined", () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const responseAdapter = new CongressSearchResponseAdapterService(
        locationLabelFormatterService
      );
      const filterType = faker.helpers.arrayElement(
        Object.values(CongressSearchFilterAutocompleteTypes)
      );
      const data = {
        aggregations: {}
      };
      const actualResponse =
        responseAdapter.adaptToCongressSearchFilterAggregations(
          data as any,
          filterType
        );
      expect(actualResponse).toEqual([]);
    });
  });
});
