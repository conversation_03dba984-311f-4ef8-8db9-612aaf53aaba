import {
  Affiliation,
  TLAffiliation,
  KeywordSearchInput,
  LocationBlock,
  TLWeightedLocationBlock,
  FormattedTopAffiliations,
  FormattedTopAffiliationsH1dn,
  FormattedTopTLAffiliations
} from "@h1nyc/search-sdk";
import { NameSearchInput } from "@h1nyc/search-sdk/dist/interfaces/NameSearchInput";
import { min, orderBy, uniqBy } from "lodash";
import _ from "lodash";
import { Service } from "typedi";
import { createLogger } from "../lib/Logger";
import { Location } from "./KeywordSearchResourceServiceRewrite";
import { stripLanguageSuffix } from "./NameSearchHighConfPersonAdapterService";
import { SavedTerritory } from "@h1nyc/account-user-entities";

enum ExperienceCategory {
  CurrentWorkAffiliation = "CurrentWorkAffiliation",
  PastWorkAffiliation = "PastWorkAffiliation",
  Education = "Education",
  Membership = "Membership",
  NoAffiliation = "NoAffiliation",
  TrialAffiliation = "TrialAffiliation"
}

type NormalizedScores = Record<TLAffiliationSignalField, number>;
type NormalizedAffiliation = Affiliation & {
  tlAffiliationScore?: number;
  normalizedScores: Partial<NormalizedScores>;
};

// Number of Patients the HCP Has Seen at the Affiliation (weight: 0.25)
// Number of Trials the HCP Has Performed at the Affiliation (weight: 0.20)
// Total Number of Patients at the Affiliation (weight: 0.15)
// Number of Trials in Total at the Affiliation (weight: 0.10)
// Total Number of Claims at the Affiliation (weight: 0.10)
// Type of Affiliation (categorical score, weight: 0.20)
const TLAffiliationSortingWeight = {
  NumberOfPatientsAtAffiliation: 0.25,
  NumberOfTrialsAtAffiliation: 0.2,
  TotalPatientsAtAffiliation: 0.15,
  TotalTrialsAtAffiliation: 0.1,
  TotalClaimsAtAffiliation: 0.1,
  AffiliationType: 0.2
} as const;

// Determines the sorting order for TL affiliation types:
//  1. Hospitals, University Systems, Health Systems
//  2. Research Labs, Research Facilities, CROs: 0.8
//  3. Private Practices, Non-Hospital Healthcare Facilities
//  4. Government Agencies, Healthcare Alliances
//  5. Other
const TLAffiliationTypeSortingWeight = {
  Hospital: 1.0,
  ResearchOrCRO: 0.8,
  NonHospitalHealthFacility: 0.6,
  GovernemntAgency: 0.4,
  Other: 0.2
} as const;

const TLAffiliationSignalFields = [
  "total_claims_at_affiliation",
  "number_of_trials_at_affiliation",
  "number_of_trials_at_organization",
  "number_of_patients_at_affiliation",
  "number_of_patients_at_organization"
] as const;

type TLAffiliationSignalField = (typeof TLAffiliationSignalFields)[number];

const getTLAffiliationTypeScore = (
  institutionType: string | null | undefined
) => {
  switch (institutionType) {
    case "Hospital":
    case "University System":
    case "University":
    case "College":
    case "Health System":
      return TLAffiliationTypeSortingWeight.Hospital;
    case "Research Lab":
    case "Research Facility":
    case "Contract Research Organization":
      return TLAffiliationTypeSortingWeight.ResearchOrCRO;
    case "Private Practice / Physician Group":
    case "Non-Hospital Healthcare Facility":
      return TLAffiliationTypeSortingWeight.NonHospitalHealthFacility;
    case "Government Agency":
    case "Healthcare Alliance Organizer":
      return TLAffiliationTypeSortingWeight.GovernemntAgency;
    case "Other":
      return TLAffiliationTypeSortingWeight.Other;
    default:
      return 0;
  }
};

export const sortAffiliationsByType = (
  institutionType: string | null | undefined
) => {
  switch (institutionType) {
    case "Hospital":
      return 0;
    case "University System":
      return 1;
    case "University":
      return 2;
    case "College":
      return 3;
    case "Research Facility":
      return 4;
    case "Research Lab":
      return 5;
    case "Contract Research Organization":
      return 6;
    case "Health System":
      return 7;
    case "Healthcare Alliance Organizer":
      return 8;
    case "Government Agency":
      return 9;
    case "Private Practice / Physician Group":
      return 10;
    case "Non-Hospital Healthcare Facility":
      return 11;
    case "Other":
      return 12;
    default:
      return 20;
  }
};

export interface SectionData<T> {
  currentWork: T[];
  education: T[];
  membership: T[];
  other: T[];
  past: T[];
}

const getAfflsToUse = <T>(
  formattedCurrentLocations: LocationBlock<T>[],
  formattedOtherAffls: LocationBlock<T>[],
  formattedPastAffls: LocationBlock<T>[]
) => {
  if (formattedCurrentLocations.length) {
    return formattedCurrentLocations;
  }

  if (formattedOtherAffls.length) {
    return formattedOtherAffls;
  }

  if (formattedPastAffls.length) {
    return formattedPastAffls;
  }

  return [];
};

const getTLAfflsToUse = <T>(
  formattedCurrentLocations: TLWeightedLocationBlock<T>[],
  formattedOtherAffls: TLWeightedLocationBlock<T>[],
  formattedPastAffls: TLWeightedLocationBlock<T>[]
) => {
  if (formattedCurrentLocations.length) {
    return formattedCurrentLocations;
  }

  if (formattedOtherAffls.length) {
    return formattedOtherAffls;
  }

  if (formattedPastAffls.length) {
    return formattedPastAffls;
  }

  return [];
};

function getAffiliationResultCountry(a: string | Affiliation) {
  if (typeof a === "string") {
    return [a.trim().toLowerCase()];
  }

  if (a?.institution?.address) {
    const countryFilterValues = [];

    if (a.institution.address.country) {
      countryFilterValues.push(
        a.institution.address.country.trim().toLowerCase()
      );
    }

    if (a.institution.address.country_level_regions) {
      countryFilterValues.push(
        ...a.institution.address.country_level_regions.map((region) =>
          region.trim().toLowerCase()
        )
      );
    }

    return countryFilterValues;
  }

  return false;
}

function getAffiliationResultState(a: string | Affiliation) {
  if (typeof a === "string") {
    return [a.trim().toLowerCase()];
  }

  if (a?.institution?.address) {
    const regionFilterValues = [];

    if (a.institution.address.region) {
      regionFilterValues.push(
        a.institution.address.region.trim().toLowerCase()
      );
    }

    if (a.institution.address.state_level_regions) {
      regionFilterValues.push(
        ...a.institution.address.state_level_regions.map((region) =>
          region.trim().toLowerCase()
        )
      );
    }

    return regionFilterValues;
  }

  return false;
}

const append = (value?: string | null, prefix?: string) => {
  if (value) {
    return prefix ? prefix + value : value;
  }

  return "";
};

function getAffiliationResultLocationToken(a: string | Affiliation) {
  if (typeof a === "string") {
    return [a.trim().toLowerCase()];
  }

  const { address } = a.institution;

  if (address) {
    return [
      (
        append(address.city) +
        append(address.regionCode, ", ") +
        append(address.country, ", ")
      )
        .trim()
        .toLowerCase()
    ];
  }

  return false;
}

function getAffiliationResultCity(a: string | Affiliation) {
  if (typeof a === "string") {
    return [a.trim().toLowerCase()];
  }

  if (a?.institution?.address) {
    const cityFilterValues = [];

    if (a.institution.address.city) {
      cityFilterValues.push(a.institution.address.city.trim().toLowerCase());
    }

    if (a.institution.address.city_level_regions) {
      cityFilterValues.push(
        ...a.institution.address.city_level_regions.map((region) =>
          region.trim().toLowerCase()
        )
      );
    }

    return cityFilterValues;
  }

  return false;
}

function getAffiliationResultZip(a: string | Affiliation) {
  if (typeof a === "string") {
    return [a.trim().toLowerCase()];
  }

  if (a?.institution?.address?.postalCode) {
    return [a.institution.address.postalCode.trim().toLowerCase()];
  }

  return false;
}

function getAffiliationResultLocationTokenUsingState(a: string | Affiliation) {
  if (typeof a === "string") {
    return [a.trim().toLowerCase()];
  }

  const { address } = a.institution;

  if (address) {
    return [
      (
        append(address.city) +
        append(address.region, ", ") +
        append(address.country, ", ")
      )
        .trim()
        .toLowerCase()
    ];
  }

  return false;
}

const affiliationIntersectionTest = <T extends Affiliation>(
  a: string | T,
  b: string | T,
  getMatchValue: (a: string | T) => string[] | false
) => {
  const names1 = getMatchValue(a);
  const names2 = getMatchValue(b);

  if (names1 === false || names2 === false) {
    return false;
  }

  return _.intersection(names1, names2).length > 0;
};

const matchingResultAffiliations = <T extends Affiliation>(
  affiliations: T[],
  values: string[],
  getMatchValue: (a: string | T) => string[] | false,
  returnDefault = true, // don't return a default affiliation if no matches are found.
  backupMatchValue?: (a: string | T) => string[] | false
) => {
  if (values.length > 0) {
    const intersection = _.intersectionWith(affiliations, values, (a, b) => {
      const result = affiliationIntersectionTest(a, b, getMatchValue);

      if (!result && backupMatchValue) {
        return affiliationIntersectionTest(a, b, backupMatchValue);
      }

      return result;
    });

    return intersection;
  }

  // I think returning the affiliations back if values.length is empty might be a bug. Not sure
  // but since the frontend path that uses it with returnDefault = true has 1.) existied for a long time
  // and 2.) is going way, it doesn't matter.
  return returnDefault ? affiliations : [];
};

const bestPreciseGeoMatch = (
  affiliations: Affiliation[],
  valueSet: string[][],
  getMatchFuncs: ((a: string | Affiliation) => string[] | false)[],
  returnDefault = true, // don't return a default affiliation if no matches are found.,
  backupMatchFuncs?: Array<
    undefined | ((a: string | Affiliation) => string[] | false)
  >
): Affiliation | undefined => {
  const valueSetEmpty =
    _.isEmpty(valueSet) || valueSet.every((v) => _.isEmpty(v));

  if (!affiliations.length) {
    return undefined;
  }

  // this is the end of the recursion. Nothing left to test so return first affiliation in list
  if (valueSetEmpty || !getMatchFuncs.length) {
    return affiliations[0];
  }

  const matchValueFunc = getMatchFuncs.shift();
  const backupValueFunc = backupMatchFuncs
    ? backupMatchFuncs.shift()
    : undefined;

  const values = valueSet.shift();

  const matches = matchingResultAffiliations(
    affiliations,
    values!,
    matchValueFunc!,
    returnDefault,
    backupValueFunc
  );

  return bestPreciseGeoMatch(
    matches,
    valueSet,
    getMatchFuncs,
    returnDefault,
    backupMatchFuncs
  );
};

function getInstitutionResultName(a: string | Affiliation) {
  if (typeof a === "string") {
    return [a.trim().toLowerCase()];
  }

  if (a && a.institution && a.institution.name) {
    return [a.institution.name.trim().toLowerCase()];
  }

  return false;
}

function getInstitutionResultAndUltimateParentName(a: string | Affiliation) {
  if (typeof a === "string") {
    return [a.trim().toLowerCase()];
  }

  if (a?.institution?.ultimateParent?.name) {
    return [a.institution.ultimateParent.name.trim().toLowerCase()];
  }

  return false;
}

/**
 * Recursive function to get all affiliations that match
 */
const bestGeoMatches = <T extends Affiliation>(
  affiliations: T[],
  valueSet: string[][],
  getMatchFuncs: ((a: string | T) => string[] | false)[],
  returnDefault = true // don't return a default affiliation if no matches are found.
): T[] => {
  const valueSetEmpty =
    _.isEmpty(valueSet) || valueSet.every((v) => _.isEmpty(v));

  if (!affiliations.length || valueSetEmpty || !getMatchFuncs.length) {
    return [];
  }

  const matchValueFunc = getMatchFuncs.shift();
  const values = valueSet.shift();

  const matches = matchingResultAffiliations(
    affiliations,
    values!,
    matchValueFunc!,
    returnDefault
  );

  if (matches.length) {
    const recursiveMatches = bestGeoMatches(
      matches,
      valueSet,
      getMatchFuncs,
      returnDefault
    );

    if (recursiveMatches.length) {
      return recursiveMatches;
    }

    return matches;
  }

  return bestGeoMatches(affiliations, valueSet, getMatchFuncs, returnDefault);
};

// Gets all affiliations that match the current filters
export const allMatchingActiveAffiliations = <T extends Affiliation>(
  affiliations: T[],
  input: Readonly<KeywordSearchInput | NameSearchInput>,
  matchedTerritoryAffiliationNames: Array<string>,
  territories: Array<SavedTerritory>
) => {
  // no affiliations just return back an empty array.
  if (!affiliations.length) {
    return [];
  }
  const searchFilters = input.suppliedFilters;
  const countriesFromTerritories = territories
    .flatMap((territory) =>
      territory.territoryData.countries
        ? territory.territoryData.countries
        : undefined
    )
    .filter((country) => !!country) as string[];
  const regionsFromTerritories = territories
    .flatMap((territory) =>
      territory.territoryData.regions
        ? territory.territoryData.regions
        : undefined
    )
    .filter((state) => !!state) as string[];
  const postalCodesFromTerritories = territories
    .flatMap((territory) =>
      territory.territoryData.postalCodes
        ? territory.territoryData.postalCodes
        : undefined
    )
    .filter((postalCode) => !!postalCode) as string[];
  const selectedInstitutions = [
    ...searchFilters.pastAndPresentWorkInstitutions.values,
    ...searchFilters.presentWorkInstitutions.values
  ];
  // no filters to match on so none will match. return empty arrray.
  if (
    !selectedInstitutions.length &&
    !matchedTerritoryAffiliationNames.length &&
    !searchFilters.zipCode.values.length &&
    !searchFilters.city.values.length &&
    !searchFilters.state.values.length &&
    !searchFilters.country.values.length &&
    !countriesFromTerritories.length &&
    !regionsFromTerritories.length &&
    !postalCodesFromTerritories.length
  ) {
    return [];
  }

  if (
    !selectedInstitutions.length &&
    !matchedTerritoryAffiliationNames.length &&
    !countriesFromTerritories.length &&
    !regionsFromTerritories.length &&
    !postalCodesFromTerritories.length
  ) {
    // Match from most precise (zip) to most broad (country)
    const bestMatches = bestGeoMatches(
      affiliations,
      [
        searchFilters.zipCode.values,
        searchFilters.city.values, // locationTokens
        searchFilters.city.values.map(
          (locationToken) => locationToken.split(",")[0]
        ),
        searchFilters.state.values,
        searchFilters.country.values
      ],
      [
        getAffiliationResultZip,
        getAffiliationResultLocationToken,
        getAffiliationResultCity,
        getAffiliationResultState,
        getAffiliationResultCountry
      ],
      false
    );

    // get the geos that match and return those
    if (bestMatches.length) {
      return bestMatches;
    }
  }

  if (
    !selectedInstitutions.length &&
    !matchedTerritoryAffiliationNames.length
  ) {
    // Match from most precise (zip) to most broad (country)
    const bestMatchesForTerritory = bestGeoMatches(
      affiliations,
      [
        postalCodesFromTerritories,
        regionsFromTerritories,
        countriesFromTerritories
      ],
      [
        getAffiliationResultZip,
        getAffiliationResultState,
        getAffiliationResultCountry
      ],
      false
    );

    // get the geos that match and return those
    if (bestMatchesForTerritory.length) {
      return bestMatchesForTerritory;
    }
  }

  if (!selectedInstitutions.length) {
    const institutionMatchesForTerritory = matchingResultAffiliations(
      affiliations,
      matchedTerritoryAffiliationNames,
      getInstitutionResultName,
      false
    );

    // Got an institution match, so return those
    if (
      institutionMatchesForTerritory.length &&
      institutionMatchesForTerritory !== affiliations
    ) {
      return institutionMatchesForTerritory;
    }
  }

  const institutionMatches = matchingResultAffiliations(
    affiliations,
    selectedInstitutions,
    getInstitutionResultName,
    false
  );

  // Got an institution match, so return those
  if (institutionMatches.length && institutionMatches !== affiliations) {
    return institutionMatches;
  }

  // no matches so return empty.
  return [];
};

@Service()
export class AffiliationAdapterService {
  private readonly logger = createLogger(this);

  private computeCategory(
    exp: Pick<Affiliation, "type" | "isPastAffiliation">
  ) {
    const affiliationType = exp.type?.toLowerCase();

    if (affiliationType === "society member") {
      return ExperienceCategory.Membership;
    }

    if (affiliationType === "student") {
      return ExperienceCategory.Education;
    }

    if (affiliationType === "no affiliation") {
      return ExperienceCategory.NoAffiliation;
    }

    if (affiliationType === "trial affiliation") {
      return ExperienceCategory.TrialAffiliation;
    }

    if (exp.isPastAffiliation) {
      return ExperienceCategory.PastWorkAffiliation;
    }

    return ExperienceCategory.CurrentWorkAffiliation;
  }

  private translateTaiwanText(
    text: string,
    isTaiwanTranslationEnabled = false,
    translate = true
  ) {
    if (!text || !isTaiwanTranslationEnabled) {
      return text;
    }

    const unconvertedTaiwanRegex = /中國台灣省/i;
    const convertedTaiwanRegex = /台灣/i;
    const unconvertedTaiwan = "中國台灣省";
    const convertedTaiwan = "台灣";
    if (translate) {
      return text.replace(unconvertedTaiwanRegex, convertedTaiwan);
    }
    return text.replace(convertedTaiwanRegex, unconvertedTaiwan);
  }

  private computeTLAffiliationScore(
    affiliation: NormalizedAffiliation
  ): number {
    const { normalizedScores } = affiliation;
    const affiliationTypeScore = getTLAffiliationTypeScore(
      affiliation.institution.type
    );
    const tlAffiliationScore =
      (normalizedScores.number_of_patients_at_affiliation || 0) *
        TLAffiliationSortingWeight.NumberOfPatientsAtAffiliation +
      (normalizedScores.number_of_trials_at_affiliation || 0) *
        TLAffiliationSortingWeight.NumberOfTrialsAtAffiliation +
      (normalizedScores.number_of_trials_at_organization || 0) *
        TLAffiliationSortingWeight.TotalTrialsAtAffiliation +
      (normalizedScores.total_claims_at_affiliation || 0) *
        TLAffiliationSortingWeight.TotalClaimsAtAffiliation +
      (normalizedScores.number_of_patients_at_organization || 0) *
        TLAffiliationSortingWeight.TotalPatientsAtAffiliation +
      affiliationTypeScore * TLAffiliationSortingWeight.AffiliationType;
    return tlAffiliationScore;
  }
  private groupAffiliations(
    affiliations: Affiliation[] | undefined,
    isTLAffiliations = false
  ) {
    if (!affiliations || !affiliations.length) {
      return {
        currentWork: [],
        education: [],
        membership: [],
        other: [],
        past: []
      };
    }

    return affiliations.reduce(
      (prev: SectionData<Affiliation>, curr) => {
        const category = this.computeCategory(curr);
        if (category === ExperienceCategory.CurrentWorkAffiliation) {
          const accuracyScore = isTLAffiliations
            ? curr.tlAccuracyScore
            : curr.accuracyScore;
          if (accuracyScore && accuracyScore > 0) {
            prev.currentWork.push(curr);
          } else {
            prev.other.push(curr);
          }
        } else if (category === ExperienceCategory.Education) {
          prev.education.push(curr);
        } else if (category === ExperienceCategory.Membership) {
          prev.membership.push(curr);
        } else if (category === ExperienceCategory.PastWorkAffiliation) {
          prev.past.push(curr);
        } else {
          prev.other.push(curr);
        }

        return prev;
      },
      {
        currentWork: [],
        education: [],
        membership: [],
        other: [],
        past: []
      }
    );
  }

  private extractValuesFromOtherSignals(
    affiliation: Affiliation,
    fields: TLAffiliationSignalField[]
  ): Partial<Record<TLAffiliationSignalField, number>> {
    const extractedValues: Partial<Record<TLAffiliationSignalField, number>> =
      {};

    fields.forEach((field) => {
      const signal = affiliation?.otherSignals?.find(
        (signal) => signal.name === field
      );
      if (signal && signal.value !== null && signal.value !== undefined) {
        const parsedValue = parseFloat(signal.value);
        if (!isNaN(parsedValue)) {
          extractedValues[field] = parsedValue;
        }
      }
    });

    return extractedValues;
  }

  private computeMinMaxAffiliationValues(
    affiliations: Affiliation[],
    fields: TLAffiliationSignalField[]
  ): Record<TLAffiliationSignalField, { min: number; max: number }> {
    const fieldValues: Record<TLAffiliationSignalField, number[]> = {} as any;

    fields.forEach((field) => {
      fieldValues[field] = [];
    });

    affiliations.forEach((affiliation) => {
      const extractedValues = this.extractValuesFromOtherSignals(
        affiliation,
        fields
      );
      fields.forEach((field) => {
        const value = extractedValues[field];
        if (value !== undefined) {
          fieldValues[field].push(value);
        } else {
          fieldValues[field].push(0);
        }
      });
    });

    const minMaxValues: Record<
      TLAffiliationSignalField,
      { min: number; max: number }
    > = {} as any;

    for (const field of TLAffiliationSignalFields) {
      const values = fieldValues[field];
      const minValue = values.length > 0 ? Math.min(...values) : 0;
      const maxValue = values.length > 0 ? Math.max(...values) : 0;
      minMaxValues[field] = { min: minValue, max: maxValue };
    }

    return minMaxValues;
  }

  private normalizeTLAffiliations(
    affiliations: Affiliation[],
    signalFields: TLAffiliationSignalField[],
    minMaxValues: Record<TLAffiliationSignalField, { min: number; max: number }>
  ) {
    return affiliations.map((affiliation) => {
      const extractedValues = this.extractValuesFromOtherSignals(
        affiliation,
        signalFields
      );
      const normalizedScores: Partial<NormalizedScores> = {};

      signalFields.forEach((field) => {
        const value = extractedValues[field];
        const { min, max } = minMaxValues[field];

        const normalizedValue =
          value !== undefined && max !== min ? (value - min) / (max - min) : 0;

        normalizedScores[field] = normalizedValue;
      });

      return {
        ...affiliation,
        normalizedScores: normalizedScores as Partial<NormalizedScores>
      };
    });
  }

  private tlAffiliationFormatter(
    currentWorkAffiliations: Affiliation[],
    translateTaiwan?: boolean
  ) {
    // extract field min-max values for each field we would like to normalize
    const minMaxValues = this.computeMinMaxAffiliationValues(
      currentWorkAffiliations,
      [...TLAffiliationSignalFields]
    );

    // Generate a normalized score for the relevant signals and store them at the top level
    const normalizedAffiliations: NormalizedAffiliation[] =
      this.normalizeTLAffiliations(
        currentWorkAffiliations,
        [...TLAffiliationSignalFields],
        minMaxValues
      );

    // Generate an Affiliation score for each affiliation to sort by the generated
    // normalized scores + affiliated institution type
    const affiliationsWithScores = normalizedAffiliations.map((affiliation) => {
      const tlAffiliationScore = this.computeTLAffiliationScore(affiliation);
      const { normalizedScores: _, ...affiliationWithoutNormalization } =
        affiliation;

      return {
        ...affiliationWithoutNormalization,
        isPerformingTrials: false,
        isSeeingPatients: false,
        institutionHasTrials: false,
        tlAffiliationScore
      } as TLAffiliation;
    });

    const locationBlocks = affiliationsWithScores.reduce((prev, curr) => {
      const { institution } = curr;
      const parent = curr.institution.ultimateParent
        ? curr.institution.ultimateParent
        : curr.institution;

      const isParent = !institution.ultimateParent;

      const location = curr.institution.address;

      const trialsAtAffiliationSignal = curr.otherSignals?.find(
        (signal) => signal.name === "number_of_trials_at_affiliation"
      );

      const institutionHasTrialsSignal = curr.otherSignals?.find(
        (signal) => signal.name === "number_of_trials_at_organization"
      );

      const isPerformingTrials =
        (trialsAtAffiliationSignal &&
          !isNaN(Number(trialsAtAffiliationSignal.value)) &&
          Number(trialsAtAffiliationSignal.value) > 0) ||
        false;

      const institutionHasTrials =
        (institutionHasTrialsSignal &&
          !isNaN(Number(institutionHasTrialsSignal.value)) &&
          Number(institutionHasTrialsSignal.value) > 0) ||
        false;

      // cache whether the current affiliation has claims associated
      const isSeeingPatients = Boolean(
        curr.claimsNumbers && curr.claimsNumbers > 0
      );

      // only handle if it has an address
      if (location) {
        const locationString = this.translateTaiwanText(
          [location.city, location.region, location.country]
            .filter(Boolean)
            .join(", "),
          translateTaiwan
        );

        const hasNoLatLong = !location.latitude || !location.longitude;

        const region = hasNoLatLong
          ? {
              region: this.translateTaiwanText(
                location.region || "",
                translateTaiwan
              ),
              country: this.translateTaiwanText(
                location.country || "",
                translateTaiwan
              )
            }
          : undefined;

        const locationIndex = prev.findIndex((p) => {
          return p.location === locationString;
        });

        if (locationIndex >= 0) {
          // The location exists, add either a parent or an affiliation ON a parent
          const locationBlock = prev[locationIndex];
          if (isParent) {
            const parentExists = locationBlock.data.find(
              (d) => d.parentInstitution.id === institution.id
            );
            if (parentExists) {
              parentExists.parentAffiliation = {
                ...curr,
                isPerformingTrials,
                institutionHasTrials,
                isSeeingPatients
              };
              parentExists.tlAffiliationScore += curr.tlAffiliationScore;
            } else {
              locationBlock.data.push({
                parentInstitution: parent,
                parentAffiliation: {
                  ...curr,
                  isPerformingTrials,
                  institutionHasTrials,
                  isSeeingPatients
                },
                childrenAffiliation: [],
                tlAffiliationScore: curr.tlAffiliationScore
              });
            }
          } else {
            const data = locationBlock.data.find(
              (d) => d.parentInstitution.id === institution.ultimateParent?.id
            );

            if (data) {
              data.childrenAffiliation.push({
                ...curr,
                isPerformingTrials,
                institutionHasTrials,
                isSeeingPatients
              });
              data.tlAffiliationScore += curr.tlAffiliationScore;
            } else {
              locationBlock.data.push({
                parentInstitution: parent,
                childrenAffiliation: [
                  {
                    ...curr,
                    isPerformingTrials,
                    isSeeingPatients,
                    institutionHasTrials
                  }
                ],
                tlAffiliationScore: curr.tlAffiliationScore
              });
            }
          }

          // sum all affiliation scores for the given locationBlock for top-level sorting
          locationBlock.tlAffiliationScore += curr.tlAffiliationScore;
        } else if (isParent) {
          // if the location is NOT FOUND and IT IS A PARENT.
          prev.push({
            location: locationString,
            region,
            highestClaimCount: curr.claimsNumbers || 0,
            data: [
              {
                parentInstitution: parent,
                parentAffiliation: {
                  ...curr,
                  isPerformingTrials,
                  institutionHasTrials,
                  isSeeingPatients
                },
                childrenAffiliation: [],
                tlAffiliationScore: curr.tlAffiliationScore || 0
              }
            ],
            isPerformingTrials: isPerformingTrials,
            tlAffiliationScore: curr.tlAffiliationScore || 0
          });
        } else {
          // location not found and it's a child
          prev.push({
            location: locationString,
            region,
            highestClaimCount: curr.claimsNumbers || 0,
            data: [
              {
                parentInstitution: parent,
                childrenAffiliation: [
                  {
                    ...curr,
                    isPerformingTrials,
                    isSeeingPatients,
                    institutionHasTrials
                  }
                ],
                tlAffiliationScore: curr.tlAffiliationScore
              }
            ],
            isPerformingTrials: isPerformingTrials,
            tlAffiliationScore: curr.tlAffiliationScore || 0
          });
        }
      }

      return prev;
    }, [] as TLWeightedLocationBlock<TLAffiliation>[]);

    // sort parents and children affiliations by tl affiliation score and name
    locationBlocks.forEach((locationBlock) => {
      locationBlock.data.forEach((data) => {
        // collapse children institutions with same name and zip
        /* eslint-disable no-param-reassign */
        data.childrenAffiliation = uniqBy(
          data.childrenAffiliation,
          (affiliation) => {
            const primaryAddress = affiliation.institution.address;
            let zip;
            if (primaryAddress) {
              if (
                primaryAddress.country?.toLocaleLowerCase() === "united states"
              ) {
                zip = primaryAddress.postalCode?.substring(0, 5);
              } else {
                zip = primaryAddress.postalCode;
              }
            }

            return this.translateTaiwanText(
              [
                affiliation.institution.name,
                primaryAddress?.city,
                primaryAddress?.region,
                primaryAddress?.country,
                zip
              ].join(","),
              translateTaiwan
            );
          }
        );

        // sort by the generated TL Affiliation score, then by name
        /* eslint-disable no-param-reassign */
        data.childrenAffiliation = orderBy(
          data.childrenAffiliation,
          [(ca) => ca.tlAffiliationScore, (ca) => ca.institution.name],
          ["desc", "asc"]
        );
      });

      // sort by cumulative tl affiliation score, then name
      /* eslint-disable no-param-reassign */
      locationBlock.data = orderBy(
        locationBlock.data,
        [
          (data) => data.tlAffiliationScore,
          (data) => data.parentInstitution.name
        ],
        ["desc", "asc"]
      );
    });

    // order by location with the highest TL AffiliationScore, and then by location name
    return orderBy(
      locationBlocks,
      ["tlAffiliationScore", "location"],
      ["desc", "asc"]
    );
  }

  private affiliationFormatter(
    currentWorkAffiliations: Affiliation[],
    translateTaiwan?: boolean
  ) {
    const locationBlocks = currentWorkAffiliations.reduce((prev, curr) => {
      const { institution } = curr;
      const parent = curr.institution.ultimateParent
        ? curr.institution.ultimateParent
        : curr.institution;

      const isParent = !institution.ultimateParent;

      const location = curr.institution.address ?? {
        city: "",
        region: "",
        country: "",
        latitude: undefined,
        longitude: undefined
      };

      const locationString = this.translateTaiwanText(
        [location.city, location.region, location.country]
          .filter(Boolean)
          .join(", "),
        translateTaiwan
      );

      const hasNoLatLong =
        location && (!location.latitude || !location.longitude);

      const region = hasNoLatLong
        ? {
            region: this.translateTaiwanText(
              location.region || "",
              translateTaiwan
            ),
            country: this.translateTaiwanText(
              location.country || "",
              translateTaiwan
            )
          }
        : undefined;

      const locationIndex = prev.findIndex((p) => {
        return p.location === locationString;
      });

      if (locationIndex >= 0) {
        // The location exists, add either a parent or an affiliation ON a parent
        const locationBlock = prev[locationIndex];
        if (isParent) {
          const parentExists = locationBlock.data.find(
            (d) => d.parentInstitution.id === institution.id
          );
          if (parentExists) {
            parentExists.parentAffiliation = curr;
          } else {
            locationBlock.data.push({
              parentInstitution: parent,
              parentAffiliation: curr,
              childrenAffiliation: []
            });
          }
        } else {
          const data = locationBlock.data.find(
            (d) => d.parentInstitution.id === institution.ultimateParent?.id
          );

          if (data) {
            data.childrenAffiliation.push(curr);
          } else {
            locationBlock.data.push({
              parentInstitution: parent,
              childrenAffiliation: [curr]
            });
          }
        }

        locationBlock.highestClaimCount = Math.max(
          locationBlock.highestClaimCount,
          curr.claimsNumbers || 0
        );
      } else if (isParent) {
        // if the location is NOT FOUND and IT IS A PARENT.
        prev.push({
          location: locationString,
          region,
          highestClaimCount: curr.claimsNumbers || 0,
          data: [
            {
              parentInstitution: parent,
              parentAffiliation: curr,
              childrenAffiliation: []
            }
          ]
        });
      } else {
        // location not found and it's a child
        prev.push({
          location: locationString,
          region,
          highestClaimCount: curr.claimsNumbers || 0,
          data: [{ parentInstitution: parent, childrenAffiliation: [curr] }]
        });
      }

      return prev;
    }, [] as LocationBlock<Affiliation>[]);

    // sort parents and children affiliations by claimscount and name
    locationBlocks.forEach((locationBlock) => {
      locationBlock.data.forEach((data) => {
        // sort by the number of claims at institution, then by type, and then institution name.
        data.childrenAffiliation = orderBy(
          data.childrenAffiliation,
          [
            (ca) => ca.claimsNumbers || 0,
            (ca) => sortAffiliationsByType(ca.institution.type),
            (ca) => ca.institution.name
          ],
          ["desc", "asc"]
        );
        // collapse children institutions with same name and zip
        data.childrenAffiliation = uniqBy(
          data.childrenAffiliation,
          (affiliation) => {
            const primaryAddress = affiliation.institution.address;
            let zip;
            if (primaryAddress) {
              if (
                primaryAddress.country?.toLocaleLowerCase() === "united states"
              ) {
                zip = primaryAddress.postalCode?.substring(0, 5);
              } else {
                zip = primaryAddress.postalCode;
              }
            }

            return this.translateTaiwanText(
              [
                affiliation.institution.name,
                primaryAddress?.city,
                primaryAddress?.region,
                primaryAddress?.country,
                zip
              ].join(","),
              translateTaiwan
            );
          }
        );
      });

      // sort by claims count then parent affiliation type
      /* eslint-disable no-param-reassign */
      locationBlock.data = orderBy(
        locationBlock.data,
        [
          (data) => {
            let claimCountSum = data.parentAffiliation?.claimsNumbers ?? 0;
            claimCountSum += data.childrenAffiliation.reduce(
              (prev, curr) => prev + (curr.claimsNumbers || 0),
              0
            );

            return claimCountSum;
          },
          (data) => sortAffiliationsByType(data.parentInstitution.type),
          (data) => data.parentInstitution.name
        ],
        ["desc", "asc", "asc"]
      );
    });

    // order by location with the single highest claims count, then by locations most preferred type
    // and then by location name
    return orderBy(
      locationBlocks,
      [
        "highestClaimCount",
        (locationBlock) =>
          min(
            locationBlock.data.map((data) =>
              sortAffiliationsByType(data.parentInstitution.type)
            )
          ),
        "location"
      ],
      ["desc", "asc", "asc"]
    );
  }

  private hasAffiliationMatchingFilters(
    affiliations: Affiliation[],
    input: Readonly<KeywordSearchInput | NameSearchInput>,
    matchedTerritoryAffiliationNames: Array<string>,
    territories: Array<SavedTerritory>
  ) {
    // no affiliations just return true.
    if (!affiliations.length) {
      return false;
    }
    const countriesFromTerritories = territories
      .flatMap((territory) =>
        territory.territoryData.countries
          ? territory.territoryData.countries
          : undefined
      )
      .filter((country) => !!country) as string[];
    const regionsFromTerritories = territories
      .flatMap((territory) =>
        territory.territoryData.regions
          ? territory.territoryData.regions
          : undefined
      )
      .filter((state) => !!state) as string[];
    const postalCodesFromTerritories = territories
      .flatMap((territory) =>
        territory.territoryData.postalCodes
          ? territory.territoryData.postalCodes
          : undefined
      )
      .filter((postalCode) => !!postalCode) as string[];
    const searchFilters = input.suppliedFilters;
    const selectedInstitutions = [
      ...searchFilters.pastAndPresentWorkInstitutions.values,
      ...searchFilters.presentWorkInstitutions.values,
      ...matchedTerritoryAffiliationNames
    ];

    // no filters just return true.
    if (
      !selectedInstitutions.length &&
      !searchFilters.zipCode.values.length &&
      !searchFilters.city.values.length &&
      !searchFilters.state.values.length &&
      !searchFilters.country.values.length &&
      !countriesFromTerritories.length &&
      !regionsFromTerritories.length &&
      !postalCodesFromTerritories.length
    ) {
      return true;
    }
    if (
      !selectedInstitutions.length &&
      !countriesFromTerritories.length &&
      !regionsFromTerritories.length &&
      !postalCodesFromTerritories.length
    ) {
      // Match from most broad country to the most precise zip
      // don't pass values or functions that aren't filtered on
      // this ensures those values are ignored in the function
      const bestMatch = bestPreciseGeoMatch(
        affiliations,
        [
          ...(searchFilters.country.values.length
            ? [searchFilters.country.values]
            : []),
          ...(searchFilters.state.values.length
            ? [searchFilters.state.values]
            : []),
          ...(searchFilters.city.values.length
            ? [searchFilters.city.values]
            : []),
          ...(searchFilters.city.values.length
            ? [
                searchFilters.city.values.map(
                  (locationToken) => locationToken.split(",")[0]
                )
              ]
            : []),
          ...(searchFilters.zipCode.values.length
            ? [searchFilters.zipCode.values]
            : [])
        ],
        [
          ...(searchFilters.country.values.length
            ? [getAffiliationResultCountry]
            : []),
          ...(searchFilters.state.values.length
            ? [getAffiliationResultState]
            : []),
          ...(searchFilters.city.values.length
            ? [getAffiliationResultLocationToken]
            : []),
          ...(searchFilters.city.values.length
            ? [getAffiliationResultCity]
            : []),
          ...(searchFilters.zipCode.values.length
            ? [getAffiliationResultZip]
            : [])
        ],
        false,
        [
          ...(searchFilters.country.values.length ? [undefined] : []),
          ...(searchFilters.state.values.length ? [undefined] : []),
          ...(searchFilters.city.values.length
            ? [getAffiliationResultLocationTokenUsingState]
            : []),
          ...(searchFilters.city.values.length ? [undefined] : []),
          ...(searchFilters.zipCode.values.length ? [undefined] : [])
        ]
      );

      // return the location based match
      if (bestMatch) {
        return true;
      } else return false;
    }
    if (!selectedInstitutions.length) {
      // Match from most broad country to the most precise zip
      // don't pass values or functions that aren't filtered on
      // this ensures those values are ignored in the function
      const bestMatchForTerritory = bestPreciseGeoMatch(
        affiliations,
        [
          ...(countriesFromTerritories.length
            ? [countriesFromTerritories]
            : []),
          ...(regionsFromTerritories.length ? [regionsFromTerritories] : []),

          ...(postalCodesFromTerritories.length
            ? [postalCodesFromTerritories]
            : [])
        ],
        [
          ...(countriesFromTerritories.length
            ? [getAffiliationResultCountry]
            : []),
          ...(regionsFromTerritories.length ? [getAffiliationResultState] : []),
          ...(postalCodesFromTerritories.length
            ? [getAffiliationResultZip]
            : [])
        ],
        false
      );

      // return the location based match
      if (bestMatchForTerritory) {
        return true;
      }
    }

    const institutionMatches = matchingResultAffiliations(
      affiliations,
      selectedInstitutions,
      getInstitutionResultName,
      false
    );

    // Got an institution match, return true
    if (institutionMatches.length) {
      return true;
    }

    const ultimateParentMatches = matchingResultAffiliations(
      affiliations,
      selectedInstitutions,
      getInstitutionResultAndUltimateParentName,
      false
    );

    // Got an institution match, return true
    if (ultimateParentMatches.length) {
      return true;
    }

    // not a single affiliation matches
    return false;
  }

  private searchTopAffiliationsH1dn(
    affiliations: Affiliation[]
  ): FormattedTopAffiliationsH1dn {
    const currentWorkAffiliations = affiliations.filter(
      (a) => a.type === "Work Affiliation" && !a.isPastAffiliation
    );

    const pastWorkAffiliations = affiliations.filter(
      (a) => a.type === "Work Affiliation" && a.isPastAffiliation
    );

    const pastAffiliationsText = pastWorkAffiliations
      .map((a) => {
        return a.titles.length
          ? `${a.titles.join(", ")} at ${a.institution.name}`
          : a.institution.name;
      })
      .join(", ");

    const affiliation = currentWorkAffiliations[0]
      ? currentWorkAffiliations[0]
      : undefined;

    const address = affiliation?.institution?.address;

    let affiliationToDisplay = undefined;

    if (affiliation) {
      affiliationToDisplay = {
        titles: affiliation.titles,
        locality: address?.city,
        institutionName: affiliation?.institution.name,
        region: address?.region,
        country: address?.country,
        postalCode: address?.postalCode
      };
    }
    return {
      affiliation: affiliationToDisplay,
      pastAffiliationsText,
      pastWorkAffiliations: {
        length: pastWorkAffiliations.length
      }
    };
  }

  searchTopAffiliations(
    affiliations: Affiliation[],
    input: Readonly<KeywordSearchInput | NameSearchInput> | undefined,
    isH1dn: boolean,
    locations: Array<Location> | undefined,
    matchedTerritoryAffiliationNames: Array<string>,
    territories?: SavedTerritory[]
  ): FormattedTopAffiliations | FormattedTopAffiliationsH1dn {
    if (isH1dn) {
      return this.searchTopAffiliationsH1dn(affiliations);
    }

    if (!input) {
      this.logger.error(
        "input is required! Skipping computation of top affiliations"
      );
      return {
        locationsToDisplay: [],
        hasAffiliationMatchingFilter: false,
        isSeeingPatients: 0,
        hasAboveTheLineAffiliations: false,
        formattedRelevantLocations: [],
        hasPastAffiliations: false
      };
    }

    const translateTaiwan = input.projectFeatures.translateTaiwan;

    // groups the affiliations by type
    const groupedAffiliations = this.groupAffiliations(affiliations);

    // formats the affiliations to locations
    const formattedAboveTheLineAffls = this.affiliationFormatter(
      groupedAffiliations.currentWork,
      translateTaiwan
    );

    const formattedOtherCurrentAffls = this.affiliationFormatter(
      !formattedAboveTheLineAffls.length ? groupedAffiliations.other : [],
      translateTaiwan
    );

    const formattedPastAffls = this.affiliationFormatter(
      !formattedOtherCurrentAffls.length && !formattedAboveTheLineAffls.length
        ? groupedAffiliations.past
        : [],
      translateTaiwan
    );

    const hasAboveTheLineAffiliations = !!formattedAboveTheLineAffls.length;
    const hasPastAffiliations = !!formattedPastAffls.length;

    const isSeeingPatients = formattedAboveTheLineAffls.reduce(
      (prev, curr) => prev + curr.highestClaimCount,
      0
    );

    const locationsToUse = getAfflsToUse(
      formattedAboveTheLineAffls,
      formattedOtherCurrentAffls,
      formattedPastAffls
    );

    const locationsToDisplay = locationsToUse.slice(0, 1);
    // these are the affiliations that will drive the display all others will not.
    const affiliationsToCheck = locationsToDisplay.flatMap((locationBlock) => {
      return locationBlock.data.flatMap(
        ({ childrenAffiliation, parentAffiliation }) => [
          ...childrenAffiliation,
          ...(parentAffiliation ? [parentAffiliation] : [])
        ]
      );
    });

    // NOTE: These next steps are designed to handle the situation where an HCP is showing in the search results because of
    // a location or institution filter but none of the top affiliations match the filters. This will find that scenario, find
    // those affiliations and return them for display.
    // given the affiliations that drive the display check if any of them match the provided search criteria
    const territoriesToCheck = territories ? territories : [];
    const hasAffiliationMatchingFilter = this.hasAffiliationMatchingFilters(
      affiliationsToCheck,
      input,
      matchedTerritoryAffiliationNames,
      territoriesToCheck
    );

    // if none of the affiliations that display match the search criteria. Find all of the affiliations that do.
    const searchTriggeredAffls = !hasAffiliationMatchingFilter
      ? allMatchingActiveAffiliations(
          affiliations || [],
          input,
          matchedTerritoryAffiliationNames,
          territoriesToCheck
        )
      : undefined;

    // format that the affiliations that do match the search criteria.
    const formattedRelevantLocations = this.affiliationFormatter(
      searchTriggeredAffls || [],
      translateTaiwan
    );

    const formattedRelevantLocationsToReturn = formattedRelevantLocations.slice(
      0,
      2
    );
    // sometimes the data in the above compare logic is wrong and results in duplicates being shown. This intersection will ensure
    // that never happens. It's a safe guard.
    const isDuplicated = formattedRelevantLocationsToReturn.some((location) => {
      const isDisplayed = locationsToDisplay.find(
        (displayedLocation) => displayedLocation.location === location.location
      );

      return Boolean(isDisplayed);
    });

    if (locationsToDisplay.length === 0 && locations?.length) {
      const location = locations[0];

      const { languageCode } = location;
      const fields = ["city", "state", "country", "zipCode5"].map(
        (prop) => `${prop}_${languageCode}`
      );

      const locFields: {
        city: string;
        state: string;
        country: string;
      } = _(location)
        .pick(fields)
        .mapKeys((value, key) => stripLanguageSuffix(key))
        .value() as {
        city: string;
        state: string;
        country: string;
      };

      const locationString = this.translateTaiwanText(
        [locFields.city, locFields.state, locFields.country]
          .filter(Boolean)
          .join(", "),
        translateTaiwan
      );

      return {
        locationsToDisplay: [
          {
            location: locationString,
            highestClaimCount: 0,
            data: []
          }
        ],
        hasAffiliationMatchingFilter: false,
        isSeeingPatients: 0,
        hasAboveTheLineAffiliations: false,
        formattedRelevantLocations: [],
        hasPastAffiliations: false
      };
    }
    return {
      locationsToDisplay,
      hasAffiliationMatchingFilter: isDuplicated
        ? false
        : hasAffiliationMatchingFilter,
      isSeeingPatients,
      hasAboveTheLineAffiliations,
      formattedRelevantLocations: isDuplicated
        ? []
        : formattedRelevantLocationsToReturn,
      hasPastAffiliations
    };
  }

  searchTopTLAffiliations(
    affiliations: Affiliation[],
    input: Readonly<KeywordSearchInput | NameSearchInput> | undefined,
    isH1dn: boolean,
    locations: Array<Location> | undefined,
    matchedTerritoryAffiliationNames: Array<string>,
    territories?: SavedTerritory[]
  ): FormattedTopTLAffiliations | FormattedTopAffiliationsH1dn {
    if (isH1dn) {
      return this.searchTopAffiliationsH1dn(affiliations);
    }

    if (!input) {
      this.logger.error(
        "input is required! Skipping computation of top affiliations"
      );
      return {
        locationsToDisplay: [],
        hasAffiliationMatchingFilter: false,
        isSeeingPatients: 0,
        hasAboveTheLineAffiliations: false,
        formattedRelevantLocations: [],
        hasPastAffiliations: false,
        isPerformingTrials: false
      };
    }

    const translateTaiwan = input.projectFeatures.translateTaiwan;

    // group affiliations based on TL Accuracy score
    const groupedAffiliations = this.groupAffiliations(affiliations, true);

    const formattedAboveTheLineAffls = this.tlAffiliationFormatter(
      groupedAffiliations.currentWork,
      translateTaiwan
    );

    const formattedOtherCurrentAffls = this.tlAffiliationFormatter(
      !formattedAboveTheLineAffls.length ? groupedAffiliations.other : [],
      translateTaiwan
    );

    const formattedPastAffls = this.tlAffiliationFormatter(
      !formattedOtherCurrentAffls.length && !formattedAboveTheLineAffls.length
        ? groupedAffiliations.past
        : [],
      translateTaiwan
    );

    const hasAboveTheLineAffiliations = !!formattedAboveTheLineAffls.length;
    const hasPastAffiliations = !!formattedPastAffls.length;

    const isSeeingPatients = formattedAboveTheLineAffls.reduce(
      (prev, curr) => prev + curr.highestClaimCount,
      0
    );

    const locationsToDisplay = getTLAfflsToUse(
      formattedAboveTheLineAffls,
      formattedOtherCurrentAffls,
      formattedPastAffls
    );

    const affiliationsToCheck = locationsToDisplay.flatMap((locationBlock) => {
      return locationBlock.data.flatMap(
        ({ childrenAffiliation, parentAffiliation }) => [
          ...childrenAffiliation,
          ...(parentAffiliation ? [parentAffiliation] : [])
        ]
      );
    });

    const territoriesToCheck = territories ? territories : [];
    const hasAffiliationMatchingFilter = this.hasAffiliationMatchingFilters(
      affiliationsToCheck,
      input,
      matchedTerritoryAffiliationNames,
      territoriesToCheck
    );

    // if none of the affiliations that display match the search criteria. Find all of the affiliations that do.
    const searchTriggeredAffls = !hasAffiliationMatchingFilter
      ? allMatchingActiveAffiliations(
          affiliations || [],
          input,
          matchedTerritoryAffiliationNames,
          territoriesToCheck
        )
      : undefined;

    // format that the affiliations that do match the search criteria.
    const formattedRelevantLocations = this.tlAffiliationFormatter(
      searchTriggeredAffls || [],
      translateTaiwan
    );

    const formattedRelevantLocationsToReturn = formattedRelevantLocations.slice(
      0,
      2
    );

    const isPerformingTrials = affiliations.some((affl) => {
      const numberOfTrialsAtAffiliation = Number(
        affl.otherSignals?.find(
          (signal) => signal.name === "number_of_trials_at_affiliation"
        )?.value || 0
      );
      return numberOfTrialsAtAffiliation > 0;
    });

    // sometimes the data in the above compare logic is wrong and results in duplicates being shown. This intersection will ensure
    // that never happens. It's a safe guard.
    const isDuplicated = formattedRelevantLocationsToReturn.some((location) => {
      const isDisplayed = locationsToDisplay.find(
        (displayedLocation) => displayedLocation.location === location.location
      );

      return Boolean(isDisplayed);
    });

    if (locationsToDisplay.length === 0 && locations?.length) {
      const location = locations[0];

      const { languageCode } = location;
      const fields = ["city", "state", "country", "zipCode5"].map(
        (prop) => `${prop}_${languageCode}`
      );

      const locFields: {
        city: string;
        state: string;
        country: string;
      } = _(location)
        .pick(fields)
        .mapKeys((value, key) => stripLanguageSuffix(key))
        .value() as {
        city: string;
        state: string;
        country: string;
      };

      const locationString = this.translateTaiwanText(
        [locFields.city, locFields.state, locFields.country]
          .filter(Boolean)
          .join(", "),
        translateTaiwan
      );

      return {
        locationsToDisplay: [
          {
            location: locationString,
            highestClaimCount: 0,
            data: [],
            isPerformingTrials: false,
            tlAffiliationScore: 0
          }
        ],
        hasAffiliationMatchingFilter: false,
        isSeeingPatients: 0,
        hasAboveTheLineAffiliations: false,
        formattedRelevantLocations: [],
        hasPastAffiliations: false,
        isPerformingTrials: false
      };
    }

    return {
      locationsToDisplay,
      hasAffiliationMatchingFilter: isDuplicated
        ? false
        : hasAffiliationMatchingFilter,
      isSeeingPatients,
      hasAboveTheLineAffiliations,
      formattedRelevantLocations: isDuplicated
        ? []
        : formattedRelevantLocationsToReturn,
      hasPastAffiliations,
      isPerformingTrials
    };
  }
}
