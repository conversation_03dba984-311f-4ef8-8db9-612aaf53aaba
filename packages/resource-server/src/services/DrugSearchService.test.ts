import { faker } from "@faker-js/faker";
import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import { DrugSearchService } from "./DrugSearchService";
import { GrpcDrugTypeEnum } from "@h1nyc/search-sdk";

describe("DrugSearchService", () => {
  describe("searchDrugValues", () => {
    it("should search drug property", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticDrugValueIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);
      const suggestions = [faker.datatype.string()];
      elasticSearchService.query.mockResolvedValue({
        took: 2,
        timed_out: false,
        _shards: {
          total: 5,
          successful: 5,
          skipped: 0,
          failed: 0
        },
        hits: {
          total: {
            value: 0,
            relation: "eq"
          },
          max_score: undefined,
          hits: [
            {
              _id: faker.datatype.string(),
              _index: faker.datatype.string(),
              _score: faker.datatype.number(),
              _source: {
                value: suggestions[0],
                property: "GENERIC_NAME"
              }
            }
          ]
        }
      });
      const drugSearchService = new DrugSearchService(
        configService,
        elasticSearchService
      );

      const searchText = faker.datatype.string();
      const size = faker.datatype.number();

      const res = await drugSearchService.searchDrugValues({
        searchText,
        size
      });

      expect(res.values).toEqual([
        {
          value: suggestions[0],
          type: GrpcDrugTypeEnum.GENERIC_NAME
        }
      ]);
      expect(elasticSearchService.query).toHaveBeenCalledWith({
        index: configService.elasticDrugValueIndex,
        _source: ["value", "property"],
        size,
        query: {
          bool: {
            filter: [
              {
                multi_match: {
                  query: searchText,
                  type: "phrase_prefix",
                  operator: "AND",
                  fields: [
                    "value.saut",
                    "value.saut._2gram",
                    "value.saut._3gram"
                  ]
                }
              },
              {
                term: {
                  has_patients: true
                }
              }
            ]
          }
        },
        sort: expect.anything()
      });
    });
  });

  describe("searchGenericDrugs", () => {
    it("should filter on generic_name when type is GENERIC_NAME", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticGenericDrugIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);
      const genericNames = [faker.datatype.string(), faker.datatype.string()];
      const brandNames = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const drugClasses = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const activeIngredients = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const patientCounts = [faker.datatype.number(), faker.datatype.number()];
      const indicationsAndUsage = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const total = faker.datatype.number();

      elasticSearchService.query.mockResolvedValue({
        took: 2,
        timed_out: false,
        _shards: {
          total: 5,
          successful: 5,
          skipped: 0,
          failed: 0
        },
        hits: {
          total: {
            value: total,
            relation: "eq"
          },
          max_score: undefined,
          hits: [
            {
              _id: faker.datatype.string(),
              _source: {
                generic_name: genericNames[0],
                brand_name: brandNames[0],
                drug_class: drugClasses[0],
                active_ingredient: activeIngredients[0],
                patient_count: patientCounts[0],
                indications_and_usage: indicationsAndUsage[0]
              }
            },
            {
              _id: faker.datatype.string(),
              _source: {
                generic_name: genericNames[1],
                brand_name: brandNames[1],
                drug_class: drugClasses[1],
                active_ingredient: activeIngredients[1],
                patient_count: patientCounts[1],
                indications_and_usage: indicationsAndUsage[1]
              }
            }
          ]
        }
      } as any);
      const drugSearchService = new DrugSearchService(
        configService,
        elasticSearchService
      );

      const value = faker.datatype.string();
      const size = faker.datatype.number();
      const from = faker.datatype.number();

      const res = await drugSearchService.searchGenericDrugs({
        value,
        type: GrpcDrugTypeEnum.GENERIC_NAME,
        size,
        from
      });

      expect(res.total).toEqual(total);
      expect(res.genericDrugs).toEqual([
        {
          genericName: genericNames[0],
          brandName: brandNames[0],
          drugClass: drugClasses[0],
          activeIngredient: activeIngredients[0],
          patientCount: patientCounts[0],
          indicationsAndUsage: indicationsAndUsage[0]
        },
        {
          genericName: genericNames[1],
          brandName: brandNames[1],
          drugClass: drugClasses[1],
          activeIngredient: activeIngredients[1],
          patientCount: patientCounts[1],
          indicationsAndUsage: indicationsAndUsage[1]
        }
      ]);
      expect(elasticSearchService.query).toHaveBeenCalledWith({
        index: configService.elasticGenericDrugIndex,
        from,
        size,
        query: {
          bool: {
            filter: [
              {
                term: {
                  generic_name: value
                }
              },
              {
                range: {
                  patient_count: {
                    gt: 0
                  }
                }
              }
            ]
          }
        },
        sort: [
          {
            patient_count: {
              order: "desc"
            }
          }
        ]
      });
    });

    it("should filter on brand_name when type is BRAND_NAME", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticGenericDrugIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);
      const genericNames = [faker.datatype.string(), faker.datatype.string()];
      const brandNames = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const drugClasses = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const activeIngredients = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const patientCounts = [faker.datatype.number(), faker.datatype.number()];
      const indicationsAndUsage = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const total = faker.datatype.number();

      elasticSearchService.query.mockResolvedValue({
        took: 2,
        timed_out: false,
        _shards: {
          total: 5,
          successful: 5,
          skipped: 0,
          failed: 0
        },
        hits: {
          total: {
            value: total,
            relation: "eq"
          },
          max_score: undefined,
          hits: [
            {
              _id: faker.datatype.string(),
              _source: {
                generic_name: genericNames[0],
                brand_name: brandNames[0],
                drug_class: drugClasses[0],
                active_ingredient: activeIngredients[0],
                patient_count: patientCounts[0],
                indications_and_usage: indicationsAndUsage[0]
              }
            },
            {
              _id: faker.datatype.string(),
              _source: {
                generic_name: genericNames[1],
                brand_name: brandNames[1],
                drug_class: drugClasses[1],
                active_ingredient: activeIngredients[1],
                patient_count: patientCounts[1],
                indications_and_usage: indicationsAndUsage[1]
              }
            }
          ]
        }
      } as any);
      const drugSearchService = new DrugSearchService(
        configService,
        elasticSearchService
      );

      const value = faker.datatype.string();
      const size = faker.datatype.number();
      const from = faker.datatype.number();

      const res = await drugSearchService.searchGenericDrugs({
        value,
        type: GrpcDrugTypeEnum.BRAND_NAME,
        size,
        from
      });

      expect(res.total).toEqual(total);
      expect(res.genericDrugs).toEqual([
        {
          genericName: genericNames[0],
          brandName: brandNames[0],
          drugClass: drugClasses[0],
          activeIngredient: activeIngredients[0],
          patientCount: patientCounts[0],
          indicationsAndUsage: indicationsAndUsage[0]
        },
        {
          genericName: genericNames[1],
          brandName: brandNames[1],
          drugClass: drugClasses[1],
          activeIngredient: activeIngredients[1],
          patientCount: patientCounts[1],
          indicationsAndUsage: indicationsAndUsage[1]
        }
      ]);
      expect(elasticSearchService.query).toHaveBeenCalledWith({
        index: configService.elasticGenericDrugIndex,
        from,
        size,
        query: {
          bool: {
            filter: [
              {
                term: {
                  brand_name: value
                }
              },
              {
                range: {
                  patient_count: {
                    gt: 0
                  }
                }
              }
            ]
          }
        },
        sort: [
          {
            patient_count: {
              order: "desc"
            }
          }
        ]
      });
    });

    it("should filter on drug_class when type is DRUG_CLASS", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticGenericDrugIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);
      const genericNames = [faker.datatype.string(), faker.datatype.string()];
      const brandNames = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const drugClasses = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const activeIngredients = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const patientCounts = [faker.datatype.number(), faker.datatype.number()];
      const indicationsAndUsage = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const total = faker.datatype.number();

      elasticSearchService.query.mockResolvedValue({
        took: 2,
        timed_out: false,
        _shards: {
          total: 5,
          successful: 5,
          skipped: 0,
          failed: 0
        },
        hits: {
          total: {
            value: total,
            relation: "eq"
          },
          max_score: undefined,
          hits: [
            {
              _id: faker.datatype.string(),
              _source: {
                generic_name: genericNames[0],
                brand_name: brandNames[0],
                drug_class: drugClasses[0],
                active_ingredient: activeIngredients[0],
                patient_count: patientCounts[0],
                indications_and_usage: indicationsAndUsage[0]
              }
            },
            {
              _id: faker.datatype.string(),
              _source: {
                generic_name: genericNames[1],
                brand_name: brandNames[1],
                drug_class: drugClasses[1],
                active_ingredient: activeIngredients[1],
                patient_count: patientCounts[1],
                indications_and_usage: indicationsAndUsage[1]
              }
            }
          ]
        }
      } as any);
      const drugSearchService = new DrugSearchService(
        configService,
        elasticSearchService
      );

      const value = faker.datatype.string();
      const size = faker.datatype.number();
      const from = faker.datatype.number();

      const res = await drugSearchService.searchGenericDrugs({
        value,
        type: GrpcDrugTypeEnum.DRUG_CLASS,
        size,
        from
      });

      expect(res.total).toEqual(total);
      expect(res.genericDrugs).toEqual([
        {
          genericName: genericNames[0],
          brandName: brandNames[0],
          drugClass: drugClasses[0],
          activeIngredient: activeIngredients[0],
          patientCount: patientCounts[0],
          indicationsAndUsage: indicationsAndUsage[0]
        },
        {
          genericName: genericNames[1],
          brandName: brandNames[1],
          drugClass: drugClasses[1],
          activeIngredient: activeIngredients[1],
          patientCount: patientCounts[1],
          indicationsAndUsage: indicationsAndUsage[1]
        }
      ]);
      expect(elasticSearchService.query).toHaveBeenCalledWith({
        index: configService.elasticGenericDrugIndex,
        from,
        size,
        query: {
          bool: {
            filter: [
              {
                term: {
                  drug_class: value
                }
              },
              {
                range: {
                  patient_count: {
                    gt: 0
                  }
                }
              }
            ]
          }
        },
        sort: [
          {
            patient_count: {
              order: "desc"
            }
          }
        ]
      });
    });

    it("should filter on active_ingredient when type is ACTIVE_INGREDIENT", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticGenericDrugIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);
      const genericNames = [faker.datatype.string(), faker.datatype.string()];
      const brandNames = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const drugClasses = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const activeIngredients = [
        [faker.datatype.string(), faker.datatype.string()],
        [faker.datatype.string(), faker.datatype.string()]
      ];
      const patientCounts = [faker.datatype.number(), faker.datatype.number()];
      const indicationsAndUsage = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const total = faker.datatype.number();

      elasticSearchService.query.mockResolvedValue({
        took: 2,
        timed_out: false,
        _shards: {
          total: 5,
          successful: 5,
          skipped: 0,
          failed: 0
        },
        hits: {
          total: {
            value: total,
            relation: "eq"
          },
          max_score: undefined,
          hits: [
            {
              _id: faker.datatype.string(),
              _source: {
                generic_name: genericNames[0],
                brand_name: brandNames[0],
                drug_class: drugClasses[0],
                active_ingredient: activeIngredients[0],
                patient_count: patientCounts[0],
                indications_and_usage: indicationsAndUsage[0]
              }
            },
            {
              _id: faker.datatype.string(),
              _source: {
                generic_name: genericNames[1],
                brand_name: brandNames[1],
                drug_class: drugClasses[1],
                active_ingredient: activeIngredients[1],
                patient_count: patientCounts[1],
                indications_and_usage: indicationsAndUsage[1]
              }
            }
          ]
        }
      } as any);
      const drugSearchService = new DrugSearchService(
        configService,
        elasticSearchService
      );

      const value = faker.datatype.string();
      const size = faker.datatype.number();
      const from = faker.datatype.number();

      const res = await drugSearchService.searchGenericDrugs({
        value,
        type: GrpcDrugTypeEnum.ACTIVE_INGREDIENT,
        size,
        from
      });

      expect(res.total).toEqual(total);
      expect(res.genericDrugs).toEqual([
        {
          genericName: genericNames[0],
          brandName: brandNames[0],
          drugClass: drugClasses[0],
          activeIngredient: activeIngredients[0],
          patientCount: patientCounts[0],
          indicationsAndUsage: indicationsAndUsage[0]
        },
        {
          genericName: genericNames[1],
          brandName: brandNames[1],
          drugClass: drugClasses[1],
          activeIngredient: activeIngredients[1],
          patientCount: patientCounts[1],
          indicationsAndUsage: indicationsAndUsage[1]
        }
      ]);
      expect(elasticSearchService.query).toHaveBeenCalledWith({
        index: configService.elasticGenericDrugIndex,
        from,
        size,
        query: {
          bool: {
            filter: [
              {
                term: {
                  active_ingredient: value
                }
              },
              {
                range: {
                  patient_count: {
                    gt: 0
                  }
                }
              }
            ]
          }
        },
        sort: [
          {
            patient_count: {
              order: "desc"
            }
          }
        ]
      });
    });

    it("should throw error when type contain unrecognized value", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticDrugValueIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const drugSearchService = new DrugSearchService(
        configService,
        elasticSearchService
      );

      const value = faker.datatype.string();
      const size = faker.datatype.number();

      await expect(
        drugSearchService.searchGenericDrugs({
          value,
          type: GrpcDrugTypeEnum.UNRECOGNIZED,
          size,
          from: 0
        })
      ).rejects.toThrow(new Error("Unrecognized drug type: -1"));

      expect(elasticSearchService.query).not.toHaveBeenCalled();
    });
  });

  describe("autocompleteGenericDrugs", () => {
    it("should search on user input", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticGenericDrugIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);
      const suggestions = [faker.datatype.string(), faker.datatype.string()];
      const indicationsAndUsage = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      elasticSearchService.query.mockResolvedValue({
        took: 2,
        timed_out: false,
        _shards: {
          total: 5,
          successful: 5,
          skipped: 0,
          failed: 0
        },
        hits: {
          total: {
            value: 0,
            relation: "eq"
          },
          max_score: undefined,
          hits: []
        },
        suggest: {
          autocomplete: [
            {
              text: faker.datatype.string(),
              offset: 0,
              length: faker.datatype.number(),
              options: [
                {
                  text: suggestions[0],
                  _id: faker.datatype.string(),
                  _index: faker.datatype.string(),
                  _score: faker.datatype.number(),
                  _source: {
                    generic_name: suggestions[0],
                    indications_and_usage: indicationsAndUsage[0]
                  }
                },
                {
                  text: suggestions[1],
                  _id: faker.datatype.string(),
                  _index: faker.datatype.string(),
                  _score: faker.datatype.number(),
                  _source: {
                    generic_name: suggestions[1],
                    indications_and_usage: indicationsAndUsage[1]
                  }
                }
              ]
            }
          ]
        }
      });
      const drugSearchService = new DrugSearchService(
        configService,
        elasticSearchService
      );

      const searchText = faker.datatype.string();
      const size = faker.datatype.number();

      const res = await drugSearchService.autocompleteGenericDrugs({
        searchText,
        size
      });

      expect(res.genericDrugs).toEqual([
        {
          genericName: suggestions[0],
          indicationsAndUsage: indicationsAndUsage[0]
        },
        {
          genericName: suggestions[1],
          indicationsAndUsage: indicationsAndUsage[1]
        }
      ]);
      expect(elasticSearchService.query).toHaveBeenCalledWith({
        index: configService.elasticGenericDrugIndex,
        _source: ["generic_name", "indications_and_usage"],
        suggest: {
          autocomplete: {
            prefix: searchText,
            completion: {
              field: "generic_name.autocomplete_search",
              size,
              contexts: {
                has_patients: "true"
              }
            }
          }
        }
      });
    });
  });
});
