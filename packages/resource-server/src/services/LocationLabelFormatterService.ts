import { Service } from "typedi";
import { ConfigService } from "./ConfigService";
import * as _ from "lodash";
import { CHINESE, JAPANESE } from "./LanguageDetectService";

enum LocationFieldType {
  COUNTRY,
  REGION,
  COUNTY,
  CITY,
  DISTRICT,
  POS<PERSON>L_CODE
}

const numberOfFields: Readonly<Record<keyof typeof LocationFieldType, number>> =
  {
    COUNTRY: 1,
    REGION: 2,
    COUNTY: 3,
    CITY: 4,
    DISTRICT: 5,
    POSTAL_CODE: 6
  };

const fieldOffsets: Readonly<Record<keyof typeof LocationFieldType, number>> = {
  COUNTRY: 0,
  REGION: 1,
  COUNTY: 2,
  CITY: 3,
  DISTRICT: 4,
  POSTAL_CODE: 5
};

export const UNITED_STATES = "us";
const DELIMITER = "|";
const DISPLAY_DELIMITER = ", ";
const JAPAN_KANJI = "日本";
const CHINA_KANJI = "中国";

/**
 * Handles converting pipe-delimited location filters into values understand by the UI.
 * This will eventually go away once the UI supports the label/id/count format.  It is
 * a very unfortunate special case where display logic has to be built into the backend.
 */
@Service()
export class LocationLabelFormatterService {
  private countryNamesKeyedByCountryCode: Readonly<Record<string, string>>;
  private countryCodesKeyedByCountryNames: Readonly<Record<string, string>>;
  private regionNamesKeyedByCountryAndRegionCode: Readonly<
    Record<string, string>
  >;
  private regionCodesKeyedByCountryCodeAndRegionName: Readonly<
    Record<string, string>
  >;

  constructor(configService: ConfigService) {
    this.countryNamesKeyedByCountryCode = _.invert(configService.countries());
    this.countryCodesKeyedByCountryNames = configService.countries();
    this.regionNamesKeyedByCountryAndRegionCode =
      this.parseRegionsToDelimiterKeyedMap(configService.regions());
    this.regionCodesKeyedByCountryCodeAndRegionName =
      this.parseRegionsToCountryCodeAndRegionNameKeyedMap(
        configService.regions()
      );
  }

  private parseRegionsToCountryCodeAndRegionNameKeyedMap(
    regionsByCountryCode: Record<string, Record<string, string>>
  ) {
    const map: Record<string, string> = {};

    for (const [countryCode, countryRegions] of Object.entries(
      regionsByCountryCode
    )) {
      for (const [regionName, regionCode] of Object.entries(countryRegions)) {
        const countryCodeRegionKey = [countryCode, regionName].join(DELIMITER);
        map[countryCodeRegionKey.toLowerCase()] = regionCode;
      }
    }

    return map;
  }

  private parseRegionsToDelimiterKeyedMap(
    regionsByCountryCode: Record<string, Record<string, string>>
  ) {
    const map: Record<string, string> = {};

    for (const [countryCode, countryRegions] of Object.entries(
      regionsByCountryCode
    )) {
      for (const [regionName, regionCode] of Object.entries(countryRegions)) {
        const countryRegionKey = [countryCode, regionCode].join(DELIMITER);
        map[countryRegionKey] = regionName;
      }
    }

    return map;
  }

  city(value: string): string {
    const [countryCode, regionValue, county, city] = value.split(
      DELIMITER,
      numberOfFields.CITY
    );

    if (!city) {
      return value;
    }

    const region = this.toCountrySpecifcRegion(countryCode, regionValue);
    const country =
      this.countryNamesKeyedByCountryCode[countryCode] || countryCode;

    return [city, county, region, country]
      .filter(Boolean)
      .join(DISPLAY_DELIMITER);
  }

  cityWithoutCounty(value: string): string {
    const [countryCode, regionValue, city] = value.split(
      DELIMITER,
      numberOfFields.CITY
    );

    if (!city) {
      return value;
    }

    const region = this.toCountrySpecifcRegion(countryCode, regionValue);
    const country =
      this.countryNamesKeyedByCountryCode[countryCode] || countryCode;

    return [city, region, country].filter(Boolean).join(DISPLAY_DELIMITER);
  }

  private toCountrySpecifcRegion(countryCode: string, region: string) {
    if (countryCode === UNITED_STATES) {
      return region;
    }

    const pipeDelimitedCountryCodeAndRegion = [countryCode, region].join(
      DELIMITER
    );

    return (
      this.regionNamesKeyedByCountryAndRegionCode[
        pipeDelimitedCountryCodeAndRegion
      ] || region
    );
  }

  region(value: string): string {
    if (this.regionNamesKeyedByCountryAndRegionCode[value]) {
      return this.regionNamesKeyedByCountryAndRegionCode[value];
    }

    if (value.includes(DELIMITER)) {
      return value.split(DELIMITER, numberOfFields.REGION)[fieldOffsets.REGION];
    }

    return value;
  }

  getRegionCodeAndCountryCode(countryCode: string, region: string) {
    const pipeDelimitedCountryCodeAndRegion = [
      countryCode.toLowerCase(),
      region.toLowerCase()
    ].join(DELIMITER);

    return [
      countryCode,
      this.regionCodesKeyedByCountryCodeAndRegionName[
        pipeDelimitedCountryCodeAndRegion
      ] || region
    ].join(DELIMITER);
  }

  postalCode(value: string): string {
    const fields = value.split(DELIMITER, numberOfFields.POSTAL_CODE);

    return fields[fieldOffsets.POSTAL_CODE] || value;
  }

  postalCodeWithoutCountyAndDistrict(value: string): string {
    const fields = value.split(DELIMITER, numberOfFields.POSTAL_CODE - 2);

    return fields[fieldOffsets.POSTAL_CODE - 2] || value;
  }

  country(value: string, userPreferredLanguage?: string): string {
    if (value === "jp") {
      return this.getJapanTranslation(userPreferredLanguage);
    }

    if (value === "cn") {
      return this.getChinaTranslation(userPreferredLanguage);
    }

    return this.countryNamesKeyedByCountryCode[value] || value;
  }

  getCountryCode(countryName: string): string {
    return this.countryCodesKeyedByCountryNames[countryName] || countryName;
  }

  getJapanTranslation(userPreferredLanguage?: string) {
    if (
      userPreferredLanguage === JAPANESE ||
      userPreferredLanguage === CHINESE
    ) {
      return JAPAN_KANJI;
    } else {
      return "Japan";
    }
  }

  getChinaTranslation(userPreferredLanguage?: string) {
    if (
      userPreferredLanguage === JAPANESE ||
      userPreferredLanguage === CHINESE
    ) {
      return CHINA_KANJI;
    } else {
      return "China";
    }
  }
}
