import {
  IndicationsSearchServiceImplementation,
  IndicationSearchRootRpcInput,
  Indications,
  IndicationSource,
  IndicationGetSubTreesRpcInput,
  IndicationSortBy,
  IndicationSearchTreesByQueryRpcInput,
  IndicationSearchByQueryRpcInput,
  IndicationType
} from "@h1nyc/search-sdk";
import { Service } from "typedi";

import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";

@Service()
export class IndicationsSearchGrpcHandlerService
  implements IndicationsSearchServiceImplementation
{
  constructor(private indicationsSearchService: IndicationsTreeSearchService) {}

  async searchRootIndications(
    request: IndicationSearchRootRpcInput
  ): Promise<Indications> {
    const indications =
      await this.indicationsSearchService.searchRootIndications({
        ...request,
        indicationSource: request.indicationSource as IndicationSource[]
      });
    return { indications };
  }

  async searchByQuery(
    request: IndicationSearchByQueryRpcInput
  ): Promise<Indications> {
    const indications =
      await this.indicationsSearchService.searchIndicationsByQuery({
        ...request,
        sortBy: request.sortBy as IndicationSortBy,
        indicationSource: request.indicationSource as IndicationSource[],
        indicationType: request.indicationType as IndicationType[]
      });

    return { indications };
  }

  async getSubTrees(
    request: IndicationGetSubTreesRpcInput
  ): Promise<Indications> {
    const indications = await this.indicationsSearchService.getSubTreesForRoot({
      ...request,
      indicationSource: request.indicationSource as IndicationSource[],
      sortBy: request.sortBy as IndicationSortBy
    });
    return { indications };
  }

  async searchIndicationTreesByQuery(
    request: IndicationSearchTreesByQueryRpcInput
  ): Promise<Indications> {
    const indications =
      await this.indicationsSearchService.searchIndicationTreesByQuery({
        ...request,
        sortBy: request.sortBy as IndicationSortBy,
        indicationSource: request.indicationSource as IndicationSource[],
        indicationType: request.indicationType as IndicationType[]
      });

    return { indications };
  }
}
