import {
  Chat<PERSON><PERSON>ple<PERSON>,
  ChatCompletionTool,
  ChatCompletionToolMessageParam
} from "openai/resources/index";

import { Client } from "@modelcontextprotocol/sdk/client/index.js";

import { createLogger } from "../lib/Logger";

export class OpenAIChatAdaptor {
  private readonly logger = createLogger(this);
  private readonly client: Client;
  private readonly options: {
    truncateDescriptionLength: number;
    strict?: boolean;
  };
  constructor(
    client: Client,
    options = {
      truncateDescriptionLength: 1024
    }
  ) {
    this.client = client;
    this.options = options;
  }

  async listTools(): Promise<ChatCompletionTool[]> {
    const toolResult = await this.client.listTools();
    return toolResult.tools.map((tool) => ({
      type: "function",
      function: {
        name: tool.name,
        description: tool.description?.slice(
          0,
          this.options?.truncateDescriptionLength
        ),
        parameters: tool.inputSchema.properties
          ? tool.inputSchema
          : { ...tool.inputSchema, properties: {} },
        strict: this.options?.strict ?? false
      }
    }));
  }

  async callTool(
    response: ChatCompletion & {
      _request_id?: string | null;
    }
  ): Promise<ChatCompletionToolMessageParam[]> {
    if (response.choices.length !== 1) {
      throw new Error("Multiple choices not supported");
    }

    const choice = response.choices[0];
    if (!choice.message.tool_calls) {
      return [];
    }

    const toolCalls = choice.message.tool_calls;
    const results = await Promise.all(
      toolCalls.map(async (toolCall) => {
        this.logger.debug(
          {
            tool: toolCall.function.name,
            arguments: JSON.parse(toolCall.function.arguments)
          },
          `Calling tool: ${toolCall.function.name}`
        );
        return await this.client.callTool({
          name: toolCall.function.name,
          arguments: JSON.parse(toolCall.function.arguments)
        });
      })
    );

    return results.map((result, index) => ({
      role: "tool",
      content: result.content as string,
      tool_call_id: toolCalls[index].id
    }));
  }
}
