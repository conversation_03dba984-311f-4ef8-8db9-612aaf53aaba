import { Service } from "typedi";

import { ElasticSearchService } from "./ElasticSearchService";
import { ElasticTrialSearchService } from "./ElasticTrialSearchService";

@Service()
export class HealthService {
  constructor(
    private elasticService: ElasticSearchService,
    private elasticTrialService: ElasticTrialSearchService
  ) {}

  async check(): Promise<void> {
    await Promise.all([
      this.elasticService.ping(),
      this.elasticTrialService.ping()
    ]);
  }
}
