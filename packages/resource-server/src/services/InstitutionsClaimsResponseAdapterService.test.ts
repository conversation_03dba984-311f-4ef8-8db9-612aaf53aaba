import {
  SearchHit,
  SearchResponse,
  SearchTotalHitsRelation
} from "@elastic/elasticsearch/lib/api/types";
import { faker } from "@faker-js/faker";
import {
  EntitySortOptions,
  SortDirection
} from "@h1nyc/account-sdk/dist/interfaces/Sort";
import { IolClaimSortOptions, IolClaimType } from "@h1nyc/search-sdk";
import _ from "lodash";
import { InstitutionsClaimsResponseAdapterService } from "./InstitutionsClaimsResponseAdapterService";

const eq: SearchTotalHitsRelation = "eq";

function generateMockInstitutionClaimsHit(
  diagnosesHits: Array<SearchHit<Record<string, any>>>,
  proceduresHits: Array<SearchHit<Record<string, any>>>,
  prescriptionsHits: Array<SearchHit<Record<string, any>>>
) {
  const instituteClaims = {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),

    inner_hits: {
      diagnoses: {
        hits: {
          total: {
            value: diagnosesHits.length,
            relation: eq
          },
          max_score: faker.datatype.number(),
          hits: diagnosesHits
        }
      },
      procedures: {
        hits: {
          total: {
            value: proceduresHits.length,
            relation: eq
          },
          max_score: faker.datatype.number(),
          hits: proceduresHits
        }
      },
      prescriptions: {
        hits: {
          total: {
            value: prescriptionsHits.length,
            relation: eq
          },
          max_score: faker.datatype.number(),
          hits: prescriptionsHits
        }
      }
    }
  };
  return instituteClaims;
}

function generateMockDiagnosesInnerHit(
  count?: number,
  claimsField = "count"
): any {
  const diagnoses = {
    _source: {
      code_eng: faker.datatype.string(),
      description_eng: faker.datatype.string()
    },
    fields: {
      "diagnoses.totalInState": [faker.datatype.number()],
      "diagnoses.stateRank": [faker.datatype.number()],
      "diagnoses.totalInCountry": [faker.datatype.number()],
      [`diagnoses.${claimsField}`]: [count ?? faker.datatype.number()],
      "diagnoses.countryRank": [faker.datatype.number()]
    }
  };
  return diagnoses;
}
function generateMockProceduresInnerHit(
  count?: number,
  claimsField = "count"
): any {
  const procedures = {
    _source: {
      code_eng: faker.datatype.string(),
      description_eng: faker.datatype.string()
    },
    fields: {
      "procedures.totalInState": [faker.datatype.number()],
      "procedures.stateRank": [faker.datatype.number()],
      "procedures.totalInCountry": [faker.datatype.number()],
      [`procedures.${claimsField}`]: [count ?? faker.datatype.number()],
      "procedures.countryRank": [faker.datatype.number()]
    }
  };
  return procedures;
}

function generateMockPrescriptionsInnerHit(): any {
  const prescriptions = {
    _source: {
      generic_name: faker.datatype.string()
    },
    fields: {
      "prescriptions.num_prescriptions": [faker.datatype.number()]
    }
  };
  return prescriptions;
}

function generateMockElasticsearchResponse(
  hits: Array<SearchHit> = []
): SearchResponse {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: hits.length,
        relation: eq
      },
      max_score: faker.datatype.number(),
      hits
    }
  };
}

describe("InstitutionClaimsResponseAdapter", () => {
  it("should return empty response when total number of inner hits is zero for diagnoses", () => {
    const searchResponse = generateMockElasticsearchResponse([
      generateMockInstitutionClaimsHit([], [], [])
    ]);
    const responseAdapter = new InstitutionsClaimsResponseAdapterService();
    const iolId = faker.datatype.string();
    const page = {
      offset: faker.datatype.number(),
      limit: faker.datatype.number()
    };

    const sort: EntitySortOptions<IolClaimSortOptions> = {
      field: "claimsCount",
      direction: SortDirection.Desc
    };

    const type = IolClaimType.Diagnosis;
    const userId = faker.datatype.string();
    const projectId = faker.datatype.string();
    const iolClaimsInput = {
      iolId,
      userId,
      projectId,
      page,
      sort,
      filters: {
        codes: [],
        descriptions: [],
        count: {},
        type,
        rank: {},
        regionRank: {},
        keywords: []
      }
    };
    const parsedResponse =
      responseAdapter.adaptToInstitutionClaimsSearchResponse(
        iolClaimsInput,
        searchResponse
      );

    expect(parsedResponse).toEqual({
      type: "diagnoses",
      count: 0,
      items: []
    });
  });

  it("should return empty response when total number of inner hits is zero for diagnoses", () => {
    const searchResponse = generateMockElasticsearchResponse([
      generateMockInstitutionClaimsHit([], [], [])
    ]);
    const responseAdapter = new InstitutionsClaimsResponseAdapterService();
    const iolId = faker.datatype.string();
    const page = {
      offset: faker.datatype.number(),
      limit: faker.datatype.number()
    };

    const sort: EntitySortOptions<IolClaimSortOptions> = {
      field: "claimsCount",
      direction: SortDirection.Desc
    };

    const type = IolClaimType.Procedure;

    const userId = faker.datatype.string();
    const projectId = faker.datatype.string();
    const iolClaimsInput = {
      iolId,
      userId,
      projectId,
      page,
      sort,
      filters: {
        codes: [],
        descriptions: [],
        count: {},
        type,
        rank: {},
        regionRank: {},
        keywords: []
      }
    };
    const parsedResponse =
      responseAdapter.adaptToInstitutionClaimsSearchResponse(
        iolClaimsInput,
        searchResponse
      );

    expect(parsedResponse).toEqual({
      type: "procedures",
      count: 0,
      items: []
    });
  });

  it("should correctly parse response when total number of inner hits is greater than zero", () => {
    const diagnosesInnerHit = generateMockDiagnosesInnerHit();

    const searchResponse = generateMockElasticsearchResponse([
      generateMockInstitutionClaimsHit([diagnosesInnerHit], [], [])
    ]);
    const responseAdapter = new InstitutionsClaimsResponseAdapterService();
    const iolId = faker.datatype.string();
    const page = {
      offset: faker.datatype.number(),
      limit: faker.datatype.number()
    };
    const sort: EntitySortOptions<IolClaimSortOptions> = {
      field: "claimsCount",
      direction: SortDirection.Desc
    };

    const type = IolClaimType.Diagnosis;
    const userId = faker.datatype.string();
    const projectId = faker.datatype.string();
    const iolClaimsInput = {
      iolId,
      userId,
      projectId,
      page,
      sort,
      filters: {
        codes: [],
        descriptions: [],
        count: {},
        type,
        rank: {},
        regionRank: {},
        keywords: []
      }
    };
    const parsedResponse =
      responseAdapter.adaptToInstitutionClaimsSearchResponse(
        iolClaimsInput,
        searchResponse
      );

    expect(parsedResponse).toEqual({
      type: "diagnoses",
      count: 1,
      items: [
        {
          code: diagnosesInnerHit._source.code_eng,
          claimsCount: diagnosesInnerHit.fields["diagnoses.count"][0],
          description: diagnosesInnerHit._source.description_eng,
          iolId: iolId,
          rank: diagnosesInnerHit.fields["diagnoses.countryRank"][0],
          totalInCountry:
            diagnosesInnerHit.fields["diagnoses.totalInCountry"][0],
          regionRank: diagnosesInnerHit.fields["diagnoses.stateRank"][0],
          totalInRegion: diagnosesInnerHit.fields["diagnoses.totalInState"][0],
          genericName: ""
        }
      ]
    });
  });

  it("should correctly parse response when total number of inner hits is greater than zero for procedure", () => {
    const proceduresInnerHit = generateMockProceduresInnerHit();

    const searchResponse = generateMockElasticsearchResponse([
      generateMockInstitutionClaimsHit([], [proceduresInnerHit], [])
    ]);
    const responseAdapter = new InstitutionsClaimsResponseAdapterService();
    const iolId = faker.datatype.string();
    const page = {
      offset: faker.datatype.number(),
      limit: faker.datatype.number()
    };
    const sort: EntitySortOptions<IolClaimSortOptions> = {
      field: "claimsCount",
      direction: SortDirection.Desc
    };

    const type = IolClaimType.Procedure;
    const userId = faker.datatype.string();
    const projectId = faker.datatype.string();
    const iolClaimsInput = {
      iolId,
      userId,
      projectId,
      page,
      sort,
      filters: {
        codes: [],
        descriptions: [],
        count: {},
        type,
        rank: {},
        regionRank: {},
        keywords: []
      }
    };
    const parsedResponse =
      responseAdapter.adaptToInstitutionClaimsSearchResponse(
        iolClaimsInput,
        searchResponse
      );

    expect(parsedResponse).toEqual({
      type: "procedures",
      count: 1,
      items: [
        {
          code: proceduresInnerHit._source.code_eng,
          claimsCount: proceduresInnerHit.fields["procedures.count"][0],
          description: proceduresInnerHit._source.description_eng,
          iolId: iolId,
          rank: proceduresInnerHit.fields["procedures.countryRank"][0],
          totalInCountry:
            proceduresInnerHit.fields["procedures.totalInCountry"][0],
          regionRank: proceduresInnerHit.fields["procedures.stateRank"][0],
          totalInRegion:
            proceduresInnerHit.fields["procedures.totalInState"][0],
          genericName: ""
        }
      ]
    });
  });

  describe("unique patient count", () => {
    it("should correctly fetch unique patient count when flag is turned on for diagnoses", () => {
      const diagnosesInnerHit = generateMockDiagnosesInnerHit(
        undefined,
        "internalUniqueCount"
      );

      const searchResponse = generateMockElasticsearchResponse([
        generateMockInstitutionClaimsHit([diagnosesInnerHit], [], [])
      ]);
      const responseAdapter = new InstitutionsClaimsResponseAdapterService();
      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };

      const type = IolClaimType.Diagnosis;
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {},
          type,
          rank: {},
          regionRank: {},
          keywords: []
        }
      };
      const parsedResponse =
        responseAdapter.adaptToInstitutionClaimsSearchResponse(
          iolClaimsInput,
          searchResponse,
          {
            shouldUseUniquePatientCount: true
          }
        );

      expect(parsedResponse).toEqual({
        type: "diagnoses",
        count: 1,
        items: [
          {
            code: diagnosesInnerHit._source.code_eng,
            claimsCount:
              diagnosesInnerHit.fields["diagnoses.internalUniqueCount"][0],
            description: diagnosesInnerHit._source.description_eng,
            iolId: iolId,
            rank: diagnosesInnerHit.fields["diagnoses.countryRank"][0],
            totalInCountry:
              diagnosesInnerHit.fields["diagnoses.totalInCountry"][0],
            regionRank: diagnosesInnerHit.fields["diagnoses.stateRank"][0],
            totalInRegion:
              diagnosesInnerHit.fields["diagnoses.totalInState"][0],
            genericName: ""
          }
        ]
      });
    });
    it("should correctly fetch unique patient count when flag is turned on for procedures", () => {
      const proceduresInnerHit = generateMockProceduresInnerHit(
        undefined,
        "internalUniqueCount"
      );

      const searchResponse = generateMockElasticsearchResponse([
        generateMockInstitutionClaimsHit([], [proceduresInnerHit], [])
      ]);
      const responseAdapter = new InstitutionsClaimsResponseAdapterService();
      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };

      const type = IolClaimType.Procedure;
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {},
          type,
          rank: {},
          regionRank: {},
          keywords: []
        }
      };
      const parsedResponse =
        responseAdapter.adaptToInstitutionClaimsSearchResponse(
          iolClaimsInput,
          searchResponse,
          {
            shouldUseUniquePatientCount: true
          }
        );

      expect(parsedResponse).toEqual({
        type: "procedures",
        count: 1,
        items: [
          {
            code: proceduresInnerHit._source.code_eng,
            claimsCount:
              proceduresInnerHit.fields["procedures.internalUniqueCount"][0],
            description: proceduresInnerHit._source.description_eng,
            iolId: iolId,
            rank: proceduresInnerHit.fields["procedures.countryRank"][0],
            totalInCountry:
              proceduresInnerHit.fields["procedures.totalInCountry"][0],
            regionRank: proceduresInnerHit.fields["procedures.stateRank"][0],
            totalInRegion:
              proceduresInnerHit.fields["procedures.totalInState"][0],
            genericName: ""
          }
        ]
      });
    });
  });

  describe("prescriptions", () => {
    it("should correctly parse response when total number of inner hits is greater than zero", () => {
      const prescriptionsInnerHit = generateMockPrescriptionsInnerHit();

      const searchResponse = generateMockElasticsearchResponse([
        generateMockInstitutionClaimsHit([], [], [prescriptionsInnerHit])
      ]);
      const responseAdapter = new InstitutionsClaimsResponseAdapterService();
      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };

      const type = IolClaimType.Prescriptions;
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {},
          type,
          rank: {},
          regionRank: {},
          keywords: []
        }
      };
      const parsedResponse =
        responseAdapter.adaptToInstitutionClaimsSearchResponse(
          iolClaimsInput,
          searchResponse
        );

      expect(parsedResponse).toEqual({
        type: "prescriptions",
        count: 1,
        items: [
          {
            code: "",
            claimsCount:
              prescriptionsInnerHit.fields[
                "prescriptions.num_prescriptions"
              ][0],
            description: "",
            iolId: iolId,
            rank: 0,
            totalInCountry: 0,
            regionRank: undefined,
            totalInRegion: undefined,
            genericName: prescriptionsInnerHit._source.generic_name
          }
        ]
      });
    });
  });
});
