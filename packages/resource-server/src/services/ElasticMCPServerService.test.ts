import { faker } from "@faker-js/faker";
import { createMockInstance } from "../util/TestUtils";
import { ElasticMCPServerService } from "./ElasticMCPServerService";
import { ElasticSearchService } from "./ElasticSearchService";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";

jest.mock("@modelcontextprotocol/sdk/server/mcp.js");
jest.mock("@modelcontextprotocol/sdk/server/stdio.js");
jest.mock("../lib/Logger", () => ({
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  })
}));

describe("ElasticMCPServerService", () => {
  let elasticMCPServerService: ElasticMCPServerService;
  let mockElasticService: jest.Mocked<ElasticSearchService>;
  let mockServer: jest.Mocked<McpServer>;

  beforeEach(() => {
    mockElasticService = createMockInstance(ElasticSearchService);
    elasticMCPServerService = new ElasticMCPServerService(mockElasticService);

    mockServer = new McpServer({
      name: faker.datatype.string(),
      version: faker.datatype.string()
    }) as jest.Mocked<McpServer>;
    mockServer.tool = jest.fn();
    mockServer.connect = jest.fn().mockResolvedValue(undefined);

    (McpServer as jest.Mock).mockImplementation(() => mockServer);
  });

  describe("createServer", () => {
    it("should create a new McpServer with correct config", async () => {
      await elasticMCPServerService.createServer();

      expect(McpServer).toHaveBeenCalledWith({
        name: expect.any(String),
        version: expect.any(String)
      });
    });

    it("should return the created server", async () => {
      const result = await elasticMCPServerService.createServer();

      expect(result).toBe(mockServer);
    });
  });

  describe("addToolsToServer", () => {
    it("should add all tools to the server", async () => {
      await elasticMCPServerService.addToolsToServer(mockServer);

      // Verify each tool is added
      expect(mockServer.tool).toHaveBeenCalledTimes(3);

      // Verify list_indices tool
      expect(mockServer.tool).toHaveBeenCalledWith(
        expect.stringContaining("list_indices"),
        expect.any(String),
        expect.any(Object),
        expect.any(Function)
      );

      // Verify get_mappings tool
      expect(mockServer.tool).toHaveBeenCalledWith(
        expect.stringContaining("get_mappings"),
        expect.any(String),
        expect.any(Object),
        expect.any(Function)
      );

      // Verify search_elasticsearch tool
      expect(mockServer.tool).toHaveBeenCalledWith(
        expect.stringContaining("search_elasticsearch"),
        expect.any(String),
        expect.any(Object),
        expect.any(Function)
      );
    });
  });

  describe("startServer", () => {
    it("should create server, add tools, and connect to transport", async () => {
      const createServerSpy = jest.spyOn(
        elasticMCPServerService,
        "createServer"
      );
      const addToolsToServerSpy = jest.spyOn(
        elasticMCPServerService,
        "addToolsToServer"
      );

      await elasticMCPServerService.startServer();

      expect(createServerSpy).toHaveBeenCalled();
      expect(addToolsToServerSpy).toHaveBeenCalledWith(mockServer);
      expect(StdioServerTransport).toHaveBeenCalled();
      expect(mockServer.connect).toHaveBeenCalled();
    });
  });
});
