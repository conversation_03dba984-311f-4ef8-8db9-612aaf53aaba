import { ConfigService } from "./ConfigService";
import { createMockInstance } from "../util/TestUtils";
import { CompletionSuggesterService } from "./CompletionSuggesterService";
import { ElasticSearchCompletionService } from "./ElasticSearchCompletionService";
import { faker } from "@faker-js/faker";

describe("CompletionSuggesterService", () => {
  describe("getCompletionSuggestions", () => {
    it("should not suggest for empty query", () => {
      const configService = createMockInstance(ConfigService);
      const elasticCompletionService = createMockInstance(
        ElasticSearchCompletionService
      );
      const err = new Error("elasticCompletionService.query threw an error");
      elasticCompletionService.query.mockRejectedValue(err);

      const completionSuggesterService = new CompletionSuggesterService(
        configService,
        elasticCompletionService
      );

      const invalidQueries = [""];

      invalidQueries.forEach(async (invalidQuery) => {
        const suggestions =
          await completionSuggesterService.getCompletionSuggestions(
            invalidQuery as any
          );
        expect(suggestions).toEqual([]);
      });
    });
  });

  it("should return suggests for valid query", async () => {
    const indexName = faker.datatype.uuid();
    const configService = createMockInstance(ConfigService);
    configService.elasticCompletionSuggesterIndex = indexName;
    const elasticCompletionService = createMockInstance(
      ElasticSearchCompletionService
    );
    const suggestions = [
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string()
    ];
    const query = faker.random.alpha(10);

    elasticCompletionService.query.mockResolvedValue({
      took: 10,
      timed_out: false,
      _shards: {
        total: 1,
        successful: 1,
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        max_score: undefined,
        hits: []
      },
      suggest: {
        autocomplete: [
          {
            text: query,
            offset: faker.datatype.number(),
            length: faker.datatype.number(),
            options: [
              {
                text: suggestions[0],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              },
              {
                text: suggestions[1],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              },
              {
                text: suggestions[2],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              }
            ]
          }
        ]
      }
    });

    const completionSuggesterService = new CompletionSuggesterService(
      configService,
      elasticCompletionService
    );

    const suggestionResult =
      await completionSuggesterService.getCompletionSuggestions(query);

    expect(elasticCompletionService.query).toHaveBeenCalledWith({
      index: indexName,
      _source: false,
      suggest: {
        autocomplete: expect.objectContaining({
          prefix: query
        })
      }
    });
    expect(suggestionResult).toEqual(suggestions);
  });

  it("should return no suggests if suggest field is undefined", async () => {
    const indexName = faker.datatype.uuid();
    const configService = createMockInstance(ConfigService);
    configService.elasticCompletionSuggesterIndex = indexName;
    const elasticCompletionService = createMockInstance(
      ElasticSearchCompletionService
    );

    elasticCompletionService.query.mockResolvedValue({
      took: 10,
      timed_out: false,
      _shards: {
        total: 1,
        successful: 1,
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        max_score: undefined,
        hits: []
      },
      suggest: undefined
    });

    const completionSuggesterService = new CompletionSuggesterService(
      configService,
      elasticCompletionService
    );

    const query = faker.random.alpha(10);

    const suggestionResult =
      await completionSuggesterService.getCompletionSuggestions(query);

    expect(elasticCompletionService.query).toHaveBeenCalledWith({
      index: indexName,
      _source: false,
      suggest: {
        autocomplete: expect.objectContaining({
          prefix: query
        })
      }
    });
    expect(suggestionResult).toEqual([]);
  });

  it("should return no suggests if autocomplete field is undefined", async () => {
    const indexName = faker.datatype.uuid();
    const configService = createMockInstance(ConfigService);
    configService.elasticCompletionSuggesterIndex = indexName;
    const elasticCompletionService = createMockInstance(
      ElasticSearchCompletionService
    );

    elasticCompletionService.query.mockResolvedValue({
      took: 10,
      timed_out: false,
      _shards: {
        total: 1,
        successful: 1,
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        max_score: undefined,
        hits: []
      },
      suggest: {}
    });

    const completionSuggesterService = new CompletionSuggesterService(
      configService,
      elasticCompletionService
    );

    const query = faker.random.alpha(10);

    const suggestionResult =
      await completionSuggesterService.getCompletionSuggestions(query);

    expect(elasticCompletionService.query).toHaveBeenCalledWith({
      index: indexName,
      _source: false,
      suggest: {
        autocomplete: expect.objectContaining({
          prefix: query
        })
      }
    });
    expect(suggestionResult).toEqual([]);
  });

  it("should return no suggests if autocomplete field is empty array", async () => {
    const indexName = faker.datatype.uuid();
    const configService = createMockInstance(ConfigService);
    configService.elasticCompletionSuggesterIndex = indexName;
    const elasticCompletionService = createMockInstance(
      ElasticSearchCompletionService
    );

    elasticCompletionService.query.mockResolvedValue({
      took: 10,
      timed_out: false,
      _shards: {
        total: 1,
        successful: 1,
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        max_score: undefined,
        hits: []
      },
      suggest: {
        autocomplete: []
      }
    });

    const completionSuggesterService = new CompletionSuggesterService(
      configService,
      elasticCompletionService
    );

    const query = faker.random.alpha(10);

    const suggestionResult =
      await completionSuggesterService.getCompletionSuggestions(query);

    expect(elasticCompletionService.query).toHaveBeenCalledWith({
      index: indexName,
      _source: false,
      suggest: {
        autocomplete: expect.objectContaining({
          prefix: query
        })
      }
    });
    expect(suggestionResult).toEqual([]);
  });

  it("should return no suggests if options field is empty array", async () => {
    const indexName = faker.datatype.uuid();
    const configService = createMockInstance(ConfigService);
    configService.elasticCompletionSuggesterIndex = indexName;
    const elasticCompletionService = createMockInstance(
      ElasticSearchCompletionService
    );
    const query = faker.random.alpha(10);

    elasticCompletionService.query.mockResolvedValue({
      took: 10,
      timed_out: false,
      _shards: {
        total: 1,
        successful: 1,
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        max_score: undefined,
        hits: []
      },
      suggest: {
        autocomplete: [
          {
            text: query,
            offset: faker.datatype.number(),
            length: faker.datatype.number(),
            options: []
          }
        ]
      }
    });

    const completionSuggesterService = new CompletionSuggesterService(
      configService,
      elasticCompletionService
    );

    const suggestionResult =
      await completionSuggesterService.getCompletionSuggestions(query);

    expect(elasticCompletionService.query).toHaveBeenCalledWith({
      index: indexName,
      _source: false,
      suggest: {
        autocomplete: expect.objectContaining({
          prefix: query
        })
      }
    });
    expect(suggestionResult).toEqual([]);
  });

  it("should filter out suggestions that are same as user query", async () => {
    const indexName = faker.datatype.uuid();
    const configService = createMockInstance(ConfigService);
    configService.elasticCompletionSuggesterIndex = indexName;
    const elasticCompletionService = createMockInstance(
      ElasticSearchCompletionService
    );
    const query = faker.random.alpha(10);
    const suggestions = [
      query,
      faker.datatype.string(),
      faker.datatype.string()
    ];
    elasticCompletionService.query.mockResolvedValue({
      took: 10,
      timed_out: false,
      _shards: {
        total: 1,
        successful: 1,
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        max_score: undefined,
        hits: []
      },
      suggest: {
        autocomplete: [
          {
            text: query,
            offset: faker.datatype.number(),
            length: faker.datatype.number(),
            options: [
              {
                text: suggestions[0],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              },
              {
                text: suggestions[1],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              },
              {
                text: suggestions[2],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              }
            ]
          }
        ]
      }
    });

    const completionSuggesterService = new CompletionSuggesterService(
      configService,
      elasticCompletionService
    );

    const suggestionResult =
      await completionSuggesterService.getCompletionSuggestions(query);

    expect(elasticCompletionService.query).toHaveBeenCalledWith({
      index: indexName,
      _source: false,
      suggest: {
        autocomplete: expect.objectContaining({
          prefix: query
        })
      }
    });
    expect(suggestionResult).toEqual([suggestions[1], suggestions[2]]);
  });

  it("should remove whitespace only at start of string", async () => {
    const indexName = faker.datatype.uuid();
    const configService = createMockInstance(ConfigService);
    configService.elasticCompletionSuggesterIndex = indexName;
    const elasticCompletionService = createMockInstance(
      ElasticSearchCompletionService
    );
    const query = "  cancer ";
    const expectedFormattedQuery = "cancer ";
    const suggestions = [faker.datatype.string(), faker.datatype.string()];
    elasticCompletionService.query.mockResolvedValue({
      took: 10,
      timed_out: false,
      _shards: {
        total: 1,
        successful: 1,
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        max_score: undefined,
        hits: []
      },
      suggest: {
        autocomplete: [
          {
            text: expectedFormattedQuery,
            offset: faker.datatype.number(),
            length: faker.datatype.number(),
            options: [
              {
                text: suggestions[0],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              },
              {
                text: suggestions[1],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              }
            ]
          }
        ]
      }
    });

    const completionSuggesterService = new CompletionSuggesterService(
      configService,
      elasticCompletionService
    );

    const suggestionResult =
      await completionSuggesterService.getCompletionSuggestions(query);

    expect(elasticCompletionService.query).toHaveBeenCalledWith({
      index: indexName,
      _source: false,
      suggest: {
        autocomplete: expect.objectContaining({
          prefix: expectedFormattedQuery
        })
      }
    });
    expect(suggestionResult).toEqual(suggestions);
  });

  it("should replace multiple whitespace with single whitespace", async () => {
    const indexName = faker.datatype.uuid();
    const configService = createMockInstance(ConfigService);
    configService.elasticCompletionSuggesterIndex = indexName;
    const elasticCompletionService = createMockInstance(
      ElasticSearchCompletionService
    );
    const query = "hidradinitis  s";
    const expectedFormattedQuery = "hidradinitis s";
    const suggestions = [faker.datatype.string(), faker.datatype.string()];
    elasticCompletionService.query.mockResolvedValue({
      took: 10,
      timed_out: false,
      _shards: {
        total: 1,
        successful: 1,
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        max_score: undefined,
        hits: []
      },
      suggest: {
        autocomplete: [
          {
            text: expectedFormattedQuery,
            offset: faker.datatype.number(),
            length: faker.datatype.number(),
            options: [
              {
                text: suggestions[0],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              },
              {
                text: suggestions[1],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              }
            ]
          }
        ]
      }
    });

    const completionSuggesterService = new CompletionSuggesterService(
      configService,
      elasticCompletionService
    );

    const suggestionResult =
      await completionSuggesterService.getCompletionSuggestions(query);

    expect(elasticCompletionService.query).toHaveBeenCalledWith({
      index: indexName,
      _source: false,
      suggest: {
        autocomplete: expect.objectContaining({
          prefix: expectedFormattedQuery
        })
      }
    });
    expect(suggestionResult).toEqual(suggestions);
  });

  it("should remove apostrophes and replace all other special characters with whitespace", async () => {
    const indexName = faker.datatype.uuid();
    const configService = createMockInstance(ConfigService);
    configService.elasticCompletionSuggesterIndex = indexName;
    const elasticCompletionService = createMockInstance(
      ElasticSearchCompletionService
    );
    const query = "huntington's-#$%&^!*()[]:dis";
    const expectedFormattedQuery = "huntingtons dis";
    const suggestions = [faker.datatype.string(), faker.datatype.string()];
    elasticCompletionService.query.mockResolvedValue({
      took: 10,
      timed_out: false,
      _shards: {
        total: 1,
        successful: 1,
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        max_score: undefined,
        hits: []
      },
      suggest: {
        autocomplete: [
          {
            text: expectedFormattedQuery,
            offset: faker.datatype.number(),
            length: faker.datatype.number(),
            options: [
              {
                text: suggestions[0],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              },
              {
                text: suggestions[1],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              }
            ]
          }
        ]
      }
    });

    const completionSuggesterService = new CompletionSuggesterService(
      configService,
      elasticCompletionService
    );

    const suggestionResult =
      await completionSuggesterService.getCompletionSuggestions(query);

    expect(elasticCompletionService.query).toHaveBeenCalledWith({
      index: indexName,
      _source: false,
      suggest: {
        autocomplete: expect.objectContaining({
          prefix: expectedFormattedQuery
        })
      }
    });
    expect(suggestionResult).toEqual(suggestions);
  });

  it("should not remove latin characters", async () => {
    const indexName = faker.datatype.uuid();
    const configService = createMockInstance(ConfigService);
    configService.elasticCompletionSuggesterIndex = indexName;
    const elasticCompletionService = createMockInstance(
      ElasticSearchCompletionService
    );
    const query = "sjögrens";
    const suggestions = [faker.datatype.string(), faker.datatype.string()];
    elasticCompletionService.query.mockResolvedValue({
      took: 10,
      timed_out: false,
      _shards: {
        total: 1,
        successful: 1,
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        max_score: undefined,
        hits: []
      },
      suggest: {
        autocomplete: [
          {
            text: query,
            offset: faker.datatype.number(),
            length: faker.datatype.number(),
            options: [
              {
                text: suggestions[0],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              },
              {
                text: suggestions[1],
                _id: faker.datatype.string(),
                _index: faker.datatype.string(),
                _score: faker.datatype.number(),
                _source: undefined
              }
            ]
          }
        ]
      }
    });

    const completionSuggesterService = new CompletionSuggesterService(
      configService,
      elasticCompletionService
    );

    const suggestionResult =
      await completionSuggesterService.getCompletionSuggestions(query);

    expect(elasticCompletionService.query).toHaveBeenCalledWith({
      index: indexName,
      _source: false,
      suggest: {
        autocomplete: expect.objectContaining({
          prefix: query
        })
      }
    });
    expect(suggestionResult).toEqual(suggestions);
  });
});
