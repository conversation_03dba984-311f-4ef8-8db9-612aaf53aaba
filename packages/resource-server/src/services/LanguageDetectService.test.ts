import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { LanguageDetectService } from "./LanguageDetectService";

describe("LanguageDetectService", () => {
  describe(".detect", () => {
    it("should return undetermined (und)", async () => {
      const configService = createMockInstance(ConfigService);
      configService.searchSupportedLanguages = "eng,cmn,jpn";

      const languageDetectService = new LanguageDetectService(configService);

      const result = await languageDetectService.determineSupportedLanguage(
        "hello, world."
      );
      expect(result).toEqual("eng");
    });

    it("should return chinese (cmn)", async () => {
      const configService = createMockInstance(ConfigService);
      configService.searchSupportedLanguages = "eng,cmn,jpn";

      const languageDetectService = new LanguageDetectService(configService);

      const result = await languageDetectService.determineSupportedLanguage(
        "子"
      );
      expect(result).toEqual("cmn");
    });

    it("should return japanese (jpn)", async () => {
      const configService = createMockInstance(ConfigService);
      configService.searchSupportedLanguages = "eng,cmn,jpn";

      const languageDetectService = new LanguageDetectService(configService);

      const result = await languageDetectService.determineSupportedLanguage(
        "せ"
      );
      expect(result).toEqual("jpn");
    });
  });

  describe(".determineSupportedLanguageWithUserPreference", () => {
    it("should return english for english text", async () => {
      const configService = createMockInstance(ConfigService);
      configService.searchSupportedLanguages = "eng,cmn,jpn";

      const languageDetectService = new LanguageDetectService(configService);

      const actual =
        await languageDetectService.determineSupportedLanguageWithUserPreference(
          "test string",
          "english"
        );
      expect(actual).toEqual("eng");
    });

    it("should return chinese for han characters when user has no preferred language", async () => {
      const configService = createMockInstance(ConfigService);
      configService.searchSupportedLanguages = "eng,cmn,jpn";

      const languageDetectService = new LanguageDetectService(configService);

      const actual =
        await languageDetectService.determineSupportedLanguageWithUserPreference(
          "子",
          "chinese_simplified"
        );
      expect(actual).toEqual("cmn");
    });

    it("should return japanese for han characters when user's preferred language is japanese", async () => {
      const configService = createMockInstance(ConfigService);
      configService.searchSupportedLanguages = "eng,cmn,jpn";

      const languageDetectService = new LanguageDetectService(configService);

      const actual =
        await languageDetectService.determineSupportedLanguageWithUserPreference(
          "子",
          "japanese"
        );
      expect(actual).toEqual("jpn");
    });

    it("should return the correct language for the text when no language is set", async () => {
      const configService = createMockInstance(ConfigService);
      configService.searchSupportedLanguages = "eng,cmn,jpn";

      const languageDetectService = new LanguageDetectService(configService);

      const actual =
        await languageDetectService.determineSupportedLanguageWithUserPreference(
          "子",
          null
        );
      expect(actual).toEqual("cmn");
    });
  });
});
