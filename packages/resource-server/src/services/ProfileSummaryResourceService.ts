import { intervalToDuration } from "date-fns";
import { Rpc<PERSON>ethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import {
  ProfileSummaryMetrics,
  ProfileSummaryResource,
  ProfileTopMeshTerms,
  RPC_NAMESPACE_PROFILE_SUMMARY
} from "@h1nyc/search-sdk";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import { createLogger } from "../lib/Logger";
import {
  buildProfileSummaryMetricsQuery,
  ProfileSummaryMetricsResponse,
  buildProfileMeshTermsQuery,
  PublicationMeshTermsResponse
} from "../lib/ProfileSummaryBuilder";
import { Service } from "typedi";

@Service()
@RpcService()
export class ProfileSummaryResourceService
  extends RpcResourceService
  implements ProfileSummaryResource
{
  private readonly logger = createLogger(this);
  private peopleIndex: string;
  private meshExcludeTerms: string;

  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_PROFILE_SUMMARY,
      config.searchRedisOptions
    );

    this.peopleIndex = config.elasticPeopleIndex;
    this.meshExcludeTerms = config.meshExcludeWords;
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    await this.elasticService.getSignedElasticRequest({}, this.peopleIndex);
    return true;
  }

  @RpcMethod()
  async getMetricsForPeople(
    peopleIds: string[],
    projectId?: string
  ): Promise<ProfileSummaryMetrics[]> {
    const req = buildProfileSummaryMetricsQuery(peopleIds, projectId);

    this.logger.debug(
      { data: req },
      "Performing profile summary metrics search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<ProfileSummaryMetricsResponse>(
        req,
        this.peopleIndex
      );

    if (!data.aggregations.people?.buckets.length) {
      this.logger.info(`Profile summary metrics search failed to get results`);
      return [];
    }

    return data.aggregations.people.buckets.map((bucket) => {
      const {
        key: personId,
        publications,
        trials,
        congresses,
        payments
      } = bucket;

      function yearsAgo(date: number): number {
        const { years } = intervalToDuration({
          start: new Date(),
          end: new Date(date)
        });

        return years ?? 0;
      }

      function round5(x: number): number {
        return Math.ceil(x / 5) * 5;
      }

      // the dates a KOL was first involved in a pub/trial/congress
      const oldestDates = [
        publications.oldestDate.value,
        trials.oldestDate.value,
        congresses.oldestDate.value
        // remove any null values if KOL doesn't have particular asset
      ].flatMap((date) => (date ? [date] : []));

      // if a KOL has at least one pub/trial/congress, calculate which asset is
      // oldest and round up to the nearest five to determine the number of
      // years they've been active
      const yearsActive = oldestDates.length
        ? round5(Math.max(...oldestDates.map(yearsAgo)))
        : 0;

      // total amount of payments for person
      const amount = payments.totalPaymentsAmount.value;

      // calc the top companies by percent of total payment amount for KOL
      const topCompanyDistributions = payments.byCompany.buckets.map(
        ({ key, sumByCompany }) => {
          const companyAmount = sumByCompany.value;
          const percentage = Math.round((companyAmount / amount) * 100);

          return {
            companyName: key,
            percentage
          };
        }
      );

      return {
        personId,
        yearsActive,
        publications: {
          total: publications.doc_count,
          last12Months: publications.last12MonthsCount.buckets[0].doc_count
        },
        trials: {
          total: trials.doc_count,
          inProgress: trials.inProgressCount.buckets[0].doc_count
        },
        congresses: {
          total: congresses.doc_count
        },
        payments: {
          total: payments.doc_count,
          amount,
          topCompanyDistributions
        }
      };
    });
  }

  @RpcMethod()
  async getTopMeshTermsForPeople(
    peopleIds: string[],
    size: number,
    startDate: Date,
    endDate: Date,
    projectId?: string
  ): Promise<ProfileTopMeshTerms[]> {
    const excludeTerms = this.meshExcludeTerms
      ? this.meshExcludeTerms.toLowerCase().split(",")
      : [];
    const req = buildProfileMeshTermsQuery(
      peopleIds,
      size,
      startDate,
      endDate,
      excludeTerms,
      projectId
    );

    this.logger.debug({ data: req }, "Performing profile mesh terms search");

    const data =
      await this.elasticService.getSignedElasticRequest<PublicationMeshTermsResponse>(
        req,
        this.peopleIndex
      );

    if (!data.aggregations.people?.buckets.length) {
      this.logger.info(`Profile mesh terms search failed to get results`);
      return [];
    }

    const allData = data.aggregations.people.buckets.map((bucket) => {
      const { key: personId, publications } = bucket;
      const meshKeywords = publications.meshKeywordFilters?.buckets[0];

      const totalPublications = meshKeywords?.doc_count ?? 0;
      const publicationsByYear = (
        meshKeywords?.publicationsByYear?.buckets || []
      ).map((bucket) => ({
        year: new Date(bucket.key_as_string).getUTCFullYear(),
        total: bucket.doc_count
      }));

      const topKeyWords = (meshKeywords?.meshTerms?.buckets || []).map(
        (keywordBucket) => {
          const keywordsByYear = (
            keywordBucket.meshTermsByYear?.buckets || []
          ).map((yearBucket) => ({
            year: new Date(yearBucket.key_as_string).getUTCFullYear(),
            total: yearBucket.doc_count
          }));
          return {
            name: keywordBucket.key,
            total: keywordBucket.doc_count,
            keywordsByYear
          };
        }
      );

      const resp = {
        personId,
        totalPublications,
        publicationsByYear,
        topKeyWords
      };

      return resp;
    });

    return allData;
  }
}
