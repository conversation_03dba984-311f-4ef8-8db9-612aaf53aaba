/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { faker } from "@faker-js/faker";
import { INSTITUTION_ACCESS_LEVEL } from "@h1nyc/account-sdk";
import {
  DiversityCategoryAggregation,
  DiversityDashboardAggregation,
  DiversityDashboardAggregationBucket,
  DiversityDashboardAggregationResponse,
  FranceNationalityEncounterCountAggregation,
  GeoChoroplethLevelTypes,
  HeatmapLocationAggregation,
  Institution,
  InstitutionFilterAggregation,
  InstitutionSortOptions,
  InstitutionsResponse,
  InstitutionsSearchInput,
  NamedLocationDiversityHeatmapAggregationResponse,
  PeopleSearchInclusionExclusionAggregationResponse,
  RuleCombinatorEnum,
  RuleFieldEnum,
  RuleOperatorEnum,
  RuleTypeEnum,
  SpainNationalityEncounterCountAggregation
} from "@h1nyc/search-sdk";
import {
  generateInstitutionSearchFeatureFlags,
  generateMockAggregations,
  generateMockDiversityDashboardAggregation,
  generateMockElasticsearchInstitution,
  generateMockElasticsearchResponseWithAggregations,
  generateMockFranceHeatmapAggregation,
  generateMockSpainHeatmapAggregation,
  mockQueryUnderstandingServerResponse
} from "../util/TestUtils";
import { InstitutionsSearchResponseAdapterService } from "./InstitutionsSearchResponseAdapterService";
import {
  ClaimsAggregationDocCountBucket,
  DiagnosesCount,
  DocCountBucket,
  ElasticsearchInstitutionDoc,
  ElasticsearchTaggedInstitutionDoc,
  mapNationalityFilterValueToFrancePatientsDiversityField,
  mapNationalityFilterValueToSpainPatientsDiversityField,
  ProceduresCount
} from "./InstitutionsResourceService";
import { SearchTotalHits } from "@elastic/elasticsearch/lib/api/types";
import { zipObject } from "lodash";
import { PrescriptionsCount } from "./KeywordSearchResourceServiceRewrite";

const responseAdapter = new InstitutionsSearchResponseAdapterService();

function generateMockGeoClaimsAggregations(): ClaimsAggregationDocCountBucket[] {
  return [
    {
      key: faker.datatype.string(),
      doc_count: faker.datatype.number(),
      procedures: {
        doc_count: faker.datatype.number(),
        filteredClaims: {
          doc_count: faker.datatype.number(),
          counts: {
            value: faker.datatype.number()
          }
        }
      },
      diagnoses: {
        doc_count: faker.datatype.number(),
        filteredClaims: {
          doc_count: faker.datatype.number(),
          counts: {
            value: faker.datatype.number()
          }
        }
      }
    }
  ];
}

function toH1Insitution(
  esHit: ElasticsearchInstitutionDoc,
  _index?: number,
  _array?: ElasticsearchInstitutionDoc[],
  sortBy?: InstitutionSortOptions
): Institution {
  let patientsDiversity = esHit.patientsDiversityRatio;
  let diagnosesTotal = esHit.diagnosis_count;

  if (
    esHit.patientsDiversityRatioUk &&
    sortBy === InstitutionSortOptions.UK_DIVERSITY
  ) {
    patientsDiversity = esHit.patientsDiversityRatioUk;
    diagnosesTotal = esHit.totalClaimCountUk ?? 0;
  } else if (
    esHit.patientsDiversityRatioFrance &&
    sortBy === InstitutionSortOptions.FRANCE_DIVERSITY
  ) {
    patientsDiversity = esHit.patientsDiversityRatioFrance;
    diagnosesTotal = esHit.totalClaimCountFrance ?? 0;
  } else if (
    esHit.patientsDiversityRatioSpain &&
    sortBy === InstitutionSortOptions.SPAIN_DIVERSITY
  ) {
    patientsDiversity = esHit.patientsDiversityRatioSpain;
    diagnosesTotal = esHit.totalClaimCountSpain ?? 0;
  }

  return {
    id: esHit.masterOrganizationId ?? esHit.id,
    name: esHit.name,
    institutionType: esHit.institution_type,
    website: esHit.website,
    beds: esHit.beds,
    region: esHit.region,
    twitterUrl: esHit.twitter_url,
    linkedinUrl: esHit.linkedin_url,
    totalTags: esHit.tagIds?.length ?? 0,
    people: {
      affiliatedMatched: 0,
      total: esHit.person_count
    },
    publications: {
      total: esHit.publication_count
    },
    congresses: {
      total: esHit.conference_count
    },
    diagnoses: {
      total: diagnosesTotal
    },
    procedures: {
      total: esHit.procedure_count
    },
    prescriptions: {
      total: esHit.num_prescriptions ?? 0
    },
    trials: {
      total: esHit.trials_count
    },
    payments: {
      total:
        esHit.general_institution_payment_total +
        esHit.research_institution_payment_total
    },
    address: {
      street1: esHit["address.street1"],
      street2: esHit["address.street2"],
      street3: esHit["address.street3"],
      city: esHit["address.city"],
      postalCode: esHit["address.postal_code"],
      region: esHit["address.region"],
      regionCode: esHit["address.region_code"],
      country: esHit["address.country"],
      countryCode: esHit["address.country_code"]
    },
    location: {
      lat: parseFloat(esHit.location.lat),
      lon: parseFloat(esHit.location.lon)
    },
    matchedDiagnosesDetails: [],
    matchedProceduresDetails: [],
    patientsDiversity,
    patientsAge: esHit.patientDiversity.age,
    patientsSex: esHit.patientDiversity.sex,
    nciDesignatedCancerCenter: esHit.nci_designated_cancer_center,
    nationalComprehensiveCancerNetworkMember:
      esHit.national_comprehensive_cancer_network_member,
    teachingHospital: esHit.teaching_hospital,
    researchFacility: esHit.research_facility,
    cro: esHit.cro,
    orgTypes: esHit.orgTypes,
    orgTypesLevel2: esHit.orgTypesLevel2,
    orgTypesLevel3: esHit.orgTypesLevel3,
    trialEnrollmentRate: esHit.trialEnrollmentRate,
    enrollmentRatesByIndications: [],
    matchedEnrollmentRatesByIndications: []
  };
}

function aggregationToBucket(
  aggregation: DocCountBucket
): InstitutionFilterAggregation {
  return {
    id: aggregation.key,
    count: aggregation.doc_count
  };
}

function aggregationGeoClaimsToBucket(
  aggregation: ClaimsAggregationDocCountBucket
): InstitutionFilterAggregation {
  return {
    id: aggregation.key,
    count:
      aggregation.diagnoses.filteredClaims.counts.value +
      aggregation.procedures.filteredClaims.counts.value
  };
}

function toDiversityDashboardAggregation({
  france_diversity_dashboard,
  spain_diversity_dashboard
}: DiversityDashboardAggregationResponse):
  | DiversityDashboardAggregation
  | undefined {
  const dashboard = france_diversity_dashboard ?? spain_diversity_dashboard;
  if (!dashboard) {
    return undefined;
  }

  const totalNationalityCount =
    dashboard.location_filtered.nationality_counts.total_nationality_count
      .value;
  const nationality =
    dashboard.location_filtered.nationality_counts.by_nationality.buckets.map(
      (bucket) => toDiversityCategoryAggregation(bucket, totalNationalityCount)
    );

  const totalAgeCount =
    dashboard.location_filtered.age_counts.total_age_count.value;
  const age = dashboard.location_filtered.age_counts.by_age.buckets.map(
    (bucket) => toDiversityCategoryAggregation(bucket, totalAgeCount)
  );

  const totalGenderCount =
    dashboard.location_filtered.gender_counts.total_gender_count.value;
  const gender =
    dashboard.location_filtered.gender_counts.by_gender.buckets.map((bucket) =>
      toDiversityCategoryAggregation(bucket, totalGenderCount)
    );

  const totalEducationCount =
    dashboard.location_filtered.education_counts.total_education_count.value;
  const education =
    dashboard.location_filtered.education_counts.by_education.buckets.map(
      (bucket) => toDiversityCategoryAggregation(bucket, totalEducationCount)
    );

  return {
    age,
    gender,
    education,
    nationality
  };
}

function toDiversityCategoryAggregation(
  bucket: DiversityDashboardAggregationBucket,
  totalNationalityCount: number
): DiversityCategoryAggregation {
  const count = bucket.count.value ?? 0;
  const percentage = totalNationalityCount ? count / totalNationalityCount : 0;
  return {
    category: bucket.key,
    count,
    percentage
  };
}

function computeTotalClaimsBasedOnNationalityFilterForFrance(
  input: Readonly<InstitutionsSearchInput>,
  filtered: FranceNationalityEncounterCountAggregation
) {
  if (!input.filters?.diversityFilters?.nationality?.length) {
    return (
      filtered.patientCountFranceNative.value +
      filtered.patientCountEuropeanUnion.value +
      filtered.patientCountOtherEuropean.value +
      filtered.patientCountCentralAmerica.value +
      filtered.patientCountCentralAndSouthAfrica.value +
      filtered.patientCountSoutheastAsia.value +
      filtered.patientCountEastAsia.value +
      filtered.patientCountMiddleEastAndNorthAfrica.value +
      filtered.patientCountNorthAmerica.value +
      filtered.patientCountOceania.value +
      filtered.patientCountSouthAmerica.value +
      filtered.patientCountSouthAsia.value
    );
  }

  return input.filters.diversityFilters.nationality.reduce(
    (count, nationality) => {
      count +=
        filtered[
          mapNationalityFilterValueToFrancePatientsDiversityField(nationality)
        ].value;
      return count;
    },
    0
  );
}

function computeTotalClaimsBasedOnNationalityFilterForSpain(
  input: Readonly<InstitutionsSearchInput>,
  filtered: SpainNationalityEncounterCountAggregation
) {
  if (!input.filters?.diversityFilters?.nationality?.length) {
    return (
      filtered.patientCountSpain.value +
      filtered.patientCountEuropeanUnion.value +
      filtered.patientCountOtherEuropean.value +
      filtered.patientCountCentralAmerica.value +
      filtered.patientCountCentralAndSouthAfrica.value +
      filtered.patientCountSoutheastAsia.value +
      filtered.patientCountEastAsia.value +
      filtered.patientCountMiddleEastAndNorthAfrica.value +
      filtered.patientCountNorthAmerica.value +
      filtered.patientCountOceania.value +
      filtered.patientCountSouthAmerica.value +
      filtered.patientCountSouthAsia.value +
      filtered.patientCountCentralAsia.value +
      filtered.patientCountOther.value +
      filtered.patientCountUnknown.value
    );
  }

  return input.filters.diversityFilters.nationality.reduce(
    (count, nationality) => {
      count +=
        filtered[
          mapNationalityFilterValueToSpainPatientsDiversityField(nationality)
        ].value;
      return count;
    },
    0
  );
}

function toNamedLocationDiversityHeatmap(
  {
    france_heatmap,
    spain_heatmap
  }: NamedLocationDiversityHeatmapAggregationResponse,
  input: Readonly<InstitutionsSearchInput>
): HeatmapLocationAggregation[] | undefined {
  if (france_heatmap) {
    return france_heatmap.buckets.map((bucket) => {
      return {
        namedLocation: bucket.key,
        count: computeTotalClaimsBasedOnNationalityFilterForFrance(
          input,
          bucket.nested_agg.filtered_matching
        )
      };
    });
  } else if (spain_heatmap) {
    return spain_heatmap.buckets.map((bucket) => {
      return {
        namedLocation: bucket.key,
        count: computeTotalClaimsBasedOnNationalityFilterForSpain(
          input,
          bucket.nested_agg.filtered_matching
        )
      };
    });
  }

  return undefined;
}

function extractTotal(total: number | SearchTotalHits): number {
  if (typeof total === "number") {
    return total;
  }
  return total.value;
}

describe("InsitutionSearchResponseAdapterService", () => {
  describe("adaptToInstitutionsResponse", () => {
    it("institutions and aggregations should be parsed from the elasticsearch response", async () => {
      const input: InstitutionsSearchInput = {
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.US,
        query: faker.datatype.string()
      };

      const institution1 = generateMockElasticsearchInstitution();
      const institution2 = generateMockElasticsearchInstitution();

      const aggregations = {
        country: {
          buckets: generateMockAggregations()
        },
        region: {
          buckets: generateMockAggregations()
        },
        city: {
          buckets: generateMockAggregations()
        },
        postal_code: {
          buckets: generateMockAggregations()
        },
        orgTypes: {
          buckets: generateMockAggregations()
        }
      };

      const elasticsearchResponse =
        generateMockElasticsearchResponseWithAggregations(
          [institution1, institution2],
          aggregations
        );

      const isNavigationalQuery = faker.datatype.boolean();
      const expectedResponse: InstitutionsResponse = {
        total: extractTotal(elasticsearchResponse.hits.total ?? 0),
        isNavigationalQuery,
        institutions: [institution1, institution2].map(toH1Insitution),
        countryAggregations:
          aggregations.country.buckets.map(aggregationToBucket),
        regionAggregations:
          aggregations.region.buckets.map(aggregationToBucket),
        cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
        postalCodeAggregations:
          aggregations.postal_code.buckets.map(aggregationToBucket),
        geoLocationAggregations: [],
        geoDistanceAggregations: [],
        geoClaimsAggregations: [],
        synonyms: [],
        icdCodeSynonyms: [],
        orgTypesAggregations:
          aggregations.orgTypes.buckets.map(aggregationToBucket)
      };

      const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
        input,
        elasticsearchResponse,
        [],
        isNavigationalQuery,
        generateInstitutionSearchFeatureFlags(),
        [],
        [],
        [],
        []
      );

      expect(actualResponse).toEqual(expectedResponse);
    });

    it("should never return null or undefined for non-nullable properties of an institution", async () => {
      const input: InstitutionsSearchInput = {
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.US,
        query: faker.datatype.string()
      };

      const institution1 = generateMockElasticsearchInstitution();
      // @ts-ignore
      delete institution1.region;
      // @ts-ignore
      delete institution1.name;
      // @ts-ignore
      delete institution1.id;

      const aggregations = {
        country: {
          buckets: generateMockAggregations()
        },
        region: {
          buckets: generateMockAggregations()
        },
        city: {
          buckets: generateMockAggregations()
        },
        postal_code: {
          buckets: generateMockAggregations()
        }
      };

      const elasticsearchResponse =
        generateMockElasticsearchResponseWithAggregations(
          [institution1],
          aggregations
        );

      const response = responseAdapter.adaptToInstitutionSearchResponse(
        input,
        elasticsearchResponse,
        [],
        faker.datatype.boolean(),
        generateInstitutionSearchFeatureFlags(),
        [],
        [],
        [],
        []
      );

      expect(response.institutions[0].region).toBeDefined();
      expect(response.institutions[0].id).toBeDefined();
      expect(response.institutions[0].name).toBeDefined();
      expect(response.institutions[0].region).not.toBeNull();
      expect(response.institutions[0].id).not.toBeNull();
      expect(response.institutions[0].name).not.toBeNull();
    });

    it("should return id field as institutionId if masterOrganizationId field is missing", async () => {
      const input: InstitutionsSearchInput = {
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.US,
        query: faker.datatype.string()
      };

      const institution1 = generateMockElasticsearchInstitution();
      const institution2 = generateMockElasticsearchInstitution();
      delete institution2.masterOrganizationId;

      const aggregations = {
        country: {
          buckets: generateMockAggregations()
        },
        region: {
          buckets: generateMockAggregations()
        },
        city: {
          buckets: generateMockAggregations()
        },
        postal_code: {
          buckets: generateMockAggregations()
        },
        orgTypes: {
          buckets: generateMockAggregations()
        }
      };

      const elasticsearchResponse =
        generateMockElasticsearchResponseWithAggregations(
          [institution1, institution2],
          aggregations
        );

      const isNavigationalQuery = faker.datatype.boolean();
      const expectedResponse: InstitutionsResponse = {
        total: extractTotal(elasticsearchResponse.hits.total ?? 0),
        isNavigationalQuery,
        institutions: [institution1, institution2].map(toH1Insitution),
        countryAggregations:
          aggregations.country.buckets.map(aggregationToBucket),
        regionAggregations:
          aggregations.region.buckets.map(aggregationToBucket),
        cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
        postalCodeAggregations:
          aggregations.postal_code.buckets.map(aggregationToBucket),
        orgTypesAggregations:
          aggregations.orgTypes.buckets.map(aggregationToBucket),
        geoLocationAggregations: [],
        geoDistanceAggregations: [],
        geoClaimsAggregations: [],
        synonyms: [],
        icdCodeSynonyms: []
      };

      const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
        input,
        elasticsearchResponse,
        [],
        isNavigationalQuery,
        generateInstitutionSearchFeatureFlags(),
        [],
        [],
        [],
        []
      );

      expect(institution2.masterOrganizationId).toBeUndefined();
      expect(actualResponse).toEqual(expectedResponse);
    });

    it("should include hasCTMSData in elasticsearch response, set to the value of inCtmsNetwork, when the enable-ctms-v2 flag is enabled", async () => {
      const input: InstitutionsSearchInput = {
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.US,
        query: faker.datatype.string()
      };

      const institution1 = generateMockElasticsearchInstitution({
        hasCtms: false,
        inCtmsNetwork: true
      });
      const institution2 = generateMockElasticsearchInstitution();
      const mockInstitutions = [institution1, institution2];

      const aggregations = {
        country: {
          buckets: generateMockAggregations()
        },
        region: {
          buckets: generateMockAggregations()
        },
        city: {
          buckets: generateMockAggregations()
        },
        postal_code: {
          buckets: generateMockAggregations()
        },
        orgTypes: {
          buckets: generateMockAggregations()
        }
      };

      const elasticsearchResponse =
        generateMockElasticsearchResponseWithAggregations(
          mockInstitutions,
          aggregations
        );

      //TODO: update toH1Institution to handle this once we remove the feature flag
      const institutions = mockInstitutions
        .map(toH1Insitution)
        .map((institution, index) => ({
          ...institution,
          hasCTMSData:
            mockInstitutions[index].inCtmsNetwork ??
            mockInstitutions[index].hasCtms
        }));

      const isNavigationalQuery = faker.datatype.boolean();
      const expectedResponse: InstitutionsResponse = {
        total: extractTotal(elasticsearchResponse.hits.total ?? 0),
        isNavigationalQuery,
        institutions,
        countryAggregations:
          aggregations.country.buckets.map(aggregationToBucket),
        regionAggregations:
          aggregations.region.buckets.map(aggregationToBucket),
        cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
        postalCodeAggregations:
          aggregations.postal_code.buckets.map(aggregationToBucket),
        geoLocationAggregations: [],
        geoDistanceAggregations: [],
        geoClaimsAggregations: [],
        synonyms: [],
        icdCodeSynonyms: [],
        orgTypesAggregations:
          aggregations.orgTypes.buckets.map(aggregationToBucket)
      };

      const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
        input,
        elasticsearchResponse,
        [],
        isNavigationalQuery,
        generateInstitutionSearchFeatureFlags({
          enableCTMSV2: true,
          enableClaimsFilterFunctionScore: true,
          enableClaimsFilteringMatchedCountsUpdate: true,
          enableTagsInElasticsearch: false,
          enableNonIOLsInTrialLandscapeInstitutionSearch: false,
          enableUniquePatientCountForClaims: false
        }),
        [],
        [],
        [],
        []
      );

      expect(actualResponse).toEqual(expectedResponse);
    });

    it("should include academic type data in elasticsearch response", async () => {
      const input: InstitutionsSearchInput = {
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.US,
        query: faker.datatype.string()
      };

      const institution1 = generateMockElasticsearchInstitution({
        nci_designated_cancer_center: true,
        national_comprehensive_cancer_network_member: false,
        teaching_hospital: true
      });

      const institution2 = generateMockElasticsearchInstitution();
      const mockInstitutions = [institution1, institution2];

      const aggregations = {
        country: {
          buckets: generateMockAggregations()
        },
        region: {
          buckets: generateMockAggregations()
        },
        city: {
          buckets: generateMockAggregations()
        },
        postal_code: {
          buckets: generateMockAggregations()
        },
        orgTypes: {
          buckets: generateMockAggregations()
        }
      };

      const elasticsearchResponse =
        generateMockElasticsearchResponseWithAggregations(
          mockInstitutions,
          aggregations
        );

      const isNavigationalQuery = faker.datatype.boolean();
      const expectedResponse: InstitutionsResponse = {
        total: extractTotal(elasticsearchResponse.hits.total ?? 0),
        isNavigationalQuery,
        institutions: [institution1, institution2].map(toH1Insitution),
        countryAggregations:
          aggregations.country.buckets.map(aggregationToBucket),
        regionAggregations:
          aggregations.region.buckets.map(aggregationToBucket),
        cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
        postalCodeAggregations:
          aggregations.postal_code.buckets.map(aggregationToBucket),
        geoLocationAggregations: [],
        geoDistanceAggregations: [],
        geoClaimsAggregations: [],
        synonyms: [],
        icdCodeSynonyms: [],
        orgTypesAggregations:
          aggregations.orgTypes.buckets.map(aggregationToBucket)
      };

      const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
        input,
        elasticsearchResponse,
        [],
        isNavigationalQuery,
        generateInstitutionSearchFeatureFlags(),
        [],
        [],
        [],
        []
      );

      expect(actualResponse).toEqual(expectedResponse);
    });

    it("should include org type data in elasticsearch response", async () => {
      const input: InstitutionsSearchInput = {
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.US,
        query: faker.datatype.string()
      };

      const institution1 = generateMockElasticsearchInstitution();

      const aggregations = {
        country: {
          buckets: generateMockAggregations()
        },
        region: {
          buckets: generateMockAggregations()
        },
        city: {
          buckets: generateMockAggregations()
        },
        postal_code: {
          buckets: generateMockAggregations()
        },
        orgTypes: {
          buckets: generateMockAggregations()
        }
      };

      const elasticsearchResponse =
        generateMockElasticsearchResponseWithAggregations(
          [institution1],
          aggregations
        );

      const isNavigationalQuery = faker.datatype.boolean();
      const expectedResponse: InstitutionsResponse = {
        total: extractTotal(elasticsearchResponse.hits.total ?? 0),
        isNavigationalQuery,
        institutions: [toH1Insitution(institution1)],
        countryAggregations:
          aggregations.country.buckets.map(aggregationToBucket),
        regionAggregations:
          aggregations.region.buckets.map(aggregationToBucket),
        cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
        postalCodeAggregations:
          aggregations.postal_code.buckets.map(aggregationToBucket),
        geoLocationAggregations: [],
        geoDistanceAggregations: [],
        geoClaimsAggregations: [],
        synonyms: [],
        icdCodeSynonyms: [],
        orgTypesAggregations:
          aggregations.orgTypes.buckets.map(aggregationToBucket)
      };

      const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
        input,
        elasticsearchResponse,
        [],
        isNavigationalQuery,
        generateInstitutionSearchFeatureFlags(),
        [],
        [],
        [],
        []
      );

      expect(actualResponse).toEqual(expectedResponse);
    });

    it("should return the synonyms in response when query understanding server response contains synonms", async () => {
      const input: InstitutionsSearchInput = {
        query: "liver cancer",
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.ALL
      };

      // say, the query got augmented by the query understanding server in the following way
      const queryUnderstandingServerResponse =
        mockQueryUnderstandingServerResponse(
          "( ( liver cancer ) OR ( hepatic carcinoma) )",
          false,
          1,
          "liver cancer",
          ["hepatic carcinoma"]
        );

      const expectedSynonym: string[] = ["hepatic carcinoma"];

      const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
        input,
        generateMockElasticsearchResponseWithAggregations(),
        [],
        false,
        generateInstitutionSearchFeatureFlags(),
        [],
        [],
        [],
        [],
        undefined,
        queryUnderstandingServerResponse
      );

      expect(actualResponse).toEqual(
        expect.objectContaining({
          synonyms: expectedSynonym
        })
      );
    });

    it("should set matched count to timeFrame count when only timeFrame filter is present", async () => {
      const input: InstitutionsSearchInput = {
        query: faker.datatype.string(),
        sortBy: InstitutionSortOptions.DIVERSITY,
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
        filters: {
          claimsFilters: {
            timeFrame: faker.helpers.arrayElement([1, 2, 5])
          }
        }
      };

      const queryUnderstandingServerResponse =
        mockQueryUnderstandingServerResponse(input.query!, false);

      const institution1 = generateMockElasticsearchInstitution();
      const elasticsearchResponse =
        generateMockElasticsearchResponseWithAggregations([institution1]);

      const response = responseAdapter.adaptToInstitutionSearchResponse(
        input,
        elasticsearchResponse,
        [],
        false,
        generateInstitutionSearchFeatureFlags(),
        [],
        [],
        [],
        [],
        undefined,
        queryUnderstandingServerResponse
      );

      expect(response.institutions[0].diagnoses.matched).toEqual(
        institution1[
          `diagnosesCount_${input.filters?.claimsFilters?.timeFrame}_year` as keyof DiagnosesCount
        ]
      );
      expect(response.institutions[0].procedures.matched).toEqual(
        institution1[
          `proceduresCount_${input.filters?.claimsFilters?.timeFrame}_year` as keyof ProceduresCount
        ]
      );
      expect(response.institutions[0].prescriptions.matched).toEqual(
        institution1[
          `num_prescriptions_${input.filters?.claimsFilters?.timeFrame}_year` as keyof PrescriptionsCount
        ]
      );
    });

    describe("getMatchedHCPForIE", () => {
      it("should correctly parse response from call to people index when patient claims filter is applied", async () => {
        const value = faker.datatype.string();
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
          filters: {
            patientClaimsFilter: {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [value],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          }
        };

        const institutionHits = [
          {
            masterOrganizationId: faker.datatype.number()
          } as ElasticsearchInstitutionDoc
        ];
        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations<ElasticsearchInstitutionDoc>(
            institutionHits
          );
        const matchedPeople = faker.datatype.number();
        const mockAggregations = {
          filtered_institutions: {
            doc_count: faker.datatype.number(),
            filtered_ids: {
              doc_count: faker.datatype.number(),
              by_institution: {
                buckets: [
                  {
                    key: institutionHits[0].masterOrganizationId!.toString(),
                    doc_count: faker.datatype.number(),
                    to_parent: {
                      doc_count: matchedPeople
                    }
                  }
                ]
              }
            }
          }
        };
        const total = faker.datatype.number();
        const elasticsearchResponseForPeopleSearch =
          generateMockElasticsearchResponseWithAggregations(
            [{ hits: [] }],
            mockAggregations,
            total
          );

        const mockDiversityAggregations = {
          patient_race_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_age_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_gender_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_ethnicity_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        };

        const diversityDistributions = [
          {
            race: mockDiversityAggregations.patient_race_count
              .buckets as DocCountBucket[],
            gender: mockDiversityAggregations.patient_gender_count
              .buckets as DocCountBucket[],
            age: mockDiversityAggregations.patient_age_count
              .buckets as DocCountBucket[],
            ethnicity: mockDiversityAggregations.patient_ethnicity_count
              .buckets as DocCountBucket[]
          }
        ];

        const patientDiversityDistributionMap = zipObject(
          [institutionHits[0].masterOrganizationId!.toString()],
          diversityDistributions
        );

        const institutionsSearchResponse =
          responseAdapter.adaptToInstitutionSearchResponse(
            input,
            elasticsearchResponse,
            [],
            false,
            generateInstitutionSearchFeatureFlags(),
            [],
            [],
            [],
            [],
            undefined,
            undefined,
            elasticsearchResponseForPeopleSearch as unknown as PeopleSearchInclusionExclusionAggregationResponse,
            undefined,
            undefined,
            patientDiversityDistributionMap
          );

        expect(institutionsSearchResponse).toEqual(
          expect.objectContaining({
            institutions: expect.arrayContaining([
              expect.objectContaining({
                people: expect.objectContaining({
                  matched: matchedPeople
                })
              })
            ])
          })
        );
      });

      it("should break out Asian Pacific Islander race category when patient claims filter is applied", async () => {
        const value = faker.datatype.string();
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
          filters: {
            patientClaimsFilter: {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [value],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          }
        };

        const asianPacificIslanderRatio = faker.datatype.float({
          min: 0.01,
          max: 1
        });
        const asianRatio = faker.datatype.float({
          max: asianPacificIslanderRatio - 0.01
        });
        const pacificIslanderRatio = asianPacificIslanderRatio - asianRatio;
        const patientsDiversityRatio = {
          asianPacificIslander: asianPacificIslanderRatio,
          asian: asianRatio,
          pacificIslander: pacificIslanderRatio
        };
        const percentOfAsianOverCombined =
          asianRatio / asianPacificIslanderRatio;
        const percentOfPacificIslanderOverCombined =
          pacificIslanderRatio / asianPacificIslanderRatio;

        const institutionHits = [
          {
            masterOrganizationId: faker.datatype.number(),
            patientsDiversityRatio
          } as ElasticsearchInstitutionDoc
        ];

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations<ElasticsearchInstitutionDoc>(
            institutionHits
          );
        const matchedPeople = faker.datatype.number();
        const mockAggregations = {
          filtered_institutions: {
            doc_count: faker.datatype.number(),
            filtered_ids: {
              doc_count: faker.datatype.number(),
              by_institution: {
                buckets: [
                  {
                    key: institutionHits[0].masterOrganizationId!.toString(),
                    doc_count: faker.datatype.number(),
                    to_parent: {
                      doc_count: matchedPeople
                    }
                  }
                ]
              }
            }
          }
        };
        const total = faker.datatype.number();
        const elasticsearchResponseForPeopleSearch =
          generateMockElasticsearchResponseWithAggregations(
            [{ hits: [] }],
            mockAggregations,
            total
          );

        const mockDiversityAggregations = {
          patient_race_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.helpers.arrayElement(["Asian Pacific Islander"]),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_age_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_gender_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_ethnicity_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        };

        const diversityDistributions = [
          {
            race: mockDiversityAggregations.patient_race_count
              .buckets as DocCountBucket[],
            gender: mockDiversityAggregations.patient_gender_count
              .buckets as DocCountBucket[],
            age: mockDiversityAggregations.patient_age_count
              .buckets as DocCountBucket[],
            ethnicity: mockDiversityAggregations.patient_ethnicity_count
              .buckets as DocCountBucket[]
          }
        ];

        const patientDiversityDistributionMap = zipObject(
          [elasticsearchResponse.hits.hits[0]._id],
          diversityDistributions
        );

        const totalPatientCountByRace =
          mockDiversityAggregations.patient_race_count.buckets[0].doc_count +
          mockDiversityAggregations.patient_race_count.buckets[1].doc_count;

        const expectedAsianPacificIslanderPercentage =
          mockDiversityAggregations.patient_race_count.buckets[0].doc_count /
          totalPatientCountByRace;

        const institutionsSearchResponse =
          responseAdapter.adaptToInstitutionSearchResponse(
            input,
            elasticsearchResponse,
            [],
            false,
            generateInstitutionSearchFeatureFlags(),
            [],
            [],
            [],
            [],
            undefined,
            undefined,
            elasticsearchResponseForPeopleSearch as unknown as PeopleSearchInclusionExclusionAggregationResponse,
            undefined,
            undefined,
            patientDiversityDistributionMap
          );

        expect(institutionsSearchResponse).toEqual(
          expect.objectContaining({
            institutions: expect.arrayContaining([
              expect.objectContaining({
                people: expect.objectContaining({
                  matched: matchedPeople
                }),
                patientsDiversity: expect.objectContaining({
                  asianPacificIslander: expectedAsianPacificIslanderPercentage,
                  asian:
                    percentOfAsianOverCombined *
                    expectedAsianPacificIslanderPercentage,
                  pacificIslander:
                    percentOfPacificIslanderOverCombined *
                    expectedAsianPacificIslanderPercentage
                })
              })
            ])
          })
        );
      });

      it("should add non white patient ratio", async () => {
        const value = faker.datatype.string();
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
          filters: {
            patientClaimsFilter: {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [value],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          }
        };

        const institutionHits = [
          {
            masterOrganizationId: faker.datatype.number(),
            tagIds: [faker.datatype.string()]
          } as ElasticsearchInstitutionDoc
        ];

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations<ElasticsearchInstitutionDoc>(
            institutionHits
          );
        const matchedPeople = faker.datatype.number();
        const mockAggregations = {
          filtered_institutions: {
            doc_count: faker.datatype.number(),
            filtered_ids: {
              doc_count: faker.datatype.number(),
              by_institution: {
                buckets: [
                  {
                    key: institutionHits[0]!.masterOrganizationId!.toString(),
                    doc_count: faker.datatype.number(),
                    to_parent: {
                      doc_count: matchedPeople
                    }
                  }
                ]
              }
            }
          }
        };
        const total = faker.datatype.number();
        const elasticsearchResponseForPeopleSearch =
          generateMockElasticsearchResponseWithAggregations(
            [{ hits: [] }],
            mockAggregations,
            total
          );

        const asianPacificIslanderCount = faker.datatype.number();
        const whiteNonHispanicCount = faker.datatype.number();
        const blackNonHispanicCount = faker.datatype.number();
        const mockDiversityAggregations = {
          patient_race_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.helpers.arrayElement(["Asian Pacific Islander"]),
                doc_count: asianPacificIslanderCount
              },
              {
                key: faker.helpers.arrayElement(["White Non-Hispanic"]),
                doc_count: whiteNonHispanicCount
              },
              {
                key: faker.helpers.arrayElement(["Black Non-Hispanic"]),
                doc_count: blackNonHispanicCount
              }
            ]
          },
          patient_age_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_gender_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_ethnicity_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        };

        const diversityDistributions = [
          {
            race: mockDiversityAggregations.patient_race_count
              .buckets as DocCountBucket[],
            gender: mockDiversityAggregations.patient_gender_count
              .buckets as DocCountBucket[],
            age: mockDiversityAggregations.patient_age_count
              .buckets as DocCountBucket[],
            ethnicity: mockDiversityAggregations.patient_ethnicity_count
              .buckets as DocCountBucket[]
          }
        ];

        const patientDiversityDistributionMap = zipObject(
          [elasticsearchResponse.hits.hits[0]._id],
          diversityDistributions
        );

        const totalPatientCountByRace =
          asianPacificIslanderCount +
          whiteNonHispanicCount +
          blackNonHispanicCount;

        const institutionsSearchResponse =
          responseAdapter.adaptToInstitutionSearchResponse(
            input,
            elasticsearchResponse,
            [],
            false,
            generateInstitutionSearchFeatureFlags(),
            [],
            [],
            [],
            [],
            undefined,
            undefined,
            elasticsearchResponseForPeopleSearch as unknown as PeopleSearchInclusionExclusionAggregationResponse,
            undefined,
            undefined,
            patientDiversityDistributionMap
          );

        expect(institutionsSearchResponse).toEqual(
          expect.objectContaining({
            institutions: expect.arrayContaining([
              expect.objectContaining({
                totalTags: 1,
                patientsDiversity: expect.objectContaining({
                  nonWhite: 1 - whiteNonHispanicCount / totalPatientCountByRace
                })
              })
            ])
          })
        );
      });

      it("should not break out Asian Pacific Islander race category when patient claims filter is applied when asian ratio not in source document", async () => {
        const value = faker.datatype.string();
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
          filters: {
            patientClaimsFilter: {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [value],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          }
        };

        const asianPacificIslanderRatio = faker.datatype.float({
          min: 0.01,
          max: 1
        });
        const asianRatio = faker.datatype.float({
          max: asianPacificIslanderRatio - 0.01
        });
        const pacificIslanderRatio = asianPacificIslanderRatio - asianRatio;
        const patientsDiversityRatio = {
          asianPacificIslander: asianPacificIslanderRatio,
          pacificIslander: pacificIslanderRatio
        };

        const institutionHits = [
          {
            masterOrganizationId: faker.datatype.number(),
            patientsDiversityRatio
          } as ElasticsearchInstitutionDoc
        ];

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations<ElasticsearchInstitutionDoc>(
            institutionHits
          );
        const matchedPeople = faker.datatype.number();
        const mockAggregations = {
          filtered_institutions: {
            doc_count: faker.datatype.number(),
            filtered_ids: {
              doc_count: faker.datatype.number(),
              by_institution: {
                buckets: [
                  {
                    key: institutionHits[0].masterOrganizationId!.toString(),
                    doc_count: faker.datatype.number(),
                    to_parent: {
                      doc_count: matchedPeople
                    }
                  }
                ]
              }
            }
          }
        };
        const total = faker.datatype.number();
        const elasticsearchResponseForPeopleSearch =
          generateMockElasticsearchResponseWithAggregations(
            [{ hits: [] }],
            mockAggregations,
            total
          );

        const mockDiversityAggregations = {
          patient_race_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.helpers.arrayElement(["Asian Pacific Islander"]),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_age_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_gender_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_ethnicity_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        };

        const diversityDistributions = [
          {
            race: mockDiversityAggregations.patient_race_count
              .buckets as DocCountBucket[],
            gender: mockDiversityAggregations.patient_gender_count
              .buckets as DocCountBucket[],
            age: mockDiversityAggregations.patient_age_count
              .buckets as DocCountBucket[],
            ethnicity: mockDiversityAggregations.patient_ethnicity_count
              .buckets as DocCountBucket[]
          }
        ];

        const patientDiversityDistributionMap = zipObject(
          [elasticsearchResponse.hits.hits[0]._id],
          diversityDistributions
        );

        const totalPatientCountByRace =
          mockDiversityAggregations.patient_race_count.buckets[0].doc_count +
          mockDiversityAggregations.patient_race_count.buckets[1].doc_count;

        const expectedAsianPacificIslanderPercentage =
          mockDiversityAggregations.patient_race_count.buckets[0].doc_count /
          totalPatientCountByRace;

        const institutionsSearchResponse =
          responseAdapter.adaptToInstitutionSearchResponse(
            input,
            elasticsearchResponse,
            [],
            false,
            generateInstitutionSearchFeatureFlags(),
            [],
            [],
            [],
            [],
            undefined,
            undefined,
            elasticsearchResponseForPeopleSearch as unknown as PeopleSearchInclusionExclusionAggregationResponse,
            undefined,
            undefined,
            patientDiversityDistributionMap
          );

        expect(institutionsSearchResponse).toEqual(
          expect.objectContaining({
            institutions: expect.arrayContaining([
              expect.objectContaining({
                people: expect.objectContaining({
                  matched: matchedPeople
                }),
                patientsDiversity: expect.objectContaining({
                  asianPacificIslander: expectedAsianPacificIslanderPercentage,
                  asian: undefined,
                  pacificIslander: undefined
                })
              })
            ])
          })
        );
      });

      it("should not break out Asian Pacific Islander race category when patient claims filter is applied when pacificIslander ratio not in source document", async () => {
        const value = faker.datatype.string();
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
          filters: {
            patientClaimsFilter: {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [value],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          }
        };

        const asianPacificIslanderRatio = faker.datatype.float({
          min: 0.01,
          max: 1
        });
        const asianRatio = faker.datatype.float({
          max: asianPacificIslanderRatio - 0.01
        });
        const patientsDiversityRatio = {
          asianPacificIslander: asianPacificIslanderRatio,
          asian: asianRatio
        };

        const institutionHits = [
          {
            masterOrganizationId: faker.datatype.number(),
            patientsDiversityRatio
          } as ElasticsearchInstitutionDoc
        ];

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations<ElasticsearchInstitutionDoc>(
            institutionHits
          );
        const matchedPeople = faker.datatype.number();
        const mockAggregations = {
          filtered_institutions: {
            doc_count: faker.datatype.number(),
            filtered_ids: {
              doc_count: faker.datatype.number(),
              by_institution: {
                buckets: [
                  {
                    key: institutionHits[0].masterOrganizationId!.toString(),
                    doc_count: faker.datatype.number(),
                    to_parent: {
                      doc_count: matchedPeople
                    }
                  }
                ]
              }
            }
          }
        };
        const total = faker.datatype.number();
        const elasticsearchResponseForPeopleSearch =
          generateMockElasticsearchResponseWithAggregations(
            [{ hits: [] }],
            mockAggregations,
            total
          );

        const mockDiversityAggregations = {
          patient_race_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.helpers.arrayElement(["Asian Pacific Islander"]),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_age_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_gender_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          },
          patient_ethnicity_count: {
            doc_count_error_upper_bound: faker.datatype.number(),
            sum_other_doc_count: faker.datatype.number(),
            buckets: [
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              },
              {
                key: faker.datatype.string(),
                doc_count: faker.datatype.number()
              }
            ]
          }
        };

        const diversityDistributions = [
          {
            race: mockDiversityAggregations.patient_race_count
              .buckets as DocCountBucket[],
            gender: mockDiversityAggregations.patient_gender_count
              .buckets as DocCountBucket[],
            age: mockDiversityAggregations.patient_age_count
              .buckets as DocCountBucket[],
            ethnicity: mockDiversityAggregations.patient_ethnicity_count
              .buckets as DocCountBucket[]
          }
        ];

        const patientDiversityDistributionMap = zipObject(
          [elasticsearchResponse.hits.hits[0]._id],
          diversityDistributions
        );

        const totalPatientCountByRace =
          mockDiversityAggregations.patient_race_count.buckets[0].doc_count +
          mockDiversityAggregations.patient_race_count.buckets[1].doc_count;

        const expectedAsianPacificIslanderPercentage =
          mockDiversityAggregations.patient_race_count.buckets[0].doc_count /
          totalPatientCountByRace;

        const institutionsSearchResponse =
          responseAdapter.adaptToInstitutionSearchResponse(
            input,
            elasticsearchResponse,
            [],
            false,
            generateInstitutionSearchFeatureFlags(),
            [],
            [],
            [],
            [],
            undefined,
            undefined,
            elasticsearchResponseForPeopleSearch as unknown as PeopleSearchInclusionExclusionAggregationResponse,
            undefined,
            undefined,
            patientDiversityDistributionMap
          );

        expect(institutionsSearchResponse).toEqual(
          expect.objectContaining({
            institutions: expect.arrayContaining([
              expect.objectContaining({
                people: expect.objectContaining({
                  matched: matchedPeople
                }),
                patientsDiversity: expect.objectContaining({
                  asianPacificIslander: expectedAsianPacificIslanderPercentage,
                  asian: undefined,
                  pacificIslander: undefined
                })
              })
            ])
          })
        );
      });
    });

    it("should adapt response to show matched counts of claim codes selected in IE criteria", async () => {
      const randomICDCode = faker.random.word();
      const randomCPTCode = faker.random.word();
      const diagnosisCodeIE = faker.datatype.string();
      const procedureCodeIE = faker.datatype.string();
      const input: InstitutionsSearchInput = {
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,

        filters: {
          claimsFilters: {
            diagnosesICD: [`${randomICDCode} - ${faker.random.word()}`],
            proceduresCPT: [`${randomCPTCode} - ${faker.random.word()}`],
            timeFrame: 2
          },
          trialsFilters: {
            status: [faker.datatype.string()]
          },
          patientClaimsFilter: {
            type: RuleTypeEnum.RULE_GROUP,
            ruleGroup: {
              combinator: RuleCombinatorEnum.OR,
              rules: [
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.DIAGNOSES_CODE,
                    value: [diagnosisCodeIE],
                    operator: RuleOperatorEnum.EQUAL
                  }
                },
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.PROCEDURE_CODE,
                    value: [procedureCodeIE],
                    operator: RuleOperatorEnum.EQUAL
                  }
                }
              ]
            }
          }
        },

        exportsSelections: {
          matchedClaimsDetails: true
        }
      };

      const mockInstitution = generateMockElasticsearchInstitution();

      const IEMatchedClaimResponse =
        generateMockElasticsearchResponseWithAggregations();
      const icdCodeCount = faker.datatype.number();
      const procCodeCount = faker.datatype.number();
      const icdCodeDescription = faker.datatype.string();
      const procCodeDescription = faker.datatype.string();
      const icdCodeScheme = faker.random.word();
      const procCodeScheme = faker.random.word();
      IEMatchedClaimResponse.aggregations = {
        by_procedure_code: {
          buckets: [{ key: procedureCodeIE, doc_count: procCodeCount }]
        },
        by_icdcode: {
          buckets: [{ key: diagnosisCodeIE, doc_count: icdCodeCount }]
        }
      };
      const elasticResponse = generateMockElasticsearchResponseWithAggregations(
        [mockInstitution]
      );

      const response = responseAdapter.adaptToInstitutionSearchResponse(
        input,
        elasticResponse,
        [],
        false,
        generateInstitutionSearchFeatureFlags(),
        [],
        [],
        [],
        [],
        undefined,
        undefined,
        undefined,
        undefined,
        {
          diagnosesCountMap: {
            [elasticResponse.hits.hits[0]._id]: {
              [diagnosisCodeIE]: {
                count: icdCodeCount,
                description: icdCodeDescription,
                scheme: icdCodeScheme
              }
            }
          },
          proceduresCountMap: {
            [elasticResponse.hits.hits[0]._id]: {
              [procedureCodeIE]: {
                count: procCodeCount,
                description: procCodeDescription,
                scheme: procCodeScheme
              }
            }
          }
        }
      );

      const matchedDiagnoses = response.institutions[0].matchedDiagnosesDetails;
      const matchedProcedures =
        response.institutions[0].matchedProceduresDetails;
      expect(matchedDiagnoses).toEqual([
        {
          code: diagnosisCodeIE,
          description: icdCodeDescription,
          count: icdCodeCount
        }
      ]);
      expect(matchedProcedures).toEqual([
        {
          code: procedureCodeIE,
          description: procCodeDescription,
          count: procCodeCount
        }
      ]);
    });

    describe("trialsOngoingCount/trialsActivelyRecruitingCount", () => {
      it("should set trial ongoing and actively recruiting count when trial filter is applied", async () => {
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.US,
          query: "",
          filters: {
            trialsFilters: {
              phase: [faker.datatype.string()]
            }
          }
        };

        const institution1 = generateMockElasticsearchInstitution();
        const institution2 = generateMockElasticsearchInstitution();
        const mockInstitutions = [institution1, institution2];

        const aggregations = {
          country: {
            buckets: generateMockAggregations()
          },
          region: {
            buckets: generateMockAggregations()
          },
          city: {
            buckets: generateMockAggregations()
          },
          postal_code: {
            buckets: generateMockAggregations()
          },
          orgTypes: {
            buckets: generateMockAggregations()
          }
        };

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations(
            mockInstitutions,
            aggregations
          );

        const trialOngoingCounts = [
          faker.datatype.number(),
          faker.datatype.number()
        ];
        const trialActivelyRecruitingCounts = [
          faker.datatype.number(),
          faker.datatype.number()
        ];

        const totalPatientDocs = faker.datatype.number();

        const expectedResponse: InstitutionsResponse = expect.objectContaining({
          institutions: [
            expect.objectContaining({
              trialOngoingCount: trialOngoingCounts[0],
              trialActivelyRecruitingCount: trialActivelyRecruitingCounts[0]
            }),
            expect.objectContaining({
              trialOngoingCount: trialOngoingCounts[1],
              trialActivelyRecruitingCount: trialActivelyRecruitingCounts[1]
            })
          ]
        });

        const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          false,
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          [],
          undefined,
          undefined,
          undefined,
          {
            [elasticsearchResponse.hits.hits[0]._id]: totalPatientDocs,
            [elasticsearchResponse.hits.hits[1]._id]: totalPatientDocs
          },
          {
            diagnosesCountMap: {},
            proceduresCountMap: {}
          },
          undefined,
          {
            [elasticsearchResponse.hits.hits[0]._id]: trialOngoingCounts[0],
            [elasticsearchResponse.hits.hits[1]._id]: trialOngoingCounts[1]
          },
          {
            [elasticsearchResponse.hits.hits[0]._id]:
              trialActivelyRecruitingCounts[0],
            [elasticsearchResponse.hits.hits[1]._id]:
              trialActivelyRecruitingCounts[1]
          }
        );

        expect(actualResponse).toEqual(expectedResponse);
      });
    });

    describe("geo-data parsing", () => {
      it("should parse geoclaimsChoropleth aggregations from the elasticsearch response", async () => {
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.US,
          query: faker.datatype.string(),
          filters: {
            geoStatsRegionLevel: {
              claims: {
                zoomLevel: GeoChoroplethLevelTypes.Country
              }
            }
          }
        };

        const queryUnderstandingServerResponse =
          mockQueryUnderstandingServerResponse(input.query!, true);

        const institution1 = generateMockElasticsearchInstitution();
        const institution2 = generateMockElasticsearchInstitution();

        const aggregations = {
          country: {
            buckets: generateMockAggregations()
          },
          region: {
            buckets: generateMockAggregations()
          },
          city: {
            buckets: generateMockAggregations()
          },
          postal_code: {
            buckets: generateMockAggregations()
          },
          geo_hash: {
            buckets: generateMockAggregations()
          },
          geoDistance: {
            buckets: generateMockAggregations()
          },
          claims_country: {
            buckets: generateMockGeoClaimsAggregations()
          },
          orgTypes: {
            buckets: generateMockAggregations()
          }
        };

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations(
            [institution1, institution2],
            aggregations
          );

        const expectedResponse: InstitutionsResponse = {
          total: extractTotal(elasticsearchResponse.hits.total ?? 0),
          isNavigationalQuery: true,
          institutions: [institution1, institution2].map(toH1Insitution),
          countryAggregations:
            aggregations.country.buckets.map(aggregationToBucket),
          regionAggregations:
            aggregations.region.buckets.map(aggregationToBucket),
          cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
          postalCodeAggregations:
            aggregations.postal_code.buckets.map(aggregationToBucket),
          geoLocationAggregations:
            aggregations.geo_hash.buckets.map(aggregationToBucket),
          geoDistanceAggregations:
            aggregations.geoDistance.buckets.map(aggregationToBucket),
          geoClaimsAggregations: aggregations.claims_country.buckets.map(
            aggregationGeoClaimsToBucket
          ),
          synonyms: [],
          icdCodeSynonyms: [],
          orgTypesAggregations:
            aggregations.orgTypes.buckets.map(aggregationToBucket)
        };

        const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          !!queryUnderstandingServerResponse
            .getQueryIntent()
            ?.hasInstitutionIntent(),
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          [],
          undefined,
          queryUnderstandingServerResponse,
          undefined,
          undefined,
          {
            diagnosesCountMap: {},
            proceduresCountMap: {}
          }
        );

        expect(actualResponse).toEqual(expectedResponse);
      });

      it("should return empty geoclaimsChoropleth aggregations from the elasticsearch response when not defined in input", async () => {
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.US,
          query: faker.datatype.string()
        };

        const queryUnderstandingServerResponse =
          mockQueryUnderstandingServerResponse(input.query!, true);

        const institution1 = generateMockElasticsearchInstitution();
        const institution2 = generateMockElasticsearchInstitution();

        const aggregations = {
          country: {
            buckets: generateMockAggregations()
          },
          region: {
            buckets: generateMockAggregations()
          },
          city: {
            buckets: generateMockAggregations()
          },
          postal_code: {
            buckets: generateMockAggregations()
          },
          orgTypes: {
            buckets: generateMockAggregations()
          }
        };

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations(
            [institution1, institution2],
            aggregations
          );

        const expectedResponse: InstitutionsResponse = {
          total: extractTotal(elasticsearchResponse.hits.total ?? 0),
          isNavigationalQuery: !!queryUnderstandingServerResponse
            .getQueryIntent()
            ?.hasInstitutionIntent(),
          institutions: [institution1, institution2].map(toH1Insitution),
          countryAggregations:
            aggregations.country.buckets.map(aggregationToBucket),
          regionAggregations:
            aggregations.region.buckets.map(aggregationToBucket),
          cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
          postalCodeAggregations:
            aggregations.postal_code.buckets.map(aggregationToBucket),
          geoLocationAggregations: [],
          geoDistanceAggregations: [],
          geoClaimsAggregations: [],
          synonyms: [],
          icdCodeSynonyms: [],
          orgTypesAggregations:
            aggregations.orgTypes.buckets.map(aggregationToBucket)
        };

        const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          !!queryUnderstandingServerResponse
            .getQueryIntent()
            ?.hasInstitutionIntent(),
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          [],
          undefined,
          queryUnderstandingServerResponse,
          undefined,
          undefined,
          {
            diagnosesCountMap: {},
            proceduresCountMap: {}
          }
        );

        expect(actualResponse).toEqual(expectedResponse);
      });

      it("should parse geoDistance aggregations from the elasticsearch response", async () => {
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.US,
          query: faker.datatype.string()
        };

        const queryUnderstandingServerResponse =
          mockQueryUnderstandingServerResponse(input.query!, true);

        const institution1 = generateMockElasticsearchInstitution();
        const institution2 = generateMockElasticsearchInstitution();

        const aggregations = {
          country: {
            buckets: generateMockAggregations()
          },
          region: {
            buckets: generateMockAggregations()
          },
          city: {
            buckets: generateMockAggregations()
          },
          postal_code: {
            buckets: generateMockAggregations()
          },
          geo_hash: {
            buckets: generateMockAggregations()
          },
          geoDistance: {
            buckets: generateMockAggregations()
          },
          orgTypes: {
            buckets: generateMockAggregations()
          }
        };

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations(
            [institution1, institution2],
            aggregations
          );

        const expectedResponse: InstitutionsResponse = {
          total: extractTotal(elasticsearchResponse.hits.total ?? 0),
          isNavigationalQuery: !!queryUnderstandingServerResponse
            .getQueryIntent()
            ?.hasInstitutionIntent(),
          institutions: [institution1, institution2].map(toH1Insitution),
          countryAggregations:
            aggregations.country.buckets.map(aggregationToBucket),
          regionAggregations:
            aggregations.region.buckets.map(aggregationToBucket),
          cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
          postalCodeAggregations:
            aggregations.postal_code.buckets.map(aggregationToBucket),
          geoLocationAggregations:
            aggregations.geo_hash.buckets.map(aggregationToBucket),
          geoDistanceAggregations:
            aggregations.geoDistance.buckets.map(aggregationToBucket),
          geoClaimsAggregations: [],
          synonyms: [],
          icdCodeSynonyms: [],
          orgTypesAggregations:
            aggregations.orgTypes.buckets.map(aggregationToBucket)
        };

        const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          !!queryUnderstandingServerResponse
            .getQueryIntent()
            ?.hasInstitutionIntent(),
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          [],
          undefined,
          queryUnderstandingServerResponse,
          undefined,
          undefined,
          {
            diagnosesCountMap: {},
            proceduresCountMap: {}
          }
        );

        expect(actualResponse).toEqual(expectedResponse);
      });

      it("should return empty geoDistance aggregations from the elasticsearch response when not defined in input", async () => {
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.US,
          query: faker.datatype.string()
        };

        const queryUnderstandingServerResponse =
          mockQueryUnderstandingServerResponse(input.query!, true);

        const institution1 = generateMockElasticsearchInstitution();
        const institution2 = generateMockElasticsearchInstitution();

        const aggregations = {
          country: {
            buckets: generateMockAggregations()
          },
          region: {
            buckets: generateMockAggregations()
          },
          city: {
            buckets: generateMockAggregations()
          },
          postal_code: {
            buckets: generateMockAggregations()
          },
          orgTypes: {
            buckets: generateMockAggregations()
          }
        };

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations(
            [institution1, institution2],
            aggregations
          );

        const expectedResponse: InstitutionsResponse = {
          total: extractTotal(elasticsearchResponse.hits.total ?? 0),
          isNavigationalQuery: !!queryUnderstandingServerResponse
            .getQueryIntent()
            ?.hasInstitutionIntent(),
          institutions: [institution1, institution2].map(toH1Insitution),
          countryAggregations:
            aggregations.country.buckets.map(aggregationToBucket),
          regionAggregations:
            aggregations.region.buckets.map(aggregationToBucket),
          cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
          postalCodeAggregations:
            aggregations.postal_code.buckets.map(aggregationToBucket),
          geoLocationAggregations: [],
          geoDistanceAggregations: [],
          geoClaimsAggregations: [],
          synonyms: [],
          icdCodeSynonyms: [],
          orgTypesAggregations:
            aggregations.orgTypes.buckets.map(aggregationToBucket)
        };

        const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          !!queryUnderstandingServerResponse
            .getQueryIntent()
            ?.hasInstitutionIntent(),
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          [],
          undefined,
          queryUnderstandingServerResponse,
          undefined,
          undefined,
          {
            diagnosesCountMap: {},
            proceduresCountMap: {}
          }
        );

        expect(actualResponse).toEqual(expectedResponse);
      });

      it("should parse geoLocation aggregations from the elasticsearch response", async () => {
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.US,
          query: faker.datatype.string()
        };

        const queryUnderstandingServerResponse =
          mockQueryUnderstandingServerResponse(input.query!, true);

        const institution1 = generateMockElasticsearchInstitution();
        const institution2 = generateMockElasticsearchInstitution();

        const aggregations = {
          country: {
            buckets: generateMockAggregations()
          },
          region: {
            buckets: generateMockAggregations()
          },
          city: {
            buckets: generateMockAggregations()
          },
          postal_code: {
            buckets: generateMockAggregations()
          },
          geo_hash: {
            buckets: generateMockAggregations()
          },
          orgTypes: {
            buckets: generateMockAggregations()
          }
        };

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations(
            [institution1, institution2],
            aggregations
          );

        const expectedResponse: InstitutionsResponse = {
          total: extractTotal(elasticsearchResponse.hits.total ?? 0),
          isNavigationalQuery: true,
          institutions: [institution1, institution2].map(toH1Insitution),
          countryAggregations:
            aggregations.country.buckets.map(aggregationToBucket),
          regionAggregations:
            aggregations.region.buckets.map(aggregationToBucket),
          cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
          postalCodeAggregations:
            aggregations.postal_code.buckets.map(aggregationToBucket),
          geoLocationAggregations:
            aggregations.geo_hash.buckets.map(aggregationToBucket),
          geoDistanceAggregations: [],
          geoClaimsAggregations: [],
          synonyms: [],
          icdCodeSynonyms: [],
          orgTypesAggregations:
            aggregations.orgTypes.buckets.map(aggregationToBucket)
        };

        const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          !!queryUnderstandingServerResponse
            .getQueryIntent()
            ?.hasInstitutionIntent(),
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          [],
          undefined,
          queryUnderstandingServerResponse,
          undefined,
          undefined,
          {
            diagnosesCountMap: {},
            proceduresCountMap: {}
          }
        );

        expect(actualResponse).toEqual(expectedResponse);
      });

      it("should return empty geoLocation aggregations from the elasticsearch response when not defined in input", async () => {
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.US,
          query: faker.datatype.string()
        };

        const queryUnderstandingServerResponse =
          mockQueryUnderstandingServerResponse(input.query!, true);

        const institution1 = generateMockElasticsearchInstitution();
        const institution2 = generateMockElasticsearchInstitution();

        const aggregations = {
          country: {
            buckets: generateMockAggregations()
          },
          region: {
            buckets: generateMockAggregations()
          },
          city: {
            buckets: generateMockAggregations()
          },
          postal_code: {
            buckets: generateMockAggregations()
          },
          orgTypes: {
            buckets: generateMockAggregations()
          }
        };

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations(
            [institution1, institution2],
            aggregations
          );

        const expectedResponse: InstitutionsResponse = {
          total: extractTotal(elasticsearchResponse.hits.total ?? 0),
          isNavigationalQuery: !!queryUnderstandingServerResponse
            .getQueryIntent()
            ?.hasInstitutionIntent(),
          institutions: [institution1, institution2].map(toH1Insitution),
          countryAggregations:
            aggregations.country.buckets.map(aggregationToBucket),
          regionAggregations:
            aggregations.region.buckets.map(aggregationToBucket),
          cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
          postalCodeAggregations:
            aggregations.postal_code.buckets.map(aggregationToBucket),
          geoLocationAggregations: [],
          geoDistanceAggregations: [],
          geoClaimsAggregations: [],
          synonyms: [],
          icdCodeSynonyms: [],
          orgTypesAggregations:
            aggregations.orgTypes.buckets.map(aggregationToBucket)
        };

        const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          !!queryUnderstandingServerResponse
            .getQueryIntent()
            ?.hasInstitutionIntent(),
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          [],
          undefined,
          queryUnderstandingServerResponse,
          undefined,
          undefined,
          {
            diagnosesCountMap: {},
            proceduresCountMap: {}
          }
        );

        expect(actualResponse).toEqual(expectedResponse);
      });
    });

    describe("UK diversity sort", () => {
      it("institutions and aggregations should be parsed from the elasticsearch response", async () => {
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.US,
          query: faker.datatype.string(),
          sortBy: InstitutionSortOptions.UK_DIVERSITY
        };

        const institution1 = generateMockElasticsearchInstitution();
        const institution2 = generateMockElasticsearchInstitution();

        const aggregations = {
          country: {
            buckets: generateMockAggregations()
          },
          region: {
            buckets: generateMockAggregations()
          },
          city: {
            buckets: generateMockAggregations()
          },
          postal_code: {
            buckets: generateMockAggregations()
          },
          orgTypes: {
            buckets: generateMockAggregations()
          }
        };

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations(
            [institution1, institution2],
            aggregations
          );

        const isNavigationalQuery = faker.datatype.boolean();
        const expectedResponse: InstitutionsResponse = {
          total: extractTotal(elasticsearchResponse.hits.total ?? 0),
          isNavigationalQuery,
          institutions: [institution1, institution2].map((institution) =>
            toH1Insitution(institution, undefined, undefined, input.sortBy)
          ),
          countryAggregations:
            aggregations.country.buckets.map(aggregationToBucket),
          regionAggregations:
            aggregations.region.buckets.map(aggregationToBucket),
          cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
          postalCodeAggregations:
            aggregations.postal_code.buckets.map(aggregationToBucket),
          geoLocationAggregations: [],
          geoDistanceAggregations: [],
          geoClaimsAggregations: [],
          synonyms: [],
          icdCodeSynonyms: [],
          orgTypesAggregations:
            aggregations.orgTypes.buckets.map(aggregationToBucket)
        };

        const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          isNavigationalQuery,
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          []
        );

        expect(actualResponse).toEqual(expectedResponse);
      });
    });

    describe("France diversity sort", () => {
      it("institutions and aggregations should be parsed from the elasticsearch response", async () => {
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.US,
          query: faker.datatype.string(),
          sortBy: InstitutionSortOptions.FRANCE_DIVERSITY
        };

        const institution1 = generateMockElasticsearchInstitution();
        const institution2 = generateMockElasticsearchInstitution();

        const aggregations = {
          country: {
            buckets: generateMockAggregations()
          },
          region: {
            buckets: generateMockAggregations()
          },
          city: {
            buckets: generateMockAggregations()
          },
          postal_code: {
            buckets: generateMockAggregations()
          },
          orgTypes: {
            buckets: generateMockAggregations()
          },
          france_heatmap: {
            buckets: generateMockFranceHeatmapAggregation()
          },
          france_diversity_dashboard:
            generateMockDiversityDashboardAggregation()
        };

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations(
            [institution1, institution2],
            aggregations
          );

        const isNavigationalQuery = faker.datatype.boolean();
        const expectedResponse: InstitutionsResponse = {
          total: extractTotal(elasticsearchResponse.hits.total ?? 0),
          isNavigationalQuery,
          institutions: [institution1, institution2].map((institution) =>
            toH1Insitution(institution, undefined, undefined, input.sortBy)
          ),
          countryAggregations:
            aggregations.country.buckets.map(aggregationToBucket),
          regionAggregations:
            aggregations.region.buckets.map(aggregationToBucket),
          cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
          postalCodeAggregations:
            aggregations.postal_code.buckets.map(aggregationToBucket),
          geoLocationAggregations: [],
          geoDistanceAggregations: [],
          geoClaimsAggregations: [],
          synonyms: [],
          icdCodeSynonyms: [],
          orgTypesAggregations:
            aggregations.orgTypes.buckets.map(aggregationToBucket),
          diversityDashboard: toDiversityDashboardAggregation(aggregations),
          namedLocationDiversityHeatmap: toNamedLocationDiversityHeatmap(
            aggregations,
            input
          )
        };

        const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          isNavigationalQuery,
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          []
        );

        expect(actualResponse).toEqual(expectedResponse);
      });
    });

    describe("Spain diversity sort", () => {
      it("institutions and aggregations should be parsed from the elasticsearch response", async () => {
        const input: InstitutionsSearchInput = {
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.US,
          query: faker.datatype.string(),
          sortBy: InstitutionSortOptions.SPAIN_DIVERSITY
        };

        const institution1 = generateMockElasticsearchInstitution();
        const institution2 = generateMockElasticsearchInstitution();

        const aggregations = {
          country: {
            buckets: generateMockAggregations()
          },
          region: {
            buckets: generateMockAggregations()
          },
          city: {
            buckets: generateMockAggregations()
          },
          postal_code: {
            buckets: generateMockAggregations()
          },
          orgTypes: {
            buckets: generateMockAggregations()
          },
          spain_heatmap: {
            buckets: generateMockSpainHeatmapAggregation()
          },
          spain_diversity_dashboard: generateMockDiversityDashboardAggregation()
        };

        const elasticsearchResponse =
          generateMockElasticsearchResponseWithAggregations(
            [institution1, institution2],
            aggregations
          );

        const isNavigationalQuery = faker.datatype.boolean();
        const expectedResponse: InstitutionsResponse = {
          total: extractTotal(elasticsearchResponse.hits.total ?? 0),
          isNavigationalQuery,
          institutions: [institution1, institution2].map((institution) =>
            toH1Insitution(institution, undefined, undefined, input.sortBy)
          ),
          countryAggregations:
            aggregations.country.buckets.map(aggregationToBucket),
          regionAggregations:
            aggregations.region.buckets.map(aggregationToBucket),
          cityAggregations: aggregations.city.buckets.map(aggregationToBucket),
          postalCodeAggregations:
            aggregations.postal_code.buckets.map(aggregationToBucket),
          geoLocationAggregations: [],
          geoDistanceAggregations: [],
          geoClaimsAggregations: [],
          synonyms: [],
          icdCodeSynonyms: [],
          orgTypesAggregations:
            aggregations.orgTypes.buckets.map(aggregationToBucket),
          diversityDashboard: toDiversityDashboardAggregation(aggregations),
          namedLocationDiversityHeatmap: toNamedLocationDiversityHeatmap(
            aggregations,
            input
          )
        };

        const actualResponse = responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          isNavigationalQuery,
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          []
        );

        expect(actualResponse).toEqual(expectedResponse);
      });
    });
  });

  describe("adaptToTaggedInstitutionsSearchResponse", () => {
    it("should return the institutions from the elasticsearch response", async () => {
      const total = faker.datatype.number({ min: 1, max: 100 });
      const mockInstitutions = Array.from(
        { length: total },
        generateMockElasticsearchInstitution,
        total
      );

      const elasticSearchResponse =
        generateMockElasticsearchResponseWithAggregations(
          mockInstitutions as any as ElasticsearchTaggedInstitutionDoc[],
          undefined,
          total
        );

      const institutionsSearchResponse =
        await responseAdapter.adaptToTaggedInstitutionsSearchResponse(
          elasticSearchResponse
        );

      expect(institutionsSearchResponse).toEqual(
        expect.objectContaining({
          total,
          institutions: expect.arrayContaining(
            mockInstitutions.map((institution) => {
              return expect.objectContaining({
                id: institution.id,
                name: institution.name,
                masterOrganizationId: institution.masterOrganizationId,
                groupH1dnOrganizationId: institution.groupH1dnOrganizationId,
                location: institution.location,
                tagIds: institution.tagIds,
                address: {
                  street1: institution["address.street1"],
                  street2: institution["address.street2"],
                  street3: institution["address.street3"],
                  city: institution["address.city"],
                  postalCode: institution["address.postal_code"],
                  region: institution["address.region"],
                  regionCode: institution["address.region_code"],
                  country: institution["address.country"],
                  countryCode: institution["address.country_code"]
                },
                region: institution.region,
                patientsDiversity: institution.patientsDiversityRatio,
                patientsAge: institution.patientDiversity.age,
                patientsSex: institution.patientDiversity.sex
              });
            })
          )
        })
      );
    });
  });

  describe("adaptToEmptyResponse", () => {
    it("should return an empty response for a request type of iol", () => {
      const isQueryAnInstitution = faker.datatype.boolean();
      const response = responseAdapter.adaptToEmptyResponse(
        "iol",
        isQueryAnInstitution
      );

      expect(response).toEqual({
        data: {
          took: 0,
          timed_out: false,
          _shards: {
            failed: 0,
            successful: 0,
            total: 0
          },
          hits: {
            total: 0,
            hits: []
          }
        },
        peopleData: [],
        resolvedIndications: [],
        isQueryAnInstitution
      });
    });

    it("should return 0 for a request type of count", () => {
      const response = responseAdapter.adaptToEmptyResponse(
        "count",
        faker.datatype.boolean()
      );

      expect(response).toEqual(0);
    });
  });

  describe("getAffiliatedHCPs", () => {
    it("should correctly pull affiliated count", () => {
      const value = faker.datatype.string();
      const input: InstitutionsSearchInput = {
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
        filters: {
          patientClaimsFilter: {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.DIAGNOSES_CODE,
              value: [value],
              operator: RuleOperatorEnum.EQUAL
            }
          },
          affiliatedPeopleIolsIds: [faker.datatype.number().toString()]
        }
      };

      const institutionHits = [
        {
          masterOrganizationId: faker.datatype.number()
        } as ElasticsearchInstitutionDoc
      ];
      const elasticsearchResponse =
        generateMockElasticsearchResponseWithAggregations<ElasticsearchInstitutionDoc>(
          institutionHits
        );

      const expectedResultCount = faker.datatype.number();

      const institutionsSearchResponse =
        responseAdapter.adaptToInstitutionSearchResponse(
          input,
          elasticsearchResponse,
          [],
          false,
          generateInstitutionSearchFeatureFlags(),
          [],
          [],
          [],
          [],
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          {
            [elasticsearchResponse.hits.hits[0]._id]: expectedResultCount
          }
        );

      expect(institutionsSearchResponse).toEqual(
        expect.objectContaining({
          institutions: expect.arrayContaining([
            expect.objectContaining({
              people: expect.objectContaining({
                affiliatedMatched: expectedResultCount
              })
            })
          ])
        })
      );
    });
  });
});
