/* eslint-disable @typescript-eslint/no-non-null-assertion */
import {
  RPC_NAMESPACE_INSTITUTION_NAME_SUGGEST,
  InstitutionsSearchInput,
  SearchTypes,
  InstitutionNameSuggestResponse,
  InstitutionNameSuggestInput,
  InstitutionNameSuggestResult
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { INSTITUTION_ACCESS_LEVEL } from "@h1nyc/account-sdk";
import { Service } from "typedi";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { MAXIMUM_QUERY_TOKENS } from "./KeywordSearchResourceServiceRewrite";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";
import {
  SearchHit,
  SearchHitsMetadata,
  SearchRequest,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import { InstitutionNameSearchResourceService } from "./InstitutionNameSearchResourceService";
import {
  InstitutionSearchFeatureFlags,
  featureFlagDefaults,
  institutionSearchFeatureFlagTypes
} from "./InstitutionsResourceService";
import { ElasticSearchInstitutionsService } from "./ElasticSearchInstitutionsService";

const SOURCE_FIELDS = [
  "id",
  "groupH1dnOrganizationId",
  "masterOrganizationId",
  "name",
  "address",
  "person_count",
  "trials_count",
  "website"
];

const HAS_ADVANCED_OPERATORS = /(\sAND\s|\sOR\s|NOT\s)/;
const LIMIT = 5;
const OFFSET = 0;

const EMPTY_RESPONSE = {
  total: 0,
  institutions: []
};

export type ElasticsearchInstitutionDoc = {
  id: string;
  institutionId: number;
  masterOrganizationId?: number;
  groupH1dnOrganizationId?: string;
  name: string;
  person_count: number;
  trials_count: number;
  "address.region"?: string;
  "address.region_code"?: string;
  "address.country"?: string;
  "address.country_code"?: string;
  "address.city"?: string;
  website?: string;
};

function getInstitutionsSearchInput(
  input: InstitutionNameSuggestInput
): InstitutionsSearchInput {
  return {
    ...input,
    paging: {
      limit: LIMIT,
      offset: OFFSET
    },
    accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
    searchType: SearchTypes.NAME
  };
}

function adaptToNameSuggestResponse(
  nameSearchResponse: SearchHitsMetadata<ElasticsearchInstitutionDoc>
) {
  const hitsWithSource = nameSearchResponse.hits.filter(
    (doc: SearchHit) => !!doc._source
  );

  const suggestResults: Array<InstitutionNameSuggestResult> =
    hitsWithSource.map(
      (
        result: SearchHit<ElasticsearchInstitutionDoc>
      ): InstitutionNameSuggestResult => {
        return {
          id: result._source!.id,
          institutionId: result._source!.institutionId,
          masterOrganizationId: result._source!.masterOrganizationId,
          groupH1dnOrganizationId: result._source!.groupH1dnOrganizationId,
          name: result._source!.name,
          personCount: result._source!.person_count ?? 0,
          trialCount: result._source!.trials_count ?? 0,
          address: {
            region: result._source!["address.region"],
            regionCode: result._source!["address.region_code"],
            country: result._source!["address.country"],
            countryCode: result._source!["address.country_code"],
            city: result._source!["address.city"]
          },
          website: result._source!.website
        };
      }
    );

  return {
    total: getTotal(nameSearchResponse.total),
    institutions: suggestResults
  };
}

function getTotal(total: number | SearchTotalHits | undefined): number {
  if (total) {
    return typeof total === "number" ? total : total.value;
  }
  return 0;
}

@Service()
@RpcService()
export class InstitutionNameSuggestResourceService
  extends RpcResourceService
  implements InstitutionNameSuggestResourceService
{
  private readonly logger = createLogger(this);

  constructor(
    config: ConfigService,
    private featureFlagsService: FeatureFlagsService,
    private elasticSearchService: ElasticSearchInstitutionsService,
    private searchAnalyticsTracerService: SearchAnalyticsTracerService,
    private institutionNameSearch: InstitutionNameSearchResourceService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_INSTITUTION_NAME_SUGGEST,
      config.searchRedisOptions
    );
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.institutions.name.suggest")
  async institutionNameSuggestSearch(
    input: InstitutionNameSuggestInput
  ): Promise<InstitutionNameSuggestResponse> {
    if (!input.query?.length || HAS_ADVANCED_OPERATORS.test(input.query)) {
      return EMPTY_RESPONSE;
    }

    input.query =
      this.sanitizeQuery(
        this.truncateQuery(input.query)?.replace(/[´]/g, "'")
      ) ?? "";

    const response = await this.doNameSuggest(input);

    this.searchAnalyticsTracerService.sendAnalyticsEvent({
      event: "institution_name_suggest_results",
      properties: {
        query: input.query,
        input,
        total: response.total,
        institutionIds: response.institutions.map(
          (institution: InstitutionNameSuggestResult) => institution.id
        ),
        projectId: input.projectId,
        type: "INSTITUTION_NAME_SUGGEST_SEARCH"
      },
      timestamp: new Date(),
      userId: input.userId
    });

    return response;
  }

  private truncateQuery(query?: string) {
    if (query) {
      return query.trim().split(/\s+/).slice(0, MAXIMUM_QUERY_TOKENS).join(" ");
    }
    return query;
  }

  private sanitizeQuery(query?: string) {
    return query?.replace(/[´]/g, "'");
  }

  @Trace("h1-search.institution.name.suggest.query")
  private async doNameSuggest(
    input: InstitutionNameSuggestInput
  ): Promise<InstitutionNameSuggestResponse> {
    const featureFlagValues = await this.getFeatureFlagValues(input);
    if (!featureFlagValues.enableInstitutionNameSuggest) {
      return EMPTY_RESPONSE;
    }

    const isBulkSearch = false;
    const body = await this.institutionNameSearch.buildNameSearchRequest(
      getInstitutionsSearchInput(input),
      featureFlagValues,
      isBulkSearch,
      "iol",
      [],
      []
    );

    if (!body) {
      return EMPTY_RESPONSE;
    }

    const request: SearchRequest = {
      ...body,
      _source: SOURCE_FIELDS,
      aggs: undefined
    };

    this.logger.debug({ data: request }, "Requesting institution name suggest");

    const result =
      await this.elasticSearchService.query<ElasticsearchInstitutionDoc>(
        request
      );

    return adaptToNameSuggestResponse(result.hits);
  }

  @Trace("h1-search.institutions.getFeatureFlagValues")
  private async getFeatureFlagValues({
    userId,
    projectId
  }: InstitutionNameSuggestInput): Promise<InstitutionSearchFeatureFlags> {
    const featureFlags: Partial<InstitutionSearchFeatureFlags> = {};

    const user = userId && projectId ? { userId, projectId } : undefined;
    const flagsState = await this.featureFlagsService.getAllFlags(user);

    for (const flag of institutionSearchFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as InstitutionSearchFeatureFlags;
  }
}
