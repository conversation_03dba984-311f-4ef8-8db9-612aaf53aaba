import { estypes } from "@elastic/elasticsearch";
import {
  IolClaim,
  IolClaimsInput,
  IolClaimsResponse,
  IolClaimType
} from "@h1nyc/search-sdk";
import _ from "lodash";
import { Service } from "typedi";
import { InstitutionClaimsQueryBuilderOptions } from "./queryBuilders/InstitutionClaimsQueryBuilder";

type NestedClaimsPath = "diagnoses" | "procedures" | "prescriptions";
@Service()
export class InstitutionsClaimsResponseAdapterService {
  adaptToInstitutionClaimsSearchResponse(
    input: Readonly<IolClaimsInput>,
    data: estypes.SearchResponse,
    options?: InstitutionClaimsQueryBuilderOptions
  ): IolClaimsResponse {
    const useClaimType = input.filters.type;
    let claimPath: NestedClaimsPath;
    if (useClaimType === IolClaimType.Diagnosis) {
      claimPath = "diagnoses";
    } else if (useClaimType === IolClaimType.Procedure) {
      claimPath = "procedures";
    } else {
      claimPath = "prescriptions";
    }

    const claimsNumber: number = _.get(
      data,
      `hits.hits[0].inner_hits["${claimPath}"].hits.total.value`,
      0
    );
    if (claimsNumber) {
      const claims: Array<IolClaim> =
        _.get(data, `hits.hits[0].inner_hits["${claimPath}"]`)!.hits.hits.map(
          (claim) =>
            this.extractClaimsFromInnerHits(
              claimPath,
              claim,
              input.iolId,
              options
            )
        ) ?? [];
      return {
        type: claimPath,
        count: claimsNumber,
        items: claims
      };
    } else {
      return {
        type: claimPath,
        count: 0,
        items: []
      };
    }
  }

  private extractClaimsFromInnerHits(
    claimType: "procedures" | "diagnoses" | "prescriptions",
    claim: estypes.SearchHit<Record<string, any>>,
    iolId: string,
    options?: InstitutionClaimsQueryBuilderOptions
  ) {
    const isProcedureWithUniqueCount =
      claimType === "procedures" &&
      !options?.disableUniquePatientCountForOnlyProcedures;
    const isDiagnosis = claimType === "diagnoses";
    const shouldUseUniquePatientCountForClaim =
      (isProcedureWithUniqueCount || isDiagnosis) &&
      options?.shouldUseUniquePatientCount;
    let claimCountField = shouldUseUniquePatientCountForClaim
      ? "internalUniqueCount"
      : "count";

    if (claimType === "prescriptions") {
      claimCountField = "num_prescriptions";
    }

    return {
      code: _.get(claim, "_source.code_eng", ""),
      claimsCount: _.get(
        claim,
        `fields["${claimType}.${claimCountField}"][0]`,
        0
      ),
      description: _.get(claim, "_source.description_eng", ""),
      genericName: _.get(claim, "_source.generic_name", ""),
      iolId,
      rank: _.get(claim, `fields["${claimType}.countryRank"][0]`, 0),
      totalInCountry: _.get(
        claim,
        `fields["${claimType}.totalInCountry"][0]`,
        0
      ),
      regionRank: _.get(claim, `fields["${claimType}.stateRank"][0]`),
      totalInRegion: _.get(claim, `fields["${claimType}.totalInState"][0]`)
    };
  }
}
