import { ConfigService } from "./ConfigService";
import { ITree, BothQueryParseResult } from "../lib/ParserTypes/types";
import { TwitterQueryParserService } from "./TwitterQueryParserService";
import { Service } from "typedi";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { ParsedQueryTree } from "./KeywordSearchResourceServiceRewrite";
import * as _ from "lodash";
import { Synonym } from "../proto/synonym_pb";
import { getQuerySynonymList } from "../lib/query_understanding/QueryUnderstandingResponseAnalyzer";
import { createLogger } from "../lib/Logger";

const HAS_ADVANCED_OPERATORS = /\s(AND|OR|NOT)\s/;
const ALL_ANDs = /\bAND\b/g;
const ALL_ORs = /\bOR\b/g;
const OR = "|";
const EMPTY_STRING = "";

export type QueryParserOptions = {
  projectSupportsAdvancedOperators: boolean;
};

export type QueryParserResponse = {
  parsedQueryTree: ParsedQueryTree;
  synonyms: Array<string>;
  queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse;
};

@Service()
export class QueryParserService {
  private readonly logger = createLogger(this);

  constructor(
    config: ConfigService,
    private twitterQueryParserService: TwitterQueryParserService,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient
  ) {}

  async getKeywordSynonyms(keyword: string): Promise<string[]> {
    // TODO: Should pass detected language based on user preferred language
    const queryUnderstandingServiceResponse =
      await this.queryUnderstandingServiceClient.analyze(keyword, "eng");

    if (_.isEmpty(keyword)) {
      throw new Error("Expected non-empty keyword");
    }

    const synonyms = getQuerySynonymList(
      keyword,
      queryUnderstandingServiceResponse
    );

    this.logger.debug(`Found synonyms for input query: ${synonyms}`);

    return synonyms;
  }

  async parseQueryWithQueryUnderstandingService(
    query: string | undefined,
    options: QueryParserOptions
  ): Promise<QueryParserResponse | undefined> {
    if (!query || _.trim(query) === EMPTY_STRING) {
      return undefined;
    }

    if (!options.projectSupportsAdvancedOperators) {
      return {
        parsedQueryTree: query,
        synonyms: []
      };
    }

    if (!HAS_ADVANCED_OPERATORS.test(query)) {
      const queryUnderstandingServiceResponse =
        await this.queryUnderstandingServiceClient.analyze(query, "eng");

      const parsedQueryTree = queryUnderstandingServiceResponse
        .getAugmentedQuery()
        .replace(ALL_ORs, OR)
        .replace(ALL_ANDs, EMPTY_STRING);

      return {
        parsedQueryTree,
        synonyms: this.getQuerySynonymList(
          query,
          queryUnderstandingServiceResponse
        ),
        queryUnderstandingServiceResponse
      };
    }

    return {
      parsedQueryTree: this.parseQuery(query),
      synonyms: []
    };
  }

  private getQuerySynonymList(
    query: string,
    response: QueryUnderstandingServiceResponse
  ): Array<string> {
    const retrievedSynonyms = response
      .getUnigramSynonymList()
      .filter(hasSynonymsForSuppliedQuery(query))
      .map(getFirstSynonym)
      .filter(isNonEmptyString)
      .sort();

    return retrievedSynonyms;
  }

  /**
   * Parses query of the form BOTH( Cardiology WITH diabetes ) to -
   * BothQueryParseResult {
   *  usedBoth: true,
       terms: ["Cardiology","diabetes"]
   * }
   * @param query 
   * @returns BothQueryParseResult
   */
  parseBothQuery(query: string | undefined): BothQueryParseResult {
    const result: BothQueryParseResult = {
      usedBoth: false,
      terms: []
    };

    if (!query || _.trim(query) === EMPTY_STRING) return result;

    const bothRegex = /BOTH\s*\(\s*(.+?)\s*\)$/i;
    const match = query.match(bothRegex);

    if (!match) return result;

    const innerContent = match[1];

    // ✅ Validate balanced parentheses
    if (!areParenthesesBalanced(innerContent)) {
      return result; // Invalid if not balanced
    }

    result.usedBoth = true;

    // Now split by WITH outside of parentheses
    const terms = [];
    let current = "";
    let parenDepth = 0;

    //We are not directly spliting on a WITH keyword because a indication could have with word present
    //For example BOTH (cardiology WITH somewordwithotherword) will resolve to
    //terms = [cardiology, somewordwithotherword]
    for (let i = 0; i < innerContent.length; i++) {
      const char = innerContent[i];

      if (char === "(") parenDepth++;
      if (char === ")") parenDepth--;

      if (
        parenDepth === 0 &&
        innerContent.slice(i, i + 4).toUpperCase() === "WITH" &&
        /\s/.test(innerContent[i + 4] || "")
      ) {
        terms.push(current.trim());
        current = "";
        i += 3; // skip 'WITH'
      } else {
        current += char;
      }
    }

    if (current.trim()) {
      terms.push(current.trim());
    }

    result.terms = terms;

    return result;
  }

  rewriteBothQueryToOrExpression(parsed: BothQueryParseResult): string {
    if (parsed.terms.length === 0) return "";

    return parsed.terms
      .map((term) => {
        const trimmed = term.trim();
        const hasOuterParens = trimmed.startsWith("(") && trimmed.endsWith(")");
        return hasOuterParens ? trimmed : `(${trimmed})`;
      })
      .join(" OR ");
  }

  /**
   * Replaces occurences of our DSL with the neccessary symbols for the library we are using to parse a query string
   * into the parse tree. Library is defined here https://github.com/tweetdeck/twitter-search-query-parser
   * @param query
   * @returns
   */
  parseQuery(query: string): ITree {
    const parseTree = this.twitterQueryParserService.parseQuery(
      this.rewriteQuery(this.groupTerms(query))
    );
    return parseTree;
  }

  /**
   * Method that applies our transformation methods to the input query to prepare the query for parsing through the
   * Tweetdeck parser. Current replaces AND with a space, NOT with a tilde, OR with OR (these first 3 are case insensitive
   * in case we start trying to support that), strips colons and semi-colons because the Tweetdeck parser considers
   * those to be operators, and folds Microsoft-style start and end quotes into normal double quotes.
   * @param query User-provided query string
   * @returns Modified query string to translate our supported grammar into the grammar that
   * the Tweetdeck parser expects
   */
  rewriteQuery(query: string): string {
    const andRegex = /\bAND\b/g;
    const notRegex = /\bNOT\b/g;
    const orRegex = /\bOR\b/g;
    return query
      .replace(";", "")
      .replace(":", "")
      .replace(/[“”]/g, '"')
      .replace(andRegex, " ")
      .replace(notRegex, "~")
      .replace(/~\s+/, "~")
      .replace(orRegex, "OR");
  }

  groupTerms(query: string): string {
    const terms = query
      .split(" ")
      .map((t) => t.trim())
      .filter((t) => t.length !== 0);
    let queue: string[] = [];
    let newQuery = "";
    const specialTerms = ["AND", "OR", "NOT", "(", ")"];
    terms.forEach((term) => {
      if (specialTerms.includes(term)) {
        let toAppend = "";
        if (queue.length > 1) {
          if (term === ")") {
            toAppend = `(${queue.join(" ")})${term}`;
          } else {
            toAppend = `(${queue.join(" ")}) ${term}`;
          }
        } else if (queue.length === 1) {
          if (term === ")") {
            toAppend = `${queue[0]}${term}`;
          } else {
            toAppend = `${queue[0]} ${term}`;
          }
        } else {
          toAppend = `${term}`;
        }
        if (newQuery.charAt(newQuery.length - 1) === "(") {
          newQuery += toAppend;
        } else {
          if (toAppend === ")") {
            newQuery += `${toAppend}`;
          } else {
            newQuery += ` ${toAppend}`;
          }
        }
        queue = [];
      } else {
        queue.push(term);
      }
    });

    if (queue.length) {
      if (newQuery.length) {
        if (queue.length > 1) {
          newQuery += ` (${queue.join(" ")})`;
        } else {
          newQuery += ` ${queue.join(" ")}`;
        }
      } else {
        newQuery += queue.join(" ");
      }
      queue = [];
    }
    return newQuery
      .replace(/["] [(]/g, '"(')
      .replace(/[)] ["]/g, ')"')
      .trim();
  }
}

function hasSynonymsForSuppliedQuery(query: string) {
  return (synonym: Synonym) => synonym.getOrig() === query;
}

function getFirstSynonym(synonym: Synonym) {
  const contextFreeSynonyms = synonym.getContextFreeSynList();

  if (!contextFreeSynonyms.length) {
    return undefined;
  }

  return contextFreeSynonyms[0]?.getSyn();
}

function isNonEmptyString(
  synonym: ReturnType<typeof getFirstSynonym>
): synonym is string {
  return typeof synonym === "string" && synonym.length > 0;
}

// ✅ Helper to check for balanced parentheses
function areParenthesesBalanced(text: string): boolean {
  let count = 0;
  for (const char of text) {
    if (char === "(") count++;
    if (char === ")") count--;
    if (count < 0) return false; // closing before opening
  }
  return count === 0;
}
