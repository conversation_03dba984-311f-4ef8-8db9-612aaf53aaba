import { faker } from "@faker-js/faker";
import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  CompositeAggregation,
  LOCALITY_FILTER,
  PatientStatsService,
  REGION_FILTER
} from "./PatientStatsService";

function generateMockCompositeAggregation(
  location_names: string[],
  after_key?: {
    location_name: string;
  }
): CompositeAggregation {
  return {
    composite_agg: {
      after_key,
      buckets: location_names.map((location_name) => {
        return {
          key: {
            location_name
          },
          race: {
            buckets: [
              "Mixed",
              "White",
              "Not Disclosed",
              "Asian",
              "Black",
              "Indigenous"
            ].map((race) => {
              return {
                key: race,
                doc_count: faker.datatype.number()
              };
            })
          },
          gender: {
            buckets: ["M", "F"].map((race) => {
              return {
                key: race,
                doc_count: faker.datatype.number()
              };
            })
          },
          age: {
            buckets: [
              "<18",
              "18-24",
              "25-29",
              "30-34",
              "35-39",
              "40-44",
              "45-49",
              "50-54",
              "55-59",
              "60-64",
              "65-69",
              "70-74",
              "75-79",
              "80-84",
              ">84"
            ].map((race) => {
              return {
                key: race,
                doc_count: faker.datatype.number()
              };
            })
          }
        };
      })
    }
  };
}

function generateMockAggregation() {
  return {
    race: {
      buckets: [
        "Mixed",
        "White",
        "Not Disclosed",
        "Asian",
        "Black",
        "Indigenous"
      ].map((race) => {
        return {
          key: race,
          doc_count: faker.datatype.number()
        };
      })
    },
    gender: {
      buckets: ["M", "F"].map((race) => {
        return {
          key: race,
          doc_count: faker.datatype.number()
        };
      })
    },
    age: {
      buckets: [
        "<18",
        "18-24",
        "25-29",
        "30-34",
        "35-39",
        "40-44",
        "45-49",
        "50-54",
        "55-59",
        "60-64",
        "65-69",
        "70-74",
        "75-79",
        "80-84",
        ">84"
      ].map((race) => {
        return {
          key: race,
          doc_count: faker.datatype.number()
        };
      })
    }
  };
}

describe("PatientStatsService", () => {
  describe("heatmap", () => {
    it("should return error when both indications and icd_codes filter is empty", async () => {
      const configService = createMockInstance(ConfigService);
      const elasticSearchService = createMockInstance(ElasticSearchService);
      configService.elasticPatientIndex = faker.datatype.string();

      const locality_names = [
        faker.address.cityName(),
        faker.address.cityName()
      ];
      const region_names = [faker.address.state()];

      const region_agg = generateMockCompositeAggregation(region_names);
      const locality_agg = generateMockCompositeAggregation(locality_names);

      elasticSearchService.query.mockResolvedValueOnce({
        aggregations: region_agg
      } as any);
      elasticSearchService.query.mockResolvedValueOnce({
        aggregations: locality_agg
      } as any);

      const patientStatsService = new PatientStatsService(
        configService,
        elasticSearchService
      );

      const input = {
        indications: [],
        icdCodes: [],
        cities: [],
        states: [],
        level: "region"
      };
      await expect(patientStatsService.heatmap(input)).rejects.toThrowError();
    });

    it("should use indications filter when present", async () => {
      const configService = createMockInstance(ConfigService);
      const elasticSearchService = createMockInstance(ElasticSearchService);
      configService.elasticPatientIndex = faker.datatype.string();

      const region_names = [faker.address.state()];

      const region_agg = generateMockCompositeAggregation(region_names);

      elasticSearchService.query.mockResolvedValueOnce({
        aggregations: region_agg
      } as any);

      const patientStatsService = new PatientStatsService(
        configService,
        elasticSearchService
      );

      const input = {
        indications: [faker.datatype.string()],
        icdCodes: [],
        cities: [],
        states: [],
        level: "region"
      };
      await patientStatsService.heatmap(input);

      expect(elasticSearchService.query).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          index: configService.elasticPatientIndex,
          size: 0,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              should: [
                expect.objectContaining({
                  terms: {
                    indications: input.indications
                  }
                })
              ],
              minimum_should_match: 1,
              filter: expect.objectContaining(REGION_FILTER)
            })
          })
        })
      );
    });

    it("should use icd_codes filter when present", async () => {
      const configService = createMockInstance(ConfigService);
      const elasticSearchService = createMockInstance(ElasticSearchService);
      configService.elasticPatientIndex = faker.datatype.string();

      const region_names = [faker.address.state()];

      const region_agg = generateMockCompositeAggregation(region_names);

      elasticSearchService.query.mockResolvedValueOnce({
        aggregations: region_agg
      } as any);

      const patientStatsService = new PatientStatsService(
        configService,
        elasticSearchService
      );

      const input = {
        indications: [],
        icdCodes: [faker.datatype.string()],
        cities: [],
        states: [],
        level: "region"
      };
      await patientStatsService.heatmap(input);

      expect(elasticSearchService.query).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          index: configService.elasticPatientIndex,
          size: 0,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              should: [
                expect.objectContaining({
                  terms: {
                    icd_codes: input.icdCodes
                  }
                })
              ],
              minimum_should_match: 1,
              filter: expect.objectContaining(REGION_FILTER)
            })
          })
        })
      );
    });

    it("should use both indications and icd_codes filter when present", async () => {
      const configService = createMockInstance(ConfigService);
      const elasticSearchService = createMockInstance(ElasticSearchService);
      configService.elasticPatientIndex = faker.datatype.string();
      const region_names = [faker.address.state()];

      const region_agg = generateMockCompositeAggregation(region_names);

      elasticSearchService.query.mockResolvedValueOnce({
        aggregations: region_agg
      } as any);

      const patientStatsService = new PatientStatsService(
        configService,
        elasticSearchService
      );

      const input = {
        indications: [faker.datatype.string()],
        icdCodes: [faker.datatype.string()],
        cities: [],
        states: [],
        level: "region"
      };
      await patientStatsService.heatmap(input);

      expect(elasticSearchService.query).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          index: configService.elasticPatientIndex,
          size: 0,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              should: [
                {
                  terms: {
                    icd_codes: input.icdCodes
                  }
                },
                {
                  terms: {
                    indications: input.indications
                  }
                }
              ],
              minimum_should_match: 1,
              filter: expect.objectContaining(REGION_FILTER)
            })
          })
        })
      );
    });

    it("should use after_key to scroll through locations", async () => {
      const configService = createMockInstance(ConfigService);
      const elasticSearchService = createMockInstance(ElasticSearchService);
      configService.elasticPatientIndex = faker.datatype.string();

      const locality_names_1 = [
        faker.address.cityName(),
        faker.address.cityName()
      ];
      const locality_names_2 = [
        faker.address.cityName(),
        faker.address.cityName()
      ];

      const locality_agg_1 = generateMockCompositeAggregation(
        locality_names_1,
        {
          location_name: locality_names_1[1]
        }
      );
      const locality_agg_2 = generateMockCompositeAggregation(locality_names_2);
      elasticSearchService.query.mockResolvedValueOnce({
        aggregations: locality_agg_1
      } as any);
      elasticSearchService.query.mockResolvedValueOnce({
        aggregations: locality_agg_2
      } as any);

      const patientStatsService = new PatientStatsService(
        configService,
        elasticSearchService
      );

      const input = {
        indications: [faker.datatype.string()],
        icdCodes: [],
        cities: [],
        states: [],
        level: "locality"
      };
      await patientStatsService.heatmap(input);

      expect(elasticSearchService.query).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining({
          index: configService.elasticPatientIndex,
          size: 0,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              should: [
                {
                  terms: {
                    indications: input.indications
                  }
                }
              ],
              minimum_should_match: 1,
              filter: LOCALITY_FILTER
            })
          }),
          aggs: expect.objectContaining({
            composite_agg: expect.objectContaining({
              composite: expect.objectContaining({
                after: {
                  location_name: locality_names_1[1]
                }
              })
            })
          })
        })
      );
    });
  });

  describe("stats", () => {
    it("should use REGION level filter when input is empty", async () => {
      const configService = createMockInstance(ConfigService);
      const elasticSearchService = createMockInstance(ElasticSearchService);
      configService.elasticPatientIndex = faker.datatype.string();

      elasticSearchService.query.mockResolvedValueOnce({
        aggregations: generateMockAggregation()
      } as any);

      const patientStatsService = new PatientStatsService(
        configService,
        elasticSearchService
      );

      const input = {
        indications: [],
        icdCodes: [],
        cities: [],
        states: [],
        level: "region"
      };
      await patientStatsService.stats(input);

      expect(elasticSearchService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticPatientIndex,
          size: 0,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: [REGION_FILTER]
            })
          })
        })
      );
    });

    it("should use indication or ICD when present", async () => {
      const configService = createMockInstance(ConfigService);
      const elasticSearchService = createMockInstance(ElasticSearchService);
      configService.elasticPatientIndex = faker.datatype.string();

      elasticSearchService.query.mockResolvedValueOnce({
        aggregations: generateMockAggregation()
      } as any);

      const patientStatsService = new PatientStatsService(
        configService,
        elasticSearchService
      );

      const input = {
        indications: [faker.datatype.string()],
        icdCodes: [faker.datatype.string()],
        cities: [],
        states: [],
        level: "region"
      };
      const result = await patientStatsService.stats(input);

      expect(elasticSearchService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticPatientIndex,
          size: 0,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: [
                {
                  bool: {
                    should: [
                      {
                        terms: {
                          icd_codes: input.icdCodes
                        }
                      },
                      {
                        terms: {
                          indications: input.indications
                        }
                      }
                    ],
                    minimum_should_match: 1
                  }
                },
                REGION_FILTER
              ]
            })
          })
        })
      );

      expect(result).toEqual({
        patientCount: expect.anything(),
        gender: expect.arrayContaining([]),
        age: expect.arrayContaining([]),
        race: expect.arrayContaining([])
      });
    });

    it("should use location filter when present", async () => {
      const configService = createMockInstance(ConfigService);
      const elasticSearchService = createMockInstance(ElasticSearchService);
      configService.elasticPatientIndex = faker.datatype.string();

      const aggregations = generateMockAggregation();
      elasticSearchService.query.mockResolvedValueOnce({
        aggregations
      } as any);

      const patientStatsService = new PatientStatsService(
        configService,
        elasticSearchService
      );

      const input = {
        indications: [faker.datatype.string()],
        icdCodes: [faker.datatype.string()],
        cities: [faker.address.cityName()],
        states: [faker.address.state()],
        level: "region"
      };
      const result = await patientStatsService.stats(input);

      expect(elasticSearchService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticPatientIndex,
          size: 0,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: [
                {
                  bool: {
                    should: [
                      {
                        terms: {
                          icd_codes: input.icdCodes
                        }
                      },
                      {
                        terms: {
                          indications: input.indications
                        }
                      }
                    ],
                    minimum_should_match: 1
                  }
                },
                {
                  bool: {
                    should: [
                      {
                        bool: {
                          filter: [
                            LOCALITY_FILTER,
                            {
                              terms: {
                                location_name: input.cities
                              }
                            }
                          ]
                        }
                      },
                      {
                        bool: {
                          filter: [
                            REGION_FILTER,
                            {
                              terms: {
                                location_name: input.states
                              }
                            }
                          ]
                        }
                      }
                    ],
                    minimum_should_match: 1
                  }
                }
              ]
            })
          })
        })
      );

      expect(result).toEqual({
        patientCount:
          aggregations.gender.buckets[0].doc_count +
          aggregations.gender.buckets[1].doc_count,
        gender: expect.arrayContaining([]),
        age: expect.arrayContaining([]),
        race: expect.arrayContaining([])
      });
    });
  });
});
