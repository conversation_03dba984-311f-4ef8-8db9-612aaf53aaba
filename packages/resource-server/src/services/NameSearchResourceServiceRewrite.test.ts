import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { faker } from "@faker-js/faker";
import {
  featureFlagDefaults,
  NameSearchFeatureFlags,
  NameSearchResourceServiceRewrite
} from "./NameSearchResourceServiceRewrite";
import {
  generateFilters,
  generateMockElasticsearchHit,
  generateMockElasticsearchResponse
} from "./KeywordSearchResourceServiceRewrite.test";
import {
  CHINESE,
  ENGLISH,
  JAPANESE,
  Language,
  LanguageDetectService
} from "./LanguageDetectService";
import { NameSearchInput } from "@h1nyc/search-sdk/dist/interfaces/NameSearchInput";
import { FeatureFlagsService, LDUserInput } from "@h1nyc/systems-feature-flags";
import { LDFlagsState } from "launchdarkly-node-server-sdk";
import { ElasticSearchService } from "./ElasticSearchService";
import { KeywordFilterClauseBuilderService } from "./KeywordFilterClauseBuilderService";
import {
  AppName,
  SavedTerritoryResourceClient,
  UserResourceClient
} from "@h1nyc/account-sdk";
import { generateMockLanguage } from "./NameSuggestResourceService.test";
import NameSearchBuilderFactory from "./queryBuilders/NameSearchBuilderFactory";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import CalculateMinimumNameVariations from "./queryBuilders/CalculateMinimumNameVariations";
import _ from "lodash";
import { NameSearchResponseAdapterService } from "./NameSearchResponseAdapterService";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";
import {
  generateMockFOPersonSearchResponse,
  generateMockPersonSearchResponse
} from "./NameSearchResponseAdapterService.test";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { UserOnboardingDataService } from "./UserOnboardingDataService";
import {
  EMPTY_FILTER_COUNTS,
  EMPTY_RANGES,
  ZERO_MIN_MAX
} from "./KeywordSearchResponseAdapterService";
export const projectId = faker.datatype.string();
const hits = [
  generateMockElasticsearchHit(projectId),
  generateMockElasticsearchHit(projectId)
];

export function generateNameSearchInput(
  overrides: Partial<NameSearchInput> = {}
): NameSearchInput {
  const page = {
    from: faker.datatype.number(),
    size: faker.datatype.number()
  };
  const userId = faker.datatype.uuid();
  const query = faker.datatype.string();
  const suppliedFilters = generateFilters();
  const projectFeatures = {
    searchMultiLanguage: true,
    claims: true,
    referrals: true,
    engagementsV2: faker.datatype.boolean(),
    translateTaiwan: true
  };
  const appNames = Object.values(AppName);
  const appName =
    appNames[faker.datatype.number({ min: 0, max: appNames.length - 1 })];

  const baseInput = {
    userId,
    page,
    projectId,
    query,
    suppliedFilters,
    projectFeatures,
    appName
  };

  return { ...baseInput, ...overrides };
}

export function generateMockOnboardingData() {
  return {
    indications: [faker.datatype.string(), faker.datatype.string()],
    countries: [faker.address.country(), faker.address.country()],
    states: [faker.address.state(), faker.address.state()]
  };
}

function generateNameSearchFeatureFlags(
  overrides: Partial<NameSearchFeatureFlags> = {}
): NameSearchFeatureFlags {
  return {
    enableResultsOutsideUsersSlice: faker.datatype.boolean(),
    enableHighConfHCPFeature: faker.datatype.boolean(),
    enableCTMSV2: faker.datatype.boolean(),
    enableIntentBasedSearchQuery: faker.datatype.boolean(),
    enableNameSearchPersonalisation: faker.datatype.boolean(),
    enableQueryIntent: faker.datatype.boolean(),
    enableTagsInElasticsearch: faker.datatype.boolean(),
    enableBrazilianClaims: faker.datatype.boolean(),
    enableUniquePatientCountForClaims: false,
    disableUniquePatientCountForOnlyProcedures: false,
    enableNestedIndicationFilter: false,
    enableCcsrExclusionForMatchedCounts: false,
    enableLocationFilterRegionRollup: false,
    enableNewGlobalLeaderTier: false,
    enableExUSGlobalLeader: false,
    enableCountrySpecificNonIndicationLeaderFilters: false,
    ...overrides
  };
}

function mockFeatureFlagsService(
  overrides: Partial<NameSearchFeatureFlags> = {}
) {
  const values = Object.entries(featureFlagDefaults).reduce(
    (acc, [key, value]) => {
      if (overrides[key as keyof NameSearchFeatureFlags]) {
        acc[value.key] = overrides[key as keyof NameSearchFeatureFlags]!;
      } else {
        acc[value.key] = value.default;
      }
      return acc;
    },
    {} as Record<string, boolean>
  );

  const featureFlagsService = createMockInstance(
    FeatureFlagsService as any
  ) as jest.Mocked<FeatureFlagsService>;

  featureFlagsService.getFlag.mockImplementation(
    (flagName: string): Promise<boolean> => {
      return Promise.resolve(values[flagName]);
    }
  );

  featureFlagsService.getAllFlags.mockImplementation(
    (_user?: LDUserInput): Promise<LDFlagsState> => {
      return Promise.resolve({
        valid: true,
        getFlagValue: jest.fn().mockImplementation((flagName: string) => {
          return values[flagName];
        }),
        getFlagReason: jest.fn(),
        allValues: jest.fn(),
        toJSON: jest.fn()
      });
    }
  );

  return featureFlagsService;
}

describe("NameSearchResourceServiceRewrite", () => {
  describe("negative tests", () => {
    it("should rethrow an error thrown by elasticSearchService.query", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const err = new Error(
        "elasticSearchService.query threw an error for some reason"
      );

      elasticSearchService.query.mockRejectedValue(err);

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );
      keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
        []
      );

      const featureFlagsService = mockFeatureFlagsService();

      const languageDetectService = createMockInstance(LanguageDetectService);
      const languageDetector = (value: string) => ENGLISH as Language;
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );
      const userResourceClient = createMockInstance(UserResourceClient);
      userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
        generateMockLanguage("english")
      );

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();
      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
        2
      );

      const queryUnderstandingService = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const nameSearchResponseAdapterService = createMockInstance(
        NameSearchResponseAdapterService
      );

      const input = generateNameSearchInput();
      mockQueryUnderstandingServiceResponse(
        queryUnderstandingService,
        input.query!
      );
      nameSearchResponseAdapterService.adapt.mockReturnValue(
        generateMockPersonSearchResponse(input)
      );

      nameSearchResponseAdapterService.adapt.mockReturnValue(
        generateMockPersonSearchResponse(input)
      );
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );
      userOnboardingDataService.getOnboardingData.mockResolvedValue(
        generateMockOnboardingData()
      );
      const savedTerritoryResourceClient = createMockInstance(
        SavedTerritoryResourceClient
      );
      const nameSearchResourceServiceRewrite =
        new NameSearchResourceServiceRewrite(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          featureFlagsService,
          keywordFilterClauseBuilderService,
          nameSearchBuilderFactory,
          queryUnderstandingService,
          calculateMinimumNameVariations,
          searchAnalyticsTracerService,
          nameSearchResponseAdapterService,
          userOnboardingDataService,
          savedTerritoryResourceClient
        );

      try {
        await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);
        fail("an error should have been thrown");
      } catch (thrownErr) {
        expect(thrownErr).toEqual(err);
      }
    });
  });

  describe("multiLang disabled", () => {
    describe("user's preferred language is eng", () => {
      it("should use eng fields", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.query.mockResolvedValue(
          generateMockElasticsearchResponse(hits)
        );

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );
        keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
          []
        );

        const nameSearchResponseAdapterService = createMockInstance(
          NameSearchResponseAdapterService
        );

        const featureFlagsService = mockFeatureFlagsService();

        const languageDetectService = createMockInstance(LanguageDetectService);
        const languageDetector = (value: string) => ENGLISH as Language;
        languageDetectService.getLanguageDetector.mockReturnValue(
          languageDetector
        );
        languageDetectService.shouldUseBothCmnAndJpnFields.mockReturnValue(
          false
        );

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage("english")
        );

        const nameSearchBuilderFactory = new NameSearchBuilderFactory();

        const calculateMinimumNameVariations = createMockInstance(
          CalculateMinimumNameVariations
        );

        calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
          hits.length
        );
        const queryUnderstandingService = createMockInstance(
          QueryUnderstandingServiceClient
        );

        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );

        const userOnboardingDataService = createMockInstance(
          UserOnboardingDataService
        );
        userOnboardingDataService.getOnboardingData.mockResolvedValue(
          generateMockOnboardingData()
        );
        const savedTerritoryResourceClient = createMockInstance(
          SavedTerritoryResourceClient
        );
        const nameSearchResourceServiceRewrite =
          new NameSearchResourceServiceRewrite(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            featureFlagsService,
            keywordFilterClauseBuilderService,
            nameSearchBuilderFactory,
            queryUnderstandingService,
            calculateMinimumNameVariations,
            searchAnalyticsTracerService,
            nameSearchResponseAdapterService,
            userOnboardingDataService,
            savedTerritoryResourceClient
          );

        const input = generateNameSearchInput({
          projectFeatures: {
            searchMultiLanguage: false,
            claims: true,
            referrals: true,
            engagementsV2: true,
            translateTaiwan: true
          }
        });
        mockQueryUnderstandingServiceResponse(
          queryUnderstandingService,
          input.query!
        );
        nameSearchResponseAdapterService.adapt.mockReturnValue(
          generateMockPersonSearchResponse(input)
        );

        await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

        expect(nameSearchResponseAdapterService.adapt).toHaveBeenCalledWith(
          ENGLISH,
          input.page,
          expect.any(Object),
          [],
          input.projectFeatures,
          false,
          expect.any(Object),
          input,
          false
        );

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(input.userId, input.projectId);
      });
    });

    describe("user's preferred language is cmn", () => {
      it("should use 'eng' fields anyway", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.query.mockResolvedValue(
          generateMockElasticsearchResponse(hits)
        );

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );
        keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
          []
        );

        const nameSearchResponseAdapterService = createMockInstance(
          NameSearchResponseAdapterService
        );

        const featureFlagsService = mockFeatureFlagsService();

        const languageDetectService = createMockInstance(LanguageDetectService);
        const languageDetector = (value: string) => ENGLISH as Language;
        languageDetectService.getLanguageDetector.mockReturnValue(
          languageDetector
        );
        languageDetectService.shouldUseBothCmnAndJpnFields.mockReturnValue(
          false
        );

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage("chinese_simplified")
        );

        const nameSearchBuilderFactory = new NameSearchBuilderFactory();

        const calculateMinimumNameVariations = createMockInstance(
          CalculateMinimumNameVariations
        );

        calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
          hits.length
        );
        const queryUnderstandingService = createMockInstance(
          QueryUnderstandingServiceClient
        );

        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );

        const userOnboardingDataService = createMockInstance(
          UserOnboardingDataService
        );
        userOnboardingDataService.getOnboardingData.mockResolvedValue(
          generateMockOnboardingData()
        );
        const savedTerritoryResourceClient = createMockInstance(
          SavedTerritoryResourceClient
        );
        const nameSearchResourceServiceRewrite =
          new NameSearchResourceServiceRewrite(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            featureFlagsService,
            keywordFilterClauseBuilderService,
            nameSearchBuilderFactory,
            queryUnderstandingService,
            calculateMinimumNameVariations,
            searchAnalyticsTracerService,
            nameSearchResponseAdapterService,
            userOnboardingDataService,
            savedTerritoryResourceClient
          );

        const input = generateNameSearchInput({
          projectFeatures: {
            searchMultiLanguage: false,
            claims: true,
            referrals: true,
            engagementsV2: true,
            translateTaiwan: true
          }
        });
        mockQueryUnderstandingServiceResponse(
          queryUnderstandingService,
          input.query!
        );
        nameSearchResponseAdapterService.adapt.mockReturnValue(
          generateMockPersonSearchResponse(input)
        );

        await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

        expect(nameSearchResponseAdapterService.adapt).toHaveBeenCalledWith(
          ENGLISH,
          input.page,
          expect.any(Object),
          [],
          input.projectFeatures,
          false,
          expect.any(Object),
          input,
          false
        );

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(input.userId, input.projectId);
      });
    });

    describe("user's preferred language is jpn", () => {
      it("should use 'eng' fields anyway", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.query.mockResolvedValue(
          generateMockElasticsearchResponse(hits)
        );

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );
        keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
          []
        );

        const nameSearchResponseAdapterService = createMockInstance(
          NameSearchResponseAdapterService
        );

        const featureFlagsService = mockFeatureFlagsService();

        const languageDetectService = createMockInstance(LanguageDetectService);
        const languageDetector = (value: string) => ENGLISH as Language;
        languageDetectService.getLanguageDetector.mockReturnValue(
          languageDetector
        );
        languageDetectService.shouldUseBothCmnAndJpnFields.mockReturnValue(
          false
        );

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage("japanese")
        );

        const nameSearchBuilderFactory = new NameSearchBuilderFactory();

        const calculateMinimumNameVariations = createMockInstance(
          CalculateMinimumNameVariations
        );

        calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
          hits.length
        );
        const queryUnderstandingService = createMockInstance(
          QueryUnderstandingServiceClient
        );

        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );

        const userOnboardingDataService = createMockInstance(
          UserOnboardingDataService
        );
        userOnboardingDataService.getOnboardingData.mockResolvedValue(
          generateMockOnboardingData()
        );
        const savedTerritoryResourceClient = createMockInstance(
          SavedTerritoryResourceClient
        );
        const nameSearchResourceServiceRewrite =
          new NameSearchResourceServiceRewrite(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            featureFlagsService,
            keywordFilterClauseBuilderService,
            nameSearchBuilderFactory,
            queryUnderstandingService,
            calculateMinimumNameVariations,
            searchAnalyticsTracerService,
            nameSearchResponseAdapterService,
            userOnboardingDataService,
            savedTerritoryResourceClient
          );

        const input = generateNameSearchInput({
          projectFeatures: {
            searchMultiLanguage: false,
            claims: true,
            referrals: true,
            engagementsV2: true,
            translateTaiwan: true
          }
        });
        mockQueryUnderstandingServiceResponse(
          queryUnderstandingService,
          input.query!
        );
        nameSearchResponseAdapterService.adapt.mockReturnValue(
          generateMockPersonSearchResponse(input)
        );

        await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

        expect(nameSearchResponseAdapterService.adapt).toHaveBeenCalledWith(
          ENGLISH,
          input.page,
          expect.any(Object),
          [],
          input.projectFeatures,
          false,
          expect.any(Object),
          input,
          false
        );

        expect(
          userResourceClient.getUsersPreferredLanguage
        ).toHaveBeenCalledWith(input.userId, input.projectId);
      });
    });
  });

  describe("multiLang enabled", () => {
    describe("with non empty input.query", () => {
      describe("detected language is 'eng'", () => {
        it("should use 'eng' fields when query's detected language is 'eng'", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.query.mockResolvedValue(
            generateMockElasticsearchResponse(hits)
          );

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );
          keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
            []
          );

          const nameSearchResponseAdapterService = createMockInstance(
            NameSearchResponseAdapterService
          );

          const featureFlagsService = mockFeatureFlagsService();

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const languageDetector = (value: string) => ENGLISH as Language;
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );
          languageDetectService.shouldUseBothCmnAndJpnFields.mockReturnValue(
            false
          );

          const userResourceClient = createMockInstance(UserResourceClient);
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage("english")
          );

          const nameSearchBuilderFactory = new NameSearchBuilderFactory();

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
            hits.length
          );
          const queryUnderstandingService = createMockInstance(
            QueryUnderstandingServiceClient
          );

          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );
          const savedTerritoryResourceClient = createMockInstance(
            SavedTerritoryResourceClient
          );
          const nameSearchResourceServiceRewrite =
            new NameSearchResourceServiceRewrite(
              configService,
              elasticSearchService,
              languageDetectService,
              userResourceClient,
              featureFlagsService,
              keywordFilterClauseBuilderService,
              nameSearchBuilderFactory,
              queryUnderstandingService,
              calculateMinimumNameVariations,
              searchAnalyticsTracerService,
              nameSearchResponseAdapterService,
              userOnboardingDataService,
              savedTerritoryResourceClient
            );

          const input = generateNameSearchInput({
            projectFeatures: {
              searchMultiLanguage: true,
              claims: true,
              referrals: true,
              engagementsV2: true,
              translateTaiwan: true
            }
          });
          mockQueryUnderstandingServiceResponse(
            queryUnderstandingService,
            input.query!
          );
          nameSearchResponseAdapterService.adapt.mockReturnValue(
            generateMockPersonSearchResponse(input)
          );

          await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

          expect(nameSearchResponseAdapterService.adapt).toHaveBeenCalledWith(
            ENGLISH,
            input.page,
            expect.any(Object),
            [],
            input.projectFeatures,
            false,
            expect.any(Object),
            input,
            false
          );

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(input.userId, input.projectId);
        });
      });

      describe("detected language is 'jpn'", () => {
        it("should use 'jpn' fields when query's detected language is 'jpn'", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.query.mockResolvedValue(
            generateMockElasticsearchResponse(hits)
          );

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );
          keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
            []
          );

          const nameSearchResponseAdapterService = createMockInstance(
            NameSearchResponseAdapterService
          );

          const featureFlagsService = mockFeatureFlagsService();

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const languageDetector = (value: string) => JAPANESE as Language;
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );
          languageDetectService.shouldUseBothCmnAndJpnFields.mockReturnValue(
            false
          );

          const userResourceClient = createMockInstance(UserResourceClient);
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage("japanese")
          );

          const nameSearchBuilderFactory = new NameSearchBuilderFactory();

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
            hits.length
          );
          const queryUnderstandingService = createMockInstance(
            QueryUnderstandingServiceClient
          );

          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );
          const savedTerritoryResourceClient = createMockInstance(
            SavedTerritoryResourceClient
          );
          const nameSearchResourceServiceRewrite =
            new NameSearchResourceServiceRewrite(
              configService,
              elasticSearchService,
              languageDetectService,
              userResourceClient,
              featureFlagsService,
              keywordFilterClauseBuilderService,
              nameSearchBuilderFactory,
              queryUnderstandingService,
              calculateMinimumNameVariations,
              searchAnalyticsTracerService,
              nameSearchResponseAdapterService,
              userOnboardingDataService,
              savedTerritoryResourceClient
            );

          const input = generateNameSearchInput({
            projectFeatures: {
              searchMultiLanguage: true,
              claims: true,
              referrals: true,
              engagementsV2: true,
              translateTaiwan: true
            }
          });

          nameSearchResponseAdapterService.adapt.mockReturnValue(
            generateMockPersonSearchResponse(input)
          );

          await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

          expect(nameSearchResponseAdapterService.adapt).toHaveBeenCalledWith(
            JAPANESE,
            input.page,
            expect.any(Object),
            [],
            input.projectFeatures,
            false,
            expect.any(Object),
            input,
            false
          );

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(input.userId, input.projectId);
        });
      });

      describe("detected language is 'cmn'", () => {
        it("should use 'cmn' fields when query's detected language is 'cmn'", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.query.mockResolvedValue(
            generateMockElasticsearchResponse(hits)
          );

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );
          keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
            []
          );

          const nameSearchResponseAdapterService = createMockInstance(
            NameSearchResponseAdapterService
          );

          const featureFlagsService = mockFeatureFlagsService();

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const languageDetector = (value: string) => CHINESE as Language;
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );
          languageDetectService.shouldUseBothCmnAndJpnFields.mockReturnValue(
            false
          );

          const userResourceClient = createMockInstance(UserResourceClient);
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage("english")
          );

          const nameSearchBuilderFactory = new NameSearchBuilderFactory();

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
            hits.length
          );
          const queryUnderstandingService = createMockInstance(
            QueryUnderstandingServiceClient
          );

          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );
          const savedTerritoryResourceClient = createMockInstance(
            SavedTerritoryResourceClient
          );
          const nameSearchResourceServiceRewrite =
            new NameSearchResourceServiceRewrite(
              configService,
              elasticSearchService,
              languageDetectService,
              userResourceClient,
              featureFlagsService,
              keywordFilterClauseBuilderService,
              nameSearchBuilderFactory,
              queryUnderstandingService,
              calculateMinimumNameVariations,
              searchAnalyticsTracerService,
              nameSearchResponseAdapterService,
              userOnboardingDataService,
              savedTerritoryResourceClient
            );

          const input = generateNameSearchInput({
            projectFeatures: {
              searchMultiLanguage: true,
              claims: true,
              referrals: true,
              engagementsV2: true,
              translateTaiwan: true
            }
          });

          nameSearchResponseAdapterService.adapt.mockReturnValue(
            generateMockPersonSearchResponse(input)
          );

          await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

          expect(nameSearchResponseAdapterService.adapt).toHaveBeenCalledWith(
            CHINESE,
            input.page,
            expect.any(Object),
            [],
            input.projectFeatures,
            false,
            expect.any(Object),
            input,
            false
          );

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(input.userId, input.projectId);
        });
      });
    });

    describe("with empty query", () => {
      describe("user preferred language is jpn", () => {
        it("should use 'jpn' fields when user preferred language is jpn", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.query.mockResolvedValue(
            generateMockElasticsearchResponse(hits)
          );

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );
          keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
            []
          );

          const nameSearchResponseAdapterService = createMockInstance(
            NameSearchResponseAdapterService
          );

          const featureFlagsService = mockFeatureFlagsService();

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          languageDetectService.shouldUseBothCmnAndJpnFields.mockReturnValue(
            false
          );

          const userResourceClient = createMockInstance(UserResourceClient);
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage("japanese")
          );

          const nameSearchBuilderFactory = new NameSearchBuilderFactory();

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
            hits.length
          );
          const queryUnderstandingService = createMockInstance(
            QueryUnderstandingServiceClient
          );

          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );
          const savedTerritoryResourceClient = createMockInstance(
            SavedTerritoryResourceClient
          );
          const nameSearchResourceServiceRewrite =
            new NameSearchResourceServiceRewrite(
              configService,
              elasticSearchService,
              languageDetectService,
              userResourceClient,
              featureFlagsService,
              keywordFilterClauseBuilderService,
              nameSearchBuilderFactory,
              queryUnderstandingService,
              calculateMinimumNameVariations,
              searchAnalyticsTracerService,
              nameSearchResponseAdapterService,
              userOnboardingDataService,
              savedTerritoryResourceClient
            );

          const input = generateNameSearchInput({
            query: "",
            projectFeatures: {
              searchMultiLanguage: true,
              claims: true,
              referrals: true,
              engagementsV2: true,
              translateTaiwan: true
            }
          });

          nameSearchResponseAdapterService.adapt.mockReturnValue(
            generateMockPersonSearchResponse(input)
          );

          await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

          expect(nameSearchResponseAdapterService.adapt).toHaveBeenCalledWith(
            JAPANESE,
            input.page,
            expect.any(Object),
            [],
            input.projectFeatures,
            false,
            expect.any(Object),
            input,
            false
          );

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(input.userId, input.projectId);
        });
      });

      describe("user preferred language is cmn", () => {
        it("should use 'cmn' fields when user preferred language is cmn", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          elasticSearchService.query.mockResolvedValue(
            generateMockElasticsearchResponse(hits)
          );

          const keywordFilterClauseBuilderService = createMockInstance(
            KeywordFilterClauseBuilderService
          );
          keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
            []
          );

          const nameSearchResponseAdapterService = createMockInstance(
            NameSearchResponseAdapterService
          );

          const featureFlagsService = mockFeatureFlagsService();

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          languageDetectService.shouldUseBothCmnAndJpnFields.mockReturnValue(
            false
          );
          const userResourceClient = createMockInstance(UserResourceClient);
          userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
            generateMockLanguage("chinese_simplified")
          );

          const nameSearchBuilderFactory = new NameSearchBuilderFactory();

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
            hits.length
          );
          const queryUnderstandingService = createMockInstance(
            QueryUnderstandingServiceClient
          );

          const searchAnalyticsTracerService = createMockInstance(
            SearchAnalyticsTracerService
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );
          const savedTerritoryResourceClient = createMockInstance(
            SavedTerritoryResourceClient
          );
          const nameSearchResourceServiceRewrite =
            new NameSearchResourceServiceRewrite(
              configService,
              elasticSearchService,
              languageDetectService,
              userResourceClient,
              featureFlagsService,
              keywordFilterClauseBuilderService,
              nameSearchBuilderFactory,
              queryUnderstandingService,
              calculateMinimumNameVariations,
              searchAnalyticsTracerService,
              nameSearchResponseAdapterService,
              userOnboardingDataService,
              savedTerritoryResourceClient
            );
          const input = generateNameSearchInput({
            query: "",
            projectFeatures: {
              searchMultiLanguage: true,
              claims: true,
              referrals: true,
              engagementsV2: true,
              translateTaiwan: true
            }
          });

          nameSearchResponseAdapterService.adapt.mockReturnValue(
            generateMockPersonSearchResponse(input)
          );

          await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

          expect(nameSearchResponseAdapterService.adapt).toHaveBeenCalledWith(
            CHINESE,
            input.page,
            expect.any(Object),
            [],
            input.projectFeatures,
            false,
            expect.any(Object),
            input,
            false
          );

          expect(
            userResourceClient.getUsersPreferredLanguage
          ).toHaveBeenCalledWith(input.userId, input.projectId);
        });
      });
    });
  });

  describe("calculate minimum name variations", () => {
    describe("query is empty", () => {
      it("should not calculate minimum name variations for name search query", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.query.mockResolvedValue(
          generateMockElasticsearchResponse(hits)
        );

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );
        keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
          []
        );

        const featureFlagsService = mockFeatureFlagsService();

        const languageDetectService = createMockInstance(LanguageDetectService);
        const languageDetector = (value: string) => ENGLISH as Language;
        languageDetectService.getLanguageDetector.mockReturnValue(
          languageDetector
        );
        languageDetectService.shouldUseBothCmnAndJpnFields.mockReturnValue(
          false
        );

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage("english")
        );

        const nameSearchBuilderFactory = new NameSearchBuilderFactory();

        const calculateMinimumNameVariations = createMockInstance(
          CalculateMinimumNameVariations
        );
        calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
          hits.length
        );

        const queryUnderstandingService = createMockInstance(
          QueryUnderstandingServiceClient
        );

        const nameSearchResponseAdapterService = createMockInstance(
          NameSearchResponseAdapterService
        );

        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );

        const userOnboardingDataService = createMockInstance(
          UserOnboardingDataService
        );
        userOnboardingDataService.getOnboardingData.mockResolvedValue(
          generateMockOnboardingData()
        );
        const savedTerritoryResourceClient = createMockInstance(
          SavedTerritoryResourceClient
        );
        const nameSearchResourceServiceRewrite =
          new NameSearchResourceServiceRewrite(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            featureFlagsService,
            keywordFilterClauseBuilderService,
            nameSearchBuilderFactory,
            queryUnderstandingService,
            calculateMinimumNameVariations,
            searchAnalyticsTracerService,
            nameSearchResponseAdapterService,
            userOnboardingDataService,
            savedTerritoryResourceClient
          );

        const input = generateNameSearchInput({ query: "" });

        nameSearchResponseAdapterService.adapt.mockReturnValue(
          generateMockPersonSearchResponse(input)
        );

        await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);
        expect(
          calculateMinimumNameVariations.calculateMinimumFieldsToMatch
        ).not.toHaveBeenCalled();
      });
    });

    describe("query is not empty", () => {
      it("should calculate minimum name variations for name search query if lang is 'eng'", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.query.mockResolvedValue(
          generateMockElasticsearchResponse(hits)
        );

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );
        keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
          []
        );

        const featureFlagsService = mockFeatureFlagsService();

        const languageDetectService = createMockInstance(LanguageDetectService);
        const languageDetector = (value: string) => ENGLISH as Language;
        languageDetectService.getLanguageDetector.mockReturnValue(
          languageDetector
        );

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage("english")
        );

        const nameSearchBuilderFactory = new NameSearchBuilderFactory();

        const calculateMinimumNameVariations = createMockInstance(
          CalculateMinimumNameVariations
        );

        const queryUnderstandingService = createMockInstance(
          QueryUnderstandingServiceClient
        );

        queryUnderstandingService;

        const nameSearchResponseAdapterService = createMockInstance(
          NameSearchResponseAdapterService
        );

        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );

        const userOnboardingDataService = createMockInstance(
          UserOnboardingDataService
        );
        userOnboardingDataService.getOnboardingData.mockResolvedValue(
          generateMockOnboardingData()
        );
        const savedTerritoryResourceClient = createMockInstance(
          SavedTerritoryResourceClient
        );
        const nameSearchResourceServiceRewrite =
          new NameSearchResourceServiceRewrite(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            featureFlagsService,
            keywordFilterClauseBuilderService,
            nameSearchBuilderFactory,
            queryUnderstandingService,
            calculateMinimumNameVariations,
            searchAnalyticsTracerService,
            nameSearchResponseAdapterService,
            userOnboardingDataService,
            savedTerritoryResourceClient
          );

        const input = generateNameSearchInput();
        mockQueryUnderstandingServiceResponse(
          queryUnderstandingService,
          input.query!
        );
        nameSearchResponseAdapterService.adapt.mockReturnValue(
          generateMockPersonSearchResponse(input)
        );

        await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

        expect(
          calculateMinimumNameVariations.calculateMinimumFieldsToMatch
        ).toHaveBeenCalledWith(
          elasticSearchService,
          configService.elasticPeopleIndex,
          input.query
        );
      });

      it("should not calculate minimum name variations for name search query if lang is 'cmn'", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        elasticSearchService.query.mockResolvedValue(
          generateMockElasticsearchResponse(hits)
        );

        const keywordFilterClauseBuilderService = createMockInstance(
          KeywordFilterClauseBuilderService
        );
        keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
          []
        );

        const featureFlagsService = mockFeatureFlagsService();

        const languageDetectService = createMockInstance(LanguageDetectService);
        const languageDetector = (value: string) => CHINESE as Language;
        languageDetectService.getLanguageDetector.mockReturnValue(
          languageDetector
        );

        const userResourceClient = createMockInstance(UserResourceClient);
        userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
          generateMockLanguage("chinese_simplified")
        );

        const nameSearchBuilderFactory = new NameSearchBuilderFactory();

        const calculateMinimumNameVariations = createMockInstance(
          CalculateMinimumNameVariations
        );

        const queryUnderstandingService = createMockInstance(
          QueryUnderstandingServiceClient
        );

        const nameSearchResponseAdapterService = createMockInstance(
          NameSearchResponseAdapterService
        );

        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );

        const userOnboardingDataService = createMockInstance(
          UserOnboardingDataService
        );
        userOnboardingDataService.getOnboardingData.mockResolvedValue(
          generateMockOnboardingData()
        );
        const savedTerritoryResourceClient = createMockInstance(
          SavedTerritoryResourceClient
        );
        const nameSearchResourceServiceRewrite =
          new NameSearchResourceServiceRewrite(
            configService,
            elasticSearchService,
            languageDetectService,
            userResourceClient,
            featureFlagsService,
            keywordFilterClauseBuilderService,
            nameSearchBuilderFactory,
            queryUnderstandingService,
            calculateMinimumNameVariations,
            searchAnalyticsTracerService,
            nameSearchResponseAdapterService,
            userOnboardingDataService,
            savedTerritoryResourceClient
          );

        const input = generateNameSearchInput();
        mockQueryUnderstandingServiceResponse(
          queryUnderstandingService,
          input.query!
        );

        nameSearchResponseAdapterService.adapt.mockReturnValue(
          generateMockPersonSearchResponse(input)
        );

        await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

        expect(
          calculateMinimumNameVariations.calculateMinimumFieldsToMatch
        ).not.toHaveBeenCalled();
      });
    });
  });

  describe("get user onboarding preference", () => {
    it("should call getOnboardingData for Name search", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponse(hits)
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );
      keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
        []
      );

      const featureFlagsService = mockFeatureFlagsService();

      const languageDetectService = createMockInstance(LanguageDetectService);
      const languageDetector = (value: string) => ENGLISH as Language;
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );

      const userResourceClient = createMockInstance(UserResourceClient);
      userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
        generateMockLanguage("english")
      );

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();

      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      const queryUnderstandingService = createMockInstance(
        QueryUnderstandingServiceClient
      );

      queryUnderstandingService;

      const nameSearchResponseAdapterService = createMockInstance(
        NameSearchResponseAdapterService
      );

      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );
      userOnboardingDataService.getOnboardingData.mockResolvedValue(
        generateMockOnboardingData()
      );
      const savedTerritoryResourceClient = createMockInstance(
        SavedTerritoryResourceClient
      );
      const nameSearchResourceServiceRewrite =
        new NameSearchResourceServiceRewrite(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          featureFlagsService,
          keywordFilterClauseBuilderService,
          nameSearchBuilderFactory,
          queryUnderstandingService,
          calculateMinimumNameVariations,
          searchAnalyticsTracerService,
          nameSearchResponseAdapterService,
          userOnboardingDataService,
          savedTerritoryResourceClient
        );

      const input = generateNameSearchInput();
      mockQueryUnderstandingServiceResponse(
        queryUnderstandingService,
        input.query!
      );
      nameSearchResponseAdapterService.adapt.mockReturnValue(
        generateMockPersonSearchResponse(input)
      );

      await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

      expect(userOnboardingDataService.getOnboardingData).toHaveBeenCalledWith(
        input.userId,
        input.projectId
      );
    });
  });

  describe("FO Name search", () => {
    it("should call adaptFOResponse when runFONameSearch is called", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponse(hits)
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );
      keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
        []
      );

      const nameSearchResponseAdapterService = createMockInstance(
        NameSearchResponseAdapterService
      );

      const featureFlagsService = mockFeatureFlagsService();

      const languageDetectService = createMockInstance(LanguageDetectService);
      const languageDetector = (value: string) => ENGLISH as Language;
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );

      const userResourceClient = createMockInstance(UserResourceClient);

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();

      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      const queryUnderstandingService = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );

      const savedTerritoryResourceClient = createMockInstance(
        SavedTerritoryResourceClient
      );
      const nameSearchResourceServiceRewrite =
        new NameSearchResourceServiceRewrite(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          featureFlagsService,
          keywordFilterClauseBuilderService,
          nameSearchBuilderFactory,
          queryUnderstandingService,
          calculateMinimumNameVariations,
          searchAnalyticsTracerService,
          nameSearchResponseAdapterService,
          userOnboardingDataService,
          savedTerritoryResourceClient
        );
      const input = generateNameSearchInput({
        query: "",
        firstName: faker.datatype.string(),
        lastName: faker.datatype.string(),
        middleName: "",
        email: faker.internet.email()
      });

      nameSearchResponseAdapterService.adaptFOResponse.mockReturnValue(
        generateMockFOPersonSearchResponse(input)
      );

      await nameSearchResourceServiceRewrite.runFONameSearch(input);

      expect(
        nameSearchResponseAdapterService.adaptFOResponse
      ).toHaveBeenCalledWith(ENGLISH, input.page, expect.any(Object));
    });
    it("should call appropriate elastic.query when runFONameSearch is called with hcpId", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponse(hits)
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );
      keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
        []
      );

      const nameSearchResponseAdapterService = createMockInstance(
        NameSearchResponseAdapterService
      );

      const featureFlagsService = mockFeatureFlagsService();

      const languageDetectService = createMockInstance(LanguageDetectService);
      const languageDetector = (value: string) => ENGLISH as Language;
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );

      const userResourceClient = createMockInstance(UserResourceClient);

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();

      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      const queryUnderstandingService = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );

      const savedTerritoryResourceClient = createMockInstance(
        SavedTerritoryResourceClient
      );
      const nameSearchResourceServiceRewrite =
        new NameSearchResourceServiceRewrite(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          featureFlagsService,
          keywordFilterClauseBuilderService,
          nameSearchBuilderFactory,
          queryUnderstandingService,
          calculateMinimumNameVariations,
          searchAnalyticsTracerService,
          nameSearchResponseAdapterService,
          userOnboardingDataService,
          savedTerritoryResourceClient
        );
      const input = generateNameSearchInput({
        query: "",
        firstName: "",
        lastName: "",
        middleName: "",
        email: "",
        hcpId: faker.datatype.string()
      });

      nameSearchResponseAdapterService.adaptFOResponse.mockReturnValue(
        generateMockFOPersonSearchResponse(input)
      );

      await nameSearchResourceServiceRewrite.runFONameSearch(input);

      expect(elasticSearchService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: {
              filter: expect.arrayContaining([
                {
                  terms: {
                    id: [input.hcpId]
                  }
                },
                {
                  terms: {
                    projectIds: ["1"]
                  }
                }
              ])
            }
          })
        })
      );
    });
    it("should call appropriate elastic.query when runFONameSearch is called with hcpId and email", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponse(hits)
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );
      keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
        []
      );

      const nameSearchResponseAdapterService = createMockInstance(
        NameSearchResponseAdapterService
      );

      const featureFlagsService = mockFeatureFlagsService();

      const languageDetectService = createMockInstance(LanguageDetectService);
      const languageDetector = (value: string) => ENGLISH as Language;
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );

      const userResourceClient = createMockInstance(UserResourceClient);

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();

      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      const queryUnderstandingService = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );

      const savedTerritoryResourceClient = createMockInstance(
        SavedTerritoryResourceClient
      );
      const nameSearchResourceServiceRewrite =
        new NameSearchResourceServiceRewrite(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          featureFlagsService,
          keywordFilterClauseBuilderService,
          nameSearchBuilderFactory,
          queryUnderstandingService,
          calculateMinimumNameVariations,
          searchAnalyticsTracerService,
          nameSearchResponseAdapterService,
          userOnboardingDataService,
          savedTerritoryResourceClient
        );
      const input = generateNameSearchInput({
        query: "",
        firstName: "",
        lastName: "",
        middleName: "",
        email: faker.internet.email(),
        hcpId: faker.datatype.string()
      });

      nameSearchResponseAdapterService.adaptFOResponse.mockReturnValue(
        generateMockFOPersonSearchResponse(input)
      );

      await nameSearchResourceServiceRewrite.runFONameSearch(input);

      expect(elasticSearchService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            bool: {
              should: [
                {
                  terms: {
                    id: [input.hcpId]
                  }
                },
                {
                  term: {
                    emails: input.email
                  }
                }
              ],
              minimum_should_match: 1,
              filter: [
                {
                  terms: {
                    projectIds: ["1"]
                  }
                }
              ]
            }
          })
        })
      );
    });
  });

  describe("Advanced queries", () => {
    it("should not run name search if query contains advanced operators", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.query.mockRejectedValue(
        new Error("elasticSearchService.query threw an error for some reason")
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );
      keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
        []
      );

      const featureFlagsService = mockFeatureFlagsService();

      const languageDetectService = createMockInstance(LanguageDetectService);

      const userResourceClient = createMockInstance(UserResourceClient);

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();

      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      const queryUnderstandingService = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const nameSearchResponseAdapterService = createMockInstance(
        NameSearchResponseAdapterService
      );

      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );

      const savedTerritoryResourceClient = createMockInstance(
        SavedTerritoryResourceClient
      );
      const nameSearchResourceServiceRewrite =
        new NameSearchResourceServiceRewrite(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          featureFlagsService,
          keywordFilterClauseBuilderService,
          nameSearchBuilderFactory,
          queryUnderstandingService,
          calculateMinimumNameVariations,
          searchAnalyticsTracerService,
          nameSearchResponseAdapterService,
          userOnboardingDataService,
          savedTerritoryResourceClient
        );

      const input = generateNameSearchInput();
      const queries = [
        `${faker.datatype.string()} OR ${faker.datatype.string()}`,
        `${faker.datatype.string()} AND ${faker.datatype.string()}`,
        `NOT ${faker.datatype.string()}`
      ];

      queries.forEach(async (query) => {
        input.query = query;
        const response =
          await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

        expect(response).toEqual(
          expect.objectContaining({
            total: 0,
            normalizedRange: { ...ZERO_MIN_MAX },
            ranges: EMPTY_RANGES,
            filterCounts: EMPTY_FILTER_COUNTS,
            queryIntent: [],
            results: []
          })
        );
      });
    });
  });

  describe("Limit query tokens", () => {
    it("should limit query tokens to 10", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponse(hits)
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );
      keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
        []
      );

      const nameSearchResponseAdapterService = createMockInstance(
        NameSearchResponseAdapterService
      );

      const featureFlagsService = mockFeatureFlagsService();

      const languageDetectService = createMockInstance(LanguageDetectService);
      const languageDetector = (value: string) => ENGLISH as Language;
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );

      const userResourceClient = createMockInstance(UserResourceClient);
      userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
        generateMockLanguage("english")
      );

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();

      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
        hits.length
      );
      const queryUnderstandingService = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );
      userOnboardingDataService.getOnboardingData.mockResolvedValue(
        generateMockOnboardingData()
      );
      const savedTerritoryResourceClient = createMockInstance(
        SavedTerritoryResourceClient
      );
      const nameSearchResourceServiceRewrite =
        new NameSearchResourceServiceRewrite(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          featureFlagsService,
          keywordFilterClauseBuilderService,
          nameSearchBuilderFactory,
          queryUnderstandingService,
          calculateMinimumNameVariations,
          searchAnalyticsTracerService,
          nameSearchResponseAdapterService,
          userOnboardingDataService,
          savedTerritoryResourceClient
        );

      const tokens = [
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word()
      ];
      const input = generateNameSearchInput({
        query: tokens.join("  \t"),
        projectFeatures: {
          searchMultiLanguage: false,
          claims: true,
          referrals: true,
          engagementsV2: true,
          translateTaiwan: true
        }
      });
      mockQueryUnderstandingServiceResponse(
        queryUnderstandingService,
        faker.datatype.string()
      );
      nameSearchResponseAdapterService.adapt.mockReturnValue(
        generateMockPersonSearchResponse(input)
      );
      const createNameSearchBodySpy = jest.spyOn(
        nameSearchBuilderFactory.getNameSearchBuilder("eng"),
        "createNameSearchBody"
      );

      await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

      expect(queryUnderstandingService.analyze).toHaveBeenCalledWith(
        tokens.slice(0, 10).join(" "),
        "eng"
      );
      expect(createNameSearchBodySpy).toHaveBeenCalledWith(
        expect.objectContaining({
          query: tokens.slice(0, 10).join(" ")
        })
      );
    });
  });

  describe("Sanitize query", () => {
    it("should replace acute accent with single quote", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponse(hits)
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );
      keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
        []
      );

      const nameSearchResponseAdapterService = createMockInstance(
        NameSearchResponseAdapterService
      );

      const featureFlagsService = mockFeatureFlagsService();

      const languageDetectService = createMockInstance(LanguageDetectService);
      const languageDetector = (value: string) => ENGLISH as Language;
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );

      const userResourceClient = createMockInstance(UserResourceClient);
      userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
        generateMockLanguage("english")
      );

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();

      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
        hits.length
      );
      const queryUnderstandingService = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );
      userOnboardingDataService.getOnboardingData.mockResolvedValue(
        generateMockOnboardingData()
      );
      const savedTerritoryResourceClient = createMockInstance(
        SavedTerritoryResourceClient
      );
      const nameSearchResourceServiceRewrite =
        new NameSearchResourceServiceRewrite(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          featureFlagsService,
          keywordFilterClauseBuilderService,
          nameSearchBuilderFactory,
          queryUnderstandingService,
          calculateMinimumNameVariations,
          searchAnalyticsTracerService,
          nameSearchResponseAdapterService,
          userOnboardingDataService,
          savedTerritoryResourceClient
        );

      const tokens = [
        faker.lorem.word(),
        faker.lorem.word(),
        faker.lorem.word()
      ];
      const input = generateNameSearchInput({
        query: tokens.join("´"),
        projectFeatures: {
          searchMultiLanguage: false,
          claims: true,
          referrals: true,
          engagementsV2: true,
          translateTaiwan: true
        }
      });
      mockQueryUnderstandingServiceResponse(
        queryUnderstandingService,
        faker.datatype.string()
      );
      nameSearchResponseAdapterService.adapt.mockReturnValue(
        generateMockPersonSearchResponse(input)
      );
      const createNameSearchBodySpy = jest.spyOn(
        nameSearchBuilderFactory.getNameSearchBuilder("eng"),
        "createNameSearchBody"
      );

      await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

      expect(queryUnderstandingService.analyze).toHaveBeenCalledWith(
        tokens.join("'"),
        "eng"
      );
      expect(createNameSearchBodySpy).toHaveBeenCalledWith(
        expect.objectContaining({
          query: tokens.join("'")
        })
      );
    });
  });

  describe("Filters with slicing", () => {
    it("should add project id filter with default project id if enableResultsOutsideUsersSlice is true ", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponse(hits)
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );
      keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
        []
      );

      const nameSearchResponseAdapterService = createMockInstance(
        NameSearchResponseAdapterService
      );

      const featureFlagsService = mockFeatureFlagsService({
        enableResultsOutsideUsersSlice: true
      });

      const languageDetectService = createMockInstance(LanguageDetectService);
      const languageDetector = (value: string) => ENGLISH as Language;
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );

      const userResourceClient = createMockInstance(UserResourceClient);
      userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
        generateMockLanguage("english")
      );

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();

      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
        hits.length
      );
      const queryUnderstandingService = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );
      userOnboardingDataService.getOnboardingData.mockResolvedValue(
        generateMockOnboardingData()
      );
      const savedTerritoryResourceClient = createMockInstance(
        SavedTerritoryResourceClient
      );
      const nameSearchResourceServiceRewrite =
        new NameSearchResourceServiceRewrite(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          featureFlagsService,
          keywordFilterClauseBuilderService,
          nameSearchBuilderFactory,
          queryUnderstandingService,
          calculateMinimumNameVariations,
          searchAnalyticsTracerService,
          nameSearchResponseAdapterService,
          userOnboardingDataService,
          savedTerritoryResourceClient
        );

      const projectId = faker.datatype.string();
      const input = generateNameSearchInput({
        query: faker.datatype.string(),
        projectId,
        projectFeatures: {
          searchMultiLanguage: false,
          claims: true,
          referrals: true,
          engagementsV2: true,
          translateTaiwan: true
        }
      });
      mockQueryUnderstandingServiceResponse(
        queryUnderstandingService,
        faker.datatype.string()
      );
      nameSearchResponseAdapterService.adapt.mockReturnValue(
        generateMockPersonSearchResponse(input)
      );
      const createNameSearchBodySpy = jest.spyOn(
        nameSearchBuilderFactory.getNameSearchBuilder("eng"),
        "createNameSearchBody"
      );

      await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

      expect(createNameSearchBodySpy).toHaveBeenCalledWith(
        expect.objectContaining({
          filter: [
            {
              bool: {
                must: {
                  terms: {
                    projectIds: ["1", projectId]
                  }
                },
                must_not: {
                  term: {
                    otherProjectsPersonIn: projectId
                  }
                }
              }
            }
          ]
        })
      );
    });

    it("should call keywordFilterClauseBuilderService.buildForSearchRequest with correct parameters", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponse(hits)
      );

      const keywordFilterClauseBuilderService = createMockInstance(
        KeywordFilterClauseBuilderService
      );
      keywordFilterClauseBuilderService.buildForSearchRequest.mockResolvedValue(
        []
      );

      const nameSearchResponseAdapterService = createMockInstance(
        NameSearchResponseAdapterService
      );

      const nameSearchFeatureFlags = generateNameSearchFeatureFlags();
      const featureFlagsService = mockFeatureFlagsService(
        nameSearchFeatureFlags
      );

      const languageDetectService = createMockInstance(LanguageDetectService);
      const languageDetector = (value: string) => ENGLISH as Language;
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );

      const userResourceClient = createMockInstance(UserResourceClient);
      userResourceClient.getUsersPreferredLanguage.mockResolvedValue(
        generateMockLanguage("english")
      );

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();

      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      calculateMinimumNameVariations.calculateMinimumFieldsToMatch.mockResolvedValue(
        hits.length
      );
      const queryUnderstandingService = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );
      userOnboardingDataService.getOnboardingData.mockResolvedValue(
        generateMockOnboardingData()
      );
      const savedTerritoryResourceClient = createMockInstance(
        SavedTerritoryResourceClient
      );
      const nameSearchResourceServiceRewrite =
        new NameSearchResourceServiceRewrite(
          configService,
          elasticSearchService,
          languageDetectService,
          userResourceClient,
          featureFlagsService,
          keywordFilterClauseBuilderService,
          nameSearchBuilderFactory,
          queryUnderstandingService,
          calculateMinimumNameVariations,
          searchAnalyticsTracerService,
          nameSearchResponseAdapterService,
          userOnboardingDataService,
          savedTerritoryResourceClient
        );

      const projectId = faker.datatype.string();
      const input = generateNameSearchInput({
        query: faker.datatype.string(),
        projectId,
        projectFeatures: {
          searchMultiLanguage: false,
          claims: true,
          referrals: true,
          engagementsV2: true,
          translateTaiwan: true
        }
      });
      mockQueryUnderstandingServiceResponse(
        queryUnderstandingService,
        faker.datatype.string()
      );
      nameSearchResponseAdapterService.adapt.mockReturnValue(
        generateMockPersonSearchResponse(input)
      );

      await nameSearchResourceServiceRewrite.runNameSearchRewrite(input);

      expect(
        keywordFilterClauseBuilderService.buildForSearchRequest
      ).toHaveBeenCalledWith(
        input,
        expect.any(Function),
        nameSearchFeatureFlags,
        undefined,
        {
          skipProjectIdFilter: true
        },
        []
      );
    });
  });
});

function mockQueryUnderstandingServiceResponse(
  queryUnderstandingService: jest.Mocked<QueryUnderstandingServiceClient>,
  query: string
) {
  const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
    new QueryUnderstandingServiceResponse();
  queryUnderstandingServiceResponse.setAugmentedQuery(query);
  queryUnderstandingService.analyze.mockResolvedValue(
    queryUnderstandingServiceResponse
  );
}
