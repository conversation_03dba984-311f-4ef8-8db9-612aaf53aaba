import {
  CollaboratorsResource,
  HasCollaboratorsResponse,
  Collaborator,
  CollaboratorsResponse,
  RPC_NAMESPACE_COLLABORATORS
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import * as _ from "lodash";

import { FindCollaboratorsQueryBuilder } from "./queryBuilders/FindCollaboratorsQueryBuilder";
import { estypes } from "@elastic/elasticsearch";
import { QueryParserService } from "./QueryParserService";
import { ProjectFeaturesResourceClient } from "@h1nyc/account-sdk";
import { Service } from "typedi";
import { ProjectIdRequiredError } from "./errors/ProjectIdRequiredError";

export interface CollaboratorsCountResponse {
  hits: {
    total: {
      value: number;
    };
  };
}

export type FindCollaboratorsRequest = {
  personId: string;
  projectId: string;
  size: number;
  query?: string;
  minDate?: number | null;
};

type HCPDocument = {
  id: string;
  publicationCount: number;
  trialCount: number;
  congressCount: number;
};

const SELF = 1;

@Service()
@RpcService()
export class CollaboratorsResourceService
  extends RpcResourceService
  implements CollaboratorsResource
{
  private readonly logger = createLogger(this);
  private peopleIndex: string;

  constructor(
    config: ConfigService,
    private projectFeaturesResourceClient: ProjectFeaturesResourceClient,
    private elasticService: ElasticSearchService,
    private queryParserService: QueryParserService,
    private queryBuilder: FindCollaboratorsQueryBuilder
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_COLLABORATORS,
      config.searchRedisOptions
    );

    this.peopleIndex = config.elasticPeopleIndex;
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.collaborators.has-collaborators")
  async hasCollaborators(
    personId: string,
    projectId: string
  ): Promise<HasCollaboratorsResponse> {
    const countCollaboratorsRequest = buildCountCollaboratorsQuery(
      this.peopleIndex,
      personId,
      projectId
    );

    this.logger.debug(
      { data: countCollaboratorsRequest },
      "Performing collaborator count request"
    );

    const { count } = await this.elasticService.count(
      countCollaboratorsRequest
    );

    return {
      exists: count > 0
    };
  }

  @RpcMethod()
  @Trace("h1-search.collaborators.find-collaborators")
  async findCollaborators(
    personId: string,
    dateRange: {
      min?: number | null;
      max?: number | null;
    },
    page: {
      limit: number;
      offset: number;
    },
    terms?: string[],
    projectId?: string
  ) {
    if (!projectId) {
      // this is required because projectId is the last parameter but the 4th parameter is optional so
      // the last parameter cannot be made to be required
      throw new ProjectIdRequiredError();
    }

    const minDate = dateRange.min ?? undefined;
    const [query] = terms || [];

    const request = {
      personId,
      projectId,
      size: page.limit,
      minDate,
      query
    };

    return this.queryForCollaborators(request);
  }

  private async queryForCollaborators(
    request: FindCollaboratorsRequest
  ): Promise<CollaboratorsResponse> {
    const features =
      await this.projectFeaturesResourceClient.getProjectFeatures(
        request.projectId
      );

    const queryParserResponse =
      await this.queryParserService.parseQueryWithQueryUnderstandingService(
        request.query,
        {
          projectSupportsAdvancedOperators: features.advancedOperatorSearch
        }
      );

    const elasticsearchRequest = this.queryBuilder.build(
      request,
      queryParserResponse?.parsedQueryTree
    );

    this.logger.debug(
      { data: elasticsearchRequest },
      "Performing find collaborators search rewrite"
    );

    const { hits: collaboratorHits } =
      await this.elasticService.query<HCPDocument>(elasticsearchRequest);

    const isSelf = idsAreEqual.bind(null, request.personId);
    const isNotSelf = _.negate(isSelf);

    const containsSelf = collaboratorHits.hits.some(isSelf);

    const total = getTotal(collaboratorHits.total ?? 0);
    const nonSelfCollaborators = collaboratorHits.hits
      .filter(isNotSelf)
      .map(elasticsearchHitToCollaborator);

    return {
      total: containsSelf ? total - SELF : total,
      collaborators: nonSelfCollaborators
    };
  }
}

function idsAreEqual(id: string, hit: estypes.SearchHit<HCPDocument>) {
  return id === hit._source!.id;
}

function getTotal(total: number | estypes.SearchTotalHits): number {
  return typeof total === "number" ? total : total.value;
}

function elasticsearchHitToCollaborator(
  hit: estypes.SearchHit<HCPDocument>
): Collaborator {
  const publicationsCount = hit._source?.publicationCount ?? 0;
  const trialsCount = hit._source?.trialCount ?? 0;
  const congressCount = hit._source?.congressCount ?? 0;

  return {
    personId: hit._source!.id,
    count: hit._score || 0,
    totalCount: publicationsCount + trialsCount + congressCount
  };
}

function termFilter(
  field: string,
  value: string
): estypes.QueryDslQueryContainer {
  return {
    term: {
      [field]: value
    }
  };
}

function buildCountCollaboratorsQuery(
  index: string,
  personId: string,
  projectId: string
): estypes.CountRequest {
  const collaboratorIdsFilter = termFilter("collaboratorIds", personId);
  const personIdFilter = termFilter("id", personId);
  const projectIdFilter = termFilter("projectIds", projectId);

  return {
    index,
    query: {
      bool: {
        filter: [collaboratorIdsFilter, projectIdFilter],
        // An HCP cannot collaborate with themself
        must_not: [personIdFilter]
      }
    }
  };
}
