import { faker } from "@faker-js/faker";
import {
  ClaimsType,
  HCPLocation,
  MetricSummaryOptions
} from "@h1nyc/search-sdk";
import {
  ProjectFeaturesResourceClient,
  UserToken,
  ProjectFeatures
} from "@h1nyc/account-sdk";

import {
  createMockInstance,
  generateMockElasticsearchHit,
  generateMockElasticsearchResponse
} from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import {
  ASSET_FIELDS_TO_QUERY,
  HCPDocumentWithOnlyLocations,
  ProfileSearchResourceService
} from "./ProfileSearchResourceService";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  CHINESE,
  ENGLISH,
  JAPANESE,
  LanguageDetectService
} from "./LanguageDetectService";
import { QueryParserService } from "./QueryParserService";

import PublicationIdsResponse from "./__fixtures__/PublicationIdsResponse.json";
import PublicationMetricsResponse from "./__fixtures__/PublicationMetricsResponse.json";
import PublicationMetricsSearchResponse from "./__fixtures__/PublicationMetricsSearchResponse.json";
import PublicationMetricsResponseForPeople from "./__fixtures__/PublicationMetricsResponseForPeople.json";
import PublicationMetricsSearchResponseForPeople from "./__fixtures__/PublicationMetricsSearchResponseForPeople.json";

import TrialIdsResponse from "./__fixtures__/TrialIdsResponse.json";
import TrialMetricsSearchResponseForPeople from "./__fixtures__/TrialMetricsSearchResponseForPeople.json";
import TrialMetricsResponseForPeople from "./__fixtures__/TrialMetricsResponseForPeople.json";

import CongressIdsResponse from "./__fixtures__/CongressIdsResponse.json";
import CongressMetricsSearchResponseForPeople from "./__fixtures__/CongressMetricsSearchResponseForPeople.json";
import CongressMetricsResponseForPeople from "./__fixtures__/CongressMetricsResponseForPeople.json";

import AllMetricsResponseForPeople from "./__fixtures__/AllMetricsResponseForPeople.json";
import AllMetricsSearchResponseForPeople from "./__fixtures__/AllMetricsSearchResponseForPeople.json";

import AllSortedIdsEmptyResponseForPeople from "./__fixtures__/AllSortedIdsEmptyResponseForPeople.json";
import AllSortedIdsResponseForPeople from "./__fixtures__/AllSortedIdsResponseForPeople.json";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { ProfileSearchAsset } from "../lib/ProfileSearchBuilder";
import {
  SearchHit,
  QueryDslQueryContainer,
  SearchResponse
} from "@elastic/elasticsearch/lib/api/types";
import { ProfileFilterValueType } from "@h1nyc/search-sdk";
import { ProfileFilterValue } from "@h1nyc/search-sdk";
import { Location } from "./KeywordSearchResourceServiceRewrite";
import { stripLanguageSuffix } from "./NameSearchHighConfPersonAdapterService";
import _ from "lodash";
import { ElasticMCPClientService } from "./ElasticMCPClientService";

function generateMockElasticsearchResponseForPaginatedTrialsSearch(
  response: {
    totalCount: number;
    ctmsTrials: { id: string; affiliatedBy: string[] }[];
    h1Trials: { id: string; affiliatedBy: string[] }[];
  },
  innerHitsOverride?: Array<SearchHit<Record<string, any>>>
): SearchResponse {
  const innerHits: Array<SearchHit<Record<string, any>>> = [];

  response.ctmsTrials.forEach(({ id }) => {
    innerHits.push({
      _index: faker.datatype.string(),
      _id: faker.datatype.uuid(),
      fields: {
        "trials.id": [id],
        "trials.source": ["ctms"],
        "trials.affiliatedBy": ["principal investigator"]
      }
    });
  });

  response.h1Trials.forEach(({ id }) => {
    innerHits.push({
      _index: faker.datatype.string(),
      _id: faker.datatype.uuid(),
      fields: {
        "trials.id": [id],
        "trials.source": ["h1"],
        "trials.affiliatedBy": ["principal investigator"]
      }
    });
  });

  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: 1,
        relation: "eq"
      },
      max_score: faker.datatype.number(),

      hits: [
        {
          _id: faker.datatype.uuid(),
          _index: faker.datatype.string(),
          _source: {},
          inner_hits: {
            trials: {
              hits: {
                total: {
                  value: response.totalCount,
                  relation: "eq"
                },
                hits: innerHitsOverride ?? innerHits
              }
            }
          }
        }
      ]
    }
  };
}

function generateMockLocation(overrides = {}): Location {
  const location = {
    city_eng: faker.address.city(),
    state_eng: faker.address.state(),
    zipCode5_eng: faker.address.zipCode(),
    state_cmn: faker.address.state(),
    state_jpn: faker.address.state(),
    languageCode: [ENGLISH, CHINESE, JAPANESE][faker.datatype.number(2)],
    token: faker.datatype.string(),
    zipCode5_cmn: faker.address.zipCode(),
    zipCode5_jpn: faker.address.zipCode(),
    country_eng: faker.address.country(),
    zipCodeFull: faker.address.zipCode(),
    country_cmn: faker.address.country(),
    stateCode: faker.address.stateAbbr(),
    city_cmn: faker.address.city(),
    country_jpn: faker.address.country(),
    city_jpn: faker.address.city()
  };

  return { ...location, ...overrides };
}

function extractLocationField(
  source: HCPDocumentWithOnlyLocations
): HCPLocation[] {
  const hcpLocations = source?.locations?.map(
    (location: Location): HCPLocation | null => {
      const { languageCode } = location;
      if (!languageCode) {
        return null;
      }

      const fields = ["city", "state", "country", "zipCode5"].map(
        (prop) => `${prop}_${languageCode}`
      );

      // Initialize with empty strings
      const locFields: {
        city: string;
        state: string;
        country: string;
        zipCode5: string;
      } = {
        city: "",
        state: "",
        country: "",
        zipCode5: ""
      };

      fields.forEach((field) => {
        const strippedFieldName = stripLanguageSuffix(field);
        locFields[strippedFieldName as keyof typeof locFields] = _.get(
          location,
          field,
          ""
        );
      });

      if (
        !locFields.city &&
        !locFields.state &&
        !locFields.country &&
        !locFields.zipCode5
      ) {
        return null;
      }

      return locFields;
    }
  );
  return (_.compact(hcpLocations) ?? []) as HCPLocation[];
}

describe("getPublicationIds()", () => {
  it("returns empty array when no publications are returned", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("publications")) {
          return queryContainers[ProfileSearchAsset.Publications];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue({
      aggregations: {}
    });

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const ids = await profileSearchResourceService.getPublicationIds(
      "4667519",
      ["cancer"]
    );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(ids).toEqual([]);
  });

  it("returns the ids of the returned publications", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("publications")) {
          return queryContainers[ProfileSearchAsset.Publications];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      PublicationIdsResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const ids = await profileSearchResourceService.getPublicationIds(
      "4667519",
      ["cancer"]
    );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(ids).toEqual(
      expect.arrayContaining([
        "0065f861-545e-4057-8b32-226ddc19a544",
        "0104c044-1426-4c56-a9e2-117131d9393a"
      ])
    );
  });
});

describe("getPublicationIdsForPeople()", () => {
  it("returns empty array when no publications are returned", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("publications")) {
          return queryContainers[ProfileSearchAsset.Publications];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue({
      aggregations: {}
    });

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const ids = await profileSearchResourceService.getPublicationIdsForPeople(
      ["4667519", "4938278"],
      ["cancer"]
    );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(ids).toEqual([]);
  });

  it("returns the ids of the returned publications", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("publications")) {
          return queryContainers[ProfileSearchAsset.Publications];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      PublicationIdsResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const results =
      await profileSearchResourceService.getPublicationIdsForPeople(
        ["4667519", "4938278"],
        ["cancer"]
      );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(results).toEqual([
      {
        personId: "4667519",
        publicationIds: [
          "0065f861-545e-4057-8b32-226ddc19a544",
          "0104c044-1426-4c56-a9e2-117131d9393a"
        ]
      },
      {
        personId: "4938278",
        publicationIds: ["012964c2-ea38-34d2-9e82-f6e1c3b996cd"]
      }
    ]);
  });
});

describe("getPublicationMetrics()", () => {
  it("returns metrics when no search terms or filters were provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      PublicationMetricsResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();

    const metrics = await profileSearchResourceService.getPublicationMetrics(
      "4667519",
      projectId
    );
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(metrics).toEqual(
      expect.objectContaining({
        personId: "4667519",
        total: 4331,
        citationSum: 168684,
        microBloggingSum: 3626,
        guidelineSum: 10,
        totalByYear: expect.arrayContaining([
          expect.objectContaining({ total: 224, year: 2018 }),
          expect.objectContaining({ total: 235, year: 2019 }),
          expect.objectContaining({ total: 28, year: 2020 })
        ])
      })
    );
  });

  it("returns metrics when search terms are provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("publications")) {
          return queryContainers[ProfileSearchAsset.Publications];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      PublicationMetricsSearchResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();

    const metrics = await profileSearchResourceService.getPublicationMetrics(
      "4667519",
      projectId,
      ["cancer"]
    );
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(metrics).toEqual(
      expect.objectContaining({
        personId: "4667519",
        total: 1925,
        citationSum: 92271,
        microBloggingSum: 1889,
        guidelineSum: 5,
        totalByYear: expect.arrayContaining([
          expect.objectContaining({ total: 124, year: 2019 }),
          expect.objectContaining({ total: 62, year: 2020 }),
          expect.objectContaining({ total: 2, year: 2021 })
        ])
      })
    );
  });
});

describe("getPublicationMetricsForPeople()", () => {
  it("returns metrics when no search terms or filters were provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      PublicationMetricsResponseForPeople
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();

    const peopleMetrics =
      await profileSearchResourceService.getPublicationMetricsForPeople(
        ["4667519", "4938278"],
        projectId
      );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(peopleMetrics[0]).toEqual(
      expect.objectContaining({
        personId: "4667519",
        total: 4331,
        citationSum: 168684,
        microBloggingSum: 3626,
        guidelineSum: 50,
        totalByYear: expect.arrayContaining([
          expect.objectContaining({ total: 224, year: 2018 }),
          expect.objectContaining({ total: 235, year: 2019 }),
          expect.objectContaining({ total: 28, year: 2020 })
        ])
      })
    );
    expect(peopleMetrics[1]).toEqual(
      expect.objectContaining({
        personId: "4938278",
        total: 2340,
        citationSum: 100000,
        microBloggingSum: 2349,
        guidelineSum: 15,
        totalByYear: expect.arrayContaining([
          expect.objectContaining({ total: 150, year: 2018 }),
          expect.objectContaining({ total: 175, year: 2019 }),
          expect.objectContaining({ total: 20, year: 2020 })
        ])
      })
    );
  });

  it("returns metrics when search terms are provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("publications")) {
          return queryContainers[ProfileSearchAsset.Publications];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      PublicationMetricsSearchResponseForPeople
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();

    const peopleMetrics =
      await profileSearchResourceService.getPublicationMetricsForPeople(
        ["4667519", "4938278"],
        projectId,
        ["cancer"]
      );
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(peopleMetrics[0]).toEqual(
      expect.objectContaining({
        personId: "4667519",
        total: 1925,
        citationSum: 92271,
        microBloggingSum: 1889,
        guidelineSum: 5,
        totalByYear: expect.arrayContaining([
          expect.objectContaining({ total: 124, year: 2019 }),
          expect.objectContaining({ total: 62, year: 2020 }),
          expect.objectContaining({ total: 2, year: 2021 })
        ])
      })
    );
    expect(peopleMetrics[1]).toEqual(
      expect.objectContaining({
        personId: "4938278",
        total: 1725,
        citationSum: 87271,
        microBloggingSum: 1689,

        totalByYear: expect.arrayContaining([
          expect.objectContaining({ total: 104, year: 2019 }),
          expect.objectContaining({ total: 42, year: 2020 }),
          expect.objectContaining({ total: 1, year: 2021 })
        ])
      })
    );
  });
});

describe("getTrialIdsForPeople()", () => {
  it("returns empty array when no trials are returned", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("trials")) {
          return queryContainers[ProfileSearchAsset.ClinicalTrials];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue({
      aggregations: {}
    });

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const ids = await profileSearchResourceService.getTrialIdsForPeople(
      ["4667519", "4938278"],
      ["cancer"]
    );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(ids).toEqual([]);
  });

  it("returns the ids of the returned trials", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("trials")) {
          return queryContainers[ProfileSearchAsset.ClinicalTrials];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      TrialIdsResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const result = await profileSearchResourceService.getTrialIdsForPeople(
      ["4667519", "4938278"],
      ["cancer"]
    );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(result).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          personId: "4667519",
          trialIds: ["ChiCTR1800014479"]
        }),
        expect.objectContaining({
          personId: "4938278",
          trialIds: ["ChiCTR1800014247", "ChiCTR1800014340"]
        })
      ])
    );
  });
});

describe("getPaginatedTrialsForPerson()", () => {
  it("returns empty array when no trials are returned", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("trials")) {
          return queryContainers[ProfileSearchAsset.ClinicalTrials];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.query.mockResolvedValue({} as SearchResponse);

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();
    const personId = faker.datatype.uuid();
    const page = {
      limit: faker.datatype.number(),
      offset: faker.datatype.number()
    };
    const terms = Array.from({ length: 5 }, faker.random.word);
    const filters: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: Date.now(),
          max: Date.parse(faker.date.future().toDateString())
        }
      }
    ];

    const response =
      await profileSearchResourceService.getPaginatedTrialsForPerson({
        personId,
        projectId,
        page,
        terms,
        filters
      });

    expect(elasticSearchService.query).toHaveBeenCalledWith({
      index: configService.elasticPeopleIndex,
      size: 1,
      _source: false,
      query: expect.anything()
    });
    expect(response).toEqual({
      totalCount: 0,
      h1Trials: [],
      ctmsTrials: []
    });
  });

  it("returns the ids of the returned H1 and CTMS trials with the correct total", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("trials")) {
          return queryContainers[ProfileSearchAsset.ClinicalTrials];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    const expectedResponse = {
      totalCount: faker.datatype.number(),
      h1Trials: Array.from({ length: 5 }, () => ({
        id: faker.datatype.uuid(),
        affiliatedBy: ["principal investigator"]
      })),
      ctmsTrials: Array.from({ length: 5 }, () => ({
        id: faker.datatype.uuid(),
        affiliatedBy: ["principal investigator"]
      }))
    };

    elasticSearchService.query.mockResolvedValue(
      generateMockElasticsearchResponseForPaginatedTrialsSearch(
        expectedResponse
      )
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();
    const personId = faker.datatype.uuid();
    const page = {
      limit: faker.datatype.number(),
      offset: faker.datatype.number()
    };
    const terms = Array.from({ length: 5 }, faker.random.word);
    const filters: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: Date.now(),
          max: Date.parse(faker.date.future().toDateString())
        }
      }
    ];

    const response =
      await profileSearchResourceService.getPaginatedTrialsForPerson({
        personId,
        projectId,
        page,
        terms,
        filters
      });

    expect(elasticSearchService.query).toHaveBeenCalledWith({
      index: configService.elasticPeopleIndex,
      size: 1,
      _source: false,
      query: expect.anything()
    });
    expect(response).toEqual(expectedResponse);
  });

  it("should assume trial is H1 trial if source is missing", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("trials")) {
          return queryContainers[ProfileSearchAsset.ClinicalTrials];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    const expectedResponse = {
      totalCount: faker.datatype.number(),
      h1Trials: Array.from({ length: 5 }, () => ({
        id: faker.datatype.uuid(),
        affiliatedBy: ["principal investigator"]
      })),
      ctmsTrials: Array.from({ length: 5 }, () => ({
        id: faker.datatype.uuid(),
        affiliatedBy: ["principal investigator"]
      }))
    };

    const innerHitsOverride: Array<SearchHit<Record<string, any>>> = [];
    expectedResponse.ctmsTrials.forEach(({ id, affiliatedBy }) => {
      innerHitsOverride.push({
        _index: faker.datatype.string(),
        _id: faker.datatype.uuid(),
        fields: {
          "trials.id": [id],
          "trials.affiliatedBy": affiliatedBy
        }
      });
    });
    expectedResponse.h1Trials.forEach(({ id, affiliatedBy }) => {
      innerHitsOverride.push({
        _index: faker.datatype.string(),
        _id: faker.datatype.uuid(),
        fields: {
          "trials.id": [id],
          "trials.source": ["h1"],
          "trials.affiliatedBy": affiliatedBy
        }
      });
    });

    elasticSearchService.query.mockResolvedValue(
      generateMockElasticsearchResponseForPaginatedTrialsSearch(
        expectedResponse,
        innerHitsOverride
      )
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();
    const personId = faker.datatype.uuid();
    const page = {
      limit: faker.datatype.number(),
      offset: faker.datatype.number()
    };
    const terms = Array.from({ length: 5 }, faker.random.word);
    const filters: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: Date.now(),
          max: Date.parse(faker.date.future().toDateString())
        }
      }
    ];

    const response =
      await profileSearchResourceService.getPaginatedTrialsForPerson({
        personId,
        projectId,
        page,
        terms,
        filters
      });

    expect(elasticSearchService.query).toHaveBeenCalledWith({
      index: configService.elasticPeopleIndex,
      size: 1,
      _source: false,
      query: expect.anything()
    });
    expect(response).toEqual({
      totalCount: expectedResponse.totalCount,
      h1Trials: [...expectedResponse.ctmsTrials, ...expectedResponse.h1Trials],
      ctmsTrials: []
    });
  });
});

describe("getTrialMetricsForPeople()", () => {
  it("returns metrics when no search terms or filters were provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      TrialMetricsResponseForPeople
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();

    const peopleMetrics =
      await profileSearchResourceService.getTrialMetricsForPeople(
        ["4667519", "4938278"],
        projectId
      );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(peopleMetrics[0]).toEqual(
      expect.objectContaining({
        trialIds: expect.arrayContaining([
          "NCT123456",
          "NCT1234567",
          "NCT987654",
          "NC876876",
          "NCT456456"
        ]),
        personId: "4667519",
        total: 4331,
        inProgressCount: 20,
        completedCount: 123,
        terminatedCount: 13,
        // TODO update sample data to include following items when ES indexes are ready
        // topConditions: [],
        // topInterventions: [],
        totalByYear: expect.arrayContaining([
          expect.objectContaining({ total: 224, year: 2018 }),
          expect.objectContaining({ total: 235, year: 2019 }),
          expect.objectContaining({ total: 28, year: 2020 })
        ]),
        topConditions: expect.arrayContaining([
          "Leukemia",
          "Acute Myeloid Leukemia",
          "Myelodysplastic Syndrome",
          "Acute Lymphoblastic Leukemia",
          "Chronic Myeloid Leukemia"
        ]),
        topInterventions: expect.arrayContaining([
          "Decitabine",
          "Cytarabine",
          "Laboratory Biomarker Analysis",
          "Azacitidine",
          "Lenalidomide"
        ])
      })
    );
    expect(peopleMetrics[1]).toEqual(
      expect.objectContaining({
        trialIds: expect.arrayContaining([
          "NCT123456",
          "NCT1234567",
          "NCT987654",
          "NC876876",
          "NCT456456"
        ]),
        personId: "4938278",
        total: 2340,
        inProgressCount: 54,
        completedCount: 156,
        terminatedCount: 10,
        totalByYear: expect.arrayContaining([
          expect.objectContaining({ total: 150, year: 2018 }),
          expect.objectContaining({ total: 175, year: 2019 }),
          expect.objectContaining({ total: 20, year: 2020 })
        ]),
        topConditions: expect.arrayContaining([
          "Acute Myeloid Leukemia",
          "Acute Lymphoblastic Leukemia",
          "Leukemia",
          "Acute Lymphoblastic Leukemia",
          "Chronic Myeloid Leukemia"
        ]),
        topInterventions: expect.arrayContaining([
          "Cytarabine",
          "Decitabine",
          "Lenalidomide",
          "Azacitidine",
          "Laboratory Biomarker Analysis"
        ])
      })
    );
  });

  it("returns metrics when search terms are provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("trials")) {
          return queryContainers[ProfileSearchAsset.ClinicalTrials];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      TrialMetricsSearchResponseForPeople
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();
    const peopleMetrics =
      await profileSearchResourceService.getTrialMetricsForPeople(
        ["4667519", "4938278"],
        projectId,
        ["cancer"]
      );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(peopleMetrics[0]).toEqual(
      expect.objectContaining({
        trialIds: expect.arrayContaining([
          "NCT123456",
          "NCT1234567",
          "NCT987654"
        ]),
        personId: "4667519",
        total: 1925,
        inProgressCount: 36,
        completedCount: 79,
        terminatedCount: 5,
        totalByYear: expect.arrayContaining([
          expect.objectContaining({ total: 124, year: 2019 }),
          expect.objectContaining({ total: 62, year: 2020 }),
          expect.objectContaining({ total: 2, year: 2021 })
        ]),
        topConditions: expect.arrayContaining([
          "Acute Myeloid Leukemia",
          "Leukemia",
          "Chronic Myeloid Leukemia",
          "Myelodysplastic Syndrome",
          "Myelodysplastic Syndromes"
        ]),
        topInterventions: expect.arrayContaining([
          "Decitabine",
          "Cytarabine",
          "Idarubicin",
          "AG-120",
          "AZD1152"
        ])
      })
    );
    expect(peopleMetrics[1]).toEqual(
      expect.objectContaining({
        trialIds: expect.arrayContaining([
          "NCT123456",
          "NCT1234567",
          "NCT987654"
        ]),
        personId: "4938278",
        total: 1725,
        inProgressCount: 24,
        completedCount: 101,
        terminatedCount: 15,
        totalByYear: expect.arrayContaining([
          expect.objectContaining({ total: 104, year: 2019 }),
          expect.objectContaining({ total: 42, year: 2020 }),
          expect.objectContaining({ total: 1, year: 2021 })
        ]),
        topConditions: expect.arrayContaining([
          "Acute Myeloid Leukemia",
          "Leukemia",
          "Chronic Myeloid Leukemia",
          "Myelodysplastic Syndrome",
          "Myelodysplastic Syndromes"
        ]),
        topInterventions: expect.arrayContaining([
          "Decitabine",
          "Cytarabine",
          "Idarubicin",
          "AG-120",
          "AZD1152"
        ])
      })
    );
  });
});

describe("getCongressIdsForPeople()", () => {
  it("returns empty array when no congresses are returned", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("congress")) {
          return queryContainers[ProfileSearchAsset.Congresses];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue({
      aggregations: {}
    });

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const ids = await profileSearchResourceService.getCongressIdsForPeople(
      ["4667519", "4938278"],
      ["cancer"]
    );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(ids).toEqual([]);
  });

  it("returns the ids of the returned congresses", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("congress")) {
          return queryContainers[ProfileSearchAsset.Congresses];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      CongressIdsResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const result = await profileSearchResourceService.getCongressIdsForPeople(
      ["4667519", "4938278"],
      ["cancer"]
    );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(result).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          personId: "4667519",
          congressIds: ["100000-137251"]
        }),
        expect.objectContaining({
          personId: "4938278",
          congressIds: ["100000-137252", "100000-137253"]
        })
      ])
    );
  });
});

describe("getCongressMetricsForPeople()", () => {
  it("returns metrics when no search terms or filters were provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      CongressMetricsResponseForPeople
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();

    const peopleMetrics =
      await profileSearchResourceService.getCongressMetricsForPeople(
        ["4667519", "4938278"],
        projectId
      );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(peopleMetrics[0]).toEqual(
      expect.objectContaining({
        personId: "4667519",
        total: 2331,
        sessionsTotal: 2200,
        postersTotal: 131,
        topCongresses: [
          { name: "Test", presentations: 5 },
          { name: "AACE's 30th Annual Meeting", presentations: 5 },
          {
            name: "ASPEN 2020 Nutrition Science & Practice Conference",
            presentations: 1
          }
        ]
      })
    );
    expect(peopleMetrics[1]).toEqual(
      expect.objectContaining({
        personId: "4938278",
        total: 1342,
        sessionsTotal: 1200,
        postersTotal: 142,
        topCongresses: [
          { name: "Test", presentations: 5 },
          { name: "AACE's 30th Annual Meeting", presentations: 5 },
          {
            name: "ASPEN 2020 Nutrition Science & Practice Conference",
            presentations: 1
          }
        ]
      })
    );
  });

  it("returns metrics when search terms are provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("congress")) {
          return queryContainers[ProfileSearchAsset.Congresses];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      CongressMetricsSearchResponseForPeople
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectId = faker.datatype.uuid();

    const peopleMetrics =
      await profileSearchResourceService.getCongressMetricsForPeople(
        ["4667519", "4938278"],
        projectId,
        ["cancer"]
      );
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(peopleMetrics[0]).toEqual(
      expect.objectContaining({
        personId: "4667519",
        total: 925,
        sessionsTotal: 900,
        postersTotal: 25,
        topCongresses: [
          { name: "Test", presentations: 5 },
          { name: "AACE's 30th Annual Meeting", presentations: 5 },
          {
            name: "ASPEN 2020 Nutrition Science & Practice Conference",
            presentations: 1
          }
        ]
      })
    );
    expect(peopleMetrics[1]).toEqual(
      expect.objectContaining({
        personId: "4938278",
        total: 1225,
        sessionsTotal: 1100,
        postersTotal: 125,
        topCongresses: [
          { name: "Test", presentations: 5 },
          { name: "AACE's 30th Annual Meeting", presentations: 5 },
          {
            name: "ASPEN 2020 Nutrition Science & Practice Conference",
            presentations: 1
          }
        ]
      })
    );
  });
});

describe("getAllMetricsForPeople()", () => {
  it("returns metrics when no search terms or filters were provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      AllMetricsResponseForPeople
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectFeatures = ProjectFeatures.createWithDefaults("1");
    projectFeatures.claimsV2 = true;

    const user: UserToken = {
      userId: "12345",
      projectId: "1",
      projectFeatures,
      okta: {
        status: null
      }
    };

    const peopleMetrics =
      await profileSearchResourceService.getAllMetricsForPeople(
        ["4667519"],
        user
      );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();

    expect(peopleMetrics[0]).toEqual(
      expect.objectContaining({
        publicationMetrics: {
          personId: "4667519",
          total: 1172,
          citationSum: 28243,
          microBloggingSum: 3603,
          guidelineSum: 30,
          totalByYear: [
            { year: 2016, total: 151 },
            { year: 2017, total: 175 },
            { year: 2018, total: 130 },
            { year: 2019, total: 172 },
            { year: 2020, total: 153 },
            { year: 2021, total: 12 }
          ]
        },
        congressMetrics: {
          personId: "4667519",
          total: 44,
          sessionsTotal: 42,
          postersTotal: 2,
          topCongresses: [
            { name: "Test", presentations: 5 },
            { name: "AACE's 30th Annual Meeting", presentations: 5 },
            {
              name: "ASPEN 2020 Nutrition Science & Practice Conference",
              presentations: 1
            }
          ]
        },
        trialMetrics: {
          trialIds: expect.arrayContaining([
            "NCT123456",
            "NCT1234567",
            "NCT987654",
            "NC876876",
            "NCT456456"
          ]),
          personId: "4667519",
          total: 148,
          inProgressCount: 20,
          completedCount: 123,
          terminatedCount: 13,
          totalByYear: [
            { year: 2016, total: 5 },
            { year: 2017, total: 6 },
            { year: 2018, total: 17 },
            { year: 2019, total: 8 },
            { year: 2020, total: 16 },
            { year: 2021, total: 6 }
          ],
          topConditions: expect.arrayContaining([
            "Leukemia",
            "Acute Myeloid Leukemia",
            "Myelodysplastic Syndrome",
            "Acute Lymphoblastic Leukemia",
            "Chronic Myeloid Leukemia"
          ]),
          topInterventions: expect.arrayContaining([
            "Decitabine",
            "Cytarabine",
            "Laboratory Biomarker Analysis",
            "Azacitidine",
            "Lenalidomide"
          ])
        },
        diagnosisMetrics: {
          personId: "4667519",
          total: 515,
          internalCountSum: 2432
        },
        procedureMetrics: {
          personId: "4667519",
          total: 248,
          internalCountSum: 1656
        },
        paymentMetrics: {
          personId: "4667519",
          total: 821,
          totalPaymentsAmount: 49172514.16
        }
      })
    );
  });

  it("returns metrics when search terms are provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.DrgDiagnoses]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.DrgProcedures]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Payments]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("trials")) {
          return queryContainers[ProfileSearchAsset.ClinicalTrials];
        }
        if (fields[0].startsWith("congress")) {
          return queryContainers[ProfileSearchAsset.Congresses];
        }
        if (fields[0].startsWith("publications")) {
          return queryContainers[ProfileSearchAsset.Publications];
        }
        if (fields[0].startsWith("payments")) {
          return queryContainers[ProfileSearchAsset.Payments];
        }
        if (fields[0].startsWith("DRG_diagnoses")) {
          return queryContainers[ProfileSearchAsset.DrgDiagnoses];
        }
        if (fields[0].startsWith("DRG_procedures")) {
          return queryContainers[ProfileSearchAsset.DrgProcedures];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      AllMetricsSearchResponseForPeople
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const projectFeatures = ProjectFeatures.createWithDefaults("1");
    projectFeatures.claimsV2 = true;

    const user: UserToken = {
      userId: "12345",
      projectId: "1",
      projectFeatures,
      okta: {
        status: null
      }
    };

    const peopleMetrics =
      await profileSearchResourceService.getAllMetricsForPeople(
        ["4667519"],
        user,
        {
          terms: ["cancer"]
        }
      );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(peopleMetrics[0]).toEqual(
      expect.objectContaining({
        publicationMetrics: {
          personId: "4667519",
          total: 556,
          citationSum: 11728,
          microBloggingSum: 1693,
          guidelineSum: 35,
          totalByYear: [
            { year: 2016, total: 63 },
            { year: 2017, total: 89 },
            { year: 2018, total: 75 },
            { year: 2019, total: 101 },
            { year: 2020, total: 64 },
            { year: 2021, total: 2 }
          ]
        },
        congressMetrics: {
          personId: "4667519",
          total: 0,
          sessionsTotal: 0,
          postersTotal: 0,
          topCongresses: []
        },
        trialMetrics: {
          trialIds: expect.arrayContaining([
            "NCT123456",
            "NCT1234567",
            "NCT987654"
          ]),
          personId: "4667519",
          total: 39,
          inProgressCount: 12,
          completedCount: 24,
          terminatedCount: 3,
          totalByYear: [
            { year: 2016, total: 1 },
            { year: 2017, total: 1 },
            { year: 2018, total: 5 },
            { year: 2019, total: 3 },
            { year: 2020, total: 7 },
            { year: 2021, total: 4 }
          ],
          topConditions: expect.arrayContaining([
            "Acute Myeloid Leukemia",
            "Leukemia",
            "Chronic Myeloid Leukemia",
            "Myelodysplastic Syndrome",
            "Myelodysplastic Syndromes"
          ]),
          topInterventions: expect.arrayContaining([
            "Decitabine",
            "Cytarabine",
            "Idarubicin",
            "AG-120",
            "AZD1152"
          ])
        },
        diagnosisMetrics: {
          personId: "4667519",
          total: 1,
          internalCountSum: 3
        },
        procedureMetrics: {
          personId: "4667519",
          total: 0,
          internalCountSum: 0
        },
        paymentMetrics: {
          personId: "4667519",
          total: 0,
          totalPaymentsAmount: 0
        }
      })
    );
  });
});

describe("getMetricSummaryForPeople()", () => {
  it("returns empty array when no buckets are returned", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const opts = {
      peopleIds: [faker.random.alphaNumeric()],
      projectId: faker.datatype.uuid(),
      ClaimsType: ClaimsType.DRG
    };
    const esResult = {
      aggregations: {
        people: {
          buckets: []
        }
      }
    };
    elasticSearchService.getSignedElasticRequest.mockResolvedValue(esResult);

    await expect(
      profileSearchResourceService.getMetricSummaryForPeople(opts)
    ).resolves.toEqual([]);
  });

  it("returns metrics for each person", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const opts = {
      peopleIds: [faker.random.alphaNumeric(), faker.random.alphaNumeric()],
      projectId: faker.datatype.uuid(),
      ClaimsType: ClaimsType.DRG
    };

    const fakeDocCountBucket = (additional?: Record<string, any>) => ({
      key: faker.random.alphaNumeric(),
      doc_count: faker.datatype.number(),
      ...additional
    });

    const fakePersonBucket = (personId: string) => ({
      key: personId,
      trials: {
        doc_count: faker.datatype.number(),
        inProgressCount: {
          doc_count: faker.datatype.number()
        },
        terminatedCount: {
          doc_count: faker.datatype.number()
        },
        completedCount: {
          doc_count: faker.datatype.number()
        },
        ids: {
          buckets: [
            {
              key: faker.random.alphaNumeric(),
              doc_count: faker.datatype.number()
            }
          ]
        },
        yearTotals: {
          buckets: [
            fakeDocCountBucket({
              key_as_string: faker.datatype.datetime().toISOString()
            })
          ]
        },
        topConditions: {
          buckets: [fakeDocCountBucket()]
        },
        topInterventions: {
          buckets: [fakeDocCountBucket()]
        }
      },
      congresses: {
        doc_count: faker.datatype.number(),
        topCongresses: {
          buckets: [fakeDocCountBucket()]
        },
        yearTotals: {
          buckets: [
            fakeDocCountBucket({
              key_as_string: faker.datatype.datetime().toISOString()
            })
          ]
        },
        sessionsTotal: {
          doc_count: faker.datatype.number()
        },
        postersTotal: {
          doc_count: faker.datatype.number()
        }
      },
      payments: {
        doc_count: faker.datatype.number(),
        totalPaymentsAmount: {
          value: faker.datatype.float()
        }
      },
      procedures: {
        doc_count: faker.datatype.number(),
        internalCount: {
          value: faker.datatype.number()
        }
      },
      diagnoses: {
        doc_count: faker.datatype.number(),
        internalCount: {
          value: faker.datatype.number()
        }
      },
      publications: {
        doc_count: faker.datatype.number(),
        citationCount: {
          value: faker.datatype.number()
        },
        microBloggingCount: {
          value: faker.datatype.number()
        },
        guidelineCount: {
          doc_count: faker.datatype.number()
        },
        yearTotals: {
          buckets: [
            fakeDocCountBucket({
              key_as_string: faker.datatype.datetime().toISOString()
            })
          ]
        }
      }
    });

    const esResult = {
      aggregations: {
        people: {
          buckets: [
            fakePersonBucket(opts.peopleIds[0]),
            fakePersonBucket(opts.peopleIds[1])
          ]
        }
      }
    };

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(esResult);

    const expectPublicationMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        totalByYear: expect.arrayContaining([
          expect.objectContaining({
            year: expect.any(Number),
            total: expect.any(Number)
          })
        ]),
        citationSum: expect.any(Number),
        microBloggingSum: expect.any(Number),
        guidelineSum: expect.any(Number)
      });

    const expectCongressMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        postersTotal: expect.any(Number),
        sessionsTotal: expect.any(Number),
        topCongresses: expect.arrayContaining([
          expect.objectContaining({
            name: expect.any(String),
            presentations: expect.any(Number)
          })
        ])
      });

    const expectTrialMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        totalByYear: expect.arrayContaining([
          expect.objectContaining({
            year: expect.any(Number),
            total: expect.any(Number)
          })
        ]),
        inProgressCount: expect.any(Number),
        completedCount: expect.any(Number),
        terminatedCount: expect.any(Number),
        topConditions: expect.arrayContaining([expect.any(String)]),
        topInterventions: expect.arrayContaining([expect.any(String)]),
        trialIds: expect.arrayContaining([expect.any(String)])
      });

    const expectDiagnosisMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        internalCountSum: expect.any(Number)
      });

    const expectProcedureMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        internalCountSum: expect.any(Number)
      });

    const expectPaymentMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        totalPaymentsAmount: expect.any(Number)
      });

    await expect(
      profileSearchResourceService.getMetricSummaryForPeople(opts)
    ).resolves.toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          publicationMetrics: expectPublicationMetrics(opts.peopleIds[0]),
          congressMetrics: expectCongressMetrics(opts.peopleIds[0]),
          trialMetrics: expectTrialMetrics(opts.peopleIds[0]),
          diagnosisMetrics: expectDiagnosisMetrics(opts.peopleIds[0]),
          procedureMetrics: expectProcedureMetrics(opts.peopleIds[0]),
          paymentMetrics: expectPaymentMetrics(opts.peopleIds[0])
        }),
        expect.objectContaining({
          publicationMetrics: expectPublicationMetrics(opts.peopleIds[1]),
          congressMetrics: expectCongressMetrics(opts.peopleIds[1]),
          trialMetrics: expectTrialMetrics(opts.peopleIds[1]),
          diagnosisMetrics: expectDiagnosisMetrics(opts.peopleIds[1]),
          procedureMetrics: expectProcedureMetrics(opts.peopleIds[1]),
          paymentMetrics: expectPaymentMetrics(opts.peopleIds[1])
        })
      ])
    );
  });

  it("use QueryParserService", async () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();

    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.DrgDiagnoses]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.DrgProcedures]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Payments]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (parsedQueryTree, fields) => {
        if (fields[0].startsWith("trials")) {
          return queryContainers[ProfileSearchAsset.ClinicalTrials];
        }
        if (fields[0].startsWith("congress")) {
          return queryContainers[ProfileSearchAsset.Congresses];
        }
        if (fields[0].startsWith("publications")) {
          return queryContainers[ProfileSearchAsset.Publications];
        }
        if (fields[0].startsWith("payments")) {
          return queryContainers[ProfileSearchAsset.Payments];
        }
        if (fields[0].startsWith("DRG_diagnoses")) {
          return queryContainers[ProfileSearchAsset.DrgDiagnoses];
        }
        if (fields[0].startsWith("DRG_procedures")) {
          return queryContainers[ProfileSearchAsset.DrgProcedures];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const opts: MetricSummaryOptions = {
      peopleIds: [faker.random.alphaNumeric(), faker.random.alphaNumeric()],
      projectId: faker.datatype.uuid(),
      termsAndFilters: {
        terms: [faker.datatype.string(), faker.datatype.string()]
      },
      languageCode: faker.datatype.string()
    };

    const fakeDocCountBucket = (additional?: Record<string, any>) => ({
      key: faker.random.alphaNumeric(),
      doc_count: faker.datatype.number(),
      ...additional
    });

    const fakePersonBucket = (personId: string) => ({
      key: personId,
      trials: {
        doc_count: faker.datatype.number(),
        inProgressCount: {
          doc_count: faker.datatype.number()
        },
        terminatedCount: {
          doc_count: faker.datatype.number()
        },
        completedCount: {
          doc_count: faker.datatype.number()
        },
        ids: {
          buckets: [
            {
              key: faker.random.alphaNumeric(),
              doc_count: faker.datatype.number()
            }
          ]
        },
        yearTotals: {
          buckets: [
            fakeDocCountBucket({
              key_as_string: faker.datatype.datetime().toISOString()
            })
          ]
        },
        topConditions: {
          buckets: [fakeDocCountBucket()]
        },
        topInterventions: {
          buckets: [fakeDocCountBucket()]
        }
      },
      congresses: {
        doc_count: faker.datatype.number(),
        topCongresses: {
          buckets: [fakeDocCountBucket()]
        },
        yearTotals: {
          buckets: [
            fakeDocCountBucket({
              key_as_string: faker.datatype.datetime().toISOString()
            })
          ]
        },
        sessionsTotal: {
          doc_count: faker.datatype.number()
        },
        postersTotal: {
          doc_count: faker.datatype.number()
        }
      },
      payments: {
        doc_count: faker.datatype.number(),
        totalPaymentsAmount: {
          value: faker.datatype.float()
        }
      },
      procedures: {
        doc_count: faker.datatype.number(),
        internalCount: {
          value: faker.datatype.number()
        }
      },
      diagnoses: {
        doc_count: faker.datatype.number(),
        internalCount: {
          value: faker.datatype.number()
        }
      },
      publications: {
        doc_count: faker.datatype.number(),
        citationCount: {
          value: faker.datatype.number()
        },
        microBloggingCount: {
          value: faker.datatype.number()
        },
        guidelineCount: {
          doc_count: faker.datatype.number()
        },
        yearTotals: {
          buckets: [
            fakeDocCountBucket({
              key_as_string: faker.datatype.datetime().toISOString()
            })
          ]
        }
      }
    });

    const esResult = {
      aggregations: {
        people: {
          buckets: [
            fakePersonBucket(opts.peopleIds[0]),
            fakePersonBucket(opts.peopleIds[1])
          ]
        }
      }
    };

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(esResult);

    const expectPublicationMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        totalByYear: expect.arrayContaining([
          expect.objectContaining({
            year: expect.any(Number),
            total: expect.any(Number)
          })
        ]),
        citationSum: expect.any(Number),
        microBloggingSum: expect.any(Number)
      });

    const expectCongressMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        postersTotal: expect.any(Number),
        sessionsTotal: expect.any(Number),
        topCongresses: expect.arrayContaining([
          expect.objectContaining({
            name: expect.any(String),
            presentations: expect.any(Number)
          })
        ])
      });

    const expectTrialMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        totalByYear: expect.arrayContaining([
          expect.objectContaining({
            year: expect.any(Number),
            total: expect.any(Number)
          })
        ]),
        inProgressCount: expect.any(Number),
        completedCount: expect.any(Number),
        terminatedCount: expect.any(Number),
        topConditions: expect.arrayContaining([expect.any(String)]),
        topInterventions: expect.arrayContaining([expect.any(String)]),
        trialIds: expect.arrayContaining([expect.any(String)])
      });

    const expectDiagnosisMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        internalCountSum: expect.any(Number)
      });

    const expectProcedureMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        internalCountSum: expect.any(Number)
      });

    const expectPaymentMetrics = (personId: string) =>
      expect.objectContaining({
        personId,
        total: expect.any(Number),
        totalPaymentsAmount: expect.any(Number)
      });

    await expect(
      profileSearchResourceService.getMetricSummaryForPeople(opts)
    ).resolves.toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          publicationMetrics: expectPublicationMetrics(opts.peopleIds[0]),
          congressMetrics: expectCongressMetrics(opts.peopleIds[0]),
          trialMetrics: expectTrialMetrics(opts.peopleIds[0]),
          diagnosisMetrics: expectDiagnosisMetrics(opts.peopleIds[0]),
          procedureMetrics: expectProcedureMetrics(opts.peopleIds[0]),
          paymentMetrics: expectPaymentMetrics(opts.peopleIds[0])
        }),
        expect.objectContaining({
          publicationMetrics: expectPublicationMetrics(opts.peopleIds[1]),
          congressMetrics: expectCongressMetrics(opts.peopleIds[1]),
          trialMetrics: expectTrialMetrics(opts.peopleIds[1]),
          diagnosisMetrics: expectDiagnosisMetrics(opts.peopleIds[1]),
          procedureMetrics: expectProcedureMetrics(opts.peopleIds[1]),
          paymentMetrics: expectPaymentMetrics(opts.peopleIds[1])
        })
      ])
    );

    expect(
      queryParserService.parseQueryWithQueryUnderstandingService
    ).toHaveBeenCalledWith(opts.termsAndFilters!.terms![0], {
      projectSupportsAdvancedOperators: true
    });

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalledWith(
      parsedQueryTree,
      ASSET_FIELDS_TO_QUERY.ClinicalTrials.fields,
      ENGLISH
    );
    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalledWith(
      parsedQueryTree,
      ASSET_FIELDS_TO_QUERY.Congresses.fields,
      undefined
    );
    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalledWith(
      parsedQueryTree,
      ASSET_FIELDS_TO_QUERY.DrgDiagnoses.fields,
      ENGLISH
    );
    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalledWith(
      parsedQueryTree,
      ASSET_FIELDS_TO_QUERY.DrgProcedures.fields,
      ENGLISH
    );
    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalledWith(
      parsedQueryTree,
      ASSET_FIELDS_TO_QUERY.Payments.fields,
      undefined
    );
    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalledWith(
      parsedQueryTree,
      ASSET_FIELDS_TO_QUERY.Publications.fields,
      undefined
    );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalledWith(
      {
        _source: "_none",
        size: 0,
        query: {
          bool: {
            filter: [
              expect.termsQuery("id", opts.peopleIds),
              expect.projectIdFilter(opts.projectId!)
            ]
          }
        },
        aggs: {
          people: {
            terms: {
              field: "id",
              size: 2
            },
            aggs: {
              congresses: {
                nested: {
                  path: "congress"
                },
                aggs: {
                  congressFilters: {
                    aggs: expect.any(Object),
                    filters: {
                      filters: [
                        {
                          bool: {
                            must: [queryContainers.Congresses]
                          }
                        }
                      ]
                    }
                  }
                }
              },
              trials: {
                nested: {
                  path: "trials"
                },
                aggs: {
                  trialFilters: {
                    aggs: expect.any(Object),
                    filters: {
                      filters: [
                        {
                          bool: {
                            must: [queryContainers.ClinicalTrials]
                          }
                        }
                      ]
                    }
                  }
                }
              },
              payments: {
                nested: {
                  path: "payments"
                },
                aggs: {
                  paymentFilters: {
                    aggs: expect.any(Object),
                    filters: {
                      filters: [
                        {
                          bool: {
                            must: [queryContainers.Payments]
                          }
                        }
                      ]
                    }
                  }
                }
              },
              publications: {
                nested: {
                  path: "publications"
                },
                aggs: {
                  publicationFilters: {
                    aggs: expect.any(Object),
                    filters: {
                      filters: [
                        {
                          bool: {
                            must: [queryContainers.Publications]
                          }
                        }
                      ]
                    }
                  }
                }
              },
              diagnoses: {
                nested: {
                  path: "DRG_diagnoses"
                },
                aggs: {
                  diagnosisFilters: {
                    aggs: expect.any(Object),
                    filters: {
                      filters: [
                        {
                          bool: {
                            must: [queryContainers.DrgDiagnoses]
                          }
                        }
                      ]
                    }
                  }
                }
              },
              procedures: {
                nested: {
                  path: "DRG_procedures"
                },
                aggs: {
                  procedureFilters: {
                    aggs: expect.any(Object),
                    filters: {
                      filters: [
                        {
                          bool: {
                            must: [queryContainers.DrgProcedures]
                          }
                        }
                      ]
                    }
                  }
                }
              }
            }
          }
        }
      },
      configService.elasticPeopleIndex
    );
  });
});

describe("getSortedIdsForPeople()", () => {
  it("returns empty array when no ids are returned (filtered search results are empty)", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("trials")) {
          return queryContainers[ProfileSearchAsset.ClinicalTrials];
        }
        if (fields[0].startsWith("congress")) {
          return queryContainers[ProfileSearchAsset.Congresses];
        }
        if (fields[0].startsWith("publications")) {
          return queryContainers[ProfileSearchAsset.Publications];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      AllSortedIdsEmptyResponseForPeople
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const result = await profileSearchResourceService.getSortedIdsForPeople(
      ["4667519"],
      {
        size: 75,
        termsAndFilters: { terms: ["asdf"] }
      }
    );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(result).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          personId: "4667519",
          trialIds: [],
          congressIds: [],
          publicationIds: []
        })
      ])
    );
  });

  it("returns the ids of the returned types (Congresses, Trials, and Publications)", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("trials")) {
          return queryContainers[ProfileSearchAsset.ClinicalTrials];
        }
        if (fields[0].startsWith("congress")) {
          return queryContainers[ProfileSearchAsset.Congresses];
        }
        if (fields[0].startsWith("publications")) {
          return queryContainers[ProfileSearchAsset.Publications];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      AllSortedIdsResponseForPeople
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const result = await profileSearchResourceService.getSortedIdsForPeople(
      ["4667519"],
      {
        size: 75,
        termsAndFilters: { terms: ["cancer"] }
      }
    );

    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(result).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          personId: "4667519",
          trialIds: [
            "NCT02668653",
            "NCT02039726",
            "NCT02115295",
            "NCT02096055",
            "NCT02013167",
            "NCT01757535",
            "NCT01787487",
            "NCT01564784",
            "NCT01546038",
            "NCT01424982",
            "NCT01371630",
            "NCT00968760",
            "NCT01025154",
            "NCT01002755"
          ],
          congressIds: [
            "100469-146970",
            "1514-112435",
            "1514-38226",
            "658-77709",
            "658-77725"
          ],
          publicationIds: [
            "6f0ca997-a4e5-4c66-b604-69fbe47683e5",
            "9c648ac4-631a-4267-9de4-b9ba47b9c614",
            "bf1b973b-df21-45ec-86f6-638013cf14a7",
            "bb224aaf-a41d-4818-8c4c-122ff70f2db3",
            "010d3603-456a-426b-b364-8f8b7c522e14",
            "4fe4d5df-a166-47a6-8b8d-8918e268c4b7",
            "72d0c91a-319f-4b89-8159-614ec3e7c6b5",
            "5c554db5-08ff-4fb7-a833-d45506ab2356",
            "b4f69e90-d459-4906-8989-bb2b2094c094"
          ]
        })
      ])
    );
  });
});

describe("getAIResponseForPerson()", () => {
  it("should return AI response when successful", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const expectedResponse = "This is the AI-generated response";
    elasticMcpClientService.queryChatCompletion.mockResolvedValue({
      role: "assistant",
      content: expectedResponse
    });

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const personId = faker.datatype.number().toString();
    const query = "Tell me about this person's research";

    const result = await profileSearchResourceService.getAIResponseForPerson(
      personId,
      query
    );

    expect(elasticMcpClientService.queryChatCompletion).toHaveBeenCalledWith(
      query,
      personId
    );
    expect(result).toEqual({ aiResponse: expectedResponse });
  });

  it("should handle errors and return graceful message", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );
    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    // Mock error from MCP client
    elasticMcpClientService.queryChatCompletion.mockRejectedValue(
      new Error("Service unavailable")
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const personId = faker.datatype.number().toString();
    const query = "Tell me about this person's research";

    const result = await profileSearchResourceService.getAIResponseForPerson(
      personId,
      query
    );

    expect(elasticMcpClientService.queryChatCompletion).toHaveBeenCalledWith(
      query,
      personId
    );
    expect(result).toEqual({
      aiResponse:
        "I'm sorry, I couldn't generate a response at this time. Please try again later."
    });
  });

  it("should handle non-Error objects thrown by MCP client", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );
    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    // Mock string error from MCP client
    elasticMcpClientService.queryChatCompletion.mockRejectedValue(
      "Unexpected error"
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const personId = faker.datatype.number().toString();
    const query = "Tell me about this person's research";

    const result = await profileSearchResourceService.getAIResponseForPerson(
      personId,
      query
    );

    expect(elasticMcpClientService.queryChatCompletion).toHaveBeenCalledWith(
      query,
      personId
    );
    expect(result).toEqual({
      aiResponse:
        "I'm sorry, I couldn't generate a response at this time. Please try again later."
    });
  });

  it("should handle null or undefined content in MCP response", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );
    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    // Mock response with null content
    elasticMcpClientService.queryChatCompletion.mockResolvedValue({
      role: "assistant",
      content: null
    });

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const personId = faker.datatype.number().toString();
    const query = "Tell me about this person's research";

    const result = await profileSearchResourceService.getAIResponseForPerson(
      personId,
      query
    );

    expect(elasticMcpClientService.queryChatCompletion).toHaveBeenCalledWith(
      query,
      personId
    );
    expect(result).toEqual({ aiResponse: null });
  });
});

describe("getTweetsForPerson()", () => {
  const buildMockTweetElasticResponse = (
    personId: string,
    referencedTweets?: { id: string; type: string }[]
  ) => {
    const mockElasticResponse = {
      took: faker.datatype.number({ min: 100 }),
      timed_out: false,
      _shards: {
        total: faker.datatype.number({ min: 100 }),
        successful: faker.datatype.number({ min: 100 }),
        skipped: 0,
        failed: 0
      },
      hits: {
        total: {
          value: 1,
          relation: "eq"
        },
        max_score: faker.datatype.number({ min: 100 }),
        hits: [
          {
            _index: "people_base_20221121",
            _type: "_doc",
            _id: faker.datatype.uuid(),
            _score: faker.datatype.number({ min: 100 }),
            _source: {
              id: personId
            },
            inner_hits: {
              tweets: {
                hits: {
                  total: {
                    value: faker.datatype.number({ min: 100 }),
                    relation: "eq"
                  },
                  max_score: null,
                  hits: [...Array(15)].map(() => {
                    const id = faker.datatype.number({ min: 1 }).toString();
                    const tweetDate = faker.date.past();

                    return {
                      _index: "people_base_20221121",
                      _type: "_doc",
                      _id: faker.datatype.uuid(),
                      _nested: {
                        field: "tweets",
                        offset: faker.datatype.number({ min: 1 })
                      },
                      _score: null,
                      _source: {
                        id,
                        text: faker.lorem.words(),
                        createdAt: tweetDate,
                        referencedTweets: referencedTweets || []
                      },
                      sort: [tweetDate, id]
                    };
                  })
                }
              }
            }
          }
        ]
      }
    };

    const total =
      mockElasticResponse.hits.hits[0].inner_hits?.tweets.hits.total.value || 0;
    const tweets = (
      mockElasticResponse.hits.hits[0].inner_hits?.tweets.hits.hits || []
    ).map((tweetDoc) => ({
      id: tweetDoc._source.id,
      text: tweetDoc._source.text,
      isRetweet: tweetDoc._source.referencedTweets.some(
        (referenceTweet) => referenceTweet.type === "retweeted"
      ),
      isQuote: tweetDoc._source.referencedTweets.some(
        (referenceTweet) => referenceTweet.type === "quoted"
      ),
      isReply: tweetDoc._source.referencedTweets.some(
        (referenceTweet) => referenceTweet.type === "replied_to"
      ),
      date: tweetDoc._source.createdAt
    }));

    const expectedResponse = {
      personId,
      total,
      tweets
    };

    return { mockElasticResponse, expectedResponse };
  };

  const buildTweetsExpectedRequest = (
    personId: string,
    projectId: string,
    page: { limit: number; offset: number },
    query: QueryDslQueryContainer
  ) => ({
    _source: ["id"],
    query: {
      bool: {
        must: [
          { terms: { id: [personId] } },
          { terms: { projectIds: [projectId] } },
          {
            nested: {
              path: "tweets",
              query,
              inner_hits: {
                from: page.offset,
                size: page.limit,
                sort: [{ "tweets.createdAt": "desc" }, { "tweets.id": "desc" }]
              }
            }
          }
        ]
      }
    }
  });

  it("returns empty array when no tweets are found", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Tweets]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("tweets")) {
          return queryContainers[ProfileSearchAsset.Tweets];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    elasticSearchService.getSignedElasticRequest.mockResolvedValue({
      hits: {
        total: { value: 0, relation: "eq" },
        hits: []
      }
    });

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const personId = faker.datatype.number({ min: 1 }).toString();
    const projectId = faker.datatype.number({ min: 1 }).toString();

    const response = await profileSearchResourceService.getTweetsForPerson({
      personId,
      projectId,
      suppliedTerms: ["cancer"],
      page: {
        limit: 15,
        offset: 0
      },
      userLanguage: "english"
    });

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(response).toEqual({
      personId,
      total: 0,
      tweets: []
    });
  });

  it("returns tweets for a given HCP and a given search term", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Tweets]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("tweets")) {
          return queryContainers[ProfileSearchAsset.Tweets];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    const personId = faker.datatype.number({ min: 1 }).toString();
    const projectId = faker.datatype.number({ min: 1 }).toString();
    const page = {
      limit: faker.datatype.number({ min: 15 }),
      offset: faker.datatype.number({ min: 1 })
    };

    const { mockElasticResponse, expectedResponse } =
      buildMockTweetElasticResponse(personId);

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      mockElasticResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const response = await profileSearchResourceService.getTweetsForPerson({
      personId,
      projectId,
      suppliedTerms: [faker.lorem.word()],
      page,
      userLanguage: "english"
    });

    const expectedElasticRequest = buildTweetsExpectedRequest(
      personId,
      projectId,
      page,
      {
        bool: { must: [queryContainers[ProfileSearchAsset.Tweets]] }
      }
    );

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalledWith(
      expectedElasticRequest,
      configService.elasticPeopleIndex
    );
    expect(response).toEqual(expectedResponse);
  });

  it("returns tweets for a given HCP without a search term", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const personId = faker.datatype.number({ min: 1 }).toString();
    const projectId = faker.datatype.number({ min: 1 }).toString();
    const page = {
      limit: faker.datatype.number({ min: 15 }),
      offset: faker.datatype.number({ min: 1 })
    };

    const { mockElasticResponse, expectedResponse } =
      buildMockTweetElasticResponse(personId);

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      mockElasticResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const response = await profileSearchResourceService.getTweetsForPerson({
      personId,
      projectId,
      page,
      userLanguage: "english"
    });

    const expectedElasticRequest = buildTweetsExpectedRequest(
      personId,
      projectId,
      page,
      {
        match_all: {}
      }
    );

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).not.toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalledWith(
      expectedElasticRequest,
      configService.elasticPeopleIndex
    );
    expect(response).toEqual(expectedResponse);
  });

  it("should correctly return isRetweet", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Tweets]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("tweets")) {
          return queryContainers[ProfileSearchAsset.Tweets];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    const personId = faker.datatype.number({ min: 1 }).toString();
    const projectId = faker.datatype.number({ min: 1 }).toString();
    const page = {
      limit: faker.datatype.number({ min: 15 }),
      offset: faker.datatype.number({ min: 1 })
    };

    const { mockElasticResponse, expectedResponse } =
      buildMockTweetElasticResponse(personId, [
        { id: faker.datatype.number({ min: 1 }).toString(), type: "retweeted" }
      ]);

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      mockElasticResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const response = await profileSearchResourceService.getTweetsForPerson({
      personId,
      projectId,
      suppliedTerms: [faker.lorem.word()],
      page,
      userLanguage: "english"
    });

    const expectedElasticRequest = buildTweetsExpectedRequest(
      personId,
      projectId,
      page,
      {
        bool: { must: [queryContainers[ProfileSearchAsset.Tweets]] }
      }
    );

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalledWith(
      expectedElasticRequest,
      configService.elasticPeopleIndex
    );
    expect(response).toEqual(expectedResponse);

    response.tweets.forEach((tweet) => {
      expect(tweet.isRetweet).toEqual(true);
      expect(tweet.isReply).toEqual(false);
      expect(tweet.isQuote).toEqual(false);
    });
  });

  it("should correctly return isReply", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Tweets]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("tweets")) {
          return queryContainers[ProfileSearchAsset.Tweets];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    const personId = faker.datatype.number({ min: 1 }).toString();
    const projectId = faker.datatype.number({ min: 1 }).toString();
    const page = {
      limit: faker.datatype.number({ min: 15 }),
      offset: faker.datatype.number({ min: 1 })
    };

    const { mockElasticResponse, expectedResponse } =
      buildMockTweetElasticResponse(personId, [
        { id: faker.datatype.number({ min: 1 }).toString(), type: "replied_to" }
      ]);

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      mockElasticResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const response = await profileSearchResourceService.getTweetsForPerson({
      personId,
      projectId,
      suppliedTerms: [faker.lorem.word()],
      page,
      userLanguage: "english"
    });

    const expectedElasticRequest = buildTweetsExpectedRequest(
      personId,
      projectId,
      page,
      {
        bool: { must: [queryContainers[ProfileSearchAsset.Tweets]] }
      }
    );

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalledWith(
      expectedElasticRequest,
      configService.elasticPeopleIndex
    );
    expect(response).toEqual(expectedResponse);

    response.tweets.forEach((tweet) => {
      expect(tweet.isRetweet).toEqual(false);
      expect(tweet.isReply).toEqual(true);
      expect(tweet.isQuote).toEqual(false);
    });
  });

  it("should correctly return isQuoted", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const parsedQueryTree = faker.datatype.string();
    queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
      {
        parsedQueryTree,
        synonyms: []
      }
    );

    const queryContainers = {
      [ProfileSearchAsset.Tweets]: {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      }
    };

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      (_parsedQueryTree, fields) => {
        if (fields[0].startsWith("tweets")) {
          return queryContainers[ProfileSearchAsset.Tweets];
        }
        throw new Error(`Unknown fields: ${JSON.stringify(fields)}`);
      }
    );

    const personId = faker.datatype.number({ min: 1 }).toString();
    const projectId = faker.datatype.number({ min: 1 }).toString();
    const page = {
      limit: faker.datatype.number({ min: 15 }),
      offset: faker.datatype.number({ min: 1 })
    };

    const { mockElasticResponse, expectedResponse } =
      buildMockTweetElasticResponse(personId, [
        { id: faker.datatype.number({ min: 1 }).toString(), type: "quoted" }
      ]);

    elasticSearchService.getSignedElasticRequest.mockResolvedValue(
      mockElasticResponse
    );

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const response = await profileSearchResourceService.getTweetsForPerson({
      personId,
      projectId,
      suppliedTerms: [faker.lorem.word()],
      page,
      userLanguage: "english"
    });

    const expectedElasticRequest = buildTweetsExpectedRequest(
      personId,
      projectId,
      page,
      {
        bool: { must: [queryContainers[ProfileSearchAsset.Tweets]] }
      }
    );

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(elasticSearchService.getSignedElasticRequest).toHaveBeenCalledWith(
      expectedElasticRequest,
      configService.elasticPeopleIndex
    );
    expect(response).toEqual(expectedResponse);

    response.tweets.forEach((tweet) => {
      expect(tweet.isRetweet).toEqual(false);
      expect(tweet.isReply).toEqual(false);
      expect(tweet.isQuote).toEqual(true);
    });
  });
});

describe("getLocationsForPerson()", () => {
  it("returns empty array when no person found", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const personId = faker.datatype.number().toString();
    const projectId = faker.datatype.number().toString();
    const mockResponse =
      generateMockElasticsearchResponse<HCPDocumentWithOnlyLocations>([]);

    elasticSearchService.query.mockResolvedValue(mockResponse);

    const getLocationsResponse =
      await profileSearchResourceService.getLocationsForPerson(
        personId,
        projectId
      );

    expect(elasticSearchService.query).toHaveBeenCalledWith({
      _source: ["locations"],
      index: configService.elasticPeopleIndex,
      query: {
        bool: {
          filter: [
            expect.termQuery("id", personId),
            expect.termQuery("projectIds", projectId)
          ]
        }
      }
    });
    expect(getLocationsResponse).toEqual([]);
  });

  it("returns locations for HCP when person found in project", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const personId = faker.datatype.number().toString();
    const projectId = faker.datatype.number().toString();
    const locations = Array.from(
      { length: faker.datatype.number({ min: 1, max: 10 }) },
      generateMockLocation
    );

    const mockResponse =
      generateMockElasticsearchResponse<HCPDocumentWithOnlyLocations>([
        generateMockElasticsearchHit<HCPDocumentWithOnlyLocations>(undefined, {
          locations
        })
      ]);

    elasticSearchService.query.mockResolvedValue(mockResponse);

    const getLocationsResponse =
      await profileSearchResourceService.getLocationsForPerson(
        personId,
        projectId
      );

    expect(elasticSearchService.query).toHaveBeenCalledWith({
      _source: ["locations"],
      index: configService.elasticPeopleIndex,
      query: {
        bool: {
          filter: [
            expect.termQuery("id", personId),
            expect.termQuery("projectIds", projectId)
          ]
        }
      }
    });
    expect(getLocationsResponse).toEqual(
      extractLocationField({
        locations
      })
    );
  });

  it("defaults empty string for missing location fields", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const personId = faker.datatype.number().toString();
    const projectId = faker.datatype.number().toString();
    const locations = Array.from(
      { length: faker.datatype.number({ min: 1, max: 10 }) },
      generateMockLocation
    );
    locations.push(
      generateMockLocation({
        city_eng: undefined,
        city_cmn: undefined,
        city_jpn: undefined
      }),
      generateMockLocation({
        state_eng: undefined,
        state_cmn: undefined,
        state_jpn: undefined
      }),
      generateMockLocation({
        country_eng: undefined,
        country_cmn: undefined,
        country_jpn: undefined
      }),
      generateMockLocation({
        zipCode5_eng: undefined,
        zipCode5_cmn: undefined,
        zipCode5_jpn: undefined
      })
    );

    const mockResponse =
      generateMockElasticsearchResponse<HCPDocumentWithOnlyLocations>([
        generateMockElasticsearchHit<HCPDocumentWithOnlyLocations>(undefined, {
          locations
        })
      ]);

    elasticSearchService.query.mockResolvedValue(mockResponse);

    const getLocationsResponse =
      await profileSearchResourceService.getLocationsForPerson(
        personId,
        projectId
      );

    expect(elasticSearchService.query).toHaveBeenCalledWith({
      _source: ["locations"],
      index: configService.elasticPeopleIndex,
      query: {
        bool: {
          filter: [
            expect.termQuery("id", personId),
            expect.termQuery("projectIds", projectId)
          ]
        }
      }
    });
    expect(getLocationsResponse).toEqual(
      extractLocationField({
        locations
      })
    );
  });

  it("filters out locations missing all fields", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const projectFeaturesResourceClient = createMockInstance(
      ProjectFeaturesResourceClient
    );
    const languageDetectService = createMockInstance(LanguageDetectService);
    const queryParserService = createMockInstance(QueryParserService);
    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const elasticMcpClientService = createMockInstance(ElasticMCPClientService);

    const profileSearchResourceService = new ProfileSearchResourceService(
      configService,
      elasticSearchService,
      projectFeaturesResourceClient,
      languageDetectService,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      elasticMcpClientService
    );

    const personId = faker.datatype.number().toString();
    const projectId = faker.datatype.number().toString();
    const locations = Array.from(
      { length: faker.datatype.number({ min: 1, max: 10 }) },
      generateMockLocation
    );
    locations.push(
      {
        languageCode: faker.datatype.string()
      } as Location,
      {} as Location
    );

    const mockResponse =
      generateMockElasticsearchResponse<HCPDocumentWithOnlyLocations>([
        generateMockElasticsearchHit<HCPDocumentWithOnlyLocations>(undefined, {
          locations
        })
      ]);

    elasticSearchService.query.mockResolvedValue(mockResponse);

    const getLocationsResponse =
      await profileSearchResourceService.getLocationsForPerson(
        personId,
        projectId
      );

    expect(elasticSearchService.query).toHaveBeenCalledWith({
      _source: ["locations"],
      index: configService.elasticPeopleIndex,
      query: {
        bool: {
          filter: [
            expect.termQuery("id", personId),
            expect.termQuery("projectIds", projectId)
          ]
        }
      }
    });
    expect(getLocationsResponse).toEqual(
      extractLocationField({
        locations
      })
    );
  });
});
