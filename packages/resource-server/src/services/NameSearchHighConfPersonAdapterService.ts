import {
  SearchHit,
  SearchHitsMetadata,
  SearchInnerHitsResult,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import {
  DocumentType,
  RecentActivity,
  Publication,
  Trial,
  Congress,
  ActivityDocument,
  FilterTypesEnum
} from "@h1nyc/search-sdk";
import _ from "lodash";
import { Service } from "typedi";
import { HCPDocument } from "./KeywordSearchResourceServiceRewrite";

// Number of recent activites to be returned for HighConf HCP
const maxNumRecentActivites = 10;
const NO_DATA = "";
const DEFAULT_TIMESTAMP = 0;
const DEFAULT_COUNT = 0;
const DEFAULT_SCORE = 0;
const FIRST_RESULT_SCORE_MULTIPLIER = 4;
const EMPTY_ARRAY: never[] = [];

export function stripLanguageSuffix(key: string) {
  return key.substring(0, key.length - 4);
}

export function getHitsTotal(total: number | SearchTotalHits): number {
  if (typeof total === "number") {
    return total;
  }

  return total.value;
}

@Service()
export class NameSearchHighConfPersonAdapterService {
  private isCongressDocument(document: ActivityDocument): document is Congress {
    return !!(document as Congress).name;
  }

  private isTrialDocument(document: ActivityDocument): document is Trial {
    return !!(document as Trial).status;
  }

  customActivitySort(doc: RecentActivity): number {
    if (this.isCongressDocument(doc.document)) {
      return doc.document.endDate;
    } else if (this.isTrialDocument(doc.document)) {
      return doc.document.startDate;
    } else {
      return doc.document.datePublished;
    }
  }

  extractHighConfPersonFields(
    highConfPerson: { highConfHcpPresent: boolean; score: number },
    hit: SearchHit<HCPDocument>
  ): { recentActivities: RecentActivity[] } | undefined {
    if (
      !highConfPerson.highConfHcpPresent ||
      highConfPerson.score != hit._score
    )
      return undefined;

    const trials = this.getTrialsData(hit.inner_hits?.trials);
    const congress = this.getCongressData(hit.inner_hits?.congress);
    const publications = this.getPublicationsData(hit.inner_hits?.publications);

    const activities: RecentActivity[] = [
      ...this.getPublicationActivities(publications),
      ...this.getTrialActivities(trials),
      ...this.getCongressActivities(congress)
    ];

    if (!activities.length) {
      return undefined;
    }

    const activitiesSortedFromNewestToOldest = _.orderBy(
      activities,
      (e) => {
        const date = this.customActivitySort(e);
        return +date;
      },
      "desc"
    );

    return {
      recentActivities: activitiesSortedFromNewestToOldest.slice(
        0,
        maxNumRecentActivites
      )
    };
  }

  getPublicationActivities(
    publications: NonNullable<Publication[]>
  ): RecentActivity[] {
    const publicationActivities: RecentActivity[] = publications.map(
      (publication) => {
        const publishedDateInUnixFormat = Math.floor(
          new Date(publication?.datePublished as number).getTime()
        );

        // Please note that language specific fields are extracted based on languageCode and not user preferred lang
        const lang = publication["languageCode"];
        const fields = ["title", "journalName"].map(
          (prop) => `${prop}_${lang}`
        );
        const langSpecificfields: {
          title: string;
          journalName: string;
        } = _(publication)
          .pick(fields)
          .mapKeys((value, key) => stripLanguageSuffix(key))
          .value() as {
          title: string;
          journalName: string;
        };

        return {
          document: {
            ...publication,
            datePublished: publishedDateInUnixFormat,
            ...langSpecificfields,
            documentType: DocumentType.PUBLICATION
          }
        };
      }
    );

    return publicationActivities;
  }

  getTrialActivities(trials: NonNullable<Trial[]>): RecentActivity[] {
    const trialActivities: RecentActivity[] = trials.map((trial) => {
      const startDateInUnixFormat = Math.floor(
        new Date(trial["startDate"] as number).getTime()
      );
      const lang = trial["languageCode"];
      const fields = ["officialTitle", "briefTitle", "status"].map(
        (prop) => `${prop}_${lang}`
      );
      const langSpecificfields: {
        officialTitle: string;
        briefTitle: string;
        status: string;
      } = _(trial)
        .pick(fields)
        .mapKeys((value, key) => stripLanguageSuffix(key))
        .value() as {
        officialTitle: string;
        briefTitle: string;
        status: string;
      };

      return {
        document: {
          ...trial,
          startDate: startDateInUnixFormat,
          ...langSpecificfields,
          documentType: DocumentType.CLINICALTRIAL
        }
      };
    });

    return trialActivities;
  }

  getCongressActivities(congress: NonNullable<Congress[]>): RecentActivity[] {
    const congressActivities: RecentActivity[] = congress.map((congress) => {
      const endDateInUnixFormat = Math.floor(
        new Date(congress["endDate"] as number).getTime()
      );
      const lang = congress["languageCode"];
      const fields = ["name", "title"].map((prop) => `${prop}_${lang}`);
      const langSpecificfields: {
        name: string;
        title: string;
      } = _(congress)
        .pick(fields)
        .mapKeys((value, key) => stripLanguageSuffix(key))
        .value() as {
        name: string;
        title: string;
      };

      return {
        document: {
          ...congress,
          endDate: endDateInUnixFormat,
          ...langSpecificfields,
          documentType: DocumentType.CONGRESS
        }
      };
    });

    return congressActivities;
  }

  getHighConfidenceHcpInfo(
    hits: SearchHitsMetadata<HCPDocument>,
    filterType?: FilterTypesEnum
  ): {
    highConfHcpPresent: boolean;
    score: number;
  } {
    const numResults = getHitsTotal(hits.total ?? 0);
    // If it's a filter auto complete request or there are no results then return false
    if (numResults == 0 || filterType)
      return { highConfHcpPresent: false, score: 0 };
    const firstResultScore = hits.hits[0]?._score ?? DEFAULT_SCORE;

    if (numResults == 1)
      return { highConfHcpPresent: true, score: firstResultScore };

    const secondResultScore = hits.hits[1]?._score ?? DEFAULT_SCORE;
    return {
      // If score of first result is greater than FIRST_RESULT_SCORE_MULTIPLIER times the score of second result
      // then we identity first result as High confidence person for the given query
      highConfHcpPresent:
        firstResultScore > FIRST_RESULT_SCORE_MULTIPLIER * secondResultScore,
      score: firstResultScore
    };
  }

  getTrialsData(innerHits: SearchInnerHitsResult | undefined): Trial[] {
    if (!innerHits) {
      return [];
    }
    return innerHits.hits.hits
      .filter((hit) => !!hit.fields)
      .map((hit): Trial => {
        return {
          id: _.get(hit.fields, ["trials.id", 0], NO_DATA),
          phase: NO_DATA,
          phase_eng: _.get(hit.fields, ["trials.phase_eng", 0], NO_DATA),
          phase_cmn: _.get(hit.fields, ["trials.phase_cmn", 0], NO_DATA),
          phase_jpn: _.get(hit.fields, ["trials.phase_jpn", 0], NO_DATA),
          startDate: _.get(
            hit.fields,
            ["trials.startDate", 0],
            DEFAULT_TIMESTAMP
          ),
          briefTitle: NO_DATA,
          briefTitle_eng: _.get(
            hit.fields,
            ["trials.briefTitle_eng.keyword", 0],
            NO_DATA
          ),
          briefTitle_cmn: _.get(
            hit.fields,
            ["trials.briefTitle_cmn.keyword", 0],
            NO_DATA
          ),
          briefTitle_jpn: _.get(
            hit.fields,
            ["trials.briefTitle_jpn.keyword", 0],
            NO_DATA
          ),
          officialTitle: NO_DATA,
          officialTitle_eng: _.get(
            hit.fields,
            ["trials.officialTitle_eng.keyword", 0],
            NO_DATA
          ),
          officialTitle_cmn: _.get(
            hit.fields,
            ["trials.officialTitle_cmn.keyword", 0],
            NO_DATA
          ),
          officialTitle_jpn: _.get(
            hit.fields,
            ["trials.officialTitle_jpn.keyword", 0],
            NO_DATA
          ),
          status: NO_DATA,
          status_eng: _.get(hit.fields, ["trials.status_eng", 0], NO_DATA),
          status_cmn: _.get(hit.fields, ["trials.status_cmn", 0], NO_DATA),
          status_jpn: _.get(hit.fields, ["trials.status_jpn", 0], NO_DATA),
          primaryCompletionDate: _.get(
            hit.fields,
            ["trials.primaryCompletionDate", 0],
            DEFAULT_TIMESTAMP
          ),
          completionDate: _.get(
            hit.fields,
            ["trials.completionDate", 0],
            DEFAULT_TIMESTAMP
          ),
          languageCode: _.get(hit.fields, ["trials.languageCode", 0], NO_DATA),
          summary: _.get(hit.fields, ["trials.summary", 0], NO_DATA),
          eligibilityCriteria: _.get(
            hit.fields,
            ["trials.eligibilityCriteria", 0],
            NO_DATA
          ),
          interventions: EMPTY_ARRAY,
          persons: EMPTY_ARRAY,
          keywords: EMPTY_ARRAY,
          conditions: EMPTY_ARRAY,
          documentType: DocumentType.CLINICALTRIAL
        };
      });
  }

  getPublicationsData(
    innerHits: SearchInnerHitsResult | undefined
  ): Publication[] {
    if (!innerHits) {
      return [];
    }
    return innerHits.hits.hits
      .filter((hit) => !!hit.fields)
      .map((hit): Publication => {
        return {
          id: _.get(hit.fields, ["publications.id", 0], NO_DATA),
          title: NO_DATA,
          title_eng: _.get(
            hit.fields,
            ["publications.title_eng.keyword", 0],
            NO_DATA
          ),
          title_cmn: _.get(
            hit.fields,
            ["publications.title_cmn.keyword", 0],
            NO_DATA
          ),
          title_jpn: _.get(
            hit.fields,
            ["publications.title_jpn.keyword", 0],
            NO_DATA
          ),
          journalName: NO_DATA,
          journalName_eng: _.get(
            hit.fields,
            ["publications.journalName_eng", 0],
            NO_DATA
          ),
          journalName_cmn: _.get(
            hit.fields,
            ["publications.journalName_cmn", 0],
            NO_DATA
          ),
          journalName_jpn: _.get(
            hit.fields,
            ["publications.journalName_jpn", 0],
            NO_DATA
          ),
          type: EMPTY_ARRAY,
          persons: EMPTY_ARRAY,
          keywords: EMPTY_ARRAY,
          citationCount: _.get(
            hit.fields,
            ["publications.citationCount", 0],
            DEFAULT_COUNT
          ),
          substances: EMPTY_ARRAY,
          abstract: _.get(hit.fields, ["publications.abstract", 0], NO_DATA),
          datePublished: _.get(
            hit.fields,
            ["publications.datePublished", 0],
            DEFAULT_TIMESTAMP
          ),
          languageCode: _.get(
            hit.fields,
            ["publications.languageCode", 0],
            NO_DATA
          ),
          PMID: _.get(hit.fields, ["publications.PMID", 0], NO_DATA),
          documentType: DocumentType.PUBLICATION
        };
      });
  }

  getCongressData(innerHits: SearchInnerHitsResult | undefined): Congress[] {
    if (!innerHits) {
      return [];
    }
    return innerHits.hits.hits
      .filter((hit) => !!hit.fields)
      .map((hit): Congress => {
        return {
          id: _.get(hit.fields, ["congress.id", 0], NO_DATA),
          endDate: _.get(
            hit.fields,
            ["congress.endDate", 0],
            DEFAULT_TIMESTAMP
          ),
          name: NO_DATA,
          name_eng: _.get(hit.fields, ["congress.name_eng", 0], NO_DATA),
          name_jpn: _.get(hit.fields, ["congress.name_jpn", 0], NO_DATA),
          name_cmn: _.get(hit.fields, ["congress.name_cmn", 0], NO_DATA),
          title: NO_DATA,
          title_eng: _.get(
            hit.fields,
            ["congress.title_eng.keyword", 0],
            NO_DATA
          ),
          title_cmn: _.get(
            hit.fields,
            ["congress.title_cmn.keyword", 0],
            NO_DATA
          ),
          title_jpn: _.get(
            hit.fields,
            ["congress.title_jpn.keyword", 0],
            NO_DATA
          ),
          languageCode: _.get(
            hit.fields,
            ["congress.languageCode", 0],
            NO_DATA
          ),
          organizer: _.get(hit.fields, ["congress.organizer", 0], NO_DATA),
          keywords: EMPTY_ARRAY,
          persons: EMPTY_ARRAY
        };
      });
  }
}
