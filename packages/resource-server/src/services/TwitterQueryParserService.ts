// @ts-ignore
import { ITree, parse, simplify } from "@geemike/twitter-search-query-parser";
import { Service } from "typedi";

/**
 * Wrapper class to allow injecting this library through our DI framework
 */
@Service()
export class TwitterQueryParserService {
  /**
   * Library is defined here https://github.com/tweetdeck/twitter-search-query-parser
   * @param query
   * @returns
   */
  parseQuery(query: string): ITree {
    const parseTree = parse(query);
    return parseTree;
  }

  /**
   * Library is defined here https://github.com/tweetdeck/twitter-search-query-parser
   * @param query
   * @returns
   */
  simplifyQuery(query: ITree, simplifyFormat: { disallow: string[] }): ITree {
    const parseTree = simplify(query, simplifyFormat);
    return parseTree;
  }
}
