jest.mock("ioredis");
jest.mock("object-hash");
import Redis from "ioredis-mock";
import objectHash from "object-hash";

import { faker } from "@faker-js/faker";
import {
  SessionsSearchByCongressInput,
  IndicationNode,
  IndicationType
} from "@h1nyc/search-sdk";
import {
  createMockInstance,
  generateMockElasticsearchResponse,
  generateMockElasticsearchResponseWithAggregations,
  mockQueryUnderstandingServerResponse
} from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { ElasticSearchCongressSessionsService } from "./ElasticSearchCongressSessionsService";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";
import CalculateMinimumNameVariations from "./queryBuilders/CalculateMinimumNameVariations";
import { UserOnboardingDataService } from "./UserOnboardingDataService";
import NameSearchBuilderFactory from "./queryBuilders/NameSearchBuilderFactory";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  CHINESE,
  ENGLISH,
  JAPANESE,
  Language,
  LanguageDetectService
} from "./LanguageDetectService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { UserResourceClient } from "@h1nyc/account-sdk";
import {
  featureFlagDefaults,
  NameSearchFeatureFlags,
  nameSearchFeatureFlagTypes
} from "./NameSearchResourceServiceRewrite";
import { when } from "jest-when";
import { LDFlagsState } from "launchdarkly-node-server-sdk";
import DefaultNameSearchBuilder from "./queryBuilders/DefaultNameSearchBuilder";
import { generateMockOnboardingData } from "./NameSearchResourceServiceRewrite.test";
import { QueryUnderstandingServiceResponseForIndications } from "../proto/query_understanding_service_pb";
import {
  CongressEventPageSearchResourceService,
  SessionsSearchFilterAutocompleteTypes
} from "./CongressEventPageSearchResourceService";
import { ElasticSearchCongressService } from "./ElasticsearchCongressService";
import { CongressSearchResponseAdapterService } from "./CongressSearchResponseAdapterService";

const FEATURE_FLAG_DEFAULTS: NameSearchFeatureFlags = {
  enableHighConfHCPFeature: false,
  enableQueryIntent: false,
  enableIntentBasedSearchQuery: false,
  enableNameSearchPersonalisation: false,
  enableCTMSV2: false,
  enableResultsOutsideUsersSlice: false,
  enableTagsInElasticsearch: false,
  enableBrazilianClaims: true,
  enableUniquePatientCountForClaims: false,
  disableUniquePatientCountForOnlyProcedures: false,
  enableNestedIndicationFilter: false,
  enableCcsrExclusionForMatchedCounts: false,
  enableLocationFilterRegionRollup: false,
  enableNewGlobalLeaderTier: false,
  enableExUSGlobalLeader: false,
  enableCountrySpecificNonIndicationLeaderFilters: false
};

const generateNameSearchFeatureFlags = (
  overrides: Partial<NameSearchFeatureFlags> = {}
): NameSearchFeatureFlags => {
  return {
    ...FEATURE_FLAG_DEFAULTS,
    ...overrides
  };
};

function generateFeatureFlagsState(
  featureFlagValues: Partial<NameSearchFeatureFlags> = {}
) {
  const featureFlags = {
    ...generateNameSearchFeatureFlags(),
    ...featureFlagValues
  };

  const getFlagValue = jest.fn();

  for (const flag of nameSearchFeatureFlagTypes) {
    const flagKey = featureFlagDefaults[flag].key;
    when(getFlagValue).calledWith(flagKey).mockReturnValue(featureFlags[flag]);
  }

  return {
    getFlagValue
  } as unknown as LDFlagsState;
}

function generateMockPersonHit() {
  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: { id: faker.datatype.uuid() }
  };
}
let redis = new Redis();

beforeEach(() => {
  jest.resetAllMocks();
});

afterEach((done) => {
  if (redis) {
    redis.flushall().then(() => done());
  }
});

describe("CongressEventPageSearchResourceService", () => {
  describe("search", () => {
    describe("elasticsearch query building", () => {
      describe("input query", () => {
        it("should include should clauses for each of the main search paths", async () => {
          const input: SessionsSearchByCongressInput = {
            query: faker.datatype.string(),
            congressId: faker.datatype.string(),
            userId: faker.datatype.string(),
            projectId: faker.datatype.string(),
            paging: {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            }
          };

          const configService = createMockInstance(ConfigService);
          configService.elasticCongressIndex = faker.datatype.string();

          const queryUnderstandingServiceClient = createMockInstance(
            QueryUnderstandingServiceClient
          );
          const queryUnderstandingServerResponse =
            mockQueryUnderstandingServerResponse(input.query!, false);
          queryUnderstandingServiceClient.analyze.mockResolvedValue(
            queryUnderstandingServerResponse
          );

          const elasticSearchCongressSessionsService = createMockInstance(
            ElasticSearchCongressSessionsService
          );
          elasticSearchCongressSessionsService.mget.mockResolvedValue({
            docs: []
          });
          elasticSearchCongressSessionsService.query.mockResolvedValue(
            generateMockElasticsearchResponseWithAggregations()
          );

          const elasticSearchCongressService = createMockInstance(
            ElasticSearchCongressService
          );
          elasticSearchCongressService.query.mockResolvedValue(
            generateMockElasticsearchResponseWithAggregations()
          );

          const indicationsTreeSearchService = createMockInstance(
            IndicationsTreeSearchService
          );

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );

          const nameSearchBuilderFactory = createMockInstance(
            NameSearchBuilderFactory
          );

          nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
            DefaultNameSearchBuilder.getInstance()
          );

          const matchingPeople = Array.from({ length: 10 }, () =>
            generateMockPersonHit()
          );
          const elasticSearchPeopleService =
            createMockInstance(ElasticSearchService);
          elasticSearchPeopleService.query.mockResolvedValue(
            generateMockElasticsearchResponse(matchingPeople)
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );

          const languageDetector = () => {
            return faker.helpers.arrayElement([
              ENGLISH,
              CHINESE,
              JAPANESE
            ]) as Language;
          };
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );

          const featureFlagsService = createMockInstance(
            FeatureFlagsService as any
          ) as FeatureFlagsService;
          featureFlagsService.getAllFlags = jest
            .fn()
            .mockResolvedValue(generateFeatureFlagsState());

          const userClient = createMockInstance(UserResourceClient);

          const hashedValue = faker.datatype.string();
          objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);

          redis = new Redis({
            data: {
              [hashedValue]: "[]"
            }
          });

          const congressSearchResponseAdapterService = createMockInstance(
            CongressSearchResponseAdapterService
          );

          const congressEventPageSearchResourceService =
            new CongressEventPageSearchResourceService(
              configService,
              elasticSearchCongressSessionsService,
              congressSearchResponseAdapterService,
              queryUnderstandingServiceClient,
              calculateMinimumNameVariations,
              elasticSearchCongressService,
              indicationsTreeSearchService,
              userOnboardingDataService,
              nameSearchBuilderFactory,
              elasticSearchPeopleService,
              languageDetectService,
              featureFlagsService,
              userClient
            );

          await congressEventPageSearchResourceService.searchSessionsByCongress(
            input
          );

          expect(
            elasticSearchCongressSessionsService.query
          ).toHaveBeenCalledWith(
            expect.objectContaining({
              index: configService.elasticCongressSessionsIndex,
              track_total_hits: true,
              _source: {
                include: [
                  "h1_session_id",
                  "h1_conference_id",
                  "h1_series_id",
                  "name",
                  "description",
                  "indication",
                  "url",
                  "translations",
                  "h1_person_ids",
                  "speakers",
                  "filters.start_date",
                  "filters.end_date",
                  "filters.session_type",
                  "sub_sessions"
                ]
              },
              query: {
                bool: {
                  filter: [
                    {
                      term: {
                        h1_conference_id: input.congressId
                      }
                    }
                  ],
                  minimum_should_match: 1,
                  should: [
                    {
                      simple_query_string: {
                        query: input.query,
                        fields: [
                          "name",
                          "name.phonetic",
                          "description",
                          "indication"
                        ],
                        default_operator: "and"
                      }
                    },
                    {
                      nested: {
                        path: "sub_sessions",
                        query: {
                          simple_query_string: {
                            query: input.query,
                            fields: [
                              "name",
                              "name.phonetic",
                              "description",
                              "indication"
                            ],
                            default_operator: "and"
                          }
                        }
                      }
                    },
                    {
                      terms: {
                        h1_person_ids: matchingPeople.map(
                          (person) => person._source.id
                        ),
                        boost: 1
                      }
                    }
                  ]
                }
              },
              from: input.paging?.offset,
              size: input.paging?.limit,
              sort: [
                "_score",
                {
                  "filters.start_date": "asc"
                },
                {
                  speaker_count: "desc"
                }
              ]
            })
          );
        });

        describe("filters", () => {
          it("should include filters in elasticsearch request when defined in input", async () => {
            const input: SessionsSearchByCongressInput = {
              query: faker.datatype.string(),
              congressId: faker.datatype.string(),
              projectId: faker.datatype.string(),
              userId: faker.datatype.string(),
              filters: {
                dateRange: {
                  min: faker.datatype.number(),
                  max: faker.datatype.number()
                },
                type: {
                  sessionTypes: [
                    faker.datatype.string(),
                    faker.datatype.string()
                  ]
                }
              },
              paging: {
                offset: faker.datatype.number(),
                limit: faker.datatype.number()
              }
            };

            const configService = createMockInstance(ConfigService);
            configService.elasticCongressIndex = faker.datatype.string();

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServerResponse =
              mockQueryUnderstandingServerResponse(input.query!, false);

            const quResponseIndiations: QueryUnderstandingServiceResponseForIndications =
              jest.createMockFromModule(
                "../proto/query_understanding_service_pb"
              );
            const indicationsParsedQuery = faker.datatype.string();
            quResponseIndiations.getIndicationsParsedQuery = jest.fn(
              () => indicationsParsedQuery
            );
            quResponseIndiations.getIndicationsIcdCodesQuery = jest.fn(() =>
              faker.datatype.string()
            );
            queryUnderstandingServiceClient.getIndicationSynonymsAndIcdCodes.mockResolvedValue(
              quResponseIndiations
            );
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServerResponse
            );

            const elasticSearchCongressSessionsService = createMockInstance(
              ElasticSearchCongressSessionsService
            );
            elasticSearchCongressSessionsService.mget.mockResolvedValue({
              docs: []
            });
            elasticSearchCongressSessionsService.query.mockResolvedValue(
              generateMockElasticsearchResponseWithAggregations()
            );

            const elasticSearchCongressService = createMockInstance(
              ElasticSearchCongressService
            );
            elasticSearchCongressService.query.mockResolvedValue(
              generateMockElasticsearchResponseWithAggregations()
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );
            const mockIndications: IndicationNode[] = [
              {
                id: faker.datatype.string(),
                indicationName: faker.datatype.string(),
                h1Id: "",
                parentH1Ids: [],
                indicationType: IndicationType.L1,
                matchedSynonyms: [],
                icdCodes: [],
                children: []
              }
            ];
            indicationsTreeSearchService.searchIndicationsByQuery.mockResolvedValue(
              mockIndications
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const userOnboardingDataService = createMockInstance(
              UserOnboardingDataService
            );
            userOnboardingDataService.getOnboardingData.mockResolvedValue(
              generateMockOnboardingData()
            );

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
              DefaultNameSearchBuilder.getInstance()
            );

            const matchingPeople = Array.from({ length: 10 }, () =>
              generateMockPersonHit()
            );
            const elasticSearchPeopleService =
              createMockInstance(ElasticSearchService);
            elasticSearchPeopleService.query.mockResolvedValue(
              generateMockElasticsearchResponse(matchingPeople)
            );

            const languageDetectService = createMockInstance(
              LanguageDetectService
            );

            const languageDetector = () => {
              return faker.helpers.arrayElement([
                ENGLISH,
                CHINESE,
                JAPANESE
              ]) as Language;
            };
            languageDetectService.getLanguageDetector.mockReturnValue(
              languageDetector
            );

            const featureFlagsService = createMockInstance(
              FeatureFlagsService as any
            ) as FeatureFlagsService;
            featureFlagsService.getAllFlags = jest
              .fn()
              .mockResolvedValue(generateFeatureFlagsState());

            const userClient = createMockInstance(UserResourceClient);

            const congressSearchResponseAdapterService = createMockInstance(
              CongressSearchResponseAdapterService
            );

            const congressEventPageSearchResourceService =
              new CongressEventPageSearchResourceService(
                configService,
                elasticSearchCongressSessionsService,
                congressSearchResponseAdapterService,
                queryUnderstandingServiceClient,
                calculateMinimumNameVariations,
                elasticSearchCongressService,
                indicationsTreeSearchService,
                userOnboardingDataService,
                nameSearchBuilderFactory,
                elasticSearchPeopleService,
                languageDetectService,
                featureFlagsService,
                userClient
              );

            await congressEventPageSearchResourceService.searchSessionsByCongress(
              input
            );

            const searchQueryWithIndications = `${input.query}|${indicationsParsedQuery}`;

            expect(
              elasticSearchCongressSessionsService.query
            ).toHaveBeenCalledWith(
              expect.objectContaining({
                index: configService.elasticCongressSessionsIndex,
                track_total_hits: true,
                _source: {
                  include: expect.any(Array)
                },
                query: {
                  bool: {
                    filter: [
                      {
                        term: {
                          h1_conference_id: input.congressId
                        }
                      },
                      {
                        range: {
                          "filters.start_date": {
                            gte: input.filters?.dateRange?.min
                          }
                        }
                      },
                      {
                        range: {
                          "filters.start_date": {
                            lte: input.filters?.dateRange?.max
                          }
                        }
                      },
                      {
                        terms: {
                          "filters.session_type":
                            input.filters?.type?.sessionTypes
                        }
                      }
                    ],
                    minimum_should_match: 1,
                    should: [
                      {
                        simple_query_string: {
                          default_operator: "and",
                          fields: [
                            "name",
                            "name.phonetic",
                            "description",
                            "indication"
                          ],
                          query: searchQueryWithIndications
                        }
                      },
                      {
                        nested: {
                          path: "sub_sessions",
                          query: {
                            simple_query_string: {
                              query: searchQueryWithIndications,
                              fields: [
                                "name",
                                "name.phonetic",
                                "description",
                                "indication"
                              ],
                              default_operator: "and"
                            }
                          }
                        }
                      },
                      {
                        terms: {
                          h1_person_ids: matchingPeople.map(
                            (person) => person._source.id
                          ),
                          boost: 1
                        }
                      }
                    ]
                  }
                },
                from: input.paging?.offset,
                size: input.paging?.limit,
                sort: [
                  "_score",
                  {
                    "filters.start_date": "asc"
                  },
                  {
                    speaker_count: "desc"
                  }
                ]
              })
            );
          });
        });
      });
    });
  });

  describe("getSpeakerIdsByRole", () => {
    it("should query the congress search index to request speakers for the given congress ID", async () => {
      const input = {
        congressId: faker.datatype.number().toString(),
        roles: [faker.datatype.string()]
      };

      const configService = createMockInstance(ConfigService);
      configService.elasticCongressIndex = faker.datatype.string();
      const elasticSearchCongressService = createMockInstance(
        ElasticSearchCongressService
      );
      elasticSearchCongressService.query.mockResolvedValue(
        generateMockElasticsearchResponseWithAggregations([])
      );
      const congressEventPageSearchResourceService =
        new CongressEventPageSearchResourceService(
          configService,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          elasticSearchCongressService,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any
        );

      await congressEventPageSearchResourceService.getSpeakerIdsByRole(input);

      expect(elasticSearchCongressService.query).toHaveBeenCalledWith({
        _source: ["speakers"],
        index: configService.elasticCongressIndex,
        size: 1,
        query: {
          term: {
            h1_conference_id: input.congressId
          }
        }
      });
    });

    it("should return the speaker ids for the given congress and role", async () => {
      const input = {
        congressId: faker.datatype.number().toString(),
        roles: [faker.datatype.string()]
      };

      const hit = {
        speakers: [
          {
            h1_person_id: faker.datatype.string(),
            role: faker.datatype.string()
          },
          {
            h1_person_id: faker.datatype.string(),
            role: input.roles[0]
          },
          {
            role: input.roles[0]
          },
          {
            h1_person_id: faker.datatype.string(),
            role: input.roles[0]
          }
        ]
      };
      const configService = createMockInstance(ConfigService);
      configService.elasticCongressIndex = faker.datatype.string();
      const elasticSearchCongressService = createMockInstance(
        ElasticSearchCongressService
      );
      elasticSearchCongressService.query.mockResolvedValue(
        generateMockElasticsearchResponseWithAggregations([hit])
      );
      const congressEventPageSearchResourceService =
        new CongressEventPageSearchResourceService(
          configService,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          elasticSearchCongressService,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any
        );

      const actualResponse =
        await congressEventPageSearchResourceService.getSpeakerIdsByRole(input);

      expect(actualResponse).toEqual(
        expect.arrayContaining([
          hit.speakers[1].h1_person_id,
          hit.speakers[3].h1_person_id
        ])
      );
    });
  });

  describe("getIndicationsForCongress", () => {
    it("should query the congress search index to request indications for the given congress ID", async () => {
      const congressId = faker.datatype.number().toString();

      const configService = createMockInstance(ConfigService);
      configService.elasticCongressIndex = faker.datatype.string();
      const elasticSearchCongressService = createMockInstance(
        ElasticSearchCongressService
      );
      elasticSearchCongressService.query.mockResolvedValue(
        generateMockElasticsearchResponseWithAggregations([])
      );
      const congressEventPageSearchResourceService =
        new CongressEventPageSearchResourceService(
          configService,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          elasticSearchCongressService,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any
        );

      await congressEventPageSearchResourceService.getIndicationsForCongress(
        congressId
      );

      expect(elasticSearchCongressService.query).toHaveBeenCalledWith({
        _source: ["indication"],
        index: configService.elasticCongressIndex,
        size: 1,
        query: {
          term: {
            h1_conference_id: congressId
          }
        }
      });
    });

    it("should return the indication for the given congress", async () => {
      const congressId = faker.datatype.number().toString();

      const hit = {
        indication: [faker.datatype.string(), faker.datatype.string()]
      };
      const configService = createMockInstance(ConfigService);
      configService.elasticCongressIndex = faker.datatype.string();
      const elasticSearchCongressService = createMockInstance(
        ElasticSearchCongressService
      );
      elasticSearchCongressService.query.mockResolvedValue(
        generateMockElasticsearchResponseWithAggregations([hit])
      );
      const congressEventPageSearchResourceService =
        new CongressEventPageSearchResourceService(
          configService,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          elasticSearchCongressService,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          {} as any
        );

      const actualResponse =
        await congressEventPageSearchResourceService.getIndicationsForCongress(
          congressId
        );

      expect(actualResponse).toEqual(hit.indication);
    });
  });

  describe("autocompleteSessionTypes", () => {
    it("should query the congress sessions search index to request session types for the given congress ID", async () => {
      const input = {
        congressId: faker.datatype.number().toString(),
        filterQuery: faker.datatype.string(),
        count: faker.datatype.number(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const configService = createMockInstance(ConfigService);
      configService.elasticCongressSessionsIndex = faker.datatype.string();

      const data = generateMockElasticsearchResponseWithAggregations([]);
      const elasticSearchCongressSessionsService = createMockInstance(
        ElasticSearchCongressSessionsService
      );
      elasticSearchCongressSessionsService.query.mockResolvedValue(data);

      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const userClient = createMockInstance(UserResourceClient);

      const featureFlagsService = createMockInstance(
        FeatureFlagsService as any
      ) as FeatureFlagsService;
      featureFlagsService.getAllFlags = jest
        .fn()
        .mockResolvedValue(generateFeatureFlagsState());

      const languageDetectService = createMockInstance(LanguageDetectService);

      const nameSearchBuilderFactory = new NameSearchBuilderFactory();

      const congressSearchResponseAdapterService = createMockInstance(
        CongressSearchResponseAdapterService
      );

      const congressEventPageSearchResourceService =
        new CongressEventPageSearchResourceService(
          configService,
          elasticSearchCongressSessionsService,
          congressSearchResponseAdapterService,
          queryUnderstandingServiceClient,
          {} as any,
          {} as any,
          {} as any,
          {} as any,
          nameSearchBuilderFactory,
          {} as any,
          languageDetectService,
          featureFlagsService,
          userClient
        );

      await congressEventPageSearchResourceService.autocompleteSessionTypes(
        input
      );

      expect(elasticSearchCongressSessionsService.query).toHaveBeenCalledWith({
        index: configService.elasticCongressSessionsIndex,
        size: 0,
        track_total_hits: false,
        _source: false,
        query: {
          bool: {
            filter: [
              {
                term: {
                  h1_conference_id: input.congressId
                }
              }
            ],
            minimum_should_match: 0,
            must: [
              {
                match_phrase: {
                  "session_type.autocomplete_search": {
                    query: input.filterQuery
                  }
                }
              }
            ]
          }
        },
        aggs: {
          type: {
            filter: {
              bool: {
                filter: [
                  {
                    match_phrase: {
                      "session_type.autocomplete_search": {
                        query: input.filterQuery
                      }
                    }
                  }
                ]
              }
            },
            aggs: {
              filtered_matching: {
                terms: {
                  field: "filters.session_type",
                  size: input.count
                }
              }
            }
          }
        }
      });
      expect(
        congressSearchResponseAdapterService.adaptToCongressSearchFilterAggregations
      ).toHaveBeenCalledWith(data, SessionsSearchFilterAutocompleteTypes.TYPE);
    });
  });
});
