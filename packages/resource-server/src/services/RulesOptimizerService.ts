import {
  RuleCombinatorEnum,
  RuleGroupType,
  RuleOperatorEnum,
  RuleOrRuleGroup,
  RuleTypeEnum,
  TermsQueryType
} from "@h1nyc/search-sdk";
import { flatten, groupBy, mapValues, omit, values } from "lodash";
import { Service } from "typedi";

@Service()
export class RulesOptimizerService {
  private isRuleGroupType(rule: RuleOrRuleGroup) {
    return rule.type === RuleTypeEnum.RULE_GROUP;
  }

  private getRuleKey(rule: RuleOrRuleGroup) {
    if (this.isRuleGroupType(rule)) {
      return RuleTypeEnum.RULE_GROUP;
    }

    return `${rule.rule?.field}.${rule.rule?.operator}`;
  }

  private combineRules(
    rules: RuleOrRuleGroup[],
    ruleCombinator: RuleCombinatorEnum
  ): RuleOrRuleGroup {
    const field = rules[0].rule!.field;
    const operator = rules[0].rule!.operator;

    const values = flatten(rules.map((rule) => rule.rule!.value));

    let termsQueryType: TermsQueryType = TermsQueryType.TERMS;

    /* NOTE: <PERSON>'s law. 
       Query should be terms(disjunction) or terms_set(conjunction)
        depending on operator
    */
    if (ruleCombinator === RuleCombinatorEnum.OR) {
      if (operator === RuleOperatorEnum.NOT_EQUAL) {
        termsQueryType = TermsQueryType.TERMS_SET;
      }
    } else {
      if (operator === RuleOperatorEnum.EQUAL) {
        termsQueryType = TermsQueryType.TERMS_SET;
      }
    }

    return {
      type: RuleTypeEnum.RULE,
      rule: {
        field,
        operator,
        value: values,
        termsQueryType
      }
    };
  }

  private optimize(root: RuleGroupType): RuleGroupType {
    if (!root?.rules && !root?.rules.length) {
      throw new Error("Rule group must contain at least one rule");
    }

    let rules: RuleOrRuleGroup[] = [];
    for (let index = 0; index < root!.rules.length; index++) {
      const rule = root.rules[index];
      if (this.isRuleGroupType(rule)) {
        const optimizedRules = this.optimize(rule.ruleGroup!);
        rules.push({
          type: RuleTypeEnum.RULE_GROUP,
          ruleGroup: optimizedRules
        });
      } else {
        rules.push(rule);
      }
    }

    const groupedRules = groupBy(rules, (rule) => this.getRuleKey(rule));
    const ruleGroups = groupedRules[RuleTypeEnum.RULE_GROUP];
    const optimizedRules = values(
      mapValues(omit(groupedRules, [RuleTypeEnum.RULE_GROUP]), (value) => {
        return this.combineRules(value, root.combinator);
      })
    );

    if (ruleGroups) {
      optimizedRules.push(...ruleGroups);
    }
    rules = optimizedRules;

    return {
      combinator: root.combinator,
      rules: rules,
      not: root.not
    };
  }

  optimizeRules(root: RuleOrRuleGroup): RuleOrRuleGroup {
    if (this.isRuleGroupType(root)) {
      return {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: this.optimize(root.ruleGroup!)
      };
    }

    return root;
  }
}
