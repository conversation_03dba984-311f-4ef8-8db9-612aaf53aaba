import { faker } from "@faker-js/faker";
import { ConfigService } from "./ConfigService";

describe("ConfigService", () => {
  it("should return basic authentication options for elasticsearch", () => {
    const configService = new ConfigService();
    configService.elasticHost = faker.internet.url();
    configService.elasticUsername = faker.internet.userName();
    configService.elasticPassword = faker.internet.password();
    configService.elasticRequestTimeout = faker.datatype.number();
    configService.elasticMaxRetries = faker.datatype.number();

    const options = configService.elasticsearchClientOptions;

    expect(options).toEqual({
      node: configService.elasticHost,
      requestTimeout: configService.elasticRequestTimeout,
      maxRetries: configService.elasticMaxRetries,
      compression: true,
      auth: {
        username: configService.elasticUsername,
        password: configService.elasticPassword
      }
    });
  });
});
