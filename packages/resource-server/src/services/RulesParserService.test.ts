import {
  PatientAgeBucketEnum,
  RuleCombinatorEnum,
  RuleFieldEnum,
  RuleOperatorEnum,
  RuleTypeEnum,
  TermsQueryType
} from "@h1nyc/search-sdk";
import { RulesParserService } from "./RulesParserService";
import { faker } from "@faker-js/faker";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { createMockInstance } from "../util/TestUtils";
import { RulesOptimizerService } from "./RulesOptimizerService";

describe("parseRulesToEsQueries", () => {
  describe("negative tests", () => {
    it("should return empty array when no patient claims filter is applied", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const queries = parserService.parseRulesToEsQueries({
        patientClaimsFilter: undefined,
        patientClaimsFilterV2: undefined
      });

      expect(queries).toEqual([]);
    });

    it("should return empty array when inclusion/exclusion keys are undefined", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const queries = parserService.parseRulesToEsQueries({
        patientClaimsFilter: undefined,
        patientClaimsFilterV2: {
          inclusionCriteria: undefined,
          exclusionCriteria: undefined
        }
      });

      expect(queries).toEqual([]);
    });

    it("should throw error when the rules array is empty", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const root = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          rules: [],
          combinator: RuleCombinatorEnum.AND
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      expect(() =>
        parserService.parseRulesToEsQueries({
          patientClaimsFilter: root
        })
      ).toThrowError(new Error("Rule group must contain at least one rule"));
    });

    it("should throw error when the unsupported operator is used", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const root = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                operator: ">" as any,
                value: [faker.datatype.string()]
              }
            }
          ],
          combinator: RuleCombinatorEnum.AND
        }
      };

      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      expect(() =>
        parserService.parseRulesToEsQueries({
          patientClaimsFilter: root
        })
      ).toThrowError(new Error(`RuleOperatorEnum(>) not supported`));
    });
  });

  describe("should map field enum to property name", () => {
    it("diagnosisIcdCode", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.DIAGNOSES_CODE,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);

      const queries = parserService.parseRulesToEsQueries({
        patientClaimsFilter: root
      });
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            "patientClaims.diagnosisIcdCode": [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("diagnosisIndications", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.DIAGNOSES_INDICATION,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const queries = parserService.parseRulesToEsQueries({
        patientClaimsFilter: root
      });
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            "patientClaims.diagnosisIndications": [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("procedureCode", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.PROCEDURE_CODE,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const queries = parserService.parseRulesToEsQueries({
        patientClaimsFilter: root
      });

      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            "patientClaims.procedureCode": [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("genericDrugNames", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.GENERIC_DRUG,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const queries = parserService.parseRulesToEsQueries({
        patientClaimsFilter: root
      });

      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            "patientClaims.genericDrugNames": [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("age", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.AGE,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const queries = parserService.parseRulesToEsQueries({
        patientClaimsFilter: root
      });
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            "patientClaims.age": [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("gender", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.GENDER,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const queries = parserService.parseRulesToEsQueries({
        patientClaimsFilter: root
      });
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            "patientClaims.gender": [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("diversity", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.DIVERSITY,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);

      const queries = parserService.parseRulesToEsQueries({
        patientClaimsFilter: root
      });
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            "patientClaims.diversity": [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("ethnicity", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.ETHNICITY,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);

      const queries = parserService.parseRulesToEsQueries({
        patientClaimsFilter: root
      });
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            "patientClaims.ethnicity": [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });
  });

  describe("should map field enum to patient level index fields when usePatientIndexForCount=true", () => {
    it("diagnosisIcdCode", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.DIAGNOSES_CODE,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);

      const queries = parserService.parseRulesToEsQueries(
        {
          patientClaimsFilter: root
        },
        { usePatientIndexForCount: true }
      );
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            diagnosis_codes: [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("diagnosisIndications", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.DIAGNOSES_INDICATION,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const queries = parserService.parseRulesToEsQueries(
        {
          patientClaimsFilter: root
        },
        { usePatientIndexForCount: true }
      );
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            indications: [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("procedureCode", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.PROCEDURE_CODE,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const queries = parserService.parseRulesToEsQueries(
        {
          patientClaimsFilter: root
        },
        { usePatientIndexForCount: true }
      );

      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            procedure_codes: [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("genericDrugNames", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.GENERIC_DRUG,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const queries = parserService.parseRulesToEsQueries(
        {
          patientClaimsFilter: root
        },
        { usePatientIndexForCount: true }
      );

      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            generic_drug_name: [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("age", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.AGE,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const queries = parserService.parseRulesToEsQueries(
        {
          patientClaimsFilter: root
        },
        { usePatientIndexForCount: true }
      );
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            age: [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("gender", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.GENDER,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const queries = parserService.parseRulesToEsQueries(
        {
          patientClaimsFilter: root
        },
        { usePatientIndexForCount: true }
      );
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            gender: [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });

    it("diversity", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE,
        rule: {
          field: RuleFieldEnum.DIVERSITY,
          value: [values[0]],
          operator: RuleOperatorEnum.EQUAL
        }
      };
      rulesOptimizerService.optimizeRules.mockReturnValue(root);

      const queries = parserService.parseRulesToEsQueries(
        {
          patientClaimsFilter: root
        },
        { usePatientIndexForCount: true }
      );
      const expectedQueries: QueryDslQueryContainer[] = [
        {
          terms: {
            race: [values[0]]
          }
        }
      ];
      expect(queries).toEqual(expectedQueries);
    });
  });

  it("should parse single root level query", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [faker.datatype.string()];
    const root = {
      type: RuleTypeEnum.RULE,
      rule: {
        field: RuleFieldEnum.DIAGNOSES_CODE,
        value: [values[0]],
        operator: RuleOperatorEnum.EQUAL
      }
    };

    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const queries = parserService.parseRulesToEsQueries({
      patientClaimsFilter: root
    });
    const expectedQueries: QueryDslQueryContainer[] = [
      {
        terms: {
          "patientClaims.diagnosisIcdCode": [values[0]]
        }
      }
    ];
    expect(queries).toEqual(expectedQueries);
  });

  it("should parse A OR B type rules", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [faker.datatype.string(), faker.datatype.string()];
    const root = {
      type: RuleTypeEnum.RULE_GROUP,
      ruleGroup: {
        rules: [
          {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.DIAGNOSES_CODE,
              value: [values[0]],
              operator: RuleOperatorEnum.EQUAL
            }
          },
          {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.PROCEDURE_CODE,
              value: [values[1]],
              operator: RuleOperatorEnum.EQUAL
            }
          }
        ],
        combinator: RuleCombinatorEnum.OR
      }
    };
    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const queries = parserService.parseRulesToEsQueries({
      patientClaimsFilter: root
    });
    const expectedQueries: QueryDslQueryContainer[] = [
      {
        bool: {
          should: [
            {
              terms: {
                "patientClaims.diagnosisIcdCode": [values[0]]
              }
            },
            {
              terms: {
                "patientClaims.procedureCode": [values[1]]
              }
            }
          ],
          minimum_should_match: 1
        }
      }
    ];
    expect(queries).toEqual(expectedQueries);
  });

  it("should parse A AND B type rules", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [faker.datatype.string(), faker.datatype.string()];
    const root = {
      type: RuleTypeEnum.RULE_GROUP,
      ruleGroup: {
        rules: [
          {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.DIAGNOSES_CODE,
              value: [values[0]],
              operator: RuleOperatorEnum.EQUAL
            }
          },
          {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.PROCEDURE_CODE,
              value: [values[1]],
              operator: RuleOperatorEnum.EQUAL
            }
          }
        ],
        combinator: RuleCombinatorEnum.AND
      }
    };

    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const queries = parserService.parseRulesToEsQueries({
      patientClaimsFilter: root
    });
    const expectedQueries: QueryDslQueryContainer[] = [
      {
        bool: {
          must: [
            {
              terms: {
                "patientClaims.diagnosisIcdCode": [values[0]]
              }
            },
            {
              terms: {
                "patientClaims.procedureCode": [values[1]]
              }
            }
          ]
        }
      }
    ];
    expect(queries).toEqual(expectedQueries);
  });

  it("should parse nested queries of type A OR (B AND C)", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string()
    ];
    const root = {
      type: RuleTypeEnum.RULE_GROUP,
      ruleGroup: {
        combinator: RuleCombinatorEnum.OR,
        rules: [
          {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.DIAGNOSES_CODE,
              value: [values[0]],
              operator: RuleOperatorEnum.EQUAL
            }
          },
          {
            type: RuleTypeEnum.RULE_GROUP,
            ruleGroup: {
              combinator: RuleCombinatorEnum.AND,
              rules: [
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.PROCEDURE_CODE,
                    value: [values[1]],
                    operator: RuleOperatorEnum.EQUAL
                  }
                },
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.DIAGNOSES_INDICATION,
                    value: [values[2]],
                    operator: RuleOperatorEnum.EQUAL
                  }
                }
              ]
            }
          }
        ]
      }
    };

    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const queries = parserService.parseRulesToEsQueries({
      patientClaimsFilter: root
    });
    const expectedQueries: QueryDslQueryContainer[] = [
      {
        bool: {
          should: [
            {
              terms: {
                "patientClaims.diagnosisIcdCode": [values[0]]
              }
            },
            {
              bool: {
                must: [
                  {
                    terms: {
                      "patientClaims.procedureCode": [values[1]]
                    }
                  },
                  {
                    terms: {
                      "patientClaims.diagnosisIndications": [values[2]]
                    }
                  }
                ]
              }
            }
          ],
          minimum_should_match: 1
        }
      }
    ];
    expect(queries).toEqual(expectedQueries);
  });

  it("should parse nested queries of type (A AND B) OR C", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string()
    ];
    const root = {
      type: RuleTypeEnum.RULE_GROUP,
      ruleGroup: {
        combinator: RuleCombinatorEnum.OR,
        rules: [
          {
            type: RuleTypeEnum.RULE_GROUP,
            ruleGroup: {
              combinator: RuleCombinatorEnum.AND,
              rules: [
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.DIAGNOSES_CODE,
                    value: [values[0]],
                    operator: RuleOperatorEnum.EQUAL
                  }
                },
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.PROCEDURE_CODE,
                    value: [values[1]],
                    operator: RuleOperatorEnum.EQUAL
                  }
                }
              ]
            }
          },
          {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.DIAGNOSES_INDICATION,
              value: [values[2]],
              operator: RuleOperatorEnum.EQUAL
            }
          }
        ]
      }
    };
    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const queries = parserService.parseRulesToEsQueries({
      patientClaimsFilter: root
    });
    const expectedQueries: QueryDslQueryContainer[] = [
      {
        bool: {
          should: [
            {
              bool: {
                must: [
                  {
                    terms: {
                      "patientClaims.diagnosisIcdCode": [values[0]]
                    }
                  },
                  {
                    terms: {
                      "patientClaims.procedureCode": [values[1]]
                    }
                  }
                ]
              }
            },
            {
              terms: {
                "patientClaims.diagnosisIndications": [values[2]]
              }
            }
          ],
          minimum_should_match: 1
        }
      }
    ];
    expect(queries).toEqual(expectedQueries);
  });

  it("should parse queries honoring termsQueryType", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string()
    ];
    const root = {
      type: RuleTypeEnum.RULE_GROUP,
      ruleGroup: {
        combinator: RuleCombinatorEnum.OR,
        rules: [
          {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.DIAGNOSES_CODE,
              value: [values[0]],
              operator: RuleOperatorEnum.EQUAL,
              termsQueryType: TermsQueryType.TERMS
            }
          },
          {
            type: RuleTypeEnum.RULE_GROUP,
            ruleGroup: {
              combinator: RuleCombinatorEnum.AND,
              rules: [
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.PROCEDURE_CODE,
                    value: [values[1]],
                    operator: RuleOperatorEnum.EQUAL,
                    termsQueryType: TermsQueryType.TERMS_SET
                  }
                },
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.DIAGNOSES_INDICATION,
                    value: [values[2], values[3]],
                    operator: RuleOperatorEnum.NOT_EQUAL,
                    termsQueryType: TermsQueryType.TERMS
                  }
                },
                {
                  type: RuleTypeEnum.RULE_GROUP,
                  ruleGroup: {
                    combinator: RuleCombinatorEnum.OR,
                    rules: [
                      {
                        type: RuleTypeEnum.RULE,
                        rule: {
                          field: RuleFieldEnum.AGE,
                          value: [values[4], values[5]],
                          operator: RuleOperatorEnum.NOT_EQUAL,
                          termsQueryType: TermsQueryType.TERMS_SET
                        }
                      }
                    ]
                  }
                }
              ]
            }
          }
        ]
      }
    };

    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const queries = parserService.parseRulesToEsQueries({
      patientClaimsFilter: root
    });
    const expectedQueries: QueryDslQueryContainer[] = [
      {
        bool: {
          should: [
            {
              terms: {
                "patientClaims.diagnosisIcdCode": [values[0]]
              }
            },
            {
              bool: {
                must: [
                  {
                    terms: {
                      "patientClaims.procedureCode": [values[1]]
                    }
                  },
                  {
                    bool: {
                      must_not: [
                        {
                          terms: {
                            "patientClaims.diagnosisIndications": [
                              values[2],
                              values[3]
                            ]
                          }
                        }
                      ]
                    }
                  },
                  {
                    bool: {
                      should: [
                        {
                          bool: {
                            must_not: [
                              {
                                terms_set: {
                                  "patientClaims.age": {
                                    terms: [values[4], values[5]],
                                    minimum_should_match_script: {
                                      source: "2"
                                    }
                                  }
                                }
                              }
                            ]
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  }
                ]
              }
            }
          ],
          minimum_should_match: 1
        }
      }
    ];
    expect(queries).toEqual(expectedQueries);
  });
});

describe("extractClaimsAndProcedureCodesFromRules", () => {
  it("should extract diagnosis codes from single root level query", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [faker.datatype.string()];
    const root = {
      type: RuleTypeEnum.RULE,
      rule: {
        field: RuleFieldEnum.DIAGNOSES_CODE,
        value: [values[0]],
        operator: RuleOperatorEnum.EQUAL
      }
    };

    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const diagnosesCodes =
      parserService.extractClaimsAndProcedureCodesFromRules({
        patientClaimsFilter: root
      });
    const expectedDiagnosesCodes = {
      diagnosesCodes: [values[0]],
      proceduresCodes: [],
      ccsrDxDescriptions: [],
      ccsrPxDescriptions: []
    };
    expect(diagnosesCodes).toEqual(expectedDiagnosesCodes);
  });
  it("should extract procedure codes from single root level query", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [faker.datatype.string()];
    const root = {
      type: RuleTypeEnum.RULE,
      rule: {
        field: RuleFieldEnum.PROCEDURE_CODE,
        value: [values[0]],
        operator: RuleOperatorEnum.EQUAL
      }
    };

    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const proceduresCodes =
      parserService.extractClaimsAndProcedureCodesFromRules({
        patientClaimsFilter: root
      });
    const expectedProceduresCodes = {
      diagnosesCodes: [],
      proceduresCodes: [values[0]],
      ccsrDxDescriptions: [],
      ccsrPxDescriptions: []
    };
    expect(proceduresCodes).toEqual(expectedProceduresCodes);
  });

  it("should extract claim codes from nested queries of type (A AND B) OR C", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string()
    ];
    const root = {
      type: RuleTypeEnum.RULE_GROUP,
      ruleGroup: {
        combinator: RuleCombinatorEnum.OR,
        rules: [
          {
            type: RuleTypeEnum.RULE_GROUP,
            ruleGroup: {
              combinator: RuleCombinatorEnum.AND,
              rules: [
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.DIAGNOSES_CODE,
                    value: [values[0]],
                    operator: RuleOperatorEnum.EQUAL
                  }
                },
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.PROCEDURE_CODE,
                    value: [values[1]],
                    operator: RuleOperatorEnum.EQUAL
                  }
                }
              ]
            }
          },
          {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.CCSR,
              value: [values[2]],
              operator: RuleOperatorEnum.EQUAL
            }
          }
        ]
      }
    };
    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const claimCodes = parserService.extractClaimsAndProcedureCodesFromRules({
      patientClaimsFilter: root
    });
    const expectedClaimCodes = {
      diagnosesCodes: [values[0]],
      proceduresCodes: [values[1]],
      ccsrDxDescriptions: [values[2]],
      ccsrPxDescriptions: []
    };
    expect(claimCodes).toEqual(expectedClaimCodes);
  });

  it("should extract ccsrDx from single root level query", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [faker.datatype.string()];
    const root = {
      type: RuleTypeEnum.RULE,
      rule: {
        field: RuleFieldEnum.CCSR,
        value: [values[0]],
        operator: RuleOperatorEnum.EQUAL
      }
    };

    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const proceduresCodes =
      parserService.extractClaimsAndProcedureCodesFromRules({
        patientClaimsFilter: root
      });
    const expectedProceduresCodes = {
      diagnosesCodes: [],
      proceduresCodes: [],
      ccsrDxDescriptions: [values[0]],
      ccsrPxDescriptions: []
    };
    expect(proceduresCodes).toEqual(expectedProceduresCodes);
  });

  it("should extract ccsrPx from single root level query", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [faker.datatype.string()];
    const root = {
      type: RuleTypeEnum.RULE,
      rule: {
        field: RuleFieldEnum.CCSR_PX,
        value: [values[0]],
        operator: RuleOperatorEnum.EQUAL
      }
    };

    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const proceduresCodes =
      parserService.extractClaimsAndProcedureCodesFromRules({
        patientClaimsFilter: root
      });
    const expectedProceduresCodes = {
      diagnosesCodes: [],
      proceduresCodes: [],
      ccsrDxDescriptions: [],
      ccsrPxDescriptions: [values[0]]
    };
    expect(proceduresCodes).toEqual(expectedProceduresCodes);
  });

  it("should extract claim codes from nested queries of type (A AND B) OR C between ccsr and ccsr_px", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string()
    ];
    const root = {
      type: RuleTypeEnum.RULE_GROUP,
      ruleGroup: {
        combinator: RuleCombinatorEnum.OR,
        rules: [
          {
            type: RuleTypeEnum.RULE_GROUP,
            ruleGroup: {
              combinator: RuleCombinatorEnum.AND,
              rules: [
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.CCSR,
                    value: [values[0]],
                    operator: RuleOperatorEnum.EQUAL
                  }
                },
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.CCSR_PX,
                    value: [values[1]],
                    operator: RuleOperatorEnum.EQUAL
                  }
                }
              ]
            }
          },
          {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.CCSR,
              value: [values[2]],
              operator: RuleOperatorEnum.EQUAL
            }
          }
        ]
      }
    };
    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const claimCodes = parserService.extractClaimsAndProcedureCodesFromRules({
      patientClaimsFilter: root
    });
    const expectedClaimCodes = {
      diagnosesCodes: [],
      proceduresCodes: [],
      ccsrDxDescriptions: [values[0], values[2]],
      ccsrPxDescriptions: [values[1]]
    };
    expect(claimCodes).toEqual(expectedClaimCodes);
  });

  describe("negative tests", () => {
    it("should not extract diagnosis codes from exclusion query", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[1]],
                operator: RuleOperatorEnum.NOT_EQUAL
              }
            }
          ],
          combinator: RuleCombinatorEnum.AND
        }
      };

      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const diagnosesCodes =
        parserService.extractClaimsAndProcedureCodesFromRules({
          patientClaimsFilter: root
        });
      const expectedDiagnosesCodes = {
        diagnosesCodes: [values[0]],
        proceduresCodes: [],
        ccsrDxDescriptions: [],
        ccsrPxDescriptions: []
      };
      expect(diagnosesCodes).toEqual(expectedDiagnosesCodes);
    });
    it("should not extract procedure codes from exclusion query", () => {
      const rulesOptimizerService = createMockInstance(RulesOptimizerService);
      const parserService = new RulesParserService(rulesOptimizerService);
      const values = [faker.datatype.string()];
      const root = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [values[1]],
                operator: RuleOperatorEnum.NOT_EQUAL
              }
            }
          ],
          combinator: RuleCombinatorEnum.AND
        }
      };

      rulesOptimizerService.optimizeRules.mockReturnValue(root);
      const proceduresCodes =
        parserService.extractClaimsAndProcedureCodesFromRules({
          patientClaimsFilter: root
        });
      const expectedProceduresCodes = {
        proceduresCodes: [values[0]],
        diagnosesCodes: [],
        ccsrDxDescriptions: [],
        ccsrPxDescriptions: []
      };
      expect(proceduresCodes).toEqual(expectedProceduresCodes);
    });
  });
});

describe("extractGenericDrugNamesFromRules", () => {
  it("should return empty array when supplied filters is undefined", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    expect(parserService.extractGenericDrugNamesFromRules(undefined)).toEqual(
      []
    );
    expect(
      parserService.extractGenericDrugNamesFromRules({
        patientClaimsFilter: undefined,
        patientClaimsFilterV2: undefined
      })
    ).toEqual([]);
  });

  it("should extract generic drug names from single root level query", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [faker.datatype.string()];
    const root = {
      type: RuleTypeEnum.RULE,
      rule: {
        field: RuleFieldEnum.GENERIC_DRUG,
        value: [values[0]],
        operator: RuleOperatorEnum.EQUAL
      }
    };

    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const genericDrugNames = parserService.extractGenericDrugNamesFromRules({
      patientClaimsFilter: root
    });
    const expectedGenericDrugNames = [values[0]];
    expect(genericDrugNames).toEqual(expectedGenericDrugNames);
  });

  it("should extract generic drug names from nested queries of type (A AND B) OR C", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string()
    ];
    const root = {
      type: RuleTypeEnum.RULE_GROUP,
      ruleGroup: {
        combinator: RuleCombinatorEnum.OR,
        rules: [
          {
            type: RuleTypeEnum.RULE_GROUP,
            ruleGroup: {
              combinator: RuleCombinatorEnum.AND,
              rules: [
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.GENERIC_DRUG,
                    value: [values[0]],
                    operator: RuleOperatorEnum.EQUAL
                  }
                },
                {
                  type: RuleTypeEnum.RULE,
                  rule: {
                    field: RuleFieldEnum.GENERIC_DRUG,
                    value: [values[1]],
                    operator: RuleOperatorEnum.EQUAL
                  }
                }
              ]
            }
          },
          {
            type: RuleTypeEnum.RULE,
            rule: {
              field: RuleFieldEnum.GENERIC_DRUG,
              value: [values[2]],
              operator: RuleOperatorEnum.NOT_EQUAL
            }
          }
        ]
      }
    };
    rulesOptimizerService.optimizeRules.mockReturnValue(root);
    const genericDrugNames = parserService.extractGenericDrugNamesFromRules({
      patientClaimsFilter: root
    });
    const expectedGenericDrugNames = [values[0], values[1], values[2]];
    expect(genericDrugNames).toEqual(expectedGenericDrugNames);
  });
});

describe("patientClaimsFilterV2", () => {
  it("should parse when only inclusionCriteria is present", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [faker.datatype.string()];
    const root = {
      inclusionCriteria: {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.OR
              }
            }
          ],
          combinator: RuleCombinatorEnum.ALL
        }
      }
    };
    const queries = parserService.parseRulesToEsQueries({
      patientClaimsFilterV2: root
    });
    const expectedQueries: QueryDslQueryContainer[] = [
      {
        bool: {
          must: [
            {
              bool: {
                must: [
                  {
                    terms: {
                      "patientClaims.diagnosisIcdCode": [values[0]]
                    }
                  }
                ]
              }
            }
          ]
        }
      }
    ];
    expect(queries).toEqual(expectedQueries);
    expect(rulesOptimizerService.optimizeRules).not.toHaveBeenCalled();
  });

  it("should parse when only exclusionCriteria is present", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [faker.datatype.string()];
    const root = {
      exclusionCriteria: {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.OR
              }
            }
          ],
          combinator: RuleCombinatorEnum.ANY
        }
      }
    };
    const queries = parserService.parseRulesToEsQueries({
      patientClaimsFilterV2: root
    });
    const expectedQueries: QueryDslQueryContainer[] = [
      {
        bool: {
          must_not: [
            {
              bool: {
                should: [
                  {
                    terms: {
                      "patientClaims.diagnosisIcdCode": [values[0]]
                    }
                  }
                ],
                minimum_should_match: 1
              }
            }
          ]
        }
      }
    ];
    expect(queries).toEqual(expectedQueries);
    expect(rulesOptimizerService.optimizeRules).not.toHaveBeenCalled();
  });

  it("should parse when both inclusionCriteria and exclusionCriteria are present", () => {
    const rulesOptimizerService = createMockInstance(RulesOptimizerService);
    const parserService = new RulesParserService(rulesOptimizerService);
    const values = [
      faker.datatype.string(),
      faker.datatype.string(),
      faker.datatype.string()
    ];
    const root = {
      inclusionCriteria: {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0], values[1]],
                operator: RuleOperatorEnum.OR
              }
            }
          ],
          combinator: RuleCombinatorEnum.ALL
        }
      },
      exclusionCriteria: {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          rules: [
            {
              type: RuleTypeEnum.RULE_GROUP,
              ruleGroup: {
                rules: [
                  {
                    type: RuleTypeEnum.RULE,
                    rule: {
                      field: RuleFieldEnum.PROCEDURE_CODE,
                      value: [values[2]],
                      operator: RuleOperatorEnum.OR
                    }
                  },
                  {
                    type: RuleTypeEnum.RULE,
                    rule: {
                      field: RuleFieldEnum.AGE,
                      value: [PatientAgeBucketEnum.FROM_18_TO_24],
                      operator: RuleOperatorEnum.OR
                    }
                  }
                ],
                combinator: RuleCombinatorEnum.ALL
              }
            }
          ],
          combinator: RuleCombinatorEnum.ANY
        }
      }
    };
    const queries = parserService.parseRulesToEsQueries({
      patientClaimsFilterV2: root
    });
    const expectedQueries: QueryDslQueryContainer[] = [
      {
        bool: {
          must: [
            {
              bool: {
                must: [
                  {
                    terms: {
                      "patientClaims.diagnosisIcdCode": [values[0], values[1]]
                    }
                  }
                ]
              }
            }
          ],
          must_not: [
            {
              bool: {
                should: [
                  {
                    bool: {
                      must: [
                        {
                          terms: {
                            "patientClaims.procedureCode": [values[2]]
                          }
                        },
                        {
                          terms: {
                            "patientClaims.age": [
                              PatientAgeBucketEnum.FROM_18_TO_24
                            ]
                          }
                        }
                      ]
                    }
                  }
                ],
                minimum_should_match: 1
              }
            }
          ]
        }
      }
    ];
    expect(queries).toEqual(expectedQueries);
    expect(rulesOptimizerService.optimizeRules).not.toHaveBeenCalled();
  });
});
