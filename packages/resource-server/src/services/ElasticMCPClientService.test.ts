import { faker } from "@faker-js/faker";
import { createMockInstance } from "../util/TestUtils";
import { ElasticMCPClientService } from "./ElasticMCPClientService";
import { ConfigService } from "./ConfigService";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { AzureOpenAI } from "openai";
import { createLogger } from "../lib/Logger";

jest.mock("@modelcontextprotocol/sdk/client/index.js");
jest.mock("@modelcontextprotocol/sdk/client/stdio.js");
jest.mock("openai");
jest.mock("../lib/Logger", () => ({
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  })
}));

describe("ElasticMCPClientService", () => {
  let elasticMCPClientService: ElasticMCPClientService;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockClient: jest.Mocked<Client>;
  let mockTransport: jest.Mocked<StdioClientTransport>;
  let mockAzureOpenAI: jest.Mocked<AzureOpenAI>;
  let mockLogger: any;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn()
    };
    (createLogger as jest.Mock).mockReturnValue(mockLogger);

    mockConfigService = createMockInstance(ConfigService);
    mockConfigService.azureOpenAiEndpoint = faker.internet.url();
    mockConfigService.azureOpenAiApiKey = faker.datatype.uuid();
    mockConfigService.azureOpenAiDeploymentName = "gpt-4";
    mockConfigService.azureOpenAiApiVersion = "2023-05-15";

    mockClient = {
      connect: jest.fn().mockResolvedValue(undefined),
      listTools: jest.fn().mockResolvedValue({ tools: [] }),
      callTool: jest.fn().mockResolvedValue({ content: "tool result" }),
      close: jest.fn().mockResolvedValue(undefined)
    } as unknown as jest.Mocked<Client>;

    (Client as unknown as jest.Mock).mockImplementation(() => mockClient);

    mockTransport = {
      close: jest.fn()
    } as unknown as jest.Mocked<StdioClientTransport>;
    (StdioClientTransport as unknown as jest.Mock).mockImplementation(
      () => mockTransport
    );

    mockAzureOpenAI = {
      chat: {
        completions: {
          create: jest.fn()
        }
      }
    } as unknown as jest.Mocked<AzureOpenAI>;
    (AzureOpenAI as unknown as jest.Mock).mockImplementation(
      () => mockAzureOpenAI
    );

    elasticMCPClientService = new ElasticMCPClientService(mockConfigService);
  });

  describe("constructor", () => {
    it("should initialize with correct configuration", () => {
      expect(Client).toHaveBeenCalledWith({
        name: expect.any(String),
        version: expect.any(String)
      });

      expect(AzureOpenAI).toHaveBeenCalledWith({
        endpoint: mockConfigService.azureOpenAiEndpoint,
        apiKey: mockConfigService.azureOpenAiApiKey,
        deployment: mockConfigService.azureOpenAiDeploymentName,
        apiVersion: mockConfigService.azureOpenAiApiVersion
      });
    });
  });

  describe("getClient", () => {
    it("should create a StdioClientTransport with correct parameters", async () => {
      await elasticMCPClientService.getClient();

      expect(StdioClientTransport).toHaveBeenCalledWith({
        command: "ts-node",
        args: ["--transpile-only", "src/mcp.ts"],
        env: expect.any(Object)
      });
    });

    it("should connect the client with the transport", async () => {
      await elasticMCPClientService.getClient();

      expect(mockClient.connect).toHaveBeenCalledWith(mockTransport);
    });

    it("should return the connected client", async () => {
      const result = await elasticMCPClientService.getClient();

      expect(result).toBe(mockClient);
    });
  });

  describe("queryChatCompletion", () => {
    const mockQuery = "What is the profile of person X?";
    const mockPersonId = "12345";

    beforeEach(() => {
      mockClient.listTools.mockResolvedValue({
        tools: [
          {
            name: "search_elasticsearch",
            description: "Search Elasticsearch",
            inputSchema: { type: "object", properties: {} }
          }
        ]
      });

      // Mock the chat completion response
      const mockChatResponse = {
        id: faker.datatype.uuid(),
        choices: [
          {
            message: {
              role: "assistant",
              content: null,
              tool_calls: [
                {
                  id: faker.datatype.uuid(),
                  type: "function",
                  function: {
                    name: "search_elasticsearch",
                    arguments: JSON.stringify({
                      index: "people",
                      query: "test"
                    })
                  }
                }
              ]
            },
            finish_reason: "tool_calls"
          }
        ]
      };
      (
        mockAzureOpenAI.chat.completions.create as jest.Mock
      ).mockResolvedValueOnce(mockChatResponse);

      // Mock the final response without tool calls
      const mockFinalResponse = {
        id: faker.datatype.uuid(),
        choices: [
          {
            message: {
              role: "assistant",
              content: "Here is information about the person.",
              tool_calls: null
            },
            finish_reason: "stop"
          }
        ]
      };
      (
        mockAzureOpenAI.chat.completions.create as jest.Mock
      ).mockResolvedValueOnce(mockFinalResponse);
    });

    it("should initialize the OpenAIChatAdaptor with the client", async () => {
      await elasticMCPClientService.queryChatCompletion(
        mockQuery,
        mockPersonId
      );

      expect(mockClient.listTools).toHaveBeenCalled();
    });

    it("should call the LLM with correct parameters", async () => {
      await elasticMCPClientService.queryChatCompletion(
        mockQuery,
        mockPersonId
      );

      expect(mockAzureOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: mockConfigService.azureOpenAiDeploymentName,
        messages: expect.arrayContaining([
          expect.objectContaining({
            role: "system",
            content: expect.stringContaining("You are a helpful assistant")
          }),
          expect.objectContaining({
            role: "user",
            content: expect.stringContaining(mockQuery)
          })
        ]),
        tools: expect.any(Array),
        max_tokens: 800,
        parallel_tool_calls: false
      });
    });

    it("should include personId in the user message", async () => {
      await elasticMCPClientService.queryChatCompletion(
        mockQuery,
        mockPersonId
      );

      expect(mockAzureOpenAI.chat.completions.create).toHaveBeenCalled();

      // Get all calls to the mock
      const calls = (mockAzureOpenAI.chat.completions.create as jest.Mock).mock
        .calls;

      // Verify at least one call was made
      expect(calls.length).toBeGreaterThan(0);

      // Get the first call's parameters
      const firstCallParams = calls[0][0];

      // Find the user message
      const userMessage = firstCallParams.messages.find(
        (m: any) => m.role === "user"
      );

      // Verify the user message contains the personId
      expect(userMessage.content).toContain(`PersonId: <${mockPersonId}>`);
    });

    it("should call tools when the LLM requests them", async () => {
      await elasticMCPClientService.queryChatCompletion(
        mockQuery,
        mockPersonId
      );

      expect(mockClient.callTool).toHaveBeenCalledWith({
        name: "search_elasticsearch",
        arguments: { index: "people", query: "test" }
      });
    });

    it("should continue the conversation with tool results", async () => {
      await elasticMCPClientService.queryChatCompletion(
        mockQuery,
        mockPersonId
      );

      // Check that the second call to the LLM includes the tool results
      const secondCallMessages = (
        mockAzureOpenAI.chat.completions.create as jest.Mock
      ).mock.calls[1][0].messages;

      expect(secondCallMessages).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            role: "tool",
            content: "tool result"
          })
        ])
      );
    });

    it("should return the final assistant message", async () => {
      const result = await elasticMCPClientService.queryChatCompletion(
        mockQuery,
        mockPersonId
      );

      expect(result).toEqual(
        expect.objectContaining({
          role: "assistant",
          content: "Here is information about the person."
        })
      );
    });

    it("should handle errors during tool calls", async () => {
      // Make the tool call throw an error
      mockClient.callTool.mockRejectedValueOnce(new Error("Tool error"));

      await expect(
        elasticMCPClientService.queryChatCompletion(mockQuery, mockPersonId)
      ).rejects.toThrow("Tool error");
    });

    it("should handle multiple tool calls in one response", async () => {
      // Mock a response with multiple tool calls
      const mockMultiToolResponse = {
        id: faker.datatype.uuid(),
        choices: [
          {
            message: {
              role: "assistant",
              content: null,
              tool_calls: [
                {
                  id: "tool1",
                  type: "function",
                  function: {
                    name: "search_elasticsearch",
                    arguments: JSON.stringify({
                      index: "people",
                      query: "test1"
                    })
                  }
                },
                {
                  id: "tool2",
                  type: "function",
                  function: {
                    name: "get_mappings",
                    arguments: JSON.stringify({ index: "people" })
                  }
                }
              ]
            },
            finish_reason: "tool_calls"
          }
        ]
      };

      (mockAzureOpenAI.chat.completions.create as jest.Mock).mockReset();
      (
        mockAzureOpenAI.chat.completions.create as jest.Mock
      ).mockResolvedValueOnce(mockMultiToolResponse);
      (
        mockAzureOpenAI.chat.completions.create as jest.Mock
      ).mockResolvedValueOnce({
        id: faker.datatype.uuid(),
        choices: [
          {
            message: {
              role: "assistant",
              content: "Final response",
              tool_calls: null
            },
            finish_reason: "stop"
          }
        ]
      });

      // Mock tool results
      mockClient.callTool
        .mockResolvedValueOnce({ content: "tool1 result" })
        .mockResolvedValueOnce({ content: "tool2 result" });

      await elasticMCPClientService.queryChatCompletion(
        mockQuery,
        mockPersonId
      );

      expect(mockClient.callTool).toHaveBeenCalledTimes(2);
      expect(mockClient.callTool).toHaveBeenCalledWith({
        name: "search_elasticsearch",
        arguments: { index: "people", query: "test1" }
      });
      expect(mockClient.callTool).toHaveBeenCalledWith({
        name: "get_mappings",
        arguments: { index: "people" }
      });
    });
  });
});
