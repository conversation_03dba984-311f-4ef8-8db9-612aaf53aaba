import { Service } from "typedi";
import _ from "lodash";
import { EntityTagResourceClient } from "@h1nyc/account-sdk";
import { TagAssignment, EntityType } from "@h1nyc/account-user-entities";
import Redis from "ioredis";
import { CTMSResourceClient } from "@h1nyc/search-sdk";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { Logger } from "pino";
import { sha1 } from "object-hash";
import { createLogger } from "../lib/Logger";

const SECONDS = "ex";
const EXPIRATION_PERIOD = 300;
@Service()
export class TagsHelperService {
  private logger: Logger;
  private redisClient;
  constructor(
    config: ConfigService,
    private entityTagResourceClient: EntityTagResourceClient,
    private ctmsResourceClient: CTMSResourceClient
  ) {
    this.redisClient = new Redis(config.searchCacheRedisOptions);
    this.logger = createLogger(this);
  }

  @Trace("h1-search.SearchHelperService.getEntitiesInProjectsByTags")
  public async getEntitiesInProjectsByTags(
    projectId: string,
    tagIds: string[]
  ): Promise<
    {
      entityId: string;
      entityType: EntityType;
      tagId: string;
    }[]
  > {
    if (!tagIds.length) {
      return [];
    }

    try {
      const entities = await this.getFromCache(projectId, tagIds);
      if (entities) return entities;
    } catch (err) {
      this.logger.error(err, "Error connecting redis for tags");
    }

    const tagAssignments: TagAssignment[] =
      await this.entityTagResourceClient.getTagAssignmentsInProjectByTags({
        projectId,
        tagIds
      });

    const entities = tagAssignments.map((assignment) => {
      let entityId = "";
      if (assignment.personId) {
        entityId = assignment.personId;
      } else if (assignment.entityId) {
        entityId = assignment.entityId;
      }

      let entityType = EntityType.PERSON;

      if (!assignment.personId && assignment.entityType) {
        entityType = assignment.entityType;
      }
      const tagId = assignment.tagId;
      return {
        entityId,
        entityType,
        tagId
      };
    });

    if (tagIds.length > 1) {
      try {
        await this.addToCache(projectId, tagIds, entities);
      } catch (err) {
        this.logger.error(err, "Error connecting redis for tags");
      }
    }

    return entities;
  }

  /**
   * Gets all person ids from CTMS index
   * Remove this when data is available on person's Index

   */
  @Trace("h1-search.SearchHelperService.getCTMSPersonIdsForSearch")
  async getCTMSPersonIdsForSearch(): Promise<string[] | null> {
    return this.ctmsResourceClient.getAllCTMSPersonIds();
  }

  private async getFromCache(
    projectId: string,
    tagIds: string[]
  ): Promise<
    { entityId: string; entityType: EntityType; tagId: string }[] | null
  > {
    const key = sha1({ projectId, tagIds });
    const raw = await this.redisClient.get(key);
    if (raw) {
      const entities = JSON.parse(raw) as {
        entityId: string;
        entityType: EntityType;
        tagId: string;
      }[];
      this.logger.debug(entities, "tags cache hit");
      return entities;
    }
    this.logger.debug(null, "tags cache miss");
    return null;
  }

  private async addToCache(
    projectId: string,
    tagIds: string[],
    results: Readonly<
      { entityId: string; entityType: EntityType; tagId: string }[]
    >
  ) {
    const key = sha1({ projectId, tagIds });
    return this.redisClient.set(
      key,
      JSON.stringify(results),
      SECONDS,
      EXPIRATION_PERIOD
    );
  }
}
