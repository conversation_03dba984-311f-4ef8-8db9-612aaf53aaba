import { createMockInstance } from "../util/TestUtils";
import { EntityTagResourceClient } from "@h1nyc/account-sdk";
import { CTMSResourceClient } from "@h1nyc/search-sdk";
import { TagsHelperService } from "./TagsHelperService";
import { faker } from "@faker-js/faker";
import { ConfigService } from "./ConfigService";

jest.mock("ioredis");
jest.mock("object-hash");

beforeEach(() => {
  jest.resetAllMocks();
});

describe("TagsHelperService", () => {
  describe("negative tests", () => {
    it("should re-throw an error thrown by entityTagResourceClient.getTagAssignmentsInProjectByTags", async () => {
      const err = new Error(
        "entityTagResourceClient.getPeopleIdsWithPublicOrPrivateTags threw an error for some reason"
      );

      const entityTagResourceClient = createMockInstance(
        EntityTagResourceClient
      );

      entityTagResourceClient.getTagAssignmentsInProjectByTags.mockRejectedValue(
        err
      );
      const ctmsResourceClient = createMockInstance(CTMSResourceClient);
      const configService = createMockInstance(ConfigService);
      const tagsHelperService = new TagsHelperService(
        configService,
        entityTagResourceClient,

        ctmsResourceClient
      );

      const tagIds: Array<string> = [
        faker.datatype.uuid(),
        faker.datatype.uuid()
      ];

      const projectId = faker.datatype.string();

      try {
        await tagsHelperService.getEntitiesInProjectsByTags(projectId, tagIds);
        fail("an error should have been thrown");
      } catch (thrownErr) {
        expect(thrownErr).toEqual(err);
      }
    });
  });

  describe("getEntitiesInProjectsByTags", () => {
    it("should return null when entityTagResourceClient returns an empty list for requested tagIds", async () => {
      const entityTagResourceClient = createMockInstance(
        EntityTagResourceClient
      );

      entityTagResourceClient.getTagAssignmentsInProjectByTags.mockResolvedValue(
        []
      );
      const ctmsResourceClient = createMockInstance(CTMSResourceClient);
      const configService = createMockInstance(ConfigService);
      const tagsHelperService = new TagsHelperService(
        configService,
        entityTagResourceClient,

        ctmsResourceClient
      );

      const tagIds: Array<string> = [
        faker.datatype.uuid(),
        faker.datatype.uuid()
      ];

      const projectId = faker.datatype.string();

      const ids = await tagsHelperService.getEntitiesInProjectsByTags(
        projectId,
        tagIds
      );

      expect(ids).toEqual([]);
      expect(
        entityTagResourceClient.getTagAssignmentsInProjectByTags
      ).toHaveBeenCalledWith({ projectId, tagIds });
    });
  });
});
