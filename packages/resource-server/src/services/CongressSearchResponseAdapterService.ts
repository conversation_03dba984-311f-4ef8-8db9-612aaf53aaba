/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { Service } from "typedi";
import { estypes } from "@elastic/elasticsearch";
import {
  AggregationsAggregate,
  AggregationsTermsAggregateBase,
  SearchResponse,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import {
  CongressSearchFilterAutocompleteTypes,
  ElasticsearchCongressDoc,
  queryableAddressFields,
  queryableRegionFields
} from "./CongressSearchResourceService";
import {
  CongressSearchResult,
  CongressSearchInput,
  CongressSearchResponse,
  CongressSearchLocationFilterAggregation,
  CongressSearchFilterAggregation
} from "@h1nyc/search-sdk";
import _ from "lodash";
import {
  REGION_TO_STATES,
  VALID_STATE_CODES
} from "./KeywordAutocompleteResponseAdapterService";
import {
  REGION_TO_STATE_CODES,
  VALID_STATE_NAMES
} from "./InstitutionsResourceService";
import { LocationLabelFormatterService } from "./LocationLabelFormatterService";
import { SessionsSearchFilterAutocompleteTypes } from "./CongressEventPageSearchResourceService";

export interface DocCountBucket {
  key: string;
  doc_count: number;
}

export interface LocationRegionsInDocCountBucket extends DocCountBucket {
  regions_in: AggregationsTermsAggregateBase<DocCountBucket>;
}

export interface RegionsDocCountBucket extends DocCountBucket {
  locations_in_region: AggregationsTermsAggregateBase<DocCountBucket>;
}

export interface LocationFilteredMatchingAggregation {
  filtered_matching: AggregationsTermsAggregateBase<LocationRegionsInDocCountBucket>;
}

export interface FilteredMatchingAggregation {
  filtered_matching: AggregationsTermsAggregateBase<DocCountBucket>;
}

@Service()
export class CongressSearchResponseAdapterService {
  constructor(
    private locationLabelFormatterService: LocationLabelFormatterService
  ) {}

  public adaptToCongressSearchResponse(
    input: Readonly<CongressSearchInput>,
    data: Readonly<estypes.SearchResponse<ElasticsearchCongressDoc>>,
    followedCongressSeriesIds: Set<string>
  ): Readonly<CongressSearchResponse> {
    if (!data.hits.total) {
      return this.buildEmptyCongressSearchResponse();
    }

    return {
      total: this.extractTotalValue(data.hits.total),
      congresses: this.buildCongressResultFromElasticsearchHit(
        input,
        data,
        followedCongressSeriesIds
      )
    };
  }

  public adaptToCongressSearchFilterAggregations(
    data: SearchResponse<never, Record<string, AggregationsAggregate>>,
    filterType:
      | CongressSearchFilterAutocompleteTypes
      | SessionsSearchFilterAutocompleteTypes,
    addressField?: keyof typeof queryableAddressFields,
    regionField?: keyof typeof queryableRegionFields,
    usersPreferredLanguage?: string
  ): CongressSearchFilterAggregation[] {
    if (addressField) {
      return this.adaptToCongressSearchLocationFilterAggregations(
        data,
        addressField,
        regionField,
        usersPreferredLanguage
      );
    }
    const buckets = (
      data.aggregations![filterType] as FilteredMatchingAggregation
    )?.filtered_matching;
    return this.docCountBucketsToAggregations(buckets);
  }

  private adaptToCongressSearchLocationFilterAggregations(
    data: SearchResponse<never, Record<string, AggregationsAggregate>>,
    field: keyof typeof queryableAddressFields,
    regionField?: keyof typeof queryableRegionFields,
    usersPreferredLanguage?: string
  ) {
    if (field === "postal_code") {
      const postalCodeAggregationBuckets = data.aggregations![
        field
      ] as LocationFilteredMatchingAggregation;

      return this.locationRegionsInDocCountBucketsToAggregations(
        postalCodeAggregationBuckets?.filtered_matching,
        field,
        usersPreferredLanguage
      );
    }

    const locationAggregation = data.aggregations![
      field
    ] as LocationFilteredMatchingAggregation;

    let regionBuckets;
    if (regionField) {
      regionBuckets = data.aggregations![
        regionField
      ] as AggregationsTermsAggregateBase<RegionsDocCountBucket>;
    }

    return [
      ...this.locationRegionsInDocCountBucketsToAggregations(
        locationAggregation?.filtered_matching,
        field
      ),
      ...this.regionDocCountBucketsToAggregations(
        regionBuckets,
        field,
        usersPreferredLanguage
      )
    ];
  }

  private locationRegionsInDocCountBucketsToAggregations(
    agg:
      | AggregationsTermsAggregateBase<LocationRegionsInDocCountBucket>
      | undefined,
    field: keyof typeof queryableAddressFields,
    usersPreferredLanguage?: string
  ): CongressSearchLocationFilterAggregation[] {
    if (!agg || !agg.buckets) {
      return [];
    }

    return (agg.buckets as LocationRegionsInDocCountBucket[]).map((bucket) => {
      return {
        id: bucket.key,
        label: this.formatLabel(field, bucket.key, usersPreferredLanguage),
        count: bucket.doc_count,
        regionsIn: this.formatRegionsIn(bucket, field)
      };
    });
  }

  private formatLabel(
    field: keyof typeof queryableAddressFields | undefined,
    value: string,
    usersPreferredLanguage?: string
  ) {
    switch (field) {
      case "city":
        return this.locationLabelFormatterService.cityWithoutCounty(value);
      case "region":
        return this.locationLabelFormatterService.region(value);
      case "country":
        return this.locationLabelFormatterService.country(
          value,
          usersPreferredLanguage
        );
      case "postal_code":
        return this.formatPostalCodeLabel(value);
      default:
        return value;
    }
  }

  private formatPostalCodeLabel(value: string) {
    const fields = value.split("|", 4);

    return fields[3] || value;
  }

  // Temp fix for data issue with incorrect regions
  private formatRegionsIn(
    bucket: LocationRegionsInDocCountBucket,
    field: keyof typeof queryableAddressFields
  ) {
    if (field === "country") {
      return _.map(bucket.regions_in.buckets, "key");
    }

    if (bucket.key.includes("|")) {
      const locationTokens = bucket.key.split("|");
      const countryCode = locationTokens[0];
      const regionCode = locationTokens[1];
      if (
        countryCode.toLowerCase() !== "us" ||
        !VALID_STATE_CODES.has(regionCode.toUpperCase())
      ) {
        return [];
      }
    } else if (!VALID_STATE_NAMES.has(bucket.key)) {
      return [];
    }

    return _.map(bucket.regions_in.buckets, "key");
  }

  private regionDocCountBucketsToAggregations(
    agg: AggregationsTermsAggregateBase<RegionsDocCountBucket> | undefined,
    field: keyof typeof queryableAddressFields,
    usersPreferredLanguage?: string
  ): CongressSearchLocationFilterAggregation[] {
    if (!agg || !agg.buckets) {
      return [];
    }

    return (agg.buckets as RegionsDocCountBucket[]).map((bucket) => {
      return {
        id: bucket.key,
        label: bucket.key,
        count: bucket.doc_count,
        locationsInRegion: this.formatLocationsInRegion(
          bucket,
          field,
          usersPreferredLanguage
        )
      };
    });
  }

  private formatLocationsInRegion(
    bucket: RegionsDocCountBucket,
    field: keyof typeof queryableAddressFields,
    usersPreferredLanguage?: string
  ) {
    const validLocationsInRegion = REGION_TO_STATES.get(bucket.key);
    const validLocationsInRegionCodes = REGION_TO_STATE_CODES.get(bucket.key);
    const locationsInRegion = this.docCountBucketsToAggregations(
      bucket.locations_in_region as AggregationsTermsAggregateBase<DocCountBucket>,
      field,
      usersPreferredLanguage
    );

    if (!locationsInRegion) {
      return undefined;
    }

    // Temp fix for data issue with duplicate locations
    const locationsInRegionMap = new Map<
      string,
      CongressSearchFilterAggregation
    >();
    locationsInRegion.forEach((bucket) => {
      const existingBucket = locationsInRegionMap.get(bucket.id);
      if (existingBucket) {
        existingBucket.count += bucket.count;
      } else {
        locationsInRegionMap.set(bucket.id, bucket);
      }
    });

    const locationBuckets = Array.from(locationsInRegionMap.values());

    if (validLocationsInRegion && validLocationsInRegionCodes) {
      return locationBuckets.filter((bucket) => {
        if (bucket.id.includes("|")) {
          const stateCode = bucket.id.split("|")[1];
          return validLocationsInRegionCodes.includes(stateCode);
        }

        return validLocationsInRegion.includes(bucket.id);
      });
    }

    return locationBuckets;
  }

  private docCountBucketsToAggregations(
    agg: AggregationsTermsAggregateBase<DocCountBucket> | undefined,
    field?: keyof typeof queryableAddressFields,
    usersPreferredLanguage?: string
  ): CongressSearchFilterAggregation[] {
    if (!agg || !agg.buckets) {
      return [];
    }

    return (agg.buckets as DocCountBucket[]).map((bucket) => {
      return {
        id: bucket.key,
        label: this.formatLabel(field, bucket.key, usersPreferredLanguage),
        count: bucket.doc_count
      };
    });
  }

  private buildCongressResultFromElasticsearchHit(
    input: Readonly<CongressSearchInput>,
    data: Readonly<estypes.SearchResponse<ElasticsearchCongressDoc>>,
    followedCongressSeriesIds: Set<string>
  ): CongressSearchResult[] {
    const congresses = data.hits.hits
      .map((hit) => this.esHitToCongress(hit, followedCongressSeriesIds))
      .filter(this.isDefined);

    return congresses;
  }

  private esHitToCongress(
    hit: estypes.SearchHit<ElasticsearchCongressDoc>,
    followedCongressSeriesIds: Set<string>
  ): CongressSearchResult | undefined {
    const _source = hit._source;

    if (!_source) {
      return undefined;
    }

    return {
      id: _source.h1_conference_id,
      seriesId: _source.h1_series_id,
      name: _source.name,
      seriesName: _source.series_name,
      type: _source["filters.congress_type"],
      addresses: _.compact(
        _source.addresses?.map(addressNestedDocToAddress) ?? []
      ),
      speakers: _source.speakers?.map(speakerNestedDocToSpeaker) ?? [],
      translations:
        _source.translations?.map(translationNestedDocToTranslation) ?? [],
      isFollowing: followedCongressSeriesIds.has(_source.h1_series_id),
      startDate: _source["filters.start_date"],
      endDate: _source["filters.end_date"],
      society: _source.society
    };
  }

  private buildEmptyCongressSearchResponse(): CongressSearchResponse {
    return {
      total: 0,
      congresses: []
    };
  }

  private isDefined(congress: unknown): congress is CongressSearchResult {
    return Boolean(congress);
  }

  private extractTotalValue(total: number | SearchTotalHits | undefined) {
    if (total === undefined) {
      return 0;
    }

    if (typeof total === "number") {
      return total;
    }

    return total.value;
  }
}

function addressNestedDocToAddress(
  address: ElasticsearchCongressDoc["addresses"][number]
) {
  if (isNonEmptyAddress(address)) {
    return {
      street1: address.street1,
      street2: address.street2,
      street3: address.street3,
      city: address.city,
      region: address.region,
      regionCode: address.region_code,
      country: address.country,
      postalCode: address.postal_code,
      county: address.county,
      district: address.district,
      languageCode: address.language_code
    };
  }

  return null;
}

function isNonEmptyAddress(
  address: ElasticsearchCongressDoc["addresses"][number]
) {
  const fields = [
    address.street1,
    address.street2,
    address.street3,
    address.city,
    address.region,
    address.region_code,
    address.country,
    address.postal_code,
    address.county,
    address.district
  ];

  return fields.some((field) => field?.trim().length);
}

function speakerNestedDocToSpeaker(
  speaker: NonNullable<ElasticsearchCongressDoc["speakers"]>[number]
) {
  return {
    id: speaker.h1_person_id,
    name: speaker.name,
    role: speaker.role
  };
}

function translationNestedDocToTranslation(
  translation: ElasticsearchCongressDoc["translations"][number]
) {
  return {
    name: translation.name,
    description: translation.description,
    society: translation.society,
    languageCode: translation.language_code
  };
}
