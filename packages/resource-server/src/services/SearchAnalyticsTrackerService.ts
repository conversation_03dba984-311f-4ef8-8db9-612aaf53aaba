import {
  AnalyticsResourceClient,
  analyticsTrackArgs
} from "@h1nyc/account-sdk";
import { Service } from "typedi";
import { createLogger, Logger } from "../lib/Logger";
import { Trace } from "../Tracer";

@Service()
export class SearchAnalyticsTracerService {
  private logger: Logger;

  constructor(private analyticsClient: AnalyticsResourceClient) {
    this.logger = createLogger("SearchAnalyticsTracerService", "info");
    this.logger.debug("Instantiated SearchAnalyticsTracerService");
  }

  @Trace("h1-search.sendAnalyticsEvent")
  public async sendAnalyticsEvent(args: analyticsTrackArgs) {
    try {
      await this.analyticsClient.analyticsTrack(args);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          error,
          `Unable to track event with args: ${JSON.stringify(args)}`
        );
      }
    }
  }
}
