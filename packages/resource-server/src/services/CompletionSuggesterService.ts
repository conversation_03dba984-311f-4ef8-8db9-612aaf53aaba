import { Service } from "typedi";
import { ConfigService } from "./ConfigService";
import { createLogger } from "../lib/Logger";
import { ElasticSearchCompletionService } from "./ElasticSearchCompletionService";
import { Trace } from "../Tracer";
import { estypes } from "@elastic/elasticsearch";
import { SearchCompletionSuggestOption } from "@elastic/elasticsearch/lib/api/types";

@Service()
export class CompletionSuggesterService {
  private readonly logger = createLogger(this);
  private completionSuggesterIndex: string;

  constructor(
    config: ConfigService,
    private elasticCompletionService: ElasticSearchCompletionService
  ) {
    this.completionSuggesterIndex = config.elasticCompletionSuggesterIndex;
  }

  private isValidInput(query: string): boolean {
    if (query) {
      return true;
    }

    return false;
  }

  private buildCompletionSuggestionRequest(
    query: string
  ): estypes.SearchRequest {
    return {
      index: this.completionSuggesterIndex,
      _source: false,
      suggest: {
        autocomplete: {
          prefix: query,
          completion: {
            field: "suggest",
            skip_duplicates: true,
            size: 3
          }
        }
      }
    };
  }

  @Trace("h1-search.search.completion-suggestions")
  async getCompletionSuggestions(query: string): Promise<string[]> {
    let suggestions: string[] = [];
    const formattedQuery = query
      .replace(/[^\p{L}0-9 '‘’]/gu, " ") // Replace characters that are not any language char or number or single quote with whitespace
      .replace(/['‘’]/g, "")
      .replace(/ +/g, " ")
      .trimStart();
    if (this.isValidInput(formattedQuery)) {
      const request = this.buildCompletionSuggestionRequest(formattedQuery);
      const { took, suggest } = await this.elasticCompletionService.query(
        request
      );

      const suggestData = suggest?.["autocomplete"]?.[0];

      if (
        suggestData &&
        (suggestData?.options as SearchCompletionSuggestOption[])?.length
      ) {
        suggestions = (suggestData.options as SearchCompletionSuggestOption[])
          .filter((option) => option.text != query)
          .map((option) => option.text);
      }

      this.logger.info(
        { took, query, formattedQuery, suggestions },
        "completion suggester info"
      );
    }
    return suggestions;
  }
}
