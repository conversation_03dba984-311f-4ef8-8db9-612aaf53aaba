import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { QueryParserService } from "./QueryParserService";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { TwitterQueryParserService } from "./TwitterQueryParserService";

describe("additional tests", () => {
  const twitterQueryParserService = new TwitterQueryParserService();

  describe.skip("not working", () => {
    it("a AND", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: the word "AND" is dropped
      expect(parserService.parseQuery("a AND")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "AND"]]
        ]
      ]);
    });

    it("a-b AND c-d", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: the hyphenated words should be retained
      expect(parserService.parseQuery("a-b AND c-d")).toEqual([
        "And",
        [
          ["Including", ["Text", "a-b"]],
          ["Including", ["Text", "c-d"]]
        ]
      ]);
    });

    it("a-b AND (c-d)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: the hyphenated words should be retained
      expect(parserService.parseQuery("a-b AND (c-d)")).toEqual([
        "And",
        [
          ["Including", ["Text", "a-b"]],
          ["Including", ["Group", ["And", [["Including", ["Text", "c-d"]]]]]]
        ]
      ]);
    });

    it("a-b AND c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: the hyphenated word should be retained
      expect(parserService.parseQuery("a-b AND c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a-b"]],
          ["Including", ["Text", "c"]]
        ]
      ]);
    });

    it("a,b,c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: tokens are "a" and ",b,c"
      expect(parserService.parseQuery("a,b,c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", ",b,c"]]
        ]
      ]);
    });

    it("a, b, c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: parses as 5 separate 1-character tokens
      expect(parserService.parseQuery("a, b, c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "b"]],
          ["Including", ["Text", "c"]]
        ]
      ]);
    });

    it("a,b, c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: splits into 3 tokens: "a", ",b,", and "c"
      expect(parserService.parseQuery("a,b, c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "b"]],
          ["Including", ["Text", "c"]]
        ]
      ]);
    });

    it("ハッピー.b AND bc", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: a.b is split into 2 separate terms
      expect(parserService.parseQuery("ハッピー.b AND bc")).toEqual([
        "And",
        [
          ["Including", ["Text", "ハッピー"]],
          ["Including", ["Text", ".b"]],
          ["Including", ["Text", "bc"]]
        ]
      ]);
    });

    it("悲しいNOT OR b", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: interaction with the Japanese characters drops the not entirely
      expect(parserService.parseQuery("悲しいNOT OR b")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "悲しいNOT"]],
                ["Including", ["Text", "伤心"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("a AND (b NOT 伤心,悲しい)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: 伤心,悲しい should be interpreted as an AND, comma isn't recognized at al
      expect(parserService.parseQuery("a AND (b NOT 伤心,悲しい)")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  ["Including", ["Text", "b"]],
                  ["Excluding", ["Text", "伤心"]],
                  ["Including", ["Text", ",悲しい"]]
                ]
              ]
            ]
          ]
        ]
      ]);
    });

    it("double quotes", () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: "AND" is dropped even though it's in double quotes
      expect(parserService.parseQuery('"a AND b"')).toEqual([
        "And",
        [["Including", ["Exactly", "a   b"]]]
      ]);
    });

    it("a-NOT-b", () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: parses as "a---b"
      expect(parserService.parseQuery("a-NOT-b")).toEqual([
        "And",
        [["Including", ["Exactly", "a-NOT-b"]]]
      ]);
    });

    it("a-AND-b", () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: parses as "a--b"
      expect(parserService.parseQuery("a-AND-b")).toEqual([
        "And",
        [["Including", ["Exactly", "a-AND-b"]]]
      ]);
    });

    it("a-OR-b", () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a-OR-b")).toEqual([
        "And",
        [["Including", ["Exactly", "a-OR-b"]]]
      ]);
    });
  });

  describe("working as expected", () => {
    it("a b c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a b c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "b"]],
          ["Including", ["Text", "c"]]
        ]
      ]);
    });

    it("a AND b", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND b")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "b"]]
        ]
      ]);
    });

    it("a OR b", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a OR b")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "a"]],
                ["Including", ["Text", "b"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("a NOT b", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a NOT b")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Excluding", ["Text", "b"]]
        ]
      ]);
    });

    it("a AND b AND c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND b AND c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "b"]],
          ["Including", ["Text", "c"]]
        ]
      ]);
    });

    it("a AND b OR c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND b OR c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "b"]],
                ["Including", ["Text", "c"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("a AND b OR c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND b OR c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "b"]],
                ["Including", ["Text", "c"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("a AND (b)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND (b)")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Group", ["And", [["Including", ["Text", "b"]]]]]]
        ]
      ]);
    });

    it("a AND (b OR c)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND (b OR c)")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  [
                    "Including",
                    [
                      "Or",
                      [
                        ["Including", ["Text", "b"]],
                        ["Including", ["Text", "c"]]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]);
    });

    it("a AND (b OR (c))", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND (b OR (c))")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  [
                    "Including",
                    [
                      "Or",
                      [
                        ["Including", ["Text", "b"]],
                        [
                          "Including",
                          ["Group", ["And", [["Including", ["Text", "c"]]]]]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]);
    });

    it("aAND", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("aAND")).toEqual([
        "And",
        [["Including", ["Text", "aAND"]]]
      ]);
    });

    it("aOR", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("aOR")).toEqual([
        "And",
        [["Including", ["Text", "aOR"]]]
      ]);
    });

    it("a OR bAND", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a OR bAND")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "a"]],
                ["Including", ["Text", "bAND"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("a OR bOR", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a OR bOR")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "a"]],
                ["Including", ["Text", "bOR"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("aAND AND b", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("aAND AND b")).toEqual([
        "And",
        [
          ["Including", ["Text", "aAND"]],
          ["Including", ["Text", "b"]]
        ]
      ]);
    });

    it("aNOT OR b", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("aNOT OR b")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "aNOT"]],
                ["Including", ["Text", "b"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("aOR AND b", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("aOR AND b")).toEqual([
        "And",
        [
          ["Including", ["Text", "aOR"]],
          ["Including", ["Text", "b"]]
        ]
      ]);
    });

    it("(a)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("(a)")).toEqual([
        "And",
        [["Including", ["Group", ["And", [["Including", ["Text", "a"]]]]]]]
      ]);
    });

    it("(a AND b)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("(a AND b)")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  ["Including", ["Text", "a"]],
                  ["Including", ["Text", "b"]]
                ]
              ]
            ]
          ]
        ]
      ]);
    });

    it("(a AND b) OR (c AND d)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("(a AND b) OR (c AND d)")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                [
                  "Including",
                  [
                    "Group",
                    [
                      "And",
                      [
                        ["Including", ["Text", "a"]],
                        ["Including", ["Text", "b"]]
                      ]
                    ]
                  ]
                ],
                [
                  "Including",
                  [
                    "Group",
                    [
                      "And",
                      [
                        ["Including", ["Text", "c"]],
                        ["Including", ["Text", "d"]]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]);
    });

    it("a AND b NOT c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND b NOT c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "b"]],
          ["Excluding", ["Text", "c"]]
        ]
      ]);
    });

    it("a AND b NOT c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND b NOT c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "b"]],
          ["Excluding", ["Text", "c"]]
        ]
      ]);
    });

    it("a AND (b NOT c)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND (b NOT c)")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  ["Including", ["Text", "b"]],
                  ["Excluding", ["Text", "c"]]
                ]
              ]
            ]
          ]
        ]
      ]);
    });

    it("a AND (b NOT (c OR d))", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND (b NOT (c OR d))")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  ["Including", ["Text", "b"]],
                  [
                    "Excluding",
                    [
                      "Group",
                      [
                        "And",
                        [
                          [
                            "Including",
                            [
                              "Or",
                              [
                                ["Including", ["Text", "c"]],
                                ["Including", ["Text", "d"]]
                              ]
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]);
    });

    it('double quotes should return an "Exactly" parse', () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery('"a"')).toEqual([
        "And",
        [["Including", ["Exactly", "a"]]]
      ]);
    });

    it("a,b AND c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: this one actually/probably parses correctly, except that it retains the comma
      expect(parserService.parseQuery("a,b AND c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", ",b"]],
          ["Including", ["Text", "c"]]
        ]
      ]);
    });

    it("a,b OR c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: this one actually/probably parses correctly, except that it retains the comma
      expect(parserService.parseQuery("a,b OR c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", ",b"]],
                ["Including", ["Text", "c"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("a NOT b NOT c", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: naively converts the 2nd NOT to a minus sign and treats as an included term
      expect(parserService.parseQuery("a NOT b NOT c")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Excluding", ["Text", "b"]],
          ["Including", ["Text", "~"]],
          ["Including", ["Text", "c"]]
        ]
      ]);
    });

    it("a AND (b NOT c,d)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: c,d should be interpreted as an AND
      expect(parserService.parseQuery("a AND (b NOT c,d)")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  ["Including", ["Text", "b"]],
                  ["Excluding", ["Text", "c"]],
                  ["Including", ["Text", ",d"]]
                ]
              ]
            ]
          ]
        ]
      ]);
    });

    it("another phrase with quotes", () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(
        parserService.parseQuery('"a-b" AND "(cd)" OR "-e" NOT "f"')
      ).toEqual([
        "And",
        [
          ["Including", ["Exactly", "a-b"]],
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Exactly", "(cd)"]],
                ["Including", ["Exactly", "-e"]]
              ]
            ]
          ],
          ["Excluding", ["Exactly", "f"]]
        ]
      ]);
    });

    it("a.b AND bc", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: a.b is split into 2 separate terms
      expect(parserService.parseQuery("a.b AND bc")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", ".b"]],
          ["Including", ["Text", "bc"]]
        ]
      ]);
    });

    it("a;b AND bc", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: a;b is split into 2 separate terms
      expect(parserService.parseQuery("a;b AND bc")).toEqual([
        "And",
        [
          ["Including", ["Text", "ab"]],
          ["Including", ["Text", "bc"]]
        ]
      ]);
    });

    it("a:b AND bc", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: ':' is interpreted as a field:value pair
      expect(parserService.parseQuery("a:b AND bc")).toEqual([
        "And",
        [
          ["Including", ["Text", "ab"]],
          ["Including", ["Text", "bc"]]
        ]
      ]);
    });

    it("double quotes", () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      // Problem: "AND" is dropped even though it's in double quotes
      expect(parserService.parseQuery('"a AND b"')).toEqual([
        "And",
        [["Including", ["Exactly", "a   b"]]]
      ]);
    });

    it("a AND 癌", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND 癌")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "癌"]]
        ]
      ]);
    });

    it("a OR 癌", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a OR 癌")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "a"]],
                ["Including", ["Text", "癌"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("a AND ハッピー", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND ハッピー")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "ハッピー"]]
        ]
      ]);
    });

    it("a OR ハッピー", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a OR ハッピー")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "a"]],
                ["Including", ["Text", "ハッピー"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("ハッ-ピー AND (癌-癌)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      // Problem: the hyphenated words should be retained
      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("ハッ-ピー AND (癌-癌)")).toEqual([
        "And",
        [
          ["Including", ["Text", "ハッ-ピー"]],
          ["Including", ["Group", ["And", [["Including", ["Text", "癌-癌"]]]]]]
        ]
      ]);
    });

    it("a AND (ハッピー OR (癌))", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND (ハッピー OR (癌))")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  [
                    "Including",
                    [
                      "Or",
                      [
                        ["Including", ["Text", "ハッピー"]],
                        [
                          "Including",
                          ["Group", ["And", [["Including", ["Text", "癌"]]]]]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]);
    });

    it("伤心 AND (悲しい)", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("伤心 AND (悲しい)")).toEqual([
        "And",
        [
          ["Including", ["Text", "伤心"]],
          ["Including", ["Group", ["And", [["Including", ["Text", "悲しい"]]]]]]
        ]
      ]);
    });

    it("a AND 疲劳的 NOT 疲れた", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a AND 疲劳的 NOT 疲れた")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Including", ["Text", "疲劳的"]],
          ["Excluding", ["Text", "疲れた"]]
        ]
      ]);
    });

    it("a NOT 癌", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a NOT 癌")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Excluding", ["Text", "癌"]]
        ]
      ]);
    });

    it("a NOT ハッピー", async () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("a NOT ハッピー")).toEqual([
        "And",
        [
          ["Including", ["Text", "a"]],
          ["Excluding", ["Text", "ハッピー"]]
        ]
      ]);
    });

    it("a b c d AND e", () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      const pq = parserService.parseQuery("a b c d AND e");
      expect(pq).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Group",
              [
                "And",
                [
                  ["Including", ["Text", "a"]],
                  ["Including", ["Text", "b"]],
                  ["Including", ["Text", "c"]],
                  ["Including", ["Text", "d"]]
                ]
              ]
            ]
          ],
          ["Including", ["Text", "e"]]
        ]
      ]);
    });

    it("老年痴呆症 OR cancer", () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("老年痴呆症 OR cancer")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "老年痴呆症"]],
                ["Including", ["Text", "cancer"]]
              ]
            ]
          ]
        ]
      ]);
    });

    it("36415 OR cancer", () => {
      const configService = createMockInstance(ConfigService);
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const parserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      expect(parserService.parseQuery("36415 OR cancer")).toEqual([
        "And",
        [
          [
            "Including",
            [
              "Or",
              [
                ["Including", ["Text", "36415"]],
                ["Including", ["Text", "cancer"]]
              ]
            ]
          ]
        ]
      ]);
    });
  });
});
