import { RpcResourceService, RpcService, RpcMethod } from "@h1nyc/systems-rpc";
import {
  Apps,
  IndicationSortBy,
  IndicationSource,
  IndicationType,
  KeywordAutocompleteResource,
  KeywordFilterAggregation,
  KeywordFilterAutocompleteFilterField,
  KeywordFilterAutocompleteInput,
  RPC_NAMESPACE_KOL_AUTOCOMPLETE,
  SearchIndicationsByQueryInput
} from "@h1nyc/search-sdk";
import { createLogger } from "../lib/Logger";
import { ConfigService } from "./ConfigService";
import { Trace } from "../Tracer";
import { ElasticSearchService } from "./ElasticSearchService";
import { QueryParserService } from "./QueryParserService";
import {
  QueryDslQueryContainer,
  SearchRequest
} from "@elastic/elasticsearch/lib/api/types";
import _ from "lodash";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { UserResourceClient } from "@h1nyc/account-sdk";
// @ts-ignore
import franc from "franc-min";
import { KeywordFilterClauseBuilderService } from "./KeywordFilterClauseBuilderService";
import {
  LanguageDetector,
  MAXIMUM_QUERY_TOKENS,
  OR,
  ParsedQueryTree,
  publicationsQueryFields,
  QueryIndicationInfo
} from "./KeywordSearchResourceServiceRewrite";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { Service } from "typedi";
import {
  KeywordAutocompleteResponseAdapterService,
  SupportedAggregations
} from "./KeywordAutocompleteResponseAdapterService";
import { CHINESE, ENGLISH, JAPANESE, Language } from "./LanguageDetectService";
import { AggregationContainerBuilderService } from "./AggregationContainerBuilderService";
import NameSearchBuilderFactory from "./queryBuilders/NameSearchBuilderFactory";
import CalculateMinimumNameVariations from "./queryBuilders/CalculateMinimumNameVariations";
import { DEFAULT_NAME_VARIATION_MATCH_COUNT } from "./queryBuilders/DefaultNameSearchBuilder";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { buildClaimsRegionFilterForPeopleSearch } from "../util/ClaimsRegionFilterUtils";
import {
  CcsrIcdMappingRepository,
  CcsrPxMappingRepository
} from "@h1nyc/pipeline-repositories";
import {
  sortIcdCodesBySchemeAndAlphabet,
  sortProcedureCodesByAlphabet
} from "../util/QueryBuildingUtils";
import Redis from "ioredis";
import { sha1 } from "object-hash";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";
import { BothQueryParseResult } from "../lib/ParserTypes/types";

export type AutocompleteFeatureFlags = {
  enableQueryContextualPaymentsFiltering: boolean;
  enableCTMSV2: boolean;
  enableTagsInElasticsearch: boolean;
  enableUniquePatientCountForClaims: boolean;
  enableBrazilianClaims: boolean;
  disableUniquePatientCountForOnlyProcedures: boolean;
  enableNestedIndicationFilter: boolean;
  enableCcsrExclusionForMatchedCounts: boolean;
  enableLocationFilterRegionRollup: boolean;
  enableAiqData: boolean;
  enableBothOperatorForSearch: boolean;
  enableNewGlobalLeaderTier: boolean;
  enableExUSGlobalLeader: boolean;
  enableCountrySpecificNonIndicationLeaderFilters: boolean;
};

export const autocompleteFeatureFlagTypes = [
  "enableQueryContextualPaymentsFiltering",
  "enableCTMSV2",
  "enableTagsInElasticsearch",
  "enableUniquePatientCountForClaims",
  "enableBrazilianClaims",
  "disableUniquePatientCountForOnlyProcedures",
  "enableNestedIndicationFilter",
  "enableCcsrExclusionForMatchedCounts",
  "enableLocationFilterRegionRollup",
  "enableAiqData",
  "enableBothOperatorForSearch",
  "enableNewGlobalLeaderTier",
  "enableExUSGlobalLeader",
  "enableCountrySpecificNonIndicationLeaderFilters"
] as const;
export type AutocompleteFeatureFlag =
  (typeof autocompleteFeatureFlagTypes)[number];

export const featureFlagDefaults: Readonly<
  Record<keyof AutocompleteFeatureFlags, { key: string; default: boolean }>
> = {
  enableQueryContextualPaymentsFiltering: {
    key: "enable-query-contextual-payments-filtering",
    default: false
  },
  enableCTMSV2: {
    key: "enable-ctms-v2",
    default: false
  },
  enableTagsInElasticsearch: {
    key: "search.enable-tags-in-elasticsearch",
    default: false
  },
  enableUniquePatientCountForClaims: {
    key: "search.enable-unique-patient-count-for-claims",
    default: false
  },
  enableBrazilianClaims: {
    key: "enable-brazilian-claims",
    default: false
  },
  disableUniquePatientCountForOnlyProcedures: {
    key: "search.disable-unique-patient-count-for-only-procedures",
    default: true
  },
  enableNestedIndicationFilter: {
    key: "search.enable-nested-indication-filter",
    default: false
  },
  enableCcsrExclusionForMatchedCounts: {
    key: "enable-ccsr-exclusion-for-matched-counts",
    default: false
  },
  enableLocationFilterRegionRollup: {
    key: "enable-location-filter-region-rollup",
    default: false
  },
  enableAiqData: {
    key: "enable-aiq-data",
    default: false
  },
  enableBothOperatorForSearch: {
    key: "enable-both-operator-for-hcpu-search",
    default: false
  },
  enableNewGlobalLeaderTier: {
    key: "enable-new-global-leader-tier",
    default: false
  },
  enableExUSGlobalLeader: {
    key: "enable-ex-us-global-leader",
    default: false
  },
  enableCountrySpecificNonIndicationLeaderFilters: {
    key: "enable-country-specific-non-indication-leader",
    default: false
  }
};

const HAS_ADVANCED_OPERATORS = /\s(AND|OR|NOT)\s/;
const AT_LEAST_ONE_ASSET = 1;
const SECONDS = "ex";
const EXPIRATION_PERIOD = 600;

export const AGGREGATIONS_THAT_NEED_NESTED_FILTERING = new Map<
  KeywordFilterAutocompleteFilterField,
  { path: NestedDocPath; field: string }
>([
  [
    KeywordFilterAutocompleteFilterField.PAYMENTS_COMPANY,
    {
      path: "payments",
      field: "payments.payerCompany"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.PAYMENTS_DRUG_OR_DEVICE,
    {
      path: "payments",
      field: "payments.associatedDrugOrDevice"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.PAYMENTS_TYPE_OF_FUNDING,
    {
      path: "payments",
      field: "payments.natureOfPayment"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.PAYMENTS_CATEGORY,
    {
      path: "payments",
      field: "payments.category"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.CONGRESS_NAME,
    {
      path: "congress",
      field: "congress.name_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.CONGRESS_ORGANIZER,
    {
      path: "congress",
      field: "congress.organizer_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.CONGRESS_CONTRIBUTOR_ROLE,
    {
      path: "congress",
      field: "congress.role"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.JOURNAL_NAME,
    {
      path: "publications",
      field: "publications.journalName_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.PUBLICATION_TYPE,
    {
      path: "publications",
      field: "publications.type_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.TRIAL_PHASE,
    {
      path: "trials",
      field: "trials.phase_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.BIOMARKERS,
    {
      path: "trials",
      field: "trials.biomarker"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.TRIAL_STATUS,
    {
      path: "trials",
      field: "trials.status_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.TRIAL_STUDY_TYPE,
    {
      path: "trials",
      field: "trials.type_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR,
    {
      path: "trials",
      field: "trials.sponsor_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.TRIAL_SPONSOR_TYPE,
    {
      path: "trials",
      field: "trials.sponsorType_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.TRIAL_ID,
    {
      path: "trials",
      field: "trials.id"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.TRIAL_ROLES,
    {
      path: "trials",
      field: "trials.roles"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES,
    {
      path: "DRG_diagnoses",
      field: "DRG_diagnoses.codeAndDescription_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.DIAGNOSES,
    {
      path: "DRG_diagnoses",
      field: "DRG_diagnoses.codeAndDescription_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.DRG_PROCEDURES,
    {
      path: "DRG_procedures",
      field: "DRG_procedures.codeAndDescription_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.PROCEDURES,
    {
      path: "DRG_procedures",
      field: "DRG_procedures.codeAndDescription_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.SOCIETY_AFFILIATIONS,
    {
      path: "affiliations",
      field: "affiliations.institution.name.keyword"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.GENERIC_NAME,
    {
      path: "prescriptions",
      field: "prescriptions.generic_name"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.BRAND_NAME,
    {
      path: "prescriptions",
      field: "prescriptions.brand_name"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.DRUG_CLASS,
    {
      path: "prescriptions",
      field: "prescriptions.drug_class"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.CCSR,
    {
      path: "ccsr",
      field: "ccsr.description_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES,
    {
      path: "DRG_diagnoses",
      field: "DRG_diagnoses.diagnosisCode_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.CCSR_PX,
    {
      path: "ccsr_px",
      field: "ccsr_px.description_eng"
    }
  ],
  [
    KeywordFilterAutocompleteFilterField.CCSR_PROCEDURES,
    {
      path: "DRG_procedures",
      field: "DRG_procedures.procedureCode_eng"
    }
  ]
]);

type ClaimsPath = "DRG_diagnoses" | "DRG_procedures";
type AssetPath =
  | "payments"
  | "congress"
  | "trials"
  | "publications"
  | "affiliations"
  | "prescriptions"
  | ClaimsPath
  | "ccsr"
  | "ccsr_px";
type NestedDocPath = AssetPath;

function getLanguageDetector(userPreferredLanguage: string): LanguageDetector {
  return (query: string): Language => {
    const detectedLanguage = franc(query, {
      minLength: 1,
      only: [ENGLISH, CHINESE, JAPANESE]
    });

    if (detectedLanguage === CHINESE && userPreferredLanguage === JAPANESE) {
      return JAPANESE;
    }

    return detectedLanguage === "und"
      ? ENGLISH
      : (detectedLanguage as Language);
  };
}
function shouldUseBothCmnAndJpnFields(
  detectedLanguage: Language,
  userPreferredLanguage: string = ENGLISH
): boolean {
  if (detectedLanguage === CHINESE && userPreferredLanguage === ENGLISH) {
    return true;
  } else if (
    detectedLanguage === JAPANESE &&
    userPreferredLanguage === ENGLISH
  ) {
    return true;
  } else {
    return false;
  }
}

// TODO: there has to be an easier way to convert a string to a language code, figure out how
export function coerceStringLanguageToTypeLanguage(
  language?: string
): Language | null {
  switch (language) {
    case ENGLISH:
    case JAPANESE:
    case CHINESE:
      return language;
    default:
      return null;
  }
}

@Service()
@RpcService()
export class KeywordAutocompleteResourceService
  extends RpcResourceService
  implements KeywordAutocompleteResource
{
  private readonly logger = createLogger(this);
  private peopleIndex: string;
  private redisClient;

  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService,
    private queryParserService: QueryParserService,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient,
    private userClient: UserResourceClient,
    private keywordFilterClauseBuilderService: KeywordFilterClauseBuilderService,
    private parsedQueryTreeToElasticsearchQueriesService: ParsedQueryTreeToElasticsearchQueriesService,
    private responseAdapter: KeywordAutocompleteResponseAdapterService,
    private featureFlagsService: FeatureFlagsService,
    private aggregationContainerBuilderService: AggregationContainerBuilderService,
    private nameSearchBuilderFactory: NameSearchBuilderFactory,
    private calculateMinimumNameVariations: CalculateMinimumNameVariations,
    private ccsrIcdMappingRepository: CcsrIcdMappingRepository,
    private ccsrPxMappingRepository: CcsrPxMappingRepository,
    private indicationsTreeSearchService: IndicationsTreeSearchService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_KOL_AUTOCOMPLETE,
      config.searchRedisOptions
    );
    this.peopleIndex = config.elasticPeopleIndex;
    this.redisClient = new Redis(config.searchCacheRedisOptions);
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.keyword.autocomplete")
  async keywordAutocomplete(
    input: KeywordFilterAutocompleteInput
  ): Promise<Array<KeywordFilterAggregation>> {
    this.logger.info({ data: input }, "autocomplete request");

    const usersPreferredLanguage = await this.getUsersPreferredLanguage(input);
    this.logger.info(
      { data: usersPreferredLanguage },
      "user's preferred language"
    );
    const languageDetector = getLanguageDetector(usersPreferredLanguage);

    const truncatedQuery = this.sanitizeQuery(this.truncateQuery(input.query));
    input.query = truncatedQuery;
    const queryIndicationInfo: QueryIndicationInfo =
      await this.resolveInputQueryToIndications(input);

    if (
      input.app === Apps.TRIAL_LANDSCAPE &&
      queryIndicationInfo.finalIndicationFilter?.length
    ) {
      input.suppliedFilters = {
        ...input.suppliedFilters,
        indications: {
          values: queryIndicationInfo.finalIndicationFilter
        }
      };
    }
    const featureFlags = await this.getFeatureFlagValues(input);

    /**
     * The Idea behind the BOTH operator is that:-
     * We convert a query like BOTH( Cardiology WITH diabetes ) to Cardiology OR diabetes and add
     * corresponding should clauses based on the new query, but add filter clauses based on  BOTH( Cardiology WITH diabetes )
     * // Nadeem - 1 trial cardiology as well as diabetes:- Use Case A ✅
     * // Yash - 1 trial cardiology and 1 trial diabetes :- Use Case B ✅
     * // Abhishek - 1 trial Cardiology and 1 pub diabetes :- Use Case C ✅
     * // Shreenath - 1 trial Cardiology and 1 pub Cardiology:- ❌
     * // BMP - Has nothing:- Use Case D ❌
     * here essentially should would want to match Shreenath(using the OR query) but filter clause has removed Shreenath
     * from search space.
     */
    let bothParserResponse: BothQueryParseResult | undefined = undefined;
    //BOTH operator right now only availale for HCPU
    if (
      featureFlags.enableBothOperatorForSearch &&
      input.app === undefined //Frontend sends undefined for HCPU
    ) {
      bothParserResponse = this.queryParserService.parseBothQuery(input.query);
      if (bothParserResponse.usedBoth) {
        input.query =
          this.queryParserService.rewriteBothQueryToOrExpression(
            bothParserResponse
          );
      }
    }

    const queryUnderstandingServiceResponse = input.query
      ? await this.queryUnderstandingServiceClient.analyze(
          input.query,
          languageDetector(input.query)
        )
      : undefined;
    const parsedQueryTree = await this.parseQueryToTree(
      input.query,
      queryUnderstandingServiceResponse
    );

    const inputWithUsersPreferredLanguage = {
      ...input,
      ...{ language: usersPreferredLanguage }
    };
    const ccsrClaimCodesToInclude = await this.fetchCcsrClaimCodesIfRequired(
      input
    );

    const elasticsearchQuery = await this.buildElasticsearchQuery(
      inputWithUsersPreferredLanguage,
      languageDetector,
      featureFlags,
      parsedQueryTree,
      ccsrClaimCodesToInclude,
      queryUnderstandingServiceResponse
    );

    this.logger.info(
      { data: elasticsearchQuery },
      "autocomplete elasticsearch query"
    );

    const { aggregations, took } = await this.elasticService.query(
      elasticsearchQuery
    );
    this.logger.debug(`TIME ACCORDING TO ELASTICSEARCH: ${took}ms`);

    return await this.responseAdapter.adapt(
      aggregations as SupportedAggregations,
      input.filterField!,
      ccsrClaimCodesToInclude,
      input.filterValue,
      input,
      usersPreferredLanguage
    );
  }

  @RpcMethod()
  @Trace("h1-search.name.autocomplete")
  async nameAutocomplete(
    input: KeywordFilterAutocompleteInput
  ): Promise<Array<KeywordFilterAggregation>> {
    this.logger.info({ data: input }, "name autocomplete request");
    const featureFlags = await this.getFeatureFlagValues(input);

    const usersPreferredLanguage = await this.getUsersPreferredLanguage(input);
    this.logger.info(
      { data: usersPreferredLanguage },
      "user's preferred language"
    );
    const languageDetector = getLanguageDetector(usersPreferredLanguage);

    const inputWithUsersPreferredLanguage = {
      ...input,
      language: usersPreferredLanguage
    };
    const ccsrClaimCodesToInclude = await this.fetchCcsrClaimCodesIfRequired(
      input
    );
    const elasticsearchQuery = await this.buildNameElasticsearchQuery(
      inputWithUsersPreferredLanguage,
      languageDetector,
      featureFlags,
      ccsrClaimCodesToInclude
    );

    this.logger.info(
      { data: elasticsearchQuery },
      "name autocomplete elasticsearch query"
    );

    const { aggregations, took } = await this.elasticService.query(
      elasticsearchQuery
    );
    this.logger.debug(`TIME ACCORDING TO ELASTICSEARCH: ${took}ms`);

    return await this.responseAdapter.adapt(
      aggregations as SupportedAggregations,
      input.filterField!,
      ccsrClaimCodesToInclude,
      input.filterValue,
      input,
      usersPreferredLanguage
    );
  }

  private async parseQueryToTree(
    query?: string,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
  ): Promise<ParsedQueryTree> {
    if (!query) {
      return;
    }

    if (
      !HAS_ADVANCED_OPERATORS.test(query) &&
      queryUnderstandingServiceResponse
    ) {
      return queryUnderstandingServiceResponse
        .getAugmentedQuery()
        .replace(/\bOR\b/g, "|");
    }

    return this.queryParserService.parseQuery(query);
  }

  @Trace("h1-search.keyword.autocomplete.getUsersPreferredLanguage")
  private async getUsersPreferredLanguage({
    userId = "",
    projectId,
    language
  }: KeywordFilterAutocompleteInput): Promise<Language> {
    const suppliedLanguage = coerceStringLanguageToTypeLanguage(language);

    if (suppliedLanguage) {
      return suppliedLanguage;
    }

    try {
      const usersPreferredLanguage =
        await this.userClient.getUsersPreferredLanguage(userId, projectId);
      this.logger.debug(
        `users preferred language: ${usersPreferredLanguage?.language}`
      );

      switch (usersPreferredLanguage?.language) {
        case "japanese":
        case JAPANESE:
          return JAPANESE;
        case "chinese_simplified":
        case "chinese_traditional":
        case CHINESE:
          return CHINESE;
        case "english":
        case ENGLISH:
        default:
          return ENGLISH;
      }
    } catch (err) {
      this.logger.error(
        { err },
        "Error thrown fetching users preferred language"
      );
      return ENGLISH;
    }
  }

  private hasPrescriptionsAssetFilter(input: KeywordFilterAutocompleteInput) {
    return (
      (input.suppliedFilters.claims.genericNames?.values?.length ?? 0) !== 0 ||
      (input.suppliedFilters.claims.brandNames?.values?.length ?? 0) !== 0 ||
      (input.suppliedFilters.claims.drugClasses?.values?.length ?? 0) !== 0
    );
  }

  private hasClaimsAssetFilter(input: KeywordFilterAutocompleteInput) {
    return (
      input.suppliedFilters.claims.diagnosesICD.values.length !== 0 ||
      input.suppliedFilters.claims.proceduresCPT.values.length !== 0 ||
      this.hasPrescriptionsAssetFilter(input)
    );
  }

  @Trace("h1-search.keyword.autocomplete.buildElasticsearchQuery")
  private async buildElasticsearchQuery(
    input: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector,
    featureFlags: AutocompleteFeatureFlags,
    parsedQueryTree: ParsedQueryTree,
    ccsrClaimCodesToInclude: string[],
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
  ): Promise<SearchRequest> {
    const filters =
      await this.keywordFilterClauseBuilderService.buildForAutocompleteRequest(
        input,
        languageDetector,
        featureFlags,
        parsedQueryTree
      );

    // If user has supplied diagnosis/procedure codes or generic names then we ignore the shoulds for other assets
    const claimsAssetFilter = this.hasClaimsAssetFilter(input);

    if (
      parsedQueryTree &&
      !claimsAssetFilter &&
      (!input.filterField ||
        input.filterField ===
          KeywordFilterAutocompleteFilterField.SOCIETY_AFFILIATIONS ||
        !AGGREGATIONS_THAT_NEED_NESTED_FILTERING.has(input.filterField))
    ) {
      const filterForQueryContext = this.buildFilterForQueryTerm(
        input,
        languageDetector,
        parsedQueryTree,
        featureFlags,
        queryUnderstandingServiceResponse
      );
      filters.push(filterForQueryContext);
    }

    const aggregations =
      await this.aggregationContainerBuilderService.buildAggregationContainer(
        input,
        languageDetector,
        filters,
        featureFlags,
        ccsrClaimCodesToInclude,
        parsedQueryTree
      );

    const claimsRegionFilter =
      buildClaimsRegionFilterForPeopleSearch(featureFlags);

    let mustNotBeInTheseCountries: QueryDslQueryContainer[] | undefined;
    if (claimsRegionFilter && this.isClaimsField(input)) {
      mustNotBeInTheseCountries = [claimsRegionFilter];
    }

    const request: SearchRequest = {
      index: this.peopleIndex,
      _source_includes: [],
      size: 0,
      query: {
        bool: {
          filter: filters,
          must_not: mustNotBeInTheseCountries
        }
      },
      aggs: aggregations
    };

    return request;
  }

  @Trace("h1-search.name.autocomplete.buildElasticsearchQuery")
  private async buildNameElasticsearchQuery(
    input: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector,
    featureFlags: AutocompleteFeatureFlags,
    ccsrClaimCodesToInclude: string[]
  ): Promise<SearchRequest> {
    const filters =
      await this.keywordFilterClauseBuilderService.buildForAutocompleteRequest(
        input,
        languageDetector,
        featureFlags
      );

    if (input.query) {
      let queryLang = ENGLISH as Language;
      if (input.projectFeatures?.searchMultiLanguage) {
        queryLang = languageDetector(input.query);
      }
      const useBothCmnAndJpnFields = shouldUseBothCmnAndJpnFields(
        queryLang,
        input.language
      );
      filters.push(
        await this.buildNameFilterForQueryTerm(
          input.query,
          queryLang,
          input.projectId,
          useBothCmnAndJpnFields
        )
      );
    }

    const aggregations =
      await this.aggregationContainerBuilderService.buildAggregationContainer(
        input,
        languageDetector,
        filters,
        featureFlags,
        ccsrClaimCodesToInclude,
        undefined
      );

    const request: SearchRequest = {
      index: this.peopleIndex,
      _source_includes: [],
      size: 0,
      query: {
        bool: {
          filter: filters
        }
      },
      aggs: aggregations
    };

    return request;
  }

  private async buildNameFilterForQueryTerm(
    query: string,
    lang: Language,
    projectId: string,
    useCmnAndJpnFields: boolean
  ): Promise<QueryDslQueryContainer> {
    const nameSearchBuilder =
      this.nameSearchBuilderFactory.getNameSearchBuilder(lang);

    const numberOfNameVariationsToMatch =
      lang === ENGLISH
        ? await this.calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
            this.elasticService,
            this.peopleIndex,
            query
          )
        : DEFAULT_NAME_VARIATION_MATCH_COUNT;
    return {
      bool: {
        should: nameSearchBuilder.getNameQueryShouldClauses(
          query,
          projectId,
          useCmnAndJpnFields,
          numberOfNameVariationsToMatch,
          lang
        ),
        minimum_should_match: AT_LEAST_ONE_ASSET
      }
    };
  }

  private buildFilterForQueryTerm(
    input: KeywordFilterAutocompleteInput,
    languageDetector: LanguageDetector,
    parsedQueryTree: NonNullable<ParsedQueryTree>,
    featureFlags: AutocompleteFeatureFlags,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
  ): QueryDslQueryContainer {
    const shoulds: QueryDslQueryContainer[] = [];

    const congressShouldClause = this.buildNestedCongressQuery(parsedQueryTree);
    const trialsShouldClause = this.buildNestedTrialsQuery(parsedQueryTree);
    const publicationsShouldClause = this.buildNestedPublicationsQuery(
      parsedQueryTree,
      languageDetector
    );
    const paymentsShouldClause = this.buildNestedPaymentsQuery(parsedQueryTree);

    shoulds.push(congressShouldClause);
    shoulds.push(trialsShouldClause);
    shoulds.push(publicationsShouldClause);
    shoulds.push(paymentsShouldClause);
    // if the user has already supplied a diagnosis / procedure code then we do not want to search for the query terms in the diagnosis and procedure fields
    const ccsrDxNotApplied = !input.suppliedFilters.claims.ccsr?.values.length;
    if (
      input.suppliedFilters.claims.diagnosesICD.values.length == 0 &&
      ccsrDxNotApplied
    ) {
      const diagnosesShouldClause = this.buildNestedClaimsDiagnosesQuery(
        parsedQueryTree,
        featureFlags,
        queryUnderstandingServiceResponse
      );
      shoulds.push(diagnosesShouldClause);
    }
    const ccsrPxNotApplied =
      !input.suppliedFilters.claims.ccsrPx?.values.length;
    if (
      input.suppliedFilters.claims.proceduresCPT.values.length == 0 &&
      ccsrPxNotApplied
    ) {
      const proceduresShouldClause = this.buildNestedClaimsProceduresQuery(
        parsedQueryTree,
        featureFlags
      );
      shoulds.push(proceduresShouldClause);
    }

    if (!this.hasPrescriptionsAssetFilter(input)) {
      const prescriptionsShouldClause =
        this.buildNestedClaimsPrescriptionsQuery(parsedQueryTree);
      shoulds.push(prescriptionsShouldClause);
    }

    return {
      bool: {
        should: shoulds,
        minimum_should_match: AT_LEAST_ONE_ASSET
      }
    };
  }

  private buildParsedPaymentsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      ["payments.associatedDrugOrDevice", "payments.payerCompany"]
    );
  }

  private buildParsedCongressQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      [
        "congress.keywords_eng",
        "congress.title_eng",
        "congress.organizer_eng.search",
        "congress.name_eng.search",
        "congress.role.search"
      ]
    );
  }

  private buildParsedPublicationsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>,
    languageDetector: LanguageDetector
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      publicationsQueryFields
    );
  }

  private buildParsedTrialsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): Readonly<QueryDslQueryContainer> {
    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      [
        "trials.officialTitle",
        "trials.briefTitle",
        "trials.conditions",
        "trials.interventions",
        "trials.keywords",
        "trials.summary"
      ],
      ENGLISH
    );
  }

  private buildNestedCongressQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): QueryDslQueryContainer {
    const filter = this.buildParsedCongressQuery(parsedQueryTree);

    return {
      nested: {
        path: "congress",
        score_mode: "sum",
        query: {
          bool: {
            must: {
              match_all: {}
            },
            filter: [filter]
          }
        }
      }
    };
  }

  private buildNestedTrialsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): QueryDslQueryContainer {
    const filter = this.buildParsedTrialsQuery(parsedQueryTree);

    return {
      nested: {
        path: "trials",
        score_mode: "sum",
        query: {
          bool: {
            must: {
              match_all: {}
            },
            filter: [filter]
          }
        }
      }
    };
  }

  private buildNestedPublicationsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>,
    languageDetector: LanguageDetector
  ): QueryDslQueryContainer {
    const queryFilter = this.buildParsedPublicationsQuery(
      parsedQueryTree,
      languageDetector
    );

    return {
      nested: {
        path: "publications",
        query: {
          bool: {
            filter: [queryFilter]
          }
        }
      }
    };
  }

  private buildNestedPaymentsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): QueryDslQueryContainer {
    const filter = this.buildParsedPaymentsQuery(parsedQueryTree);

    return {
      nested: {
        path: "payments",
        score_mode: "sum",
        query: {
          bool: {
            must: {
              match_all: {}
            },
            filter: [filter]
          }
        }
      }
    };
  }

  private buildNestedClaimsDiagnosesQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>,
    featureFlags: AutocompleteFeatureFlags,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
  ): QueryDslQueryContainer {
    const filter = this.parsedQueryTreeToElasticsearchQueriesService.parse(
      this.constructClaimsQueryString(
        parsedQueryTree,
        queryUnderstandingServiceResponse
      )!,
      ["DRG_diagnoses.codeAndDescription"],
      ENGLISH
    );

    const nestedQuery = {
      nested: {
        path: "DRG_diagnoses",
        query: {
          bool: {
            filter: [filter]
          }
        }
      }
    };

    return this.buildClaimsQueryWithRegionFilter(nestedQuery, featureFlags);
  }

  private constructClaimsQueryString(
    parsedQueryTree: NonNullable<ParsedQueryTree>,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): ParsedQueryTree {
    if (
      queryUnderstandingServiceResponse?.getDiagnosisCodesList().length &&
      typeof parsedQueryTree === "string"
    ) {
      return queryUnderstandingServiceResponse!
        .getDiagnosisCodesList()
        .map((diagnosisCode) => `(${diagnosisCode}*)`)
        .join(OR);
    } else {
      return parsedQueryTree;
    }
  }

  private buildNestedClaimsProceduresQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>,
    featureFlags: AutocompleteFeatureFlags
  ): QueryDslQueryContainer {
    const filter = this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      ["DRG_procedures.codeAndDescription"],
      ENGLISH
    );

    const nestedQuery = {
      nested: {
        path: "DRG_procedures",
        query: {
          bool: {
            filter: [filter]
          }
        }
      }
    };

    return this.buildClaimsQueryWithRegionFilter(nestedQuery, featureFlags);
  }

  private buildNestedClaimsPrescriptionsQuery(
    parsedQueryTree: NonNullable<ParsedQueryTree>
  ): QueryDslQueryContainer {
    const filter = this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      ["prescriptions.generic_name.text"]
    );

    const nestedQuery = {
      nested: {
        path: "prescriptions",
        query: {
          bool: {
            filter: [filter]
          }
        }
      }
    };

    return nestedQuery;
  }

  private buildClaimsQueryWithRegionFilter(
    nestedQuery: QueryDslQueryContainer,
    featureFlags: AutocompleteFeatureFlags
  ) {
    const claimsRegionFilter =
      buildClaimsRegionFilterForPeopleSearch(featureFlags);

    if (claimsRegionFilter) {
      return {
        bool: {
          must_not: [claimsRegionFilter],
          must: [nestedQuery]
        }
      };
    }

    return nestedQuery;
  }

  private isClaimsField({ filterField }: KeywordFilterAutocompleteInput) {
    return (
      filterField === KeywordFilterAutocompleteFilterField.DRG_DIAGNOSES ||
      filterField === KeywordFilterAutocompleteFilterField.DIAGNOSES ||
      filterField === KeywordFilterAutocompleteFilterField.DRG_PROCEDURES ||
      filterField === KeywordFilterAutocompleteFilterField.PROCEDURES ||
      filterField === KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES ||
      filterField === KeywordFilterAutocompleteFilterField.CCSR
    );
  }

  private truncateQuery(query?: string) {
    if (query && !HAS_ADVANCED_OPERATORS.test(query)) {
      return query.trim().split(/\s+/).slice(0, MAXIMUM_QUERY_TOKENS).join(" ");
    }
    return query;
  }

  private sanitizeQuery(query?: string) {
    return query?.replace(/[´]/g, "'");
  }

  private async resolveInputQueryToIndications(
    input: Readonly<KeywordFilterAutocompleteInput>
  ): Promise<QueryIndicationInfo> {
    // contains the final list of indication filters that need to be applied to the query
    const finalIndicationFilter: string[] = [];
    // contains the list of indications that are close to the query, the user may not have typed an exact indication
    let indicationsMatchedToQuery: string[] = [];
    let queryIsIndication = false;
    if (!_.isEmpty(input.suppliedFilters?.indications?.values)) {
      // add indication filters added by the user to the list of final indications.
      finalIndicationFilter.push(...input.suppliedFilters!.indications!.values);
    }

    if (input.query) {
      const indicationSearchInput: SearchIndicationsByQueryInput = {
        query: input.query,
        indicationType: [IndicationType.L3],
        size: 5,
        indicationSource: [IndicationSource.ALL],
        sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
        projectId: input.projectId
      };
      const indicationNodes =
        await this.indicationsTreeSearchService.searchIndicationsByQuery(
          indicationSearchInput
        );

      if (indicationNodes?.length) {
        indicationsMatchedToQuery = indicationNodes?.map((node) =>
          node.indicationName.trim().toLowerCase()
        );
      }
      // if the query itself is an indication then we need to ignore other indication matches
      const inputUserQuery = input.query.trim().toLowerCase();
      if (indicationsMatchedToQuery?.includes(inputUserQuery)) {
        indicationsMatchedToQuery = [inputUserQuery];
        queryIsIndication = true;
      }
    }
    // add the indications matched to the query to the final indication filter
    if (indicationsMatchedToQuery.length > 0) {
      finalIndicationFilter.push(...indicationsMatchedToQuery);
    }

    const queryIndicationInfo: QueryIndicationInfo = {
      finalIndicationFilter,
      indicationsMatchedToQuery,
      isIndication: queryIsIndication
    };

    this.logger.info(queryIndicationInfo, "resolved indications from query");

    return queryIndicationInfo;
  }

  // TODO: Find a way to make this generic
  // - Refactor once userId is required
  @Trace("h1-search.keyword.autocomplete.getFeatureFlagValues")
  private async getFeatureFlagValues({
    userId,
    projectId
  }: KeywordFilterAutocompleteInput): Promise<AutocompleteFeatureFlags> {
    const featureFlags: Partial<AutocompleteFeatureFlags> = {};

    const user = userId ? { userId, projectId } : undefined;
    const flagsState = await this.featureFlagsService.getAllFlags(user);

    for (const flag of autocompleteFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as AutocompleteFeatureFlags;
  }

  private async getClaimCodeListForCcsrOrderedById(
    path: ClaimsPath,
    ccsrToExpand?: string,
    ccsrIcdOffset?: number,
    ccsrIcdSize?: number
  ): Promise<string[]> {
    if (
      _.isUndefined(ccsrToExpand) ||
      _.isUndefined(ccsrIcdOffset) ||
      _.isUndefined(ccsrIcdSize)
    ) {
      return [];
    }
    const isDiagnosis = path === "DRG_diagnoses";
    let claimCodeList = [];
    const finalClaimCodeList: string[] = [];
    if (isDiagnosis) {
      claimCodeList = await this.ccsrIcdMappingRepository.getIcdCodesForCcsr([
        ccsrToExpand!
      ]);
      if (claimCodeList.length) {
        claimCodeList.sort(sortIcdCodesBySchemeAndAlphabet);
        for (const claimCodeObj of claimCodeList) {
          finalClaimCodeList.push(claimCodeObj.icdCode.toUpperCase());
        }
      }
    } else {
      claimCodeList =
        await this.ccsrPxMappingRepository.getProcedureCodesForCcsr([
          ccsrToExpand!
        ]);
      if (claimCodeList.length) {
        claimCodeList.sort(sortProcedureCodesByAlphabet);
        for (const claimCodeObj of claimCodeList) {
          finalClaimCodeList.push(claimCodeObj.procedureCode.toUpperCase());
        }
      }
    }

    if (finalClaimCodeList.length) {
      const lengthToUse = _.min([
        (ccsrIcdOffset ?? 0) + (ccsrIcdSize ?? finalClaimCodeList.length),
        finalClaimCodeList.length
      ]);

      return finalClaimCodeList.slice(ccsrIcdOffset ?? 0, lengthToUse);
    }
    return finalClaimCodeList;
  }

  private async fetchCcsrClaimCodesIfRequired(
    input: KeywordFilterAutocompleteInput
  ) {
    if (
      input.filterField &&
      (input.filterField ===
        KeywordFilterAutocompleteFilterField.CCSR_PROCEDURES ||
        input.filterField ===
          KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES)
    ) {
      if (
        _.isUndefined(input.ccsrToExpand) ||
        _.isUndefined(input.ccsrIcdOffset) ||
        _.isUndefined(input.ccsrIcdSize)
      ) {
        return [];
      }
      const path: ClaimsPath =
        input.filterField ===
        KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES
          ? "DRG_diagnoses"
          : "DRG_procedures";

      try {
        const entities = await this.getFromCache(
          input.ccsrToExpand!,
          input.ccsrIcdOffset!,
          input.ccsrIcdSize
        );
        if (entities) return entities;
      } catch (err) {
        this.logger.error(err, "Error connecting redis for ccsr claim codes");
      }

      const codesToIncludeInAutocomplete =
        await this.getClaimCodeListForCcsrOrderedById(
          path,
          input.ccsrToExpand,
          input.ccsrIcdOffset,
          input.ccsrIcdSize
        );

      if (codesToIncludeInAutocomplete.length > 1) {
        try {
          await this.addToCache(
            input.ccsrToExpand,
            input.ccsrIcdOffset,
            input.ccsrIcdSize,
            codesToIncludeInAutocomplete
          );
        } catch (err) {
          this.logger.error(err, "Error connecting redis for ccsr claim codes");
        }
      }
      return codesToIncludeInAutocomplete;
    } else {
      return [];
    }
  }

  private async getFromCache(
    ccsrToExpand: string,
    ccsrIcdOffset: number,
    ccsrIcdSize: number
  ): Promise<string[] | null> {
    const key = sha1({ ccsrToExpand, ccsrIcdOffset, ccsrIcdSize });
    const raw = await this.redisClient.get(key);
    if (raw) {
      const entities = JSON.parse(raw) as string[];
      this.logger.debug(entities, "ccsr code cache hit");
      return entities;
    }
    this.logger.debug(null, "ccsr code cache miss");
    return null;
  }

  private async addToCache(
    ccsrToExpand: string,
    ccsrIcdOffset: number,
    ccsrIcdSize: number,
    results: Readonly<string[]>
  ) {
    const key = sha1({ ccsrToExpand, ccsrIcdOffset, ccsrIcdSize });
    return this.redisClient.set(
      key,
      JSON.stringify(results),
      SECONDS,
      EXPIRATION_PERIOD
    );
  }
}
