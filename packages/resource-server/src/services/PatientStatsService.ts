import {
  PatientDiversityStats,
  PatientDiversityStatsForHeatmap,
  PatientStatsRpcInput
} from "@h1nyc/search-sdk";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  AggregationsTermsAggregateBase,
  QueryDslQueryContainer,
  SearchRequest
} from "@elastic/elasticsearch/lib/api/types";
import { createLogger } from "../lib/Logger";
import { Service } from "typedi";

type DocCountBucket = {
  key: string;
  doc_count: number;
};

type LocationAggregation = {
  key: {
    location_name: string;
  };
  race: AggregationsTermsAggregateBase<DocCountBucket>;
  gender: AggregationsTermsAggregateBase<DocCountBucket>;
  age: AggregationsTermsAggregateBase<DocCountBucket>;
};

export type CompositeAggregation = Record<
  "composite_agg",
  {
    after_key?: AfterKey;
    buckets: LocationAggregation[];
  }
>;

type AfterKey = {
  location_name: string;
};

export const REGION_FILTER: QueryDslQueryContainer = {
  term: {
    location_level: "region"
  }
};

export const LOCALITY_FILTER: QueryDslQueryContainer = {
  term: {
    location_level: "locality"
  }
};

const BRAZIL_RACES = [
  "White Non-Hispanic",
  "Black Non-Hispanic",
  "Asian Pacific Islander",
  "Mixed (Brazil Only)",
  "Indigenous (Brazil Only)"
];

@Service()
export class PatientStatsService {
  private readonly logger = createLogger(this);

  constructor(
    private configService: ConfigService,
    private elasticSearchService: ElasticSearchService
  ) {}

  async heatmap(
    input: PatientStatsRpcInput
  ): Promise<PatientDiversityStatsForHeatmap> {
    const shoulds: QueryDslQueryContainer[] =
      this.getIcdIndicationShoulds(input);

    if (!shoulds.length) {
      throw new Error("No ICD/indication provided");
    }

    const buckets = await this.getCompositeAggregations(
      shoulds,
      input.level === "locality" ? LOCALITY_FILTER : REGION_FILTER
    );

    this.logger.info({
      bucketLength: buckets.length
    });

    return this.mapBucketToDiversityStat(buckets);
  }

  async stats(input: PatientStatsRpcInput): Promise<PatientDiversityStats> {
    const shoulds: QueryDslQueryContainer[] =
      this.getIcdIndicationShoulds(input);
    const locationShoulds: QueryDslQueryContainer[] =
      this.getLocationShoulds(input);

    const filters: QueryDslQueryContainer[] = [];

    if (shoulds.length) {
      filters.push({
        bool: {
          should: shoulds,
          minimum_should_match: 1
        }
      });
    }

    if (locationShoulds.length) {
      filters.push({
        bool: {
          should: locationShoulds,
          minimum_should_match: 1
        }
      });
    } else {
      // We apply locality OR region filter only if there are no city or state filter applied
      if (input.level === "locality") {
        filters.push(LOCALITY_FILTER);
      } else {
        // When no location filter is applied we just aggregate on the REGION level data
        filters.push(REGION_FILTER);
      }
    }

    const query: SearchRequest = {
      index: this.configService.elasticPatientIndex,
      size: 0,
      query: {
        bool: {
          filter: filters
        }
      },
      aggs: {
        gender: {
          terms: {
            field: "gender"
          }
        },
        age: {
          terms: {
            field: "age",
            size: 100
          }
        },
        race: {
          terms: {
            field: "race",
            size: 10
          }
        }
      }
    };
    this.logger.info({ query });

    const { aggregations } = await this.elasticSearchService.query(query);
    const genderAgg = aggregations!
      .gender as AggregationsTermsAggregateBase<DocCountBucket>;
    const ageAgg = aggregations!
      .age as AggregationsTermsAggregateBase<DocCountBucket>;
    const raceAgg = aggregations!
      .race as AggregationsTermsAggregateBase<DocCountBucket>;
    const genderTotal = (genderAgg.buckets as DocCountBucket[]).reduce(
      (prev, bucket) => {
        return prev + bucket.doc_count;
      },
      0
    );
    const ageTotal = (ageAgg.buckets as DocCountBucket[]).reduce(
      (prev, bucket) => {
        return prev + bucket.doc_count;
      },
      0
    );
    const raceBuckets = (raceAgg.buckets as DocCountBucket[]).filter(
      (bucket) => bucket.key !== "Not Disclosed"
    );
    const raceTotal = raceBuckets.reduce((prev, bucket) => {
      return prev + bucket.doc_count;
    }, 0);
    return {
      patientCount: genderTotal,
      gender: (genderAgg.buckets as DocCountBucket[]).map((bucket) => {
        return {
          key: bucket.key,
          value: bucket.doc_count,
          percent: bucket.doc_count / genderTotal
        };
      }),
      age: (ageAgg.buckets as DocCountBucket[]).map((bucket) => {
        return {
          key: bucket.key,
          value: bucket.doc_count,
          percent: bucket.doc_count / ageTotal
        };
      }),
      race: raceBuckets.map((bucket) => {
        return {
          key: bucket.key,
          value: bucket.doc_count,
          percent: bucket.doc_count / raceTotal
        };
      })
    };
  }

  private getIcdIndicationShoulds(input: PatientStatsRpcInput) {
    const shoulds: QueryDslQueryContainer[] = [];
    if (input.icdCodes.length) {
      shoulds.push({
        terms: {
          icd_codes: input.icdCodes
        }
      });
    }

    if (input.indications.length) {
      shoulds.push({
        terms: {
          indications: input.indications
        }
      });
    }

    return shoulds;
  }

  private getLocationShoulds(input: PatientStatsRpcInput) {
    const locationShoulds: QueryDslQueryContainer[] = [];
    if (input.cities.length) {
      locationShoulds.push({
        bool: {
          filter: [
            LOCALITY_FILTER,
            {
              terms: {
                location_name: input.cities
              }
            }
          ]
        }
      });
    }

    if (input.states.length) {
      locationShoulds.push({
        bool: {
          filter: [
            REGION_FILTER,
            {
              terms: {
                location_name: input.states
              }
            }
          ]
        }
      });
    }

    return locationShoulds;
  }

  private getRaceCount(bucket: LocationAggregation, race: string) {
    const raceBucket = (bucket.race.buckets as DocCountBucket[]).find(
      (bucket) => bucket.key === race
    );
    if (raceBucket) {
      return raceBucket.doc_count;
    }
    return 0;
  }

  private mapBucketToDiversityStat(
    buckets: LocationAggregation[]
  ): PatientDiversityStatsForHeatmap {
    return {
      diversityStats: buckets.map((bucket) => {
        return {
          locationName: bucket.key.location_name,
          values: BRAZIL_RACES.map((race) => this.getRaceCount(bucket, race))
        };
      })
    };
  }

  private createLocationStatsAggregationQuery(
    shoulds: QueryDslQueryContainer[],
    filter: typeof REGION_FILTER | typeof LOCALITY_FILTER,
    after_key?: AfterKey
  ) {
    return {
      index: this.configService.elasticPatientIndex,
      size: 0,
      query: {
        bool: {
          should: shoulds,
          minimum_should_match: 1,
          filter
        }
      },
      aggs: {
        composite_agg: {
          composite: {
            size: 1000,
            after: after_key,
            sources: [
              {
                location_name: {
                  terms: {
                    field: "location_name"
                  }
                }
              }
            ]
          },
          aggs: {
            race: {
              terms: {
                field: "race",
                size: 10
              }
            }
          }
        }
      }
    };
  }

  private async getCompositeAggregations(
    shoulds: QueryDslQueryContainer[],
    filter: typeof REGION_FILTER | typeof LOCALITY_FILTER
  ) {
    let query: SearchRequest = this.createLocationStatsAggregationQuery(
      shoulds,
      filter
    );
    this.logger.info({ query });
    let after_key = undefined;
    let buckets: LocationAggregation[] = [];
    do {
      const { aggregations } = await this.elasticSearchService.query(query);
      const composite_agg = (aggregations as CompositeAggregation)
        .composite_agg;
      buckets = buckets.concat(composite_agg?.buckets);
      after_key = composite_agg.after_key;
      query = this.createLocationStatsAggregationQuery(
        shoulds,
        filter,
        after_key
      );
    } while (after_key);

    return buckets;
  }
}
