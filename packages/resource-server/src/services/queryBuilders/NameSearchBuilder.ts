import {
  QueryDslQueryContainer,
  SearchRequest,
  QueryDslFunctionScoreContainer
} from "@elastic/elasticsearch/lib/api/types";
import { QueryUnderstandingServiceResponse } from "../../proto/query_understanding_service_pb";
import { Language } from "../LanguageDetectService";
import { NameSearchFeatureFlags } from "../NameSearchResourceServiceRewrite";
import { OnboardingData } from "../UserOnboardingDataService";

export const PROMINENCE_SCORE_FUNCTION: Readonly<
  Array<QueryDslFunctionScoreContainer>
> = [
  {
    script_score: {
      script: {
        source: "3*Math.tanh(doc['presentWorkInstitutionCount'].value)"
      }
    }
  },
  {
    script_score: {
      script: {
        source: "0.5*Math.log(doc['publicationCount'].value + 1)"
      }
    }
  },
  {
    script_score: {
      script: {
        source: "0.35*Math.log(doc['trialCount'].value + 1)"
      }
    }
  },
  {
    script_score: {
      script: {
        source: "0.15*Math.log(doc['congressCount'].value + 1)"
      }
    }
  }
];

export const HIGH_CONF_PERSON_INNER_HIT_QUERY: Array<QueryDslQueryContainer> = [
  {
    nested: {
      path: "publications",
      score_mode: "none",
      query: {
        bool: {
          must: {
            match_all: {}
          }
        }
      },
      inner_hits: {
        sort: [
          {
            "publications.datePublished": {
              order: "desc"
            }
          }
        ],
        size: 5,
        _source: false,
        docvalue_fields: [
          "publications.PMID",
          "publications.id",
          "publications.journalName_eng",
          "publications.journalName_cmn",
          "publications.journalName_jpn",
          "publications.title_eng.keyword",
          "publications.title_cmn.keyword",
          "publications.title_jpn.keyword",
          "publications.languageCode",
          "publications.datePublished"
        ]
      }
    }
  },
  {
    nested: {
      path: "trials",
      score_mode: "none",
      query: {
        bool: {
          must: {
            match_all: {}
          }
        }
      },
      inner_hits: {
        sort: [
          {
            "trials.startDate": {
              order: "desc"
            }
          }
        ],
        size: 5,
        _source: false,
        docvalue_fields: [
          "trials.id",
          "trials.phase_eng",
          "trials.phase_cmn",
          "trials.phase_jpn",
          "trials.startDate",
          "trials.status_eng",
          "trials.status_cmn",
          "trials.status_jpn",
          "trials.briefTitle_eng.keyword",
          "trials.briefTitle_cmn.keyword",
          "trials.briefTitle_jpn.keyword",
          "trials.officialTitle_eng.keyword",
          "trials.officialTitle_cmn.keyword",
          "trials.officialTitle_jpn.keyword",
          "trials.languageCode",
          "trials.primaryCompletionDate"
        ]
      }
    }
  },
  {
    nested: {
      path: "congress",
      score_mode: "none",
      query: {
        bool: {
          must: {
            match_all: {}
          }
        }
      },
      inner_hits: {
        sort: [
          {
            "congress.endDate": {
              order: "desc"
            }
          }
        ],
        size: 5,
        _source: false,
        docvalue_fields: [
          "congress.id",
          "congress.languageCode",
          "congress.name_eng",
          "congress.name_cmn",
          "congress.name_jpn",
          "congress.title_eng.keyword",
          "congress.title_cmn.keyword",
          "congress.title_jpn.keyword",
          "congress.endDate"
        ]
      }
    }
  }
];
export const PROMINENCE_SCORE_WITH_TAG_COUNT: Array<QueryDslFunctionScoreContainer> =
  [
    {
      script_score: {
        script: {
          source: "3*Math.tanh(doc['presentWorkInstitutionCount'].value)"
        }
      }
    },
    {
      script_score: {
        script: {
          source: "0.5*Math.log(doc['publicationCount'].value + 1)"
        }
      }
    },
    {
      script_score: {
        script: {
          source: "0.35*Math.log(doc['trialCount'].value + 1)"
        }
      }
    },
    {
      script_score: {
        script: {
          source: "0.15*Math.log(doc['congressCount'].value + 1)"
        }
      }
    },
    {
      script_score: {
        script: {
          source:
            "0.35*Math.log((doc['privateTagsCount'].size()==0  ? 0 : doc['privateTagsCount'].value) + (doc['publicTagsCount'].size()==0  ? 0 : doc['publicTagsCount'].value) + 1)"
        }
      }
    }
  ];

export interface NameSearchBuilderArgWithKeyword
  extends NameSearchBuilderArgWithoutKeyword {
  query: string;
  numberOfNameVariationsToMatch: number;
  onboardingData: OnboardingData;
  queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse;
  projectId: string;
  sourceOverride?: string[];
  useCongressContributorRanking?: boolean;
  congressFilters?: QueryDslQueryContainer[];
}

export interface NameSearchBuilderArgWithoutKeyword {
  from: number;
  size: number;
  filter: QueryDslQueryContainer[];
  language: Language;
  featureFlags: NameSearchFeatureFlags;
  useCmnAndJpnFields: boolean;
}

export interface NameSearchBuilder {
  createNameSearchBody(args: NameSearchBuilderArgWithKeyword): SearchRequest;
  createNameSearchBodyNoKeyWord(
    args: NameSearchBuilderArgWithoutKeyword
  ): SearchRequest;
  getNameQueryShouldClauses(
    query: string,
    projectId: string,
    useCmnAndJpnFields: boolean,
    numberOfNameVariationsToMatch?: number,
    lang?: string,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse,
    featureFlags?: NameSearchFeatureFlags
  ): QueryDslQueryContainer[];
}
