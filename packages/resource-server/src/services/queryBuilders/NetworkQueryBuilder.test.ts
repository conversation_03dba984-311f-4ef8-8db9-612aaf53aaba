import { faker } from "@faker-js/faker";
import { ParsedQueryTreeToElasticsearchQueriesService } from "../ParsedQueryTreeToElasticsearchQueries";
import { createMockInstance } from "../../util/TestUtils";
import { ConfigService } from "../ConfigService";
import {
  NetworkCollaboratorRequest,
  NetworkFilterAutocompleteField,
  NetworkFilters,
  NetworkSortBy
} from "@h1nyc/search-sdk";
import {
  NetworkQueryBuilder,
  networkNestedDocTypes,
  nestedDocTypeToCollaborationType,
  nestedDocs,
  NetworkNestedDocPathForKeywordFilter,
  NetworkNestedDocPath
} from "./NetworkQueryBuilder";
import { generateNetworkFilters } from "../NetworkResourceService.test";
import { ENGLISH, JAPANESE, CHINESE, Language } from "../LanguageDetectService";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import {
  generateMockElasticsearchTermQuery,
  mockParseTreeToElasticsearchQueries
} from "../HCPDocumentTestingUtils";
import { NetworkFeatureFlags } from "../NetworkResourceService";

const EMPTY_STRING = "";
const SOURCE_FIELDS = ["id"];
const LOCATION_FILTER_AUTOCOMPLETE_FIELDS = [
  NetworkFilterAutocompleteField.CITY,
  NetworkFilterAutocompleteField.COUNTRY,
  NetworkFilterAutocompleteField.REGION,
  NetworkFilterAutocompleteField.POSTAL_CODE
];
const LANGUAGES_SUPPORTED: Language[] = [ENGLISH, JAPANESE, CHINESE];
const DESCENDING = "desc";
const ASCENDING = "asc";
const SORT_TIEBREAKER = [
  {
    _score: { order: DESCENDING }
  },
  {
    id: {
      order: ASCENDING
    }
  },
  {
    h1dn_id: {
      order: ASCENDING
    }
  }
];

const FEATURE_FLAGS = generateFeatureFlags();

function generateFeatureFlags(
  overrides?: Partial<NetworkFeatureFlags>
): NetworkFeatureFlags {
  const flags: NetworkFeatureFlags = {
    enableBrazilianClaims: true
  };

  return { ...flags, ...overrides };
}

describe("NetworkQueryBuilder", () => {
  describe("buildFindNetworkQuery", () => {
    it("should return null if all collaboration types need to be filtered out", () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFiltersAllFalse = generateNetworkFilters({
        collaborationType: {
          trials: false,
          congresses: false,
          publications: false
        }
      });
      const suppliedFiltersEmpty: NetworkFilters = {
        collaborationType: {}
      };
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const requestWithAllFalse = {
        personId,
        dateRange,
        page,
        suppliedFilters: suppliedFiltersAllFalse,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const requestWithEmpty = {
        personId,
        dateRange,
        page,
        suppliedFilters: suppliedFiltersEmpty,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const queryAllFalse = networkQueryBuilder.buildFindNetworkQuery(
        requestWithAllFalse,
        [],
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );
      const queryEmpty = networkQueryBuilder.buildFindNetworkQuery(
        requestWithEmpty,
        [],
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );
      expect(queryAllFalse).toBeNull();
      expect(queryEmpty).toBeNull();
    });

    it("should filter out the HCP themself from the request, but filter on the HCP's ID as a collaborator and on project ID", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personId,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const query = networkQueryBuilder.buildFindNetworkQuery(
        request,
        [],
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: page.limit,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              filter: [
                expect.termQuery("collaboratorIds", personId),
                {
                  bool: {
                    must_not: expect.termQuery("id", personId)
                  }
                },
                expect.projectIdFilter(projectId)
              ]
            })
          }
        })
      );
    });

    it("should aggregate trials/publications/congress docs", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personId,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const query = networkQueryBuilder.buildFindNetworkQuery(
        request,
        [],
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: page.limit,
          _source_includes: SOURCE_FIELDS,
          aggs: {
            trials: {
              nested: {
                path: "trials"
              },
              aggs: {
                nested: {
                  filter: {
                    bool: {
                      filter: [expect.termQuery("trials.persons.id", personId)]
                    }
                  },
                  aggs: {
                    people: {
                      reverse_nested: {}
                    }
                  }
                }
              }
            },
            publications: {
              nested: {
                path: "publications"
              },
              aggs: {
                nested: {
                  filter: {
                    bool: {
                      filter: [
                        expect.termQuery("publications.persons.id", personId)
                      ]
                    }
                  },
                  aggs: {
                    people: {
                      reverse_nested: {}
                    }
                  }
                }
              }
            },
            congress: {
              nested: {
                path: "congress"
              },
              aggs: {
                nested: {
                  filter: {
                    bool: {
                      filter: [
                        expect.termQuery("congress.persons.id", personId)
                      ]
                    }
                  },
                  aggs: {
                    people: {
                      reverse_nested: {}
                    }
                  }
                }
              }
            }
          }
        })
      );
    });

    it("should add an optional should clause labeled 'sharedAffiliations' with a terms query using the elements in affiliatedInstitutionIds when it's non-empty", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personId,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const affiliatedInstitutionIds = Array.from({ length: 10 }, () => {
        return faker.datatype.uuid();
      });

      const query = networkQueryBuilder.buildFindNetworkQuery(
        request,
        affiliatedInstitutionIds,
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: page.limit,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              should: expect.arrayContaining([
                {
                  nested: {
                    path: "affiliations",
                    query: {
                      bool: {
                        filter: expect.termsQuery(
                          "affiliations.institution.id",
                          affiliatedInstitutionIds
                        )
                      }
                    },
                    inner_hits: {
                      _source: {
                        includes: [
                          "affiliations.type",
                          "affiliations.isCurrent",
                          "affiliations.institution.id",
                          "affiliations.institution.ultimateParentId",
                          "affiliations.insitution.name",
                          "affiliations.institution.address",
                          "affiliations.institution.nameTranslations",
                          "affiliations.institution.addressTranslations"
                        ]
                      },
                      name: "sharedAffiliations",
                      size: 10
                    }
                  }
                }
              ]),
              minimum_should_match: 0
            })
          }
        })
      );
    });

    it("should NOT add a 'sharedAffiliations' labled query when affiliatedInstitutionIds is empty", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personId,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const affiliatedInstitutionIds: string[] = [];

      const query = networkQueryBuilder.buildFindNetworkQuery(
        request,
        affiliatedInstitutionIds,
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: page.limit,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              should: expect.not.arrayContaining([
                {
                  nested: {
                    path: "affiliations",
                    query: expect.anything(),
                    inner_hits: expect.objectContaining({
                      name: "sharedAffiliations"
                    })
                  }
                }
              ]),
              minimum_should_match: 0
            })
          }
        })
      );
    });

    it("should add an optional should clause labeled 'locations' to find the locations of collaborators from current work affiliations", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personId,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const query = networkQueryBuilder.buildFindNetworkQuery(
        request,
        [],
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: page.limit,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              should: [
                {
                  nested: {
                    path: "affiliations",
                    query: {
                      bool: {
                        filter: expect.arrayContaining([
                          expect.termQuery("affiliations.isCurrent", true),
                          expect.termQuery(
                            "affiliations.type",
                            "Work Affiliation"
                          )
                        ])
                      }
                    },
                    inner_hits: {
                      _source: {
                        includes: [
                          "affiliations.type",
                          "affiliations.isCurrent",
                          "affiliations.institution.id",
                          "affiliations.institution.ultimateParentId",
                          "affiliations.insitution.name",
                          "affiliations.institution.address",
                          "affiliations.institution.nameTranslations",
                          "affiliations.institution.addressTranslations"
                        ]
                      },
                      name: "locations",
                      size: 10
                    }
                  }
                }
              ],
              minimum_should_match: 0
            })
          }
        })
      );
    });

    describe("filters", () => {
      it("should add nested document type as an optional should query if that specific type is set to false in the collaborationType filter", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };
        const parsedQueryTree = faker.datatype.string();

        networkNestedDocTypes.forEach((nestedDocType) => {
          const collaborationType =
            nestedDocTypeToCollaborationType[nestedDocType];
          const suppliedFilters = generateNetworkFilters(
            {},
            {
              [collaborationType]: false
            }
          );

          const request = {
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH
          };

          const query = networkQueryBuilder.buildFindNetworkQuery(
            request,
            [],
            parsedQueryTree,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              from: page.offset,
              size: page.limit,
              _source_includes: SOURCE_FIELDS,
              query: {
                bool: expect.objectContaining({
                  should: expect.arrayContaining([
                    {
                      nested: {
                        path: "affiliations",
                        query: {
                          bool: {
                            filter: expect.arrayContaining([
                              expect.termQuery("affiliations.isCurrent", true),
                              expect.termQuery(
                                "affiliations.type",
                                "Work Affiliation"
                              )
                            ])
                          }
                        },
                        inner_hits: {
                          _source: {
                            includes: [
                              "affiliations.type",
                              "affiliations.isCurrent",
                              "affiliations.institution.id",
                              "affiliations.institution.ultimateParentId",
                              "affiliations.insitution.name",
                              "affiliations.institution.address",
                              "affiliations.institution.nameTranslations",
                              "affiliations.institution.addressTranslations"
                            ]
                          },
                          name: "locations",
                          size: 10
                        }
                      }
                    },
                    {
                      nested: {
                        path: nestedDocType,
                        score_mode: "sum",
                        query: {
                          constant_score: {
                            filter: {
                              bool: {
                                filter: expect.arrayContaining([
                                  expect.termQuery(
                                    `${nestedDocType}.persons.id`,
                                    personId
                                  )
                                ])
                              }
                            },
                            boost: 1
                          }
                        },
                        inner_hits: {
                          _source: false
                        }
                      }
                    }
                  ]),
                  minimum_should_match: 0
                })
              }
            })
          );
        });
      });

      it("should add all nested document types queries to the must clause when all collaborationType filter values are true", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const shoulds: QueryDslQueryContainer[] = [];

        networkNestedDocTypes.forEach((nestedDocType) => {
          shoulds.push({
            nested: {
              path: nestedDocType,
              score_mode: "sum",
              query: {
                constant_score: {
                  filter: {
                    bool: {
                      filter: expect.arrayContaining([
                        expect.termQuery(
                          `${nestedDocType}.persons.id`,
                          personId
                        )
                      ])
                    }
                  },
                  boost: 1
                }
              },
              inner_hits: {
                _source: false
              }
            }
          });
        });

        const suppliedFilters = generateNetworkFilters();

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildFindNetworkQuery(
          request,
          [],
          undefined,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            from: page.offset,
            size: page.limit,
            _source_includes: SOURCE_FIELDS,
            query: {
              bool: expect.objectContaining({
                must: {
                  bool: {
                    should: shoulds,
                    minimum_should_match: 1
                  }
                }
              })
            }
          })
        );
      });

      it("should return null if the collaborationType filter is omitted from the request", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const suppliedFilters: NetworkFilters = {
          keyword: EMPTY_STRING
        };

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildFindNetworkQuery(
          request,
          [],
          undefined,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toBeNull();
      });

      it("should NOT include a nested document type in the must clause when that type is set to false in the collaborationType filter", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        networkNestedDocTypes.forEach((optionalNestedDocType) => {
          const shoulds: QueryDslQueryContainer[] = [];
          const collaborationType =
            nestedDocTypeToCollaborationType[optionalNestedDocType];
          const suppliedFilters = generateNetworkFilters(
            {},
            {
              [collaborationType]: false
            }
          );

          networkNestedDocTypes.forEach((nestedDocType) => {
            if (nestedDocType === optionalNestedDocType) {
              return;
            }

            shoulds.push({
              nested: {
                path: nestedDocType,
                score_mode: "sum",
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        filter: expect.arrayContaining([
                          expect.termQuery(
                            `${nestedDocType}.persons.id`,
                            personId
                          )
                        ])
                      }
                    },
                    boost: 1
                  }
                },
                inner_hits: {
                  _source: false
                }
              }
            });
          });

          const request = {
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH
          };

          const query = networkQueryBuilder.buildFindNetworkQuery(
            request,
            [],
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              from: page.offset,
              size: page.limit,
              _source_includes: SOURCE_FIELDS,
              query: {
                bool: expect.objectContaining({
                  must: {
                    bool: {
                      should: shoulds,
                      minimum_should_match: 1
                    }
                  }
                })
              }
            })
          );
        });
      });

      it("should add a range filter query when a minimum date range filter is supplied", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {
          min: faker.date.past().getSeconds()
        };
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const shoulds: QueryDslQueryContainer[] = [];

        networkNestedDocTypes.forEach((nestedDocType) => {
          const pathQualifiedDateField = `${nestedDocType}.${nestedDocs[nestedDocType].dateField}`;

          shoulds.push({
            nested: {
              path: nestedDocType,
              score_mode: "sum",
              query: {
                constant_score: {
                  filter: {
                    bool: {
                      filter: expect.arrayContaining([
                        expect.termQuery(
                          `${nestedDocType}.persons.id`,
                          personId
                        ),
                        {
                          bool: {
                            should: [
                              {
                                range: {
                                  [pathQualifiedDateField]: {
                                    gte: dateRange.min
                                  }
                                }
                              },
                              {
                                bool: {
                                  must_not: expect.fieldExists(
                                    pathQualifiedDateField
                                  )
                                }
                              }
                            ],
                            minimum_should_match: 1
                          }
                        }
                      ])
                    }
                  },
                  boost: 1
                }
              },
              inner_hits: {
                _source: false
              }
            }
          });
        });

        const suppliedFilters = generateNetworkFilters();

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildFindNetworkQuery(
          request,
          [],
          undefined,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            from: page.offset,
            size: page.limit,
            _source_includes: SOURCE_FIELDS,
            query: {
              bool: expect.objectContaining({
                must: {
                  bool: {
                    should: shoulds,
                    minimum_should_match: 1
                  }
                }
              })
            }
          })
        );
      });

      it("input with parsedQueryTree should add should clauses for trials/congress/publications/DRG_procedures/DRG_diagnoses with the parsed query, seperate from the should clauses used to find shared works", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Partial<
          Record<NetworkNestedDocPathForKeywordFilter, QueryDslQueryContainer>
        > = {
          trials: generateMockElasticsearchTermQuery(),
          publications: generateMockElasticsearchTermQuery(),
          congress: generateMockElasticsearchTermQuery(),
          DRG_diagnoses: generateMockElasticsearchTermQuery(),
          DRG_procedures: generateMockElasticsearchTermQuery()
        };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };
        const suppliedFilters = generateNetworkFilters();

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildFindNetworkQuery(
          request,
          [],
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: page.limit,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              must: {
                bool: expect.objectContaining({
                  must: {
                    bool: {
                      should: [
                        {
                          nested: {
                            path: "publications",
                            score_mode: "sum",
                            query: {
                              constant_score: {
                                filter: {
                                  bool: {
                                    filter: [
                                      expect.termQuery(
                                        "publications.persons.id",
                                        personId
                                      )
                                    ]
                                  }
                                },
                                boost: 1
                              }
                            },
                            inner_hits: {
                              _source: false
                            }
                          }
                        },
                        {
                          nested: {
                            path: "trials",
                            score_mode: "sum",
                            query: {
                              constant_score: {
                                filter: {
                                  bool: {
                                    filter: [
                                      expect.termQuery(
                                        "trials.persons.id",
                                        personId
                                      )
                                    ]
                                  }
                                },
                                boost: 1
                              }
                            },
                            inner_hits: {
                              _source: false
                            }
                          }
                        },
                        {
                          nested: {
                            path: "congress",
                            score_mode: "sum",
                            query: {
                              constant_score: {
                                filter: {
                                  bool: {
                                    filter: [
                                      expect.termQuery(
                                        "congress.persons.id",
                                        personId
                                      )
                                    ]
                                  }
                                },
                                boost: 1
                              }
                            },
                            inner_hits: {
                              _source: false
                            }
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  },
                  should: [
                    {
                      nested: {
                        path: "publications",
                        score_mode: "sum",
                        query: {
                          bool: {
                            filter: [pathSpecificQueries.publications]
                          }
                        }
                      }
                    },
                    {
                      nested: {
                        path: "trials",
                        score_mode: "sum",
                        query: {
                          bool: {
                            filter: [pathSpecificQueries.trials]
                          }
                        }
                      }
                    },
                    {
                      nested: {
                        path: "congress",
                        score_mode: "sum",
                        query: {
                          bool: {
                            filter: [pathSpecificQueries.congress]
                          }
                        }
                      }
                    },
                    {
                      nested: {
                        path: "DRG_diagnoses",
                        score_mode: "sum",
                        query: {
                          bool: {
                            filter: [pathSpecificQueries.DRG_diagnoses]
                          }
                        }
                      }
                    },
                    {
                      nested: {
                        path: "DRG_procedures",
                        score_mode: "sum",
                        query: {
                          bool: {
                            filter: [pathSpecificQueries.DRG_procedures]
                          }
                        }
                      }
                    }
                  ],
                  minimum_should_match: 1
                })
              }
            })
          },
          sort: expect.anything(),
          aggs: expect.anything()
        });

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          [
            "publications.publicationAbstract",
            "publications.keywords",
            "publications.title"
          ],
          expect.any(Function)
        );

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          [
            "trials.briefTitle",
            "trials.conditions",
            "trials.interventions",
            "trials.keywords",
            "trials.officialTitle",
            "trials.summary"
          ],
          expect.any(Function)
        );

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          ["congress.keywords", "congress.title"],
          expect.any(Function)
        );

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          ["DRG_diagnoses.codeAndDescription"],
          expect.any(Function)
        );

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          ["DRG_procedures.codeAndDescription"],
          expect.any(Function)
        );
      });

      describe("Brazilian claims disabled", () => {
        it("input with parsedQueryTree should add should clauses for trials/congress/publications/DRG_procedures/DRG_diagnoses with the parsed query, with a must_not clause to filter out Brazilian HCPs for the claims queries", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const pathSpecificQueries: Partial<
            Record<NetworkNestedDocPathForKeywordFilter, QueryDslQueryContainer>
          > = {
            trials: generateMockElasticsearchTermQuery(),
            publications: generateMockElasticsearchTermQuery(),
            congress: generateMockElasticsearchTermQuery(),
            DRG_diagnoses: generateMockElasticsearchTermQuery(),
            DRG_procedures: generateMockElasticsearchTermQuery()
          };

          const mockParseTreeToQueries =
            mockParseTreeToElasticsearchQueries(pathSpecificQueries);

          parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
            mockParseTreeToQueries
          );

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const parsedQueryTree = faker.datatype.string();
          const personId = faker.datatype.uuid();
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };
          const suppliedFilters = generateNetworkFilters();

          const request = {
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH
          };

          const query = networkQueryBuilder.buildFindNetworkQuery(
            request,
            [],
            parsedQueryTree,
            () => ENGLISH,
            generateFeatureFlags({
              enableBrazilianClaims: false
            })
          );

          expect(query).toEqual({
            index: configService.elasticPeopleIndex,
            from: page.offset,
            size: page.limit,
            _source_includes: SOURCE_FIELDS,
            query: {
              bool: expect.objectContaining({
                must: {
                  bool: expect.objectContaining({
                    must: expect.anything(),
                    should: [
                      {
                        nested: {
                          path: "publications",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.publications]
                            }
                          }
                        }
                      },
                      {
                        nested: {
                          path: "trials",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.trials]
                            }
                          }
                        }
                      },
                      {
                        nested: {
                          path: "congress",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.congress]
                            }
                          }
                        }
                      },
                      {
                        bool: {
                          must_not: [
                            expect.termsQuery("country_multi", ["Brazil"])
                          ],
                          must: [
                            {
                              nested: {
                                path: "DRG_diagnoses",
                                score_mode: "sum",
                                query: {
                                  bool: {
                                    filter: [pathSpecificQueries.DRG_diagnoses]
                                  }
                                }
                              }
                            }
                          ]
                        }
                      },
                      {
                        bool: {
                          must_not: [
                            expect.termsQuery("country_multi", ["Brazil"])
                          ],
                          must: [
                            {
                              nested: {
                                path: "DRG_procedures",
                                score_mode: "sum",
                                query: {
                                  bool: {
                                    filter: [pathSpecificQueries.DRG_procedures]
                                  }
                                }
                              }
                            }
                          ]
                        }
                      }
                    ],
                    minimum_should_match: 1
                  })
                }
              })
            },
            sort: expect.anything(),
            aggs: expect.anything()
          });

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            [
              "publications.publicationAbstract",
              "publications.keywords",
              "publications.title"
            ],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            [
              "trials.briefTitle",
              "trials.conditions",
              "trials.interventions",
              "trials.keywords",
              "trials.officialTitle",
              "trials.summary"
            ],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            ["congress.keywords", "congress.title"],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            ["DRG_diagnoses.codeAndDescription"],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            ["DRG_procedures.codeAndDescription"],
            expect.any(Function)
          );
        });
      });

      it("should include all location filters when values are supplied", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const countries = ["us", "ca", "au"];
        const regions = ["us|PA", "ca|QC", "au|NSW"];
        const cities = [
          "us|PA|Allegheny|Amish City",
          "ca|QC|Mauricie|Apologyville",
          "au|NSW|Booroondarra|Kangarootown"
        ];
        const postalCodes = [
          "us|PA|Allegheny|Amish City|17366",
          "ca|QC|Mauricie|Apologyville|G3N 1C9",
          "au|NSW|Booroondarra|Kangarootown|2213"
        ];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          country: countries,
          region: regions,
          city: cities,
          postalCode: postalCodes
        });

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildFindNetworkQuery(
          request,
          [],
          undefined,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            from: page.offset,
            size: page.limit,
            _source_includes: SOURCE_FIELDS,
            query: {
              bool: expect.objectContaining({
                filter: [
                  expect.termQuery("collaboratorIds", personId),
                  {
                    bool: {
                      must_not: expect.termQuery("id", personId)
                    }
                  },
                  expect.projectIdFilter(projectId),
                  {
                    nested: {
                      path: "affiliations",
                      query: {
                        bool: {
                          filter: [
                            expect.termsQuery(
                              "affiliations.institution.filters.country",
                              suppliedFilters.collaboratorLocation!.country!
                            ),
                            expect.termsQuery(
                              "affiliations.institution.filters.region",
                              suppliedFilters.collaboratorLocation!.region!
                            ),
                            expect.termsQuery(
                              "affiliations.institution.filters.city",
                              suppliedFilters.collaboratorLocation!.city!
                            ),
                            expect.termsQuery(
                              "affiliations.institution.filters.postal_code",
                              suppliedFilters.collaboratorLocation!.postalCode!
                            ),
                            expect.termQuery(
                              "affiliations.type",
                              "Work Affiliation"
                            ),
                            expect.termQuery("affiliations.isCurrent", true)
                          ]
                        }
                      }
                    }
                  }
                ]
              })
            }
          })
        );
      });

      describe("country", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should include terms query when country values are supplied as full country names", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = ["United States", "Canada", "Thailand"];
            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              parsedQueryTree,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.country",
                                  ["us", "ca", "th"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should convert cmn/jpn names to codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = ["中國台灣省", "中国台湾省", "中国", "日本"];
            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              parsedQueryTree,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.country",
                                  ["tw", "tw", "cn", "jp"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include country names that don't have known 2-character country codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = [
              "United States",
              "a value that is clearly not a country name",
              "Canada"
            ];
            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              parsedQueryTree,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.country",
                                  ["us", "ca"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include a nested affiliations filter when no country codes are supported", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = [
              "unknown country name 1",
              "unknown country name 2"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              parsedQueryTree,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId)
                    ]
                  })
                }
              })
            );
          });
        });
      });

      describe("region", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should include terms query with region name->code and corresponding country codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const regions = ["Pennsylvania", "Quebec"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                region: regions
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.region",
                                  ["us|PA", "ca|QC"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should skip regions with unknown region names", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const regions = ["Pennsylvania", "unknown region name", "Manitoba"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                region: regions
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.region",
                                  ["us|PA", "ca|MB"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include a region terms filter when none have known names", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const regions = ["unknown region name 1", "unknown region name 2"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                region: regions
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId)
                    ]
                  })
                }
              })
            );
          });
        });
      });

      describe("city", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should format 4-field cities as pipe-delimited country code + region + county + city", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, PA, United States",
              "Montreal, Montreal Region, QC, Canada"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  [
                                    "us|PA|Lancaster County|Lancaster",
                                    "ca|QC|Montreal Region|Montreal"
                                  ]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should convert full region names to codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, Pennsylvania, United States",
              "Montreal, Montreal Region, Quebec, Canada"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  [
                                    "us|PA|Lancaster County|Lancaster",
                                    "ca|QC|Montreal Region|Montreal"
                                  ]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should convert to 2 values, one with a blank county and the other with a blank region, when filter value is in the ambiguous 3-field format", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, PA, United States",
              "Montreal, QC, Canada"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  [
                                    "us|PA||Lancaster",
                                    "us||PA|Lancaster",
                                    "ca|QC||Montreal",
                                    "ca||QC|Montreal"
                                  ]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should use a blank region and county name when the filter value is 2-field format", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = ["Berlin, Germany"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  ["de|||Berlin"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should use the region name as supplied when abbreviation is not available for region", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = ["Asunción, Asunción, Capital District, Paraguay"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  ["py|Capital District|Asunción|Asunción"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include cities with unknown countries", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, PA, United States",
              "Montreal, Montreal Region, QC, unknown country name"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  ["us|PA|Lancaster County|Lancaster"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should ignore filter values with fewer than 2 fields", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, PA, United States",
              "Pittsburgh, PA, United States",
              "New York, United States",
              "Montreal"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  [
                                    "us|PA|Lancaster County|Lancaster",
                                    "us|PA||Pittsburgh",
                                    "us||PA|Pittsburgh",
                                    "us|||New York"
                                  ]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include a city terms filter when no filter values are parseable", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "city name, region code, unknown country name 1",
              "unqualified city name"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId)
                    ]
                  })
                }
              })
            );
          });
        });
      });

      describe("postal code", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should use a regex filter when postal codes are not already pipe-delimited", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const zipCode1 = faker.address.zipCode();
            const zipCode2 = faker.address.zipCode();

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                postalCode: [zipCode1, zipCode2]
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                {
                                  regexp: {
                                    "affiliations.institution.filters.postal_code":
                                      {
                                        value: `.+\\|(${zipCode1}|${zipCode2})`
                                      }
                                  }
                                },
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include empty string values from non-pipe-delimited filter values", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const zipCode1 = faker.address.zipCode();
            const zipCode2 = faker.address.zipCode();

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                postalCode: [zipCode1, EMPTY_STRING, zipCode2]
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                {
                                  regexp: {
                                    "affiliations.institution.filters.postal_code":
                                      {
                                        value: `.+\\|(${zipCode1}|${zipCode2})`
                                      }
                                  }
                                },
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include a postal_code terms filter when no filter values are parseable", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                postalCode: []
              }
            );

            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      expect.termQuery("collaboratorIds", personId),
                      {
                        bool: {
                          must_not: expect.termQuery("id", personId)
                        }
                      },
                      expect.projectIdFilter(projectId)
                    ]
                  })
                }
              })
            );
          });
        });
      });

      describe("sorting", () => {
        it("should add all nested document types queries to the must clause with a boost of 1 when no sortBy is passed in", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personId = faker.datatype.uuid();
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          const shoulds: QueryDslQueryContainer[] = [];

          networkNestedDocTypes.forEach((nestedDocType) => {
            shoulds.push({
              nested: {
                path: nestedDocType,
                score_mode: "sum",
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        filter: expect.arrayContaining([
                          expect.termQuery(
                            `${nestedDocType}.persons.id`,
                            personId
                          )
                        ])
                      }
                    },
                    boost: 1
                  }
                },
                inner_hits: {
                  _source: false
                }
              }
            });
          });

          const suppliedFilters = generateNetworkFilters();

          const request = {
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH
          };

          const query = networkQueryBuilder.buildFindNetworkQuery(
            request,
            [],
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              from: page.offset,
              size: page.limit,
              _source_includes: SOURCE_FIELDS,
              query: {
                bool: expect.objectContaining({
                  must: {
                    bool: {
                      should: shoulds,
                      minimum_should_match: 1
                    }
                  }
                })
              },
              sort: SORT_TIEBREAKER
            })
          );
        });

        it("should add all nested document types queries to the must clause with a boost of 1 when sorting by TOTAL_SHARED_WORKS", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personId = faker.datatype.uuid();
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          const shoulds: QueryDslQueryContainer[] = [];

          networkNestedDocTypes.forEach((nestedDocType) => {
            shoulds.push({
              nested: {
                path: nestedDocType,
                score_mode: "sum",
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        filter: expect.arrayContaining([
                          expect.termQuery(
                            `${nestedDocType}.persons.id`,
                            personId
                          )
                        ])
                      }
                    },
                    boost: 1
                  }
                },
                inner_hits: {
                  _source: false
                }
              }
            });
          });

          const suppliedFilters = generateNetworkFilters();

          const request = {
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH,
            sortBy: NetworkSortBy.TOTAL_SHARED_WORKS
          };

          const query = networkQueryBuilder.buildFindNetworkQuery(
            request,
            [],
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              from: page.offset,
              size: page.limit,
              _source_includes: SOURCE_FIELDS,
              query: {
                bool: expect.objectContaining({
                  must: {
                    bool: {
                      should: shoulds,
                      minimum_should_match: 1
                    }
                  }
                })
              },
              sort: SORT_TIEBREAKER
            })
          );
        });

        it("should add all nested document types queries to the must clause with a boost of 0 when sorting by an option that is not one of TOTAL_SHARED_WORKS, TRIALS, CONGRESSES, PUBLICATIONS", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personId = faker.datatype.uuid();
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          const shoulds: QueryDslQueryContainer[] = [];

          networkNestedDocTypes.forEach((nestedDocType) => {
            shoulds.push({
              nested: {
                path: nestedDocType,
                score_mode: "sum",
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        filter: expect.arrayContaining([
                          expect.termQuery(
                            `${nestedDocType}.persons.id`,
                            personId
                          )
                        ])
                      }
                    },
                    boost: 0
                  }
                },
                inner_hits: {
                  _source: false
                }
              }
            });
          });

          for (const sortBy of Object.values(NetworkSortBy)) {
            const sortByValue = Number(sortBy);
            if (
              isNaN(sortByValue) ||
              sortByValue === NetworkSortBy.TOTAL_SHARED_WORKS ||
              sortByValue === NetworkSortBy.TRIALS ||
              sortByValue === NetworkSortBy.CONGRESSES ||
              sortByValue === NetworkSortBy.PUBLICATIONS
            ) {
              continue;
            }

            const suppliedFilters = generateNetworkFilters();
            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH,
              sortBy: sortByValue
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    must: {
                      bool: {
                        should: shoulds,
                        minimum_should_match: 1
                      }
                    }
                  })
                }
              })
            );
          }
        });

        it("should only add a boost of 1 for the respective nested document when sorting by one of TRIALS, CONGRESSES, PUBLICATIONS", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personId = faker.datatype.uuid();
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          for (const sortBy of Object.values(NetworkSortBy)) {
            const sortByValue = Number(sortBy);
            if (
              isNaN(sortByValue) ||
              sortByValue === NetworkSortBy.TOTAL_SHARED_WORKS ||
              sortByValue === NetworkSortBy.NAME ||
              sortByValue === NetworkSortBy.AFFILIATIONS
            ) {
              continue;
            }

            const shoulds: QueryDslQueryContainer[] = [];

            networkNestedDocTypes.forEach((nestedDocType) => {
              let boost = 0;
              switch (sortByValue) {
                case NetworkSortBy.TRIALS:
                  boost = nestedDocType === "trials" ? 1 : 0;
                  break;
                case NetworkSortBy.CONGRESSES:
                  boost = nestedDocType === "congress" ? 1 : 0;
                  break;
                case NetworkSortBy.PUBLICATIONS:
                  boost = nestedDocType === "publications" ? 1 : 0;
                  break;
              }

              shoulds.push({
                nested: {
                  path: nestedDocType,
                  score_mode: "sum",
                  query: {
                    constant_score: {
                      filter: {
                        bool: {
                          filter: expect.arrayContaining([
                            expect.termQuery(
                              `${nestedDocType}.persons.id`,
                              personId
                            )
                          ])
                        }
                      },
                      boost
                    }
                  },
                  inner_hits: {
                    _source: false
                  }
                }
              });
            });

            const suppliedFilters = generateNetworkFilters();
            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH,
              sortBy: sortByValue
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    must: {
                      bool: {
                        should: shoulds,
                        minimum_should_match: 1
                      }
                    }
                  })
                },
                sort: SORT_TIEBREAKER
              })
            );
          }
        });

        it("should sort by collaborator first and last name in alphabetical order when sortBy is set to NAME", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personId = faker.datatype.uuid();
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          const suppliedFilters = generateNetworkFilters();

          for (const language of LANGUAGES_SUPPORTED) {
            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language,
              sortBy: NetworkSortBy.NAME
            };

            const query = networkQueryBuilder.buildFindNetworkQuery(
              request,
              [],
              undefined,
              () => language,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: page.limit,
                _source_includes: SOURCE_FIELDS,
                sort: expect.arrayContaining([
                  {
                    [`firstName_${language}.sort`]: ASCENDING
                  },
                  {
                    [`lastName_${language}.sort`]: ASCENDING
                  },
                  ...SORT_TIEBREAKER
                ])
              })
            );
          }
        });

        it("should sort by shared affiliation names in alphabetical order when sortBy is set to AFFILIATIONS", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personId = faker.datatype.uuid();
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          const suppliedFilters = generateNetworkFilters();

          const request = {
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH,
            sortBy: NetworkSortBy.AFFILIATIONS
          };

          const sharedAffiliations = Array.from(
            { length: faker.datatype.number({ max: 10 }) },
            faker.datatype.uuid
          );

          const query = networkQueryBuilder.buildFindNetworkQuery(
            request,
            sharedAffiliations,
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              from: page.offset,
              size: page.limit,
              _source_includes: SOURCE_FIELDS,
              sort: expect.arrayContaining([
                {
                  [`affiliations.institution.name.keyword`]: {
                    order: ASCENDING,
                    nested: {
                      path: "affiliations",
                      filter: {
                        bool: {
                          filter: expect.termsQuery(
                            "affiliations.institution.id",
                            sharedAffiliations
                          )
                        }
                      }
                    }
                  }
                },
                ...SORT_TIEBREAKER
              ])
            })
          );
        });
      });
    });
  });

  describe("buildBulkFindNetworkQuery", () => {
    it("should return null if all collaboration types need to be filtered out", () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personIds = [faker.datatype.uuid()];
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFiltersAllFalse = generateNetworkFilters({
        collaborationType: {
          trials: false,
          congresses: false,
          publications: false
        }
      });
      const suppliedFiltersEmpty: NetworkFilters = {
        collaborationType: {}
      };
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const requestWithAllFalse = {
        personIds,
        dateRange,
        page,
        suppliedFilters: suppliedFiltersAllFalse,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const requestWithEmpty = {
        personIds,
        dateRange,
        page,
        suppliedFilters: suppliedFiltersEmpty,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const queryAllFalse = networkQueryBuilder.buildBulkFindNetworkQuery(
        requestWithAllFalse,
        [],
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );
      const queryEmpty = networkQueryBuilder.buildBulkFindNetworkQuery(
        requestWithEmpty,
        [],
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );
      expect(queryAllFalse).toBeNull();
      expect(queryEmpty).toBeNull();
    });

    it("should filter out the HCP from the request, but filter on the HCP's ID as a collaborator and on project ID", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personIds = [faker.datatype.uuid(), faker.datatype.uuid()];
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personIds,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const query = networkQueryBuilder.buildBulkFindNetworkQuery(
        request,
        [],
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: 0,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                {
                  terms: {
                    collaboratorIds: personIds
                  }
                },
                {
                  bool: {
                    must_not: {
                      bool: expect.objectContaining({
                        should: personIds.map((personId) => {
                          const otherPersonIds = personIds.filter(
                            (id) => id !== personId
                          );
                          return {
                            bool: {
                              filter: [
                                { term: { id: personId } },
                                {
                                  bool: {
                                    must_not: {
                                      terms: { collaboratorIds: otherPersonIds }
                                    }
                                  }
                                }
                              ]
                            }
                          };
                        })
                      })
                    }
                  }
                }
              ])
            })
          }
        })
      );
    });

    it("should aggregate trials/publications/congress/total collaborator docs", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personIds = [faker.datatype.uuid()];
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personIds,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const query = networkQueryBuilder.buildBulkFindNetworkQuery(
        request,
        [],
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: 0,
          _source_includes: SOURCE_FIELDS,
          aggs: {
            trials: {
              nested: {
                path: "trials"
              },
              aggs: {
                nested: {
                  filter: {
                    bool: {
                      filter: [
                        {
                          terms: {
                            "trials.persons.id": personIds
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    per_person: {
                      terms: {
                        field: "trials.persons.id",
                        size: personIds.length,
                        include: personIds
                      },
                      aggs: {
                        people: {
                          reverse_nested: {}
                        }
                      }
                    }
                  }
                }
              }
            },
            publications: {
              nested: {
                path: "publications"
              },
              aggs: {
                nested: {
                  filter: {
                    bool: {
                      filter: [
                        {
                          terms: {
                            "publications.persons.id": personIds
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    per_person: {
                      terms: {
                        field: "publications.persons.id",
                        size: personIds.length,
                        include: personIds
                      },
                      aggs: {
                        people: {
                          reverse_nested: {}
                        }
                      }
                    }
                  }
                }
              }
            },
            congress: {
              nested: {
                path: "congress"
              },
              aggs: {
                nested: {
                  filter: {
                    bool: {
                      filter: [
                        {
                          terms: {
                            "congress.persons.id": personIds
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    per_person: {
                      terms: {
                        field: "congress.persons.id",
                        size: personIds.length,
                        include: personIds
                      },
                      aggs: {
                        people: {
                          reverse_nested: {}
                        }
                      }
                    }
                  }
                }
              }
            },
            total_collaborators: {
              terms: {
                field: "collaboratorIds",
                include: personIds,
                size: personIds.length
              }
            }
          }
        })
      );
    });

    it("should add an optional should clause labeled 'sharedAffiliations' with a terms query using the elements in affiliatedInstitutionIds when it's non-empty", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personIds = [faker.datatype.uuid()];
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personIds,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const affiliatedInstitutionIds = Array.from({ length: 10 }, () => {
        return faker.datatype.uuid();
      });

      const query = networkQueryBuilder.buildBulkFindNetworkQuery(
        request,
        affiliatedInstitutionIds,
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: 0,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              should: expect.arrayContaining([
                {
                  nested: {
                    path: "affiliations",
                    query: {
                      bool: {
                        filter: expect.termsQuery(
                          "affiliations.institution.id",
                          affiliatedInstitutionIds
                        )
                      }
                    },
                    inner_hits: {
                      _source: {
                        includes: [
                          "affiliations.type",
                          "affiliations.isCurrent",
                          "affiliations.institution.id",
                          "affiliations.institution.ultimateParentId",
                          "affiliations.insitution.name",
                          "affiliations.institution.address",
                          "affiliations.institution.nameTranslations",
                          "affiliations.institution.addressTranslations"
                        ]
                      },
                      name: "sharedAffiliations",
                      size: 10
                    }
                  }
                }
              ]),
              minimum_should_match: 0
            })
          }
        })
      );
    });

    it("should NOT add a 'sharedAffiliations' labled query when affiliatedInstitutionIds is empty", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personIds = [faker.datatype.uuid()];
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personIds,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const affiliatedInstitutionIds: string[] = [];

      const query = networkQueryBuilder.buildBulkFindNetworkQuery(
        request,
        affiliatedInstitutionIds,
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: 0,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              should: expect.not.arrayContaining([
                {
                  nested: {
                    path: "affiliations",
                    query: expect.anything(),
                    inner_hits: expect.objectContaining({
                      name: "sharedAffiliations"
                    })
                  }
                }
              ]),
              minimum_should_match: 0
            })
          }
        })
      );
    });

    it("should add an optional should clause labeled 'locations' to find the locations of collaborators from current work affiliations", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personIds = [faker.datatype.uuid()];
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personIds,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const query = networkQueryBuilder.buildBulkFindNetworkQuery(
        request,
        [],
        parsedQueryTree,
        () => ENGLISH,
        FEATURE_FLAGS
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: 0,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              should: [
                {
                  nested: {
                    path: "affiliations",
                    query: {
                      bool: {
                        filter: expect.arrayContaining([
                          expect.termQuery("affiliations.isCurrent", true),
                          expect.termQuery(
                            "affiliations.type",
                            "Work Affiliation"
                          )
                        ])
                      }
                    },
                    inner_hits: {
                      _source: {
                        includes: [
                          "affiliations.type",
                          "affiliations.isCurrent",
                          "affiliations.institution.id",
                          "affiliations.institution.ultimateParentId",
                          "affiliations.insitution.name",
                          "affiliations.institution.address",
                          "affiliations.institution.nameTranslations",
                          "affiliations.institution.addressTranslations"
                        ]
                      },
                      name: "locations",
                      size: 10
                    }
                  }
                }
              ],
              minimum_should_match: 0
            })
          }
        })
      );
    });

    describe("filters", () => {
      it("should add nested document type as an optional should query if that specific type is set to false in the collaborationType filter", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personIds = [faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };
        const parsedQueryTree = faker.datatype.string();

        networkNestedDocTypes.forEach((nestedDocType) => {
          const collaborationType =
            nestedDocTypeToCollaborationType[nestedDocType];
          const suppliedFilters = generateNetworkFilters(
            {},
            {
              [collaborationType]: false
            }
          );

          const request = {
            personIds,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH
          };

          const query = networkQueryBuilder.buildBulkFindNetworkQuery(
            request,
            [],
            parsedQueryTree,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              from: page.offset,
              size: 0,
              _source_includes: SOURCE_FIELDS,
              query: {
                bool: expect.objectContaining({
                  should: expect.arrayContaining([
                    {
                      nested: {
                        path: "affiliations",
                        query: {
                          bool: {
                            filter: expect.arrayContaining([
                              expect.termQuery("affiliations.isCurrent", true),
                              expect.termQuery(
                                "affiliations.type",
                                "Work Affiliation"
                              )
                            ])
                          }
                        },
                        inner_hits: {
                          _source: {
                            includes: [
                              "affiliations.type",
                              "affiliations.isCurrent",
                              "affiliations.institution.id",
                              "affiliations.institution.ultimateParentId",
                              "affiliations.insitution.name",
                              "affiliations.institution.address",
                              "affiliations.institution.nameTranslations",
                              "affiliations.institution.addressTranslations"
                            ]
                          },
                          name: "locations",
                          size: 10
                        }
                      }
                    },
                    {
                      nested: {
                        path: nestedDocType,
                        score_mode: "sum",
                        query: {
                          constant_score: {
                            filter: {
                              bool: {
                                filter: expect.arrayContaining([
                                  {
                                    terms: {
                                      [`${nestedDocType}.persons.id`]: personIds
                                    }
                                  }
                                ])
                              }
                            },
                            boost: 1
                          }
                        },
                        inner_hits: {
                          _source: false
                        }
                      }
                    }
                  ]),
                  minimum_should_match: 0
                })
              }
            })
          );
        });
      });

      it("should add all nested document types queries to the must clause when all collaborationType filter values are true", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personIds = [faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const shoulds: QueryDslQueryContainer[] = [];

        networkNestedDocTypes.forEach((nestedDocType) => {
          shoulds.push({
            nested: {
              path: nestedDocType,
              score_mode: "sum",
              query: {
                constant_score: {
                  filter: {
                    bool: {
                      filter: expect.arrayContaining([
                        {
                          terms: {
                            [`${nestedDocType}.persons.id`]: personIds
                          }
                        }
                      ])
                    }
                  },
                  boost: 1
                }
              },
              inner_hits: {
                _source: false
              }
            }
          });
        });

        const suppliedFilters = generateNetworkFilters();

        const request = {
          personIds,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildBulkFindNetworkQuery(
          request,
          [],
          undefined,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            from: page.offset,
            size: 0,
            _source_includes: SOURCE_FIELDS,
            query: {
              bool: expect.objectContaining({
                must: {
                  bool: {
                    should: shoulds,
                    minimum_should_match: 1
                  }
                }
              })
            }
          })
        );
      });

      it("should return null if the collaborationType filter is omitted from the request", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personIds = [faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const suppliedFilters: NetworkFilters = {
          keyword: EMPTY_STRING
        };

        const request = {
          personIds,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildBulkFindNetworkQuery(
          request,
          [],
          undefined,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toBeNull();
      });

      it("should NOT include a nested document type in the must clause when that type is set to false in the collaborationType filter", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personIds = [faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        networkNestedDocTypes.forEach((optionalNestedDocType) => {
          const shoulds: QueryDslQueryContainer[] = [];
          const collaborationType =
            nestedDocTypeToCollaborationType[optionalNestedDocType];
          const suppliedFilters = generateNetworkFilters(
            {},
            {
              [collaborationType]: false
            }
          );

          networkNestedDocTypes.forEach((nestedDocType) => {
            if (nestedDocType === optionalNestedDocType) {
              return;
            }

            shoulds.push({
              nested: {
                path: nestedDocType,
                score_mode: "sum",
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        filter: expect.arrayContaining([
                          {
                            terms: {
                              [`${nestedDocType}.persons.id`]: personIds
                            }
                          }
                        ])
                      }
                    },
                    boost: 1
                  }
                },
                inner_hits: {
                  _source: false
                }
              }
            });
          });

          const request = {
            personIds,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH
          };

          const query = networkQueryBuilder.buildBulkFindNetworkQuery(
            request,
            [],
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              from: page.offset,
              size: 0,
              _source_includes: SOURCE_FIELDS,
              query: {
                bool: expect.objectContaining({
                  must: {
                    bool: {
                      should: shoulds,
                      minimum_should_match: 1
                    }
                  }
                })
              }
            })
          );
        });
      });

      it("should add a range filter query when a minimum date range filter is supplied", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personIds = [faker.datatype.uuid()];
        const dateRange = {
          min: faker.date.past().getSeconds()
        };
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const shoulds: QueryDslQueryContainer[] = [];

        networkNestedDocTypes.forEach((nestedDocType) => {
          const pathQualifiedDateField = `${nestedDocType}.${nestedDocs[nestedDocType].dateField}`;

          shoulds.push({
            nested: {
              path: nestedDocType,
              score_mode: "sum",
              query: {
                constant_score: {
                  filter: {
                    bool: {
                      filter: expect.arrayContaining([
                        {
                          terms: {
                            [`${nestedDocType}.persons.id`]: personIds
                          }
                        },
                        {
                          bool: {
                            should: [
                              {
                                range: {
                                  [pathQualifiedDateField]: {
                                    gte: dateRange.min
                                  }
                                }
                              },
                              {
                                bool: {
                                  must_not: expect.fieldExists(
                                    pathQualifiedDateField
                                  )
                                }
                              }
                            ],
                            minimum_should_match: 1
                          }
                        }
                      ])
                    }
                  },
                  boost: 1
                }
              },
              inner_hits: {
                _source: false
              }
            }
          });
        });

        const suppliedFilters = generateNetworkFilters();

        const request = {
          personIds,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildBulkFindNetworkQuery(
          request,
          [],
          undefined,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            from: page.offset,
            size: 0,
            _source_includes: SOURCE_FIELDS,
            query: {
              bool: expect.objectContaining({
                must: {
                  bool: {
                    should: shoulds,
                    minimum_should_match: 1
                  }
                }
              })
            }
          })
        );
      });

      it("input with parsedQueryTree should add should clauses for trials/congress/publications/DRG_procedures/DRG_diagnoses with the parsed query, seperate from the should clauses used to find shared works", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Partial<
          Record<NetworkNestedDocPathForKeywordFilter, QueryDslQueryContainer>
        > = {
          trials: generateMockElasticsearchTermQuery(),
          publications: generateMockElasticsearchTermQuery(),
          congress: generateMockElasticsearchTermQuery(),
          DRG_diagnoses: generateMockElasticsearchTermQuery(),
          DRG_procedures: generateMockElasticsearchTermQuery()
        };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personIds = [faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };
        const suppliedFilters = generateNetworkFilters();

        const request = {
          personIds,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildBulkFindNetworkQuery(
          request,
          [],
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual({
          index: configService.elasticPeopleIndex,
          from: page.offset,
          size: 0,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              must: {
                bool: expect.objectContaining({
                  must: {
                    bool: {
                      should: [
                        {
                          nested: {
                            path: "publications",
                            score_mode: "sum",
                            query: {
                              constant_score: {
                                filter: {
                                  bool: {
                                    filter: [
                                      {
                                        terms: {
                                          ["publications.persons.id"]: personIds
                                        }
                                      }
                                    ]
                                  }
                                },
                                boost: 1
                              }
                            },
                            inner_hits: {
                              _source: false
                            }
                          }
                        },
                        {
                          nested: {
                            path: "trials",
                            score_mode: "sum",
                            query: {
                              constant_score: {
                                filter: {
                                  bool: {
                                    filter: [
                                      {
                                        terms: {
                                          ["trials.persons.id"]: personIds
                                        }
                                      }
                                    ]
                                  }
                                },
                                boost: 1
                              }
                            },
                            inner_hits: {
                              _source: false
                            }
                          }
                        },
                        {
                          nested: {
                            path: "congress",
                            score_mode: "sum",
                            query: {
                              constant_score: {
                                filter: {
                                  bool: {
                                    filter: [
                                      {
                                        terms: {
                                          ["congress.persons.id"]: personIds
                                        }
                                      }
                                    ]
                                  }
                                },
                                boost: 1
                              }
                            },
                            inner_hits: {
                              _source: false
                            }
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  },
                  should: [
                    {
                      nested: {
                        path: "publications",
                        score_mode: "sum",
                        query: {
                          bool: {
                            filter: [pathSpecificQueries.publications]
                          }
                        }
                      }
                    },
                    {
                      nested: {
                        path: "trials",
                        score_mode: "sum",
                        query: {
                          bool: {
                            filter: [pathSpecificQueries.trials]
                          }
                        }
                      }
                    },
                    {
                      nested: {
                        path: "congress",
                        score_mode: "sum",
                        query: {
                          bool: {
                            filter: [pathSpecificQueries.congress]
                          }
                        }
                      }
                    },
                    {
                      nested: {
                        path: "DRG_diagnoses",
                        score_mode: "sum",
                        query: {
                          bool: {
                            filter: [pathSpecificQueries.DRG_diagnoses]
                          }
                        }
                      }
                    },
                    {
                      nested: {
                        path: "DRG_procedures",
                        score_mode: "sum",
                        query: {
                          bool: {
                            filter: [pathSpecificQueries.DRG_procedures]
                          }
                        }
                      }
                    }
                  ],
                  minimum_should_match: 1
                })
              }
            })
          },
          sort: expect.anything(),
          aggs: expect.anything()
        });

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          [
            "publications.publicationAbstract",
            "publications.keywords",
            "publications.title"
          ],
          expect.any(Function)
        );

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          [
            "trials.briefTitle",
            "trials.conditions",
            "trials.interventions",
            "trials.keywords",
            "trials.officialTitle",
            "trials.summary"
          ],
          expect.any(Function)
        );

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          ["congress.keywords", "congress.title"],
          expect.any(Function)
        );

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          ["DRG_diagnoses.codeAndDescription"],
          expect.any(Function)
        );

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          ["DRG_procedures.codeAndDescription"],
          expect.any(Function)
        );
      });

      describe("Brazilian claims disabled", () => {
        it("input with parsedQueryTree should add should clauses for trials/congress/publications/DRG_procedures/DRG_diagnoses with the parsed query, with a must_not clause to filter out Brazilian HCPs for the claims queries", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const pathSpecificQueries: Partial<
            Record<NetworkNestedDocPathForKeywordFilter, QueryDslQueryContainer>
          > = {
            trials: generateMockElasticsearchTermQuery(),
            publications: generateMockElasticsearchTermQuery(),
            congress: generateMockElasticsearchTermQuery(),
            DRG_diagnoses: generateMockElasticsearchTermQuery(),
            DRG_procedures: generateMockElasticsearchTermQuery()
          };

          const mockParseTreeToQueries =
            mockParseTreeToElasticsearchQueries(pathSpecificQueries);

          parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
            mockParseTreeToQueries
          );

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const parsedQueryTree = faker.datatype.string();
          const personIds = [faker.datatype.uuid()];
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };
          const suppliedFilters = generateNetworkFilters();

          const request = {
            personIds,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH
          };

          const query = networkQueryBuilder.buildBulkFindNetworkQuery(
            request,
            [],
            parsedQueryTree,
            () => ENGLISH,
            generateFeatureFlags({
              enableBrazilianClaims: false
            })
          );

          expect(query).toEqual({
            index: configService.elasticPeopleIndex,
            from: page.offset,
            size: 0,
            _source_includes: SOURCE_FIELDS,
            query: {
              bool: expect.objectContaining({
                must: {
                  bool: expect.objectContaining({
                    must: expect.anything(),
                    should: [
                      {
                        nested: {
                          path: "publications",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.publications]
                            }
                          }
                        }
                      },
                      {
                        nested: {
                          path: "trials",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.trials]
                            }
                          }
                        }
                      },
                      {
                        nested: {
                          path: "congress",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.congress]
                            }
                          }
                        }
                      },
                      {
                        bool: {
                          must_not: [
                            expect.termsQuery("country_multi", ["Brazil"])
                          ],
                          must: [
                            {
                              nested: {
                                path: "DRG_diagnoses",
                                score_mode: "sum",
                                query: {
                                  bool: {
                                    filter: [pathSpecificQueries.DRG_diagnoses]
                                  }
                                }
                              }
                            }
                          ]
                        }
                      },
                      {
                        bool: {
                          must_not: [
                            expect.termsQuery("country_multi", ["Brazil"])
                          ],
                          must: [
                            {
                              nested: {
                                path: "DRG_procedures",
                                score_mode: "sum",
                                query: {
                                  bool: {
                                    filter: [pathSpecificQueries.DRG_procedures]
                                  }
                                }
                              }
                            }
                          ]
                        }
                      }
                    ],
                    minimum_should_match: 1
                  })
                }
              })
            },
            sort: expect.anything(),
            aggs: expect.anything()
          });

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            [
              "publications.publicationAbstract",
              "publications.keywords",
              "publications.title"
            ],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            [
              "trials.briefTitle",
              "trials.conditions",
              "trials.interventions",
              "trials.keywords",
              "trials.officialTitle",
              "trials.summary"
            ],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            ["congress.keywords", "congress.title"],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            ["DRG_diagnoses.codeAndDescription"],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            ["DRG_procedures.codeAndDescription"],
            expect.any(Function)
          );
        });
      });

      it("should include all location filters when values are supplied", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personIds = [faker.datatype.uuid(), faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const countries = ["us", "ca", "au"];
        const regions = ["us|PA", "ca|QC", "au|NSW"];
        const cities = [
          "us|PA|Allegheny|Amish City",
          "ca|QC|Mauricie|Apologyville",
          "au|NSW|Booroondarra|Kangarootown"
        ];
        const postalCodes = [
          "us|PA|Allegheny|Amish City|17366",
          "ca|QC|Mauricie|Apologyville|G3N 1C9",
          "au|NSW|Booroondarra|Kangarootown|2213"
        ];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          country: countries,
          region: regions,
          city: cities,
          postalCode: postalCodes
        });

        const request = {
          personIds,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildBulkFindNetworkQuery(
          request,
          [],
          undefined,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            from: page.offset,
            size: 0,
            _source_includes: SOURCE_FIELDS,
            query: {
              bool: expect.objectContaining({
                filter: [
                  {
                    terms: {
                      collaboratorIds: personIds
                    }
                  },
                  {
                    bool: {
                      must_not: {
                        bool: {
                          should: personIds.map((personId) => {
                            // Exclude the current personId to get the list of other personIds
                            const otherPersonIds = personIds.filter(
                              (id) => id !== personId
                            );
                            return {
                              bool: {
                                filter: [
                                  { term: { id: personId } },
                                  {
                                    bool: {
                                      must_not: {
                                        terms: {
                                          collaboratorIds: otherPersonIds
                                        }
                                      }
                                    }
                                  }
                                ]
                              }
                            };
                          }),
                          minimum_should_match: 1
                        }
                      }
                    }
                  },
                  expect.projectIdFilter(projectId),
                  {
                    nested: {
                      path: "affiliations",
                      query: {
                        bool: {
                          filter: [
                            expect.termsQuery(
                              "affiliations.institution.filters.country",
                              suppliedFilters.collaboratorLocation!.country!
                            ),
                            expect.termsQuery(
                              "affiliations.institution.filters.region",
                              suppliedFilters.collaboratorLocation!.region!
                            ),
                            expect.termsQuery(
                              "affiliations.institution.filters.city",
                              suppliedFilters.collaboratorLocation!.city!
                            ),
                            expect.termsQuery(
                              "affiliations.institution.filters.postal_code",
                              suppliedFilters.collaboratorLocation!.postalCode!
                            ),
                            expect.termQuery(
                              "affiliations.type",
                              "Work Affiliation"
                            ),
                            expect.termQuery("affiliations.isCurrent", true)
                          ]
                        }
                      }
                    }
                  }
                ]
              })
            }
          })
        );
      });

      describe("country", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should include terms query when country values are supplied as full country names", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = ["United States", "Canada", "Thailand"];
            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              parsedQueryTree,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.country",
                                  ["us", "ca", "th"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should convert cmn/jpn names to codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = ["中國台灣省", "中国台湾省", "中国", "日本"];
            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              parsedQueryTree,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.country",
                                  ["tw", "tw", "cn", "jp"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include country names that don't have known 2-character country codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = [
              "United States",
              "a value that is clearly not a country name",
              "Canada"
            ];
            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              parsedQueryTree,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.country",
                                  ["us", "ca"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include a nested affiliations filter when no country codes are supported", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = [
              "unknown country name 1",
              "unknown country name 2"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              parsedQueryTree,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId)
                    ]
                  })
                }
              })
            );
          });
        });
      });

      describe("region", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should include terms query with region name->code and corresponding country codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const regions = ["Pennsylvania", "Quebec"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                region: regions
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.region",
                                  ["us|PA", "ca|QC"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should skip regions with unknown region names", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const regions = ["Pennsylvania", "unknown region name", "Manitoba"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                region: regions
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.region",
                                  ["us|PA", "ca|MB"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include a region terms filter when none have known names", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const regions = ["unknown region name 1", "unknown region name 2"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                region: regions
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId)
                    ]
                  })
                }
              })
            );
          });
        });
      });

      describe("city", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should format 4-field cities as pipe-delimited country code + region + county + city", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, PA, United States",
              "Montreal, Montreal Region, QC, Canada"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  [
                                    "us|PA|Lancaster County|Lancaster",
                                    "ca|QC|Montreal Region|Montreal"
                                  ]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should convert full region names to codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, Pennsylvania, United States",
              "Montreal, Montreal Region, Quebec, Canada"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  [
                                    "us|PA|Lancaster County|Lancaster",
                                    "ca|QC|Montreal Region|Montreal"
                                  ]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should convert to 2 values, one with a blank county and the other with a blank region, when filter value is in the ambiguous 3-field format", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, PA, United States",
              "Montreal, QC, Canada"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  [
                                    "us|PA||Lancaster",
                                    "us||PA|Lancaster",
                                    "ca|QC||Montreal",
                                    "ca||QC|Montreal"
                                  ]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should use a blank region and county name when the filter value is 2-field format", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = ["Berlin, Germany"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  ["de|||Berlin"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should use the region name as supplied when abbreviation is not available for region", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = ["Asunción, Asunción, Capital District, Paraguay"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  ["py|Capital District|Asunción|Asunción"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include cities with unknown countries", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, PA, United States",
              "Montreal, Montreal Region, QC, unknown country name"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  ["us|PA|Lancaster County|Lancaster"]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should ignore filter values with fewer than 2 fields", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, PA, United States",
              "Pittsburgh, PA, United States",
              "New York, United States",
              "Montreal"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                expect.termsQuery(
                                  "affiliations.institution.filters.city",
                                  [
                                    "us|PA|Lancaster County|Lancaster",
                                    "us|PA||Pittsburgh",
                                    "us||PA|Pittsburgh",
                                    "us|||New York"
                                  ]
                                ),
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include a city terms filter when no filter values are parseable", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "city name, region code, unknown country name 1",
              "unqualified city name"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId)
                    ]
                  })
                }
              })
            );
          });
        });
      });

      describe("postal code", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should use a regex filter when postal codes are not already pipe-delimited", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const zipCode1 = faker.address.zipCode();
            const zipCode2 = faker.address.zipCode();

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                postalCode: [zipCode1, zipCode2]
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                {
                                  regexp: {
                                    "affiliations.institution.filters.postal_code":
                                      {
                                        value: `.+\\|(${zipCode1}|${zipCode2})`
                                      }
                                  }
                                },
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include empty string values from non-pipe-delimited filter values", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const zipCode1 = faker.address.zipCode();
            const zipCode2 = faker.address.zipCode();

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                postalCode: [zipCode1, EMPTY_STRING, zipCode2]
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId),
                      {
                        nested: {
                          path: "affiliations",
                          query: {
                            bool: {
                              filter: [
                                {
                                  regexp: {
                                    "affiliations.institution.filters.postal_code":
                                      {
                                        value: `.+\\|(${zipCode1}|${zipCode2})`
                                      }
                                  }
                                },
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          }
                        }
                      }
                    ]
                  })
                }
              })
            );
          });

          it("should not include a postal_code terms filter when no filter values are parseable", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personIds = [faker.datatype.uuid()];
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                postalCode: []
              }
            );

            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    filter: [
                      {
                        terms: {
                          collaboratorIds: personIds
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            bool: {
                              should: personIds.map((personId) => {
                                // Exclude the current personId to get the list of other personIds
                                const otherPersonIds = personIds.filter(
                                  (id) => id !== personId
                                );
                                return {
                                  bool: {
                                    filter: [
                                      { term: { id: personId } },
                                      {
                                        bool: {
                                          must_not: {
                                            terms: {
                                              collaboratorIds: otherPersonIds
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                };
                              }),
                              minimum_should_match: 1
                            }
                          }
                        }
                      },
                      expect.projectIdFilter(projectId)
                    ]
                  })
                }
              })
            );
          });
        });
      });

      describe("sorting", () => {
        it("should add all nested document types queries to the must clause with a boost of 1 when no sortBy is passed in", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personIds = [faker.datatype.uuid()];
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          const shoulds: QueryDslQueryContainer[] = [];

          networkNestedDocTypes.forEach((nestedDocType) => {
            shoulds.push({
              nested: {
                path: nestedDocType,
                score_mode: "sum",
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        filter: expect.arrayContaining([
                          {
                            terms: {
                              [`${nestedDocType}.persons.id`]: personIds
                            }
                          }
                        ])
                      }
                    },
                    boost: 1
                  }
                },
                inner_hits: {
                  _source: false
                }
              }
            });
          });

          const suppliedFilters = generateNetworkFilters();

          const request = {
            personIds,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH
          };

          const query = networkQueryBuilder.buildBulkFindNetworkQuery(
            request,
            [],
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              from: page.offset,
              size: 0,
              _source_includes: SOURCE_FIELDS,
              query: {
                bool: expect.objectContaining({
                  must: {
                    bool: {
                      should: shoulds,
                      minimum_should_match: 1
                    }
                  }
                })
              },
              sort: SORT_TIEBREAKER
            })
          );
        });

        it("should add all nested document types queries to the must clause with a boost of 1 when sorting by TOTAL_SHARED_WORKS", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personIds = [faker.datatype.uuid()];
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          const shoulds: QueryDslQueryContainer[] = [];

          networkNestedDocTypes.forEach((nestedDocType) => {
            shoulds.push({
              nested: {
                path: nestedDocType,
                score_mode: "sum",
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        filter: expect.arrayContaining([
                          {
                            terms: {
                              [`${nestedDocType}.persons.id`]: personIds
                            }
                          }
                        ])
                      }
                    },
                    boost: 1
                  }
                },
                inner_hits: {
                  _source: false
                }
              }
            });
          });

          const suppliedFilters = generateNetworkFilters();

          const request = {
            personIds,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH,
            sortBy: NetworkSortBy.TOTAL_SHARED_WORKS
          };

          const query = networkQueryBuilder.buildBulkFindNetworkQuery(
            request,
            [],
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              from: page.offset,
              size: 0,
              _source_includes: SOURCE_FIELDS,
              query: {
                bool: expect.objectContaining({
                  must: {
                    bool: {
                      should: shoulds,
                      minimum_should_match: 1
                    }
                  }
                })
              },
              sort: SORT_TIEBREAKER
            })
          );
        });

        it("should add all nested document types queries to the must clause with a boost of 0 when sorting by an option that is not one of TOTAL_SHARED_WORKS, TRIALS, CONGRESSES, PUBLICATIONS", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personIds = [faker.datatype.uuid()];
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          const shoulds: QueryDslQueryContainer[] = [];

          networkNestedDocTypes.forEach((nestedDocType) => {
            shoulds.push({
              nested: {
                path: nestedDocType,
                score_mode: "sum",
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        filter: expect.arrayContaining([
                          {
                            terms: {
                              [`${nestedDocType}.persons.id`]: personIds
                            }
                          }
                        ])
                      }
                    },
                    boost: 0
                  }
                },
                inner_hits: {
                  _source: false
                }
              }
            });
          });

          for (const sortBy of Object.values(NetworkSortBy)) {
            const sortByValue = Number(sortBy);
            if (
              isNaN(sortByValue) ||
              sortByValue === NetworkSortBy.TOTAL_SHARED_WORKS ||
              sortByValue === NetworkSortBy.TRIALS ||
              sortByValue === NetworkSortBy.CONGRESSES ||
              sortByValue === NetworkSortBy.PUBLICATIONS
            ) {
              continue;
            }

            const suppliedFilters = generateNetworkFilters();
            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH,
              sortBy: sortByValue
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    must: {
                      bool: {
                        should: shoulds,
                        minimum_should_match: 1
                      }
                    }
                  })
                }
              })
            );
          }
        });

        it("should only add a boost of 1 for the respective nested document when sorting by one of TRIALS, CONGRESSES, PUBLICATIONS", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personIds = [faker.datatype.uuid()];
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          for (const sortBy of Object.values(NetworkSortBy)) {
            const sortByValue = Number(sortBy);
            if (
              isNaN(sortByValue) ||
              sortByValue === NetworkSortBy.TOTAL_SHARED_WORKS ||
              sortByValue === NetworkSortBy.NAME ||
              sortByValue === NetworkSortBy.AFFILIATIONS
            ) {
              continue;
            }

            const shoulds: QueryDslQueryContainer[] = [];

            networkNestedDocTypes.forEach((nestedDocType) => {
              let boost = 0;
              switch (sortByValue) {
                case NetworkSortBy.TRIALS:
                  boost = nestedDocType === "trials" ? 1 : 0;
                  break;
                case NetworkSortBy.CONGRESSES:
                  boost = nestedDocType === "congress" ? 1 : 0;
                  break;
                case NetworkSortBy.PUBLICATIONS:
                  boost = nestedDocType === "publications" ? 1 : 0;
                  break;
              }

              shoulds.push({
                nested: {
                  path: nestedDocType,
                  score_mode: "sum",
                  query: {
                    constant_score: {
                      filter: {
                        bool: {
                          filter: expect.arrayContaining([
                            {
                              terms: {
                                [`${nestedDocType}.persons.id`]: personIds
                              }
                            }
                          ])
                        }
                      },
                      boost
                    }
                  },
                  inner_hits: {
                    _source: false
                  }
                }
              });
            });

            const suppliedFilters = generateNetworkFilters();
            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH,
              sortBy: sortByValue
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                query: {
                  bool: expect.objectContaining({
                    must: {
                      bool: {
                        should: shoulds,
                        minimum_should_match: 1
                      }
                    }
                  })
                },
                sort: SORT_TIEBREAKER
              })
            );
          }
        });

        it("should sort by collaborator first and last name in alphabetical order when sortBy is set to NAME", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personIds = [faker.datatype.uuid()];
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          const suppliedFilters = generateNetworkFilters();

          for (const language of LANGUAGES_SUPPORTED) {
            const request = {
              personIds,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language,
              sortBy: NetworkSortBy.NAME
            };

            const query = networkQueryBuilder.buildBulkFindNetworkQuery(
              request,
              [],
              undefined,
              () => language,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                from: page.offset,
                size: 0,
                _source_includes: SOURCE_FIELDS,
                sort: expect.arrayContaining([
                  {
                    [`firstName_${language}.sort`]: ASCENDING
                  },
                  {
                    [`lastName_${language}.sort`]: ASCENDING
                  },
                  ...SORT_TIEBREAKER
                ])
              })
            );
          }
        });

        it("should sort by shared affiliation names in alphabetical order when sortBy is set to AFFILIATIONS", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const personIds = [faker.datatype.uuid()];
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };

          const suppliedFilters = generateNetworkFilters();

          const request = {
            personIds,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH,
            sortBy: NetworkSortBy.AFFILIATIONS
          };

          const sharedAffiliations = Array.from(
            { length: faker.datatype.number({ max: 10 }) },
            faker.datatype.uuid
          );

          const query = networkQueryBuilder.buildBulkFindNetworkQuery(
            request,
            sharedAffiliations,
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              from: page.offset,
              size: 0,
              _source_includes: SOURCE_FIELDS,
              sort: expect.arrayContaining([
                {
                  [`affiliations.institution.name.keyword`]: {
                    order: ASCENDING,
                    nested: {
                      path: "affiliations",
                      filter: {
                        bool: {
                          filter: expect.termsQuery(
                            "affiliations.institution.id",
                            sharedAffiliations
                          )
                        }
                      }
                    }
                  }
                },
                ...SORT_TIEBREAKER
              ])
            })
          );
        });
      });
    });
  });

  describe("buildFindCollaboratorQuery", () => {
    it("should add an optional should clause labeled 'sharedAffiliations' with a terms query using the elements in affiliatedInstitutionIds when it's non-empty", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const collaboratorId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        trialOffset: faker.datatype.number(),
        congressOffset: faker.datatype.number(),
        publicationOffset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personId,
        collaboratorId,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectFeatures,
        language: ENGLISH
      };

      const affiliatedInstitutionIds = Array.from({ length: 10 }, () => {
        return faker.datatype.uuid();
      });

      const query = networkQueryBuilder.buildFindCollaboratorQuery(
        request,
        affiliatedInstitutionIds,
        parsedQueryTree,
        () => ENGLISH
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          size: 1,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              should: expect.arrayContaining([
                {
                  nested: {
                    path: "affiliations",
                    query: {
                      bool: {
                        filter: expect.termsQuery(
                          "affiliations.institution.id",
                          affiliatedInstitutionIds
                        )
                      }
                    },
                    inner_hits: {
                      _source: {
                        includes: [
                          "affiliations.type",
                          "affiliations.isCurrent",
                          "affiliations.institution.id",
                          "affiliations.institution.ultimateParentId",
                          "affiliations.insitution.name",
                          "affiliations.institution.address",
                          "affiliations.institution.nameTranslations",
                          "affiliations.institution.addressTranslations"
                        ]
                      },
                      name: "sharedAffiliations",
                      size: 10
                    }
                  }
                }
              ]),
              minimum_should_match: 0
            })
          }
        })
      );
    });

    it("should NOT add a 'sharedAffiliations' labled query when affiliatedInstitutionIds is empty", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const collaboratorId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        trialOffset: faker.datatype.number(),
        congressOffset: faker.datatype.number(),
        publicationOffset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      const request = {
        personId,
        collaboratorId,
        dateRange,
        page,
        suppliedFilters: suppliedFilters,
        projectFeatures,
        language: ENGLISH
      };

      const affiliatedInstitutionIds: string[] = [];

      const query = networkQueryBuilder.buildFindCollaboratorQuery(
        request,
        affiliatedInstitutionIds,
        parsedQueryTree,
        () => ENGLISH
      );

      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticPeopleIndex,
          size: 1,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              should: expect.not.arrayContaining([
                {
                  nested: {
                    path: "affiliations",
                    query: expect.anything(),
                    inner_hits: expect.objectContaining({
                      name: "sharedAffiliations"
                    })
                  }
                }
              ]),
              minimum_should_match: 1
            })
          }
        })
      );
    });

    it("should filter on the requested collaboratorId and the main HCP personId", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const collaboratorId = faker.datatype.uuid();
      const page = {
        trialOffset: faker.datatype.number(),
        congressOffset: faker.datatype.number(),
        publicationOffset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const suppliedFilters = generateNetworkFilters();

      const request: NetworkCollaboratorRequest = {
        personId,
        collaboratorId,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const query = networkQueryBuilder.buildFindCollaboratorQuery(
        request,
        [],
        undefined,
        () => ENGLISH
      );

      expect(query).toEqual({
        index: configService.elasticPeopleIndex,
        size: 1,
        _source_includes: SOURCE_FIELDS,
        query: {
          bool: expect.objectContaining({
            filter: [
              expect.termQuery("collaboratorIds", personId),
              expect.termQuery("id", collaboratorId)
            ]
          })
        }
      });
    });

    it("should add a should clause for each shared work type filtering on works that involve the main HCP, retrieving the relevant fields from inner_hits and sorting based on date", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const collaboratorId = faker.datatype.uuid();
      const page = {
        trialOffset: faker.datatype.number(),
        congressOffset: faker.datatype.number(),
        publicationOffset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const suppliedFilters = generateNetworkFilters();

      const request: NetworkCollaboratorRequest = {
        personId,
        collaboratorId,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const query = networkQueryBuilder.buildFindCollaboratorQuery(
        request,
        [],
        undefined,
        () => ENGLISH
      );

      expect(query).toEqual({
        index: configService.elasticPeopleIndex,
        size: 1,
        _source_includes: SOURCE_FIELDS,
        query: {
          bool: expect.objectContaining({
            should: [
              {
                nested: {
                  path: "publications",
                  score_mode: "sum",
                  query: {
                    bool: {
                      filter: [
                        expect.termQuery("publications.persons.id", personId)
                      ]
                    }
                  },
                  inner_hits: {
                    _source: {
                      include: [
                        "publications.id",
                        "publications.datePublished",
                        "publications.persons",
                        "publications.title_eng",
                        "publications.title_jpn",
                        "publications.title_cmn",
                        "publications.languageCode"
                      ]
                    },
                    docvalue_fields: [
                      "publications.type_eng",
                      "publications.type_jpn",
                      "publications.type_cmn",
                      "publications.journalName_eng",
                      "publications.journalName_jpn",
                      "publications.journalName_cmn"
                    ],
                    from: page.publicationOffset,
                    size: page.limit,
                    sort: [
                      {
                        "publications.datePublished": {
                          order: DESCENDING
                        }
                      }
                    ]
                  }
                }
              },
              {
                nested: {
                  path: "trials",
                  score_mode: "sum",
                  query: {
                    bool: {
                      filter: [expect.termQuery("trials.persons.id", personId)]
                    }
                  },
                  inner_hits: {
                    _source: {
                      include: [
                        "trials.id",
                        "trials.officialTitle_eng",
                        "trials.phase_eng",
                        "trials.status_eng",
                        "trials.sponsor_eng",
                        "trials.startDate",
                        "trials.persons"
                      ]
                    },
                    docvalue_fields: [],
                    from: page.trialOffset,
                    size: page.limit,
                    sort: [
                      {
                        "trials.startDate": {
                          order: DESCENDING
                        }
                      }
                    ]
                  }
                }
              },
              {
                nested: {
                  path: "congress",
                  score_mode: "sum",
                  query: {
                    bool: {
                      filter: [
                        expect.termQuery("congress.persons.id", personId)
                      ]
                    }
                  },
                  inner_hits: {
                    _source: {
                      include: [
                        "congress.id",
                        "congress.title_eng",
                        "congress.persons",
                        "congress.type",
                        "congress.masterInitialDate"
                      ]
                    },
                    docvalue_fields: [
                      "congress.organizer_eng",
                      "congress.name_eng"
                    ],
                    from: page.congressOffset,
                    size: page.limit,
                    sort: [
                      {
                        "congress.masterInitialDate": {
                          order: DESCENDING
                        }
                      }
                    ]
                  }
                }
              }
            ],
            minimum_should_match: 1
          })
        }
      });
    });

    describe("filters", () => {
      it("input with parsedQueryTree should add the parsed query to each shared work query", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Partial<
          Record<NetworkNestedDocPath, QueryDslQueryContainer>
        > = {
          trials: generateMockElasticsearchTermQuery(),
          publications: generateMockElasticsearchTermQuery(),
          congress: generateMockElasticsearchTermQuery()
        };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const collaboratorId = faker.datatype.uuid();
        const page = {
          trialOffset: faker.datatype.number(),
          congressOffset: faker.datatype.number(),
          publicationOffset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };
        const suppliedFilters = generateNetworkFilters();

        const request: NetworkCollaboratorRequest = {
          personId,
          collaboratorId,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildFindCollaboratorQuery(
          request,
          [],
          parsedQueryTree,
          () => ENGLISH
        );

        expect(query).toEqual({
          index: configService.elasticPeopleIndex,
          size: 1,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              should: [
                {
                  nested: {
                    path: "publications",
                    score_mode: "sum",
                    query: {
                      bool: {
                        filter: [
                          expect.termQuery("publications.persons.id", personId),
                          pathSpecificQueries.publications
                        ]
                      }
                    },
                    inner_hits: {
                      _source: {
                        include: [
                          "publications.id",
                          "publications.datePublished",
                          "publications.persons",
                          "publications.title_eng",
                          "publications.title_jpn",
                          "publications.title_cmn",
                          "publications.languageCode"
                        ]
                      },
                      docvalue_fields: [
                        "publications.type_eng",
                        "publications.type_jpn",
                        "publications.type_cmn",
                        "publications.journalName_eng",
                        "publications.journalName_jpn",
                        "publications.journalName_cmn"
                      ],
                      from: page.publicationOffset,
                      size: page.limit,
                      sort: [
                        {
                          "publications.datePublished": {
                            order: DESCENDING
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  nested: {
                    path: "trials",
                    score_mode: "sum",
                    query: {
                      bool: {
                        filter: [
                          expect.termQuery("trials.persons.id", personId),
                          pathSpecificQueries.trials
                        ]
                      }
                    },
                    inner_hits: {
                      _source: {
                        include: [
                          "trials.id",
                          "trials.officialTitle_eng",
                          "trials.phase_eng",
                          "trials.status_eng",
                          "trials.sponsor_eng",
                          "trials.startDate",
                          "trials.persons"
                        ]
                      },
                      docvalue_fields: [],
                      from: page.trialOffset,
                      size: page.limit,
                      sort: [
                        {
                          "trials.startDate": {
                            order: DESCENDING
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  nested: {
                    path: "congress",
                    score_mode: "sum",
                    query: {
                      bool: {
                        filter: [
                          expect.termQuery("congress.persons.id", personId),
                          pathSpecificQueries.congress
                        ]
                      }
                    },
                    inner_hits: {
                      _source: {
                        include: [
                          "congress.id",
                          "congress.title_eng",
                          "congress.persons",
                          "congress.type",
                          "congress.masterInitialDate"
                        ]
                      },
                      docvalue_fields: [
                        "congress.organizer_eng",
                        "congress.name_eng"
                      ],
                      from: page.congressOffset,
                      size: page.limit,
                      sort: [
                        {
                          "congress.masterInitialDate": {
                            order: DESCENDING
                          }
                        }
                      ]
                    }
                  }
                }
              ],
              minimum_should_match: 1
            })
          }
        });

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          [
            "publications.publicationAbstract",
            "publications.keywords",
            "publications.title"
          ],
          expect.any(Function)
        );

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          [
            "trials.briefTitle",
            "trials.conditions",
            "trials.interventions",
            "trials.keywords",
            "trials.officialTitle",
            "trials.summary"
          ],
          expect.any(Function)
        );

        expect(
          parsedQueryTreeToElasticsearchQueriesService.parse
        ).toHaveBeenCalledWith(
          parsedQueryTree,
          ["congress.keywords", "congress.title"],
          expect.any(Function)
        );
      });

      it("should set inner_hits size to zero for the should clause with the specific shared work type that is set to false in the collaborationType filter", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const collaboratorId = faker.datatype.uuid();
        const page = {
          trialOffset: faker.datatype.number(),
          congressOffset: faker.datatype.number(),
          publicationOffset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        networkNestedDocTypes.forEach((nestedDocType) => {
          const collaborationType =
            nestedDocTypeToCollaborationType[nestedDocType];
          const suppliedFilters = generateNetworkFilters(
            {},
            {
              [collaborationType]: false
            }
          );

          const request: NetworkCollaboratorRequest = {
            personId,
            collaboratorId,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH
          };

          const query = networkQueryBuilder.buildFindCollaboratorQuery(
            request,
            [],
            undefined,
            () => ENGLISH
          );

          expect(query).toEqual({
            index: configService.elasticPeopleIndex,
            size: 1,
            _source_includes: SOURCE_FIELDS,
            query: {
              bool: expect.objectContaining({
                should: expect.arrayContaining([
                  {
                    nested: {
                      path: "publications",
                      score_mode: "sum",
                      query: {
                        bool: {
                          filter: [
                            expect.termQuery(
                              "publications.persons.id",
                              personId
                            )
                          ]
                        }
                      },
                      inner_hits: {
                        _source: {
                          include: [
                            "publications.id",
                            "publications.datePublished",
                            "publications.persons",
                            "publications.title_eng",
                            "publications.title_jpn",
                            "publications.title_cmn",
                            "publications.languageCode"
                          ]
                        },
                        docvalue_fields: [
                          "publications.type_eng",
                          "publications.type_jpn",
                          "publications.type_cmn",
                          "publications.journalName_eng",
                          "publications.journalName_jpn",
                          "publications.journalName_cmn"
                        ],
                        from: page.publicationOffset,
                        size:
                          collaborationType === "publications" ? 0 : page.limit,
                        sort: [
                          {
                            "publications.datePublished": {
                              order: DESCENDING
                            }
                          }
                        ]
                      }
                    }
                  },
                  {
                    nested: {
                      path: "trials",
                      score_mode: "sum",
                      query: {
                        bool: {
                          filter: [
                            expect.termQuery("trials.persons.id", personId)
                          ]
                        }
                      },
                      inner_hits: {
                        _source: {
                          include: [
                            "trials.id",
                            "trials.officialTitle_eng",
                            "trials.phase_eng",
                            "trials.status_eng",
                            "trials.sponsor_eng",
                            "trials.startDate",
                            "trials.persons"
                          ]
                        },
                        docvalue_fields: [],
                        from: page.trialOffset,
                        size: collaborationType === "trials" ? 0 : page.limit,
                        sort: [
                          {
                            "trials.startDate": {
                              order: DESCENDING
                            }
                          }
                        ]
                      }
                    }
                  },
                  {
                    nested: {
                      path: "congress",
                      score_mode: "sum",
                      query: {
                        bool: {
                          filter: [
                            expect.termQuery("congress.persons.id", personId)
                          ]
                        }
                      },
                      inner_hits: {
                        _source: {
                          include: [
                            "congress.id",
                            "congress.title_eng",
                            "congress.persons",
                            "congress.type",
                            "congress.masterInitialDate"
                          ]
                        },
                        docvalue_fields: [
                          "congress.organizer_eng",
                          "congress.name_eng"
                        ],
                        from: page.congressOffset,
                        size:
                          collaborationType === "congresses" ? 0 : page.limit,
                        sort: [
                          {
                            "congress.masterInitialDate": {
                              order: DESCENDING
                            }
                          }
                        ]
                      }
                    }
                  }
                ]),
                minimum_should_match: 1
              })
            }
          });
        });
      });

      it("should set inner_hits size to zero for all the shared work clauses if the collaborationType filter is omitted from the request", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const collaboratorId = faker.datatype.uuid();
        const page = {
          trialOffset: faker.datatype.number(),
          congressOffset: faker.datatype.number(),
          publicationOffset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const suppliedFilters: NetworkFilters = {
          keyword: EMPTY_STRING
        };

        const request: NetworkCollaboratorRequest = {
          personId,
          collaboratorId,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH
        };

        const query = networkQueryBuilder.buildFindCollaboratorQuery(
          request,
          [],
          undefined,
          () => ENGLISH
        );

        expect(query).toEqual({
          index: configService.elasticPeopleIndex,
          size: 1,
          _source_includes: SOURCE_FIELDS,
          query: {
            bool: expect.objectContaining({
              should: expect.arrayContaining([
                {
                  nested: {
                    path: "publications",
                    score_mode: "sum",
                    query: {
                      bool: {
                        filter: [
                          expect.termQuery("publications.persons.id", personId)
                        ]
                      }
                    },
                    inner_hits: {
                      _source: {
                        include: [
                          "publications.id",
                          "publications.datePublished",
                          "publications.persons",
                          "publications.title_eng",
                          "publications.title_jpn",
                          "publications.title_cmn",
                          "publications.languageCode"
                        ]
                      },
                      docvalue_fields: [
                        "publications.type_eng",
                        "publications.type_jpn",
                        "publications.type_cmn",
                        "publications.journalName_eng",
                        "publications.journalName_jpn",
                        "publications.journalName_cmn"
                      ],
                      from: page.publicationOffset,
                      size: 0,
                      sort: [
                        {
                          "publications.datePublished": {
                            order: DESCENDING
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  nested: {
                    path: "trials",
                    score_mode: "sum",
                    query: {
                      bool: {
                        filter: [
                          expect.termQuery("trials.persons.id", personId)
                        ]
                      }
                    },
                    inner_hits: {
                      _source: {
                        include: [
                          "trials.id",
                          "trials.officialTitle_eng",
                          "trials.phase_eng",
                          "trials.status_eng",
                          "trials.sponsor_eng",
                          "trials.startDate",
                          "trials.persons"
                        ]
                      },
                      docvalue_fields: [],
                      from: page.trialOffset,
                      size: 0,
                      sort: [
                        {
                          "trials.startDate": {
                            order: DESCENDING
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  nested: {
                    path: "congress",
                    score_mode: "sum",
                    query: {
                      bool: {
                        filter: [
                          expect.termQuery("congress.persons.id", personId)
                        ]
                      }
                    },
                    inner_hits: {
                      _source: {
                        include: [
                          "congress.id",
                          "congress.title_eng",
                          "congress.persons",
                          "congress.type",
                          "congress.masterInitialDate"
                        ]
                      },
                      docvalue_fields: [
                        "congress.organizer_eng",
                        "congress.name_eng"
                      ],
                      from: page.congressOffset,
                      size: 0,
                      sort: [
                        {
                          "congress.masterInitialDate": {
                            order: DESCENDING
                          }
                        }
                      ]
                    }
                  }
                }
              ]),
              minimum_should_match: 1
            })
          }
        });
      });
    });
  });

  describe("buildFindAffiliationsQuery", () => {
    it("should filter on the main HCP personId and only include institutionWithTypes.id in _source", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const collaboratorId = faker.datatype.uuid();
      const page = {
        trialOffset: faker.datatype.number(),
        congressOffset: faker.datatype.number(),
        publicationOffset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const suppliedFilters = generateNetworkFilters();

      const request: NetworkCollaboratorRequest = {
        personId,
        collaboratorId,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH
      };

      const query = networkQueryBuilder.buildFindAffiliationsQuery(request);

      expect(query).toEqual({
        index: configService.elasticPeopleIndex,
        _source_includes: ["affiliations.institution.id"],
        query: expect.termQuery("id", personId)
      });
    });
  });

  describe("buildLocationFilterAutocomplete", () => {
    it("should return null if all collaboration types need to be filtered out", () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const suppliedFiltersAllFalse = generateNetworkFilters({
        collaborationType: {
          trials: false,
          congresses: false,
          publications: false
        }
      });
      const suppliedFiltersEmpty: NetworkFilters = {
        collaborationType: {}
      };
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
        const requestWithAllFalse = {
          personId,
          dateRange,
          suppliedFilters: suppliedFiltersAllFalse,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue: faker.datatype.string()
        };

        const requestWithEmpty = {
          personId,
          dateRange,
          suppliedFilters: suppliedFiltersEmpty,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue: faker.datatype.string()
        };

        const queryAllFalse =
          networkQueryBuilder.buildLocationFilterAutocomplete(
            requestWithAllFalse,
            parsedQueryTree,
            () => ENGLISH,
            FEATURE_FLAGS
          );
        const queryEmpty = networkQueryBuilder.buildLocationFilterAutocomplete(
          requestWithEmpty,
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );
        expect(queryAllFalse).toBeNull();
        expect(queryEmpty).toBeNull();
      }
    });

    it("should filter out the HCP themself from the request, but filter on the HCP's ID as a collaborator and on project ID", () => {
      const configService = {
        elasticPeopleIndex: faker.random.word()
      } as ConfigService;

      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const networkQueryBuilder = new NetworkQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: true,
          congresses: true,
          publications: true
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };
      const parsedQueryTree = faker.datatype.string();

      for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
        const request = {
          personId,
          dateRange,
          suppliedFilters: suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue: faker.datatype.string()
        };

        const query = networkQueryBuilder.buildLocationFilterAutocomplete(
          request,
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            size: 0,
            _source_includes: [],
            query: {
              bool: expect.objectContaining({
                filter: expect.arrayContaining([
                  expect.termQuery("collaboratorIds", personId),
                  {
                    bool: {
                      must_not: expect.termQuery("id", personId)
                    }
                  },
                  expect.projectIdFilter(projectId)
                ])
              })
            }
          })
        );
      }
    });

    describe("country", () => {
      it("should not include any country filters when filterField is COUNTRY and filterValue is empty string", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const countries = ["us", "ca", "au"];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          country: countries
        });

        const filterField = NetworkFilterAutocompleteField.COUNTRY;
        const filterValue = EMPTY_STRING;

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue
        };

        const query = networkQueryBuilder.buildLocationFilterAutocomplete(
          request,
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            size: 0,
            _source_includes: [],
            query: {
              bool: expect.objectContaining({
                filter: [
                  expect.termQuery("collaboratorIds", personId),
                  {
                    bool: {
                      must_not: expect.termQuery("id", personId)
                    }
                  },
                  expect.projectIdFilter(projectId)
                ]
              })
            },
            aggs: {
              nested: {
                nested: {
                  path: "affiliations"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          expect.termQuery(
                            "affiliations.type",
                            "Work Affiliation"
                          ),
                          expect.termQuery("affiliations.isCurrent", true)
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: "affiliations.institution.filters.country"
                        },
                        aggs: {
                          people: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          })
        );
      });

      it("should include match_phrase_prefix query when filterField is COUNTRY and exclude country terms filter", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const countries = ["us", "ca", "au"];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          country: countries
        });

        const filterField = NetworkFilterAutocompleteField.COUNTRY;
        const filterValue = faker.random.word();

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue
        };

        const query = networkQueryBuilder.buildLocationFilterAutocomplete(
          request,
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            size: 0,
            _source_includes: [],
            query: {
              bool: expect.objectContaining({
                filter: [
                  expect.termQuery("collaboratorIds", personId),
                  {
                    bool: {
                      must_not: expect.termQuery("id", personId)
                    }
                  },
                  expect.projectIdFilter(projectId),
                  {
                    nested: {
                      path: "affiliations",
                      query: {
                        bool: {
                          filter: [
                            {
                              match_phrase_prefix: {
                                "affiliations.institution.address.country": {
                                  query: filterValue
                                }
                              }
                            },
                            expect.termQuery(
                              "affiliations.type",
                              "Work Affiliation"
                            ),
                            expect.termQuery("affiliations.isCurrent", true)
                          ]
                        }
                      }
                    }
                  }
                ]
              })
            },
            aggs: {
              nested: {
                nested: {
                  path: "affiliations"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          {
                            match_phrase_prefix: {
                              "affiliations.institution.address.country": {
                                query: filterValue
                              }
                            }
                          },
                          expect.termQuery(
                            "affiliations.type",
                            "Work Affiliation"
                          ),
                          expect.termQuery("affiliations.isCurrent", true)
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: "affiliations.institution.filters.country"
                        },
                        aggs: {
                          people: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          })
        );
      });
    });

    describe("region", () => {
      it("should not include any region filters when filterField is REGION and filterValue is empty string", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const regions = ["us|PA", "ca|QC", "au|NSW"];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          region: regions
        });

        const filterField = NetworkFilterAutocompleteField.REGION;
        const filterValue = EMPTY_STRING;

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue
        };

        const query = networkQueryBuilder.buildLocationFilterAutocomplete(
          request,
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            size: 0,
            _source_includes: [],
            query: {
              bool: expect.objectContaining({
                filter: [
                  expect.termQuery("collaboratorIds", personId),
                  {
                    bool: {
                      must_not: expect.termQuery("id", personId)
                    }
                  },
                  expect.projectIdFilter(projectId)
                ]
              })
            },
            aggs: {
              nested: {
                nested: {
                  path: "affiliations"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          expect.termQuery(
                            "affiliations.type",
                            "Work Affiliation"
                          ),
                          expect.termQuery("affiliations.isCurrent", true)
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: "affiliations.institution.filters.region"
                        },
                        aggs: {
                          people: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          })
        );
      });

      it("should include match_phrase_prefix query when filterField is REGION and exclude region terms filter", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const regions = ["us|PA", "ca|QC", "au|NSW"];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          region: regions
        });

        const filterField = NetworkFilterAutocompleteField.REGION;
        const filterValue = faker.random.word();

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue
        };

        const query = networkQueryBuilder.buildLocationFilterAutocomplete(
          request,
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        const locationFilters = [
          {
            match_phrase_prefix: {
              "affiliations.institution.address.region": {
                query: filterValue
              }
            }
          },
          expect.termQuery("affiliations.type", "Work Affiliation"),
          expect.termQuery("affiliations.isCurrent", true)
        ];

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            size: 0,
            _source_includes: [],
            query: {
              bool: expect.objectContaining({
                filter: [
                  expect.termQuery("collaboratorIds", personId),
                  {
                    bool: {
                      must_not: expect.termQuery("id", personId)
                    }
                  },
                  expect.projectIdFilter(projectId),
                  {
                    nested: {
                      path: "affiliations",
                      query: {
                        bool: {
                          filter: locationFilters
                        }
                      }
                    }
                  }
                ]
              })
            },
            aggs: {
              nested: {
                nested: {
                  path: "affiliations"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: locationFilters
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: "affiliations.institution.filters.region"
                        },
                        aggs: {
                          people: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          })
        );
      });
    });

    describe("city", () => {
      it("should not include any city filters when filterField is CITY and filterValue is empty string", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const cities = [
          "us|PA|Allegheny|Amish City",
          "ca|QC|Mauricie|Apologyville",
          "au|NSW|Booroondarra|Kangarootown"
        ];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          city: cities
        });

        const filterField = NetworkFilterAutocompleteField.CITY;
        const filterValue = EMPTY_STRING;

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue
        };

        const query = networkQueryBuilder.buildLocationFilterAutocomplete(
          request,
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            size: 0,
            _source_includes: [],
            query: {
              bool: expect.objectContaining({
                filter: [
                  expect.termQuery("collaboratorIds", personId),
                  {
                    bool: {
                      must_not: expect.termQuery("id", personId)
                    }
                  },
                  expect.projectIdFilter(projectId)
                ]
              })
            },
            aggs: {
              nested: {
                nested: {
                  path: "affiliations"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          expect.termQuery(
                            "affiliations.type",
                            "Work Affiliation"
                          ),
                          expect.termQuery("affiliations.isCurrent", true)
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: "affiliations.institution.filters.city"
                        },
                        aggs: {
                          people: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          })
        );
      });

      it("should include match_phrase_prefix query when filterField is CITY and exclude city terms filter", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const cities = [
          "us|PA|Allegheny|Amish City",
          "ca|QC|Mauricie|Apologyville",
          "au|NSW|Booroondarra|Kangarootown"
        ];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          city: cities
        });

        const filterField = NetworkFilterAutocompleteField.CITY;
        const filterValue = faker.random.word();

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue
        };

        const query = networkQueryBuilder.buildLocationFilterAutocomplete(
          request,
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        const locationFilters = [
          {
            match_phrase_prefix: {
              "affiliations.institution.address.city": {
                query: filterValue
              }
            }
          },
          expect.termQuery("affiliations.type", "Work Affiliation"),
          expect.termQuery("affiliations.isCurrent", true)
        ];

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            size: 0,
            _source_includes: [],
            query: {
              bool: expect.objectContaining({
                filter: [
                  expect.termQuery("collaboratorIds", personId),
                  {
                    bool: {
                      must_not: expect.termQuery("id", personId)
                    }
                  },
                  expect.projectIdFilter(projectId),
                  {
                    nested: {
                      path: "affiliations",
                      query: {
                        bool: {
                          filter: locationFilters
                        }
                      }
                    }
                  }
                ]
              })
            },
            aggs: {
              nested: {
                nested: {
                  path: "affiliations"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: locationFilters
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: "affiliations.institution.filters.city"
                        },
                        aggs: {
                          people: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          })
        );
      });
    });

    describe("postal code", () => {
      it("should not include any postal code filters when filterField is POSTAL_CODE and filterValue is empty string", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const postalCodes = [
          "us|PA|Allegheny|Amish City|17366",
          "ca|QC|Mauricie|Apologyville|G3N 1C9",
          "au|NSW|Booroondarra|Kangarootown|2213"
        ];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          postalCode: postalCodes
        });

        const filterField = NetworkFilterAutocompleteField.POSTAL_CODE;
        const filterValue = EMPTY_STRING;

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue
        };

        const query = networkQueryBuilder.buildLocationFilterAutocomplete(
          request,
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            size: 0,
            _source_includes: [],
            query: {
              bool: expect.objectContaining({
                filter: [
                  expect.termQuery("collaboratorIds", personId),
                  {
                    bool: {
                      must_not: expect.termQuery("id", personId)
                    }
                  },
                  expect.projectIdFilter(projectId)
                ]
              })
            },
            aggs: {
              nested: {
                nested: {
                  path: "affiliations"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: [
                          expect.termQuery(
                            "affiliations.type",
                            "Work Affiliation"
                          ),
                          expect.termQuery("affiliations.isCurrent", true)
                        ]
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: "affiliations.institution.filters.postal_code"
                        },
                        aggs: {
                          people: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          })
        );
      });

      it("should include regexp query when filterField is POSTAL_CODE and exclude postal code terms filter", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const postalCodes = [
          "us|PA|Allegheny|Amish City|17366",
          "ca|QC|Mauricie|Apologyville|G3N 1C9",
          "au|NSW|Booroondarra|Kangarootown|2213"
        ];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          postalCode: postalCodes
        });

        const filterField = NetworkFilterAutocompleteField.POSTAL_CODE;
        const filterValue = faker.random.word();

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH,
          filterField,
          filterValue
        };

        const query = networkQueryBuilder.buildLocationFilterAutocomplete(
          request,
          parsedQueryTree,
          () => ENGLISH,
          FEATURE_FLAGS
        );

        const locationFilters = [
          {
            regexp: {
              "affiliations.institution.filters.postal_code": `.+\\|${filterValue}[^|]*`
            }
          },
          expect.termQuery("affiliations.type", "Work Affiliation"),
          expect.termQuery("affiliations.isCurrent", true)
        ];

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticPeopleIndex,
            size: 0,
            _source_includes: [],
            query: {
              bool: expect.objectContaining({
                filter: [
                  expect.termQuery("collaboratorIds", personId),
                  {
                    bool: {
                      must_not: expect.termQuery("id", personId)
                    }
                  },
                  expect.projectIdFilter(projectId),
                  {
                    nested: {
                      path: "affiliations",
                      query: {
                        bool: {
                          filter: locationFilters
                        }
                      }
                    }
                  }
                ]
              })
            },
            aggs: {
              nested: {
                nested: {
                  path: "affiliations"
                },
                aggs: {
                  filtered_matching: {
                    filter: {
                      bool: {
                        filter: locationFilters
                      }
                    },
                    aggs: {
                      matching: {
                        terms: {
                          field: "affiliations.institution.filters.postal_code"
                        },
                        aggs: {
                          people: {
                            reverse_nested: {}
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          })
        );
      });
    });

    describe("additional filters", () => {
      it("should add all nested document types queries to the must clause when all collaborationType filter values are true", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        LOCATION_FILTER_AUTOCOMPLETE_FIELDS.forEach((filterField) => {
          const shoulds: QueryDslQueryContainer[] = [];

          networkNestedDocTypes.forEach((nestedDocType) => {
            shoulds.push({
              nested: {
                path: nestedDocType,
                score_mode: "sum",
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        filter: expect.arrayContaining([
                          expect.termQuery(
                            `${nestedDocType}.persons.id`,
                            personId
                          )
                        ])
                      }
                    },
                    boost: 1
                  }
                },
                inner_hits: {
                  _source: false
                }
              }
            });
          });

          const suppliedFilters = generateNetworkFilters();

          const request = {
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH,
            filterField,
            filterValue: faker.datatype.string()
          };

          const query = networkQueryBuilder.buildLocationFilterAutocomplete(
            request,
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              size: 0,
              _source_includes: [],
              query: {
                bool: expect.objectContaining({
                  must: {
                    bool: {
                      should: shoulds,
                      minimum_should_match: 1
                    }
                  }
                })
              }
            })
          );
        });
      });

      it("should return null if the collaborationType filter is omitted from the request", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const suppliedFilters: NetworkFilters = {
          keyword: EMPTY_STRING
        };

        for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
          const request = {
            personId,
            dateRange,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH,
            filterField,
            filterValue: faker.datatype.string()
          };

          const query = networkQueryBuilder.buildLocationFilterAutocomplete(
            request,
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toBeNull();
        }
      });

      it("should NOT include a nested document type in the must clause when that type is set to false in the collaborationType filter", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        networkNestedDocTypes.forEach((optionalNestedDocType) => {
          const shoulds: QueryDslQueryContainer[] = [];
          const collaborationType =
            nestedDocTypeToCollaborationType[optionalNestedDocType];
          const suppliedFilters = generateNetworkFilters(
            {},
            {
              [collaborationType]: false
            }
          );

          networkNestedDocTypes.forEach((nestedDocType) => {
            if (nestedDocType === optionalNestedDocType) {
              return;
            }

            shoulds.push({
              nested: {
                path: nestedDocType,
                score_mode: "sum",
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        filter: expect.arrayContaining([
                          expect.termQuery(
                            `${nestedDocType}.persons.id`,
                            personId
                          )
                        ])
                      }
                    },
                    boost: 1
                  }
                },
                inner_hits: {
                  _source: false
                }
              }
            });
          });

          LOCATION_FILTER_AUTOCOMPLETE_FIELDS.forEach((filterField) => {
            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH,
              filterField,
              filterValue: faker.datatype.string()
            };

            const query = networkQueryBuilder.buildLocationFilterAutocomplete(
              request,
              undefined,
              () => ENGLISH,
              FEATURE_FLAGS
            );

            expect(query).toEqual(
              expect.objectContaining({
                index: configService.elasticPeopleIndex,
                size: 0,
                _source_includes: [],
                query: {
                  bool: expect.objectContaining({
                    must: {
                      bool: {
                        should: shoulds,
                        minimum_should_match: 1
                      }
                    }
                  })
                }
              })
            );
          });
        });
      });

      it("should add a range filter query when a minimum date range filter is supplied", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {
          min: faker.date.past().getSeconds()
        };
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const shoulds: QueryDslQueryContainer[] = [];

        networkNestedDocTypes.forEach((nestedDocType) => {
          const pathQualifiedDateField = `${nestedDocType}.${nestedDocs[nestedDocType].dateField}`;

          shoulds.push({
            nested: {
              path: nestedDocType,
              score_mode: "sum",
              query: {
                constant_score: {
                  filter: {
                    bool: {
                      filter: expect.arrayContaining([
                        expect.termQuery(
                          `${nestedDocType}.persons.id`,
                          personId
                        ),
                        {
                          bool: {
                            should: [
                              {
                                range: {
                                  [pathQualifiedDateField]: {
                                    gte: dateRange.min
                                  }
                                }
                              },
                              {
                                bool: {
                                  must_not: expect.fieldExists(
                                    pathQualifiedDateField
                                  )
                                }
                              }
                            ],
                            minimum_should_match: 1
                          }
                        }
                      ])
                    }
                  },
                  boost: 1
                }
              },
              inner_hits: {
                _source: false
              }
            }
          });
        });

        const suppliedFilters = generateNetworkFilters();

        LOCATION_FILTER_AUTOCOMPLETE_FIELDS.forEach((filterField) => {
          const request = {
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH,
            filterField,
            filterValue: faker.datatype.string()
          };

          const query = networkQueryBuilder.buildLocationFilterAutocomplete(
            request,
            undefined,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              size: 0,
              _source_includes: [],
              query: {
                bool: expect.objectContaining({
                  must: {
                    bool: {
                      should: shoulds,
                      minimum_should_match: 1
                    }
                  }
                })
              }
            })
          );
        });
      });

      it("input with parsedQueryTree should add should clauses for trials/congress/publications/DRG_procedures/DRG_diagnoses with the parsed query, seperate from the should clauses used to find shared works", () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Partial<
          Record<NetworkNestedDocPathForKeywordFilter, QueryDslQueryContainer>
        > = {
          trials: generateMockElasticsearchTermQuery(),
          publications: generateMockElasticsearchTermQuery(),
          congress: generateMockElasticsearchTermQuery(),
          DRG_diagnoses: generateMockElasticsearchTermQuery(),
          DRG_procedures: generateMockElasticsearchTermQuery()
        };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };
        const suppliedFilters = generateNetworkFilters();

        for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
          const request = {
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH,
            filterField,
            filterValue: faker.datatype.string()
          };

          const query = networkQueryBuilder.buildLocationFilterAutocomplete(
            request,
            parsedQueryTree,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          expect(query).toEqual({
            index: configService.elasticPeopleIndex,
            size: 0,
            _source_includes: [],
            query: {
              bool: expect.objectContaining({
                must: {
                  bool: expect.objectContaining({
                    must: {
                      bool: {
                        should: [
                          {
                            nested: {
                              path: "publications",
                              score_mode: "sum",
                              query: {
                                constant_score: {
                                  filter: {
                                    bool: {
                                      filter: [
                                        expect.termQuery(
                                          "publications.persons.id",
                                          personId
                                        )
                                      ]
                                    }
                                  },
                                  boost: 1
                                }
                              },
                              inner_hits: {
                                _source: false
                              }
                            }
                          },
                          {
                            nested: {
                              path: "trials",
                              score_mode: "sum",
                              query: {
                                constant_score: {
                                  filter: {
                                    bool: {
                                      filter: [
                                        expect.termQuery(
                                          "trials.persons.id",
                                          personId
                                        )
                                      ]
                                    }
                                  },
                                  boost: 1
                                }
                              },
                              inner_hits: {
                                _source: false
                              }
                            }
                          },
                          {
                            nested: {
                              path: "congress",
                              score_mode: "sum",
                              query: {
                                constant_score: {
                                  filter: {
                                    bool: {
                                      filter: [
                                        expect.termQuery(
                                          "congress.persons.id",
                                          personId
                                        )
                                      ]
                                    }
                                  },
                                  boost: 1
                                }
                              },
                              inner_hits: {
                                _source: false
                              }
                            }
                          }
                        ],
                        minimum_should_match: 1
                      }
                    },
                    should: [
                      {
                        nested: {
                          path: "publications",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.publications]
                            }
                          }
                        }
                      },
                      {
                        nested: {
                          path: "trials",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.trials]
                            }
                          }
                        }
                      },
                      {
                        nested: {
                          path: "congress",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.congress]
                            }
                          }
                        }
                      },
                      {
                        nested: {
                          path: "DRG_diagnoses",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.DRG_diagnoses]
                            }
                          }
                        }
                      },
                      {
                        nested: {
                          path: "DRG_procedures",
                          score_mode: "sum",
                          query: {
                            bool: {
                              filter: [pathSpecificQueries.DRG_procedures]
                            }
                          }
                        }
                      }
                    ],
                    minimum_should_match: 1
                  })
                }
              })
            },
            aggs: expect.anything()
          });

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            [
              "publications.publicationAbstract",
              "publications.keywords",
              "publications.title"
            ],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            [
              "trials.briefTitle",
              "trials.conditions",
              "trials.interventions",
              "trials.keywords",
              "trials.officialTitle",
              "trials.summary"
            ],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            ["congress.keywords", "congress.title"],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            ["DRG_diagnoses.codeAndDescription"],
            expect.any(Function)
          );

          expect(
            parsedQueryTreeToElasticsearchQueriesService.parse
          ).toHaveBeenCalledWith(
            parsedQueryTree,
            ["DRG_procedures.codeAndDescription"],
            expect.any(Function)
          );
        }
      });

      describe("Brazilian claims disabled", () => {
        it("input with parsedQueryTree should add should clauses for trials/congress/publications/DRG_procedures/DRG_diagnoses with the parsed query, with a must_not clause to filter out Brazilian HCPs for the claims queries", () => {
          const configService = {
            elasticPeopleIndex: faker.random.word()
          } as ConfigService;

          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          const pathSpecificQueries: Partial<
            Record<NetworkNestedDocPathForKeywordFilter, QueryDslQueryContainer>
          > = {
            trials: generateMockElasticsearchTermQuery(),
            publications: generateMockElasticsearchTermQuery(),
            congress: generateMockElasticsearchTermQuery(),
            DRG_diagnoses: generateMockElasticsearchTermQuery(),
            DRG_procedures: generateMockElasticsearchTermQuery()
          };

          const mockParseTreeToQueries =
            mockParseTreeToElasticsearchQueries(pathSpecificQueries);

          parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
            mockParseTreeToQueries
          );

          const networkQueryBuilder = new NetworkQueryBuilder(
            configService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const parsedQueryTree = faker.datatype.string();
          const personId = faker.datatype.uuid();
          const dateRange = {};
          const page = {
            offset: faker.datatype.number(),
            limit: faker.datatype.number()
          };
          const projectId = faker.datatype.string();
          const projectFeatures = {
            advancedOperators: true
          };
          const suppliedFilters = generateNetworkFilters();

          for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
            const request = {
              personId,
              dateRange,
              page,
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH,
              filterField,
              filterValue: faker.datatype.string()
            };

            const query = networkQueryBuilder.buildLocationFilterAutocomplete(
              request,
              parsedQueryTree,
              () => ENGLISH,
              generateFeatureFlags({
                enableBrazilianClaims: false
              })
            );

            expect(query).toEqual({
              index: configService.elasticPeopleIndex,
              size: 0,
              _source_includes: [],
              query: {
                bool: expect.objectContaining({
                  must: {
                    bool: expect.objectContaining({
                      must: expect.anything(),
                      should: [
                        {
                          nested: {
                            path: "publications",
                            score_mode: "sum",
                            query: {
                              bool: {
                                filter: [pathSpecificQueries.publications]
                              }
                            }
                          }
                        },
                        {
                          nested: {
                            path: "trials",
                            score_mode: "sum",
                            query: {
                              bool: {
                                filter: [pathSpecificQueries.trials]
                              }
                            }
                          }
                        },
                        {
                          nested: {
                            path: "congress",
                            score_mode: "sum",
                            query: {
                              bool: {
                                filter: [pathSpecificQueries.congress]
                              }
                            }
                          }
                        },
                        {
                          bool: {
                            must_not: [
                              expect.termsQuery("country_multi", ["Brazil"])
                            ],
                            must: [
                              {
                                nested: {
                                  path: "DRG_diagnoses",
                                  score_mode: "sum",
                                  query: {
                                    bool: {
                                      filter: [
                                        pathSpecificQueries.DRG_diagnoses
                                      ]
                                    }
                                  }
                                }
                              }
                            ]
                          }
                        },
                        {
                          bool: {
                            must_not: [
                              expect.termsQuery("country_multi", ["Brazil"])
                            ],
                            must: [
                              {
                                nested: {
                                  path: "DRG_procedures",
                                  score_mode: "sum",
                                  query: {
                                    bool: {
                                      filter: [
                                        pathSpecificQueries.DRG_procedures
                                      ]
                                    }
                                  }
                                }
                              }
                            ]
                          }
                        }
                      ],
                      minimum_should_match: 1
                    })
                  }
                })
              },
              aggs: expect.anything()
            });

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(
              parsedQueryTree,
              [
                "publications.publicationAbstract",
                "publications.keywords",
                "publications.title"
              ],
              expect.any(Function)
            );

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(
              parsedQueryTree,
              [
                "trials.briefTitle",
                "trials.conditions",
                "trials.interventions",
                "trials.keywords",
                "trials.officialTitle",
                "trials.summary"
              ],
              expect.any(Function)
            );

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(
              parsedQueryTree,
              ["congress.keywords", "congress.title"],
              expect.any(Function)
            );

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(
              parsedQueryTree,
              ["DRG_diagnoses.codeAndDescription"],
              expect.any(Function)
            );

            expect(
              parsedQueryTreeToElasticsearchQueriesService.parse
            ).toHaveBeenCalledWith(
              parsedQueryTree,
              ["DRG_procedures.codeAndDescription"],
              expect.any(Function)
            );
          }
        });
      });

      it("should include all location filters when values are supplied", async () => {
        const configService = {
          elasticPeopleIndex: faker.random.word()
        } as ConfigService;

        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const networkQueryBuilder = new NetworkQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const parsedQueryTree = faker.datatype.string();
        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        // faker can't produce reliable country codes that align with ours, so hardcode them
        const countries = ["us", "ca", "au"];
        const regions = ["us|PA", "ca|QC", "au|NSW"];
        const cities = [
          "us|PA|Allegheny|Amish City",
          "ca|QC|Mauricie|Apologyville",
          "au|NSW|Booroondarra|Kangarootown"
        ];
        const postalCodes = [
          "us|PA|Allegheny|Amish City|17366",
          "ca|QC|Mauricie|Apologyville|G3N 1C9",
          "au|NSW|Booroondarra|Kangarootown|2213"
        ];
        const suppliedFilters = generateNetworkFilters(undefined, undefined, {
          country: countries,
          region: regions,
          city: cities,
          postalCode: postalCodes
        });

        for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
          const filterValue = faker.datatype.string();
          const request = {
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH,
            filterField,
            filterValue
          };

          const query = networkQueryBuilder.buildLocationFilterAutocomplete(
            request,
            parsedQueryTree,
            () => ENGLISH,
            FEATURE_FLAGS
          );

          const locationFilters = [
            expect.termQuery("affiliations.type", "Work Affiliation"),
            expect.termQuery("affiliations.isCurrent", true)
          ];

          if (filterField === NetworkFilterAutocompleteField.CITY) {
            locationFilters.push({
              match_phrase_prefix: {
                "affiliations.institution.address.city": {
                  query: filterValue
                }
              }
            });
          } else {
            locationFilters.push(
              expect.termsQuery(
                "affiliations.institution.filters.city",
                suppliedFilters.collaboratorLocation!.city!
              )
            );
          }

          if (filterField === NetworkFilterAutocompleteField.COUNTRY) {
            locationFilters.push({
              match_phrase_prefix: {
                "affiliations.institution.address.country": {
                  query: filterValue
                }
              }
            });
          } else {
            locationFilters.push(
              expect.termsQuery(
                "affiliations.institution.filters.country",
                suppliedFilters.collaboratorLocation!.country!
              )
            );
          }

          if (filterField === NetworkFilterAutocompleteField.REGION) {
            locationFilters.push({
              match_phrase_prefix: {
                "affiliations.institution.address.region": {
                  query: filterValue
                }
              }
            });
          } else {
            locationFilters.push(
              expect.termsQuery(
                "affiliations.institution.filters.region",
                suppliedFilters.collaboratorLocation!.region!
              )
            );
          }

          if (filterField === NetworkFilterAutocompleteField.POSTAL_CODE) {
            locationFilters.push({
              regexp: {
                "affiliations.institution.filters.postal_code": `.+\\|${filterValue}[^|]*`
              }
            });
          } else {
            locationFilters.push(
              expect.termsQuery(
                "affiliations.institution.filters.postal_code",
                suppliedFilters.collaboratorLocation!.postalCode!
              )
            );
          }

          expect(query).toEqual(
            expect.objectContaining({
              index: configService.elasticPeopleIndex,
              size: 0,
              _source_includes: [],
              query: {
                bool: expect.objectContaining({
                  filter: [
                    expect.termQuery("collaboratorIds", personId),
                    {
                      bool: {
                        must_not: expect.termQuery("id", personId)
                      }
                    },
                    expect.projectIdFilter(projectId),
                    {
                      nested: {
                        path: "affiliations",
                        query: {
                          bool: {
                            filter: expect.arrayContaining(locationFilters)
                          }
                        }
                      }
                    }
                  ]
                })
              },
              aggs: {
                nested: {
                  nested: {
                    path: "affiliations"
                  },
                  aggs: {
                    filtered_matching: {
                      filter: {
                        bool: {
                          filter: expect.arrayContaining(locationFilters)
                        }
                      },
                      aggs: expect.any(Object)
                    }
                  }
                }
              }
            })
          );
        }
      });

      describe("country", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should include terms query when country values are supplied as full country names", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = ["United States", "Canada", "Thailand"];
            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.COUNTRY) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                parsedQueryTree,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.country", [
                  "us",
                  "ca",
                  "th"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should convert cmn/jpn names to codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = ["中國台灣省", "中国台湾省", "中国", "日本"];
            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.COUNTRY) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                parsedQueryTree,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.country", [
                  "tw",
                  "tw",
                  "cn",
                  "jp"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should not include country names that don't have known 2-character country codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = [
              "United States",
              "a value that is clearly not a country name",
              "Canada"
            ];
            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.COUNTRY) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                parsedQueryTree,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.country", [
                  "us",
                  "ca"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should not include a nested affiliations filter when no country codes are supported", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const parsedQueryTree = faker.datatype.string();
            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const countries = [
              "unknown country name 1",
              "unknown country name 2"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                country: countries
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.COUNTRY) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                parsedQueryTree,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId)
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: [
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });
        });
      });

      describe("region", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should include terms query with region name->code and corresponding country codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const regions = ["Pennsylvania", "Quebec"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                region: regions
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.REGION) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.region", [
                  "us|PA",
                  "ca|QC"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should skip regions with unknown region names", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const regions = ["Pennsylvania", "unknown region name", "Manitoba"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                region: regions
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.REGION) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.region", [
                  "us|PA",
                  "ca|MB"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should not include a region terms filter when none have known names", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const regions = ["unknown region name 1", "unknown region name 2"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                region: regions
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.REGION) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId)
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: [
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });
        });
      });

      describe("city", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should format 4-field cities as pipe-delimited country code + region + county + city", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, PA, United States",
              "Montreal, Montreal Region, QC, Canada"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.CITY) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.city", [
                  "us|PA|Lancaster County|Lancaster",
                  "ca|QC|Montreal Region|Montreal"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should convert full region names to codes", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, Pennsylvania, United States",
              "Montreal, Montreal Region, Quebec, Canada"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.CITY) {
                continue;
              }
              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.city", [
                  "us|PA|Lancaster County|Lancaster",
                  "ca|QC|Montreal Region|Montreal"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should convert to 2 values, one with a blank county and the other with a blank region, when filter value is in the ambiguous 3-field format", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, PA, United States",
              "Montreal, QC, Canada"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.CITY) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.city", [
                  "us|PA||Lancaster",
                  "us||PA|Lancaster",
                  "ca|QC||Montreal",
                  "ca||QC|Montreal"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should use a blank region and county name when the filter value is 2-field format", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = ["Berlin, Germany"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.CITY) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.city", [
                  "de|||Berlin"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should use the region name as supplied when abbreviation is not available for region", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = ["Asunción, Asunción, Capital District, Paraguay"];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.CITY) {
                continue;
              }
              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.city", [
                  "py|Capital District|Asunción|Asunción"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should not include cities with unknown countries", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, PA, United States",
              "Montreal, Montreal Region, QC, unknown country name"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.CITY) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.city", [
                  "us|PA|Lancaster County|Lancaster"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should ignore filter values with fewer than 2 fields", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "Lancaster, Lancaster County, PA, United States",
              "Pittsburgh, PA, United States",
              "New York, United States",
              "Montreal"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.CITY) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                expect.termsQuery("affiliations.institution.filters.city", [
                  "us|PA|Lancaster County|Lancaster",
                  "us|PA||Pittsburgh",
                  "us||PA|Pittsburgh",
                  "us|||New York"
                ]),
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should not include a city terms filter when no filter values are parseable", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const cities = [
              "city name, region code, unknown country name 1",
              "unqualified city name"
            ];

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                city: cities
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.CITY) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId)
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: [
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });
        });
      });

      describe("postal code", () => {
        describe("tests that can be removed when the UI supports id/label/count", () => {
          it("should use a regex filter when postal codes are not already pipe-delimited", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const zipCode1 = faker.address.zipCode();
            const zipCode2 = faker.address.zipCode();

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                postalCode: [zipCode1, zipCode2]
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.POSTAL_CODE) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                {
                  regexp: {
                    "affiliations.institution.filters.postal_code": {
                      value: `.+\\|(${zipCode1}|${zipCode2})`
                    }
                  }
                },
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should not include empty string values from non-pipe-delimited filter values", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const zipCode1 = faker.address.zipCode();
            const zipCode2 = faker.address.zipCode();

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                postalCode: [zipCode1, EMPTY_STRING, zipCode2]
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.POSTAL_CODE) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              const locationFilters = [
                {
                  regexp: {
                    "affiliations.institution.filters.postal_code": {
                      value: `.+\\|(${zipCode1}|${zipCode2})`
                    }
                  }
                },
                expect.termQuery("affiliations.type", "Work Affiliation"),
                expect.termQuery("affiliations.isCurrent", true)
              ];

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId),
                        {
                          nested: {
                            path: "affiliations",
                            query: {
                              bool: {
                                filter: locationFilters
                              }
                            }
                          }
                        }
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: locationFilters
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });

          it("should not include a postal_code terms filter when no filter values are parseable", async () => {
            const configService = {
              elasticPeopleIndex: faker.random.word()
            } as ConfigService;

            const parsedQueryTreeToElasticsearchQueriesService =
              createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

            const networkQueryBuilder = new NetworkQueryBuilder(
              configService,
              parsedQueryTreeToElasticsearchQueriesService
            );

            const personId = faker.datatype.uuid();
            const dateRange = {};
            const page = {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            };
            const projectId = faker.datatype.string();
            const projectFeatures = {
              advancedOperators: true
            };

            const suppliedFilters = generateNetworkFilters(
              undefined,
              undefined,
              {
                postalCode: []
              }
            );

            for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
              if (filterField === NetworkFilterAutocompleteField.POSTAL_CODE) {
                continue;
              }

              const request = {
                personId,
                dateRange,
                page,
                suppliedFilters,
                projectId,
                projectFeatures,
                language: ENGLISH,
                filterField,
                filterValue: EMPTY_STRING
              };

              const query = networkQueryBuilder.buildLocationFilterAutocomplete(
                request,
                undefined,
                () => ENGLISH,
                FEATURE_FLAGS
              );

              expect(query).toEqual(
                expect.objectContaining({
                  index: configService.elasticPeopleIndex,
                  size: 0,
                  _source_includes: [],
                  query: {
                    bool: expect.objectContaining({
                      filter: [
                        expect.termQuery("collaboratorIds", personId),
                        {
                          bool: {
                            must_not: expect.termQuery("id", personId)
                          }
                        },
                        expect.projectIdFilter(projectId)
                      ]
                    })
                  },
                  aggs: {
                    nested: {
                      nested: {
                        path: "affiliations"
                      },
                      aggs: {
                        filtered_matching: {
                          filter: {
                            bool: {
                              filter: [
                                expect.termQuery(
                                  "affiliations.type",
                                  "Work Affiliation"
                                ),
                                expect.termQuery("affiliations.isCurrent", true)
                              ]
                            }
                          },
                          aggs: expect.any(Object)
                        }
                      }
                    }
                  }
                })
              );
            }
          });
        });
      });
    });
  });
});
