import {
  QueryDslQueryContainer,
  SearchRequest,
  SortCombinations
} from "@elastic/elasticsearch/lib/api/types";
import { createLogger } from "../../lib/Logger";
import { CHINESE, JAPANESE, Language } from "../LanguageDetectService";
import {
  AT_LEAST_ONE,
  DEFAULT_NAME_VARIATION_MATCH_COUNT,
  getProjectIdBoostQuery
} from "./DefaultNameSearchBuilder";
import {
  HIGH_CONF_PERSON_INNER_HIT_QUERY,
  NameSearchBuilder,
  NameSearchBuilderArgWithKeyword,
  NameSearchBuilderArgWithoutKeyword,
  PROMINENCE_SCORE_WITH_TAG_COUNT
} from "./NameSearchBuilder";

const PROJECT_ID_BOOST = 2;
const RESEARCH_LEADER_BOOST = 2;
const KEYNOTE_SPEAKER_BOOST = 2;
const ALL_ASSETS_OPTIONAL = 0;

export default class ChineseJapaneseNameSearchBuilder
  implements NameSearchBuilder
{
  private readonly logger;
  private static instance: ChineseJapaneseNameSearchBuilder;

  private constructor() {
    this.logger = createLogger(this);
  }

  public static getInstance(): ChineseJapaneseNameSearchBuilder {
    if (!ChineseJapaneseNameSearchBuilder.instance) {
      ChineseJapaneseNameSearchBuilder.instance =
        new ChineseJapaneseNameSearchBuilder();
    }

    return ChineseJapaneseNameSearchBuilder.instance;
  }

  private getSourceFields(
    lang: Language,
    useCongressContributorRanking?: boolean
  ): string[] {
    let sourceFields = [
      `firstName_${lang}`,
      `lastName_${lang}`,
      `middleName_${lang}`,
      `name_${lang}`,
      "id",
      "h1dn_id",
      "designations",
      "totalWorks",
      "trialCount",
      "paymentTotal",
      `specialty_${lang}`,
      "publicationCount",
      "microBloggingTotal",
      "DRG_diagnosesCount",
      "DRG_proceduresCount",
      "num_prescriptions",
      "presentWorkInstitutionCount",
      "citationTotal",
      "congressCount",
      "affiliations",
      "hasCtms",
      "projectIds",
      "inCtmsNetwork",
      "locations",
      "isInactive",
      "isIndustry",
      "hasSocietyAffiliation",
      "trialEnrollmentRate",
      "totalPatientDocs"
    ];

    if (useCongressContributorRanking) {
      sourceFields = [...sourceFields, "congress"];
    }

    return sourceFields;
  }

  private getSortCriterion(lang: Language): Array<SortCombinations> {
    return [
      {
        _score: { order: "desc" }
      },
      {
        [`lastName_${lang}.sort`]: {
          order: "asc"
        }
      },
      {
        [`firstName_${lang}.sort`]: {
          order: "asc"
        }
      }
    ];
  }

  private getSortCriterionForBothLanguages(): Array<SortCombinations> {
    return [
      {
        _score: { order: "desc" }
      },
      {
        [`lastName_jpn.sort`]: {
          order: "asc"
        }
      },
      {
        [`lastName_cmn.sort`]: {
          order: "asc"
        }
      },
      {
        [`firstName_jpn.sort`]: {
          order: "asc"
        }
      },
      {
        [`firstName_cmn.sort`]: {
          order: "asc"
        }
      }
    ];
  }

  createNameSearchBody = (
    args: NameSearchBuilderArgWithKeyword
  ): SearchRequest => {
    const sizeOf = args.size;
    const from = args.from;
    const filter = args.filter;
    const lang = args.language;
    if (lang !== CHINESE && lang != JAPANESE) {
      this.logger.error(
        "Value different than cmn/jpn passed to ChineseJapaneseNameSearchBuilder."
      );
      throw new RangeError();
    }
    const combinedSourceFieldsForCmnAndJpn = new Set(
      this.getSourceFields("cmn").concat(this.getSourceFields("jpn"))
    );
    const _source =
      args.sourceOverride ?? args.useCmnAndJpnFields
        ? [...combinedSourceFieldsForCmnAndJpn]
        : this.getSourceFields(lang, args.useCongressContributorRanking);

    const res = {
      track_total_hits: true,
      track_scores: true,
      _source_includes: _source,
      from,
      size: sizeOf,
      query: this.nameQueryWithProminenceAndAttributeSupport(
        args.query,
        filter,
        lang,
        args.featureFlags.enableHighConfHCPFeature,
        from,
        args.projectId,
        args.useCmnAndJpnFields,
        args.useCongressContributorRanking,
        args.congressFilters
      ),
      sort: args.useCmnAndJpnFields
        ? this.getSortCriterionForBothLanguages()
        : this.getSortCriterion(lang)
    };

    return res;
  };

  createNameSearchBodyNoKeyWord = (
    args: NameSearchBuilderArgWithoutKeyword
  ): SearchRequest => {
    const sizeOf = args.size;
    const from = args.from;
    const filter = args.filter;
    const lang = args.language;
    const _source = args.useCmnAndJpnFields
      ? [
          ...new Set(
            this.getSourceFields("cmn").concat(this.getSourceFields("jpn"))
          )
        ]
      : this.getSourceFields(lang);
    return {
      track_total_hits: true,
      _source_includes: _source,
      from,
      size: sizeOf,
      query: {
        function_score: {
          query: {
            bool: {
              filter,
              must: [{ match_all: {} }]
            }
          },
          functions: PROMINENCE_SCORE_WITH_TAG_COUNT,
          // All script scores should be added
          boost_mode: "sum",
          // Final score from function score should be added to the document score
          score_mode: "sum"
        }
      },
      sort: args.useCmnAndJpnFields
        ? this.getSortCriterionForBothLanguages()
        : this.getSortCriterion(lang)
    };
  };

  private chineseNameVariationsQueries(
    query: string
  ): QueryDslQueryContainer[] {
    return [
      {
        term: {
          lastName_cmn: {
            value: query,
            boost: 1.5
          }
        }
      },
      {
        term: {
          name_cmn: {
            value: query,
            boost: 3
          }
        }
      },
      {
        prefix: {
          name_cmn: {
            value: query
          }
        }
      }
    ];
  }

  private japaneseNameVariationsQueries(
    query: string
  ): QueryDslQueryContainer[] {
    return [
      {
        term: {
          lastName_jpn: {
            _name: "lastName",
            value: query
          }
        }
      },
      {
        term: {
          name_jpn: {
            _name: "name",
            value: query
          }
        }
      }
    ];
  }

  private nameAndAttributeQueries(
    query: string,
    lang: Language
  ): QueryDslQueryContainer {
    return {
      bool: {
        filter: {
          match: {
            [`name_${lang}.search`]: {
              query: query
            }
          }
        },
        must: [
          {
            multi_match: {
              _name: "multi_match_name_and_attributes",
              query: query,
              operator: "and",
              type: "cross_fields",
              boost: 1,
              fields: [
                `name_${lang}.search^2`,
                `presentWorkInstitutionNames_${lang}.search`,
                `locations.country_${lang}.search`,
                `locations.state_${lang}.search`,
                `locations.city_${lang}.search^0.75`
              ]
            }
          }
        ]
      }
    };
  }

  private nameQueryWithProminenceAndAttributeSupport(
    query: string,
    filter: any,
    lang: Language,
    enableHighConfHCPFeature: boolean,
    pageNumber: number,
    projectId: string,
    useCmnAndJpnFields: boolean,
    useCongressContributorRanking?: boolean,
    congressFilters?: QueryDslQueryContainer[]
  ): QueryDslQueryContainer {
    return enableHighConfHCPFeature && pageNumber == 0
      ? this.nameQueryWithHighConfPersonNestedInfo(
          query,
          filter,
          lang,
          projectId,
          useCmnAndJpnFields,
          useCongressContributorRanking,
          congressFilters
        )
      : this.nameQueryWithoutHighConfPersonNestedInfo(
          query,
          filter,
          lang,
          projectId,
          useCmnAndJpnFields,
          useCongressContributorRanking,
          congressFilters
        );
  }

  private nameQueryWithoutHighConfPersonNestedInfo(
    query: string,
    filter: any,
    lang: Language,
    projectId: string,
    useCmnAndJpnFields: boolean,
    useCongressContributorRanking?: boolean,
    congressFilters?: QueryDslQueryContainer[]
  ): QueryDslQueryContainer {
    return {
      function_score: {
        query: {
          bool: {
            filter,
            must: [
              {
                bool: {
                  must: useCongressContributorRanking
                    ? this.getCongressContributorRankingQueries(
                        query,
                        congressFilters
                      )
                    : undefined,
                  should: this.getNameQueryShouldClauses(
                    query,
                    projectId,
                    useCmnAndJpnFields,
                    DEFAULT_NAME_VARIATION_MATCH_COUNT,
                    lang
                  ),
                  minimum_should_match: AT_LEAST_ONE
                }
              }
            ],
            should: getProjectIdBoostQuery(projectId, PROJECT_ID_BOOST)
          }
        },
        functions: PROMINENCE_SCORE_WITH_TAG_COUNT,
        boost_mode: "sum",
        score_mode: "sum"
      }
    };
  }

  public getNameQueryShouldClauses(
    query: string,
    projectId: string,
    useCmnAndJpnFields: boolean,
    _numberOfNameVariationsToMatch: number,
    lang: Language
  ): QueryDslQueryContainer[] {
    const shoulds: QueryDslQueryContainer[] = [];

    if (useCmnAndJpnFields) {
      shoulds.push(...this.chineseNameVariationsQueries(query));
      shoulds.push(...this.japaneseNameVariationsQueries(query));
      shoulds.push(this.nameAndAttributeQueries(query, "jpn"));
      shoulds.push(this.nameAndAttributeQueries(query, "cmn"));
    } else {
      lang === CHINESE
        ? shoulds.push(...this.chineseNameVariationsQueries(query))
        : shoulds.push(...this.japaneseNameVariationsQueries(query));
      shoulds.push(this.nameAndAttributeQueries(query, lang));
    }

    return shoulds;
  }

  private nameQueryWithHighConfPersonNestedInfo(
    query: string,
    filter: any,
    lang: Language,
    projectId: string,
    useCmnAndJpnFields: boolean,
    useCongressContributorRanking?: boolean,
    congressFilters?: QueryDslQueryContainer[]
  ): QueryDslQueryContainer {
    const congressContributorRankingQueries = useCongressContributorRanking
      ? this.getCongressContributorRankingQueries(query, congressFilters)
      : [];

    return {
      function_score: {
        query: {
          bool: {
            filter,
            must: [
              {
                bool: {
                  should: this.getNameQueryShouldClauses(
                    query,
                    projectId,
                    useCmnAndJpnFields,
                    DEFAULT_NAME_VARIATION_MATCH_COUNT,
                    lang
                  ),
                  minimum_should_match: AT_LEAST_ONE
                }
              },
              ...congressContributorRankingQueries
            ],
            should: [
              ...HIGH_CONF_PERSON_INNER_HIT_QUERY,
              getProjectIdBoostQuery(projectId, PROJECT_ID_BOOST)
            ]
          }
        },
        functions: PROMINENCE_SCORE_WITH_TAG_COUNT,
        boost_mode: "sum",
        score_mode: "sum"
      }
    };
  }

  private getCongressContributorRankingQueries(
    query: string,
    congressFilters?: QueryDslQueryContainer[]
  ): QueryDslQueryContainer[] {
    if (!congressFilters?.length) {
      return [
        {
          match_all: {}
        }
      ];
    }

    const filters =
      congressFilters[0].function_score?.query?.nested?.query?.bool?.filter ??
      congressFilters[0].nested?.query.bool?.filter;

    const congressNestedQuery: QueryDslQueryContainer = {
      nested: {
        path: "congress",
        query: {
          function_score: {
            query: {
              bool: {
                filter: filters,
                should: [
                  {
                    simple_query_string: {
                      query,
                      fields: [
                        "congress.role.search",
                        "congress.title_eng",
                        "congress.title_cmn",
                        "congress.title_jpn"
                      ],
                      default_operator: "OR"
                    }
                  }
                ],
                minimum_should_match: ALL_ASSETS_OPTIONAL
              }
            },
            functions: [
              {
                script_score: {
                  script: {
                    source: `doc['congress.role'].value === 'Keynote' ? ${KEYNOTE_SPEAKER_BOOST} : 1`
                  }
                }
              }
            ]
          }
        }
      }
    };

    const isResearchLeaderQuery = {
      term: {
        isScholarLeader: {
          value: true,
          boost: RESEARCH_LEADER_BOOST
        }
      }
    };

    return [
      {
        bool: {
          should: [congressNestedQuery, isResearchLeaderQuery]
        }
      }
    ];
  }
}
