import {
  QueryDslQueryContainer,
  SearchRequest,
  AggregationsAggregationContainer,
  QueryDslBoolQuery,
  Sort,
  SortCombinations
} from "@elastic/elasticsearch/lib/api/types";
import {
  NetworkRequest,
  NetworkCollaboratorRequest,
  NetworkFilterAutocompleteRequest,
  NetworkFilterAutocompleteField,
  CollaborationTypeFilter,
  NetworkFilters,
  SharedWorksPage,
  NetworkSortBy,
  BulkNetworkRequest
} from "@h1nyc/search-sdk";
import { Service } from "typedi";
import {
  LanguageDetector,
  ParsedQueryTree
} from "../KeywordSearchResourceServiceRewrite";
import { ParsedQueryTreeToElasticsearchQueriesService } from "../ParsedQueryTreeToElasticsearchQueries";
import { ConfigService } from "../ConfigService";
import { ENGLISH } from "../LanguageDetectService";
import { COUNTRY_NAME_TO_CODE } from "../../lib/data/countries";
import { REGION_CODES_BY_COUNTRY_CODE_AND_REGION_NAME } from "../../lib/data/regions";
import * as _ from "lodash";
import { NetworkFeatureFlags } from "../NetworkResourceService";
import { buildClaimsRegionFilterForPeopleSearch } from "../../util/ClaimsRegionFilterUtils";
import { buildMatchPhrasePrefixQuery } from "../../util/QueryBuildingUtils";

const SOURCE_FIELDS = ["id"];
const AFFILIATIONS_INNER_HITS_SIZE = 10;
const AFFILIATIONS = "affiliations";
const AFFILIATIONS_INSTITUTION = "affiliations.institution";
const AFFILIATIONS_SOURCE_FIELDS: Readonly<Array<string>> = [
  "type",
  "isCurrent",
  "institution.id",
  "institution.ultimateParentId",
  "insitution.name",
  "institution.address",
  "institution.nameTranslations",
  "institution.addressTranslations"
];
const ALL_ASSETS_OPTIONAL = 0;
const AT_LEAST_ONE_ASSET = 1;
const WORK_AFFILIATION: Readonly<QueryDslQueryContainer> = {
  term: {
    [`${AFFILIATIONS}.type`]: "Work Affiliation"
  }
};
const IS_CURRENT_AFFILIATION: Readonly<QueryDslQueryContainer> = {
  term: {
    [`${AFFILIATIONS}.isCurrent`]: true
  }
};
const USE_ENGLISH_FIELDS: LanguageDetector = () => ENGLISH;
const EMPTY_SET: Readonly<Set<string>> = new Set<string>();
const ADD_1_TO_SCORE_PER_INNER_HIT = 1.0;
const ONE_COLLABORATOR = 1;
const DESCENDING = "desc";
const ASCENDING = "asc";
const ZERO_INNER_HITS = 0;
const EMPTY_STRING = "";

const pathQualifiedAffiliationsFields = AFFILIATIONS_SOURCE_FIELDS.map(
  (field: string) => {
    return `${AFFILIATIONS}.${field}`;
  }
);

const LOCATIONS_QUERY: Readonly<QueryDslQueryContainer> = {
  nested: {
    path: AFFILIATIONS,
    query: {
      bool: {
        filter: [IS_CURRENT_AFFILIATION, WORK_AFFILIATION]
      }
    },
    inner_hits: {
      name: "locations",
      _source: {
        includes: pathQualifiedAffiliationsFields
      },
      size: AFFILIATIONS_INNER_HITS_SIZE
    }
  }
};

const SORT_TIEBREAKER: SortCombinations[] = [
  {
    _score: { order: DESCENDING }
  },
  {
    id: {
      order: ASCENDING
    }
  },
  {
    h1dn_id: {
      order: ASCENDING
    }
  }
];

export const networkNestedDocTypes = [
  "publications",
  "trials",
  "congress"
] as const;
export const totalCollaborators = "total_collaborators";
export type NetworkNestedDocPath = (typeof networkNestedDocTypes)[number];
const nestedDocTypes = networkNestedDocTypes;
type NestedDocPath = NetworkNestedDocPath;

const nestedDocTypesForKeywordFilter = [
  "publications",
  "trials",
  "congress",
  "DRG_diagnoses",
  "DRG_procedures"
] as const;
export type NetworkNestedDocPathForKeywordFilter =
  (typeof nestedDocTypesForKeywordFilter)[number];
type NestedDocPathForKeywordFilter = NetworkNestedDocPathForKeywordFilter;

type NestedDocFields = {
  dateField: Readonly<string>;
  sourceFields: Readonly<Array<string>>;
  docvalueFields: Readonly<Array<string>>;
} & NestedDocFieldsForKeywordFilter;

type NestedDocFieldsForKeywordFilter = {
  textFields: Readonly<Array<string>>;
};

export const nestedDocs: Readonly<
  Record<NestedDocPath, Readonly<NestedDocFields>>
> = {
  publications: {
    textFields: ["publicationAbstract", "keywords", "title"],
    dateField: "datePublished",
    sourceFields: [
      "id",
      "datePublished",
      "persons",
      "title_eng",
      "title_jpn",
      "title_cmn",
      "languageCode"
    ],
    docvalueFields: [
      "type_eng",
      "type_jpn",
      "type_cmn",
      "journalName_eng",
      "journalName_jpn",
      "journalName_cmn"
    ]
  },
  trials: {
    textFields: [
      "briefTitle",
      "conditions",
      "interventions",
      "keywords",
      "officialTitle",
      "summary"
    ],
    dateField: "startDate",
    sourceFields: [
      "id",
      "officialTitle_eng",
      "phase_eng",
      "status_eng",
      "sponsor_eng",
      "startDate",
      "persons"
    ],
    docvalueFields: []
  },
  congress: {
    textFields: ["keywords", "title"],
    dateField: "masterInitialDate",
    sourceFields: ["id", "title_eng", "persons", "type", "masterInitialDate"],
    docvalueFields: ["organizer_eng", "name_eng"]
  }
};

export const availableMultiLangSourceFields: Readonly<
  Record<NestedDocPathForKeywordFilter, Set<string>>
> = {
  publications: new Set(["title", "type", "journalName"]),
  congress: EMPTY_SET,
  trials: EMPTY_SET,
  DRG_diagnoses: EMPTY_SET,
  DRG_procedures: EMPTY_SET
};

export const nestedDocsForKeywordFilter: Readonly<
  Record<
    NestedDocPathForKeywordFilter,
    Readonly<NestedDocFieldsForKeywordFilter>
  >
> = {
  publications: {
    textFields: nestedDocs.publications.textFields
  },
  trials: {
    textFields: nestedDocs.trials.textFields
  },
  congress: {
    textFields: nestedDocs.congress.textFields
  },
  DRG_diagnoses: {
    textFields: ["codeAndDescription"]
  },
  DRG_procedures: {
    textFields: ["codeAndDescription"]
  }
};

const pathQualifiedTextFields: Readonly<Record<NestedDocPath, Array<string>>> =
  qualifyNestedFields("textFields");
const pathQualifiedSourceFields: Readonly<
  Record<NestedDocPath, Array<string>>
> = qualifyNestedFields("sourceFields");
const pathQualifiedDocvalueFields: Readonly<
  Record<NestedDocPath, Array<string>>
> = qualifyNestedFields("docvalueFields");

function qualifyNestedFields(
  fieldType: keyof Omit<NestedDocFields, "dateField">
) {
  return nestedDocTypes.reduce((acc, path: NestedDocPath) => {
    return {
      ...acc,
      [path]: nestedDocs[path][fieldType].map((field) => `${path}.${field}`)
    };
  }, {} as Record<NestedDocPath, Array<string>>);
}

const pathQualifiedTextFieldsForKeywordFilter: Readonly<
  Record<NestedDocPathForKeywordFilter, Array<string>>
> = nestedDocTypesForKeywordFilter.reduce(
  (acc, path: NestedDocPathForKeywordFilter) => {
    return {
      ...acc,
      [path]: nestedDocsForKeywordFilter[path]["textFields"].map(
        (field) => `${path}.${field}`
      )
    };
  },
  {} as Record<NestedDocPathForKeywordFilter, Array<string>>
);

export const nestedDocTypeToCollaborationType: Readonly<
  Record<NestedDocPath, keyof CollaborationTypeFilter>
> = {
  publications: "publications",
  congress: "congresses",
  trials: "trials"
};

export const nestedDocTypeToOffset: Readonly<
  Record<NestedDocPath, keyof SharedWorksPage>
> = {
  publications: "publicationOffset",
  congress: "congressOffset",
  trials: "trialOffset"
};

export const locationFilterFields: Readonly<
  Record<NetworkFilterAutocompleteField, string>
> = {
  [NetworkFilterAutocompleteField.CITY]: `${AFFILIATIONS_INSTITUTION}.filters.city`,
  [NetworkFilterAutocompleteField.REGION]: `${AFFILIATIONS_INSTITUTION}.filters.region`,
  [NetworkFilterAutocompleteField.POSTAL_CODE]: `${AFFILIATIONS_INSTITUTION}.filters.postal_code`,
  [NetworkFilterAutocompleteField.COUNTRY]: `${AFFILIATIONS_INSTITUTION}.filters.country`
};

const nestedDocTypeToSortBy: Readonly<Record<NestedDocPath, NetworkSortBy>> = {
  publications: NetworkSortBy.PUBLICATIONS,
  congress: NetworkSortBy.CONGRESSES,
  trials: NetworkSortBy.TRIALS
};

@Service()
export class NetworkQueryBuilder {
  private peopleIndexName;

  constructor(
    config: ConfigService,
    private parsedQueryTreeToElasticsearchQueriesService: ParsedQueryTreeToElasticsearchQueriesService
  ) {
    this.peopleIndexName = config.elasticPeopleIndex;
  }

  public buildFindNetworkQuery(
    request: Readonly<NetworkRequest>,
    affiliatedInstitutionIds: string[],
    parsedQueryTree: Readonly<ParsedQueryTree>,
    languageDetector: LanguageDetector,
    featureFlags: Readonly<NetworkFeatureFlags>
  ): Readonly<SearchRequest> | null {
    const { suppliedFilters, page } = request;
    const { limit: size, offset: from } = page;

    const {
      requiredCollaborationTypesToSearchOn,
      optionalCollaborationTypesToSearchOn
    } = this.applyCollaborationTypeFilter(suppliedFilters);

    if (requiredCollaborationTypesToSearchOn.length === 0) {
      return null;
    }

    const must = this.buildMustClauseToFindNetwork(
      request,
      parsedQueryTree,
      languageDetector,
      requiredCollaborationTypesToSearchOn,
      featureFlags
    );

    const optionalShoulds = this.buildOptionalShouldQueries(
      request,
      affiliatedInstitutionIds,
      optionalCollaborationTypesToSearchOn
    );

    const filter = this.buildFilters(request);

    const query: QueryDslQueryContainer = {
      bool: {
        must,
        should: optionalShoulds,
        minimum_should_match: ALL_ASSETS_OPTIONAL,
        filter
      }
    };

    const aggs: Record<string, AggregationsAggregationContainer> = {};
    for (const path of nestedDocTypes) {
      aggs[path] = this.buildNestedAggregation(path, request);
    }

    const sort = this.buildSort(request, affiliatedInstitutionIds);

    return {
      index: this.peopleIndexName,
      from,
      size,
      _source_includes: SOURCE_FIELDS,
      query,
      aggs,
      sort
    };
  }

  public buildBulkFindNetworkQuery(
    request: Readonly<BulkNetworkRequest>,
    affiliatedInstitutionIds: string[],
    parsedQueryTree: Readonly<ParsedQueryTree>,
    languageDetector: LanguageDetector,
    featureFlags: Readonly<NetworkFeatureFlags>
  ): Readonly<SearchRequest> | null {
    const { suppliedFilters, page } = request;
    const { offset: from } = page;

    const {
      requiredCollaborationTypesToSearchOn,
      optionalCollaborationTypesToSearchOn
    } = this.applyCollaborationTypeFilter(suppliedFilters);

    if (requiredCollaborationTypesToSearchOn.length === 0) {
      return null;
    }

    const must = this.buildMustClauseToFindBulkNetwork(
      request,
      parsedQueryTree,
      languageDetector,
      requiredCollaborationTypesToSearchOn,
      featureFlags
    );

    const optionalShoulds = this.buildOptionalShouldQueriesForBulkRequest(
      request,
      affiliatedInstitutionIds,
      optionalCollaborationTypesToSearchOn
    );

    const filter = this.buildBulkFilters(request);

    const query: QueryDslQueryContainer = {
      bool: {
        must,
        should: optionalShoulds,
        minimum_should_match: ALL_ASSETS_OPTIONAL,
        filter
      }
    };

    const aggs: Record<string, AggregationsAggregationContainer> = {};
    for (const path of nestedDocTypes) {
      aggs[path] = this.buildNestedAggregationForBulkRequest(path, request);
    }

    aggs[totalCollaborators] =
      this.buildTotalCollaboratorCountAggregation(request);

    const sort = this.buildSort(request, affiliatedInstitutionIds);

    return {
      index: this.peopleIndexName,
      from,
      size: 0,
      _source_includes: SOURCE_FIELDS,
      query,
      aggs,
      sort
    };
  }

  private buildSort(
    { sortBy, language }: NetworkRequest | BulkNetworkRequest,
    affiliatedInstitutionIds: string[]
  ): Sort {
    switch (sortBy) {
      case NetworkSortBy.NAME: {
        return [
          {
            [`firstName_${language}.sort`]: ASCENDING
          },
          {
            [`lastName_${language}.sort`]: ASCENDING
          },
          ...SORT_TIEBREAKER
        ];
      }
      case NetworkSortBy.AFFILIATIONS: {
        return [
          {
            [`${AFFILIATIONS_INSTITUTION}.name.keyword`]: {
              order: ASCENDING,
              nested: {
                path: AFFILIATIONS,
                filter: this.buildAffiliationsTermsQuery(
                  affiliatedInstitutionIds
                )
              }
            }
          },
          ...SORT_TIEBREAKER
        ];
      }
      default: {
        return SORT_TIEBREAKER;
      }
    }
  }

  private buildOptionalShouldQueries(
    request: Readonly<NetworkRequest>,
    affiliatedInstitutionIds: Readonly<string[]>,
    optionalCollaborationTypesToSearchOn: Readonly<NestedDocPath[]>
  ): Readonly<QueryDslQueryContainer>[] {
    const optionalShoulds = [];

    if (affiliatedInstitutionIds.length) {
      const sharedAffiliationsQuery = this.buildSharedAffiliationsQuery(
        affiliatedInstitutionIds
      );

      optionalShoulds.push(sharedAffiliationsQuery);
    }

    optionalShoulds.push(LOCATIONS_QUERY);

    optionalCollaborationTypesToSearchOn.forEach((nestedDocPath) => {
      const nestedDocQuery = this.buildNestedDocumentQuery(
        nestedDocPath,
        request
      );
      optionalShoulds.push(nestedDocQuery);
    });

    return optionalShoulds;
  }

  private buildOptionalShouldQueriesForBulkRequest(
    request: Readonly<BulkNetworkRequest>,
    affiliatedInstitutionIds: Readonly<string[]>,
    optionalCollaborationTypesToSearchOn: Readonly<NestedDocPath[]>
  ): Readonly<QueryDslQueryContainer>[] {
    const optionalShoulds = [];

    if (affiliatedInstitutionIds.length) {
      const sharedAffiliationsQuery = this.buildSharedAffiliationsQuery(
        affiliatedInstitutionIds
      );

      optionalShoulds.push(sharedAffiliationsQuery);
    }

    optionalShoulds.push(LOCATIONS_QUERY);

    optionalCollaborationTypesToSearchOn.forEach((nestedDocPath) => {
      const nestedDocQuery = this.buildNestedDocumentQueryForBulkRequest(
        nestedDocPath,
        request
      );
      optionalShoulds.push(nestedDocQuery);
    });

    return optionalShoulds;
  }

  private applyCollaborationTypeFilter({
    collaborationType
  }: NetworkFilters): Readonly<{
    requiredCollaborationTypesToSearchOn: NestedDocPath[];
    optionalCollaborationTypesToSearchOn: NestedDocPath[];
  }> {
    const requiredCollaborationTypesToSearchOn: NestedDocPath[] = [];
    const optionalCollaborationTypesToSearchOn: NestedDocPath[] = [];

    for (const type of nestedDocTypes) {
      const convertedType = nestedDocTypeToCollaborationType[type];

      if (collaborationType?.[convertedType]) {
        requiredCollaborationTypesToSearchOn.push(type);
      } else {
        optionalCollaborationTypesToSearchOn.push(type);
      }
    }

    return {
      requiredCollaborationTypesToSearchOn,
      optionalCollaborationTypesToSearchOn
    };
  }

  public buildFindAffiliationsQuery({
    personId
  }: {
    personId: string;
  }): Readonly<SearchRequest> {
    return {
      index: this.peopleIndexName,
      _source_includes: [`${AFFILIATIONS_INSTITUTION}.id`],
      query: {
        term: {
          id: personId
        }
      }
    };
  }

  public buildFindAffiliationsQueryForBulkNetworkRequest(
    peronIds: string[]
  ): Readonly<SearchRequest> {
    return {
      index: this.peopleIndexName,
      _source_includes: [`${AFFILIATIONS_INSTITUTION}.id`],
      query: {
        terms: {
          id: peronIds
        }
      }
    };
  }

  public buildFindCollaboratorQuery(
    request: NetworkCollaboratorRequest,
    affiliatedInstitutionIds: string[],
    parsedQueryTree: Readonly<ParsedQueryTree>,
    languageDetector: LanguageDetector
  ): Readonly<SearchRequest> {
    const { suppliedFilters } = request;
    const {
      requiredCollaborationTypesToSearchOn,
      optionalCollaborationTypesToSearchOn
    } = this.applyCollaborationTypeFilter(suppliedFilters);

    const nestedDocumentQueries = requiredCollaborationTypesToSearchOn.map(
      (nestedDocPath) => {
        return this.buildNestedDocumentQueryToRetrieveSharedWorks(
          nestedDocPath,
          request,
          parsedQueryTree,
          languageDetector
        );
      }
    );

    nestedDocumentQueries.push(
      ...optionalCollaborationTypesToSearchOn.map((nestedDocPath) => {
        return this.buildNestedDocumentQueryToRetrieveSharedWorks(
          nestedDocPath,
          request,
          parsedQueryTree,
          languageDetector,
          false
        );
      })
    );

    const bool: QueryDslBoolQuery = {
      should: nestedDocumentQueries,
      minimum_should_match: AT_LEAST_ONE_ASSET
    };

    let query: QueryDslQueryContainer;
    if (affiliatedInstitutionIds.length) {
      const sharedAffiliationsQuery = this.buildSharedAffiliationsQuery(
        affiliatedInstitutionIds
      );

      query = {
        bool: {
          must: {
            bool
          },
          should: [sharedAffiliationsQuery],
          minimum_should_match: ALL_ASSETS_OPTIONAL
        }
      };
    } else {
      query = {
        bool
      };
    }

    query.bool!.filter = [
      {
        term: {
          collaboratorIds: request.personId
        }
      },
      {
        term: {
          id: request.collaboratorId
        }
      }
    ];

    return {
      index: this.peopleIndexName,
      size: ONE_COLLABORATOR,
      _source_includes: SOURCE_FIELDS,
      query
    };
  }

  public buildLocationFilterAutocomplete(
    request: NetworkFilterAutocompleteRequest,
    parsedQueryTree: Readonly<ParsedQueryTree>,
    languageDetector: LanguageDetector,
    featureFlags: Readonly<NetworkFeatureFlags>
  ): Readonly<SearchRequest> | null {
    const { suppliedFilters, filterField, filterValue } = request;
    const { requiredCollaborationTypesToSearchOn } =
      this.applyCollaborationTypeFilter(suppliedFilters);

    if (requiredCollaborationTypesToSearchOn.length === 0) {
      return null;
    }

    const must = this.buildMustClauseToFindNetwork(
      request,
      parsedQueryTree,
      languageDetector,
      requiredCollaborationTypesToSearchOn,
      featureFlags
    );
    const filter = this.buildFilters(request, filterField, filterValue);

    const query: QueryDslQueryContainer = {
      bool: {
        must,
        filter
      }
    };

    const nestedLocationFilters = this.buildLocationFilters(
      suppliedFilters,
      filterField,
      filterValue
    );

    const locationFilters = nestedLocationFilters[0]?.nested?.query?.bool
      ?.filter ?? [WORK_AFFILIATION, IS_CURRENT_AFFILIATION];

    const aggs: Record<string, AggregationsAggregationContainer> = {
      nested: {
        nested: {
          path: AFFILIATIONS
        },
        aggs: {
          filtered_matching: {
            filter: {
              bool: {
                filter: locationFilters
              }
            },
            aggs: {
              matching: {
                terms: {
                  field: locationFilterFields[filterField]
                },
                aggs: {
                  people: {
                    reverse_nested: {}
                  }
                }
              }
            }
          }
        }
      }
    };

    return {
      index: this.peopleIndexName,
      size: 0,
      _source_includes: [],
      query,
      aggs
    };
  }

  private buildMustClauseToFindNetwork(
    request: Readonly<NetworkRequest | NetworkFilterAutocompleteRequest>,
    parsedQueryTree: Readonly<ParsedQueryTree>,
    languageDetector: LanguageDetector,
    requiredCollaborationTypesToSearchOn: Readonly<NestedDocPath[]>,
    featureFlags: Readonly<NetworkFeatureFlags>
  ): Readonly<QueryDslQueryContainer> {
    const requiredCollaborationQueries =
      requiredCollaborationTypesToSearchOn.map((nestedDocPath) => {
        return this.buildNestedDocumentQuery(nestedDocPath, request);
      });

    const must = {
      bool: {
        should: requiredCollaborationQueries,
        minimum_should_match: AT_LEAST_ONE_ASSET
      }
    };

    let finalMust: QueryDslQueryContainer;
    if (parsedQueryTree) {
      const keywordFilterQueries = nestedDocTypesForKeywordFilter.map(
        (nestedDocPath) => {
          const languageToUse =
            availableMultiLangSourceFields[nestedDocPath].size > 0
              ? languageDetector
              : USE_ENGLISH_FIELDS;
          return this.buildNestedDocumentQueryWithKeyword(
            nestedDocPath,
            parsedQueryTree,
            languageToUse,
            featureFlags
          );
        }
      );

      finalMust = {
        bool: {
          must,
          should: keywordFilterQueries,
          minimum_should_match: AT_LEAST_ONE_ASSET
        }
      };
    } else {
      finalMust = must;
    }

    return finalMust;
  }

  private buildMustClauseToFindBulkNetwork(
    request: Readonly<BulkNetworkRequest>,
    parsedQueryTree: Readonly<ParsedQueryTree>,
    languageDetector: LanguageDetector,
    requiredCollaborationTypesToSearchOn: Readonly<NestedDocPath[]>,
    featureFlags: Readonly<NetworkFeatureFlags>
  ): Readonly<QueryDslQueryContainer> {
    const requiredCollaborationQueries =
      requiredCollaborationTypesToSearchOn.map((nestedDocPath) => {
        return this.buildNestedDocumentQueryForBulkRequest(
          nestedDocPath,
          request
        );
      });

    const must = {
      bool: {
        should: requiredCollaborationQueries,
        minimum_should_match: AT_LEAST_ONE_ASSET
      }
    };

    let finalMust: QueryDslQueryContainer;
    if (parsedQueryTree) {
      const keywordFilterQueries = nestedDocTypesForKeywordFilter.map(
        (nestedDocPath) => {
          const languageToUse =
            availableMultiLangSourceFields[nestedDocPath].size > 0
              ? languageDetector
              : USE_ENGLISH_FIELDS;
          return this.buildNestedDocumentQueryWithKeyword(
            nestedDocPath,
            parsedQueryTree,
            languageToUse,
            featureFlags
          );
        }
      );

      finalMust = {
        bool: {
          must,
          should: keywordFilterQueries,
          minimum_should_match: AT_LEAST_ONE_ASSET
        }
      };
    } else {
      finalMust = must;
    }

    return finalMust;
  }

  private buildSharedAffiliationsQuery(
    affiliatedInstitutionIds: Readonly<string[]>
  ): Readonly<QueryDslQueryContainer> {
    return {
      nested: {
        path: AFFILIATIONS,
        query: this.buildAffiliationsTermsQuery(affiliatedInstitutionIds),
        inner_hits: {
          name: "sharedAffiliations",
          _source: {
            includes: pathQualifiedAffiliationsFields
          },
          size: AFFILIATIONS_INNER_HITS_SIZE
        }
      }
    };
  }

  private buildNestedDocumentQueryWithKeyword(
    path: NestedDocPathForKeywordFilter,
    parsedQueryTree: Readonly<NonNullable<ParsedQueryTree>>,
    languageDetector: LanguageDetector,
    featureFlags: Readonly<NetworkFeatureFlags>
  ): Readonly<QueryDslQueryContainer> {
    const textQueryFilter =
      this.parsedQueryTreeToElasticsearchQueriesService.parse(
        parsedQueryTree,
        pathQualifiedTextFieldsForKeywordFilter[path],
        languageDetector
      );

    const nestedDocumentQuery: QueryDslQueryContainer = {
      nested: {
        path,
        score_mode: "sum",
        query: {
          bool: {
            filter: [textQueryFilter]
          }
        }
      }
    };

    if (path === "DRG_diagnoses" || path === "DRG_procedures") {
      return this.buildClaimsQueryWithRegionFilter(
        nestedDocumentQuery,
        featureFlags
      );
    }

    return nestedDocumentQuery;
  }

  private buildClaimsQueryWithRegionFilter(
    nestedDocumentQuery: Readonly<QueryDslQueryContainer>,
    featureFlags: Readonly<NetworkFeatureFlags>
  ) {
    const claimsRegionFilter =
      buildClaimsRegionFilterForPeopleSearch(featureFlags);

    if (claimsRegionFilter) {
      return {
        bool: {
          must_not: [claimsRegionFilter],
          must: [nestedDocumentQuery]
        }
      };
    }

    return nestedDocumentQuery;
  }

  private getBoostForNestedDocument(
    path: NestedDocPath,
    sortBy: NetworkSortBy | null | undefined
  ) {
    if (
      sortBy == null ||
      sortBy === NetworkSortBy.TOTAL_SHARED_WORKS ||
      nestedDocTypeToSortBy[path] === sortBy
    ) {
      return ADD_1_TO_SCORE_PER_INNER_HIT;
    }

    return 0;
  }

  private buildNestedDocumentQuery(
    path: NestedDocPath,
    request: Readonly<NetworkRequest | NetworkFilterAutocompleteRequest>
  ): Readonly<QueryDslQueryContainer> {
    const filters = this.buildFiltersForNestedDocument(path, request);

    if (!_.isNil(request.dateRange?.min)) {
      const minimumDateQueryFilter = this.toMinimumDateRangeQuery(
        path,
        nestedDocs[path].dateField,
        request.dateRange!.min
      );

      filters.push(minimumDateQueryFilter);
    }

    return {
      nested: {
        path,
        score_mode: "sum",
        query: {
          constant_score: {
            filter: {
              bool: {
                filter: filters
              }
            },
            boost: this.getBoostForNestedDocument(path, request.sortBy)
          }
        },
        inner_hits: {
          _source: false
        }
      }
    };
  }

  private buildNestedDocumentQueryForBulkRequest(
    path: NestedDocPath,
    request: Readonly<BulkNetworkRequest>
  ): Readonly<QueryDslQueryContainer> {
    const filters = this.buildFiltersForNestedDocumentForBulkRequest(
      path,
      request
    );

    if (!_.isNil(request.dateRange?.min)) {
      const minimumDateQueryFilter = this.toMinimumDateRangeQuery(
        path,
        nestedDocs[path].dateField,
        request.dateRange!.min
      );

      filters.push(minimumDateQueryFilter);
    }

    return {
      nested: {
        path,
        score_mode: "sum",
        query: {
          constant_score: {
            filter: {
              bool: {
                filter: filters
              }
            },
            boost: this.getBoostForNestedDocument(path, request.sortBy)
          }
        },
        inner_hits: {
          _source: false
        }
      }
    };
  }

  private buildNestedDocumentQueryToRetrieveSharedWorks(
    path: NestedDocPath,
    request: Readonly<NetworkCollaboratorRequest>,
    parsedQueryTree: Readonly<ParsedQueryTree>,
    languageDetector: LanguageDetector,
    shouldIncludeThisCollaborationType = true
  ): Readonly<QueryDslQueryContainer> {
    const offset = nestedDocTypeToOffset[path];
    const { page } = request;
    const { limit: size, [offset]: from } = page;
    const filters = this.buildFiltersForNestedDocumentWithKeyword(
      path,
      request,
      parsedQueryTree,
      languageDetector
    );

    return {
      nested: {
        path,
        score_mode: "sum",
        query: {
          bool: {
            filter: filters
          }
        },
        inner_hits: {
          _source: {
            include: pathQualifiedSourceFields[path]
          },
          docvalue_fields: pathQualifiedDocvalueFields[path],
          from,
          size: shouldIncludeThisCollaborationType ? size : ZERO_INNER_HITS,
          sort: [
            {
              [`${path}.${nestedDocs[path].dateField}`]: {
                order: DESCENDING
              }
            }
          ]
        }
      }
    };
  }

  private buildNestedAggregation(
    path: NestedDocPath,
    request: Readonly<NetworkRequest>
  ): Readonly<AggregationsAggregationContainer> {
    const filters = this.buildFiltersForNestedDocument(path, request);

    return {
      nested: {
        path: path
      },
      aggs: {
        nested: {
          filter: {
            bool: {
              filter: filters
            }
          },
          aggs: {
            people: {
              reverse_nested: {}
            }
          }
        }
      }
    };
  }

  private buildNestedAggregationForBulkRequest(
    path: NestedDocPath,
    request: Readonly<BulkNetworkRequest>
  ): Readonly<AggregationsAggregationContainer> {
    const filters = this.buildFiltersForNestedDocumentForBulkRequest(
      path,
      request
    );

    return {
      nested: {
        path: path
      },
      aggs: {
        nested: {
          filter: {
            bool: {
              filter: filters
            }
          },
          aggs: {
            per_person: {
              terms: {
                field: `${path}.persons.id`,
                size: request.personIds.length,
                include: request.personIds
              },
              aggs: {
                people: {
                  reverse_nested: {}
                }
              }
            }
          }
        }
      }
    };
  }

  private buildTotalCollaboratorCountAggregation(
    request: Readonly<BulkNetworkRequest>
  ): Readonly<AggregationsAggregationContainer> {
    return {
      terms: {
        field: "collaboratorIds",
        size: request.personIds.length,
        include: request.personIds
      }
    };
  }

  private buildFilters(
    {
      personId,
      projectId,
      suppliedFilters
    }: Readonly<NetworkRequest | NetworkFilterAutocompleteRequest>,
    filterField?: NetworkFilterAutocompleteField,
    filterValue?: string
  ) {
    const filters: QueryDslQueryContainer[] = [
      {
        term: {
          collaboratorIds: personId
        }
      },
      {
        bool: {
          must_not: {
            term: {
              id: personId
            }
          }
        }
      }
    ];

    if (projectId) {
      filters.push({
        term: {
          projectIds: projectId
        }
      });
    }

    filters.push(
      ...this.buildLocationFilters(suppliedFilters, filterField, filterValue)
    );

    return filters;
  }

  private buildBulkFilters(
    { personIds, projectId, suppliedFilters }: Readonly<BulkNetworkRequest>,
    filterField?: NetworkFilterAutocompleteField,
    filterValue?: string
  ) {
    const filters: QueryDslQueryContainer[] = [
      {
        terms: {
          collaboratorIds: personIds
        }
      },
      {
        bool: {
          must_not: {
            bool: {
              should: personIds.map((personId) => {
                // Exclude the current personId to get the list of other personIds
                const otherPersonIds = personIds.filter(
                  (id) => id !== personId
                );
                return {
                  bool: {
                    filter: [
                      { term: { id: personId } },
                      {
                        bool: {
                          must_not: {
                            terms: { collaboratorIds: otherPersonIds }
                          }
                        }
                      }
                    ]
                  }
                };
              }),
              minimum_should_match: 1
            }
          }
        }
      }
    ];

    if (projectId) {
      filters.push({
        term: {
          projectIds: projectId
        }
      });
    }

    filters.push(
      ...this.buildLocationFilters(suppliedFilters, filterField, filterValue)
    );

    return filters;
  }

  private buildLocationFilters(
    suppliedFilters: NetworkFilters,
    filterField?: NetworkFilterAutocompleteField,
    filterValue?: string
  ): Array<QueryDslQueryContainer> {
    const filters: Array<QueryDslQueryContainer> = [];

    if (filterField === NetworkFilterAutocompleteField.COUNTRY) {
      if (filterValue) {
        filters.push(
          buildMatchPhrasePrefixQuery(
            `${AFFILIATIONS_INSTITUTION}.address.country`,
            filterValue
          )
        );
      }
    } else if (suppliedFilters.collaboratorLocation?.country?.length) {
      const countryFilterValues = this.collectCountryFilterValues(
        suppliedFilters.collaboratorLocation.country
      );

      if (countryFilterValues.length) {
        filters.push(
          this.toTermsFilter(
            locationFilterFields[NetworkFilterAutocompleteField.COUNTRY],
            countryFilterValues
          )
        );
      }
    }

    if (filterField === NetworkFilterAutocompleteField.REGION) {
      if (filterValue) {
        filters.push(
          buildMatchPhrasePrefixQuery(
            `${AFFILIATIONS_INSTITUTION}.address.region`,
            filterValue
          )
        );
      }
    } else if (suppliedFilters.collaboratorLocation?.region?.length) {
      const regionFilterValues = this.collectRegionFilterValues(
        suppliedFilters.collaboratorLocation.region
      );

      if (regionFilterValues.length) {
        filters.push(
          this.toTermsFilter(
            locationFilterFields[NetworkFilterAutocompleteField.REGION],
            regionFilterValues
          )
        );
      }
    }

    if (filterField === NetworkFilterAutocompleteField.CITY) {
      if (filterValue) {
        filters.push(
          buildMatchPhrasePrefixQuery(
            `${AFFILIATIONS_INSTITUTION}.address.city`,
            filterValue
          )
        );
      }
    } else if (suppliedFilters.collaboratorLocation?.city?.length) {
      const cityFilterValues = this.collectCityFilterValues(
        suppliedFilters.collaboratorLocation.city
      );

      if (cityFilterValues.length) {
        filters.push(
          this.toTermsFilter(
            locationFilterFields[NetworkFilterAutocompleteField.CITY],
            cityFilterValues
          )
        );
      }
    }

    if (filterField === NetworkFilterAutocompleteField.POSTAL_CODE) {
      if (filterValue) {
        filters.push({
          regexp: {
            [locationFilterFields[filterField]]: `.+\\|${filterValue}[^|]*`
          }
        });
      }
    } else if (suppliedFilters.collaboratorLocation?.postalCode?.length) {
      const postalCodeFilter = this.collectPostalCodeFilterValues(
        suppliedFilters.collaboratorLocation.postalCode
      );

      filters.push(postalCodeFilter);
    }

    if (filters.length > 0) {
      filters.push(WORK_AFFILIATION);
      filters.push(IS_CURRENT_AFFILIATION);

      const nestedAffiliationsFilter = {
        nested: {
          path: AFFILIATIONS,
          query: {
            bool: {
              filter: filters
            }
          }
        }
      };

      return [nestedAffiliationsFilter];
    }

    return [];
  }

  private buildAffiliationsTermsQuery(institutionIds: Readonly<string[]>) {
    return {
      bool: {
        filter: this.toTermsFilter(
          `${AFFILIATIONS_INSTITUTION}.id`,
          institutionIds
        )
      }
    };
  }

  private buildFiltersForNestedDocument(
    path: NestedDocPath,
    { personId }: { personId: string }
  ) {
    const filters: Array<QueryDslQueryContainer> = [
      this.toPersonIdTermQuery(path, personId)
    ];

    return filters;
  }

  private buildFiltersForNestedDocumentForBulkRequest(
    path: NestedDocPath,
    { personIds }: { personIds: string[] }
  ) {
    const filters: Array<QueryDslQueryContainer> = [
      this.toPersonIdTermQueryForBulkRequest(path, personIds)
    ];

    return filters;
  }

  private buildFiltersForNestedDocumentWithKeyword(
    path: NestedDocPath,
    request: Readonly<NetworkCollaboratorRequest>,
    parsedQueryTree: Readonly<ParsedQueryTree>,
    languageDetector: LanguageDetector
  ) {
    const filters = this.buildFiltersForNestedDocument(path, request);

    if (parsedQueryTree) {
      const textQueryFilter =
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          pathQualifiedTextFields[path],
          languageDetector
        );

      filters.push(textQueryFilter);
    }

    return filters;
  }

  private toPersonIdTermQuery(
    path: NestedDocPath,
    personId: string
  ): QueryDslQueryContainer {
    const pathQualifiedPersonIdField = `${path}.persons.id`;

    return {
      term: {
        [pathQualifiedPersonIdField]: personId
      }
    };
  }

  private toPersonIdTermQueryForBulkRequest(
    path: NestedDocPath,
    personIds: string[]
  ): QueryDslQueryContainer {
    const pathQualifiedPersonIdField = `${path}.persons.id`;

    return {
      terms: {
        [pathQualifiedPersonIdField]: personIds
      }
    };
  }

  private toMinimumDateRangeQuery(
    path: NestedDocPath,
    dateField: string,
    minDate: number
  ) {
    const pathQualifiedDateField = `${path}.${dateField}`;

    return {
      bool: {
        should: [
          {
            range: {
              [pathQualifiedDateField]: {
                gte: minDate
              }
            }
          },
          {
            bool: {
              must_not: {
                exists: {
                  field: pathQualifiedDateField
                }
              }
            }
          }
        ],
        minimum_should_match: AT_LEAST_ONE_ASSET
      }
    };
  }

  private toTermsFilter(
    fieldName: string,
    values: Readonly<string[]>
  ): QueryDslQueryContainer {
    return {
      terms: {
        [fieldName]: [...values]
      }
    };
  }

  private collectCountryFilterValues(
    countryValues: Array<string> = []
  ): Array<string> {
    if (countryValues.every(is2CharacterISOCountryCode)) {
      return countryValues;
    }

    // REMOVE WHEN FIXED
    const countryCodes: Array<string> = [];
    for (const countryName of countryValues) {
      if (countryName in COUNTRY_NAME_TO_CODE) {
        countryCodes.push(COUNTRY_NAME_TO_CODE[countryName]);
      }
    }

    return countryCodes;
  }

  private collectRegionFilterValues(
    regionValues: Array<string> = []
  ): Array<string> {
    if (regionValues.every(isFilterValueAlreadyPipeDelimited)) {
      return regionValues;
    }

    // REMOVE WHEN FIXED
    const regionFilterValues: Array<string> = [];
    for (const regionName of regionValues) {
      if (regionName in REGIONS_KEYED_BY_NAME) {
        const region = REGIONS_KEYED_BY_NAME[regionName];
        regionFilterValues.push(`${region.country}|${region.code}`);
      }
    }

    return regionFilterValues;
  }

  private collectCityFilterValues(
    cityValues: Array<string> = []
  ): Array<string> {
    if (cityValues.every(isFilterValueAlreadyPipeDelimited)) {
      return cityValues;
    }

    // REMOVE WHEN FIXED
    const cityFilterValues: Array<string> = [];
    for (const city of cityValues) {
      const delimitedCityFilterValues = toDelimitedCityFilterValues(city);
      if (delimitedCityFilterValues) {
        cityFilterValues.push(...delimitedCityFilterValues);
      }
    }

    return cityFilterValues;
  }

  private collectPostalCodeFilterValues(
    postalCodeValues: Array<string> = []
  ): QueryDslQueryContainer {
    if (postalCodeValues.every(isFilterValueAlreadyPipeDelimited)) {
      return {
        terms: {
          [`${AFFILIATIONS_INSTITUTION}.filters.postal_code`]: postalCodeValues
        }
      };
    }

    // REMOVE WHEN FIXED
    const pipeDelimitedPostalCodes = postalCodeValues
      .filter((value) => !!value)
      .join("|");
    const wildcardPrefixMatchForAmbiguousPostalCodes = `.+\\|(${pipeDelimitedPostalCodes})`;
    return {
      regexp: {
        [`${AFFILIATIONS_INSTITUTION}.filters.postal_code`]: {
          value: wildcardPrefixMatchForAmbiguousPostalCodes
        }
      }
    };
  }
}

// The following functions can be removed once the location filters are converted
// to use the pipe-delimited format and no longer need to be converted.

// this is required to assist the translation of "Pennsylvania" => "us|PA"
const REGIONS_KEYED_BY_NAME = Object.entries(
  REGION_CODES_BY_COUNTRY_CODE_AND_REGION_NAME
).reduce((acc, [country, regions]) => {
  Object.entries(regions).forEach(([regionName, code]) => {
    acc[regionName] = { code, country };
  });
  return acc;
}, {} as Record<string, Region>);

const IS_CITY_COUNTY_STATE_COUNTRY_FORMAT = 4;
const IS_AMBIGUOUS_CITY_STATEORCOUNTY_COUNTRY_FORMAT = 3;
const IS_CITY_COUNTRY_FORMAT = 2;

interface Region {
  code: string;
  country: string;
}

const indicesFor4FieldCityFilterFormat = {
  city: 0,
  county: 1,
  region: 2,
  country: 3
};

const COUNTRY_CODES = Object.values(COUNTRY_NAME_TO_CODE);

function is2CharacterISOCountryCode(countryName: string) {
  return COUNTRY_CODES.includes(countryName);
}

function lookupCountryCode(countryName: string) {
  return COUNTRY_NAME_TO_CODE[countryName as keyof typeof COUNTRY_NAME_TO_CODE];
}

function isFilterValueAlreadyPipeDelimited(filterValue: string) {
  return filterValue.includes("|");
}

function getCountyValueFromCityFilter(locationFields: string[]) {
  if (
    locationFields[indicesFor4FieldCityFilterFormat.country] ||
    locationFields[indicesFor4FieldCityFilterFormat.region]
  ) {
    return locationFields[indicesFor4FieldCityFilterFormat.county];
  }
  return "";
}

/*
inputs can be "Lancaster, PA, United States" and "Lancaster, United States" where the region is missing
REMOVE WHEN FIXED
*/
function getRegionValueFromCityFilter(locationFields: string[]) {
  let region;
  if (locationFields[indicesFor4FieldCityFilterFormat.country]) {
    region = locationFields[indicesFor4FieldCityFilterFormat.region];
  } else if (locationFields[indicesFor4FieldCityFilterFormat.region]) {
    region = locationFields[indicesFor4FieldCityFilterFormat.county];
  }

  if (region) {
    return hasRegionCode(region) ? REGIONS_KEYED_BY_NAME[region].code : region;
  }

  return "";
}

function getCountryValueFromCityFilter(locationFields: string[]) {
  if (locationFields[indicesFor4FieldCityFilterFormat.country]) {
    return locationFields[indicesFor4FieldCityFilterFormat.country];
  } else if (locationFields[indicesFor4FieldCityFilterFormat.region]) {
    return locationFields[indicesFor4FieldCityFilterFormat.region];
  }

  return locationFields[indicesFor4FieldCityFilterFormat.county];
}

function hasRegionCode(region: string) {
  return region in REGIONS_KEYED_BY_NAME;
}

function toDelimitedCityFilterValues(userCityFilter: string) {
  if (isFilterValueAlreadyPipeDelimited(userCityFilter)) {
    return [userCityFilter];
  }

  const locationFields = userCityFilter.split(/,/).map(_.trim);

  if (
    locationFields.length !== IS_CITY_COUNTY_STATE_COUNTRY_FORMAT &&
    locationFields.length !== IS_AMBIGUOUS_CITY_STATEORCOUNTY_COUNTRY_FORMAT &&
    locationFields.length !== IS_CITY_COUNTRY_FORMAT
  ) {
    return null;
  }

  const city = locationFields[indicesFor4FieldCityFilterFormat.city];
  const county = getCountyValueFromCityFilter(locationFields);
  const region = getRegionValueFromCityFilter(locationFields);
  const countryName = getCountryValueFromCityFilter(locationFields);
  const countryCode = lookupCountryCode(countryName);

  if (!countryCode) {
    return null;
  }

  if (
    locationFields.length === IS_AMBIGUOUS_CITY_STATEORCOUNTY_COUNTRY_FORMAT
  ) {
    return [
      [countryCode, region, EMPTY_STRING, city].join("|"),
      [countryCode, EMPTY_STRING, county, city].join("|")
    ];
  }

  return [[countryCode, region, county, city].join("|")];
}
