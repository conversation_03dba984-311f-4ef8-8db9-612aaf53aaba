import NameSearchBuilderFactory from "./NameSearchBuilderFactory";
import { CHINESE, ENGLISH, JAPANESE } from "../LanguageDetectService";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import {
  featureFlagDefaults,
  NameSearchFeatureFlags
} from "../NameSearchResourceServiceRewrite";
import { QueryUnderstandingServiceResponse } from "../../proto/query_understanding_service_pb";
import { EMPTY_ARRAY } from "./DefaultNameSearchBuilder.test";

describe("NameSearchBuilder", () => {
  it("eng: should return a match all when no keyword is specified", async () => {
    const from = 0;
    const size = 10;
    const language = ENGLISH;
    const filter: QueryDslQueryContainer[] = [];
    const nameSearchBuilderFactory = new NameSearchBuilderFactory();
    const nameSearchBuilder =
      nameSearchBuilderFactory.getNameSearchBuilder(ENGLISH);

    const actual = nameSearchBuilder.createNameSearchBodyNoKeyWord({
      from,
      size,
      filter,
      language,
      featureFlags: getFeatureFlags({}),
      useCmnAndJpnFields: false
    });

    expect(JSON.stringify(actual)).toMatchSnapshot();
  });

  it("eng: should return a keyword match", async () => {
    const from = 0;
    const size = 10;
    const query = "john";
    const language = ENGLISH;
    const projectId = "1";
    const numberOfNameVariationsToMatch = 2;
    const filter: QueryDslQueryContainer[] = [];
    const nameSearchBuilderFactory = new NameSearchBuilderFactory();
    const nameSearchBuilder =
      nameSearchBuilderFactory.getNameSearchBuilder(ENGLISH);

    const actual = nameSearchBuilder.createNameSearchBody({
      from,
      size,
      filter,
      language,
      query,
      numberOfNameVariationsToMatch,
      featureFlags: getFeatureFlags({}),
      useCmnAndJpnFields: false,
      queryUnderstandingServiceResponse:
        new QueryUnderstandingServiceResponse(),
      onboardingData: {
        indications: EMPTY_ARRAY,
        countries: EMPTY_ARRAY,
        states: EMPTY_ARRAY
      },
      projectId
    });
    expect(JSON.stringify(actual)).toMatchSnapshot();
  });

  it("eng: should return a keyword match with high confidence person fields if high conf false is enabled", async () => {
    const from = 0;
    const size = 10;
    const query = "john";
    const language = ENGLISH;
    const projectId = "1";
    const numberOfNameVariationsToMatch = 2;
    const filter: QueryDslQueryContainer[] = [];
    const nameSearchBuilderFactory = new NameSearchBuilderFactory();
    const nameSearchBuilder =
      nameSearchBuilderFactory.getNameSearchBuilder(ENGLISH);

    const actual = nameSearchBuilder.createNameSearchBody({
      from,
      size,
      filter,
      language,
      query,
      numberOfNameVariationsToMatch,
      featureFlags: getFeatureFlags({ enableHighConfHCPFeature: true }),
      useCmnAndJpnFields: false,
      queryUnderstandingServiceResponse:
        new QueryUnderstandingServiceResponse(),
      onboardingData: {
        indications: EMPTY_ARRAY,
        countries: EMPTY_ARRAY,
        states: EMPTY_ARRAY
      },
      projectId
    });
    expect(JSON.stringify(actual)).toMatchSnapshot();
  });

  it("eng: should return a keyword match without high confidence person fields if high conf falg is enabled but page requested is not the first one", async () => {
    const from = 1;
    const size = 10;
    const query = "john";
    const language = ENGLISH;
    const projectId = "1";
    const numberOfNameVariationsToMatch = 2;
    const filter: QueryDslQueryContainer[] = [];
    const nameSearchBuilderFactory = new NameSearchBuilderFactory();
    const nameSearchBuilder =
      nameSearchBuilderFactory.getNameSearchBuilder(ENGLISH);

    const actual = nameSearchBuilder.createNameSearchBody({
      from,
      size,
      filter,
      language,
      query,
      numberOfNameVariationsToMatch,
      featureFlags: getFeatureFlags({ enableHighConfHCPFeature: true }),
      useCmnAndJpnFields: false,
      queryUnderstandingServiceResponse:
        new QueryUnderstandingServiceResponse(),
      onboardingData: {
        indications: EMPTY_ARRAY,
        countries: EMPTY_ARRAY,
        states: EMPTY_ARRAY
      },
      projectId
    });
    expect(JSON.stringify(actual)).toMatchSnapshot();
  });

  it("cmn: should return a match all when no keyword is specified", async () => {
    const from = 0;
    const size = 10;

    const language = CHINESE;
    const filter: QueryDslQueryContainer[] = [];
    const nameSearchBuilderFactory = new NameSearchBuilderFactory();
    const nameSearchBuilder =
      nameSearchBuilderFactory.getNameSearchBuilder(CHINESE);

    const actual = nameSearchBuilder.createNameSearchBodyNoKeyWord({
      from,
      size,
      filter,
      language,
      featureFlags: getFeatureFlags({}),
      useCmnAndJpnFields: false
    });
    expect(JSON.stringify(actual)).toMatchSnapshot();
  });

  it("cmn: should return a keyword match", async () => {
    const from = 0;
    const size = 10;
    const query = "吴";
    const language = CHINESE;
    const projectId = "1";
    const numberOfNameVariationsToMatch = 2;
    const filter: QueryDslQueryContainer[] = [];
    const nameSearchBuilderFactory = new NameSearchBuilderFactory();
    const nameSearchBuilder =
      nameSearchBuilderFactory.getNameSearchBuilder(CHINESE);

    const actual = nameSearchBuilder.createNameSearchBody({
      from,
      size,
      filter,
      language,
      query,
      numberOfNameVariationsToMatch,
      featureFlags: getFeatureFlags({}),
      useCmnAndJpnFields: false,
      queryUnderstandingServiceResponse:
        new QueryUnderstandingServiceResponse(),
      onboardingData: {
        indications: EMPTY_ARRAY,
        countries: EMPTY_ARRAY,
        states: EMPTY_ARRAY
      },
      projectId
    });
    expect(JSON.stringify(actual)).toMatchSnapshot();
  });

  it("jpn: should return a match all when no keyword is specified", async () => {
    const from = 0;
    const size = 10;

    const language = JAPANESE;
    const filter: QueryDslQueryContainer[] = [];
    const nameSearchBuilderFactory = new NameSearchBuilderFactory();
    const nameSearchBuilder =
      nameSearchBuilderFactory.getNameSearchBuilder(JAPANESE);

    const actual = nameSearchBuilder.createNameSearchBodyNoKeyWord({
      from,
      size,
      filter,
      language,
      featureFlags: getFeatureFlags({}),
      useCmnAndJpnFields: false
    });
    expect(JSON.stringify(actual)).toMatchSnapshot();
  });

  it("jpn: should return a keyword match", async () => {
    const from = 0;
    const size = 10;
    const query = "鈴木";
    const language = JAPANESE;
    const projectId = "1";
    const numberOfNameVariationsToMatch = 2;
    const filter: QueryDslQueryContainer[] = [];
    const nameSearchBuilderFactory = new NameSearchBuilderFactory();
    const nameSearchBuilder =
      nameSearchBuilderFactory.getNameSearchBuilder(JAPANESE);

    const actual = nameSearchBuilder.createNameSearchBody({
      from,
      size,
      filter,
      language,
      query,
      numberOfNameVariationsToMatch,
      featureFlags: getFeatureFlags({}),
      useCmnAndJpnFields: false,
      queryUnderstandingServiceResponse:
        new QueryUnderstandingServiceResponse(),
      onboardingData: {
        indications: EMPTY_ARRAY,
        countries: EMPTY_ARRAY,
        states: EMPTY_ARRAY
      },
      projectId
    });
    expect(JSON.stringify(actual)).toMatchSnapshot();
  });
});

function getFeatureFlags(
  overrides: Partial<NameSearchFeatureFlags> = {}
): NameSearchFeatureFlags {
  const values = Object.entries(featureFlagDefaults).reduce(
    (acc, [key, value]) => {
      if (overrides[key as keyof NameSearchFeatureFlags]) {
        acc[value.key] = overrides[key as keyof NameSearchFeatureFlags]!;
      } else {
        acc[value.key] = value.default;
      }
      return acc;
    },
    {} as Record<string, boolean>
  );
  return values as NameSearchFeatureFlags;
}
