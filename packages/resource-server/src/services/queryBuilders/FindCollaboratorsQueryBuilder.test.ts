import { faker } from "@faker-js/faker";
import { FindCollaboratorsRequest } from "../CollaboratorsResourceService";
import { FindCollaboratorsQueryBuilder } from "./FindCollaboratorsQueryBuilder";
import { ParsedQueryTreeToElasticsearchQueriesService } from "../ParsedQueryTreeToElasticsearchQueries";
import { createMockInstance } from "../../util/TestUtils";
import { NestedPath } from "../../util/QueryParsingUtils";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import {
  generateMockElasticsearchTermQuery,
  mockParseTreeToElasticsearchQueries
} from "../HCPDocumentTestingUtils";
import { ConfigService } from "../ConfigService";

describe("FindCollaboratorsQueryBuilder", () => {
  it("input w/o minDate or parsedQueryTree should only filter on personId", () => {
    const request: FindCollaboratorsRequest = {
      personId: faker.datatype.string(),
      projectId: faker.datatype.string(),
      size: faker.datatype.number(),
      minDate: null
    };
    const parsedQueryTree = undefined;

    const configService = {
      elasticPeopleIndex: faker.random.word()
    } as ConfigService;

    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryBuilder = new FindCollaboratorsQueryBuilder(
      configService,
      parsedQueryTreeToElasticsearchQueriesService
    );

    const query = queryBuilder.build(request, parsedQueryTree);

    expect(query).toEqual({
      index: configService.elasticPeopleIndex,
      size: request.size,
      _source_includes: [
        "id",
        "publicationCount",
        "trialCount",
        "congressCount"
      ],
      query: {
        bool: {
          should: [
            {
              nested: {
                path: "publications",
                score_mode: "sum",
                query: {
                  bool: {
                    filter: [
                      expect.termQuery(
                        "publications.persons.id",
                        request.personId
                      )
                    ]
                  }
                }
              }
            },
            {
              nested: {
                path: "trials",
                score_mode: "sum",
                query: {
                  bool: {
                    filter: [
                      expect.termQuery("trials.persons.id", request.personId)
                    ]
                  }
                }
              }
            },
            {
              nested: {
                path: "congress",
                score_mode: "sum",
                query: {
                  bool: {
                    filter: [
                      expect.termQuery("congress.persons.id", request.personId)
                    ]
                  }
                }
              }
            }
          ],
          minimum_should_match: 1,
          filter: [
            expect.termQuery("collaboratorIds", request.personId),
            expect.termQuery("projectIds", request.projectId)
          ]
        }
      }
    });

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).not.toHaveBeenCalled();
  });

  it("input w/minDate should filter on nested object dates", () => {
    const request: FindCollaboratorsRequest = {
      personId: faker.datatype.string(),
      projectId: faker.datatype.string(),
      size: faker.datatype.number(),
      minDate: faker.datatype.number()
    };
    const parsedQueryTree = undefined;

    const configService = {
      elasticPeopleIndex: faker.random.word()
    } as ConfigService;

    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const queryBuilder = new FindCollaboratorsQueryBuilder(
      configService,
      parsedQueryTreeToElasticsearchQueriesService
    );

    const query = queryBuilder.build(request, parsedQueryTree);

    expect(query).toEqual({
      index: configService.elasticPeopleIndex,
      size: request.size,
      _source_includes: [
        "id",
        "publicationCount",
        "trialCount",
        "congressCount"
      ],
      query: {
        bool: {
          should: [
            {
              nested: {
                path: "publications",
                score_mode: "sum",
                query: {
                  bool: {
                    filter: [
                      expect.termQuery(
                        "publications.persons.id",
                        request.personId
                      ),
                      {
                        bool: {
                          should: [
                            {
                              range: {
                                "publications.datePublished": {
                                  gte: request.minDate
                                }
                              }
                            },
                            {
                              bool: {
                                must_not: expect.fieldExists(
                                  "publications.datePublished"
                                )
                              }
                            }
                          ],
                          minimum_should_match: 1
                        }
                      }
                    ]
                  }
                }
              }
            },
            {
              nested: {
                path: "trials",
                score_mode: "sum",
                query: {
                  bool: {
                    filter: [
                      expect.termQuery("trials.persons.id", request.personId),
                      {
                        bool: {
                          should: [
                            {
                              range: {
                                "trials.primaryCompletionDate": {
                                  gte: request.minDate
                                }
                              }
                            },
                            {
                              bool: {
                                must_not: expect.fieldExists(
                                  "trials.primaryCompletionDate"
                                )
                              }
                            }
                          ],
                          minimum_should_match: 1
                        }
                      }
                    ]
                  }
                }
              }
            },
            {
              nested: {
                path: "congress",
                score_mode: "sum",
                query: {
                  bool: {
                    filter: [
                      expect.termQuery("congress.persons.id", request.personId),
                      {
                        bool: {
                          should: [
                            {
                              range: {
                                "congress.startDate": {
                                  gte: request.minDate
                                }
                              }
                            },
                            {
                              bool: {
                                must_not:
                                  expect.fieldExists("congress.startDate")
                              }
                            }
                          ],
                          minimum_should_match: 1
                        }
                      }
                    ]
                  }
                }
              }
            }
          ],
          minimum_should_match: 1,
          filter: [
            expect.termQuery("collaboratorIds", request.personId),
            expect.termQuery("projectIds", request.projectId)
          ]
        }
      }
    });

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).not.toHaveBeenCalled();
  });

  it("input w/parsedQueryTree should filter on nested multi_match fields", () => {
    const request: FindCollaboratorsRequest = {
      personId: faker.datatype.string(),
      projectId: faker.datatype.string(),
      size: faker.datatype.number()
    };
    const parsedQueryTree = faker.random.word();

    const configService = {
      elasticPeopleIndex: faker.random.word()
    } as ConfigService;

    const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
      ParsedQueryTreeToElasticsearchQueriesService
    );

    const pathSpecificQueries: Partial<
      Record<NestedPath, QueryDslQueryContainer>
    > = {
      trials: generateMockElasticsearchTermQuery(),
      publications: generateMockElasticsearchTermQuery(),
      congress: generateMockElasticsearchTermQuery()
    };

    const mockParseTreeToQueries =
      mockParseTreeToElasticsearchQueries(pathSpecificQueries);

    parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
      mockParseTreeToQueries
    );

    const queryBuilder = new FindCollaboratorsQueryBuilder(
      configService,
      parsedQueryTreeToElasticsearchQueriesService
    );

    const query = queryBuilder.build(request, parsedQueryTree);

    expect(query).toEqual({
      index: configService.elasticPeopleIndex,
      size: request.size,
      _source_includes: [
        "id",
        "publicationCount",
        "trialCount",
        "congressCount"
      ],
      query: {
        bool: {
          should: [
            {
              nested: {
                path: "publications",
                score_mode: "sum",
                query: {
                  bool: {
                    filter: [
                      expect.termQuery(
                        "publications.persons.id",
                        request.personId
                      ),
                      pathSpecificQueries.publications
                    ]
                  }
                }
              }
            },
            {
              nested: {
                path: "trials",
                score_mode: "sum",
                query: {
                  bool: {
                    filter: [
                      expect.termQuery("trials.persons.id", request.personId),
                      pathSpecificQueries.trials
                    ]
                  }
                }
              }
            },
            {
              nested: {
                path: "congress",
                score_mode: "sum",
                query: {
                  bool: {
                    filter: [
                      expect.termQuery("congress.persons.id", request.personId),
                      pathSpecificQueries.congress
                    ]
                  }
                }
              }
            }
          ],
          minimum_should_match: 1,
          filter: [
            expect.termQuery("collaboratorIds", request.personId),
            expect.termQuery("projectIds", request.projectId)
          ]
        }
      }
    });

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalledWith(
      parsedQueryTree,
      [
        "publications.publicationAbstract",
        "publications.keywords",
        "publications.title"
      ],
      expect.any(Function)
    );

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalledWith(
      parsedQueryTree,
      [
        "trials.briefTitle",
        "trials.conditions",
        "trials.interventions",
        "trials.keywords",
        "trials.officialTitle",
        "trials.summary"
      ],
      expect.any(Function)
    );

    expect(
      parsedQueryTreeToElasticsearchQueriesService.parse
    ).toHaveBeenCalledWith(
      parsedQueryTree,
      ["congress.keywords", "congress.title"],
      expect.any(Function)
    );
  });
});
