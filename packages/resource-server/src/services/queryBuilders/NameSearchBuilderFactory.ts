import DefaultNameSearchBuilder from "./DefaultNameSearchBuilder";
import { NameSearchBuilder } from "./NameSearchBuilder";
import { Service } from "typedi";
import ChineseJapaneseNameSearchBuilder from "./ChineseJapaneseNameSearchBuilder";
import { CHINESE, JAPANESE, Language } from "../LanguageDetectService";
import FONameSearchQueryBuilder from "./FONameSearchQueryBuilder";

@Service()
export default class NameSearchBuilderFactory {
  getNameSearchBuilder(language: Language): NameSearchBuilder {
    if (language === CHINESE || language === JAPANESE) {
      return ChineseJapaneseNameSearchBuilder.getInstance();
    } else {
      return DefaultNameSearchBuilder.getInstance();
    }
  }

  getFoNameSearchBuilder(): FONameSearchQueryBuilder {
    return FONameSearchQueryBuilder.getInstance();
  }
}
