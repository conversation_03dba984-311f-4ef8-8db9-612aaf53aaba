import {
  QueryDslFunctionScoreContainer,
  QueryDslMatchQuery,
  QueryDslQueryContainer
} from "@elastic/elasticsearch/lib/api/types";
import { Service } from "typedi";
import { buildFunctionScoreQuery } from "../../util/QueryBuildingUtils";
import _ from "lodash";
import { expandFilterValueToRegex } from "../AggregationContainerBuilderService";

const LOG1P_FIELD_VALUE_FACTOR_MODIFIER = "log1p";
const BOOST_MODE_SUM = "sum";
const SCORE_MODE_SUM = "sum";
const FUZZINESS = "AUTO:4,7";
export const PUNCTUATION_REMOVER_ANALYZER = "institute_name_analyzer";
export const PUNCTUATION_SPLITTER_ANALYZER = "name_analyzer";
const SECONDARY_WEIGHT = 0.8;
const TERTIARY_WEIGHT = 0.6;
const QUARTERNARY_WEIGHT = 0.4;
const QUINARY_WEIGHT = 0.2;
const INSTITUTION_NAME_FIELD_HIT_BOOST = 100.0;
const INSTITUTION_NAME_PREFIX_HIT_BOOST = 5.0;
const MATCH_QUERY_PARAMETERS_PUNCTUATION_REMOVER_SCORING_FUNCTION: Partial<QueryDslMatchQuery> =
  {
    operator: "AND",
    analyzer: PUNCTUATION_REMOVER_ANALYZER
  };
const MATCH_QUERY_PARAMETERS_PUNCTUATION_SPLITTER_SCORING_FUNCTION: Partial<QueryDslMatchQuery> =
  {
    operator: "AND",
    analyzer: PUNCTUATION_SPLITTER_ANALYZER
  };
const MATCH_QUERY_PARAMETERS_PUNCTUATION_REMOVER: Partial<QueryDslMatchQuery> =
  {
    operator: "AND",
    prefix_length: 1,
    fuzziness: FUZZINESS,
    ...MATCH_QUERY_PARAMETERS_PUNCTUATION_REMOVER_SCORING_FUNCTION
  };
const MATCH_QUERY_PARAMETERS_PUNCTUATION_SPLITTER: Partial<QueryDslMatchQuery> =
  {
    operator: "AND",
    prefix_length: 1,
    fuzziness: FUZZINESS,
    ...MATCH_QUERY_PARAMETERS_PUNCTUATION_SPLITTER_SCORING_FUNCTION
  };

@Service()
export class InstitutionNameSearchQueryBuilder {
  public buildNameSearchQuery(query: string): QueryDslQueryContainer | null {
    if (_.isEmpty(query)) {
      return null;
    }

    const scoringFunctions: QueryDslFunctionScoreContainer[] = [
      {
        filter: {
          dis_max: {
            queries: [
              {
                match: {
                  name: {
                    query,
                    ...MATCH_QUERY_PARAMETERS_PUNCTUATION_SPLITTER_SCORING_FUNCTION
                  }
                }
              },
              {
                match: {
                  name: {
                    query,
                    ...MATCH_QUERY_PARAMETERS_PUNCTUATION_REMOVER_SCORING_FUNCTION
                  }
                }
              },
              {
                match: {
                  "name.phonetic": {
                    query,
                    ...MATCH_QUERY_PARAMETERS_PUNCTUATION_SPLITTER_SCORING_FUNCTION
                  }
                }
              },
              {
                match: {
                  "name.phonetic": {
                    query,
                    ...MATCH_QUERY_PARAMETERS_PUNCTUATION_REMOVER_SCORING_FUNCTION
                  }
                }
              },
              {
                match: {
                  "name.autocomplete_search": {
                    query,
                    operator: "AND"
                  }
                }
              }
            ]
          }
        },
        weight: INSTITUTION_NAME_FIELD_HIT_BOOST
      },
      {
        filter: {
          regexp: {
            "name.keyword": {
              value: `${expandFilterValueToRegex(query)}.*`
            }
          }
        },
        weight: INSTITUTION_NAME_PREFIX_HIT_BOOST
      },
      {
        field_value_factor: {
          field: "trials_count",
          factor: 1,
          modifier: LOG1P_FIELD_VALUE_FACTOR_MODIFIER,
          missing: 0
        }
      },
      {
        field_value_factor: {
          field: "research_institution_payment_count_3_year",
          factor: 1,
          modifier: LOG1P_FIELD_VALUE_FACTOR_MODIFIER,
          missing: 0
        }
      },
      {
        field_value_factor: {
          field: "trialLandscapeTagCount",
          factor: 1,
          modifier: LOG1P_FIELD_VALUE_FACTOR_MODIFIER,
          missing: 0
        }
      },
      {
        field_value_factor: {
          field: "trialInvestigatorCount",
          factor: 1,
          modifier: LOG1P_FIELD_VALUE_FACTOR_MODIFIER,
          missing: 0
        }
      },
      {
        field_value_factor: {
          field: "research_institution_payment_total_3_year",
          factor: 0.01,
          modifier: LOG1P_FIELD_VALUE_FACTOR_MODIFIER,
          missing: 0
        },
        weight: SECONDARY_WEIGHT
      },
      {
        field_value_factor: {
          field: "ctmsOrganizationStats.trialCount",
          factor: 1,
          modifier: LOG1P_FIELD_VALUE_FACTOR_MODIFIER,
          missing: 0
        },
        weight: SECONDARY_WEIGHT
      },
      {
        script_score: {
          script: {
            source: `if (doc['ultimateParentOrgType'].size() > 0 && doc['ultimateParentOrgType'].value == 'Health System') {
                return 1;
              } else {
                return 0;
              }`
          }
        },
        weight: TERTIARY_WEIGHT
      },
      {
        field_value_factor: {
          field: "totalPatientDocs",
          factor: 1,
          modifier: LOG1P_FIELD_VALUE_FACTOR_MODIFIER,
          missing: 0
        },
        weight: QUARTERNARY_WEIGHT
      },
      {
        script_score: {
          script: {
            source: `if (doc['orgTypes.keyword'].size() > 0 && ((doc['orgTypes.keyword'].value == 'Hospital') || (doc['orgTypes.keyword'].value == 'Research Center'))) {
                return 1;
              } else {
                return 0;
              }`
          }
        },
        weight: QUINARY_WEIGHT
      }
    ];

    return buildFunctionScoreQuery(
      {
        dis_max: {
          queries: [
            {
              match: {
                name: {
                  query,
                  ...MATCH_QUERY_PARAMETERS_PUNCTUATION_SPLITTER
                }
              }
            },
            {
              match: {
                name: {
                  query,
                  ...MATCH_QUERY_PARAMETERS_PUNCTUATION_REMOVER
                }
              }
            },
            {
              match: {
                "name.phonetic": {
                  query,
                  ...MATCH_QUERY_PARAMETERS_PUNCTUATION_SPLITTER
                }
              }
            },
            {
              match: {
                "name.phonetic": {
                  query,
                  ...MATCH_QUERY_PARAMETERS_PUNCTUATION_REMOVER
                }
              }
            },
            {
              match: {
                "name.autocomplete_search": {
                  query,
                  operator: "AND"
                }
              }
            }
          ]
        }
      },
      scoringFunctions,
      BOOST_MODE_SUM,
      SCORE_MODE_SUM
    );
  }
}
