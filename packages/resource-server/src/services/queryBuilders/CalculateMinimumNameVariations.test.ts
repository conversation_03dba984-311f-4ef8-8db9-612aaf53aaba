import { faker } from "@faker-js/faker";
import _ from "lodash";
import { ElasticSearchService } from "../ElasticSearchService";
import CalculateMinimumNameVariations from "./CalculateMinimumNameVariations";

const peopleIndex = faker.datatype.string();
describe("CalculateMinimumNameVariations", () => {
  it("should return 0 if input query is empty", async () => {
    const calculateMinimumNameVariations = new CalculateMinimumNameVariations();

    const elasticService: unknown = {
      analyzeIndices: jest.fn().mockResolvedValue({
        tokens: [
          {
            token: "mike",
            start_offset: 0,
            end_offset: 4,
            type: "<ALPHANUM>",
            position: 0
          }
        ]
      })
    };

    const queryText = "";

    const response =
      await calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
        elasticService as ElasticSearchService,
        peopleIndex,
        queryText
      );
    expect(response).toEqual(0);
  });

  it("should return 1 for a query containing single token without any apostrophe", async () => {
    const calculateMinimumNameVariations = new CalculateMinimumNameVariations();
    const queryText = faker.name.firstName();
    const elasticService: unknown = {
      analyzeIndices: jest.fn().mockResolvedValue({
        tokens: [
          {
            token: queryText,
            start_offset: 0,
            end_offset: queryText.length,
            type: "<ALPHANUM>",
            position: 0
          }
        ]
      })
    };

    const response =
      await calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
        elasticService as ElasticSearchService,
        peopleIndex,
        queryText
      );
    expect(response).toEqual(1);
  });

  it("should return 2 for a query containing 2 tokens without any apostrophe", async () => {
    const calculateMinimumNameVariations = new CalculateMinimumNameVariations();
    const firstName = faker.name.firstName();
    const lastName = faker.name.lastName();
    const inputTokenArray = [firstName, lastName];
    const queryText = _.join(inputTokenArray, " ");
    const elasticService: unknown = {
      analyzeIndices: jest.fn().mockResolvedValue({
        tokens: [
          {
            token: firstName,
            start_offset: 0,
            end_offset: firstName.length,
            type: "<ALPHANUM>",
            position: 0
          },
          {
            token: lastName,
            start_offset: firstName.length,
            end_offset: queryText.length,
            type: "<ALPHANUM>",
            position: 1
          }
        ]
      })
    };

    const response =
      await calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
        elasticService as ElasticSearchService,
        peopleIndex,
        queryText
      );
    expect(response).toEqual(2);
  });

  it("should return 2 for a query containing 3 tokens without any apostrophe", async () => {
    const calculateMinimumNameVariations = new CalculateMinimumNameVariations();
    const firstName = faker.name.firstName();
    const middleName = faker.name.middleName();
    const lastName = faker.name.lastName();
    const inputTokenArray = [firstName, middleName, lastName];
    const queryText = _.join(inputTokenArray, " ");
    const elasticService: unknown = {
      analyzeIndices: jest.fn().mockResolvedValue({
        tokens: [
          {
            token: firstName,
            start_offset: 0,
            end_offset: firstName.length,
            type: "<ALPHANUM>",
            position: 0
          },
          {
            token: middleName,
            start_offset: 0,
            end_offset: firstName.length + middleName.length + 1,
            type: "<ALPHANUM>",
            position: 1
          },
          {
            token: lastName,
            start_offset: firstName.length + middleName.length + 1,
            end_offset: queryText.length,
            type: "<ALPHANUM>",
            position: 2
          }
        ]
      })
    };

    const response =
      await calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
        elasticService as ElasticSearchService,
        peopleIndex,
        queryText
      );
    expect(response).toEqual(2);
  });

  it("should return 3 for a query containing more than 3 tokens", async () => {
    const calculateMinimumNameVariations = new CalculateMinimumNameVariations();
    const firstName = faker.name.firstName();
    const middleName = faker.name.middleName();
    const lastName = faker.name.lastName();
    const affiliation = faker.company.name();
    const inputTokenArray = [firstName, middleName, lastName, affiliation];
    const queryText = _.join(inputTokenArray, " ");
    const elasticService: unknown = {
      analyzeIndices: jest.fn().mockResolvedValue({
        tokens: [
          {
            token: firstName,
            start_offset: 0,
            end_offset: firstName.length,
            type: "<ALPHANUM>",
            position: 0
          },
          {
            token: middleName,
            start_offset: 0,
            end_offset: firstName.length + middleName.length + 1,
            type: "<ALPHANUM>",
            position: 1
          },
          {
            token: lastName,
            start_offset: firstName.length + middleName.length + 1,
            end_offset:
              firstName.length + middleName.length + lastName.length + 1,
            type: "<ALPHANUM>",
            position: 2
          },
          {
            token: affiliation,
            start_offset:
              firstName.length + middleName.length + lastName.length + 1,
            end_offset: queryText.length,
            type: "<ALPHANUM>",
            position: 3
          }
        ]
      })
    };

    const response =
      await calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
        elasticService as ElasticSearchService,
        peopleIndex,
        queryText
      );
    expect(response).toEqual(3);
  });

  it("should return 2 for a query containing 2 tokens where one or more tokens contain name apostrophe variation starting with 'O'", async () => {
    const calculateMinimumNameVariations = new CalculateMinimumNameVariations();
    const firstName = faker.name.firstName();
    const lastName = "O'brian";
    const inputTokenArray = [firstName, lastName];
    const queryText = _.join(inputTokenArray, " ");
    const elasticService: unknown = {
      analyzeIndices: jest.fn().mockResolvedValue({
        tokens: [
          {
            token: firstName,
            start_offset: 0,
            end_offset: firstName.length,
            type: "<ALPHANUM>",
            position: 0
          },
          {
            token: "O",
            start_offset: firstName.length,
            end_offset: firstName.length + 1,
            type: "<ALPHANUM>",
            position: 1
          },
          {
            token: "brian",
            start_offset: firstName.length + 1,
            end_offset: queryText.length,
            type: "<ALPHANUM>",
            position: 2
          }
        ]
      })
    };

    const response =
      await calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
        elasticService as ElasticSearchService,
        peopleIndex,
        queryText
      );
    expect(response).toEqual(2);
  });

  it("should return 2 for a query containing 2 tokens where one or more tokens contain name apostrophe variation starting with 'O'", async () => {
    const calculateMinimumNameVariations = new CalculateMinimumNameVariations();
    const firstName = faker.name.firstName();
    const lastName = "D'souza";
    const inputTokenArray = [firstName, lastName];
    const queryText = _.join(inputTokenArray, " ");
    const elasticService: unknown = {
      analyzeIndices: jest.fn().mockResolvedValue({
        tokens: [
          {
            token: firstName,
            start_offset: 0,
            end_offset: firstName.length,
            type: "<ALPHANUM>",
            position: 0
          },
          {
            token: "D",
            start_offset: firstName.length,
            end_offset: firstName.length + 1,
            type: "<ALPHANUM>",
            position: 1
          },
          {
            token: "souza",
            start_offset: firstName.length + 1,
            end_offset: queryText.length,
            type: "<ALPHANUM>",
            position: 2
          }
        ]
      })
    };

    const response =
      await calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
        elasticService as ElasticSearchService,
        peopleIndex,
        queryText
      );
    expect(response).toEqual(2);
  });
});
