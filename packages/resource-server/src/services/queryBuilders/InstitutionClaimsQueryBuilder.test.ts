import { estypes } from "@elastic/elasticsearch";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { faker } from "@faker-js/faker";
import {
  EntitySortOptions,
  SortDirection
} from "@h1nyc/account-sdk/dist/interfaces/Sort";
import {
  ClaimsPage,
  IolClaimSortOptions,
  IolClaimType
} from "@h1nyc/search-sdk";
import { ITree } from "twitter-search-query-parser";
import { QueryUnderstandingServiceResponse } from "../../proto/query_understanding_service_pb";
import { createMockInstance } from "../../util/TestUtils";
import { ConfigService } from "../ConfigService";
import {
  generateMockElasticsearchTermQuery,
  mockParseTreeToElasticsearchQueries
} from "../HCPDocumentTestingUtils";
import { ParsedQueryTreeToElasticsearchQueriesService } from "../ParsedQueryTreeToElasticsearchQueries";
import { InstitutionClaimsQueryBuilder } from "./InstitutionClaimsQueryBuilder";
import { IolClaimsInput } from "@h1nyc/search-sdk";

function generateMockParsedTree(): [string, ITree] {
  const textValue1 = faker.datatype.string();
  const textValue2 = faker.datatype.string();
  const query = textValue1 + " AND " + textValue2;
  const parsedTree: ITree = [
    "And",
    [
      ["Including", ["Text", textValue1]],
      ["Including", ["Text", textValue2]]
    ]
  ];

  return [query, parsedTree];
}
const OR = " | ";
const MATCH_ALL = {
  match_all: {}
};

function expectClaimsInnerHitsObject(
  claimsType: "diagnoses" | "procedures" | "prescriptions",
  page: ClaimsPage,
  shouldUseUniquePatientCount?: boolean
): estypes.SearchInnerHits {
  if (claimsType === "prescriptions") {
    return {
      _source: {
        includes: [`${claimsType}.generic_name`]
      },
      from: page.offset,
      docvalue_fields: [`${claimsType}.num_prescriptions`],
      size: page.limit,
      sort: {
        [`${claimsType}.num_prescriptions`]: { order: "desc" }
      }
    };
  }
  const claimsCountField = shouldUseUniquePatientCount
    ? "internalUniqueCount"
    : "count";
  return {
    _source: {
      includes: [`${claimsType}.code_eng`, `${claimsType}.description_eng`]
    },
    from: page.offset,
    docvalue_fields: [
      `${claimsType}.${claimsCountField}`,
      `${claimsType}.countryRank`,
      `${claimsType}.totalInCountry`,
      `${claimsType}.stateRank`,
      `${claimsType}.totalInState`
    ],
    size: page.limit,
    sort: { [`${claimsType}.${claimsCountField}`]: { order: "desc" } }
  };
}

describe("InstitutionClaimsQueryBuilder", () => {
  describe("buildClaimsQueryWithoutFilters", () => {
    it("should include must clause for diagnoses to get inner hits of diagnoses without parsedQuery or queryUnderstandingService response ", async () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };

      const type = IolClaimType.Diagnosis;

      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {},
          type,
          rank: {},
          regionRank: {},
          keywords: []
        }
      };

      const query =
        institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
          iolClaimsInput,
          undefined,
          undefined
        );
      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          from: 0,
          size: 10000,
          track_total_hits: true,
          _source: false,
          query: {
            bool: {
              filter: [
                {
                  term: {
                    masterOrganizationId: {
                      value: iolId
                    }
                  }
                },
                {
                  nested: {
                    path: "diagnoses",
                    _name: "diagnoses",
                    query: MATCH_ALL,
                    inner_hits: expect.objectContaining(
                      expectClaimsInnerHitsObject("diagnoses", page)
                    )
                  }
                }
              ]
            }
          }
        })
      );
    });

    it("should use diagnoses codes list/ augmented query to get inner hits of claims when queryUnderstandingService response contains them", async () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );
      const quResponse: QueryUnderstandingServiceResponse =
        jest.createMockFromModule("../../proto/query_understanding_service_pb");
      const augmentedQuery = faker.datatype.string();
      quResponse.getAugmentedQuery = jest.fn(() => augmentedQuery);
      quResponse.getUnigramSynonymList = jest.fn(() => []);
      const diagnosisCodes = [faker.datatype.string(), faker.datatype.string()];
      quResponse.hasQueryIntent = jest.fn(() => false);
      // @ts-ignore
      const expectedDiagnosisQuery =
        "(" + diagnosisCodes[0] + "*) | (" + diagnosisCodes[1] + "*)";
      quResponse.getDiagnosisCodesList = jest.fn(() => diagnosisCodes);
      const parsedQuery = faker.datatype.string();

      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };
      const keywords = [faker.datatype.string()];
      const type = IolClaimType.Diagnosis;
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();

      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {},
          type,
          rank: {},
          regionRank: {},
          keywords
        }
      };

      const query =
        institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
          iolClaimsInput,
          parsedQuery,
          quResponse
        );
      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          from: 0,
          size: 10000,
          track_total_hits: true,
          _source: false,
          query: {
            bool: {
              filter: [
                {
                  term: {
                    masterOrganizationId: {
                      value: iolId
                    }
                  }
                },
                {
                  nested: {
                    path: "diagnoses",
                    query: {
                      bool: {
                        must: MATCH_ALL,
                        should: [
                          {
                            prefix: {
                              "diagnoses.code_eng": {
                                value: keywords[0]
                              }
                            }
                          },
                          {
                            prefix: {
                              "diagnoses.description_eng": {
                                value: keywords[0]
                              }
                            }
                          },
                          {
                            simple_query_string: {
                              _name: "diagnoses",
                              query: expectedDiagnosisQuery,
                              fields: [
                                "diagnoses.code_eng",
                                "diagnoses.description_eng"
                              ],
                              default_operator: "and",
                              analyzer: "main_analyzer"
                            }
                          }
                        ],
                        minimum_should_match: 1
                      }
                    },
                    inner_hits: expect.objectContaining(
                      expectClaimsInnerHitsObject("diagnoses", page)
                    )
                  }
                }
              ]
            }
          }
        })
      );
    });

    it("should use procedure augmented query to get inner hits of claims when queryUnderstandingService response contains them", async () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );
      const quResponse: QueryUnderstandingServiceResponse =
        jest.createMockFromModule("../../proto/query_understanding_service_pb");
      const augmentedQuery = faker.datatype.string();
      quResponse.getAugmentedQuery = jest.fn(() => augmentedQuery);
      quResponse.getUnigramSynonymList = jest.fn(() => []);
      const diagnosisCodes = [faker.datatype.string(), faker.datatype.string()];
      quResponse.hasQueryIntent = jest.fn(() => false);
      // @ts-ignore
      const expectedDiagnosisQuery =
        "(" + diagnosisCodes[0] + "*) | (" + diagnosisCodes[1] + "*)";
      quResponse.getDiagnosisCodesList = jest.fn(() => diagnosisCodes);
      const parsedQuery = faker.datatype.string();

      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };
      const keywords = [faker.datatype.string()];
      const type = IolClaimType.Procedure;
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {},
          type,
          rank: {},
          regionRank: {},
          keywords
        }
      };

      const query =
        institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
          iolClaimsInput,
          parsedQuery,
          quResponse
        );
      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          from: 0,
          size: 10000,
          track_total_hits: true,
          _source: false,
          query: {
            bool: {
              filter: [
                {
                  term: {
                    masterOrganizationId: {
                      value: iolId
                    }
                  }
                },
                {
                  nested: {
                    path: "procedures",
                    query: {
                      bool: {
                        must: MATCH_ALL,
                        should: [
                          {
                            prefix: {
                              "procedures.code_eng": {
                                value: keywords[0]
                              }
                            }
                          },
                          {
                            prefix: {
                              "procedures.description_eng": {
                                value: keywords[0]
                              }
                            }
                          },
                          {
                            simple_query_string: {
                              _name: "procedures",
                              query: augmentedQuery,
                              fields: [
                                "procedures.code_eng",
                                "procedures.description_eng"
                              ],
                              default_operator: "and",
                              analyzer: "main_analyzer"
                            }
                          }
                        ],
                        minimum_should_match: 1
                      }
                    },
                    inner_hits: expect.objectContaining(
                      expectClaimsInnerHitsObject("procedures", page)
                    )
                  }
                }
              ]
            }
          }
        })
      );
    });

    it("should use parsed diagnosesquery  to get inner hits of claims when parsedQueryTree is not string", async () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const pathSpecificQueries: Record<string, QueryDslQueryContainer> = {
        diagnoses: generateMockElasticsearchTermQuery(),
        procedures: generateMockElasticsearchTermQuery()
      };

      const mockParseTreeToQueries =
        mockParseTreeToElasticsearchQueries(pathSpecificQueries);

      parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
        mockParseTreeToQueries
      );

      const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const [, parsedQuery] = generateMockParsedTree();

      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),

        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };
      const keywords = [faker.datatype.string()];
      const type = IolClaimType.Diagnosis;
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {},
          type,
          rank: {},
          regionRank: {},
          keywords
        }
      };

      const query =
        institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
          iolClaimsInput,
          parsedQuery,
          undefined
        );
      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          from: 0,
          size: 10000,
          track_total_hits: true,
          _source: false,
          query: {
            bool: {
              filter: [
                {
                  term: {
                    masterOrganizationId: {
                      value: iolId
                    }
                  }
                },
                {
                  nested: {
                    path: "diagnoses",
                    _name: "diagnoses",
                    query: {
                      bool: {
                        must: MATCH_ALL,
                        should: expect.arrayContaining([
                          {
                            prefix: {
                              "diagnoses.code_eng": {
                                value: keywords[0]
                              }
                            }
                          },
                          {
                            prefix: {
                              "diagnoses.description_eng": {
                                value: keywords[0]
                              }
                            }
                          },
                          pathSpecificQueries.diagnoses
                        ]),
                        minimum_should_match: 1
                      }
                    },
                    inner_hits: expect.objectContaining(
                      expectClaimsInnerHitsObject("diagnoses", page)
                    )
                  }
                }
              ]
            }
          }
        })
      );
    });

    it("should use parsed procedures query  to get inner hits of claims when parsedQueryTree is not string", async () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const pathSpecificQueries: Record<string, QueryDslQueryContainer> = {
        diagnoses: generateMockElasticsearchTermQuery(),
        procedures: generateMockElasticsearchTermQuery()
      };

      const mockParseTreeToQueries =
        mockParseTreeToElasticsearchQueries(pathSpecificQueries);

      parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
        mockParseTreeToQueries
      );

      const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const [, parsedQuery] = generateMockParsedTree();

      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),

        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };
      const keywords = [faker.datatype.string()];
      const type = IolClaimType.Procedure;
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {},
          type,
          rank: {},
          regionRank: {},
          keywords
        }
      };

      const query =
        institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
          iolClaimsInput,
          parsedQuery,
          undefined
        );
      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          from: 0,
          size: 10000,
          track_total_hits: true,
          _source: false,
          query: {
            bool: {
              filter: [
                {
                  term: {
                    masterOrganizationId: {
                      value: iolId
                    }
                  }
                },
                {
                  nested: {
                    path: "procedures",
                    _name: "procedures",
                    query: {
                      bool: {
                        must: MATCH_ALL,
                        should: [
                          {
                            prefix: {
                              "procedures.code_eng": {
                                value: keywords[0]
                              }
                            }
                          },
                          {
                            prefix: {
                              "procedures.description_eng": {
                                value: keywords[0]
                              }
                            }
                          },
                          pathSpecificQueries["procedures"]
                        ],
                        minimum_should_match: 1
                      }
                    },
                    inner_hits: expect.objectContaining(
                      expectClaimsInnerHitsObject("procedures", page)
                    )
                  }
                }
              ]
            }
          }
        })
      );
    });

    describe("prescriptions", () => {
      it("should include must clause for prescriptions to get inner hits of prescriptions without parsedQuery or queryUnderstandingService response ", async () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };

        const type = IolClaimType.Prescriptions;

        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "prescriptions",
                      _name: "prescriptions",
                      query: MATCH_ALL,
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("prescriptions", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should use prescriptions generic name list/ augmented query to get inner hits of prescriptions when queryUnderstandingService response contains them", async () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );
        const quResponse: QueryUnderstandingServiceResponse =
          jest.createMockFromModule(
            "../../proto/query_understanding_service_pb"
          );
        const augmentedQuery = faker.datatype.string();
        quResponse.getAugmentedQuery = jest.fn(() => augmentedQuery);
        quResponse.getUnigramSynonymList = jest.fn(() => []);
        quResponse.hasQueryIntent = jest.fn(() => false);
        quResponse.getDiagnosisCodesList = jest.fn(() => []);
        const parsedQuery = faker.datatype.string();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Prescriptions;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();

        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            quResponse
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "prescriptions",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "prescriptions.generic_name.text": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              simple_query_string: {
                                _name: "prescriptions",
                                query: parsedQuery,
                                fields: ["prescriptions.generic_name.text"],
                                default_operator: "and",
                                analyzer: "main_analyzer"
                              }
                            }
                          ],
                          minimum_should_match: 1
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("prescriptions", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should not use prescriptions augmented query to get inner hits of prescriptions when queryUnderstandingService response contains them", async () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );
        const quResponse: QueryUnderstandingServiceResponse =
          jest.createMockFromModule(
            "../../proto/query_understanding_service_pb"
          );
        const augmentedQuery = faker.datatype.string();
        quResponse.getAugmentedQuery = jest.fn(() => augmentedQuery);
        quResponse.getUnigramSynonymList = jest.fn(() => []);
        quResponse.hasQueryIntent = jest.fn(() => false);
        quResponse.getDiagnosisCodesList = jest.fn(() => []);
        const parsedQuery = faker.datatype.string();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Prescriptions;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            quResponse
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "prescriptions",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "prescriptions.generic_name.text": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              simple_query_string: {
                                _name: "prescriptions",
                                query: parsedQuery,
                                fields: ["prescriptions.generic_name.text"],
                                default_operator: "and",
                                analyzer: "main_analyzer"
                              }
                            }
                          ],
                          minimum_should_match: 1
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("prescriptions", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should use parsed prescriptions query  to get inner hits of prescriptions when parsedQueryTree is not string", async () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Record<string, QueryDslQueryContainer> = {
          diagnoses: generateMockElasticsearchTermQuery(),
          procedures: generateMockElasticsearchTermQuery(),
          prescriptions: generateMockElasticsearchTermQuery()
        };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const [, parsedQuery] = generateMockParsedTree();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Prescriptions;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "prescriptions",
                      _name: "prescriptions",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: expect.arrayContaining([
                            {
                              prefix: {
                                "prescriptions.generic_name.text": {
                                  value: keywords[0]
                                }
                              }
                            },
                            pathSpecificQueries.prescriptions
                          ]),
                          minimum_should_match: 1
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("prescriptions", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });
    });
  });

  describe("buildClaimsQueryWithFilters", () => {
    describe("codes", () => {
      it("should apply code filter clause when supplied with no keyword", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };

        const codes = [faker.datatype.string(), faker.datatype.string()];
        const type = IolClaimType.Diagnosis;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes,
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              simple_query_string: {
                                _name: "diagnoses",
                                query: codes.map((code) => `${code}*`).join(OR),
                                fields: ["diagnoses.code_eng"],
                                default_operator: "and"
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply code filter clause when supplied with keyword and parsedQueryTree is string for diagnoses", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );
        const quResponse: QueryUnderstandingServiceResponse =
          jest.createMockFromModule(
            "../../proto/query_understanding_service_pb"
          );
        const augmentedQuery = faker.datatype.string();
        quResponse.getAugmentedQuery = jest.fn(() => augmentedQuery);
        quResponse.getUnigramSynonymList = jest.fn(() => []);
        const diagnosisCodes = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        quResponse.hasQueryIntent = jest.fn(() => false);
        // @ts-ignore
        const expectedDiagnosisQuery =
          "(" + diagnosisCodes[0] + "*) | (" + diagnosisCodes[1] + "*)";
        quResponse.getDiagnosisCodesList = jest.fn(() => diagnosisCodes);
        const parsedQuery = faker.datatype.string();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const codes = [faker.datatype.string(), faker.datatype.string()];
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Diagnosis;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes,
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            quResponse
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "diagnoses.code_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              prefix: {
                                "diagnoses.description_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              simple_query_string: {
                                _name: "diagnoses",
                                query: expectedDiagnosisQuery,
                                fields: [
                                  "diagnoses.code_eng",
                                  "diagnoses.description_eng"
                                ],
                                default_operator: "and",
                                analyzer: "main_analyzer"
                              }
                            }
                          ],
                          minimum_should_match: 1,
                          filter: [
                            {
                              simple_query_string: {
                                _name: "diagnoses",
                                query: codes.map((code) => `${code}*`).join(OR),
                                fields: ["diagnoses.code_eng"],
                                default_operator: "and"
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });
      it("should apply code filter clause when supplied with keyword and parsedQueryTree is string for procedures", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );
        const quResponse: QueryUnderstandingServiceResponse =
          jest.createMockFromModule(
            "../../proto/query_understanding_service_pb"
          );
        const augmentedQuery = faker.datatype.string();
        quResponse.getAugmentedQuery = jest.fn(() => augmentedQuery);
        quResponse.getUnigramSynonymList = jest.fn(() => []);
        const diagnosisCodes = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        quResponse.hasQueryIntent = jest.fn(() => false);
        // @ts-ignore
        const expectedDiagnosisQuery =
          "(" + diagnosisCodes[0] + "*) | (" + diagnosisCodes[1] + "*)";
        quResponse.getDiagnosisCodesList = jest.fn(() => diagnosisCodes);
        const parsedQuery = faker.datatype.string();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const codes = [faker.datatype.string(), faker.datatype.string()];
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Procedure;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes,
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            quResponse
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "procedures",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "procedures.code_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              prefix: {
                                "procedures.description_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              simple_query_string: {
                                _name: "procedures",
                                query: augmentedQuery,
                                fields: [
                                  "procedures.code_eng",
                                  "procedures.description_eng"
                                ],
                                default_operator: "and",
                                analyzer: "main_analyzer"
                              }
                            }
                          ],
                          minimum_should_match: 1,
                          filter: [
                            {
                              simple_query_string: {
                                _name: "procedures",
                                query: codes.map((code) => `${code}*`).join(OR),
                                fields: ["procedures.code_eng"],
                                default_operator: "and"
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("procedures", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply code filter clause when supplied with keyword and parsedQueryTree is not string", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Record<string, QueryDslQueryContainer> = {
          diagnoses: generateMockElasticsearchTermQuery(),
          procedures: generateMockElasticsearchTermQuery()
        };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const [, parsedQuery] = generateMockParsedTree();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const codes = [faker.datatype.string(), faker.datatype.string()];
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Diagnosis;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes,
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "diagnoses.code_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              prefix: {
                                "diagnoses.description_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            pathSpecificQueries["diagnoses"]
                          ],
                          minimum_should_match: 1,
                          filter: [
                            {
                              simple_query_string: {
                                _name: "diagnoses",
                                query: codes.map((code) => `${code}*`).join(OR),
                                fields: ["diagnoses.code_eng"],
                                default_operator: "and"
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });
    });
    describe("description", () => {
      it("should apply description filter clause when supplied with no keyword", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };

        const descriptions = [faker.datatype.string(), faker.datatype.string()];
        const type = IolClaimType.Diagnosis;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions,
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );

        const expectedDescriptionPrefixClause: Array<QueryDslQueryContainer> =
          descriptions.map((description) => {
            return {
              prefix: {
                ["diagnoses.description_eng"]: {
                  value: description
                }
              }
            };
          });

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              bool: {
                                should: [
                                  {
                                    simple_query_string: {
                                      _name: "diagnoses",
                                      query: descriptions.join(OR),
                                      fields: ["diagnoses.description_eng"],
                                      default_operator: "and"
                                    }
                                  },
                                  ...expectedDescriptionPrefixClause
                                ],
                                minimum_should_match: 1
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply description filter clause when supplied with keyword and parsedQueryTree is string for diagnoses", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );
        const quResponse: QueryUnderstandingServiceResponse =
          jest.createMockFromModule(
            "../../proto/query_understanding_service_pb"
          );
        const augmentedQuery = faker.datatype.string();
        quResponse.getAugmentedQuery = jest.fn(() => augmentedQuery);
        quResponse.getUnigramSynonymList = jest.fn(() => []);
        const diagnosisCodes = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        quResponse.hasQueryIntent = jest.fn(() => false);
        // @ts-ignore
        const expectedDiagnosisQuery =
          "(" + diagnosisCodes[0] + "*) | (" + diagnosisCodes[1] + "*)";
        quResponse.getDiagnosisCodesList = jest.fn(() => diagnosisCodes);
        const parsedQuery = faker.datatype.string();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const descriptions = [faker.datatype.string(), faker.datatype.string()];
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Diagnosis;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions,
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            quResponse
          );

        const expectedDescriptionPrefixClause = descriptions.map(
          (description) => {
            return {
              prefix: {
                ["diagnoses.description_eng"]: {
                  value: description
                }
              }
            };
          }
        );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "diagnoses.code_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              prefix: {
                                "diagnoses.description_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              simple_query_string: {
                                _name: "diagnoses",
                                query: expectedDiagnosisQuery,
                                fields: [
                                  "diagnoses.code_eng",
                                  "diagnoses.description_eng"
                                ],
                                default_operator: "and",
                                analyzer: "main_analyzer"
                              }
                            }
                          ],
                          minimum_should_match: 1,
                          filter: [
                            {
                              bool: {
                                should: [
                                  {
                                    simple_query_string: {
                                      _name: "diagnoses",
                                      query: descriptions.join(OR),
                                      fields: ["diagnoses.description_eng"],
                                      default_operator: "and"
                                    }
                                  },
                                  ...expectedDescriptionPrefixClause
                                ],
                                minimum_should_match: 1
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply description filter clause when supplied with keyword and parsedQueryTree is string for procedure", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );
        const quResponse: QueryUnderstandingServiceResponse =
          jest.createMockFromModule(
            "../../proto/query_understanding_service_pb"
          );
        const augmentedQuery = faker.datatype.string();
        quResponse.getAugmentedQuery = jest.fn(() => augmentedQuery);
        quResponse.getUnigramSynonymList = jest.fn(() => []);
        const diagnosisCodes = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        quResponse.hasQueryIntent = jest.fn(() => false);
        // @ts-ignore
        const expectedDiagnosisQuery =
          "(" + diagnosisCodes[0] + "*) | (" + diagnosisCodes[1] + "*)";
        quResponse.getDiagnosisCodesList = jest.fn(() => diagnosisCodes);
        const parsedQuery = faker.datatype.string();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const descriptions = [faker.datatype.string(), faker.datatype.string()];
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Procedure;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();

        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions,
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            quResponse
          );
        const expectedDescriptionPrefixClause = descriptions.map(
          (description) => {
            return {
              prefix: {
                ["procedures.description_eng"]: {
                  value: description
                }
              }
            };
          }
        );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "procedures",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "procedures.code_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              prefix: {
                                "procedures.description_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              simple_query_string: {
                                _name: "procedures",
                                query: augmentedQuery,
                                fields: [
                                  "procedures.code_eng",
                                  "procedures.description_eng"
                                ],
                                default_operator: "and",
                                analyzer: "main_analyzer"
                              }
                            }
                          ],
                          minimum_should_match: 1,
                          filter: [
                            {
                              bool: {
                                should: [
                                  {
                                    simple_query_string: {
                                      _name: "procedures",
                                      query: descriptions.join(OR),
                                      fields: ["procedures.description_eng"],
                                      default_operator: "and"
                                    }
                                  },
                                  ...expectedDescriptionPrefixClause
                                ],
                                minimum_should_match: 1
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("procedures", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply description filter clause when supplied with keyword and parsedQueryTree is not string", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Record<string, QueryDslQueryContainer> = {
          diagnoses: generateMockElasticsearchTermQuery(),
          procedures: generateMockElasticsearchTermQuery()
        };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const [, parsedQuery] = generateMockParsedTree();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const descriptions = [faker.datatype.string(), faker.datatype.string()];
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Diagnosis;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions,
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            undefined
          );

        const expectedDescriptionPrefixClause = descriptions.map(
          (description) => {
            return {
              prefix: {
                ["diagnoses.description_eng"]: {
                  value: description
                }
              }
            };
          }
        );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "diagnoses.code_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              prefix: {
                                "diagnoses.description_eng": {
                                  value: keywords[0]
                                }
                              }
                            },
                            pathSpecificQueries["diagnoses"]
                          ],
                          minimum_should_match: 1,
                          filter: [
                            {
                              bool: {
                                should: [
                                  {
                                    simple_query_string: {
                                      _name: "diagnoses",
                                      query: descriptions.join(OR),
                                      fields: ["diagnoses.description_eng"],
                                      default_operator: "and"
                                    }
                                  },
                                  ...expectedDescriptionPrefixClause
                                ],
                                minimum_should_match: 1
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });
    });

    describe("genericNames", () => {
      it("should apply prescriptions filter clause when supplied with no keyword", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };

        const genericNames = [faker.datatype.string(), faker.datatype.string()];
        const type = IolClaimType.Prescriptions;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            genericNames,
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );

        const expectedPrescriptionsPrefixClause: Array<QueryDslQueryContainer> =
          genericNames.map((genericName) => {
            return {
              prefix: {
                ["prescriptions.generic_name.text"]: {
                  value: genericName
                }
              }
            };
          });

        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "prescriptions",
                      _name: "prescriptions",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              bool: {
                                should: [
                                  {
                                    simple_query_string: {
                                      _name: "prescriptions",
                                      query: genericNames.join(OR),
                                      fields: [
                                        "prescriptions.generic_name.text"
                                      ],
                                      default_operator: "and"
                                    }
                                  },
                                  ...expectedPrescriptionsPrefixClause
                                ],
                                minimum_should_match: 1
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("prescriptions", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply prescriptions filter clause when supplied with keyword and parsedQueryTree is string for prescriptions", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );
        const quResponse: QueryUnderstandingServiceResponse =
          jest.createMockFromModule(
            "../../proto/query_understanding_service_pb"
          );
        const augmentedQuery = faker.datatype.string();
        quResponse.getAugmentedQuery = jest.fn(() => augmentedQuery);
        quResponse.getUnigramSynonymList = jest.fn(() => []);
        quResponse.hasQueryIntent = jest.fn(() => false);
        quResponse.getDiagnosisCodesList = jest.fn(() => []);
        const parsedQuery = faker.datatype.string();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const genericNames = [faker.datatype.string(), faker.datatype.string()];
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Prescriptions;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            genericNames,
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            quResponse
          );

        const expectedPrescriptionsPrefixClause = genericNames.map(
          (genericName) => {
            return {
              prefix: {
                ["prescriptions.generic_name.text"]: {
                  value: genericName
                }
              }
            };
          }
        );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "prescriptions",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "prescriptions.generic_name.text": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              simple_query_string: {
                                _name: "prescriptions",
                                query: parsedQuery,
                                fields: ["prescriptions.generic_name.text"],
                                default_operator: "and",
                                analyzer: "main_analyzer"
                              }
                            }
                          ],
                          minimum_should_match: 1,
                          filter: [
                            {
                              bool: {
                                should: [
                                  {
                                    simple_query_string: {
                                      _name: "prescriptions",
                                      query: genericNames.join(OR),
                                      fields: [
                                        "prescriptions.generic_name.text"
                                      ],
                                      default_operator: "and"
                                    }
                                  },
                                  ...expectedPrescriptionsPrefixClause
                                ],
                                minimum_should_match: 1
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("prescriptions", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply prescriptions filter clause when supplied with keyword and parsedQueryTree is string for prescriptions", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );
        const quResponse: QueryUnderstandingServiceResponse =
          jest.createMockFromModule(
            "../../proto/query_understanding_service_pb"
          );
        const augmentedQuery = faker.datatype.string();
        quResponse.getAugmentedQuery = jest.fn(() => augmentedQuery);
        quResponse.getUnigramSynonymList = jest.fn(() => []);
        const diagnosisCodes = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        quResponse.hasQueryIntent = jest.fn(() => false);
        quResponse.getDiagnosisCodesList = jest.fn(() => diagnosisCodes);
        const parsedQuery = faker.datatype.string();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const genericNames = [faker.datatype.string(), faker.datatype.string()];
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Prescriptions;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();

        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            genericNames,
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            quResponse
          );
        const expectedPrescriptionsPrefixClause = genericNames.map(
          (genericName) => {
            return {
              prefix: {
                ["prescriptions.generic_name.text"]: {
                  value: genericName
                }
              }
            };
          }
        );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "prescriptions",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "prescriptions.generic_name.text": {
                                  value: keywords[0]
                                }
                              }
                            },
                            {
                              simple_query_string: {
                                _name: "prescriptions",
                                query: parsedQuery,
                                fields: ["prescriptions.generic_name.text"],
                                default_operator: "and",
                                analyzer: "main_analyzer"
                              }
                            }
                          ],
                          minimum_should_match: 1,
                          filter: [
                            {
                              bool: {
                                should: [
                                  {
                                    simple_query_string: {
                                      _name: "prescriptions",
                                      query: genericNames.join(OR),
                                      fields: [
                                        "prescriptions.generic_name.text"
                                      ],
                                      default_operator: "and"
                                    }
                                  },
                                  ...expectedPrescriptionsPrefixClause
                                ],
                                minimum_should_match: 1
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("prescriptions", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply prescriptions filter clause when supplied with keyword and parsedQueryTree is not string", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const pathSpecificQueries: Record<string, QueryDslQueryContainer> = {
          diagnoses: generateMockElasticsearchTermQuery(),
          procedures: generateMockElasticsearchTermQuery(),
          prescriptions: generateMockElasticsearchTermQuery()
        };

        const mockParseTreeToQueries =
          mockParseTreeToElasticsearchQueries(pathSpecificQueries);

        parsedQueryTreeToElasticsearchQueriesService.parse.mockImplementation(
          mockParseTreeToQueries
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const [, parsedQuery] = generateMockParsedTree();

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const genericNames = [faker.datatype.string(), faker.datatype.string()];
        const keywords = [faker.datatype.string()];
        const type = IolClaimType.Prescriptions;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            genericNames,
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            parsedQuery,
            undefined
          );

        const expectedPrescriptionsPrefixClause = genericNames.map(
          (genericName) => {
            return {
              prefix: {
                ["prescriptions.generic_name.text"]: {
                  value: genericName
                }
              }
            };
          }
        );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "prescriptions",
                      _name: "prescriptions",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          should: [
                            {
                              prefix: {
                                "prescriptions.generic_name.text": {
                                  value: keywords[0]
                                }
                              }
                            },
                            pathSpecificQueries["prescriptions"]
                          ],
                          minimum_should_match: 1,
                          filter: [
                            {
                              bool: {
                                should: [
                                  {
                                    simple_query_string: {
                                      _name: "prescriptions",
                                      query: genericNames.join(OR),
                                      fields: [
                                        "prescriptions.generic_name.text"
                                      ],
                                      default_operator: "and"
                                    }
                                  },
                                  ...expectedPrescriptionsPrefixClause
                                ],
                                minimum_should_match: 1
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("prescriptions", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });
    });

    describe("types", () => {
      it("should show  diagnoses should clause when diagnoses type filter is applied", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };

        const type = IolClaimType.Diagnosis;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: expect.anything(),
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should show only procedures should clause when procedures type filter is applied", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };

        const type = IolClaimType.Procedure;
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "procedures",
                      _name: "procedures",
                      query: expect.anything(),
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("procedures", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });
    });

    describe("count", () => {
      it("should apply appropriate count min filter clause when supplied with only min", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const type = IolClaimType.Diagnosis;

        const minCount = faker.datatype.number();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {
              min: minCount
            },
            type,
            rank: {},
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              range: {
                                ["diagnoses.count"]: {
                                  gte: minCount
                                }
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply appropriate count max filter clause when supplied with only max", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const type = IolClaimType.Diagnosis;
        const maxCount = faker.datatype.number();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {
              max: maxCount
            },
            type,
            rank: {},
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              range: {
                                ["diagnoses.count"]: {
                                  lte: maxCount
                                }
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply appropriate count min-max filter clause when supplied with min and max", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const type = IolClaimType.Diagnosis;

        const minCount = faker.datatype.number();
        const maxCount = faker.datatype.number();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {
              min: minCount,
              max: maxCount
            },
            type,
            rank: {},
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              range: {
                                ["diagnoses.count"]: {
                                  gte: minCount,
                                  lte: maxCount
                                }
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });
    });

    describe("countryRank", () => {
      it("should apply appropriate countryRank min filter clause when supplied with only min", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const type = IolClaimType.Diagnosis;

        const minRank = faker.datatype.number();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            rank: {
              min: minRank
            },
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              range: {
                                ["diagnoses.countryRank"]: {
                                  gte: minRank
                                }
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply appropriate count max filter clause when supplied with only max", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const type = IolClaimType.Diagnosis;

        const maxRank = faker.datatype.number();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            rank: {
              max: maxRank
            },
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              range: {
                                ["diagnoses.countryRank"]: {
                                  lte: maxRank
                                }
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply appropriate count min-max filter clause when supplied with min and max", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const type = IolClaimType.Diagnosis;

        const minRank = faker.datatype.number();
        const maxRank = faker.datatype.number();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            rank: {
              min: minRank,
              max: maxRank
            },
            regionRank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              range: {
                                ["diagnoses.countryRank"]: {
                                  gte: minRank,
                                  lte: maxRank
                                }
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });
    });

    describe("stateRank", () => {
      it("should apply appropriate stateRank min filter clause when supplied with only min", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const type = IolClaimType.Diagnosis;

        const minRank = faker.datatype.number();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            regionRank: {
              min: minRank
            },
            rank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              range: {
                                ["diagnoses.stateRank"]: {
                                  gte: minRank
                                }
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply appropriate count max filter clause when supplied with only max", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const type = IolClaimType.Diagnosis;

        const maxRank = faker.datatype.number();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            regionRank: {
              max: maxRank
            },
            rank: {},
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              range: {
                                ["diagnoses.stateRank"]: {
                                  lte: maxRank
                                }
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });

      it("should apply appropriate count min-max filter clause when supplied with min and max", () => {
        const configService = createMockInstance(ConfigService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
          configService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const iolId = faker.datatype.string();
        const page = {
          offset: faker.datatype.number(),

          limit: faker.datatype.number()
        };
        const sort: EntitySortOptions<IolClaimSortOptions> = {
          field: "claimsCount",
          direction: SortDirection.Desc
        };
        const type = IolClaimType.Diagnosis;

        const minRank = faker.datatype.number();
        const maxRank = faker.datatype.number();
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const iolClaimsInput = {
          iolId,
          userId,
          projectId,
          page,
          sort,
          filters: {
            codes: [],
            descriptions: [],
            count: {},
            type,
            rank: {},
            regionRank: {
              min: minRank,
              max: maxRank
            },
            keywords: []
          }
        };

        const query =
          institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
            iolClaimsInput,
            undefined,
            undefined
          );
        expect(query).toEqual(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            from: 0,
            size: 10000,
            track_total_hits: true,
            _source: false,
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      masterOrganizationId: {
                        value: iolId
                      }
                    }
                  },
                  {
                    nested: {
                      path: "diagnoses",
                      _name: "diagnoses",
                      query: {
                        bool: {
                          must: MATCH_ALL,
                          filter: [
                            {
                              range: {
                                ["diagnoses.stateRank"]: {
                                  gte: minRank,
                                  lte: maxRank
                                }
                              }
                            }
                          ]
                        }
                      },
                      inner_hits: expect.objectContaining(
                        expectClaimsInnerHitsObject("diagnoses", page)
                      )
                    }
                  }
                ]
              }
            }
          })
        );
      });
    });
  });
  describe("unique patient count", () => {
    it("should fetch appropriate field in claims inner hits when unique patient count feature flag is true", async () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();

      const type = IolClaimType.Diagnosis;

      const iolClaimsInput: IolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {},
          type,
          rank: {},
          regionRank: {},
          keywords: []
        }
      };

      const query =
        institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
          iolClaimsInput,
          undefined,
          undefined,
          { shouldUseUniquePatientCount: true }
        );
      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          from: 0,
          size: 10000,
          track_total_hits: true,
          _source: false,
          query: {
            bool: {
              filter: [
                {
                  term: {
                    masterOrganizationId: {
                      value: iolId
                    }
                  }
                },
                {
                  nested: {
                    path: "diagnoses",
                    _name: "diagnoses",
                    query: MATCH_ALL,
                    inner_hits: expect.objectContaining(
                      expectClaimsInnerHitsObject("diagnoses", page, true)
                    )
                  }
                }
              ]
            }
          }
        })
      );
    });
    it("should use appropriate field in claims filter clause when unique patient count feature flag is true for diagnoses", async () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),

        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };
      const type = IolClaimType.Diagnosis;

      const minCount = faker.datatype.number();
      const maxCount = faker.datatype.number();
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {
            min: minCount,
            max: maxCount
          },
          type,
          rank: {},
          regionRank: {},
          keywords: []
        }
      };

      const query =
        institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
          iolClaimsInput,
          undefined,
          undefined,
          { shouldUseUniquePatientCount: true }
        );
      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          from: 0,
          size: 10000,
          track_total_hits: true,
          _source: false,
          query: {
            bool: {
              filter: [
                {
                  term: {
                    masterOrganizationId: {
                      value: iolId
                    }
                  }
                },
                {
                  nested: {
                    path: "diagnoses",
                    _name: "diagnoses",
                    query: {
                      bool: {
                        must: MATCH_ALL,
                        filter: [
                          {
                            range: {
                              ["diagnoses.internalUniqueCount"]: {
                                gte: minCount,
                                lte: maxCount
                              }
                            }
                          }
                        ]
                      }
                    },
                    inner_hits: expect.objectContaining(
                      expectClaimsInnerHitsObject("diagnoses", page, true)
                    )
                  }
                }
              ]
            }
          }
        })
      );
    });
    it("should use appropriate field in claims filter clause when unique patient count feature flag is true for procedures", async () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),

        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };
      const type = IolClaimType.Procedure;

      const minCount = faker.datatype.number();
      const maxCount = faker.datatype.number();
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {
            min: minCount,
            max: maxCount
          },
          type,
          rank: {},
          regionRank: {},
          keywords: []
        }
      };

      const query =
        institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
          iolClaimsInput,
          undefined,
          undefined,
          { shouldUseUniquePatientCount: true }
        );
      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          from: 0,
          size: 10000,
          track_total_hits: true,
          _source: false,
          query: {
            bool: {
              filter: [
                {
                  term: {
                    masterOrganizationId: {
                      value: iolId
                    }
                  }
                },
                {
                  nested: {
                    path: "procedures",
                    _name: "procedures",
                    query: {
                      bool: {
                        must: MATCH_ALL,
                        filter: [
                          {
                            range: {
                              ["procedures.internalUniqueCount"]: {
                                gte: minCount,
                                lte: maxCount
                              }
                            }
                          }
                        ]
                      }
                    },
                    inner_hits: expect.objectContaining(
                      expectClaimsInnerHitsObject("procedures", page, true)
                    )
                  }
                }
              ]
            }
          }
        })
      );
    });
    it("should use appropriate field in claims filter clause when disable unique patient count feature flag is true for procedures", async () => {
      const configService = createMockInstance(ConfigService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const institutionClaimsQueryBuilder = new InstitutionClaimsQueryBuilder(
        configService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const iolId = faker.datatype.string();
      const page = {
        offset: faker.datatype.number(),

        limit: faker.datatype.number()
      };
      const sort: EntitySortOptions<IolClaimSortOptions> = {
        field: "claimsCount",
        direction: SortDirection.Desc
      };
      const type = IolClaimType.Procedure;

      const minCount = faker.datatype.number();
      const maxCount = faker.datatype.number();
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const iolClaimsInput = {
        iolId,
        userId,
        projectId,
        page,
        sort,
        filters: {
          codes: [],
          descriptions: [],
          count: {
            min: minCount,
            max: maxCount
          },
          type,
          rank: {},
          regionRank: {},
          keywords: []
        }
      };

      const query =
        institutionClaimsQueryBuilder.buildClaimsSearchForInstitutionQuery(
          iolClaimsInput,
          undefined,
          undefined,
          {
            shouldUseUniquePatientCount: true,
            disableUniquePatientCountForOnlyProcedures: true
          }
        );
      expect(query).toEqual(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          from: 0,
          size: 10000,
          track_total_hits: true,
          _source: false,
          query: {
            bool: {
              filter: [
                {
                  term: {
                    masterOrganizationId: {
                      value: iolId
                    }
                  }
                },
                {
                  nested: {
                    path: "procedures",
                    _name: "procedures",
                    query: {
                      bool: {
                        must: MATCH_ALL,
                        filter: [
                          {
                            range: {
                              ["procedures.count"]: {
                                gte: minCount,
                                lte: maxCount
                              }
                            }
                          }
                        ]
                      }
                    },
                    inner_hits: expect.objectContaining(
                      expectClaimsInnerHitsObject("procedures", page, false)
                    )
                  }
                }
              ]
            }
          }
        })
      );
    });
  });
});
