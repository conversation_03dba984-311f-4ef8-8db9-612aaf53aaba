import { Service } from "typedi";
import { FindCollaboratorsRequest } from "../CollaboratorsResourceService";
import { ConfigService } from "../ConfigService";
import { ParsedQueryTree } from "../KeywordSearchResourceServiceRewrite";
import { ENGLISH } from "../LanguageDetectService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "../ParsedQueryTreeToElasticsearchQueries";
import {
  QueryDslQueryContainer,
  SearchRequest
} from "@elastic/elasticsearch/lib/api/types";

const SOURCE_FIELDS = ["id", "publicationCount", "trialCount", "congressCount"];

const nestedDocTypes = ["publications", "trials", "congress"] as const;
type NestedDocPath = (typeof nestedDocTypes)[number];

type NestedDocFields = {
  textFields: Readonly<Array<string>>;
  dateField: string;
};

const nestedDocs: Readonly<Record<NestedDocPath, Readonly<NestedDocFields>>> = {
  publications: {
    textFields: ["publicationAbstract", "keywords", "title"],
    dateField: "datePublished"
  },
  trials: {
    textFields: [
      "briefTitle",
      "conditions",
      "interventions",
      "keywords",
      "officialTitle",
      "summary"
    ],
    dateField: "primaryCompletionDate"
  },
  congress: {
    textFields: ["keywords", "title"],
    dateField: "startDate"
  }
};

const pathQualifiedTextFields: Readonly<Record<NestedDocPath, Array<string>>> =
  nestedDocTypes.reduce((acc, path: NestedDocPath) => {
    return {
      ...acc,
      [path]: nestedDocs[path].textFields.map((field) => `${path}.${field}`)
    };
  }, {} as Record<NestedDocPath, Array<string>>);

@Service()
export class FindCollaboratorsQueryBuilder {
  private peopleIndex: string;

  constructor(
    config: ConfigService,
    private parsedQueryTreeToElasticsearchQueriesService: ParsedQueryTreeToElasticsearchQueriesService
  ) {
    this.peopleIndex = config.elasticPeopleIndex;
  }

  public build(
    request: FindCollaboratorsRequest,
    parsedQueryTree: ParsedQueryTree
  ): Readonly<SearchRequest> {
    const nestedDocumentQueries = nestedDocTypes.map((nestedDocPath) => {
      return this.buildNestedQuery(nestedDocPath, request, parsedQueryTree);
    });

    const query: QueryDslQueryContainer = {
      bool: {
        should: nestedDocumentQueries,
        minimum_should_match: 1,
        filter: [
          {
            term: {
              collaboratorIds: request.personId
            }
          },
          {
            term: {
              projectIds: request.projectId
            }
          }
        ]
      }
    };

    return {
      index: this.peopleIndex,
      size: request.size,
      _source_includes: SOURCE_FIELDS,
      query
    };
  }

  private buildNestedQuery(
    path: NestedDocPath,
    request: FindCollaboratorsRequest,
    parsedQueryTree: ParsedQueryTree
  ): QueryDslQueryContainer {
    const filters: Array<QueryDslQueryContainer> = [
      this.toPersonIdTermQuery(path, request.personId)
    ];

    if (parsedQueryTree) {
      const textQueryFilter =
        this.parsedQueryTreeToElasticsearchQueriesService.parse(
          parsedQueryTree,
          pathQualifiedTextFields[path],
          () => ENGLISH
        );

      filters.push(textQueryFilter);
    }

    if (request.minDate) {
      const minimumDateQueryFilter = this.toMinimumDateRangeQuery(
        path,
        nestedDocs[path].dateField,
        request.minDate
      );

      filters.push(minimumDateQueryFilter);
    }

    return {
      nested: {
        path,
        score_mode: "sum",
        query: {
          bool: {
            filter: filters
          }
        }
      }
    };
  }

  private toPersonIdTermQuery(
    path: NestedDocPath,
    personId: string
  ): QueryDslQueryContainer {
    const pathQualifiedPersonIdField = `${path}.persons.id`;

    return {
      term: {
        [pathQualifiedPersonIdField]: personId
      }
    };
  }

  private toMinimumDateRangeQuery(
    path: NestedDocPath,
    dateField: string,
    minDate: number
  ) {
    const pathQualifiedDateField = `${path}.${dateField}`;

    return {
      bool: {
        should: [
          {
            range: {
              [pathQualifiedDateField]: {
                gte: minDate
              }
            }
          },
          {
            bool: {
              must_not: {
                exists: {
                  field: pathQualifiedDateField
                }
              }
            }
          }
        ],
        minimum_should_match: 1
      }
    };
  }
}
