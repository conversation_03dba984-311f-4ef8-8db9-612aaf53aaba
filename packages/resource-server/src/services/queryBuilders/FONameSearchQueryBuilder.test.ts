import { faker } from "@faker-js/faker";
import { NameSearchInput } from "@h1nyc/search-sdk/dist/interfaces/NameSearchInput";
import { generateNameSearchInput } from "../NameSearchResourceServiceRewrite.test";
import { AT_LEAST_ONE } from "./DefaultNameSearchBuilder";
import FONameSearchQueryBuilder, {
  FUZZINESS,
  LAST_NAME_APOSTROPHE_ANALYZER,
  NICK_NAME_ANALYZER
} from "./FONameSearchQueryBuilder";

describe("FO Name Search Builder Tests", () => {
  it("should build email Query", () => {
    const nameSearchBuilder = FONameSearchQueryBuilder.getInstance();

    const email = faker.internet.email();
    const actualQuery = nameSearchBuilder.getEmailOrHcpIdQuery([], email);

    expect(actualQuery).toEqual({
      bool: {
        filter: [
          {
            terms: {
              projectIds: ["1"]
            }
          },
          {
            term: {
              emails: email
            }
          }
        ]
      }
    });
  });

  it("should build name Query", () => {
    const nameSearchBuilder = FONameSearchQueryBuilder.getInstance();

    const firstName = faker.datatype.string();
    const lastName = faker.datatype.string();
    const nameSearchInput: NameSearchInput = generateNameSearchInput({
      query: "",
      firstName: firstName,
      lastName: lastName,
      middleName: "",
      email: ""
    });

    const actualQuery = nameSearchBuilder.getNameQuery(nameSearchInput, []);

    expect(actualQuery).toEqual({
      bool: {
        filter: [],
        should: [
          {
            bool: {
              must: [
                {
                  dis_max: {
                    queries: [
                      {
                        match: {
                          firstName_eng: {
                            _name: "firstName",
                            query: firstName
                          }
                        }
                      },
                      {
                        match: {
                          "firstName_eng.partial_name": {
                            _name: "firstName_partial_name",
                            query: firstName,
                            boost: 0.7
                          }
                        }
                      },
                      {
                        match: {
                          firstName_eng: {
                            _name: "firstName_nick_name_query_time",
                            query: firstName,
                            analyzer: NICK_NAME_ANALYZER,
                            boost: 0.7
                          }
                        }
                      },
                      {
                        match: {
                          "firstName_eng.nickname": {
                            _name: "firstName_nick_name_index_time",
                            query: firstName,
                            boost: 0.7
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  dis_max: {
                    queries: [
                      {
                        match: {
                          lastName_eng: {
                            _name: "last_name",
                            query: lastName,
                            boost: 1.5
                          }
                        }
                      },
                      {
                        match: {
                          lastName_eng: {
                            _name: "last_name_apostrophe",
                            query: lastName,
                            analyzer: LAST_NAME_APOSTROPHE_ANALYZER,
                            boost: 1.2
                          }
                        }
                      },
                      {
                        match: {
                          "lastName_eng.apostrophe_variations": {
                            _name: "last_name_apostrophe_index_time_variations",
                            query: lastName,
                            boost: 1.2
                          }
                        }
                      },
                      {
                        match: {
                          "lastName_eng.stripped": {
                            _name: "last_name_stripped",
                            query: lastName,
                            boost: 1.0
                          }
                        }
                      },
                      {
                        match: {
                          lastName_eng: {
                            _name: "last_name_apostrophe_fuzzy",
                            query: lastName,
                            prefix_length: 1,
                            analyzer: LAST_NAME_APOSTROPHE_ANALYZER,
                            boost: 0.7,
                            fuzziness: FUZZINESS
                          }
                        }
                      }
                    ]
                  }
                }
              ]
            }
          },
          {
            bool: {
              must: [
                {
                  terms: {
                    _name: "cmn",
                    firstName_cmn: [firstName]
                  }
                },
                {
                  terms: {
                    _name: "cmn",
                    lastName_cmn: [lastName]
                  }
                }
              ]
            }
          },
          {
            bool: {
              must: [
                {
                  terms: {
                    _name: "jpn",
                    firstName_jpn: [firstName]
                  }
                },
                {
                  terms: {
                    _name: "jpn",
                    lastName_jpn: [lastName]
                  }
                }
              ]
            }
          }
        ],
        minimum_should_match: AT_LEAST_ONE
      }
    });
  });
});
