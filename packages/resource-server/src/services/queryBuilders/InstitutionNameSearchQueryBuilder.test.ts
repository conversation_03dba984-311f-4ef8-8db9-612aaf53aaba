import { faker } from "@faker-js/faker";
import { InstitutionNameSearchQueryBuilder } from "./InstitutionNameSearchQueryBuilder";
import { expandFilterValueToRegex } from "../AggregationContainerBuilderService";

describe("InstitutionNameSearchQueryBuilder", () => {
  describe("buildNameSearchQuery", () => {
    it("should build a query given the input", () => {
      const queryBuilder = new InstitutionNameSearchQueryBuilder();
      const query = faker.lorem.word();
      const result = queryBuilder.buildNameSearchQuery(query);

      expect(result).toEqual({
        function_score: {
          query: {
            dis_max: {
              queries: [
                {
                  match: {
                    name: {
                      query,
                      operator: "AND",
                      fuzziness: "AUTO:4,7",
                      analyzer: "name_analyzer",
                      prefix_length: 1
                    }
                  }
                },
                {
                  match: {
                    name: {
                      query,
                      operator: "AND",
                      fuzziness: "AUTO:4,7",
                      analyzer: "institute_name_analyzer",
                      prefix_length: 1
                    }
                  }
                },
                {
                  match: {
                    "name.phonetic": {
                      analyzer: "name_analyzer",
                      fuzziness: "AUTO:4,7",
                      operator: "AND",
                      prefix_length: 1,
                      query
                    }
                  }
                },
                {
                  match: {
                    "name.phonetic": {
                      analyzer: "institute_name_analyzer",
                      fuzziness: "AUTO:4,7",
                      operator: "AND",
                      prefix_length: 1,
                      query
                    }
                  }
                },
                {
                  match: {
                    "name.autocomplete_search": {
                      query,
                      operator: "AND"
                    }
                  }
                }
              ]
            }
          },
          boost_mode: "sum",
          score_mode: "sum",
          functions: [
            {
              filter: {
                dis_max: {
                  queries: [
                    {
                      match: {
                        name: {
                          analyzer: "name_analyzer",
                          operator: "AND",
                          query
                        }
                      }
                    },
                    {
                      match: {
                        name: {
                          analyzer: "institute_name_analyzer",
                          operator: "AND",
                          query
                        }
                      }
                    },
                    {
                      match: {
                        "name.phonetic": {
                          analyzer: "name_analyzer",
                          operator: "AND",
                          query
                        }
                      }
                    },
                    {
                      match: {
                        "name.phonetic": {
                          analyzer: "institute_name_analyzer",
                          operator: "AND",
                          query
                        }
                      }
                    },
                    {
                      match: {
                        "name.autocomplete_search": {
                          query,
                          operator: "AND"
                        }
                      }
                    }
                  ]
                }
              },
              weight: 100
            },
            {
              filter: {
                regexp: {
                  "name.keyword": {
                    value: `${expandFilterValueToRegex(query)}.*`
                  }
                }
              },
              weight: 5
            },
            {
              field_value_factor: {
                field: "trials_count",
                factor: 1,
                modifier: "log1p",
                missing: 0
              }
            },
            {
              field_value_factor: {
                field: "research_institution_payment_count_3_year",
                factor: 1,
                modifier: "log1p",
                missing: 0
              }
            },
            {
              field_value_factor: {
                field: "trialLandscapeTagCount",
                factor: 1,
                modifier: "log1p",
                missing: 0
              }
            },
            {
              field_value_factor: {
                field: "trialInvestigatorCount",
                factor: 1,
                modifier: "log1p",
                missing: 0
              }
            },
            {
              field_value_factor: {
                field: "research_institution_payment_total_3_year",
                factor: 0.01,
                modifier: "log1p",
                missing: 0
              },
              weight: 0.8
            },
            {
              field_value_factor: {
                field: "ctmsOrganizationStats.trialCount",
                factor: 1,
                modifier: "log1p",
                missing: 0
              },
              weight: 0.8
            },
            {
              script_score: {
                script: {
                  source:
                    "if (doc['ultimateParentOrgType'].size() > 0 && doc['ultimateParentOrgType'].value == 'Health System') {\n                return 1;\n              } else {\n                return 0;\n              }"
                }
              },
              weight: 0.6
            },
            {
              field_value_factor: {
                field: "totalPatientDocs",
                factor: 1,
                modifier: "log1p",
                missing: 0
              },
              weight: 0.4
            },
            {
              script_score: {
                script: {
                  source:
                    "if (doc['orgTypes.keyword'].size() > 0 && ((doc['orgTypes.keyword'].value == 'Hospital') || (doc['orgTypes.keyword'].value == 'Research Center'))) {\n                return 1;\n              } else {\n                return 0;\n              }"
                }
              },
              weight: 0.2
            }
          ]
        }
      });
    });

    it("should return null if the query is empty", () => {
      const queryBuilder = new InstitutionNameSearchQueryBuilder();
      const query = "";
      const result = queryBuilder.buildNameSearchQuery(query);

      expect(result).toBeNull();
    });
  });
});
