import { estypes } from "@elastic/elasticsearch";
import {
  QueryDslBoolQuery,
  SearchInnerHits,
  QueryDslQueryContainer
} from "@elastic/elasticsearch/lib/api/types";
import { EntitySortOptions } from "@h1nyc/account-sdk/dist/interfaces/Sort";
import { SortDirection } from "@h1nyc/account-sdk";

import {
  ClaimsPage,
  IolClaimsFilters,
  IolClaimsInput,
  IolClaimSortOptions,
  IolClaimType
} from "@h1nyc/search-sdk";
import _ from "lodash";
import { Service } from "typedi";
import { QueryUnderstandingServiceResponse } from "../../proto/query_understanding_service_pb";
import { ConfigService } from "../ConfigService";
import { ParsedQueryTree } from "../KeywordSearchResourceServiceRewrite";
import { ENGLISH } from "../LanguageDetectService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "../ParsedQueryTreeToElasticsearchQueries";

type nestedClaimsPath = "diagnoses" | "procedures" | "prescriptions";
const CLAIMS_FIELDS = ["code_eng", "description_eng"];

const sortFieldMap: Readonly<Record<string, string>> = {
  claimsCount: "count",
  claimsUniqueCount: "internalUniqueCount",
  code: "code_eng.keyword",
  description: "description_eng.keyword",
  rank: "countryRank",
  regionRank: "stateRank"
};

const CLAIMS_DESCRIPTION_ANALYZER = "main_analyzer";

interface QueryBuilderArguments {
  name?: nestedClaimsPath;
  path: nestedClaimsPath;
  fields: string[];
  searchQuery?: string;
  innerHits?: estypes.SearchInnerHits;
  filters?: Array<QueryDslQueryContainer>;
  analyzer?: string;
  prefixClauses?: Array<QueryDslQueryContainer>;
}

const MATCH_ALL: Readonly<QueryDslQueryContainer> = {
  match_all: {}
};

type ValueRange = {
  min?: number | null;
  max?: number | null;
};

const OR = " | ";
const PREFIX_MATCH = "*";

const h1InstitutionIdField = "masterOrganizationId";
export type InstitutionClaimsQueryBuilderOptions = {
  shouldUseUniquePatientCount: boolean;
  disableUniquePatientCountForOnlyProcedures?: boolean;
};

@Service()
export class InstitutionClaimsQueryBuilder {
  private institutionIndexName;

  constructor(
    config: ConfigService,
    private parsedQueryTreeToElasticQueryService: ParsedQueryTreeToElasticsearchQueriesService
  ) {
    this.institutionIndexName = config.elasticInstitutionsIndex;
  }

  public buildClaimsSearchForInstitutionQuery(
    input: IolClaimsInput,
    parsedQueryTree?: ParsedQueryTree,
    queryUnderstandingServerResponse?: QueryUnderstandingServiceResponse,
    options?: InstitutionClaimsQueryBuilderOptions
  ): estypes.SearchRequest {
    const filterClauses = [];
    const claimType = input.filters.type;
    filterClauses.push({
      term: {
        [h1InstitutionIdField]: {
          value: input.iolId
        }
      }
    });

    const keywordQueryForClaims = this.buildClaimsQueryClause(
      input,
      claimType,
      parsedQueryTree,
      queryUnderstandingServerResponse,
      options
    );
    const boolQuery: QueryDslBoolQuery = {
      filter: [...filterClauses, keywordQueryForClaims]
    };

    const request: estypes.SearchRequest = {
      index: this.institutionIndexName,
      from: 0,
      size: 10000,
      track_total_hits: true,
      _source: false,
      query: {
        bool: boolQuery
      }
    };

    return request;
  }

  private buildClaimsQueryClause(
    input: IolClaimsInput,
    claimType: IolClaimType,
    parsedQueryTree?: ParsedQueryTree,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse,
    options?: InstitutionClaimsQueryBuilderOptions
  ): QueryDslQueryContainer {
    let queryClause: QueryDslQueryContainer;

    let nestedClaimPath: nestedClaimsPath;

    if (claimType == IolClaimType.Diagnosis) {
      nestedClaimPath = "diagnoses";
    } else if (claimType === IolClaimType.Procedure) {
      nestedClaimPath = "procedures";
    } else {
      nestedClaimPath = "prescriptions";
    }

    const claimFilters = input.filters
      ? this.buildClaimsFilters(nestedClaimPath, input.filters, options)
      : [];

    const claimInnerHits = this.buildClaimsInnerHits(
      nestedClaimPath,
      input.page,
      input.sort,
      options
    );

    if (parsedQueryTree) {
      if (claimType === IolClaimType.Prescriptions) {
        const parsedQueryContainerForClaim =
          this.parsedQueryTreeToElasticQueryService.parse(parsedQueryTree, [
            `${nestedClaimPath}.generic_name.text`
          ]);

        const isParsedQueryString = typeof parsedQueryTree === "string";

        let searchQueryForClaims;

        if (isParsedQueryString) {
          searchQueryForClaims = parsedQueryTree;
        }

        const prefixClauseForGenericNameArray: Array<QueryDslQueryContainer> =
          this.buildQueryClauseForPrefixMatching(
            input.filters.keywords!,
            `${nestedClaimPath}.generic_name.text`
          );

        queryClause = this.buildQueryClauseForClaim(
          nestedClaimPath,
          parsedQueryContainerForClaim,
          claimFilters,
          claimInnerHits,
          [...prefixClauseForGenericNameArray],
          searchQueryForClaims
        );
      } else {
        const parsedQueryContainerForClaim =
          this.parsedQueryTreeToElasticQueryService.parse(
            parsedQueryTree,
            [`${nestedClaimPath}.code`, `${nestedClaimPath}.description`],
            ENGLISH
          );

        const prefixClauseForCodeArray: Array<QueryDslQueryContainer> =
          this.buildQueryClauseForPrefixMatching(
            input.filters.keywords!,
            `${nestedClaimPath}.code_eng`
          );

        const prefixClauseForDescArray: Array<QueryDslQueryContainer> =
          this.buildQueryClauseForPrefixMatching(
            input.filters.keywords!,
            `${nestedClaimPath}.description_eng`
          );

        const isParsedQueryString = typeof parsedQueryTree === "string";

        let searchQueryForClaims;

        if (isParsedQueryString) {
          if (claimType === IolClaimType.Procedure) {
            searchQueryForClaims = this.getSynonymizedQuery(
              parsedQueryTree,
              queryUnderstandingServiceResponse
            );
          } else if (claimType === IolClaimType.Diagnosis) {
            searchQueryForClaims = this.constructDiagnosesQueryString(
              parsedQueryTree,
              queryUnderstandingServiceResponse
            );
          }
        }

        queryClause = this.buildQueryClauseForClaim(
          nestedClaimPath,
          parsedQueryContainerForClaim,
          claimFilters,
          claimInnerHits,
          [...prefixClauseForCodeArray, ...prefixClauseForDescArray],
          searchQueryForClaims
        );
      }
    } else {
      queryClause = {
        nested: {
          _name: nestedClaimPath,
          path: nestedClaimPath,
          query: !_.isEmpty(claimFilters)
            ? this.buildMatchAllWithFilterClause(claimFilters!)
            : MATCH_ALL,
          inner_hits: claimInnerHits
        }
      };
    }

    return queryClause;
  }

  private buildClaimsInnerHits(
    claimsPath: nestedClaimsPath,
    page: ClaimsPage,
    sort: EntitySortOptions<IolClaimSortOptions>,
    options?: InstitutionClaimsQueryBuilderOptions
  ) {
    const sortDirection = sort.direction == SortDirection.Desc ? "desc" : "asc";
    const isProcedureWithUniqueCount =
      claimsPath === "procedures" &&
      !options?.disableUniquePatientCountForOnlyProcedures;
    const isDiagnosis = claimsPath === "diagnoses";
    const shouldUseUniquePatientCountForClaim =
      (isProcedureWithUniqueCount || isDiagnosis) &&
      options?.shouldUseUniquePatientCount;

    const sortFieldKeyForUniquePatientCount =
      sort.field === "claimsCount" && shouldUseUniquePatientCountForClaim
        ? "claimsUniqueCount"
        : sort.field;
    const sortField = sortFieldMap[sortFieldKeyForUniquePatientCount];
    const claimCountField = shouldUseUniquePatientCountForClaim
      ? "internalUniqueCount"
      : "count";

    if (claimsPath === "prescriptions") {
      let sortField = "generic_name";

      if (sort.field === "claimsCount") {
        sortField = "num_prescriptions";
      }
      const innerHits: estypes.SearchInnerHits = {
        _source: {
          includes: [`${claimsPath}.generic_name`]
        },
        from: page.offset,
        docvalue_fields: [`${claimsPath}.num_prescriptions`],
        size: page.limit,
        sort: {
          [`${claimsPath}.${sortField}`]: { order: `${sortDirection}` }
        }
      };
      return innerHits;
    } else {
      const innerHits: estypes.SearchInnerHits = {
        _source: {
          includes: [`${claimsPath}.code_eng`, `${claimsPath}.description_eng`]
        },
        from: page.offset,
        docvalue_fields: [
          `${claimsPath}.${claimCountField}`,
          `${claimsPath}.countryRank`,
          `${claimsPath}.totalInCountry`,
          `${claimsPath}.stateRank`,
          `${claimsPath}.totalInState`
        ],
        size: page.limit,
        sort: { [`${claimsPath}.${sortField}`]: { order: `${sortDirection}` } }
      };
      return innerHits;
    }
  }

  private constructDiagnosesQueryString(
    parsedQueryTree: string,
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): string {
    if (queryUnderstandingServiceResponse?.getDiagnosisCodesList().length) {
      return queryUnderstandingServiceResponse!
        .getDiagnosisCodesList()
        .map((diagnosisCode) => `(${diagnosisCode}*)`)
        .join(OR);
    } else {
      return parsedQueryTree;
    }
  }

  private toNestedSimpleQueryStringWithPrefixMatchingForCode({
    path,
    name,
    fields,
    searchQuery,
    innerHits,
    filters,
    analyzer,
    prefixClauses
  }: QueryBuilderArguments): QueryDslQueryContainer {
    const simple_query_string = this.toSimpleQueryString({
      path,
      name,
      fields,
      searchQuery,
      analyzer
    });
    return {
      nested: {
        path: path,
        query: {
          bool: {
            must: MATCH_ALL,
            should: [...prefixClauses!, { simple_query_string }],
            minimum_should_match: 1,
            filter: !_.isEmpty(filters) ? filters : undefined
          }
        },
        inner_hits: innerHits
      }
    };
  }

  private getSynonymizedQuery(
    query?: string,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
  ): string | undefined {
    let searchQuery = query;
    // If the query was augmented with synonyms by the query understanding service then use the augmented query
    if (
      queryUnderstandingServiceResponse &&
      queryUnderstandingServiceResponse.getAugmentedQuery()
    ) {
      searchQuery = this.convertQueryToSimpleQueryStringSyntax(
        queryUnderstandingServiceResponse.getAugmentedQuery()
      );
    }
    return searchQuery;
  }

  private convertQueryToSimpleQueryStringSyntax(query: string): string {
    const orRegex = /\sOR\s/g;
    const andRegex = /\sAND\s/g;
    let simpleQueryString = query.replace(orRegex, OR);
    simpleQueryString = simpleQueryString.replace(andRegex, " + ");
    return simpleQueryString;
  }

  private toSimpleQueryString({
    path,
    name,
    fields,
    searchQuery,
    analyzer
  }: QueryBuilderArguments): estypes.QueryDslSimpleQueryStringQuery {
    const fieldsWithPath = fields.map((field) => {
      return `${path}.${field}`;
    });

    return {
      _name: name ?? path,
      query: searchQuery!,
      fields: fieldsWithPath,
      default_operator: "and",
      analyzer
    };
  }

  private toFieldRangeMinMaxFilter(
    fieldName: string,
    valueRange: ValueRange
  ): QueryDslQueryContainer {
    return {
      range: {
        [fieldName]: {
          gte: valueRange.min ?? undefined,
          lte: valueRange.max ?? undefined
        }
      }
    };
  }

  private buildClaimsFilters(
    claimsPath: nestedClaimsPath,
    inputFilters: IolClaimsFilters,
    options?: InstitutionClaimsQueryBuilderOptions
  ): QueryDslQueryContainer[] {
    const filters = [];

    if (!_.isEmpty(inputFilters.codes)) {
      filters.push({
        simple_query_string: this.toSimpleQueryString({
          path: claimsPath,
          name: claimsPath,
          searchQuery: inputFilters
            .codes!.map((code) => code + PREFIX_MATCH)
            .join(OR),
          fields: ["code_eng"]
        })
      });
    }
    if (!_.isEmpty(inputFilters.descriptions)) {
      const descriptionExactMatchClause = {
        simple_query_string: this.toSimpleQueryString({
          path: claimsPath,
          name: claimsPath,
          searchQuery: inputFilters.descriptions!.join(OR),
          fields: ["description_eng"]
        })
      };
      const descPrefixArray: Array<QueryDslQueryContainer> =
        this.buildQueryClauseForPrefixMatching(
          inputFilters.descriptions!,
          `${claimsPath}.description_eng`
        );
      filters.push({
        bool: {
          should: [descriptionExactMatchClause, ...descPrefixArray],
          minimum_should_match: 1
        }
      });
    }

    if (!_.isEmpty(inputFilters.genericNames)) {
      const prescriptionsExactMatchClause = {
        simple_query_string: this.toSimpleQueryString({
          path: claimsPath,
          name: claimsPath,
          searchQuery: inputFilters.genericNames!.join(OR),
          fields: ["generic_name.text"]
        })
      };
      const prescriptionsPrefixArray: Array<QueryDslQueryContainer> =
        this.buildQueryClauseForPrefixMatching(
          inputFilters.genericNames!,
          `${claimsPath}.generic_name.text`
        );
      filters.push({
        bool: {
          should: [prescriptionsExactMatchClause, ...prescriptionsPrefixArray],
          minimum_should_match: 1
        }
      });
    }

    const isProcedureWithUniqueCount =
      claimsPath === "procedures" &&
      !options?.disableUniquePatientCountForOnlyProcedures;
    const isDiagnosis = claimsPath === "diagnoses";
    const shouldUseUniquePatientCountForClaim =
      (isProcedureWithUniqueCount || isDiagnosis) &&
      options?.shouldUseUniquePatientCount;
    let claimCountField = shouldUseUniquePatientCountForClaim
      ? "internalUniqueCount"
      : "count";

    if (claimsPath === "prescriptions") {
      claimCountField = "num_prescriptions";
    }

    if (_.has(inputFilters, "count.max") || _.has(inputFilters, "count.min")) {
      filters.push(
        this.toFieldRangeMinMaxFilter(`${claimsPath}.${claimCountField}`, {
          min: inputFilters.count?.min,
          max: inputFilters.count?.max
        })
      );
    }

    if (claimsPath !== "prescriptions") {
      if (_.has(inputFilters, "rank.max") || _.has(inputFilters, "rank.min")) {
        filters.push(
          this.toFieldRangeMinMaxFilter(`${claimsPath}.countryRank`, {
            min: inputFilters.rank?.min,
            max: inputFilters.rank?.max
          })
        );
      }
      if (
        _.has(inputFilters, "regionRank.max") ||
        _.has(inputFilters, "regionRank.min")
      ) {
        filters.push(
          this.toFieldRangeMinMaxFilter(`${claimsPath}.stateRank`, {
            min: inputFilters.regionRank?.min,
            max: inputFilters.regionRank?.max
          })
        );
      }
    }

    return filters;
  }
  private buildMatchAllWithFilterClause(
    claimsFilters: estypes.QueryDslQueryContainer[]
  ): estypes.QueryDslQueryContainer {
    return {
      bool: {
        must: MATCH_ALL,
        filter: claimsFilters
      }
    };
  }
  private buildQueryClauseWithParsedQueryContainer(
    parsedQueryContainerForClaims: Readonly<estypes.QueryDslQueryContainer>,
    claimsFilters: QueryDslQueryContainer[],
    claimsPath: nestedClaimsPath,
    inner_hits: SearchInnerHits,
    prefixClauses: QueryDslQueryContainer[]
  ): QueryDslQueryContainer {
    return {
      nested: {
        _name: claimsPath,
        path: claimsPath,
        query: {
          bool: {
            must: MATCH_ALL,
            should: [...prefixClauses!, parsedQueryContainerForClaims],
            minimum_should_match: 1,
            filter: !_.isEmpty(claimsFilters) ? [...claimsFilters!] : undefined
          }
        },
        inner_hits
      }
    };
  }
  private buildQueryClauseForClaim(
    claimsPath: nestedClaimsPath,
    parsedQueryContainerForClaims: QueryDslQueryContainer,
    claimsFilters: QueryDslQueryContainer[] = [],
    innerHits: SearchInnerHits,
    prefixClauses: QueryDslQueryContainer[],
    searchQueryForClaims?: string
  ) {
    if (searchQueryForClaims) {
      return this.toNestedSimpleQueryStringWithPrefixMatchingForCode({
        name: claimsPath,
        path: claimsPath,
        fields:
          claimsPath === "prescriptions"
            ? ["generic_name.text"]
            : CLAIMS_FIELDS,
        searchQuery: searchQueryForClaims,
        innerHits: innerHits,
        filters: claimsFilters,
        analyzer: CLAIMS_DESCRIPTION_ANALYZER,
        prefixClauses
      });
    } else {
      return this.buildQueryClauseWithParsedQueryContainer(
        parsedQueryContainerForClaims,
        claimsFilters,
        claimsPath,
        innerHits,
        prefixClauses
      );
    }
  }

  private buildQueryClauseForPrefixMatching(
    queries: string[],
    prefixField: string
  ) {
    return queries.map((query) => {
      return {
        prefix: {
          [prefixField]: {
            value: query
          }
        }
      };
    });
  }
}
