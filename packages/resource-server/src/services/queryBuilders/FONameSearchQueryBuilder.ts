import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";

import { NameSearchInput } from "@h1nyc/search-sdk/dist/interfaces/NameSearchInput";
import { CHINESE, JAPANESE, Language } from "../LanguageDetectService";
import { AT_LEAST_ONE } from "./DefaultNameSearchBuilder";

export const FUZZINESS = "AUTO:4,7";
export const NICK_NAME_ANALYZER = "name_nickname_analyzer";
export const LAST_NAME_APOSTROPHE_ANALYZER = "last_name_apostrophe_analyzer";
export default class FONameSearchQueryBuilder {
  private static instance: FONameSearchQueryBuilder;

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {}

  public static getInstance(): FONameSearchQueryBuilder {
    if (!FONameSearchQueryBuilder.instance) {
      FONameSearchQueryBuilder.instance = new FONameSearchQueryBuilder();
    }

    return FONameSearchQueryBuilder.instance;
  }

  getEmailOrHcpIdQuery(
    filters: QueryDslQueryContainer[],
    email?: string,
    hcpId?: string
  ): QueryDslQueryContainer {
    let res;

    if (email?.length && hcpId?.length) {
      res = {
        bool: {
          should: [
            ...this.buildHcpIdFilter(hcpId!),
            this.buildEmailIdFilter(email)
          ],
          minimum_should_match: 1,
          filter: [...this.buildProjectIdFilter(["1"]), ...filters]
        }
      };
    } else if (email?.length) {
      res = {
        bool: {
          filter: [
            ...this.buildProjectIdFilter(["1"]),
            ...filters,
            this.buildEmailIdFilter(email!)
          ]
        }
      };
    } else {
      res = {
        bool: {
          filter: [
            ...this.buildProjectIdFilter(["1"]),
            ...filters,
            ...this.buildHcpIdFilter(hcpId!)
          ]
        }
      };
    }

    return res;
  }

  private buildEmailIdFilter(email: string): QueryDslQueryContainer {
    const emailIdFilter = {
      term: {
        emails: email
      }
    };

    return emailIdFilter;
  }

  getNameQuery = (
    nameSearchInput: NameSearchInput,
    filter: QueryDslQueryContainer[]
  ): QueryDslQueryContainer => {
    const res = {
      bool: {
        filter,
        should: [
          {
            bool: {
              must: this.nameVariationsQuery(nameSearchInput)
            }
          },
          {
            bool: {
              must: this.nameQueryCmnJpn(nameSearchInput, CHINESE)
            }
          },
          {
            bool: {
              must: this.nameQueryCmnJpn(nameSearchInput, JAPANESE)
            }
          }
        ],
        minimum_should_match: AT_LEAST_ONE
      }
    };

    return res;
  };

  private firstNameVariationQueries(query: string): QueryDslQueryContainer {
    return {
      dis_max: {
        queries: [
          {
            match: {
              firstName_eng: {
                _name: "firstName",
                query: query
              }
            }
          },
          {
            match: {
              "firstName_eng.partial_name": {
                _name: "firstName_partial_name",
                query: query,
                boost: 0.7
              }
            }
          },
          {
            match: {
              firstName_eng: {
                _name: "firstName_nick_name_query_time",
                query: query,
                analyzer: NICK_NAME_ANALYZER,
                boost: 0.7
              }
            }
          },
          // Handle data issue where we have nick name in profile
          // Once data issue is fixed this won't be required.
          {
            match: {
              "firstName_eng.nickname": {
                _name: "firstName_nick_name_index_time",
                query: query,
                boost: 0.7
              }
            }
          }
        ]
      }
    };
  }

  private middleNameVariationQueries(query: string): QueryDslQueryContainer {
    return {
      dis_max: {
        queries: [
          {
            match: {
              middleName_eng: {
                _name: "middle_name",
                query: query,
                boost: 0.5
              }
            }
          },
          {
            match: {
              middleName_eng: {
                _name: "middle_name_nickname",
                query: query,
                analyzer: NICK_NAME_ANALYZER,
                boost: 0.3
              }
            }
          },
          {
            match: {
              "middleName_eng.initial": {
                _name: "middle_name_initial",
                query: query,
                boost: 0.2
              }
            }
          }
        ]
      }
    };
  }

  private lastNameVariationsQueries(query: string): QueryDslQueryContainer {
    return {
      dis_max: {
        queries: [
          {
            match: {
              lastName_eng: {
                _name: "last_name",
                query: query,
                boost: 1.5
              }
            }
          },
          {
            match: {
              lastName_eng: {
                _name: "last_name_apostrophe",
                query: query,
                analyzer: LAST_NAME_APOSTROPHE_ANALYZER,
                boost: 1.2
              }
            }
          },
          {
            match: {
              "lastName_eng.apostrophe_variations": {
                _name: "last_name_apostrophe_index_time_variations",
                query: query,
                boost: 1.2
              }
            }
          },
          {
            match: {
              "lastName_eng.stripped": {
                _name: "last_name_stripped",
                query: query,
                boost: 1.0
              }
            }
          },
          {
            match: {
              lastName_eng: {
                _name: "last_name_apostrophe_fuzzy",
                query: query,
                prefix_length: 1,
                analyzer: LAST_NAME_APOSTROPHE_ANALYZER,
                boost: 0.7,
                fuzziness: FUZZINESS
              }
            }
          }
        ]
      }
    };
  }

  private nameVariationsQuery(
    nameSearchInput: NameSearchInput
  ): Array<QueryDslQueryContainer> {
    const mustClauses = [];
    if (nameSearchInput?.firstName)
      mustClauses.push(
        this.firstNameVariationQueries(nameSearchInput.firstName)
      );
    if (nameSearchInput?.middleName)
      mustClauses.push(
        this.middleNameVariationQueries(nameSearchInput.middleName)
      );
    if (nameSearchInput?.lastName)
      mustClauses.push(
        this.lastNameVariationsQueries(nameSearchInput.lastName)
      );

    return mustClauses;
  }

  private nameQueryCmnJpn(
    nameSearchInput: NameSearchInput,
    lang: Language
  ): Array<QueryDslQueryContainer> {
    const mustClauses = [];
    if (nameSearchInput?.firstName)
      mustClauses.push({
        terms: {
          [`firstName_${lang}`]: [nameSearchInput.firstName],
          _name: lang
        }
      });

    if (nameSearchInput?.lastName)
      mustClauses.push({
        terms: {
          [`lastName_${lang}`]: [nameSearchInput.lastName],
          _name: lang
        }
      });

    return mustClauses;
  }

  private buildHcpIdFilter(hcpId: string): Array<QueryDslQueryContainer> {
    const mustClauses = [];

    mustClauses.push({
      terms: {
        id: [hcpId]
      }
    });

    return mustClauses;
  }
  private buildProjectIdFilter(
    projectIds: string[]
  ): Array<QueryDslQueryContainer> {
    const mustClauses = [];

    mustClauses.push({
      terms: {
        projectIds
      }
    });

    return mustClauses;
  }
}
