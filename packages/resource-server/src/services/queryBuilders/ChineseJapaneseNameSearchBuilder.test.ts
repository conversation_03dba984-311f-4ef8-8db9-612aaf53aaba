import { faker } from "@faker-js/faker";
import { NameSearchBuilderArgWithKeyword } from "./NameSearchBuilder";
import { NameSearchFeatureFlags } from "../NameSearchResourceServiceRewrite";
import { EMPTY_ARRAY } from "./DefaultNameSearchBuilder.test";
import { QueryUnderstandingServiceResponse } from "../../proto/query_understanding_service_pb";
import ChineseJapaneseNameSearchBuilder from "./ChineseJapaneseNameSearchBuilder";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
const FEATURE_FLAG_DEFAULTS: NameSearchFeatureFlags = {
  enableHighConfHCPFeature: false,
  enableQueryIntent: false,
  enableIntentBasedSearchQuery: false,
  enableNameSearchPersonalisation: false,
  enableCTMSV2: false,
  enableResultsOutsideUsersSlice: false,
  enableTagsInElasticsearch: false,
  enableBrazilianClaims: true,
  enableUniquePatientCountForClaims: false,
  disableUniquePatientCountForOnlyProcedures: false,
  enableNestedIndicationFilter: false,
  enableCcsrExclusionForMatchedCounts: false,
  enableLocationFilterRegionRollup: false,
  enableNewGlobalLeaderTier: false,
  enableExUSGlobalLeader: false,
  enableCountrySpecificNonIndicationLeaderFilters: false
};
const generateFeatureFlags = (
  overrides: Partial<NameSearchFeatureFlags>
): NameSearchFeatureFlags => {
  return {
    ...FEATURE_FLAG_DEFAULTS,
    ...overrides
  };
};

describe("useCmnAndJpnFields", () => {
  it("should have both jpn and cmn clauses in should clauses", () => {
    const query = faker.datatype.string();
    const projectId = faker.datatype.string();

    const nameSearchBuilder = ChineseJapaneseNameSearchBuilder.getInstance();
    const queryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();
    const args: NameSearchBuilderArgWithKeyword = {
      query,
      numberOfNameVariationsToMatch: 1,
      from: 0,
      size: 10,
      filter: [],
      language: "eng",
      queryUnderstandingServiceResponse: queryUnderstandingServiceResponse,
      featureFlags: generateFeatureFlags({}),
      onboardingData: {
        indications: EMPTY_ARRAY,
        countries: EMPTY_ARRAY,
        states: EMPTY_ARRAY
      },
      projectId,
      useCmnAndJpnFields: true
    };
    const shouldClauses = nameSearchBuilder.getNameQueryShouldClauses(
      args.query,
      args.projectId,
      args.useCmnAndJpnFields,
      args.numberOfNameVariationsToMatch,
      "eng"
    );

    expect(shouldClauses).toBeDefined();

    expect(shouldClauses).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          term: expect.objectContaining({
            name_cmn: expect.objectContaining({
              value: query
            })
          })
        }),
        expect.objectContaining({
          term: expect.objectContaining({
            name_jpn: expect.objectContaining({
              value: query
            })
          })
        }),
        expect.objectContaining({
          term: expect.objectContaining({
            lastName_jpn: expect.objectContaining({
              value: query
            })
          })
        }),
        expect.objectContaining({
          term: expect.objectContaining({
            lastName_cmn: expect.objectContaining({
              value: query
            })
          })
        }),
        expect.objectContaining({
          prefix: expect.objectContaining({
            name_cmn: expect.objectContaining({
              value: query
            })
          })
        }),
        expect.objectContaining({
          bool: expect.objectContaining({
            filter: expect.objectContaining({
              match: expect.objectContaining({
                ["name_jpn.search"]: {
                  query
                }
              })
            }),
            must: expect.arrayContaining([
              {
                multi_match: expect.objectContaining({
                  fields: expect.arrayContaining(["name_jpn.search^2"])
                })
              }
            ])
          })
        }),
        expect.objectContaining({
          bool: expect.objectContaining({
            filter: expect.objectContaining({
              match: expect.objectContaining({
                ["name_cmn.search"]: {
                  query
                }
              })
            }),
            must: expect.arrayContaining([
              {
                multi_match: expect.objectContaining({
                  fields: expect.arrayContaining(["name_cmn.search^2"])
                })
              }
            ])
          })
        })
      ])
    );
  });

  it("should have both jpn and cmn fields in source field and sort criterion when query is present", () => {
    const query = faker.datatype.string();
    const projectId = faker.datatype.string();

    const nameSearchBuilder = ChineseJapaneseNameSearchBuilder.getInstance();
    const queryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();
    const args: NameSearchBuilderArgWithKeyword = {
      query,
      numberOfNameVariationsToMatch: 1,
      from: 0,
      size: 10,
      filter: [],
      language: "jpn",
      queryUnderstandingServiceResponse: queryUnderstandingServiceResponse,
      featureFlags: generateFeatureFlags({}),
      onboardingData: {
        indications: EMPTY_ARRAY,
        countries: EMPTY_ARRAY,
        states: EMPTY_ARRAY
      },
      projectId,
      useCmnAndJpnFields: true
    };
    const actualQuery = nameSearchBuilder.createNameSearchBody(args);
    expect(actualQuery).toEqual(
      expect.objectContaining({
        _source_includes: expect.arrayContaining([
          `firstName_jpn`,
          `lastName_jpn`,
          `middleName_jpn`,
          `name_jpn`,
          `specialty_jpn`,
          `firstName_cmn`,
          `lastName_cmn`,
          `middleName_cmn`,
          `name_cmn`,
          `specialty_cmn`
        ]),
        sort: expect.arrayContaining([
          {
            _score: { order: "desc" }
          },
          {
            [`lastName_jpn.sort`]: {
              order: "asc"
            }
          },
          {
            [`lastName_cmn.sort`]: {
              order: "asc"
            }
          },
          {
            [`firstName_jpn.sort`]: {
              order: "asc"
            }
          },
          {
            [`firstName_cmn.sort`]: {
              order: "asc"
            }
          }
        ])
      })
    );
  });

  it("should have both jpn and cmn fields in source field and sort criterion when query is not present", () => {
    const query = "";
    const projectId = faker.datatype.string();

    const nameSearchBuilder = ChineseJapaneseNameSearchBuilder.getInstance();
    const queryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();
    const args: NameSearchBuilderArgWithKeyword = {
      query,
      numberOfNameVariationsToMatch: 1,
      from: 0,
      size: 10,
      filter: [],
      language: "cmn",
      queryUnderstandingServiceResponse: queryUnderstandingServiceResponse,
      featureFlags: generateFeatureFlags({}),
      onboardingData: {
        indications: EMPTY_ARRAY,
        countries: EMPTY_ARRAY,
        states: EMPTY_ARRAY
      },
      projectId,
      useCmnAndJpnFields: true
    };
    const actualQuery = nameSearchBuilder.createNameSearchBodyNoKeyWord(args);
    expect(actualQuery).toEqual(
      expect.objectContaining({
        _source_includes: expect.arrayContaining([
          `firstName_jpn`,
          `lastName_jpn`,
          `middleName_jpn`,
          `name_jpn`,
          `specialty_jpn`,
          `firstName_cmn`,
          `lastName_cmn`,
          `middleName_cmn`,
          `name_cmn`,
          `specialty_cmn`
        ]),
        sort: expect.arrayContaining([
          {
            _score: { order: "desc" }
          },
          {
            [`lastName_jpn.sort`]: {
              order: "asc"
            }
          },
          {
            [`lastName_cmn.sort`]: {
              order: "asc"
            }
          },
          {
            [`firstName_jpn.sort`]: {
              order: "asc"
            }
          },
          {
            [`firstName_cmn.sort`]: {
              order: "asc"
            }
          }
        ])
      })
    );
  });
});

describe("Congress contributor ranking", () => {
  it("should add congress contributor ranking query with congress filters", () => {
    const query = faker.datatype.string();
    const projectId = faker.datatype.string();
    const userPreferredCountry = faker.address.country();
    const userPreferredState = faker.address.state();
    const qusWithoutLocationIntent = new QueryUnderstandingServiceResponse();

    const nameSearchBuilder = ChineseJapaneseNameSearchBuilder.getInstance();
    const args: NameSearchBuilderArgWithKeyword = {
      query,
      numberOfNameVariationsToMatch: 1,
      queryUnderstandingServiceResponse: qusWithoutLocationIntent,
      from: 0,
      size: 10,
      filter: [],
      language: faker.helpers.arrayElement(["jpn", "cmn"]),
      featureFlags: generateFeatureFlags({
        enableHighConfHCPFeature: false,
        enableQueryIntent: false,
        enableIntentBasedSearchQuery: true,
        enableNameSearchPersonalisation: true,
        enableCTMSV2: true,
        enableResultsOutsideUsersSlice: false,
        enableTagsInElasticsearch: false
      }),
      onboardingData: {
        indications: EMPTY_ARRAY,
        countries: [userPreferredCountry],
        states: [userPreferredState]
      },
      projectId,
      useCmnAndJpnFields: false,
      useCongressContributorRanking: true,
      congressFilters: [
        {
          nested: {
            path: "congress",
            query: {
              bool: {
                filter: [
                  {
                    term: {
                      h1_conference_id: faker.datatype.string()
                    }
                  }
                ]
              }
            }
          }
        }
      ]
    };
    const actualQuery = nameSearchBuilder.createNameSearchBody(args);

    const congressContributorRankingQueries = (
      actualQuery.query!.function_score!.query!.bool!
        .must as QueryDslQueryContainer[]
    )[0].bool!.must as QueryDslQueryContainer[];

    expect(congressContributorRankingQueries).toEqual([
      {
        bool: {
          should: [
            {
              nested: {
                path: "congress",
                query: {
                  function_score: {
                    query: {
                      bool: {
                        filter:
                          args.congressFilters?.[0]?.nested?.query?.bool
                            ?.filter,
                        should: [
                          {
                            simple_query_string: {
                              query,
                              fields: [
                                "congress.role.search",
                                "congress.title_eng",
                                "congress.title_cmn",
                                "congress.title_jpn"
                              ],
                              default_operator: "OR"
                            }
                          }
                        ],
                        minimum_should_match: 0
                      }
                    },
                    functions: [
                      {
                        script_score: {
                          script: {
                            source:
                              "doc['congress.role'].value === 'Keynote' ? 2 : 1"
                          }
                        }
                      }
                    ]
                  }
                }
              }
            },
            {
              term: {
                isScholarLeader: {
                  value: true,
                  boost: 2
                }
              }
            }
          ]
        }
      }
    ]);
  });
});
