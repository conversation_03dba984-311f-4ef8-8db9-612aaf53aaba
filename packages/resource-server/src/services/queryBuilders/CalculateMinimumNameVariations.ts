import { Service } from "typedi";
import { ElasticSearchService } from "../ElasticSearchService";

const NAME_ANALYZER = "name_analyzer";
const LAST_NAME_APOSTROPHE_WITH_SPACES_REGEX =
  /\bO [BCDFGHKLMNQRST]\w+|\bD [AEIOUS]\w+/gi;

@Service()
export default class CalculateMinimumNameVariations {
  private async tokenCountForAnalyzer(
    elasticService: ElasticSearchService,
    peopleIndex: string,
    queryText: string,
    analyzerName: string
  ) {
    const response = await elasticService.analyzeIndices({
      index: peopleIndex,
      analyzer: analyzerName,
      text: queryText
    });

    return response.tokens?.length || 0;
  }

  private async getAdjustedTokenCount(
    elasticService: ElasticSearchService,
    peopleIndex: string,
    queryText: string
  ) {
    let tokenCount = await this.tokenCountForAnalyzer(
      elasticService,
      peopleIndex,
      queryText,
      NAME_ANALYZER
    );

    const apostropheSpaceVariationMatches = queryText.match(
      LAST_NAME_APOSTROPHE_WITH_SPACES_REGEX
    );

    if (apostropheSpaceVariationMatches != null) {
      tokenCount = tokenCount - apostropheSpaceVariationMatches.length;
    }

    return tokenCount;
  }

  async calculateMinimumFieldsToMatch(
    elasticService: ElasticSearchService,
    peopleIndex: string,
    queryText: string
  ): Promise<number> {
    if (!queryText) {
      return 0;
    }

    const adjustedTokenCount = await this.getAdjustedTokenCount(
      elasticService,
      peopleIndex,
      queryText
    );

    let minimumFieldsToMatch = adjustedTokenCount;

    if (adjustedTokenCount === 3) {
      minimumFieldsToMatch = 2;
    } else if (adjustedTokenCount > 3) {
      minimumFieldsToMatch = 3;
    }

    return minimumFieldsToMatch;
  }
}
