import {
  PatientDiversityStats,
  PatientDiversityStatsForHeatmap,
  PatientStatsRpcInput,
  PatientStatsServiceImplementation
} from "@h1nyc/search-sdk";
import { PatientStatsService } from "./PatientStatsService";
import { Service } from "typedi";

@Service()
export class PatientStatsGrpcHandlerService
  implements PatientStatsServiceImplementation
{
  constructor(private patientStatsService: PatientStatsService) {}

  heatmap(
    request: PatientStatsRpcInput
  ): Promise<PatientDiversityStatsForHeatmap> {
    return this.patientStatsService.heatmap(request);
  }

  stats(request: PatientStatsRpcInput): Promise<PatientDiversityStats> {
    return this.patientStatsService.stats(request);
  }
}
