import { estypes } from "@elastic/elasticsearch";
import {
  DiversityResource,
  ProviderExportDiversityInfo,
  RPC_NAMESPACE_DIVERSITY,
  PatientRace,
  PatientAge,
  PatientSex
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { Service } from "typedi";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";

type DiversityResponse = {
  id: string;
  patientsDiversity: PatientDiversity[];
  firstName_eng: string;
  lastName_eng: string;
  providerDiversity: ProviderDiversity[];
  patientsDiversityCount: number;
};

type PatientDiversity = {
  races_eng?: PatientRace[];
  age?: PatientAge[];
  sex?: PatientSex[];
};

type ProviderDiversity = {
  races_eng?: string[];
  sex?: string[];
  languagesSpoken?: string[];
};

@Service()
@RpcService()
export class DiversityResourceService
  extends RpcResourceService
  implements DiversityResource
{
  private readonly logger = createLogger(this);
  private peopleIndex: string;

  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_DIVERSITY,
      config.searchRedisOptions
    );

    this.peopleIndex = config.elasticPeopleIndex;
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.diversity")
  async getDiversity(
    personIds: string[]
  ): Promise<ProviderExportDiversityInfo[]> {
    const request: estypes.SearchRequest = {
      index: this.peopleIndex,
      _source_includes: [
        "id",
        "patientsDiversity",
        "providerDiversity",
        "firstName_eng",
        "lastName_eng",
        "patientsDiversityCount"
      ],
      query: {
        terms: {
          id: personIds
        }
      }
    };

    this.logger.debug(
      { data: JSON.stringify(request) },
      "Performing diversity search"
    );

    try {
      const data = await this.elasticService.query<DiversityResponse>(request);

      return data.hits.hits
        .filter((hit) => !!hit._source)
        .map(({ _source }) => {
          return {
            id: _source!.id,
            firstName: _source!.firstName_eng || "",
            lastName: _source!.lastName_eng || "",
            patientDiversity: {
              races: _source!.patientsDiversity?.[0]?.races_eng ?? [],
              age: _source!.patientsDiversity?.[0]?.age ?? [],
              sex: _source!.patientsDiversity?.[0]?.sex ?? []
            },
            providerDiversity: {
              races: _source!.providerDiversity?.[0]?.races_eng ?? [],
              sex: _source!.providerDiversity?.[0]?.sex ?? [],
              languagesSpoken:
                _source!.providerDiversity?.[0]?.languagesSpoken ?? []
            },
            patientsDiversityCount: _source!.patientsDiversityCount || 0
          };
        });
    } catch (error) {
      this.logger.error(
        { request, error },
        "Error getting diversity data",
        error
      );
    }

    return [];
  }
}
