import { Service } from "typedi";
import { ElasticCommonSearchService } from "./ElasticCommonSearchService";
import { ConfigService } from "./ConfigService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";

@Service()
export class ElasticTrialSearchServiceV2 extends ElasticCommonSearchService {
  constructor(config: ConfigService, featureFlagService: FeatureFlagsService) {
    super(
      config.elasticsearchClientOptions,
      config.elasticsearchClientOptionsV2,
      featureFlagService,
      config.elasticTrialsIndexV2
    );
  }
}
