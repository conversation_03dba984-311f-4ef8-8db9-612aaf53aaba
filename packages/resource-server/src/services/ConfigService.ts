import { Service } from "typedi";
import { Env } from "@h1nyc/systems-config";
import { RedisOptions } from "@h1nyc/systems-rpc";

import { COUNTRY_NAME_TO_CODE } from "../lib/data/countries";
import { REGION_CODES_BY_COUNTRY_CODE_AND_REGION_NAME } from "../lib/data/regions";
import { ClientOptions } from "@elastic/elasticsearch";

const ONE_HOUR = 60 * 60;

@Service()
export class ConfigService {
  @Env("HEALTH_PORT", false, 9000)
  healthPort: number;

  @Env("SEARCH_RPC_SIGNING_KEY", true)
  searchRpcSigningKey: string;

  @Env("SEARCH_RPC_REDIS_HOST", true)
  searchRedisHost: string;

  @Env("SEARCH_RPC_REDIS_PORT")
  searchRedisPort: number;

  @Env("SEARCH_RPC_REDIS_USERNAME")
  searchRedisUsername: string;

  @Env("SEARCH_RPC_REDIS_PASSWORD")
  searchRedisPassword: string;

  @Env("PIPELINE_RPC_SIGNING_KEY", true)
  pipelineRpcSigningKey: string;

  @Env("PIPELINE_RPC_REDIS_HOST", true)
  pipelineRedisHost: string;

  @Env("PIPELINE_RPC_REDIS_PORT")
  pipelineRedisPort: number;

  @Env("PIPELINE_RPC_REDIS_USERNAME")
  pipelineRedisUsername: string;

  @Env("PIPELINE_RPC_REDIS_PASSWORD")
  pipelineRedisPassword: string;

  @Env("ACCOUNT_RPC_SIGNING_KEY", true)
  accountRpcSigningKey: string;

  @Env("ACCOUNT_RPC_REDIS_HOST", true)
  accountRedisHost: string;

  @Env("ACCOUNT_RPC_REDIS_PORT")
  accountRedisPort: number;

  @Env("ACCOUNT_RPC_REDIS_USERNAME")
  accountRedisUsername: string;

  @Env("ACCOUNT_RPC_REDIS_PASSWORD")
  accountRedisPassword: string;

  // NOTE: k8s will resolve the service name to host IP address
  @Env("QUERY_UNDERSTANDING_SERVICE_HOST", true, "query-understanding-service")
  queryUnderstandingServiceHost: string;

  @Env("QUERY_UNDERSTANDING_SERVICE_SERVICE_PORT")
  queryUnderstandingServicePort: string;

  @Env("ELASTIC_HOST", true)
  elasticHost: string;

  @Env("ELASTIC_HOST_V2", false, "https://localhost")
  elasticHostV2: string;

  @Env("ELASTIC_REQUEST_TIMEOUT", false, 30000)
  elasticRequestTimeout: number;

  @Env("ELASTIC_PHRASE_SUGGESTER_REQUEST_TIMEOUT", false, 1000)
  elasticPhraseSuggesterRequestTimeout: number;

  @Env("ELASTIC_COMPLETION_SUGGESTER_REQUEST_TIMEOUT", false, 500)
  elasticCompletionSuggesterRequestTimeout: number;

  @Env("ELASTIC_MAX_RETRIES", false, 0)
  elasticMaxRetries: number;

  @Env("ELASTIC_PEOPLE_INDEX", true)
  elasticPeopleIndex: string;

  @Env("ELASTIC_PATIENT_LEVEL_INDEX", true)
  elasticPatientLevelIndex: string;

  @Env("ELASTIC_PATIENT_INDEX", true)
  elasticPatientIndex: string;

  @Env("ELASTIC_CLINICAL_TRIALS_HOST", true)
  elasticTrialsHost: string;

  @Env("ELASTIC_CLINICAL_TRIALS_INDEX", true)
  elasticTrialsIndex: string;

  @Env("ELASTIC_CLINICAL_TRIALS_INDEX_WITH_CTMS", true)
  elasticTrialsIndexWithCTMS: string;

  @Env("ELASTIC_CLINICAL_TRIALS_HOST_V2", true)
  elasticTrialsHostV2: string;

  @Env("ELASTIC_CLINICAL_TRIALS_INDEX_V2", true)
  elasticTrialsIndexV2: string;

  @Env("ELASTIC_CLINICAL_TRIALS_HOST_V3", true)
  elasticTrialsHostV3: string;

  @Env("ELASTIC_CLINICAL_TRIALS_INDEX_V3", true)
  elasticTrialsIndexV3: string;

  @Env("ELASTIC_CLINICAL_TRIALS_HOST_V4", true)
  elasticTrialsHostV4: string;

  @Env("ELASTIC_CLINICAL_TRIALS_INDEX_V4", true)
  elasticTrialsIndexV4: string;

  @Env("ELASTIC_INSTITUTIONS_INDEX", true)
  elasticInstitutionsIndex: string;

  @Env("ELASTIC_CONGRESS_INDEX", true)
  elasticCongressIndex: string;

  @Env("ELASTIC_CONGRESS_SESSIONS_INDEX", true)
  elasticCongressSessionsIndex: string;

  @Env("ELASTIC_SPELL_CHECK_INDEX", true)
  elasticSpellCheckIndex: string;

  @Env("ELASTIC_COMPLETION_INDEX", true)
  elasticCompletionSuggesterIndex: string;

  @Env("ELASTIC_INDICATIONS_INDEX", true)
  elasticIndicationsIndex: string;

  @Env("ELASTIC_DRUG_VALUE_INDEX", false, "drug_value")
  elasticDrugValueIndex: string;

  @Env("ELASTIC_GENERIC_DRUG_INDEX", false, "generic_drug")
  elasticGenericDrugIndex: string;

  @Env("SEARCH_SUPPORTED_LANGUAGES", false, "eng,cmn,jpn")
  searchSupportedLanguages: string;

  @Env("MESH_KEYWORD_EXCLUDE_WORDS", false)
  meshExcludeWords: string;

  //Env var instead of constant as product would like to be able to change this if necessary.
  @Env("MAX_QUERY_PARSER_DEPTH", false, 3)
  maxQueryParserDepth: number;

  @Env("MIN_MAX_RESULT_CACHE_EXPIRE_SECONDS", false, 30 * 60)
  minMaxResultCacheExpireSeconds: number;

  @Env("INITIAL_OPTIONS_CACHE_EXPIRE_SECONDS", false, ONE_HOUR)
  initialOptionsCacheExpireSeconds: number;

  @Env("LAUNCH_DARKLY_CLIENT_ID", false)
  launchDarklyClientID: string;

  @Env("ELASTIC_CTMS_FACILITY_AGGS_INDEX", true)
  ctmsFacilityAggregationIndex: string;

  @Env("ELASTIC_CTMS_INVESTIGATORS_AGGS_INDEX", true)
  ctmsInvestigatorAggregationIndex: string;

  @Env("ELASTIC_USERNAME")
  elasticUsername: string;

  @Env("ELASTIC_PASSWORD")
  elasticPassword: string;

  @Env("SEARCH_RESOURCE_SERVER_SERVICE_HOST")
  searchGrpcServerHost: string;

  @Env("SEARCH_RESOURCE_SERVER_SERVICE_PORT_GRPC")
  searchGrpcServerPort: string;

  @Env("PIPELINE_POSTGRES_HOST", true)
  pipelinePostgresHost: string;

  @Env("PIPELINE_POSTGRES_PORT", true)
  pipelinePostgresPort: number;

  @Env("PIPELINE_POSTGRES_DB", true)
  pipelinePostgresDb: string;

  @Env("PIPELINE_POSTGRES_USERNAME", true)
  pipelinePostgresUsername: string;

  @Env("PIPELINE_POSTGRES_PASSWORD", true)
  pipelinePostgresPassword: string;

  @Env("ORM_LOGGING")
  ormLogging: boolean;

  @Env("SEARCH_CACHE_REDIS_HOST", true)
  searchCacheRedisHost: string;

  @Env("SEARCH_CACHE_REDIS_PORT")
  searchCacheRedisPort: number;

  @Env("SEARCH_CACHE_REDIS_USERNAME")
  searchCacheRedisUsername: string;

  @Env("SEARCH_CACHE_REDIS_PASSWORD")
  searchCacheRedisPassword: string;

  @Env("NOTIFICATIONS_RPC_SIGNING_KEY", true)
  notificationsRpcSigningKey: string;

  @Env("NOTIFICATIONS_RPC_REDIS_HOST", true)
  notificationsRedisHost: string;

  @Env("NOTIFICATIONS_RPC_REDIS_PORT")
  notificationsRedisPort: number;

  @Env("NOTIFICATIONS_RPC_REDIS_USERNAME")
  notificationsRedisUsername: string;

  @Env("NOTIFICATIONS_RPC_REDIS_PASSWORD")
  notificationsRedisPassword: string;

  @Env("AZURE_OPEN_AI_API_KEY")
  azureOpenAiApiKey: string;

  @Env("AZURE_OPEN_AI_ENDPOINT")
  azureOpenAiEndpoint: string;

  @Env("AZURE_OPEN_AI_DEPLOYMENT")
  azureOpenAiDeploymentName: string;

  @Env("AZURE_OPEN_AI_API_VERSON")
  azureOpenAiApiVersion: string;

  get searchCacheRedisOptions(): RedisOptions {
    return {
      host: this.searchCacheRedisHost,
      port: this.searchCacheRedisPort,
      username: this.searchCacheRedisUsername,
      password: this.searchCacheRedisPassword
    };
  }

  get searchRedisOptions(): RedisOptions {
    return {
      host: this.searchRedisHost,
      port: this.searchRedisPort,
      username: this.searchRedisUsername,
      password: this.searchRedisPassword
    };
  }

  get pipelineRedisOptions(): RedisOptions {
    return {
      host: this.pipelineRedisHost,
      port: this.pipelineRedisPort,
      username: this.pipelineRedisUsername,
      password: this.pipelineRedisPassword
    };
  }

  get accountRedisOptions(): RedisOptions {
    return {
      host: this.accountRedisHost,
      port: this.accountRedisPort,
      username: this.accountRedisUsername,
      password: this.accountRedisPassword
    };
  }

  get notificationsRpcRedisOptions() {
    return {
      host: this.notificationsRedisHost,
      port: this.notificationsRedisPort,
      username: this.notificationsRedisUsername,
      password: this.notificationsRedisPassword
    };
  }

  regions(): Readonly<Record<string, Readonly<Record<string, string>>>> {
    return REGION_CODES_BY_COUNTRY_CODE_AND_REGION_NAME;
  }

  countries(): Readonly<Record<string, string>> {
    return COUNTRY_NAME_TO_CODE;
  }

  get elasticsearchClientOptions(): ClientOptions {
    return {
      node: this.elasticHost,
      requestTimeout: this.elasticRequestTimeout,
      maxRetries: this.elasticMaxRetries,
      compression: true,
      auth: {
        username: this.elasticUsername,
        password: this.elasticPassword
      }
    };
  }

  get elasticsearchClientOptionsV2(): ClientOptions {
    return {
      node: this.elasticHostV2,
      requestTimeout: this.elasticRequestTimeout,
      maxRetries: this.elasticMaxRetries,
      compression: true,
      auth: {
        username: this.elasticUsername,
        password: this.elasticPassword
      }
    };
  }
}
