/* eslint-disable  @typescript-eslint/no-non-null-assertion */
import {
  createMockInstance,
  generateInstitutionSearchFeatureFlags,
  generateMockElasticsearchResponse
} from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { faker } from "@faker-js/faker";
import { ElasticSearchInstitutionsService } from "./ElasticSearchInstitutionsService";
import {
  INSTITUTION_ACCESS_LEVEL,
  InstitutionTagResourceClient
} from "@h1nyc/account-sdk";
import { InstitutionNameSearchQueryBuilder } from "./queryBuilders/InstitutionNameSearchQueryBuilder";
import { InstitutionNameSearchResourceService } from "./InstitutionNameSearchResourceService";
import { RulesParserService } from "./RulesParserService";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { QueryIntent } from "../proto/query_intent_pb";
import { Synonym } from "../proto/synonym_pb";
import {
  CountResponse,
  QueryDslQueryContainer,
  ShardStatistics
} from "@elastic/elasticsearch/lib/api/types";
import { InstitutionsSearchInput } from "@h1nyc/search-sdk";

const INSTITUTION_IS_IOL = {
  term: {
    isIol: true
  }
};

const IS_IOL_DOC = {
  term: {
    join_field: "iol"
  }
};

const configService = createMockInstance(ConfigService);
configService.elasticInstitutionsIndex = faker.datatype.string();

const rulesParserService = createMockInstance(RulesParserService);
const elasticSearchService = createMockInstance(
  ElasticSearchInstitutionsService
);
const institutionTagResourceClient = createMockInstance(
  InstitutionTagResourceClient
);
const institutionNameSearchQueryBuilder = createMockInstance(
  InstitutionNameSearchQueryBuilder
);

function mockQueryUnderstandingServerResponse(
  augmentedQuery: string,
  isInstitutionQuery: boolean,
  score = 1.0,
  query?: string,
  synonyms?: string[]
): QueryUnderstandingServiceResponse {
  const response = new QueryUnderstandingServiceResponse();
  response.setAugmentedQuery(augmentedQuery);
  if (isInstitutionQuery) {
    const intent = new QueryIntent.Intent();
    intent.setIntentType(QueryIntent.IntentType.INSTITUTION);
    intent.setScore(score);
    const queryIntent = new QueryIntent();
    queryIntent.setInstitutionIntent(intent);
    response.setQueryIntent(queryIntent);
  }

  if (synonyms && query) {
    // phrasal synonyms are synonyms of the whole query
    const phrasalSynonyms = new Synonym();
    phrasalSynonyms.setOrig(query);
    for (const synonym of synonyms) {
      const synonymAndScore = new Synonym.SynScore();
      synonymAndScore.setScore(1.0);
      synonymAndScore.setSyn(synonym);
      phrasalSynonyms.addContextFreeSyn(synonymAndScore);
    }
    response.setUnigramSynonymList([phrasalSynonyms]);
  }
  return response;
}

function mockInstitutionNameSearchQueryBuilderResponse(
  query: string
): QueryDslQueryContainer {
  return {
    function_score: {
      query: {
        dis_max: {
          queries: [
            {
              match: {
                name: {
                  query,
                  operator: "AND",
                  fuzziness: "AUTO:4,7",
                  analyzer: "name_analyzer",
                  prefix_length: 1
                }
              }
            },
            {
              match: {
                name: {
                  query,
                  operator: "AND",
                  fuzziness: "AUTO:4,7",
                  analyzer: "institute_name_analyzer",
                  prefix_length: 1
                }
              }
            },
            {
              match: {
                "name.phonetic": {
                  analyzer: "name_analyzer",
                  fuzziness: "AUTO:4,7",
                  operator: "AND",
                  prefix_length: 1,
                  query
                }
              }
            },
            {
              match: {
                "name.phonetic": {
                  analyzer: "institute_name_analyzer",
                  fuzziness: "AUTO:4,7",
                  operator: "AND",
                  prefix_length: 1,
                  query
                }
              }
            }
          ]
        }
      },
      boost_mode: "sum",
      score_mode: "sum",
      functions: [
        {
          filter: {
            dis_max: {
              queries: [
                {
                  match: {
                    name: {
                      analyzer: "name_analyzer",
                      operator: "AND",
                      query
                    }
                  }
                },
                {
                  match: {
                    name: {
                      analyzer: "institute_name_analyzer",
                      operator: "AND",
                      query
                    }
                  }
                },
                {
                  match: {
                    "name.phonetic": {
                      analyzer: "name_analyzer",
                      operator: "AND",
                      query
                    }
                  }
                },
                {
                  match: {
                    "name.phonetic": {
                      analyzer: "institute_name_analyzer",
                      operator: "AND",
                      query
                    }
                  }
                }
              ]
            }
          },
          weight: 100
        }
      ]
    }
  };
}

function buildProjectIdFilter(projectId: string) {
  return {
    bool: {
      should: [
        {
          bool: {
            must: [
              {
                exists: {
                  field: "projectIds"
                }
              },
              {
                terms: {
                  projectIds: [projectId]
                }
              }
            ]
          }
        },
        {
          bool: {
            must_not: [
              {
                exists: {
                  field: "projectIds"
                }
              }
            ]
          }
        }
      ]
    }
  };
}

beforeEach(() => {
  jest.resetAllMocks();
});

describe("InstitutionNameSearchResourceService", () => {
  describe("query building", () => {
    describe("input query", () => {
      it("should return empty response if input.query is empty", async () => {
        const input: InstitutionsSearchInput = {
          query: "",
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.ALL
        };

        const institutionNameSearchResourceService =
          new InstitutionNameSearchResourceService(
            configService,
            rulesParserService,
            elasticSearchService,
            institutionTagResourceClient,
            institutionNameSearchQueryBuilder
          );

        const featureFlags = generateInstitutionSearchFeatureFlags();
        const extraFilters: QueryDslQueryContainer[] = [];
        const queryUnderstandingResponse = mockQueryUnderstandingServerResponse(
          input.query!,
          true
        );

        expect(
          await institutionNameSearchResourceService.searchInstitutionsByName(
            input,
            featureFlags,
            false,
            "iol",
            extraFilters,
            [],
            queryUnderstandingResponse
          )
        ).toEqual({
          took: 0,
          timed_out: false,
          _shards: {
            failed: 0,
            successful: 0,
            total: 0
          },
          hits: {
            total: 0,
            hits: []
          }
        });

        expect(
          await institutionNameSearchResourceService.searchInstitutionsByName(
            {
              ...input,
              query: undefined
            },
            featureFlags,
            false,
            "iol",
            extraFilters,
            [],
            queryUnderstandingResponse
          )
        ).toEqual({
          took: 0,
          timed_out: false,
          _shards: {
            failed: 0,
            successful: 0,
            total: 0
          },
          hits: {
            total: 0,
            hits: []
          }
        });
      });

      it("should call query builder using input.query when defined", async () => {
        const input: InstitutionsSearchInput = {
          query: faker.datatype.string(),
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.ALL
        };

        const institutionNameSearchResourceService =
          new InstitutionNameSearchResourceService(
            configService,
            rulesParserService,
            elasticSearchService,
            institutionTagResourceClient,
            institutionNameSearchQueryBuilder
          );

        const featureFlags = generateInstitutionSearchFeatureFlags();
        const extraFilters: QueryDslQueryContainer[] = [];
        const queryUnderstandingResponse = mockQueryUnderstandingServerResponse(
          input.query!,
          true
        );

        institutionNameSearchQueryBuilder.buildNameSearchQuery.mockReturnValue(
          mockInstitutionNameSearchQueryBuilderResponse(input.query!)
        );

        await institutionNameSearchResourceService.searchInstitutionsByName(
          input,
          featureFlags,
          false,
          "iol",
          extraFilters,
          [],
          queryUnderstandingResponse
        );

        const query = input.query!;

        expect(
          institutionNameSearchQueryBuilder.buildNameSearchQuery
        ).toHaveBeenCalledWith(input.query!);
        expect(elasticSearchService.query).toHaveBeenCalledWith(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            track_total_hits: true,
            _source: expect.any(Object),
            query: {
              function_score: expect.objectContaining({
                query: expect.objectContaining({
                  bool: {
                    must: [
                      {
                        dis_max: {
                          queries: [
                            {
                              match: {
                                name: {
                                  query,
                                  operator: "AND",
                                  fuzziness: "AUTO:4,7",
                                  analyzer: "name_analyzer",
                                  prefix_length: 1
                                }
                              }
                            },
                            {
                              match: {
                                name: {
                                  query,
                                  operator: "AND",
                                  fuzziness: "AUTO:4,7",
                                  analyzer: "institute_name_analyzer",
                                  prefix_length: 1
                                }
                              }
                            },
                            {
                              match: {
                                "name.phonetic": {
                                  analyzer: "name_analyzer",
                                  fuzziness: "AUTO:4,7",
                                  operator: "AND",
                                  prefix_length: 1,
                                  query
                                }
                              }
                            },
                            {
                              match: {
                                "name.phonetic": {
                                  analyzer: "institute_name_analyzer",
                                  fuzziness: "AUTO:4,7",
                                  operator: "AND",
                                  prefix_length: 1,
                                  query
                                }
                              }
                            }
                          ]
                        }
                      }
                    ],
                    filter: [
                      INSTITUTION_IS_IOL,
                      buildProjectIdFilter(input.projectId),
                      IS_IOL_DOC
                    ]
                  }
                }),
                functions: [
                  {
                    filter: {
                      dis_max: {
                        queries: [
                          {
                            match: {
                              name: {
                                analyzer: "name_analyzer",
                                operator: "AND",
                                query
                              }
                            }
                          },
                          {
                            match: {
                              name: {
                                analyzer: "institute_name_analyzer",
                                operator: "AND",
                                query
                              }
                            }
                          },
                          {
                            match: {
                              "name.phonetic": {
                                analyzer: "name_analyzer",
                                operator: "AND",
                                query
                              }
                            }
                          },
                          {
                            match: {
                              "name.phonetic": {
                                analyzer: "institute_name_analyzer",
                                operator: "AND",
                                query
                              }
                            }
                          }
                        ]
                      }
                    },
                    weight: 100
                  }
                ]
              })
            },
            aggs: {}
          })
        );
      });

      it("should perform a count query when RequestType is count", async () => {
        const input: InstitutionsSearchInput = {
          query: faker.datatype.string(),
          projectId: faker.datatype.string(),
          accessLevel: INSTITUTION_ACCESS_LEVEL.ALL
        };

        const elasticSearchService = createMockInstance(
          ElasticSearchInstitutionsService
        );

        const countResponse: CountResponse = {
          _shards: {} as ShardStatistics,
          count: faker.datatype.number()
        };
        elasticSearchService.count.mockResolvedValue(countResponse);

        const institutionNameSearchResourceService =
          new InstitutionNameSearchResourceService(
            configService,
            rulesParserService,
            elasticSearchService,
            institutionTagResourceClient,
            institutionNameSearchQueryBuilder
          );

        const featureFlags = generateInstitutionSearchFeatureFlags();
        const extraFilters: QueryDslQueryContainer[] = [];
        const queryUnderstandingResponse = mockQueryUnderstandingServerResponse(
          input.query!,
          true
        );

        institutionNameSearchQueryBuilder.buildNameSearchQuery.mockReturnValue(
          mockInstitutionNameSearchQueryBuilderResponse(input.query!)
        );
        rulesParserService.parseRulesToEsQueries.mockReturnValue([]);

        expect(
          await institutionNameSearchResourceService.searchInstitutionsByName(
            input,
            featureFlags,
            false,
            "count",
            extraFilters,
            [],
            queryUnderstandingResponse
          )
        ).toEqual(countResponse.count);

        const query = input.query!;

        expect(
          institutionNameSearchQueryBuilder.buildNameSearchQuery
        ).toHaveBeenCalledWith(input.query!);
        expect(elasticSearchService.count).toHaveBeenCalledWith(
          expect.objectContaining({
            index: configService.elasticInstitutionsIndex,
            query: {
              bool: {
                filter: [
                  {
                    has_parent: {
                      parent_type: "iol",
                      query: {
                        bool: {
                          filter: {
                            function_score: {
                              boost_mode: "replace",
                              query: expect.objectContaining({
                                bool: {
                                  must: [
                                    {
                                      dis_max: {
                                        queries: [
                                          {
                                            match: {
                                              name: {
                                                query,
                                                operator: "AND",
                                                fuzziness: "AUTO:4,7",
                                                analyzer: "name_analyzer",
                                                prefix_length: 1
                                              }
                                            }
                                          },
                                          {
                                            match: {
                                              name: {
                                                query,
                                                operator: "AND",
                                                fuzziness: "AUTO:4,7",
                                                analyzer:
                                                  "institute_name_analyzer",
                                                prefix_length: 1
                                              }
                                            }
                                          },
                                          {
                                            match: {
                                              "name.phonetic": {
                                                analyzer: "name_analyzer",
                                                fuzziness: "AUTO:4,7",
                                                operator: "AND",
                                                prefix_length: 1,
                                                query
                                              }
                                            }
                                          },
                                          {
                                            match: {
                                              "name.phonetic": {
                                                analyzer:
                                                  "institute_name_analyzer",
                                                fuzziness: "AUTO:4,7",
                                                operator: "AND",
                                                prefix_length: 1,
                                                query
                                              }
                                            }
                                          }
                                        ]
                                      }
                                    }
                                  ],
                                  filter: [
                                    INSTITUTION_IS_IOL,
                                    buildProjectIdFilter(input.projectId),
                                    IS_IOL_DOC
                                  ]
                                }
                              }),
                              functions: [
                                {
                                  filter: {
                                    dis_max: {
                                      queries: [
                                        {
                                          match: {
                                            name: {
                                              analyzer: "name_analyzer",
                                              operator: "AND",
                                              query
                                            }
                                          }
                                        },
                                        {
                                          match: {
                                            name: {
                                              analyzer:
                                                "institute_name_analyzer",
                                              operator: "AND",
                                              query
                                            }
                                          }
                                        },
                                        {
                                          match: {
                                            "name.phonetic": {
                                              analyzer: "name_analyzer",
                                              operator: "AND",
                                              query
                                            }
                                          }
                                        },
                                        {
                                          match: {
                                            "name.phonetic": {
                                              analyzer:
                                                "institute_name_analyzer",
                                              operator: "AND",
                                              query
                                            }
                                          }
                                        }
                                      ]
                                    }
                                  },
                                  weight: 100
                                }
                              ],
                              score_mode: "sum"
                            }
                          }
                        }
                      }
                    }
                  }
                ]
              }
            }
          })
        );
      });
    });

    it("should not add name search query to request when query builder returns null/undefined", async () => {
      const input: InstitutionsSearchInput = {
        query: faker.datatype.string(),
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.ALL
      };

      const elasticSearchService = createMockInstance(
        ElasticSearchInstitutionsService
      );

      const institutionNameSearchResourceService =
        new InstitutionNameSearchResourceService(
          configService,
          rulesParserService,
          elasticSearchService,
          institutionTagResourceClient,
          institutionNameSearchQueryBuilder
        );

      const featureFlags = generateInstitutionSearchFeatureFlags();
      const extraFilters: QueryDslQueryContainer[] = [];
      const queryUnderstandingResponse = mockQueryUnderstandingServerResponse(
        input.query!,
        true
      );

      institutionNameSearchQueryBuilder.buildNameSearchQuery.mockReturnValue(
        null
      );

      await institutionNameSearchResourceService.searchInstitutionsByName(
        input,
        featureFlags,
        false,
        "iol",
        extraFilters,
        [],
        queryUnderstandingResponse
      );

      expect(elasticSearchService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          track_total_hits: true,
          _source: expect.any(Object),
          query: {
            function_score: expect.objectContaining({
              query: expect.objectContaining({
                bool: {
                  filter: [
                    INSTITUTION_IS_IOL,
                    buildProjectIdFilter(input.projectId),
                    IS_IOL_DOC
                  ]
                }
              }),
              functions: []
            })
          },
          aggs: {}
        })
      );

      institutionNameSearchQueryBuilder.buildNameSearchQuery.mockReturnValue(
        undefined as any
      );

      await institutionNameSearchResourceService.searchInstitutionsByName(
        input,
        featureFlags,
        false,
        "iol",
        extraFilters,
        [],
        queryUnderstandingResponse
      );

      expect(elasticSearchService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          track_total_hits: true,
          _source: expect.any(Object),
          query: {
            function_score: expect.objectContaining({
              query: expect.objectContaining({
                bool: {
                  filter: [
                    INSTITUTION_IS_IOL,
                    buildProjectIdFilter(input.projectId),
                    IS_IOL_DOC
                  ]
                }
              }),
              functions: []
            })
          },
          aggs: {}
        })
      );
    });

    it("should not use indications in query clause when supplied with query that is an institution", async () => {
      const input: InstitutionsSearchInput = {
        query: faker.datatype.string(),
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
        filters: {
          indications: [faker.datatype.string(), faker.datatype.string()]
        }
      };

      const elasticSearchService = createMockInstance(
        ElasticSearchInstitutionsService
      );
      elasticSearchService.mget.mockResolvedValue({ docs: [] });
      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponse()
      );

      rulesParserService.parseRulesToEsQueries.mockReturnValue([]);

      institutionNameSearchQueryBuilder.buildNameSearchQuery.mockReturnValue(
        mockInstitutionNameSearchQueryBuilderResponse(input.query!)
      );

      const institutionNameSearchResourceService =
        new InstitutionNameSearchResourceService(
          configService,
          rulesParserService,
          elasticSearchService,
          institutionTagResourceClient,
          institutionNameSearchQueryBuilder
        );

      const featureFlags = generateInstitutionSearchFeatureFlags();
      const extraFilters: QueryDslQueryContainer[] = [];
      const queryUnderstandingResponse = mockQueryUnderstandingServerResponse(
        input.query!,
        true
      );
      await institutionNameSearchResourceService.searchInstitutionsByName(
        input,
        featureFlags,
        false,
        "iol",
        extraFilters,
        [],
        queryUnderstandingResponse
      );

      expect(elasticSearchService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          track_total_hits: true,
          _source: expect.any(Object),
          query: {
            function_score: expect.objectContaining({
              query: expect.objectContaining({
                bool: {
                  must: expect.arrayContaining([
                    {
                      dis_max: expect.objectContaining({
                        queries: expect.arrayContaining([
                          {
                            match: expect.objectContaining({
                              name: expect.objectContaining({
                                query: input.query,
                                analyzer: "institute_name_analyzer"
                              })
                            })
                          },
                          {
                            match: expect.objectContaining({
                              name: expect.objectContaining({
                                query: input.query,
                                analyzer: "name_analyzer"
                              })
                            })
                          }
                        ])
                      })
                    }
                  ]),
                  filter: [
                    INSTITUTION_IS_IOL,
                    buildProjectIdFilter(input.projectId),
                    IS_IOL_DOC
                  ]
                }
              })
            })
          },
          aggs: {}
        })
      );
    });

    it("should not include should clause for CTMSTrials for navigational queries", async () => {
      const input: InstitutionsSearchInput = {
        query: faker.datatype.string(),
        projectId: faker.datatype.string(),
        accessLevel: INSTITUTION_ACCESS_LEVEL.ALL,
        filters: {
          hasCTMSData: true
        }
      };

      const elasticSearchService = createMockInstance(
        ElasticSearchInstitutionsService
      );

      elasticSearchService.mget.mockResolvedValue({ docs: [] });
      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponse()
      );

      rulesParserService.parseRulesToEsQueries.mockReturnValue([]);

      institutionNameSearchQueryBuilder.buildNameSearchQuery.mockReturnValue(
        mockInstitutionNameSearchQueryBuilderResponse(input.query!)
      );

      const institutionNameSearchResourceService =
        new InstitutionNameSearchResourceService(
          configService,
          rulesParserService,
          elasticSearchService,
          institutionTagResourceClient,
          institutionNameSearchQueryBuilder
        );

      const featureFlags = generateInstitutionSearchFeatureFlags({
        enableCTMSV2: true,
        enableEnhancedHasCTMSDataFilterBehavior: faker.datatype.boolean()
      });
      const extraFilters: QueryDslQueryContainer[] = [];
      const queryUnderstandingResponse = mockQueryUnderstandingServerResponse(
        input.query!,
        true
      );
      await institutionNameSearchResourceService.searchInstitutionsByName(
        input,
        featureFlags,
        false,
        "iol",
        extraFilters,
        [],
        queryUnderstandingResponse
      );

      const query = input.query!;

      expect(elasticSearchService.query).toHaveBeenCalledWith(
        expect.objectContaining({
          index: configService.elasticInstitutionsIndex,
          _source: expect.any(Object),
          query: {
            function_score: {
              boost_mode: "replace",
              score_mode: "sum",
              query: {
                bool: {
                  filter: [
                    INSTITUTION_IS_IOL,
                    buildProjectIdFilter(input.projectId),
                    IS_IOL_DOC,
                    expect.termQuery("inCtmsNetwork", true)
                  ],
                  must: expect.any(Array)
                }
              },
              functions: [
                {
                  filter: {
                    dis_max: {
                      queries: [
                        {
                          match: {
                            name: {
                              analyzer: "name_analyzer",
                              operator: "AND",
                              query
                            }
                          }
                        },
                        {
                          match: {
                            name: {
                              analyzer: "institute_name_analyzer",
                              operator: "AND",
                              query
                            }
                          }
                        },
                        {
                          match: {
                            "name.phonetic": {
                              analyzer: "name_analyzer",
                              operator: "AND",
                              query
                            }
                          }
                        },
                        {
                          match: {
                            "name.phonetic": {
                              analyzer: "institute_name_analyzer",
                              operator: "AND",
                              query
                            }
                          }
                        }
                      ]
                    }
                  },
                  weight: 100
                }
              ]
            }
          },
          aggs: {}
        })
      );
    });
  });
});
