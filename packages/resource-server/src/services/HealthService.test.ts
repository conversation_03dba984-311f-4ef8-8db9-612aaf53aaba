import { HealthService } from "./HealthService";
import { ElasticSearchService } from "./ElasticSearchService";
import { ElasticTrialSearchService } from "./ElasticTrialSearchService";
import { createMockInstance } from "../util/TestUtils";

describe("HealthService", () => {
  it("should not throw", async () => {
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );

    const healthService = new HealthService(
      elasticSearchService,
      elasticTrialSearchService
    );

    expect(await healthService.check()).toBeUndefined();
  });

  it("should throw an error when elastic search fails to connect", async () => {
    const elasticSearchService = createMockInstance(ElasticSearchService);
    elasticSearchService.ping.mockRejectedValue(new Error());
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );

    const healthService = new HealthService(
      elasticSearchService,
      elasticTrialSearchService
    );

    expect(healthService.check()).rejects.toThrow();
  });

  it("should throw an error when elastic trial search fails to connect", async () => {
    const elasticSearchService = createMockInstance(ElasticSearchService);
    const elasticTrialSearchService = createMockInstance(
      ElasticTrialSearchService
    );
    elasticTrialSearchService.ping.mockRejectedValue(new Error());

    const healthService = new HealthService(
      elasticSearchService,
      elasticTrialSearchService
    );

    expect(healthService.check()).rejects.toThrow();
  });
});
