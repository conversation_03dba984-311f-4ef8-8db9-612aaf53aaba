import { errors, Client, ClientOptions, estypes } from "@elastic/elasticsearch";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { SearchRequest } from "@elastic/elasticsearch/lib/api/types";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";

type ElasticSearchType = "dfs_query_then_fetch" | "query_then_fetch";

interface ElasticSearchOptions {
  searchType?: ElasticSearchType;
}

export abstract class ElasticCommonSearchService {
  private readonly logger = createLogger(this);
  readonly client: Client;
  readonly clientV2: Client;
  readonly index: string;

  constructor(
    clientOptions: ClientOptions,
    clientOptionsV2: ClientOptions,
    private featureFlagService: FeatureFlagsService,
    index: string
  ) {
    this.index = index;
    this.client = new Client(clientOptions);
    this.clientV2 = new Client(clientOptionsV2);
  }

  private async getClient() {
    const flag = await this.getFeatureFlag();

    if (flag) {
      return this.clientV2;
    }

    return this.client;
  }

  private async getFeatureFlag(): Promise<boolean> {
    try {
      return this.featureFlagService.getFlag(
        "use-self-hosted-elasticsearch",
        false
      );
    } catch (e) {
      this.logger.warn(
        `Failed to call FeatureFlagsService. Defaulting to flag false. ${JSON.stringify(
          e
        )}`
      );
      return false;
    }
  }

  async ping() {
    const client = await this.getClient();
    return client.ping();
  }

  /**
   * @deprecated Use one of the other functions in the file, they use the official elasticsearch types
   */
  @Trace("h1-search.ElasticCommonSearchService.getSignedElasticRequest")
  async getSignedElasticRequest<T>(
    requestBody: SearchRequest,
    index?: string,
    opts?: ElasticSearchOptions
  ): Promise<T> {
    const client = await this.getClient();
    const params: estypes.SearchRequest = {
      index: index || this.index,
      ...requestBody,
      search_type: opts?.searchType
    };

    return client.search(params) as unknown as T;
  }

  @Trace("h1-search.ElasticCommonSearchService.query")
  async query<T, A = Record<string, estypes.AggregationsAggregate>>(
    req: estypes.SearchRequest
  ): Promise<estypes.SearchResponse<T, A>> {
    const client = await this.getClient();
    try {
      const result = await client.search<T, A>(req);
      return result;
    } catch (error) {
      this.logger.error({ error }, "query encountered error");
      throw error as errors.ResponseError;
    }
  }

  @Trace("h1-search.ElasticCommonSearchService.mget")
  async mget<T>(req: estypes.MgetRequest): Promise<estypes.MgetResponse<T>> {
    const client = await this.getClient();
    return await client.mget<T>(req);
  }

  @Trace("h1-search.ElasticCommonSearchService.msearch")
  async msearch<T>(
    req: Readonly<estypes.MsearchRequest>
  ): Promise<estypes.MsearchResponse<T>> {
    const client = await this.getClient();
    return await client.msearch<T>(req);
  }

  @Trace("h1-search.ElasticCommonSearchService.count")
  async count(req: estypes.CountRequest): Promise<estypes.CountResponse> {
    const client = await this.getClient();
    return await client.count(req);
  }

  @Trace("h1-search.ElasticCommonSearchService.analyzeIndices")
  async analyzeIndices(req: estypes.IndicesAnalyzeRequest) {
    const client = await this.getClient();
    return await client.indices.analyze(req);
  }
}
