import {
  ClaimsFilterRange,
  DiagnosesCcsrSortFields,
  DiagnosesSortFields,
  ProcedureSortFields,
  ProceduresCcsrSortFields,
  SortDirection
} from "@h1nyc/search-sdk";

import { ClaimsResourceService } from "./ClaimsResourceService";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  buildDiagnosesCcsrQuery,
  buildDiagnosesQuery,
  buildProceduresCcsrQuery,
  buildProceduresQuery,
  getDiagnosesCcsrRangeAdjustedFieldsForUniquePatientCount,
  getDiagnosesRangeAdjustedFields,
  getDiagnosesRangeAdjustedFieldsForUniquePatientCount,
  getProceduresCcsrRangeAdjustedFieldsForUniquePatientCount,
  getProceduresRangeAdjustedFields
} from "../lib/ClaimsQueryBuilder";

import ClaimsDiagnosesResponse from "./__fixtures__/ClaimsResourceService/ClaimsDiagnosesResponse.json";
import ClaimsProceduresResponse from "./__fixtures__/ClaimsResourceService/ClaimsProceduresResponse.json";
import ClaimsTotalResponse from "./__fixtures__/ClaimsResourceService/ClaimsTotalResponse.json";
import ClaimsDiagnosesResponseFiltered from "./__fixtures__/ClaimsResourceService/ClaimsDiagnosesResponseFiltered.json";
import ClaimsCcsrDiagnosesResponseFiltered from "./__fixtures__/ClaimsResourceService/ClaimsCcsrDiagnosesResponseFiltered.json";
import ClaimsPrescriptionsResponseFiltered from "./__fixtures__/ClaimsResourceService/ClaimsPrescriptionsResponseFiltered.json";
import ClaimsDiagnosesResponseFilteredForUniquePatientCount from "./__fixtures__/ClaimsResourceService/ClaimsDiagnosesResponseFilteredForUniquePatientCount.json";
import ClaimsProceduresResponseFiltered from "./__fixtures__/ClaimsResourceService/ClaimsProceduresResponseFiltered.json";
import ClaimsProceduresResponseFilteredForUniquePatientCount from "./__fixtures__/ClaimsResourceService/ClaimsProceduresResponseFilteredForUniquePatient.json";
import ClaimsTotalResponseFiltered from "./__fixtures__/ClaimsResourceService/ClaimsTotalResponseFiltered.json";
import ClaimsUniquePatientCountTotalResponse from "./__fixtures__/ClaimsResourceService/ClaimsUniquePatientCountTotalResponse.json";
import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { faker } from "@faker-js/faker";
import { CcsrIcdMappingRepository } from "@h1nyc/pipeline-repositories";
import * as ClaimsQueryBuilder from "../lib/ClaimsQueryBuilder";

const emptyClaimsResp = { from: 0, pageSize: 0, results: [], total: 0 };
const emptyEsResp = {
  hits: {
    total: {
      value: 0,
      relation: "eq"
    },
    max_score: null,
    hits: []
  }
};

describe("getDiagnoses()", () => {
  it("returns empty array when no diagnoses are returned", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(emptyEsResp);

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const resp = await claimsResourceService.getDiagnoses("asdfssss");

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(emptyClaimsResp);
  });

  it("returns the paged diagnoses for the person", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponse
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const resp = await claimsResourceService.getDiagnoses(
      personId,
      terms,
      paging
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(
      expect.objectContaining({
        from: 0,
        pageSize: 5,
        total: 843,
        results: [
          {
            description: "Other long term (current) drug therapy",
            diagnosisCode: "Z79899",
            percentOfClaims: 7.88,
            percentage: 7.879640653750405,
            count: "728",
            internalCount: 728,
            codeScheme: "ICD10"
          },
          {
            description:
              "Acute myeloblastic leukemia, not having achieved remission",
            diagnosisCode: "C9200",
            percentOfClaims: 5.57,
            percentage: 5.574196341595411,
            count: "515",
            internalCount: 515,
            codeScheme: "ICD10"
          },
          {
            description:
              "Chronic myeloid leukemia, BCR/ABL-positive, not having achieved remission",
            diagnosisCode: "C9210",
            percentOfClaims: 3.11,
            percentage: 3.106396796190064,
            count: "287",
            internalCount: 287,
            codeScheme: "ICD10"
          },
          {
            description: "Encounter for antineoplastic chemotherapy",
            diagnosisCode: "Z5111",
            percentOfClaims: 2.79,
            percentage: 2.7925100119060504,
            count: "258",
            internalCount: 258,
            codeScheme: "ICD10"
          },
          {
            description:
              "Chronic myeloid leukemia, BCR/ABL-positive, in remission",
            diagnosisCode: "C9211",
            percentOfClaims: 2.5,
            percentage: 2.500270592055417,
            count: "231",
            internalCount: 231,
            codeScheme: "ICD10"
          }
        ]
      })
    );
  });
});

describe("getFilteredDiagnoses()", () => {
  it("returns empty array when no diagnoses are returned", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(emptyEsResp);

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const resp = await claimsResourceService.getFilteredDiagnoses("asdfssss", {
      filterRange: ClaimsFilterRange.twoYear
    });

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(emptyClaimsResp);
  });

  it("returns the paged diagnoses for the person", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponseFiltered
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const resp = await claimsResourceService.getFilteredDiagnoses(
      personId,
      {
        filterRange: ClaimsFilterRange.twoYear
      },
      terms,
      paging
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(
      expect.objectContaining({
        from: 0,
        pageSize: 5,
        total: 843,
        results: [
          {
            description: "Other long term (current) drug therapy",
            diagnosisCode: "Z79899",
            percentOfClaims: 7.88,
            percentage: 7.879640653750405,
            count: "728",
            internalCount: 728,
            codeScheme: "ICD10"
          },
          {
            description:
              "Acute myeloblastic leukemia, not having achieved remission",
            diagnosisCode: "C9200",
            percentOfClaims: 5.57,
            percentage: 5.574196341595411,
            count: "515",
            internalCount: 515,
            codeScheme: "ICD10"
          },
          {
            description:
              "Chronic myeloid leukemia, BCR/ABL-positive, not having achieved remission",
            diagnosisCode: "C9210",
            percentOfClaims: 3.11,
            percentage: 3.106396796190064,
            count: "287",
            internalCount: 287,
            codeScheme: "ICD10"
          },
          {
            description: "Encounter for antineoplastic chemotherapy",
            diagnosisCode: "Z5111",
            percentOfClaims: 2.79,
            percentage: 2.7925100119060504,
            count: "258",
            internalCount: 258,
            codeScheme: "ICD10"
          },
          {
            description:
              "Chronic myeloid leukemia, BCR/ABL-positive, in remission",
            diagnosisCode: "C9211",
            percentOfClaims: 2.5,
            percentage: 2.500270592055417,
            count: "231",
            internalCount: 231,
            codeScheme: "ICD10"
          }
        ]
      })
    );
  });

  it("should pass correct size and offset when ccsr codes are provided", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponseFilteredForUniquePatientCount
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const mockedIcdCodes = [
      {
        icdCode: "C9201",
        ccsr: "Chronic myeloid leukemia",
        claimSize: 100
      },
      {
        icdCode: "C9200",
        ccsr: "Chronic myeloid leukemia",
        claimSize: 10
      }
    ];
    ccsrIcdMappingRepository.getIcdCodesForCcsr.mockResolvedValue(
      mockedIcdCodes
    );

    const buildDiagnosesQuerySpy = jest.spyOn(
      ClaimsQueryBuilder,
      "buildDiagnosesQuery"
    );

    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );
    const ccsrToExpand = "Chronic myeloid leukemia";
    await claimsResourceService.getFilteredDiagnoses(
      "1199780",
      {
        filterRange: ClaimsFilterRange.twoYear
      },
      [],
      {
        limit: 10,
        offset: 10
      },
      undefined,
      {
        shouldUseUniquePatientCount: true
      },
      ccsrToExpand
    );
    expect(buildDiagnosesQuerySpy).toHaveBeenCalledWith(
      "1199780",
      expect.any(Object),
      [],
      10000,
      0,
      undefined,
      undefined,
      ["C9201", "C9200"]
    );

    buildDiagnosesQuerySpy.mockRestore();
  });
});

describe("getFilteredPrescriptions()", () => {
  it("returns empty array when no prescriptions are returned", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(emptyEsResp);

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const resp = await claimsResourceService.getFilteredPrescriptions(
      "asdfssss",
      {
        filterRange: ClaimsFilterRange.twoYear
      }
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(emptyClaimsResp);
  });

  it("returns the paged prescriptions for the person", async () => {
    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = "people";
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsPrescriptionsResponseFiltered
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = "1199780";
    const terms: string[] = [faker.datatype.string()];
    const paging = {
      limit: 5,
      offset: 0
    };

    const resp = await claimsResourceService.getFilteredPrescriptions(
      personId,
      {
        filterRange: ClaimsFilterRange.twoYear
      },
      terms,
      paging
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalledWith(
      {
        _source: "prescriptions_patient_count_2_year",
        size: 1,
        query: {
          bool: {
            must: [
              {
                nested: {
                  path: "prescriptions",
                  query: {
                    bool: {
                      must: [
                        {
                          match_all: {}
                        }
                      ],
                      filter: [
                        {
                          bool: {
                            must: [],
                            should: [
                              {
                                match_phrase: {
                                  ["prescriptions.generic_name.text"]: terms[0]
                                }
                              },
                              {
                                match_phrase: {
                                  "prescriptions.generic_name.autocomplete_search":
                                    terms[0]
                                }
                              },
                              {
                                match_phrase: {
                                  ["prescriptions.brand_name.text"]: terms[0]
                                }
                              },
                              {
                                match_phrase: {
                                  "prescriptions.brand_name.autocomplete_search":
                                    terms[0]
                                }
                              },
                              {
                                match_phrase: {
                                  ["prescriptions.drug_class.text"]: terms[0]
                                }
                              },
                              {
                                match_phrase: {
                                  "prescriptions.drug_class.autocomplete_search":
                                    terms[0]
                                }
                              }
                            ]
                          }
                        },
                        {
                          range: {
                            "prescriptions.num_prescriptions_2_year": {
                              gte: 1
                            }
                          }
                        }
                      ]
                    }
                  },
                  inner_hits: {
                    size: 5,
                    from: 0,
                    _source: false,
                    sort: {
                      "prescriptions.num_prescriptions_2_year": {
                        order: "desc"
                      }
                    },
                    docvalue_fields: [
                      {
                        field: "prescriptions.generic_name"
                      },
                      {
                        field: "prescriptions.brand_name"
                      },
                      {
                        field: "prescriptions.drug_class"
                      },
                      {
                        field: "prescriptions.num_prescriptions_2_year"
                      },
                      {
                        field: "prescriptions.patient_count_2_year"
                      },
                      {
                        field: "prescriptions.rank_2_year_prescriptions"
                      },
                      {
                        field: "prescriptions.hcp_quartile_2_year"
                      },
                      {
                        field: "prescriptions.pctOfUniquePatients_2_year"
                      }
                    ]
                  }
                }
              }
            ],
            filter: [
              {
                term: {
                  id: personId
                }
              }
            ]
          }
        }
      },
      "people"
    );

    const totalPatientCount =
      ClaimsPrescriptionsResponseFiltered.hits.hits[0]._source
        .prescriptions_patient_count_2_year;
    expect(resp).toEqual(
      expect.objectContaining({
        from: 0,
        pageSize: 5,
        total: 843,
        results: [
          {
            genericName: "Amlodipine Besylate",
            brandName: "Amlodipine Besylate",
            drugClass: ["Dihydropyridines [CS]"],
            patientCount: 1000.0,
            numPrescriptions: "728",
            rank: 1,
            quartile: 1,
            percentage: 1000 / totalPatientCount
          },
          {
            genericName: "Hydrocodone Bitartrate And Acetaminophen",
            brandName: "Hydrocodone Bitartrate And Acetaminophen",
            drugClass: ["Opioid Agonist [EPC]"],
            patientCount: 900.0,
            numPrescriptions: "515",
            rank: 2,
            quartile: 2,
            percentage: 900 / totalPatientCount
          },
          {
            genericName: "Benzonatate",
            brandName: "Benzonatate",
            drugClass: [
              "Decreased Tracheobronchial Stretch Receptor Activity [PE]"
            ],
            patientCount: 500,
            numPrescriptions: "287",
            rank: 3,
            quartile: 3,
            percentage: 500 / totalPatientCount
          },
          {
            genericName: "Bupropion",
            brandName: "Bupropion",
            drugClass: ["Aminoketone [EPC]"],
            patientCount: 1200,
            numPrescriptions: "258",
            rank: 4,
            quartile: 4,
            percentage: 1200 / totalPatientCount
          },
          {
            genericName: "Clopidogrel",
            brandName: "Clopidogrel",
            drugClass: ["Cytochrome P450 2c8 Inhibitors [MoA]"],
            patientCount: 500,
            numPrescriptions: "231",
            rank: 5,
            quartile: 5,
            percentage: 500 / totalPatientCount
          }
        ]
      })
    );
  });
});

describe("getProcedures()", () => {
  it("returns empty array when no procedures are returned", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(emptyEsResp);

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const resp = await claimsResourceService.getProcedures("asdfssss");

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(emptyClaimsResp);
  });

  it("returns the paged procedures for the person", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsProceduresResponse
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const resp = await claimsResourceService.getProcedures(
      personId,
      terms,
      paging
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(
      expect.objectContaining({
        from: 0,
        pageSize: 5,
        total: 518,
        results: [
          {
            description: "Blood typing, serologic; ABO",
            procedureCode: "86900",
            percentOfClaims: 2.49,
            percentage: 3.1078365706630944,
            count: "232",
            internalCount: 232,
            codeScheme: "CPT"
          },
          {
            description:
              "Office or other outpatient visit for the evaluation and management of an established patient, which requires at least 2 of these 3 key components: An expanded problem focused history; An expanded problem focused examination; Medical decision making of low complexity. Counseling and coordination of care with other physicians, other qualified health care professionals, or agencies are provided consistent with the nature of the problem(s) and the patient's and/or family's needs. Usually, the presenting problem(s) are of low to moderate severity. Typically, 15 minutes are spent face-to-face with the patient and/or family.",
            procedureCode: "99213",
            percentOfClaims: 2.23,
            percentage: 2.786336235766912,
            count: "208",
            internalCount: 208,
            codeScheme: "CPT"
          },
          {
            description: "Magnesium",
            procedureCode: "83735",
            percentOfClaims: 2.19,
            percentage: 2.7327528466175486,
            count: "204",
            internalCount: 204,
            codeScheme: "CPT"
          },
          {
            description: "Uric acid; blood",
            procedureCode: "84550",
            percentOfClaims: 2.16,
            percentage: 2.692565304755526,
            count: "201",
            internalCount: 201,
            codeScheme: "CPT"
          },
          {
            description: "Lactate dehydrogenase (LD), (LDH);",
            procedureCode: "83615",
            percentOfClaims: 2.16,
            percentage: 2.692565304755526,
            count: "201",
            internalCount: 201,
            codeScheme: "CPT"
          }
        ]
      })
    );
  });
});

describe("getFilteredProcedures()", () => {
  it("returns empty array when no procedures are returned", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(emptyEsResp);

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const resp = await claimsResourceService.getFilteredProcedures("asdfssss", {
      filterRange: ClaimsFilterRange.twoYear
    });

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(emptyClaimsResp);
  });

  it("returns the paged procedures for the person", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsProceduresResponseFiltered
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const resp = await claimsResourceService.getFilteredProcedures(
      personId,
      {
        filterRange: ClaimsFilterRange.twoYear
      },
      terms,
      paging
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(
      expect.objectContaining({
        from: 0,
        pageSize: 5,
        total: 518,
        results: [
          {
            description: "Blood typing, serologic; ABO",
            procedureCode: "86900",
            percentOfClaims: 2.49,
            percentage: 3.1078365706630944,
            count: "232",
            internalCount: 232,
            codeScheme: "CPT"
          },
          {
            description:
              "Office or other outpatient visit for the evaluation and management of an established patient, which requires at least 2 of these 3 key components: An expanded problem focused history; An expanded problem focused examination; Medical decision making of low complexity. Counseling and coordination of care with other physicians, other qualified health care professionals, or agencies are provided consistent with the nature of the problem(s) and the patient's and/or family's needs. Usually, the presenting problem(s) are of low to moderate severity. Typically, 15 minutes are spent face-to-face with the patient and/or family.",
            procedureCode: "99213",
            percentOfClaims: 2.23,
            percentage: 2.786336235766912,
            count: "208",
            internalCount: 208,
            codeScheme: "CPT"
          },
          {
            description: "Magnesium",
            procedureCode: "83735",
            percentOfClaims: 2.19,
            percentage: 2.7327528466175486,
            count: "204",
            internalCount: 204,
            codeScheme: "CPT"
          },
          {
            description: "Uric acid; blood",
            procedureCode: "84550",
            percentOfClaims: 2.16,
            percentage: 2.692565304755526,
            count: "201",
            internalCount: 201,
            codeScheme: "CPT"
          },
          {
            description: "Lactate dehydrogenase (LD), (LDH);",
            procedureCode: "83615",
            percentOfClaims: 2.16,
            percentage: 2.692565304755526,
            count: "201",
            internalCount: 201,
            codeScheme: "CPT"
          }
        ]
      })
    );
  });
});

describe("getClaimsTotals()", () => {
  it("should return claims total for person", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsTotalResponse
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = "1199780";

    const resp = await claimsResourceService.getClaimsTotals(personId);

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();

    expect(resp).toEqual(
      expect.objectContaining({ diagnoses: 9239, procedures: 7465 })
    );
  });

  it("should return 0 for non existent KOL.", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(emptyEsResp);

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = "asdfvdw112";

    const resp = await claimsResourceService.getClaimsTotals(personId);

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();

    expect(resp).toEqual(
      expect.objectContaining({ diagnoses: 0, procedures: 0 })
    );
  });
});

describe("getFilteredClaimsTotals()", () => {
  it("should return claims total for person", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsTotalResponseFiltered
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = "1199780";

    const resp = await claimsResourceService.getFilteredClaimsTotals(personId, {
      filterRange: ClaimsFilterRange.twoYear
    });

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();

    expect(resp).toEqual(
      expect.objectContaining({
        diagnoses: 3204,
        procedures: 1250,
        prescriptions: 1234,
        prescriptionPatients: 123
      })
    );
  });

  it("should return 0 for non existent KOL.", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(emptyEsResp);

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = "asdfvdw112";

    const resp = await claimsResourceService.getFilteredClaimsTotals(personId, {
      filterRange: ClaimsFilterRange.twoYear
    });

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();

    expect(resp).toEqual(
      expect.objectContaining({ diagnoses: 0, procedures: 0 })
    );
  });
});

// The buildDiagnosesQuery and buildProceduresQuery test below can change and still be considered a non breaking change.
// However, I feel it's important to test that with given input the query will be constructed as expected.
// Will likely ahve to update these if the query changes. But I tried to keep the test honed in to the specific parts
// of the queries that change based on inputs, so hopefully we won't have to change these test much.
describe("buildDiagnosesQuery()", () => {
  it("should return properly formatted base diagnoses es query (without terms etc).", async () => {
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponse
    );

    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const diagnosesRangeFields = getDiagnosesRangeAdjustedFields(
      ClaimsFilterRange.max
    );

    const esQuery = buildDiagnosesQuery(
      personId,
      diagnosesRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    expect(esQuery._source).toEqual("DRG_diagnosesCount");
    expect(
      (esQuery.query?.bool?.must as QueryDslQueryContainer[])?.[0].nested?.path
    ).toEqual("DRG_diagnoses");
    expect(
      (esQuery.query?.bool?.filter as QueryDslQueryContainer[])?.[0].term?.id
    ).toEqual("1199780");
  });

  it("should return properly formatted diagnoses search query with terms", async () => {
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponse
    );

    const personId = "1199780";
    const terms: string[] = ["Cancer"]; // search terms
    const paging = {
      limit: 5,
      offset: 0
    };

    const diagnosesRangeFields = getDiagnosesRangeAdjustedFields(
      ClaimsFilterRange.max
    );

    const esQuery = buildDiagnosesQuery(
      personId,
      diagnosesRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    const mustClause = (
      esQuery.query?.bool?.must as QueryDslQueryContainer[]
    )[0];
    expect(esQuery._source).toEqual("DRG_diagnosesCount");
    expect(mustClause?.nested?.path).toEqual("DRG_diagnoses");
    expect(
      (esQuery?.query?.bool?.filter as QueryDslQueryContainer[])?.[0]?.term?.id
    ).toEqual("1199780");
    expect(mustClause?.nested?.inner_hits?.size).toEqual(5);
    expect(mustClause?.nested?.inner_hits?.from).toEqual(0);
    expect(mustClause?.nested?.query.bool).toEqual(
      expect.objectContaining({
        must: [{ match_all: {} }],
        filter: [
          {
            bool: {
              must: [],
              should: [
                {
                  match_phrase: {
                    "DRG_diagnoses.description_eng": "Cancer"
                  }
                },
                {
                  match_phrase: {
                    "DRG_diagnoses.diagnosisCode_eng.autocomplete_search":
                      "Cancer"
                  }
                }
              ]
            }
          },
          { range: { "DRG_diagnoses.internalCount": { gte: 1 } } }
        ]
      })
    );
  });

  it("should return properly formatted diagnoses page query", async () => {
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponse
    );

    const personId = "1199780";
    const terms: string[] = ["Cancer"];
    const paging = {
      limit: 5,
      offset: 5 // different offset
    };

    const diagnosesRangeFields = getDiagnosesRangeAdjustedFields(
      ClaimsFilterRange.max
    );

    const esQuery = buildDiagnosesQuery(
      personId,
      diagnosesRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    const mustClause = (
      esQuery.query?.bool?.must as QueryDslQueryContainer[]
    )[0];
    expect(esQuery._source).toEqual("DRG_diagnosesCount");
    expect(mustClause?.nested?.path).toEqual("DRG_diagnoses");
    expect(
      (esQuery.query?.bool?.filter as QueryDslQueryContainer[])?.[0].term?.id
    ).toEqual("1199780");
    expect(mustClause?.nested?.inner_hits?.size).toEqual(5);
    expect(mustClause?.nested?.inner_hits?.from).toEqual(5);
  });

  it("should return properly formatted diagnoses sort query", async () => {
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponse
    );

    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 5
    };
    const sortField = DiagnosesSortFields.DiagnosisCode;
    const sortDirection = SortDirection.Desc;

    const diagnosesRangeFields = getDiagnosesRangeAdjustedFields(
      ClaimsFilterRange.max
    );

    const esQuery = buildDiagnosesQuery(
      personId,
      diagnosesRangeFields,
      terms,
      paging.limit,
      paging.offset,
      sortField,
      sortDirection
    );

    expect(esQuery._source).toEqual("DRG_diagnosesCount");

    const mustClauses: QueryDslQueryContainer[] = esQuery.query?.bool
      ?.must as QueryDslQueryContainer[];
    expect(mustClauses?.[0]?.nested?.path).toEqual("DRG_diagnoses");
    expect(
      (esQuery.query?.bool?.filter as QueryDslQueryContainer[])?.[0]?.term?.id
    ).toEqual("1199780");
    expect(mustClauses?.[0]?.nested?.inner_hits?.size).toEqual(5);
    expect(mustClauses?.[0]?.nested?.inner_hits?.from).toEqual(5);
    expect(mustClauses?.[0]?.nested?.inner_hits?.sort).toEqual(
      expect.objectContaining({
        "DRG_diagnoses.diagnosisCode_eng": { order: "desc" }
      })
    );
  });

  it("should return properly formatted diagnoses search query with ccsr codes", async () => {
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponse
    );

    const personId = "1199780";
    const terms: string[] = [];

    const diagnosesRangeFields =
      getDiagnosesRangeAdjustedFieldsForUniquePatientCount(
        ClaimsFilterRange.max
      );

    const ccsrClaimCodes = ["C9200", "C9210", "C9211", "Z5111"];

    const esQuery = buildDiagnosesQuery(
      personId,
      diagnosesRangeFields,
      terms,
      10000,
      0,
      undefined,
      undefined,
      ccsrClaimCodes
    );
    expect(esQuery).toEqual({
      _source: "DRG_diagnosesUniqueCount",
      size: 1,
      query: {
        bool: {
          must: [
            {
              nested: {
                path: "DRG_diagnoses",
                query: {
                  bool: {
                    must: [
                      {
                        match_all: {}
                      }
                    ],
                    filter: [
                      {
                        bool: {
                          must: [],
                          should: [
                            {
                              terms: {
                                "DRG_diagnoses.diagnosisCode_eng":
                                  ccsrClaimCodes
                              }
                            }
                          ]
                        }
                      },
                      {
                        range: {
                          "DRG_diagnoses.internalUniqueCount": {
                            gte: 1
                          }
                        }
                      }
                    ]
                  }
                },
                inner_hits: {
                  size: 10000,
                  from: 0,
                  _source: false,
                  sort: {
                    "DRG_diagnoses.internalUniqueCount": {
                      order: "desc"
                    }
                  },
                  docvalue_fields: [
                    {
                      field: "DRG_diagnoses.description_eng.keyword"
                    },
                    {
                      field: "DRG_diagnoses.diagnosisCode_eng"
                    },
                    {
                      field: "DRG_diagnoses.pctOfUniqueClaims"
                    },
                    {
                      field: "DRG_diagnoses.internalUniqueCount"
                    },
                    {
                      field: "DRG_diagnoses.codeScheme.keyword"
                    }
                  ]
                }
              }
            }
          ],
          filter: [
            {
              term: {
                id: personId
              }
            }
          ]
        }
      }
    });
  });
});

describe("buildProceduresQuery()", () => {
  it("should return properly formatted base procedures es query (without terms etc).", async () => {
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsProceduresResponse
    );

    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const procedureRangeFields = getProceduresRangeAdjustedFields(
      ClaimsFilterRange.max
    );

    const esQuery = buildProceduresQuery(
      personId,
      procedureRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    expect(esQuery._source).toEqual("DRG_proceduresCount");
    expect(esQuery.query.bool.must[0].nested.path).toEqual("DRG_procedures");
    expect(esQuery.query.bool.filter[0].term.id).toEqual("1199780");
  });

  it("should return properly formatted procedures search query with terms", async () => {
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponse
    );

    const personId = "1199780";
    const terms: string[] = ["Cancer"]; // search terms
    const paging = {
      limit: 5,
      offset: 0
    };

    const procedureRangeFields = getProceduresRangeAdjustedFields(
      ClaimsFilterRange.max
    );

    const esQuery = buildProceduresQuery(
      personId,
      procedureRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    expect(esQuery._source).toEqual("DRG_proceduresCount");
    expect(esQuery.query.bool.must[0].nested.path).toEqual("DRG_procedures");
    expect(esQuery.query.bool.filter[0].term.id).toEqual("1199780");
    expect(esQuery.query.bool.must[0].nested.inner_hits.size).toEqual(5);
    expect(esQuery.query.bool.must[0].nested.inner_hits.from).toEqual(0);
    expect(esQuery.query.bool.must[0].nested.query.bool).toEqual(
      expect.objectContaining({
        must: [{ match_all: {} }],
        filter: [
          {
            bool: {
              must: [],
              should: [
                {
                  match_phrase: {
                    "DRG_procedures.description_eng": "Cancer"
                  }
                },
                {
                  match_phrase: {
                    "DRG_procedures.procedureCode_eng.autocomplete_search":
                      "Cancer"
                  }
                }
              ]
            }
          },
          {
            range: { "DRG_procedures.internalCount": { gte: 1 } }
          }
        ]
      })
    );
  });

  it("should return properly formatted diagnoses page query", async () => {
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponse
    );

    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 5 // different offset
    };

    const procedureRangeFields = getProceduresRangeAdjustedFields(
      ClaimsFilterRange.max
    );

    const esQuery = buildProceduresQuery(
      personId,
      procedureRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    expect(esQuery._source).toEqual("DRG_proceduresCount");
    expect(esQuery.query.bool.must[0].nested.path).toEqual("DRG_procedures");
    expect(esQuery.query.bool.filter[0].term.id).toEqual("1199780");
    expect(esQuery.query.bool.must[0].nested.inner_hits.size).toEqual(5);
    expect(esQuery.query.bool.must[0].nested.inner_hits.from).toEqual(5);
  });

  it("should return properly formatted diagnoses sort query", async () => {
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponse
    );

    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 5
    };
    const sortField = ProcedureSortFields.ProcedureCode;
    const sortDirection = SortDirection.Desc;

    const procedureRangeFields = getProceduresRangeAdjustedFields(
      ClaimsFilterRange.max
    );

    const esQuery = buildProceduresQuery(
      personId,
      procedureRangeFields,
      terms,
      paging.limit,
      paging.offset,
      sortField,
      sortDirection
    );

    expect(esQuery._source).toEqual("DRG_proceduresCount");
    expect(esQuery.query.bool.must[0].nested.path).toEqual("DRG_procedures");
    expect(esQuery.query.bool.filter[0].term.id).toEqual("1199780");
    expect(esQuery.query.bool.must[0].nested.inner_hits.size).toEqual(5);
    expect(esQuery.query.bool.must[0].nested.inner_hits.from).toEqual(5);
    expect(esQuery.query.bool.must[0].nested.inner_hits.sort).toEqual(
      expect.objectContaining({
        "DRG_procedures.procedureCode_eng.keyword": {
          order: "desc"
        }
      })
    );
  });
});

describe("unique patient count", () => {
  it("should use unique patient count field when flag is true for getFilteredDiagnoses", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsDiagnosesResponseFilteredForUniquePatientCount
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = faker.datatype.string();
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const resp = await claimsResourceService.getFilteredDiagnoses(
      personId,
      {
        filterRange: ClaimsFilterRange.twoYear
      },
      terms,
      paging,
      undefined,
      {
        shouldUseUniquePatientCount: true
      }
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(
      expect.objectContaining({
        from: 0,
        pageSize: 5,
        total: 843,
        results: [
          {
            description: "Other long term (current) drug therapy",
            diagnosisCode: "Z79899",
            percentOfClaims: 7.88,
            percentage: 7.879640653750405,
            count: "728",
            internalCount: 728,
            codeScheme: "ICD10"
          },
          {
            description:
              "Acute myeloblastic leukemia, not having achieved remission",
            diagnosisCode: "C9200",
            percentOfClaims: 5.57,
            percentage: 5.574196341595411,
            count: "515",
            internalCount: 515,
            codeScheme: "ICD10"
          },
          {
            description:
              "Chronic myeloid leukemia, BCR/ABL-positive, not having achieved remission",
            diagnosisCode: "C9210",
            percentOfClaims: 3.11,
            percentage: 3.106396796190064,
            count: "287",
            internalCount: 287,
            codeScheme: "ICD10"
          },
          {
            description: "Encounter for antineoplastic chemotherapy",
            diagnosisCode: "Z5111",
            percentOfClaims: 2.79,
            percentage: 2.7925100119060504,
            count: "258",
            internalCount: 258,
            codeScheme: "ICD10"
          },
          {
            description:
              "Chronic myeloid leukemia, BCR/ABL-positive, in remission",
            diagnosisCode: "C9211",
            percentOfClaims: 2.5,
            percentage: 2.500270592055417,
            count: "231",
            internalCount: 231,
            codeScheme: "ICD10"
          }
        ]
      })
    );
  });
  it("should use unique patient count field when flag is true for getFilteredProcedures", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsProceduresResponseFilteredForUniquePatientCount
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = faker.datatype.string();
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const resp = await claimsResourceService.getFilteredProcedures(
      personId,
      {
        filterRange: ClaimsFilterRange.twoYear
      },
      terms,
      paging,
      undefined,
      {
        shouldUseUniquePatientCount: true
      }
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(
      expect.objectContaining({
        from: 0,
        pageSize: 5,
        total: 518,
        results: [
          {
            description: "Blood typing, serologic; ABO",
            procedureCode: "86900",
            percentOfClaims: 2.49,
            percentage: 3.1078365706630944,
            count: "232",
            internalCount: 232,
            codeScheme: "CPT"
          },
          {
            description:
              "Office or other outpatient visit for the evaluation and management of an established patient, which requires at least 2 of these 3 key components: An expanded problem focused history; An expanded problem focused examination; Medical decision making of low complexity. Counseling and coordination of care with other physicians, other qualified health care professionals, or agencies are provided consistent with the nature of the problem(s) and the patient's and/or family's needs. Usually, the presenting problem(s) are of low to moderate severity. Typically, 15 minutes are spent face-to-face with the patient and/or family.",
            procedureCode: "99213",
            percentOfClaims: 2.23,
            percentage: 2.786336235766912,
            count: "208",
            internalCount: 208,
            codeScheme: "CPT"
          },
          {
            description: "Magnesium",
            procedureCode: "83735",
            percentOfClaims: 2.19,
            percentage: 2.7327528466175486,
            count: "204",
            internalCount: 204,
            codeScheme: "CPT"
          },
          {
            description: "Uric acid; blood",
            procedureCode: "84550",
            percentOfClaims: 2.16,
            percentage: 2.692565304755526,
            count: "201",
            internalCount: 201,
            codeScheme: "CPT"
          },
          {
            description: "Lactate dehydrogenase (LD), (LDH);",
            procedureCode: "83615",
            percentOfClaims: 2.16,
            percentage: 2.692565304755526,
            count: "201",
            internalCount: 201,
            codeScheme: "CPT"
          }
        ]
      })
    );
  });

  it("should not use unique patient count field when disable unique patient count for procedures flag is true for getFilteredProcedures", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsProceduresResponseFiltered
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = faker.datatype.string();
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const resp = await claimsResourceService.getFilteredProcedures(
      personId,
      {
        filterRange: ClaimsFilterRange.twoYear
      },
      terms,
      paging,
      undefined,
      {
        shouldUseUniquePatientCount: true,
        disableUniquePatientCountForOnlyProcedures: true
      }
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(
      expect.objectContaining({
        from: 0,
        pageSize: 5,
        total: 518,
        results: [
          {
            description: "Blood typing, serologic; ABO",
            procedureCode: "86900",
            percentOfClaims: 2.49,
            percentage: 3.1078365706630944,
            count: "232",
            internalCount: 232,
            codeScheme: "CPT"
          },
          {
            description:
              "Office or other outpatient visit for the evaluation and management of an established patient, which requires at least 2 of these 3 key components: An expanded problem focused history; An expanded problem focused examination; Medical decision making of low complexity. Counseling and coordination of care with other physicians, other qualified health care professionals, or agencies are provided consistent with the nature of the problem(s) and the patient's and/or family's needs. Usually, the presenting problem(s) are of low to moderate severity. Typically, 15 minutes are spent face-to-face with the patient and/or family.",
            procedureCode: "99213",
            percentOfClaims: 2.23,
            percentage: 2.786336235766912,
            count: "208",
            internalCount: 208,
            codeScheme: "CPT"
          },
          {
            description: "Magnesium",
            procedureCode: "83735",
            percentOfClaims: 2.19,
            percentage: 2.7327528466175486,
            count: "204",
            internalCount: 204,
            codeScheme: "CPT"
          },
          {
            description: "Uric acid; blood",
            procedureCode: "84550",
            percentOfClaims: 2.16,
            percentage: 2.692565304755526,
            count: "201",
            internalCount: 201,
            codeScheme: "CPT"
          },
          {
            description: "Lactate dehydrogenase (LD), (LDH);",
            procedureCode: "83615",
            percentOfClaims: 2.16,
            percentage: 2.692565304755526,
            count: "201",
            internalCount: 201,
            codeScheme: "CPT"
          }
        ]
      })
    );
  });
});

describe("getFilteredDiagnosisCareClusters()", () => {
  it("returns empty array when no ccsr are returned", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(emptyEsResp);

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const resp = await claimsResourceService.getFilteredDiagnosisCareClusters(
      "asdfssss",
      {
        filterRange: ClaimsFilterRange.twoYear
      }
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(emptyClaimsResp);
  });

  it("returns the paged ccsr diagnoses for the person", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsCcsrDiagnosesResponseFiltered
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const resp = await claimsResourceService.getFilteredDiagnosisCareClusters(
      personId,
      {
        filterRange: ClaimsFilterRange.twoYear
      },
      terms,
      paging
    );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(
      expect.objectContaining({
        from: 0,
        pageSize: 3,
        total: 3,
        results: [
          {
            description: "breast cancer - all other types",
            percentOfClaims: 83.71428571428572,
            hcpQuartile: 1,
            count: 293
          },
          {
            description: "musculoskeletal pain, not low back pain",
            percentOfClaims: 31.428571428571427,
            hcpQuartile: 1,
            count: 110
          },
          {
            description: "secondary malignancies",
            percentOfClaims: 18.857142857142858,
            hcpQuartile: 1,
            count: 66
          }
        ]
      })
    );
  });
});

describe("buildCcsrDiagnosesQuery()", () => {
  it("should return properly formatted base ccsr diagnoses es query (without terms etc).", async () => {
    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const diagnosesCcsrRangeFields =
      getDiagnosesCcsrRangeAdjustedFieldsForUniquePatientCount(
        ClaimsFilterRange.max
      );

    const esQuery = buildDiagnosesCcsrQuery(
      personId,
      diagnosesCcsrRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    expect(esQuery).toEqual(
      expect.objectContaining({
        _source: "DRG_diagnosesUniqueCount",
        size: 1,
        query: {
          bool: {
            must: [
              {
                nested: expect.objectContaining({
                  path: "ccsr",
                  query: {
                    bool: expect.objectContaining({
                      must: [
                        {
                          match_all: {}
                        }
                      ],
                      filter: [
                        {
                          range: {
                            ["ccsr.internalUniqueCount"]: {
                              gt: 0
                            }
                          }
                        }
                      ]
                    })
                  }
                })
              }
            ],
            filter: [
              {
                term: {
                  id: personId
                }
              }
            ]
          }
        }
      })
    );
  });

  it("should return properly formatted base ccsr diagnoses es query with terms", async () => {
    const personId = "1199780";
    const terms: string[] = [faker.datatype.string()];
    const paging = {
      limit: 5,
      offset: 0
    };

    const diagnosesCcsrRangeFields =
      getDiagnosesCcsrRangeAdjustedFieldsForUniquePatientCount(
        ClaimsFilterRange.max
      );

    const esQuery = buildDiagnosesCcsrQuery(
      personId,
      diagnosesCcsrRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    expect(esQuery).toEqual(
      expect.objectContaining({
        _source: "DRG_diagnosesUniqueCount",
        size: 1,
        query: {
          bool: {
            must: [
              {
                nested: expect.objectContaining({
                  path: "ccsr",
                  query: {
                    bool: expect.objectContaining({
                      must: [
                        {
                          match_all: {}
                        }
                      ],
                      filter: [
                        {
                          terms: {
                            "ccsr.description_eng": terms
                          }
                        },
                        {
                          range: {
                            ["ccsr.internalUniqueCount"]: {
                              gt: 0
                            }
                          }
                        }
                      ]
                    })
                  }
                })
              }
            ],
            filter: [
              {
                term: {
                  id: personId
                }
              }
            ]
          }
        }
      })
    );
  });

  it("should return properly formatted base ccsr diagnoses es query with terms and timeframe", async () => {
    const personId = "1199780";
    const terms: string[] = [faker.datatype.string()];
    const paging = {
      limit: 5,
      offset: 0
    };

    const diagnosesCcsrRangeFields =
      getDiagnosesCcsrRangeAdjustedFieldsForUniquePatientCount(
        ClaimsFilterRange.twoYear
      );

    const esQuery = buildDiagnosesCcsrQuery(
      personId,
      diagnosesCcsrRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    expect(esQuery).toEqual(
      expect.objectContaining({
        _source: "DRG_diagnosesUniqueCount_2_year",
        size: 1,
        query: {
          bool: {
            must: [
              {
                nested: expect.objectContaining({
                  path: "ccsr",
                  query: {
                    bool: expect.objectContaining({
                      must: [
                        {
                          match_all: {}
                        }
                      ],
                      filter: [
                        {
                          terms: {
                            "ccsr.description_eng": terms
                          }
                        },
                        {
                          range: {
                            ["ccsr.internalUniqueCount_2_year"]: {
                              gt: 0
                            }
                          }
                        }
                      ]
                    })
                  }
                })
              }
            ],
            filter: [
              {
                term: {
                  id: personId
                }
              }
            ]
          }
        }
      })
    );
  });

  it("should return properly formatted base ccsr diagnoses es query with terms and timeframe and sort", async () => {
    const personId = "1199780";
    const terms: string[] = [faker.datatype.string()];
    const paging = {
      limit: 5,
      offset: 0
    };

    const diagnosesCcsrRangeFields =
      getDiagnosesCcsrRangeAdjustedFieldsForUniquePatientCount(
        ClaimsFilterRange.twoYear
      );
    const sortField = DiagnosesCcsrSortFields.Description;
    const sortDirection = SortDirection.Desc;
    const esQuery = buildDiagnosesCcsrQuery(
      personId,
      diagnosesCcsrRangeFields,
      terms,
      paging.limit,
      paging.offset,
      sortField,
      sortDirection
    );

    expect(esQuery).toEqual(
      expect.objectContaining({
        _source: "DRG_diagnosesUniqueCount_2_year",
        size: 1,
        query: {
          bool: {
            must: [
              {
                nested: expect.objectContaining({
                  path: "ccsr",
                  query: {
                    bool: expect.objectContaining({
                      must: [
                        {
                          match_all: {}
                        }
                      ],
                      filter: [
                        {
                          terms: {
                            "ccsr.description_eng": terms
                          }
                        },
                        {
                          range: {
                            ["ccsr.internalUniqueCount_2_year"]: {
                              gt: 0
                            }
                          }
                        }
                      ]
                    })
                  },
                  inner_hits: expect.objectContaining({
                    sort: [
                      {
                        ["ccsr.description_eng"]: {
                          order: sortDirection
                        }
                      },
                      {
                        ["ccsr.internalUniqueCount_2_year"]: {
                          order: "desc"
                        }
                      },
                      {
                        ["ccsr.description_eng"]: {
                          order: "asc"
                        }
                      }
                    ]
                  })
                })
              }
            ],
            filter: [
              {
                term: {
                  id: personId
                }
              }
            ]
          }
        }
      })
    );
  });
});

describe("buildCcsrProceduresQuery()", () => {
  it("should return properly formatted base ccsr procedures es query (without terms etc).", async () => {
    const personId = "1199780";
    const terms: string[] = [];
    const paging = {
      limit: 5,
      offset: 0
    };

    const proceduresCcsrRangeFields =
      getProceduresCcsrRangeAdjustedFieldsForUniquePatientCount(
        ClaimsFilterRange.max
      );

    const esQuery = buildProceduresCcsrQuery(
      personId,
      proceduresCcsrRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    expect(esQuery).toEqual(
      expect.objectContaining({
        _source: "DRG_proceduresUniqueCount",
        size: 1,
        query: {
          bool: {
            must: [
              {
                nested: expect.objectContaining({
                  path: "ccsr_px",
                  query: {
                    bool: expect.objectContaining({
                      must: [
                        {
                          match_all: {}
                        }
                      ],
                      filter: [
                        {
                          range: {
                            ["ccsr_px.internalUniqueCount"]: {
                              gt: 0
                            }
                          }
                        }
                      ]
                    })
                  }
                })
              }
            ],
            filter: [
              {
                term: {
                  id: personId
                }
              }
            ]
          }
        }
      })
    );
  });

  it("should return properly formatted base ccsr procedures es query with terms", async () => {
    const personId = "1199780";
    const terms: string[] = [faker.datatype.string()];
    const paging = {
      limit: 5,
      offset: 0
    };

    const proceduresCcsrRangeFields =
      getProceduresCcsrRangeAdjustedFieldsForUniquePatientCount(
        ClaimsFilterRange.max
      );

    const esQuery = buildProceduresCcsrQuery(
      personId,
      proceduresCcsrRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    expect(esQuery).toEqual(
      expect.objectContaining({
        _source: "DRG_proceduresUniqueCount",
        size: 1,
        query: {
          bool: {
            must: [
              {
                nested: expect.objectContaining({
                  path: "ccsr_px",
                  query: {
                    bool: expect.objectContaining({
                      must: [
                        {
                          match_all: {}
                        }
                      ],
                      filter: [
                        {
                          terms: {
                            "ccsr_px.description_eng": terms
                          }
                        },
                        {
                          range: {
                            ["ccsr_px.internalUniqueCount"]: {
                              gt: 0
                            }
                          }
                        }
                      ]
                    })
                  }
                })
              }
            ],
            filter: [
              {
                term: {
                  id: personId
                }
              }
            ]
          }
        }
      })
    );
  });

  it("should return properly formatted base ccsr procedures es query with terms and timeframe", async () => {
    const personId = "1199780";
    const terms: string[] = [faker.datatype.string()];
    const paging = {
      limit: 5,
      offset: 0
    };

    const proceduresCcsrRangeFields =
      getProceduresCcsrRangeAdjustedFieldsForUniquePatientCount(
        ClaimsFilterRange.twoYear
      );

    const esQuery = buildProceduresCcsrQuery(
      personId,
      proceduresCcsrRangeFields,
      terms,
      paging.limit,
      paging.offset
    );

    expect(esQuery).toEqual(
      expect.objectContaining({
        _source: "DRG_proceduresUniqueCount_2_year",
        size: 1,
        query: {
          bool: {
            must: [
              {
                nested: expect.objectContaining({
                  path: "ccsr_px",
                  query: {
                    bool: expect.objectContaining({
                      must: [
                        {
                          match_all: {}
                        }
                      ],
                      filter: [
                        {
                          terms: {
                            "ccsr_px.description_eng": terms
                          }
                        },
                        {
                          range: {
                            ["ccsr_px.internalUniqueCount_2_year"]: {
                              gt: 0
                            }
                          }
                        }
                      ]
                    })
                  }
                })
              }
            ],
            filter: [
              {
                term: {
                  id: personId
                }
              }
            ]
          }
        }
      })
    );
  });

  it("should return properly formatted base ccsr procedures es query with terms and timeframe and sort", async () => {
    const personId = "1199780";
    const terms: string[] = [faker.datatype.string()];
    const paging = {
      limit: 5,
      offset: 0
    };

    const proceduresCcsrRangeFields =
      getProceduresCcsrRangeAdjustedFieldsForUniquePatientCount(
        ClaimsFilterRange.twoYear
      );
    const sortField = ProceduresCcsrSortFields.Description;
    const sortDirection = SortDirection.Desc;
    const esQuery = buildProceduresCcsrQuery(
      personId,
      proceduresCcsrRangeFields,
      terms,
      paging.limit,
      paging.offset,
      sortField,
      sortDirection
    );

    expect(esQuery).toEqual(
      expect.objectContaining({
        _source: "DRG_proceduresUniqueCount_2_year",
        size: 1,
        query: {
          bool: {
            must: [
              {
                nested: expect.objectContaining({
                  path: "ccsr_px",
                  query: {
                    bool: expect.objectContaining({
                      must: [
                        {
                          match_all: {}
                        }
                      ],
                      filter: [
                        {
                          terms: {
                            "ccsr_px.description_eng": terms
                          }
                        },
                        {
                          range: {
                            ["ccsr_px.internalUniqueCount_2_year"]: {
                              gt: 0
                            }
                          }
                        }
                      ]
                    })
                  }
                })
              }
            ],
            filter: [
              {
                term: {
                  id: personId
                }
              }
            ]
          }
        }
      })
    );
  });
});

describe("getUniquePatientCountTotals()", () => {
  it("returns empty array when no values are returned", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(emptyEsResp);

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const resp = await claimsResourceService.getUniquePatientCountTotals([
      "123"
    ]);

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual([]);
  });

  it("returns the unique patient counts for each person ID", async () => {
    const configService = createMockInstance(ConfigService);

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      ClaimsUniquePatientCountTotalResponse
    );

    const ccsrIcdMappingRepository = createMockInstance(
      CcsrIcdMappingRepository
    );
    const claimsResourceService = new ClaimsResourceService(
      configService,
      elasticsearchService,
      ccsrIcdMappingRepository
    );

    const resp = await claimsResourceService.getUniquePatientCountTotals([
      "10872034",
      "1199780"
    ]);

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(resp).toEqual(
      expect.arrayContaining([
        {
          personId: "10872034",
          diagnosesPatients: "848",
          proceduresPatients: "849"
        },
        {
          personId: "1199780",
          diagnosesPatients: "425",
          proceduresPatients: "425"
        }
      ])
    );
  });
});
