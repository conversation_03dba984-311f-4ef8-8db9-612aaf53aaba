import { ProfileSummaryResourceService } from "./ProfileSummaryResourceService";
import { ElasticSearchService } from "./ElasticSearchService";

import SummaryMetricsResponseForPeople from "./__fixtures__/SummaryMetricsResponseForPeople.json";
import TopMeshTermsResponseForPeople from "./__fixtures__/TopMeshTermsResponseForPeople.json";
import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { faker } from "@faker-js/faker";

describe("getMetricsForPeople()", () => {
  it("returns metrics", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      SummaryMetricsResponseForPeople
    );

    jest.useFakeTimers();
    jest.setSystemTime(new Date("2022-01-01"));

    const profileSummaryResourceService = new ProfileSummaryResourceService(
      configService,
      elasticsearchService
    );

    const peopleMetrics =
      await profileSummaryResourceService.getMetricsForPeople(
        ["4667519", "4938278"],
        faker.datatype.string()
      );

    jest.useRealTimers();

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();
    expect(peopleMetrics).toEqual([
      expect.objectContaining({
        personId: "4667519",
        yearsActive: 20,
        publications: {
          total: 5050,
          last12Months: 222
        },
        trials: {
          total: 4331,
          inProgress: 100
        },
        congresses: {
          total: 75
        },
        payments: {
          total: 50,
          amount: 500,
          topCompanyDistributions: [
            {
              companyName: "Novartis",
              percentage: 20
            },
            {
              companyName: "Merck",
              percentage: 10
            },
            {
              companyName: "Cara Therapeutics",
              percentage: 5
            }
          ]
        }
      }),
      expect.objectContaining({
        personId: "4938278",
        yearsActive: 5,
        publications: {
          total: 505,
          last12Months: 22
        },
        trials: {
          total: 433,
          inProgress: 10
        },
        congresses: {
          total: 0
        },
        payments: {
          total: 5,
          amount: 50,
          topCompanyDistributions: [
            {
              companyName: "Novartis",
              percentage: 20
            },
            {
              companyName: "Merck",
              percentage: 10
            },
            {
              companyName: "Cara Therapeutics",
              percentage: 4
            }
          ]
        }
      }),
      expect.objectContaining({
        personId: "1116272",
        yearsActive: 0,
        publications: {
          total: 0,
          last12Months: 0
        },
        trials: {
          total: 0,
          inProgress: 0
        },
        congresses: {
          total: 0
        },
        payments: {
          total: 0,
          amount: 0,
          topCompanyDistributions: []
        }
      })
    ]);
  });
});

describe("getTopMeshTermsForPeople()", () => {
  it("returns top keywords", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      TopMeshTermsResponseForPeople
    );

    const profileSummaryResourceService = new ProfileSummaryResourceService(
      configService,
      elasticsearchService
    );

    const startDate = new Date(1293858059000);
    const endDate = new Date(1626727979993);

    const projectId = faker.datatype.uuid();

    const meshTerms =
      await profileSummaryResourceService.getTopMeshTermsForPeople(
        ["1199780", "1016536"],
        10,
        startDate,
        endDate,
        projectId
      );

    expect(elasticsearchService.getSignedElasticRequest).toHaveBeenCalled();

    expect(meshTerms).toEqual([
      expect.objectContaining({
        personId: "1016536",
        totalPublications: 760,
        publicationsByYear: [
          { year: 2011, total: 60 },
          { year: 2012, total: 54 },
          { year: 2013, total: 66 },
          { year: 2014, total: 91 },
          { year: 2015, total: 80 },
          { year: 2016, total: 76 },
          { year: 2017, total: 76 },
          { year: 2018, total: 76 },
          { year: 2019, total: 70 },
          { year: 2020, total: 86 },
          { year: 2021, total: 25 }
        ],
        topKeyWords: [
          {
            name: "Research Support, Non-U.S. Gov't",
            total: 117,
            keywordsByYear: [
              { year: 2011, total: 3 },
              { year: 2012, total: 5 },
              { year: 2013, total: 10 },
              { year: 2014, total: 19 },
              { year: 2015, total: 13 },
              { year: 2016, total: 13 },
              { year: 2017, total: 5 },
              { year: 2018, total: 16 },
              { year: 2019, total: 12 },
              { year: 2020, total: 18 },
              { year: 2021, total: 3 }
            ]
          },
          {
            name: "Research Support, N.I.H., Extramural",
            total: 111,
            keywordsByYear: [
              { year: 2011, total: 5 },
              { year: 2012, total: 9 },
              { year: 2013, total: 9 },
              { year: 2014, total: 14 },
              { year: 2015, total: 14 },
              { year: 2016, total: 7 },
              { year: 2017, total: 5 },
              { year: 2018, total: 18 },
              { year: 2019, total: 13 },
              { year: 2020, total: 14 },
              { year: 2021, total: 3 }
            ]
          },
          {
            name: "Clinical Trial, Phase I",
            total: 108,
            keywordsByYear: [
              { year: 2011, total: 7 },
              { year: 2012, total: 12 },
              { year: 2013, total: 14 },
              { year: 2014, total: 21 },
              { year: 2015, total: 15 },
              { year: 2016, total: 8 },
              { year: 2017, total: 8 },
              { year: 2018, total: 11 },
              { year: 2019, total: 4 },
              { year: 2020, total: 8 }
            ]
          },
          {
            name: "Multicenter Study",
            total: 29,
            keywordsByYear: [
              { year: 2011, total: 1 },
              { year: 2012, total: 1 },
              { year: 2013, total: 1 },
              { year: 2014, total: 3 },
              { year: 2015, total: 1 },
              { year: 2016, total: 1 },
              { year: 2017, total: 4 },
              { year: 2018, total: 5 },
              { year: 2019, total: 5 },
              { year: 2020, total: 7 }
            ]
          },
          {
            name: "Review",
            total: 24,
            keywordsByYear: [
              { year: 2011, total: 1 },
              { year: 2012, total: 0 },
              { year: 2013, total: 1 },
              { year: 2014, total: 7 },
              { year: 2015, total: 2 },
              { year: 2016, total: 3 },
              { year: 2017, total: 3 },
              { year: 2018, total: 2 },
              { year: 2019, total: 3 },
              { year: 2020, total: 1 },
              { year: 2021, total: 1 }
            ]
          },
          {
            name: "Clinical Trial, Phase II",
            total: 10,
            keywordsByYear: [
              { year: 2017, total: 1 },
              { year: 2018, total: 1 },
              { year: 2019, total: 5 },
              { year: 2020, total: 3 }
            ]
          },
          {
            name: "Comment",
            total: 7,
            keywordsByYear: [
              { year: 2013, total: 2 },
              { year: 2014, total: 0 },
              { year: 2015, total: 0 },
              { year: 2016, total: 1 },
              { year: 2017, total: 2 },
              { year: 2018, total: 0 },
              { year: 2019, total: 0 },
              { year: 2020, total: 0 },
              { year: 2021, total: 2 }
            ]
          },
          {
            name: "Published Erratum",
            total: 7,
            keywordsByYear: [
              { year: 2015, total: 1 },
              { year: 2016, total: 2 },
              { year: 2017, total: 1 },
              { year: 2018, total: 1 },
              { year: 2019, total: 2 }
            ]
          },
          {
            name: "Clinical Trial",
            total: 6,
            keywordsByYear: [
              { year: 2013, total: 1 },
              { year: 2014, total: 2 },
              { year: 2015, total: 1 },
              { year: 2016, total: 2 }
            ]
          },
          {
            name: "Research Support, U.S. Gov't, Non-P.H.S.",
            total: 6,
            keywordsByYear: [
              { year: 2015, total: 1 },
              { year: 2016, total: 1 },
              { year: 2017, total: 0 },
              { year: 2018, total: 2 },
              { year: 2019, total: 0 },
              { year: 2020, total: 1 },
              { year: 2021, total: 1 }
            ]
          }
        ]
      }),
      expect.objectContaining({
        personId: "1199780",
        totalPublications: 2369,
        publicationsByYear: [
          { year: 2011, total: 198 },
          { year: 2012, total: 242 },
          { year: 2013, total: 242 },
          { year: 2014, total: 216 },
          { year: 2015, total: 272 },
          { year: 2016, total: 252 },
          { year: 2017, total: 261 },
          { year: 2018, total: 223 },
          { year: 2019, total: 218 },
          { year: 2020, total: 182 },
          { year: 2021, total: 63 }
        ],
        topKeyWords: [
          {
            name: "Research Support, Non-U.S. Gov't",
            total: 395,
            keywordsByYear: [
              { year: 2011, total: 30 },
              { year: 2012, total: 43 },
              { year: 2013, total: 51 },
              { year: 2014, total: 31 },
              { year: 2015, total: 53 },
              { year: 2016, total: 40 },
              { year: 2017, total: 22 },
              { year: 2018, total: 48 },
              { year: 2019, total: 30 },
              { year: 2020, total: 40 },
              { year: 2021, total: 7 }
            ]
          },
          {
            name: "Research Support, N.I.H., Extramural",
            total: 316,
            keywordsByYear: [
              { year: 2011, total: 11 },
              { year: 2012, total: 20 },
              { year: 2013, total: 39 },
              { year: 2014, total: 29 },
              { year: 2015, total: 53 },
              { year: 2016, total: 32 },
              { year: 2017, total: 30 },
              { year: 2018, total: 43 },
              { year: 2019, total: 22 },
              { year: 2020, total: 29 },
              { year: 2021, total: 8 }
            ]
          },
          {
            name: "Clinical Trial, Phase II",
            total: 150,
            keywordsByYear: [
              { year: 2011, total: 14 },
              { year: 2012, total: 16 },
              { year: 2013, total: 22 },
              { year: 2014, total: 8 },
              { year: 2015, total: 23 },
              { year: 2016, total: 13 },
              { year: 2017, total: 12 },
              { year: 2018, total: 21 },
              { year: 2019, total: 12 },
              { year: 2020, total: 8 },
              { year: 2021, total: 1 }
            ]
          },
          {
            name: "Review",
            total: 123,
            keywordsByYear: [
              { year: 2011, total: 18 },
              { year: 2012, total: 13 },
              { year: 2013, total: 14 },
              { year: 2014, total: 8 },
              { year: 2015, total: 14 },
              { year: 2016, total: 7 },
              { year: 2017, total: 10 },
              { year: 2018, total: 16 },
              { year: 2019, total: 11 },
              { year: 2020, total: 8 },
              { year: 2021, total: 4 }
            ]
          },
          {
            name: "Letter",
            total: 100,
            keywordsByYear: [
              { year: 2011, total: 1 },
              { year: 2012, total: 4 },
              { year: 2013, total: 12 },
              { year: 2014, total: 7 },
              { year: 2015, total: 10 },
              { year: 2016, total: 16 },
              { year: 2017, total: 15 },
              { year: 2018, total: 10 },
              { year: 2019, total: 6 },
              { year: 2020, total: 9 },
              { year: 2021, total: 10 }
            ]
          },
          {
            name: "Multicenter Study",
            total: 90,
            keywordsByYear: [
              { year: 2011, total: 6 },
              { year: 2012, total: 10 },
              { year: 2013, total: 11 },
              { year: 2014, total: 9 },
              { year: 2015, total: 15 },
              { year: 2016, total: 12 },
              { year: 2017, total: 10 },
              { year: 2018, total: 8 },
              { year: 2019, total: 6 },
              { year: 2020, total: 3 }
            ]
          },
          {
            name: "Randomized Controlled Trial",
            total: 88,
            keywordsByYear: [
              { year: 2011, total: 5 },
              { year: 2012, total: 15 },
              { year: 2013, total: 8 },
              { year: 2014, total: 9 },
              { year: 2015, total: 8 },
              { year: 2016, total: 8 },
              { year: 2017, total: 9 },
              { year: 2018, total: 10 },
              { year: 2019, total: 8 },
              { year: 2020, total: 7 },
              { year: 2021, total: 1 }
            ]
          },
          {
            name: "Clinical Trial, Phase I",
            total: 85,
            keywordsByYear: [
              { year: 2011, total: 7 },
              { year: 2012, total: 6 },
              { year: 2013, total: 11 },
              { year: 2014, total: 8 },
              { year: 2015, total: 16 },
              { year: 2016, total: 7 },
              { year: 2017, total: 8 },
              { year: 2018, total: 9 },
              { year: 2019, total: 5 },
              { year: 2020, total: 7 },
              { year: 2021, total: 1 }
            ]
          },
          {
            name: "Comparative Study",
            total: 56,
            keywordsByYear: [
              { year: 2011, total: 4 },
              { year: 2012, total: 7 },
              { year: 2013, total: 6 },
              { year: 2014, total: 5 },
              { year: 2015, total: 4 },
              { year: 2016, total: 7 },
              { year: 2017, total: 8 },
              { year: 2018, total: 6 },
              { year: 2019, total: 4 },
              { year: 2020, total: 1 },
              { year: 2021, total: 4 }
            ]
          },
          {
            name: "Clinical Trial",
            total: 47,
            keywordsByYear: [
              { year: 2011, total: 5 },
              { year: 2012, total: 3 },
              { year: 2013, total: 5 },
              { year: 2014, total: 0 },
              { year: 2015, total: 7 },
              { year: 2016, total: 5 },
              { year: 2017, total: 9 },
              { year: 2018, total: 5 },
              { year: 2019, total: 5 },
              { year: 2020, total: 3 }
            ]
          }
        ]
      })
    ]);
  });
});
