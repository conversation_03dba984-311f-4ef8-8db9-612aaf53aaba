import {
  Affiliation,
  AffiliationInstitution,
  FilterInterface,
  FormattedTopTLAffiliations,
  KeywordSearchInput,
  WeightedSortBy
} from "@h1nyc/search-sdk";
import { AffiliationAdapterService } from "./AffiliationAdapterService";
import { faker } from "@faker-js/faker";
import { ENGLISH } from "./LanguageDetectService";
import { max } from "lodash";
import {
  SavedTerritory,
  SavedTerritoryLocale,
  SavedTerritoryType
} from "@h1nyc/account-user-entities";
import { getEmptyKeywordSearchFilters } from "../util/TestUtils";
const TAIWAN_PROVINCE_OF_CHINA = "中國台灣省";
const TAIWAN = "台灣";
function generateFilters(
  overrides: Partial<FilterInterface> = {}
): FilterInterface {
  const baseFilters: FilterInterface = getEmptyKeywordSearchFilters();

  return { ...baseFilters, ...overrides };
}

function generateStandardWeightedSortBy(
  overrides: Partial<WeightedSortBy> = {}
): WeightedSortBy {
  const baseSortBy = {
    publication: faker.datatype.number(),
    microBloggingCount: faker.datatype.number(),
    citation: faker.datatype.number(),
    trial: faker.datatype.number(),
    congress: faker.datatype.number(),
    payment: faker.datatype.number(),
    diagnoses: faker.datatype.number(),
    procedures: faker.datatype.number(),
    prescriptions: faker.datatype.number(),
    referralsReceived: faker.datatype.number({ min: 0.0001 }),
    referralsSent: faker.datatype.number({ min: 0.0001 }),
    grant: faker.datatype.number(),
    patientsDiversityRank: 0,
    h1DefaultRank: 0,
    twitterFollowersCount: 0,
    twitterTweetCount: 0
  };

  return { ...baseSortBy, ...overrides };
}

function generateKeywordSearchInput(
  overrides: Partial<KeywordSearchInput> = {}
): KeywordSearchInput {
  const page = {
    from: faker.datatype.number(),
    size: faker.datatype.number()
  };
  const userId = faker.datatype.uuid();
  const projectId = faker.datatype.string();
  const query = faker.datatype.string();
  const suppliedFilters = generateFilters();
  const language = ENGLISH;
  const sortBy = generateStandardWeightedSortBy();
  const projectFeatures = {
    advancedOperators: true,
    claims: true,
    referrals: true,
    engagementsV2: faker.datatype.boolean(),
    translateTaiwan: true
  };

  const baseInput = {
    userId,
    page,
    projectId,
    query,
    suppliedFilters,
    language,
    sortBy,
    projectFeatures
  };

  return { ...baseInput, ...overrides };
}

function generateAffiliation(
  overrides: Partial<Affiliation> = {}
): Affiliation {
  const baseInstitution: AffiliationInstitution = {
    id: faker.datatype.number(),
    name: faker.datatype.string(),
    type: faker.datatype.string(),
    orgTypes: [faker.datatype.string()],
    iol: {
      id: faker.datatype.number(),
      region: faker.address.state()
    },
    ultimateParent: undefined,
    address: {
      id: faker.datatype.number(),
      city: faker.address.city(),
      region: faker.address.state(),
      regionCode: faker.address.zipCode(),
      country: faker.address.country(),
      country_level_regions: [faker.address.country()],
      state_level_regions: [faker.address.state()],
      city_level_regions: [faker.address.city()]
    }
  };
  const baseAffiliation: Affiliation = {
    id: faker.datatype.number(),
    titles: [faker.datatype.string()],
    department: faker.datatype.string(),
    type: faker.datatype.string(),
    isPastAffiliation: faker.datatype.boolean(),
    institution: baseInstitution,
    accuracyScore: faker.datatype.number({ min: 0, max: 1 }),
    tlAccuracyScore: faker.datatype.number({ min: 0, max: 1 }),
    claimsNumbers: faker.datatype.number(),
    school: undefined,
    otherSignals: []
  };

  return { ...baseAffiliation, ...overrides };
}

export function generateMockSavedTerritory(
  overrides: Partial<SavedTerritory> = {}
): SavedTerritory {
  const baseSavedTerritory: SavedTerritory = {
    id: faker.datatype.uuid(),
    name: faker.datatype.string(),
    description: null,
    type: Object.values(SavedTerritoryType)[0],
    locale: Object.values(SavedTerritoryLocale)[0],
    territoryData: {
      countries: Array.from(
        { length: faker.datatype.number({ min: 1, max: 5 }) },
        () => faker.address.country()
      ),
      postalCodes: Array.from(
        { length: faker.datatype.number({ min: 1, max: 5 }) },
        () => faker.address.zipCode()
      )
    },
    projectId: faker.datatype.uuid(),
    createdBy: faker.datatype.string(),
    createdByUser: undefined,
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent(),
    deletedAt: null,
    deletedBy: null
  };
  return { ...baseSavedTerritory, ...overrides };
}

describe("AffiliationAdapterService", () => {
  describe("searchTopAffiliations", () => {
    it("should set empty response if there are no affiliations", () => {
      const affiliationAdapterService = new AffiliationAdapterService();
      const input = generateKeywordSearchInput();
      const response = affiliationAdapterService.searchTopAffiliations(
        [],
        input,
        false,
        [],
        []
      );
      expect(response).toEqual({
        formattedRelevantLocations: [],
        hasAboveTheLineAffiliations: false,
        hasAffiliationMatchingFilter: false,
        hasPastAffiliations: false,
        isSeeingPatients: 0,
        locationsToDisplay: []
      });
    });

    it("should set empty response if there are no work affiliations", () => {
      const affiliationAdapterService = new AffiliationAdapterService();
      const input = generateKeywordSearchInput();
      const educationAffiliation = generateAffiliation({
        type: "student"
      });
      const memberAffiliation = generateAffiliation({
        type: "society member"
      });
      const response = affiliationAdapterService.searchTopAffiliations(
        [educationAffiliation, memberAffiliation],
        input,
        false,
        [],
        []
      );
      expect(response).toEqual({
        formattedRelevantLocations: [],
        hasAboveTheLineAffiliations: false,
        hasAffiliationMatchingFilter: false,
        hasPastAffiliations: false,
        isSeeingPatients: 0,
        locationsToDisplay: []
      });
    });

    it("should use currentWork affiliations that has good accuracy", () => {
      const affiliationAdapterService = new AffiliationAdapterService();
      const input = generateKeywordSearchInput();
      const currentWorkAffiliation = generateAffiliation({
        isPastAffiliation: false,
        accuracyScore: 1
      });
      const currentWorkAffiliationWithBadAccuracy = generateAffiliation({
        isPastAffiliation: false,
        accuracyScore: 0
      });
      const pastWorkAffiliation = generateAffiliation({
        isPastAffiliation: true
      });
      const response = affiliationAdapterService.searchTopAffiliations(
        [
          currentWorkAffiliation,
          currentWorkAffiliationWithBadAccuracy,
          pastWorkAffiliation
        ],
        input,
        false,
        [],
        []
      );
      const address = currentWorkAffiliation.institution.address;
      expect(response).toEqual({
        formattedRelevantLocations: [],
        hasAboveTheLineAffiliations: true,
        hasAffiliationMatchingFilter: true,
        hasPastAffiliations: false,
        isSeeingPatients: currentWorkAffiliation.claimsNumbers,
        locationsToDisplay: [
          {
            data: [
              {
                childrenAffiliation: [],
                parentAffiliation: expect.objectContaining({
                  accuracyScore: 1,
                  claimsNumbers: currentWorkAffiliation.claimsNumbers,
                  institution: currentWorkAffiliation.institution
                }),
                parentInstitution: currentWorkAffiliation.institution
              }
            ],
            highestClaimCount: currentWorkAffiliation.claimsNumbers,
            location: [address?.city, address?.region, address?.country].join(
              ", "
            ),
            region: {
              country: address?.country,
              region: address?.region
            }
          }
        ]
      });
    });

    it("should use currentWork affiliations that has bad accuracy if no other current affiliation is present", () => {
      const affiliationAdapterService = new AffiliationAdapterService();
      const input = generateKeywordSearchInput();
      const currentWorkAffiliation = generateAffiliation({
        isPastAffiliation: false,
        accuracyScore: 0
      });
      const pastWorkAffiliation = generateAffiliation({
        isPastAffiliation: true
      });
      const response = affiliationAdapterService.searchTopAffiliations(
        [currentWorkAffiliation, pastWorkAffiliation],
        input,
        false,
        [],
        []
      );
      const address = currentWorkAffiliation.institution.address;
      expect(response).toEqual({
        formattedRelevantLocations: [],
        hasAboveTheLineAffiliations: false,
        hasAffiliationMatchingFilter: true,
        hasPastAffiliations: false,
        isSeeingPatients: 0,
        locationsToDisplay: [
          {
            data: [
              {
                childrenAffiliation: [],
                parentAffiliation: expect.objectContaining({
                  accuracyScore: 0,
                  claimsNumbers: currentWorkAffiliation.claimsNumbers,
                  institution: currentWorkAffiliation.institution
                }),
                parentInstitution: currentWorkAffiliation.institution
              }
            ],
            highestClaimCount: currentWorkAffiliation.claimsNumbers,
            location: [address?.city, address?.region, address?.country].join(
              ", "
            ),
            region: {
              country: address?.country,
              region: address?.region
            }
          }
        ]
      });
    });

    it("should use past affiliations if current affiliation is not present", () => {
      const affiliationAdapterService = new AffiliationAdapterService();
      const input = generateKeywordSearchInput();

      const pastWorkAffiliation = generateAffiliation({
        isPastAffiliation: true
      });
      const response = affiliationAdapterService.searchTopAffiliations(
        [pastWorkAffiliation],
        input,
        false,
        [],
        []
      );
      const address = pastWorkAffiliation.institution.address;
      expect(response).toEqual({
        formattedRelevantLocations: [],
        hasAboveTheLineAffiliations: false,
        hasAffiliationMatchingFilter: true,
        hasPastAffiliations: true,
        isSeeingPatients: 0,
        locationsToDisplay: [
          {
            data: [
              {
                childrenAffiliation: [],
                parentAffiliation: expect.objectContaining({
                  accuracyScore: pastWorkAffiliation.accuracyScore,
                  claimsNumbers: pastWorkAffiliation.claimsNumbers,
                  institution: pastWorkAffiliation.institution
                }),
                parentInstitution: pastWorkAffiliation.institution
              }
            ],
            highestClaimCount: pastWorkAffiliation.claimsNumbers,
            location: [address?.city, address?.region, address?.country].join(
              ", "
            ),
            region: {
              country: address?.country,
              region: address?.region
            }
          }
        ]
      });
    });

    it("should group child affiliations with parent", () => {
      const affiliationAdapterService = new AffiliationAdapterService();
      const input = generateKeywordSearchInput();
      const city = faker.address.city();
      const region = faker.address.state();
      const country = faker.address.country();
      const parentAffiliation = generateAffiliation({
        isPastAffiliation: false,
        accuracyScore: 1,
        institution: {
          id: faker.datatype.number(),
          name: faker.datatype.string(),
          type: faker.datatype.string(),
          orgTypes: [faker.datatype.string()],
          iol: {
            id: faker.datatype.number(),
            region: faker.address.state()
          },
          ultimateParent: undefined,
          address: {
            id: faker.datatype.number(),
            city,
            region,
            country
          }
        }
      });
      const childAffiliation1 = generateAffiliation({
        isPastAffiliation: false,
        accuracyScore: 1,
        institution: {
          id: faker.datatype.number(),
          name: faker.datatype.string(),
          type: faker.datatype.string(),
          orgTypes: [faker.datatype.string()],
          iol: {
            id: faker.datatype.number(),
            region: faker.address.state()
          },
          ultimateParent: parentAffiliation.institution,
          address: {
            id: faker.datatype.number(),
            city,
            region,
            country
          }
        }
      });
      const childAffiliation2 = generateAffiliation({
        isPastAffiliation: false,
        accuracyScore: 1,
        institution: {
          id: faker.datatype.number(),
          name: faker.datatype.string(),
          type: faker.datatype.string(),
          orgTypes: [faker.datatype.string()],
          iol: {
            id: faker.datatype.number(),
            region: faker.address.state()
          },
          ultimateParent: parentAffiliation.institution,
          address: {
            id: faker.datatype.number(),
            city,
            region,
            country
          }
        }
      });
      const response = affiliationAdapterService.searchTopAffiliations(
        [parentAffiliation, childAffiliation1, childAffiliation2],
        input,
        false,
        [],
        []
      );
      const address = parentAffiliation.institution.address;
      expect(response).toEqual({
        formattedRelevantLocations: [],
        hasAboveTheLineAffiliations: true,
        hasAffiliationMatchingFilter: true,
        hasPastAffiliations: false,
        isSeeingPatients: max([
          parentAffiliation.claimsNumbers || 0,
          childAffiliation1.claimsNumbers || 0,
          childAffiliation2.claimsNumbers || 0
        ]),
        locationsToDisplay: [
          {
            data: [
              {
                childrenAffiliation: expect.arrayContaining([
                  childAffiliation1,
                  childAffiliation2
                ]),
                parentAffiliation: expect.objectContaining({
                  accuracyScore: 1,
                  claimsNumbers: parentAffiliation.claimsNumbers,
                  institution: parentAffiliation.institution
                }),
                parentInstitution: parentAffiliation.institution
              }
            ],
            highestClaimCount: max([
              parentAffiliation.claimsNumbers || 0,
              childAffiliation1.claimsNumbers || 0,
              childAffiliation2.claimsNumbers || 0
            ]),
            location: [address?.city, address?.region, address?.country].join(
              ", "
            ),
            region: {
              country: address?.country,
              region: address?.region
            }
          }
        ]
      });
    });

    describe("location filter is applied", () => {
      describe("locationsToDisplay doesn't include best match affiliation", () => {
        it("should set relevant affiliation if zipCode filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const zipCodeFilterValue = faker.address.zipCode();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: zipCodeFilterValue
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.zipCode.values = [zipCodeFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should set relevant affiliation if city filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const cityFilterValue = faker.address.city();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: cityFilterValue,
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.city.values = [cityFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should set relevant affiliation if city_level_region filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const cityFilterValue = faker.address.city();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: faker.address.zipCode(),
                city_level_regions: [cityFilterValue]
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.city.values = [cityFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should set relevant affiliation if state filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const stateFilterValue = faker.datatype.string();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: stateFilterValue,
                country: faker.address.country(),
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.state.values = [stateFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should set relevant affiliation if state_level_region filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const stateFilterValue = faker.datatype.string();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: faker.address.zipCode(),
                state_level_regions: [stateFilterValue]
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.state.values = [stateFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should set relevant affiliation if country filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const countryFilterValue = faker.datatype.string();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: countryFilterValue,
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.country.values = [countryFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should set relevant affiliation if country_level_region filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const countryFilterValue = faker.datatype.string();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: faker.address.zipCode(),
                country_level_regions: [countryFilterValue]
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.country.values = [countryFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should set relevant affiliation if presentWorkInstitutions filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const institutionFilterValue = faker.datatype.string();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            accuracyScore: 0,
            institution: {
              id: faker.datatype.number(),
              name: institutionFilterValue,
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.presentWorkInstitutions.values = [
            institutionFilterValue
          ];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should set relevant affiliation if pastAndPresentWorkInstitutions filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const institutionFilterValue = faker.datatype.string();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            accuracyScore: 0,
            institution: {
              id: faker.datatype.number(),
              name: institutionFilterValue,
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.pastAndPresentWorkInstitutions.values = [
            institutionFilterValue
          ];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should set relevant affiliation if custom territory filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const matchedTerritoryAffiliationName = faker.datatype.string();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            accuracyScore: 0,
            institution: {
              id: faker.datatype.number(),
              name: matchedTerritoryAffiliationName,
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            [matchedTerritoryAffiliationName]
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });
        it("should set relevant affiliation if country territory filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const matchedTerritoryCountry = faker.datatype.string();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            accuracyScore: 0,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: matchedTerritoryCountry,
                postalCode: faker.address.zipCode()
              }
            }
          });

          const input = generateKeywordSearchInput();
          const mockTerritoryData = generateMockSavedTerritory({
            territoryData: {
              countries: [matchedTerritoryCountry]
            }
          });

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            [],
            [mockTerritoryData]
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });
        it("should set relevant affiliation if region territory filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const matchedTerritoryRegion = faker.datatype.string();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            accuracyScore: 0,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: matchedTerritoryRegion,
                country: faker.address.country(),
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();
          const mockTerritoryData = generateMockSavedTerritory({
            territoryData: {
              regions: [matchedTerritoryRegion]
            }
          });
          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            [],
            [mockTerritoryData]
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });
        it("should set relevant affiliation if zipcodes territory filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const matchedTerritoryPostalCode = faker.datatype.string();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true,
            accuracyScore: 0,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: matchedTerritoryPostalCode
              }
            }
          });
          const input = generateKeywordSearchInput();
          const mockTerritoryData = generateMockSavedTerritory({
            territoryData: {
              postalCodes: [matchedTerritoryPostalCode]
            }
          });
          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            [],
            [mockTerritoryData]
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: pastWorkAffiliation.accuracyScore,
                      claimsNumbers: pastWorkAffiliation.claimsNumbers,
                      institution: pastWorkAffiliation.institution
                    }),
                    parentInstitution: pastWorkAffiliation.institution
                  }
                ],
                highestClaimCount: pastWorkAffiliation.claimsNumbers,
                location: [
                  pastWorkAffiliation.institution.address?.city,
                  pastWorkAffiliation.institution.address?.region,
                  pastWorkAffiliation.institution.address?.country
                ].join(", "),
                region: {
                  country: pastWorkAffiliation.institution.address?.country,
                  region: pastWorkAffiliation.institution.address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });
        it("should set relevant affiliation of pastAndPresentWorkInstitutions over territory filter if both are applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const matchedTerritoryAffiliationName = faker.datatype.string();
          const institutionFilterValue = faker.datatype.string();
          const pastWorkAffiliationMatchingTerritoryFilter =
            generateAffiliation({
              isPastAffiliation: true,
              accuracyScore: 0,
              institution: {
                id: faker.datatype.number(),
                name: matchedTerritoryAffiliationName,
                type: faker.datatype.string(),
                orgTypes: [faker.datatype.string()],
                iol: {
                  id: faker.datatype.number(),
                  region: faker.address.state()
                },
                ultimateParent: undefined,
                address: {
                  id: faker.datatype.number(),
                  city: faker.address.city(),
                  region: faker.address.state(),
                  country: faker.address.country(),
                  postalCode: faker.address.zipCode()
                }
              }
            });
          const pastWorkAffiliationMatchingPastAndPresentFilter =
            generateAffiliation({
              isPastAffiliation: true,
              accuracyScore: 0,
              institution: {
                id: faker.datatype.number(),
                name: institutionFilterValue,
                type: faker.datatype.string(),
                orgTypes: [faker.datatype.string()],
                iol: {
                  id: faker.datatype.number(),
                  region: faker.address.state()
                },
                ultimateParent: undefined,
                address: {
                  id: faker.datatype.number(),
                  city: faker.address.city(),
                  region: faker.address.state(),
                  country: faker.address.country(),
                  postalCode: faker.address.zipCode()
                }
              }
            });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.pastAndPresentWorkInstitutions.values = [
            institutionFilterValue
          ];
          const response = affiliationAdapterService.searchTopAffiliations(
            [
              currentWorkAffiliation,
              pastWorkAffiliationMatchingPastAndPresentFilter,
              pastWorkAffiliationMatchingTerritoryFilter
            ],
            input,
            false,
            [],
            [matchedTerritoryAffiliationName]
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore:
                        pastWorkAffiliationMatchingPastAndPresentFilter.accuracyScore,
                      claimsNumbers:
                        pastWorkAffiliationMatchingPastAndPresentFilter.claimsNumbers,
                      institution:
                        pastWorkAffiliationMatchingPastAndPresentFilter.institution
                    }),
                    parentInstitution:
                      pastWorkAffiliationMatchingPastAndPresentFilter.institution
                  }
                ],
                highestClaimCount:
                  pastWorkAffiliationMatchingPastAndPresentFilter.claimsNumbers,
                location: [
                  pastWorkAffiliationMatchingPastAndPresentFilter.institution
                    .address?.city,
                  pastWorkAffiliationMatchingPastAndPresentFilter.institution
                    .address?.region,
                  pastWorkAffiliationMatchingPastAndPresentFilter.institution
                    .address?.country
                ].join(", "),
                region: {
                  country:
                    pastWorkAffiliationMatchingPastAndPresentFilter.institution
                      .address?.country,
                  region:
                    pastWorkAffiliationMatchingPastAndPresentFilter.institution
                      .address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });
        it("should set relevant affiliation of territory location filter over location filter if both are applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1
          });
          const matchedTerritoryAffiliationCountry = faker.datatype.string();
          const matchedLocationCountry = faker.datatype.string();
          const pastWorkAffiliationMatchingLocationFilter = generateAffiliation(
            {
              isPastAffiliation: true,
              accuracyScore: 0,
              institution: {
                id: faker.datatype.number(),
                name: faker.datatype.string(),
                type: faker.datatype.string(),
                orgTypes: [faker.datatype.string()],
                iol: {
                  id: faker.datatype.number(),
                  region: faker.address.state()
                },
                ultimateParent: undefined,
                address: {
                  id: faker.datatype.number(),
                  city: faker.address.city(),
                  region: faker.address.state(),
                  country: matchedLocationCountry,
                  postalCode: faker.address.zipCode()
                }
              }
            }
          );
          const pastWorkAffiliationMatchingTerritoryFilter =
            generateAffiliation({
              isPastAffiliation: true,
              accuracyScore: 0,
              institution: {
                id: faker.datatype.number(),
                name: faker.datatype.string(),
                type: faker.datatype.string(),
                orgTypes: [faker.datatype.string()],
                iol: {
                  id: faker.datatype.number(),
                  region: faker.address.state()
                },
                ultimateParent: undefined,
                address: {
                  id: faker.datatype.number(),
                  city: faker.address.city(),
                  region: faker.address.state(),
                  country: matchedTerritoryAffiliationCountry,
                  postalCode: faker.address.zipCode()
                }
              }
            });
          const input = generateKeywordSearchInput({
            suppliedFilters: generateFilters({
              country: {
                values: [matchedLocationCountry]
              }
            })
          });
          const mockTerritoryData = generateMockSavedTerritory({
            territoryData: {
              countries: [matchedTerritoryAffiliationCountry]
            }
          });

          const response = affiliationAdapterService.searchTopAffiliations(
            [
              currentWorkAffiliation,
              pastWorkAffiliationMatchingLocationFilter,
              pastWorkAffiliationMatchingTerritoryFilter
            ],
            input,
            false,
            [],
            [],
            [mockTerritoryData]
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore:
                        pastWorkAffiliationMatchingTerritoryFilter.accuracyScore,
                      claimsNumbers:
                        pastWorkAffiliationMatchingTerritoryFilter.claimsNumbers,
                      institution:
                        pastWorkAffiliationMatchingTerritoryFilter.institution
                    }),
                    parentInstitution:
                      pastWorkAffiliationMatchingTerritoryFilter.institution
                  }
                ],
                highestClaimCount:
                  pastWorkAffiliationMatchingTerritoryFilter.claimsNumbers,
                location: [
                  pastWorkAffiliationMatchingTerritoryFilter.institution.address
                    ?.city,
                  pastWorkAffiliationMatchingTerritoryFilter.institution.address
                    ?.region,
                  pastWorkAffiliationMatchingTerritoryFilter.institution.address
                    ?.country
                ].join(", "),
                region: {
                  country:
                    pastWorkAffiliationMatchingTerritoryFilter.institution
                      .address?.country,
                  region:
                    pastWorkAffiliationMatchingTerritoryFilter.institution
                      .address?.region
                }
              }
            ],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });
      });

      describe("locationsToDisplay includes best match affiliation", () => {
        it("should not set relevant affiliation if zipCode filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const zipCodeFilterValue = faker.address.zipCode();

          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: zipCodeFilterValue
              }
            }
          });
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.zipCode.values = [zipCodeFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: true,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });
        it("should not set relevant affiliation if city filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true
          });
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                regionCode: faker.address.zipCode()
              }
            }
          });
          const address = currentWorkAffiliation.institution.address;

          const cityFilterValue = [
            address?.city,
            address?.regionCode,
            address?.country
          ].join(", ");

          const input = generateKeywordSearchInput();
          input.suppliedFilters.city.values = [cityFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          expect(response).toEqual({
            formattedRelevantLocations: [],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: true,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should not set relevant affiliation if state filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true
          });
          const stateFilterValue = faker.datatype.string();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: stateFilterValue,
                country: faker.address.country(),
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.state.values = [stateFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: true,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should not set relevant affiliation if country filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true
          });
          const countryFilterValue = faker.datatype.string();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1,
            institution: {
              id: faker.datatype.number(),
              name: faker.datatype.string(),
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: countryFilterValue,
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.country.values = [countryFilterValue];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: true,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should not set relevant affiliation if presentWorkInstitutions filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true
          });
          const institutionFilterValue = faker.datatype.string();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1,
            institution: {
              id: faker.datatype.number(),
              name: institutionFilterValue,
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.presentWorkInstitutions.values = [
            institutionFilterValue
          ];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: true,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });

        it("should not set relevant affiliation if pastAndPresentWorkInstitutions filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true
          });
          const institutionFilterValue = faker.datatype.string();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1,
            institution: {
              id: faker.datatype.number(),
              name: institutionFilterValue,
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();
          input.suppliedFilters.pastAndPresentWorkInstitutions.values = [
            institutionFilterValue
          ];

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            []
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: true,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });
        it("should not set relevant affiliation if territory filter is applied", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const pastWorkAffiliation = generateAffiliation({
            isPastAffiliation: true
          });
          const matchedTerritoryAffiliationName = faker.datatype.string();
          const currentWorkAffiliation = generateAffiliation({
            isPastAffiliation: false,
            accuracyScore: 1,
            institution: {
              id: faker.datatype.number(),
              name: matchedTerritoryAffiliationName,
              type: faker.datatype.string(),
              orgTypes: [faker.datatype.string()],
              iol: {
                id: faker.datatype.number(),
                region: faker.address.state()
              },
              ultimateParent: undefined,
              address: {
                id: faker.datatype.number(),
                city: faker.address.city(),
                region: faker.address.state(),
                country: faker.address.country(),
                postalCode: faker.address.zipCode()
              }
            }
          });
          const input = generateKeywordSearchInput();

          const response = affiliationAdapterService.searchTopAffiliations(
            [currentWorkAffiliation, pastWorkAffiliation],
            input,
            false,
            [],
            [matchedTerritoryAffiliationName]
          );
          const address = currentWorkAffiliation.institution.address;
          expect(response).toEqual({
            formattedRelevantLocations: [],
            hasAboveTheLineAffiliations: true,
            hasAffiliationMatchingFilter: true,
            hasPastAffiliations: false,
            isSeeingPatients: currentWorkAffiliation.claimsNumbers,
            locationsToDisplay: [
              {
                data: [
                  {
                    childrenAffiliation: [],
                    parentAffiliation: expect.objectContaining({
                      accuracyScore: 1,
                      claimsNumbers: currentWorkAffiliation.claimsNumbers,
                      institution: currentWorkAffiliation.institution
                    }),
                    parentInstitution: currentWorkAffiliation.institution
                  }
                ],
                highestClaimCount: currentWorkAffiliation.claimsNumbers,
                location: [
                  address?.city,
                  address?.region,
                  address?.country
                ].join(", "),
                region: {
                  country: address?.country,
                  region: address?.region
                }
              }
            ]
          });
        });
      });
    });

    describe("translateTaiwan", () => {
      it("should translate TAIWAN_PROVINCE_OF_CHINA to TAIWAN if translateTaiwan is true", () => {
        const affiliationAdapterService = new AffiliationAdapterService();
        const input = generateKeywordSearchInput();
        const currentWorkAffiliation = generateAffiliation({
          isPastAffiliation: false,
          accuracyScore: 1,
          institution: {
            id: faker.datatype.number(),
            name: faker.datatype.string(),
            type: faker.datatype.string(),
            orgTypes: [faker.datatype.string()],
            iol: {
              id: faker.datatype.number(),
              region: faker.address.state()
            },
            ultimateParent: undefined,
            address: {
              id: faker.datatype.number(),
              city: faker.address.city(),
              region: faker.address.state(),
              country: TAIWAN_PROVINCE_OF_CHINA,
              postalCode: faker.address.zipCode()
            }
          }
        });
        const response = affiliationAdapterService.searchTopAffiliations(
          [currentWorkAffiliation],
          input,
          false,
          [],
          []
        );
        const address = currentWorkAffiliation.institution.address;
        expect(response).toEqual({
          formattedRelevantLocations: [],
          hasAboveTheLineAffiliations: true,
          hasAffiliationMatchingFilter: true,
          hasPastAffiliations: false,
          isSeeingPatients: currentWorkAffiliation.claimsNumbers,
          locationsToDisplay: [
            {
              data: [
                {
                  childrenAffiliation: [],
                  parentAffiliation: expect.objectContaining({
                    accuracyScore: 1,
                    claimsNumbers: currentWorkAffiliation.claimsNumbers,
                    institution: currentWorkAffiliation.institution
                  }),
                  parentInstitution: currentWorkAffiliation.institution
                }
              ],
              highestClaimCount: currentWorkAffiliation.claimsNumbers,
              location: [address?.city, address?.region, TAIWAN].join(", "),
              region: {
                country: TAIWAN,
                region: address?.region
              }
            }
          ]
        });
      });

      it("should not translate TAIWAN_PROVINCE_OF_CHINA to TAIWAN if translateTaiwan is false", () => {
        const affiliationAdapterService = new AffiliationAdapterService();
        const input = generateKeywordSearchInput();
        input.projectFeatures.translateTaiwan = false;
        const currentWorkAffiliation = generateAffiliation({
          isPastAffiliation: false,
          accuracyScore: 1,
          institution: {
            id: faker.datatype.number(),
            name: faker.datatype.string(),
            type: faker.datatype.string(),
            orgTypes: [faker.datatype.string()],
            iol: {
              id: faker.datatype.number(),
              region: faker.address.state()
            },
            ultimateParent: undefined,
            address: {
              id: faker.datatype.number(),
              city: faker.address.city(),
              region: faker.address.state(),
              country: TAIWAN_PROVINCE_OF_CHINA,
              postalCode: faker.address.zipCode()
            }
          }
        });
        const response = affiliationAdapterService.searchTopAffiliations(
          [currentWorkAffiliation],
          input,
          false,
          [],
          []
        );
        const address = currentWorkAffiliation.institution.address;
        expect(response).toEqual({
          formattedRelevantLocations: [],
          hasAboveTheLineAffiliations: true,
          hasAffiliationMatchingFilter: true,
          hasPastAffiliations: false,
          isSeeingPatients: currentWorkAffiliation.claimsNumbers,
          locationsToDisplay: [
            {
              data: [
                {
                  childrenAffiliation: [],
                  parentAffiliation: expect.objectContaining({
                    accuracyScore: 1,
                    claimsNumbers: currentWorkAffiliation.claimsNumbers,
                    institution: currentWorkAffiliation.institution
                  }),
                  parentInstitution: currentWorkAffiliation.institution
                }
              ],
              highestClaimCount: currentWorkAffiliation.claimsNumbers,
              location: [
                address?.city,
                address?.region,
                TAIWAN_PROVINCE_OF_CHINA
              ].join(", "),
              region: {
                country: TAIWAN_PROVINCE_OF_CHINA,
                region: address?.region
              }
            }
          ]
        });
      });
    });

    describe("H1DN only HCPs", () => {
      it("should set current affiliations as affiliation to display in response", () => {
        const affiliationAdapterService = new AffiliationAdapterService();
        const input = generateKeywordSearchInput();
        const currentWorkAffiliation = generateAffiliation({
          isPastAffiliation: false,
          type: "Work Affiliation"
        });
        const pastWorkAffiliation = generateAffiliation({
          isPastAffiliation: true,
          type: "Work Affiliation"
        });
        const response = affiliationAdapterService.searchTopAffiliations(
          [currentWorkAffiliation, pastWorkAffiliation],
          input,
          true,
          [],
          []
        );
        const address = currentWorkAffiliation.institution.address;
        expect(response).toEqual({
          affiliation: {
            titles: currentWorkAffiliation.titles,
            locality: address?.city,
            institutionName: currentWorkAffiliation?.institution.name,
            region: address?.region,
            country: address?.country,
            postalCode: address?.postalCode
          },
          pastAffiliationsText: `${pastWorkAffiliation.titles.join(", ")} at ${
            pastWorkAffiliation.institution.name
          }`,
          pastWorkAffiliations: {
            length: 1
          }
        });
      });

      it("should set undefined as affiliation to display in response when no current affiliation is present", () => {
        const affiliationAdapterService = new AffiliationAdapterService();
        const input = generateKeywordSearchInput();

        const pastWorkAffiliation = generateAffiliation({
          isPastAffiliation: true,
          type: "Work Affiliation"
        });
        const response = affiliationAdapterService.searchTopAffiliations(
          [pastWorkAffiliation],
          input,
          true,
          [],
          []
        );
        expect(response).toEqual({
          affiliation: undefined,
          pastAffiliationsText: `${pastWorkAffiliation.titles.join(", ")} at ${
            pastWorkAffiliation.institution.name
          }`,
          pastWorkAffiliations: {
            length: 1
          }
        });
      });
    });

    describe("No affiliations", () => {
      it("should use first location info if no affiliations are present", () => {
        const affiliationAdapterService = new AffiliationAdapterService();
        const input = generateKeywordSearchInput();
        const location = {
          languageCode: "eng",
          city_eng: faker.address.cityName(),
          state_eng: faker.address.cityName(),
          country_eng: faker.address.cityName(),
          zipCode5_eng: faker.address.zipCode(),
          city_cmn: "",
          state_cmn: "",
          country_cmn: "",
          zipCode5_cmn: "",
          city_jpn: "",
          state_jpn: "",
          country_jpn: "",
          zipCode5_jpn: "",
          token: "",
          zipCodeFull: "",
          stateCode: ""
        };
        const anotherLocation = {
          languageCode: "eng",
          city_eng: faker.address.cityName(),
          state_eng: faker.address.state(),
          country_eng: faker.address.country(),
          zipCode5_eng: faker.address.zipCode(),
          city_cmn: "",
          state_cmn: "",
          country_cmn: "",
          zipCode5_cmn: "",
          city_jpn: "",
          state_jpn: "",
          country_jpn: "",
          zipCode5_jpn: "",
          token: "",
          zipCodeFull: "",
          stateCode: ""
        };
        const response = affiliationAdapterService.searchTopAffiliations(
          [],
          input,
          false,
          [location, anotherLocation],
          []
        );
        expect(response).toEqual({
          locationsToDisplay: [
            {
              location: [
                location.city_eng,
                location.state_eng,
                location.country_eng
              ].join(", "),
              highestClaimCount: 0,
              data: []
            }
          ],
          hasAboveTheLineAffiliations: false,
          hasAffiliationMatchingFilter: false,
          hasPastAffiliations: false,
          isSeeingPatients: 0,
          formattedRelevantLocations: []
        });
      });

      describe("translateTaiwan", () => {
        it("should translate TAIWAN_PROVINCE_OF_CHINA to TAIWAN if translateTaiwan is true", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const input = generateKeywordSearchInput();
          const location = {
            languageCode: "jpn",
            city_jpn: faker.address.cityName(),
            state_jpn: faker.address.state(),
            country_jpn: TAIWAN_PROVINCE_OF_CHINA,
            zipCode5_jpn: faker.address.zipCode(),
            city_eng: "",
            state_eng: "",
            country_eng: "",
            zipCode5_eng: "",
            city_cmn: "",
            state_cmn: "",
            country_cmn: "",
            zipCode5_cmn: "",
            token: "",
            zipCodeFull: "",
            stateCode: ""
          };
          const response = affiliationAdapterService.searchTopAffiliations(
            [],
            input,
            false,
            [location],
            []
          );
          expect(response).toEqual({
            locationsToDisplay: [
              {
                location: [location.city_jpn, location.state_jpn, TAIWAN].join(
                  ", "
                ),
                highestClaimCount: 0,
                data: []
              }
            ],
            hasAboveTheLineAffiliations: false,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: 0,
            formattedRelevantLocations: []
          });
        });

        it("should not translate TAIWAN_PROVINCE_OF_CHINA to TAIWAN if translateTaiwan is false", () => {
          const affiliationAdapterService = new AffiliationAdapterService();
          const input = generateKeywordSearchInput();
          input.projectFeatures.translateTaiwan = false;
          const location = {
            languageCode: "cmn",
            city_cmn: faker.address.cityName(),
            state_cmn: faker.address.state(),
            country_cmn: TAIWAN_PROVINCE_OF_CHINA,
            zipCode5_cmn: faker.address.zipCode(),
            city_eng: "",
            state_eng: "",
            country_eng: "",
            zipCode5_eng: "",
            city_jpn: "",
            state_jpn: "",
            country_jpn: "",
            zipCode5_jpn: "",
            token: "",
            zipCodeFull: "",
            stateCode: ""
          };
          const response = affiliationAdapterService.searchTopAffiliations(
            [],
            input,
            false,
            [location],
            []
          );
          expect(response).toEqual({
            locationsToDisplay: [
              {
                location: [
                  location.city_cmn,
                  location.state_cmn,
                  TAIWAN_PROVINCE_OF_CHINA
                ].join(", "),
                highestClaimCount: 0,
                data: []
              }
            ],
            hasAboveTheLineAffiliations: false,
            hasAffiliationMatchingFilter: false,
            hasPastAffiliations: false,
            isSeeingPatients: 0,
            formattedRelevantLocations: []
          });
        });
      });
    });
  });

  describe("searchTopTLAffiliations", () => {
    let affiliationAdapterService: AffiliationAdapterService;
    let input: KeywordSearchInput;

    beforeEach(() => {
      affiliationAdapterService = new AffiliationAdapterService();
      input = generateKeywordSearchInput();
    });

    it("should return an object containing locationsToDisplay and formattedRelevantLocations", () => {
      const affiliations = [
        generateAffiliation({
          isPastAffiliation: false,
          tlAccuracyScore: 1.0,
          institution: {
            ...generateAffiliation().institution,
            type: "Hospital"
          },
          otherSignals: [
            { name: "total_claims_at_affiliation", value: "2000" },
            { name: "number_of_trials_at_affiliation", value: "50" },
            { name: "number_of_trials_at_organization", value: "100" },
            { name: "number_of_patients_at_affiliation", value: "1000" }
          ]
        }),
        generateAffiliation({
          isPastAffiliation: false,
          tlAccuracyScore: 1.0,
          institution: {
            ...generateAffiliation().institution,
            type: "Research Lab"
          },
          otherSignals: [
            { name: "total_claims_at_affiliation", value: "2500" },
            { name: "number_of_trials_at_affiliation", value: "60" },
            { name: "number_of_trials_at_organization", value: "110" },
            { name: "number_of_patients_at_affiliation", value: "1500" }
          ]
        }),
        generateAffiliation({
          isPastAffiliation: false,
          tlAccuracyScore: 1.0,
          institution: {
            ...generateAffiliation().institution,
            type: "Private Practice / Physician Group"
          },
          otherSignals: [
            { name: "total_claims_at_affiliation", value: "1800" },
            { name: "number_of_trials_at_affiliation", value: "40" },
            { name: "number_of_trials_at_organization", value: "90" },
            { name: "number_of_patients_at_affiliation", value: "800" }
          ]
        })
      ];

      const response = affiliationAdapterService.searchTopTLAffiliations(
        affiliations,
        input,
        false,
        [],
        []
      ) as FormattedTopTLAffiliations;

      expect(response).toBeDefined();
      expect(response).toHaveProperty("locationsToDisplay");
      expect(response).toHaveProperty("formattedRelevantLocations");

      // Check that locationsToDisplay is an array
      expect(Array.isArray(response.locationsToDisplay)).toBe(true);
      expect(response.locationsToDisplay?.length).toBe(affiliations.length);
    });

    it("should handle affiliations with missing otherSignals gracefully", () => {
      const affiliations = [
        generateAffiliation({
          isPastAffiliation: false,
          tlAccuracyScore: 1.0,
          otherSignals: [],
          institution: {
            ...generateAffiliation().institution,
            type: "Hospital"
          }
        })
      ];

      const response = affiliationAdapterService.searchTopTLAffiliations(
        affiliations,
        input,
        false,
        [],
        []
      ) as FormattedTopTLAffiliations;

      expect(response).toBeDefined();
      expect(response).toHaveProperty("locationsToDisplay");
      expect(Array.isArray(response.locationsToDisplay)).toBe(true);
      expect(response.locationsToDisplay.length).toBe(affiliations.length);

      const location = response.locationsToDisplay[0];
      expect(location.data[0].parentAffiliation).toMatchObject({
        tlAffiliationScore: 0.2 // only affiliation type scores
      });
    });

    it("should correctly normalize and compute scores when min equals max", () => {
      // All affiliations have the same otherSignals values
      const affiliations = [
        generateAffiliation({
          isPastAffiliation: false,
          tlAccuracyScore: 1.0,
          institution: {
            ...generateAffiliation().institution,
            type: "Hospital"
          },
          otherSignals: [
            { name: "total_claims_at_affiliation", value: "2000" },
            { name: "number_of_trials_at_affiliation", value: "50" },
            { name: "number_of_trials_at_organization", value: "100" },
            { name: "number_of_patients_at_affiliation", value: "1000" }
          ]
        }),
        generateAffiliation({
          isPastAffiliation: false,
          tlAccuracyScore: 1.0,
          institution: {
            ...generateAffiliation().institution,
            type: "Hospital"
          },
          otherSignals: [
            { name: "total_claims_at_affiliation", value: "2000" },
            { name: "number_of_trials_at_affiliation", value: "50" },
            { name: "number_of_trials_at_organization", value: "100" },
            { name: "number_of_patients_at_affiliation", value: "1000" }
          ]
        })
      ];

      const response = affiliationAdapterService.searchTopTLAffiliations(
        affiliations,
        input,
        false,
        [],
        []
      ) as FormattedTopTLAffiliations;

      expect(response).toBeDefined();
      expect(response).toHaveProperty("locationsToDisplay");
      expect(response.locationsToDisplay.length).toBe(affiliations.length);

      expect(response.locationsToDisplay[0].tlAffiliationScore).toBe(0.2);
    });

    it("should correctly handle affiliations with ultimate parents", () => {
      const ultimateParentInstitution: AffiliationInstitution = {
        id: faker.datatype.number(),
        name: "Ultimate Parent Institution",
        type: "Hospital",
        address: {
          id: faker.datatype.number(),
          city: faker.address.city(),
          region: faker.address.state(),
          country: faker.address.country()
        },
        orgTypes: [faker.datatype.string()]
      };

      const childAffiliations = [
        generateAffiliation({
          isPastAffiliation: false,
          tlAccuracyScore: 1.0,
          institution: {
            ...generateAffiliation().institution,
            ultimateParent: ultimateParentInstitution,
            type: "Hospital"
          },
          otherSignals: [
            { name: "total_claims_at_affiliation", value: "2200" },
            { name: "number_of_trials_at_affiliation", value: "55" },
            { name: "number_of_trials_at_organization", value: "105" },
            { name: "number_of_patients_at_affiliation", value: "1100" }
          ]
        })
      ];

      const response = affiliationAdapterService.searchTopTLAffiliations(
        childAffiliations,
        input,
        false,
        [],
        []
      ) as FormattedTopTLAffiliations;

      expect(response).toBeDefined();
      expect(response).toHaveProperty("locationsToDisplay");
      expect(response.locationsToDisplay.length).toBe(childAffiliations.length);
      expect(response.locationsToDisplay[0].data[0].parentInstitution.id).toBe(
        ultimateParentInstitution.id
      );
      expect(
        response.locationsToDisplay[0].data[0].childrenAffiliation.length
      ).toBe(1);
    });

    it("should handle affiliations without address", () => {
      const affiliation = generateAffiliation({
        isPastAffiliation: false,
        institution: {
          ...generateAffiliation().institution,
          address: undefined // Missing address
        }
      });

      const response = affiliationAdapterService.searchTopTLAffiliations(
        [affiliation],
        input,
        false,
        [],
        []
      ) as FormattedTopTLAffiliations;

      expect(response.locationsToDisplay.length).toBe(0); // Should be empty since affiliation has no address
    });

    it("should return the highest affiliation score in locationsToDisplay", () => {
      // All affiliations have the same otherSignals values
      const affiliations = [
        generateAffiliation({
          isPastAffiliation: false,
          institution: {
            ...generateAffiliation().institution,
            type: "Private Practice / Physician Group"
          },
          tlAccuracyScore: 1.0,
          otherSignals: [
            { name: "total_claims_at_affiliation", value: "2000" },
            { name: "number_of_trials_at_affiliation", value: "50" },
            { name: "number_of_trials_at_organization", value: "100" },
            { name: "number_of_patients_at_affiliation", value: "1000" }
          ]
        }),
        generateAffiliation({
          isPastAffiliation: false,
          institution: {
            ...generateAffiliation().institution,
            type: "Hospital"
          },
          tlAccuracyScore: 1.0,
          otherSignals: [
            { name: "total_claims_at_affiliation", value: "50" },
            { name: "number_of_trials_at_affiliation", value: "16" },
            { name: "number_of_trials_at_organization", value: "75" },
            { name: "number_of_patients_at_affiliation", value: "750" }
          ]
        }),
        generateAffiliation({
          isPastAffiliation: false,
          institution: {
            ...generateAffiliation().institution,
            type: "Other"
          },
          tlAccuracyScore: 1.0,
          otherSignals: [
            { name: "total_claims_at_affiliation", value: "300" },
            { name: "number_of_trials_at_affiliation", value: "22" },
            { name: "number_of_trials_at_organization", value: "80" },
            { name: "number_of_patients_at_affiliation", value: "1200" }
          ]
        })
      ];

      const response = affiliationAdapterService.searchTopTLAffiliations(
        affiliations,
        input,
        false,
        [],
        []
      ) as FormattedTopTLAffiliations;

      expect(response).toBeDefined();
      expect(response).toHaveProperty("locationsToDisplay");
      expect(response.locationsToDisplay.length).toBe(affiliations.length);
      expect(response.locationsToDisplay[0].tlAffiliationScore).toBe(
        0.6588888888888889
      );
    });
    it("should set institutionHasTrials to true when an affiliations otherSignals includes number_of_trials_at_organization", () => {
      const affiliationAdapterService = new AffiliationAdapterService();
      const input = generateKeywordSearchInput();

      // create a child affiliation
      const currentWorkAffiliation = generateAffiliation({
        isPastAffiliation: false,
        tlAccuracyScore: 1,
        institution: {
          ...generateAffiliation().institution,
          ultimateParent: {
            id: 1234,
            orgTypes: ["Hospital"]
          }
        },
        otherSignals: [
          {
            name: "number_of_trials_at_organization",
            value: "12"
          }
        ]
      });

      const response = affiliationAdapterService.searchTopTLAffiliations(
        [currentWorkAffiliation],
        input,
        false,
        [],
        []
      ) as FormattedTopTLAffiliations;
      expect(
        response.locationsToDisplay[0].data[0].childrenAffiliation[0]
          .institutionHasTrials
      ).toBe(true);
    });
    it("should set institutionHasTrials to false when an affiliations otherSignals does not include number_of_trials_at_organization", () => {
      const affiliationAdapterService = new AffiliationAdapterService();
      const input = generateKeywordSearchInput();

      const currentWorkAffiliation = generateAffiliation({
        isPastAffiliation: false,
        institution: {
          ...generateAffiliation().institution,
          ultimateParent: {
            id: 12345,
            orgTypes: ["University System"]
          }
        },
        tlAccuracyScore: 1,
        otherSignals: [
          {
            name: "number_of_patients_at_affiliation",
            value: "1200"
          }
        ]
      });

      const response = affiliationAdapterService.searchTopTLAffiliations(
        [currentWorkAffiliation],
        input,
        false,
        [],
        []
      ) as FormattedTopTLAffiliations;

      expect(
        response.locationsToDisplay[0].data[0].childrenAffiliation[0]
          .institutionHasTrials
      ).toBe(false);
    });
  });
});
