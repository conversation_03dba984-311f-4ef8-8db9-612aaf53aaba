import {
  createMockInstance,
  generateKeywordSearchFeatureFlags
} from "../util/TestUtils";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { EntitySearchProcessorService } from "./EntitySearchProcessorService";

import {
  EntitiesV2,
  LocationsV2,
  Claims,
  TrialInfo,
  QueryUnderstandingServiceResponse
} from "../proto/query_understanding_service_pb";
import { KeywordSearchFeatureFlags } from "./KeywordSearchResourceServiceRewrite";
import { ElasticSearchService } from "./ElasticSearchService";
import { ConfigService } from "./ConfigService";
import {
  Apps,
  FilterInterface,
  KeywordSearchInput,
  WeightedSortBy
} from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";

export const getEmptyKeywordSearchFilters = (): FilterInterface => {
  return {
    dateRangePicker: {
      // TODO: figure out why someone decided this can't be nullable
      min: 0,
      max: null,
      active: false
    },
    designations: {
      values: []
    },
    designationsExclusion: {
      values: []
    },
    npi: {
      values: []
    },
    institutionType: {
      values: []
    },
    specialty: {
      values: []
    },
    specialtyExclusion: {
      values: []
    },
    country: {
      values: []
    },
    excludeCountry: {
      values: []
    },
    state: {
      values: []
    },
    excludeState: {
      values: []
    },
    city: {
      values: []
    },
    excludeCity: {
      values: []
    },
    zipCode: {
      values: []
    },
    excludeZipCode: {
      values: []
    },
    institution: {
      values: []
    },
    presentWorkInstitutions: {
      values: []
    },
    presentWorkInstitutionsExclusion: {
      values: []
    },
    pastAndPresentWorkInstitutions: {
      values: []
    },
    studentInstitutions: {
      values: []
    },
    studentInstitutionsExclusion: {
      values: []
    },
    graduationYearRange: {
      min: null,
      max: null
    },
    publications: {
      minCount: {
        value: null
      },
      maxCount: {
        value: null
      },
      socialMediaMinCount: {
        value: null
      },
      socialMediaMaxCount: {
        value: null
      },
      journal: {
        values: []
      },
      type: {
        values: []
      },
      isFirstOrder: {
        value: null
      },
      isLastOrder: {
        value: null
      },
      publicationDate: {
        value: null
      }
    },
    publicationsExclusion: {
      journal: {
        values: []
      },
      type: {
        values: []
      }
    },
    tags: {
      name: {
        values: []
      },
      publicTagIds: [],
      privateTagIds: [],
      programmaticTagIds: []
    },
    exclusionTags: {
      publicTagIds: [],
      privateTagIds: [],
      programmaticTagIds: []
    },
    claims: {
      diagnosesICDMinCount: {
        value: null
      },
      diagnosesICDMaxCount: {
        value: null
      },
      diagnosesICD: {
        values: []
      },
      genericNames: {
        values: []
      },
      drugClasses: {
        values: []
      },
      brandNames: {
        values: []
      },
      brandNameOrGeneric: { values: [] },
      prescriptionsMinCount: {
        value: null
      },
      prescriptionsMaxCount: {
        value: null
      },
      prescriptionsTimeFrame: {
        value: null
      },
      proceduresCPTMinCount: {
        value: null
      },
      proceduresCPTMaxCount: {
        value: null
      },
      proceduresCPT: {
        values: []
      },
      proceduresHCPCMinCount: {
        value: null
      },
      proceduresHCPC: {
        values: []
      },
      timeFrame: {
        value: null
      },
      showUniquePatients: {
        value: null
      },
      ccsr: {
        values: []
      },
      ccsrPx: {
        values: []
      }
    },
    exclusionClaims: {
      diagnosesICD: {
        values: []
      },
      proceduresCPT: {
        values: []
      },
      proceduresHCPC: {
        values: []
      },
      genericNames: {
        values: []
      },
      drugClasses: {
        values: []
      },
      brandNames: {
        values: []
      },
      ccsr: {
        values: []
      }
    },
    trials: {
      minCount: {
        value: null
      },
      maxCount: {
        value: null
      },
      status: {
        values: []
      },
      phase: {
        values: []
      },
      studyType: {
        values: []
      },
      id: {
        values: []
      },
      funderType: {
        values: []
      },
      sponsor: {
        values: []
      },
      sponsorType: {
        values: []
      },
      timeFrame: {
        min: null,
        max: null,
        key: "max"
      },
      biomarkers: {
        values: []
      }
    },
    trialsExclusion: {
      status: {
        values: []
      },
      phase: {
        values: []
      },
      studyType: {
        values: []
      },
      sponsor: {
        values: []
      },
      sponsorType: {
        values: []
      }
    },
    congresses: {
      minCount: {
        value: null
      },
      maxCount: {
        value: null
      },
      name: {
        values: []
      },
      type: {
        values: []
      },
      organizerName: {
        values: []
      },
      sessionType: {
        values: []
      },
      timeFrame: {
        value: null
      }
    },
    congressesExclusion: {
      name: {
        values: []
      },
      organizerName: {
        values: []
      }
    },
    engagements: {
      minCount: {
        value: null
      }
    },
    grants: {
      minAmount: {
        value: null
      },
      funder: {
        values: []
      }
    },
    payments: {
      minAmount: {
        value: null
      },
      maxAmount: {
        value: null
      },
      company: {
        values: []
      },
      drugOrDevice: {
        values: []
      },
      fundingType: {
        values: []
      },
      category: {
        values: []
      }
    },
    paymentsExclusion: {
      company: {
        values: []
      },
      drugOrDevice: {
        values: []
      },
      fundingType: {
        values: []
      },
      category: {
        values: []
      }
    },
    referrals: {
      serviceLine: {
        values: []
      },
      minReferralsReceived: {
        value: null
      },
      maxReferralsReceived: {
        value: null
      },
      minReferralsSent: {
        value: null
      },
      maxReferralsSent: {
        value: null
      }
    },
    referralsExclusion: {
      serviceLine: {
        values: []
      }
    },
    patientsDiversity: {
      ageRange: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      },
      raceMix: {
        thresholdValue: []
      }
    },
    patientsDiversityExclusion: {
      ageRange: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      }
    },
    providerDiversity: {
      languagesSpoken: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      }
    },
    providerDiversityExclusion: {
      languagesSpoken: {
        values: []
      },
      sex: {
        values: []
      },
      race: {
        values: []
      }
    },
    hasLinkedin: {
      value: null
    },
    hasTwitter: {
      value: null
    },
    hasCTMSData: {
      value: null
    },
    isFacultyOpinionsMember: {
      value: null
    },
    geoBoundingBox: {
      value: null
    },
    digitalLeader: {
      value: null
    },
    geoDistance: {
      value: null
    },
    geoShape: {
      value: null
    },
    peopleIds: undefined,
    h1dnIds: undefined,
    indications: {
      values: []
    },
    societyAffiliations: {
      values: []
    }
  };
};

export function generateFilters(
  overrides: Partial<FilterInterface> = {}
): FilterInterface {
  const baseFilters: FilterInterface = getEmptyKeywordSearchFilters();

  return { ...baseFilters, ...overrides };
}

export function generateStandardWeightedSortBy(
  overrides: Partial<WeightedSortBy> = {}
): WeightedSortBy {
  const baseSortBy = {
    publication: faker.datatype.number(),
    microBloggingCount: faker.datatype.number(),
    citation: faker.datatype.number(),
    trial: faker.datatype.number(),
    congress: faker.datatype.number(),
    payment: faker.datatype.number(),
    diagnoses: faker.datatype.number(),
    procedures: faker.datatype.number(),
    prescriptions: faker.datatype.number(),
    referralsReceived: faker.datatype.number({ min: 0.0001 }),
    referralsSent: faker.datatype.number({ min: 0.0001 }),
    grant: faker.datatype.number(),
    patientsDiversityRank: 0,
    h1DefaultRank: 0,
    twitterFollowersCount: 0,
    twitterTweetCount: 0,
    age: 0
  };

  return { ...baseSortBy, ...overrides };
}

export function generateKeywordSearchInput(
  overrides: Partial<KeywordSearchInput> = {}
): KeywordSearchInput {
  const page = {
    from: faker.datatype.number(),
    size: faker.datatype.number()
  };
  const userId = faker.datatype.uuid();
  const projectId = faker.datatype.string();
  const query = faker.datatype.string();
  const suppliedFilters = generateFilters();
  const sortBy = generateStandardWeightedSortBy();
  const projectFeatures = {
    advancedOperators: true,
    claims: true,
    referrals: true,
    engagementsV2: faker.datatype.boolean(),
    translateTaiwan: true
  };
  const language = "eng";
  const app = Apps.TRIAL_LANDSCAPE;

  const baseInput = {
    userId,
    language,
    page,
    projectId,
    query,
    suppliedFilters,
    sortBy,
    projectFeatures,
    app
  };

  return { ...baseInput, ...overrides };
}

describe("EntitySearchProcessorService", () => {
  let queryUnderstandingServiceClient: jest.Mocked<QueryUnderstandingServiceClient>;
  let elasticSearchService: jest.Mocked<ElasticSearchService>;
  let entitySearchProcessorService: EntitySearchProcessorService;
  let keywordSearchFeatureFlags: KeywordSearchFeatureFlags;

  beforeEach(() => {
    const configService = createMockInstance(ConfigService);
    queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );
    elasticSearchService = createMockInstance(ElasticSearchService);

    // Mock the msearch method to return empty responses by default
    elasticSearchService.msearch.mockResolvedValue({
      took: 5,
      responses: []
    });

    entitySearchProcessorService = new EntitySearchProcessorService(
      configService,
      queryUnderstandingServiceClient,
      elasticSearchService
    );

    keywordSearchFeatureFlags = generateKeywordSearchFeatureFlags({
      enableLlmQueryParserForSearch: true
    });
  });

  // Helper function to create mock msearch responses
  function createMockMsearchResponse(responses: any[] = []) {
    return {
      took: 5,
      responses: responses.map((response) => {
        if (response.error) {
          return response; // Return error responses as-is
        }
        return {
          hits: {
            total: { value: response.hits?.length || 0, relation: "eq" },
            hits: response.hits || []
          }
        };
      })
    };
  }

  // Helper function to create mock nested query response
  function createMockNestedHit(path: string, field: string, values: string[]) {
    const fieldName = field.split(".").pop();
    return {
      _id: "test-id",
      _source: {},
      inner_hits: {
        [path]: {
          hits: {
            hits: values.map((value) => ({
              _source: { [fieldName!]: value }
            }))
          }
        }
      }
    };
  }

  // Helper function to create mock non-nested query response
  // function
  // createMockNonNestedHit(field: string, values: string[]) {
  //   return {
  //     _id: "test-id",
  //     _source: { [field]: values }
  //   };
  // }

  function createMockEntitiesV2Response(
    searchFor = "",
    indicationList: string[] = [],
    specialtyList: string[] = [],
    workInstitutionList: string[] = [],
    countryList: string[] = [],
    stateList: string[] = [],
    cityList: string[] = [],
    diagnosesClaimsList: string[] = [],
    proceduresClaimsList: string[] = [],
    minDiagnosesClaimCount: number | null = null,
    maxDiagnosesClaimCount: number | null = null,
    minProceduresClaimCount: number | null = null,
    maxProceduresClaimCount: number | null = null,
    claimsTimeframe: string | null = null,
    trialMinCount: number | null = null,
    trialMaxCount: number | null = null,
    trialStatus: string | null = null,
    trialPhase: string | null = null
  ): QueryUnderstandingServiceResponse {
    const response = new QueryUnderstandingServiceResponse();

    const entitiesV2 = new EntitiesV2();

    entitiesV2.setSearchFor(searchFor);
    entitiesV2.setIndicationList(indicationList);
    entitiesV2.setSpecialtyList(specialtyList);
    entitiesV2.setWorkInstitutionList(workInstitutionList);

    // Set locations if any location data is provided
    if (countryList.length > 0 || stateList.length > 0 || cityList.length > 0) {
      const locationsV2 = new LocationsV2();
      locationsV2.setCountryList(countryList);
      locationsV2.setStateList(stateList);
      locationsV2.setCityList(cityList);
      entitiesV2.setLocations(locationsV2);
    }

    // Set claims if any claims data is provided
    if (
      diagnosesClaimsList.length > 0 ||
      proceduresClaimsList.length > 0 ||
      minDiagnosesClaimCount !== null ||
      maxDiagnosesClaimCount !== null ||
      minProceduresClaimCount !== null ||
      maxProceduresClaimCount !== null ||
      claimsTimeframe !== null
    ) {
      const claims = new Claims();
      claims.setDiagnosesList(diagnosesClaimsList);
      claims.setProceduresList(proceduresClaimsList);

      if (minDiagnosesClaimCount !== null) {
        claims.setMinDiagnosesClaimCount(minDiagnosesClaimCount);
      }
      if (maxDiagnosesClaimCount !== null) {
        claims.setMaxDiagnosesClaimCount(maxDiagnosesClaimCount);
      }
      if (minProceduresClaimCount !== null) {
        claims.setMinProceduresClaimCount(minProceduresClaimCount);
      }
      if (maxProceduresClaimCount !== null) {
        claims.setMaxProceduresClaimCount(maxProceduresClaimCount);
      }
      if (claimsTimeframe !== null) {
        claims.setTimeframe(claimsTimeframe);
      }

      entitiesV2.setClaims(claims);
    }

    // Set trial info if any trial data is provided
    if (
      trialMinCount !== null ||
      trialMaxCount !== null ||
      trialStatus !== null ||
      trialPhase !== null
    ) {
      const trialInfo = new TrialInfo();

      if (trialMinCount !== null) {
        trialInfo.setMinCount(trialMinCount);
      }
      if (trialMaxCount !== null) {
        trialInfo.setMaxCount(trialMaxCount);
      }
      if (trialStatus !== null) {
        trialInfo.setStatus(trialStatus);
      }
      if (trialPhase !== null) {
        trialInfo.setPhase(trialPhase);
      }

      entitiesV2.setTrial(trialInfo);
    }

    response.setEntitiesV2(entitiesV2);
    return response;
  }

  it("should continue processing when some msearch responses have no hits", async () => {
    const input = generateKeywordSearchInput({
      query: "find doctors in Boston with rare specialty"
    });
    const mockResponse = createMockEntitiesV2Response(
      "people",
      [],
      ["rare-specialty"],
      [],
      ["US"],
      [],
      ["Boston"]
    );

    queryUnderstandingServiceClient.getEntitiesForQuery.mockResolvedValue(
      mockResponse
    );

    // Mock msearch response with mixed empty and successful responses
    const mockMsearchResponse = createMockMsearchResponse([
      {
        hits: [
          createMockNestedHit("addressesForHCPU", "addressesForHCPU.country", [
            "US"
          ])
        ]
      }, // Success for country
      {
        hits: [
          createMockNestedHit("addressesForHCPU", "addressesForHCPU.city", [
            "Boston"
          ])
        ]
      }, // Success for city
      { hits: [] } // Empty hits for specialty
    ]);

    elasticSearchService.msearch.mockResolvedValue(mockMsearchResponse);

    const result =
      await entitySearchProcessorService.processSearchWithEntityDetection(
        input,
        keywordSearchFeatureFlags
      );

    expect(elasticSearchService.msearch).toHaveBeenCalledTimes(1);
    expect(result.shouldExecuteSearch).toBe(true);

    // Should have successful results and empty results for no hits
    expect(result.detectedEntities?.specialty).toEqual([]); // No hits, should be empty
    expect(result.detectedEntities?.locations?.country).toEqual(["US"]); // Successful
    expect(result.detectedEntities?.locations?.city).toEqual(["Boston"]); // Successful
  });
});
