import {
  ClaimsResource,
  ClaimsResourceOptions,
  RPC_NAMESPACE_CLAIMS_SEARCH,
  PaginationOptions,
  DiagnosesSortOptions,
  PaginationResponse,
  ClaimsDiagnosis,
  ClaimsProcedure,
  ProceduresSortOptions,
  ClaimsTotals,
  ClaimsFilterValue,
  ClaimsFilterRange,
  DiagnosesSortOptionsDeprecated,
  ProceduresSortOptionsDeprecated,
  mapDiagnosesFieldToSort,
  mapProceduresFieldToSort,
  PrescriptionsSortOptions,
  ClaimsPrescription,
  UniquePatientCountTotal
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  buildClaimsTotalsQuery,
  buildDiagnosesCcsrQuery,
  buildDiagnosesQuery,
  buildPrescriptionsQuery,
  buildProceduresCcsrQuery,
  buildProceduresQuery,
  buildUniquePatientCountTotalsQuery,
  ClaimsTotalsResponse,
  DiagnosesCcsrResponse,
  DiagnosesResponse,
  getDiagnosesCcsrRangeAdjustedFieldsForUniquePatientCount,
  getDiagnosesRangeAdjustedFields,
  getDiagnosesRangeAdjustedFieldsForUniquePatientCount,
  getPrescriptionsRangeAdjustedFields,
  getProceduresCcsrRangeAdjustedFieldsForUniquePatientCount,
  getProceduresRangeAdjustedFields,
  getProceduresRangeAdjustedFieldsForUniquePatientCount,
  PrescriptionsResponse,
  ProceduresCcsrResponse,
  ProceduresResponse,
  UniquePatientCountTotalsResponse
} from "../lib/ClaimsQueryBuilder";
import { Service } from "typedi";
import { DiagnosesCcsrSortOptions } from "@h1nyc/search-sdk";
import { CcsrDiagnosesResponse } from "@h1nyc/search-sdk";
import { ProceduresCcsrSortOptions } from "@h1nyc/search-sdk";
import { CcsrIcdMappingRepository } from "@h1nyc/pipeline-repositories";
import _ from "lodash";
@Service()
@RpcService()
export class ClaimsResourceService
  extends RpcResourceService
  implements ClaimsResource
{
  private readonly logger = createLogger(this);
  private peopleIndex: string;

  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService,
    private ccsrIcdMappingRepository: CcsrIcdMappingRepository
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_CLAIMS_SEARCH,
      config.searchRedisOptions
    );

    this.peopleIndex = config.elasticPeopleIndex;
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  /**
   * @deprecated Use getFilteredDiagnoses instead
   */
  @RpcMethod()
  @Trace("h1-search.claims.diagnoses")
  async getDiagnoses(
    personId: string,
    terms?: string[],
    page?: PaginationOptions,
    sort?: DiagnosesSortOptionsDeprecated
  ): Promise<PaginationResponse<ClaimsDiagnosis>> {
    const sortField = mapDiagnosesFieldToSort(sort?.sortBy);
    return await this.getFilteredDiagnoses(
      personId,
      { filterRange: ClaimsFilterRange.max },
      terms,
      page,
      {
        ...sort,
        sortBy: sortField
      }
    );
  }

  @RpcMethod()
  @Trace("h1-search.claims.diagnoses")
  async getFilteredDiagnoses(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: DiagnosesSortOptions,
    options?: ClaimsResourceOptions,
    ccsrToExpand?: string
  ): Promise<PaginationResponse<ClaimsDiagnosis>> {
    const rangeFields = !options?.shouldUseUniquePatientCount
      ? getDiagnosesRangeAdjustedFields(filters.filterRange)
      : getDiagnosesRangeAdjustedFieldsForUniquePatientCount(
          filters.filterRange
        );
    const ccsrClaimCodes = await this.getClaimCodeListForCcsr(ccsrToExpand);

    const size = ccsrClaimCodes.length > 0 ? 10000 : page?.limit;
    const offset = ccsrClaimCodes.length > 0 ? 0 : page?.offset;
    const req = buildDiagnosesQuery(
      personId,
      rangeFields,
      terms,
      size,
      offset,
      sort?.sortBy,
      sort?.direction,
      ccsrClaimCodes
    );

    this.logger.debug(
      { data: JSON.stringify(req) },
      "Performing diagnoses search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<DiagnosesResponse>(
        req,
        this.peopleIndex
      );

    const hit = data.hits.hits[0];
    const totalDiagnoses = hit?._source[rangeFields.diagnosesCount] ?? 0;
    const hitTotal = hit?.inner_hits.DRG_diagnoses.hits.total.value ?? 0;

    const results = (hit?.inner_hits.DRG_diagnoses.hits.hits ?? []).map(
      (diagnosis) => {
        const description = diagnosis.fields[
          "DRG_diagnoses.description_eng.keyword"
        ] || [""];
        return {
          description: description[0],
          diagnosisCode: diagnosis.fields["DRG_diagnoses.diagnosisCode_eng"][0],
          percentOfClaims: parseFloat(diagnosis.fields[rangeFields.percent][0]),
          percentage:
            (diagnosis.fields[rangeFields.internalCount][0] / totalDiagnoses) *
            100,
          count: this.getHIPAACompliantCount(
            diagnosis.fields[rangeFields.internalCount][0]
          ),
          internalCount: diagnosis.fields[rangeFields.internalCount][0],
          codeScheme: diagnosis.fields["DRG_diagnoses.codeScheme.keyword"][0]
        };
      }
    );

    return {
      from: page?.offset ?? 0,
      pageSize: results.length,
      total: hitTotal,
      results
    };
  }

  /**
   * @deprecated Use getFilteredProcedures instead
   */
  @RpcMethod()
  @Trace("h1-search.claims.procedures")
  async getProcedures(
    personId: string,
    terms?: string[],
    page?: PaginationOptions,
    sort?: ProceduresSortOptionsDeprecated
  ): Promise<PaginationResponse<ClaimsProcedure>> {
    const sortField = mapProceduresFieldToSort(sort?.sortBy);

    return await this.getFilteredProcedures(
      personId,
      { filterRange: ClaimsFilterRange.max },
      terms,
      page,
      { ...sort, sortBy: sortField }
    );
  }

  @RpcMethod()
  @Trace("h1-search.claims.procedures")
  async getFilteredProcedures(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: ProceduresSortOptions,
    options?: ClaimsResourceOptions
  ): Promise<PaginationResponse<ClaimsProcedure>> {
    const rangeFields =
      !options?.disableUniquePatientCountForOnlyProcedures &&
      options?.shouldUseUniquePatientCount
        ? getProceduresRangeAdjustedFieldsForUniquePatientCount(
            filters.filterRange
          )
        : getProceduresRangeAdjustedFields(filters.filterRange);

    const req = buildProceduresQuery(
      personId,
      rangeFields,
      terms,
      page?.limit,
      page?.offset,
      sort?.sortBy,
      sort?.direction
    );

    this.logger.debug(
      { data: JSON.stringify(req) },
      "Performing procedures search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<ProceduresResponse>(
        req,
        this.peopleIndex
      );

    const hit = data.hits.hits[0];
    const totalProcedures = hit?._source[rangeFields.proceduresCount] ?? 0;
    const hitTotal = hit?.inner_hits.DRG_procedures.hits.total.value ?? 0;

    const results = (hit?.inner_hits.DRG_procedures.hits.hits ?? []).map(
      (procedure) => ({
        description:
          procedure.fields["DRG_procedures.description_eng.keyword"][0],
        procedureCode:
          procedure.fields["DRG_procedures.procedureCode_eng.keyword"][0],
        percentOfClaims: parseFloat(procedure.fields[rangeFields.percent][0]),
        percentage:
          (procedure.fields[rangeFields.internalCount][0] / totalProcedures) *
          100,
        count: this.getHIPAACompliantCount(
          procedure.fields[rangeFields.internalCount][0]
        ),
        internalCount: procedure.fields[rangeFields.internalCount][0],
        codeScheme: procedure.fields["DRG_procedures.codeScheme"][0]
      })
    );

    return {
      from: page?.offset ?? 0,
      pageSize: results.length,
      total: hitTotal,
      results
    };
  }

  @RpcMethod()
  @Trace("h1-search.claims.prescriptions")
  async getFilteredPrescriptions(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: PrescriptionsSortOptions
  ): Promise<PaginationResponse<ClaimsPrescription>> {
    const rangeFields = getPrescriptionsRangeAdjustedFields(
      filters.filterRange
    );

    const req = buildPrescriptionsQuery(
      personId,
      rangeFields,
      terms,
      page?.limit,
      page?.offset,
      sort?.sortBy,
      sort?.direction
    );

    this.logger.debug(
      { data: JSON.stringify(req) },
      "Performing prescriptions search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<PrescriptionsResponse>(
        req,
        this.peopleIndex
      );

    const hit = data.hits.hits[0];
    const hitTotal = hit?.inner_hits.prescriptions.hits.total.value ?? 0;
    const totalPatientCount = hit?._source[rangeFields.totalPatientCount] ?? 0;
    const results = (hit?.inner_hits.prescriptions.hits.hits ?? []).map(
      (prescription) => ({
        genericName: prescription.fields["prescriptions.generic_name"][0],
        brandName: prescription.fields["prescriptions.brand_name"][0],
        drugClass: prescription.fields["prescriptions.drug_class"],
        patientCount: prescription.fields[rangeFields.patientCount]?.[0],
        numPrescriptions: this.getHIPAACompliantCount(
          prescription.fields[rangeFields.numPrescriptions]?.[0]
        ),
        rank: prescription.fields[rangeFields.rank]?.[0],
        quartile: prescription.fields[rangeFields.quartile]?.[0],
        percentage:
          totalPatientCount > 0
            ? (prescription.fields[rangeFields.patientCount]?.[0] ?? 0) /
              totalPatientCount
            : 0
      })
    );

    return {
      from: page?.offset ?? 0,
      pageSize: results.length,
      total: hitTotal,
      results
    };
  }

  @RpcMethod()
  @Trace("h1-search.claims.claims-totals")
  async getClaimsTotals(personId: string): Promise<ClaimsTotals> {
    return await this.getFilteredClaimsTotals(personId, {
      filterRange: ClaimsFilterRange.max
    });
  }

  @RpcMethod()
  @Trace("h1-search.claims.claims-totals")
  async getFilteredClaimsTotals(
    personId: string,
    filters: ClaimsFilterValue,
    options?: ClaimsResourceOptions
  ): Promise<ClaimsTotals> {
    const proceduresEncounterRangeFields = getProceduresRangeAdjustedFields(
      filters.filterRange
    );

    const diagnosesEncounterRangeFields = getDiagnosesRangeAdjustedFields(
      filters.filterRange
    );

    const proceduresPatientRangeFields =
      getProceduresRangeAdjustedFieldsForUniquePatientCount(
        filters.filterRange
      );
    const diagnosesPatientRangeFields =
      getDiagnosesRangeAdjustedFieldsForUniquePatientCount(filters.filterRange);
    const prescriptionsRangeFields = getPrescriptionsRangeAdjustedFields(
      filters.filterRange
    );

    const req = buildClaimsTotalsQuery(
      personId,
      proceduresEncounterRangeFields,
      diagnosesEncounterRangeFields,
      prescriptionsRangeFields,
      proceduresPatientRangeFields,
      diagnosesPatientRangeFields
    );

    this.logger.debug(
      { data: JSON.stringify(req) },
      "Performing claims totals search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<ClaimsTotalsResponse>(
        req,
        this.peopleIndex
      );

    const source = data.hits.hits[0]?._source;
    if (!source) {
      return {
        diagnoses: 0,
        procedures: 0,
        prescriptions: 0,
        prescriptionPatients: 0,
        diagnosesPatients: 0,
        proceduresPatients: 0,
        diagnosesEncounters: 0,
        proceduresEncounters: 0
      };
    }
    const totalToUseForDiagnoses = options?.shouldUseUniquePatientCount
      ? source[diagnosesPatientRangeFields.diagnosesCount]
      : source[diagnosesEncounterRangeFields.diagnosesCount];
    const totalToUseForProcedures = options?.shouldUseUniquePatientCount
      ? source[proceduresPatientRangeFields.proceduresCount]
      : source[proceduresEncounterRangeFields.proceduresCount];
    return {
      diagnoses: totalToUseForDiagnoses ?? 0,
      procedures: totalToUseForProcedures ?? 0,
      prescriptions:
        source[prescriptionsRangeFields.totalNumPrescriptions] ?? 0,
      prescriptionPatients:
        source[prescriptionsRangeFields.totalPatientCount] ?? 0,
      diagnosesPatients:
        source[diagnosesPatientRangeFields.diagnosesCount] ?? 0,
      proceduresPatients:
        source[proceduresPatientRangeFields.proceduresCount] ?? 0,
      diagnosesEncounters:
        source[diagnosesEncounterRangeFields.diagnosesCount] ?? 0,
      proceduresEncounters:
        source[proceduresEncounterRangeFields.proceduresCount] ?? 0
    };
  }

  @RpcMethod()
  @Trace("h1-search.claims.unique-patient-count-totals")
  async getUniquePatientCountTotals(
    peopleIds: string[]
  ): Promise<UniquePatientCountTotal[]> {
    const req = buildUniquePatientCountTotalsQuery(peopleIds);

    this.logger.debug(
      { data: JSON.stringify(req) },
      "Performing unique patient count totals search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<UniquePatientCountTotalsResponse>(
        req,
        this.peopleIndex
      );

    const source = data.hits.hits[0]?._source;

    if (!source) {
      return [];
    }

    return data.hits.hits.map((hit) => {
      const { _source } = hit;
      const diagnosesUniqueCount = _source["DRG_diagnosesUniqueCount"];
      const proceduresUniqueCount = _source["DRG_proceduresUniqueCount"];

      return {
        personId: _source.id,
        diagnosesPatients:
          diagnosesUniqueCount === 0
            ? "0"
            : this.getHIPAACompliantCount(diagnosesUniqueCount),
        proceduresPatients:
          proceduresUniqueCount === 0
            ? "0"
            : this.getHIPAACompliantCount(proceduresUniqueCount)
      };
    });
  }

  @RpcMethod()
  @Trace("h1-search.claims.diagnoses-care-clusters")
  async getFilteredDiagnosisCareClusters(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: DiagnosesCcsrSortOptions
  ): Promise<CcsrDiagnosesResponse> {
    const rangeFields =
      getDiagnosesCcsrRangeAdjustedFieldsForUniquePatientCount(
        filters.filterRange
      );

    const req = buildDiagnosesCcsrQuery(
      personId,
      rangeFields,
      terms,
      page?.limit,
      page?.offset,
      sort?.sortBy,
      sort?.direction
    );

    this.logger.info({ data: req }, "Performing diagnoses search");

    const data =
      await this.elasticService.getSignedElasticRequest<DiagnosesCcsrResponse>(
        req,
        this.peopleIndex
      );
    if (data.hits.hits.length) {
      const hit = data.hits.hits[0];
      const hitTotal = hit?.inner_hits.ccsr.hits.total.value ?? 0;
      const totalDiagnoses =
        hit?._source[rangeFields.diagnosesPatientCount] ?? 0;
      const results = (hit?.inner_hits.ccsr.hits.hits ?? []).map((ccsr) => {
        const description = ccsr.fields["ccsr.description_eng"] || [""];
        return {
          description: description[0],
          count: ccsr.fields[rangeFields.internalUniqueCount][0] ?? 0,
          percentOfClaims:
            totalDiagnoses > 0
              ? (ccsr.fields[rangeFields.internalUniqueCount][0] /
                  totalDiagnoses) *
                100
              : 0,
          hcpQuartile: ccsr.fields[rangeFields.hcpQuartile][0]
        };
      });

      return {
        from: page?.offset ?? 0,
        pageSize: results.length,
        total: hitTotal,
        results
      };
    }

    return {
      from: page?.offset ?? 0,
      pageSize: 0,
      total: 0,
      results: []
    };
  }

  @RpcMethod()
  @Trace("h1-search.claims.procedures-care-clusters")
  async getFilteredProcedureCareClusters(
    personId: string,
    filters: ClaimsFilterValue,
    terms?: string[],
    page?: PaginationOptions,
    sort?: ProceduresCcsrSortOptions
  ): Promise<CcsrDiagnosesResponse> {
    const rangeFields =
      getProceduresCcsrRangeAdjustedFieldsForUniquePatientCount(
        filters.filterRange
      );

    const req = buildProceduresCcsrQuery(
      personId,
      rangeFields,
      terms,
      page?.limit,
      page?.offset,
      sort?.sortBy,
      sort?.direction
    );

    this.logger.info({ data: req }, "Performing diagnoses search");

    const data =
      await this.elasticService.getSignedElasticRequest<ProceduresCcsrResponse>(
        req,
        this.peopleIndex
      );
    if (data.hits.hits.length) {
      const hit = data.hits.hits[0];
      const hitTotal = hit?.inner_hits.ccsr_px.hits.total.value ?? 0;
      const totalProcedures =
        hit?._source[rangeFields.proceduresPatientCount] ?? 0;
      const results = (hit?.inner_hits.ccsr_px.hits.hits ?? []).map((ccsr) => {
        const description = ccsr.fields["ccsr_px.description_eng"] || [""];
        return {
          description: description[0],
          count: ccsr.fields[rangeFields.internalUniqueCount][0] ?? 0,
          percentOfClaims:
            totalProcedures > 0
              ? (ccsr.fields[rangeFields.internalUniqueCount][0] /
                  totalProcedures) *
                100
              : 0,
          hcpQuartile: ccsr.fields[rangeFields.hcpQuartile][0]
        };
      });

      return {
        from: page?.offset ?? 0,
        pageSize: results.length,
        total: hitTotal,
        results
      };
    }

    return {
      from: page?.offset ?? 0,
      pageSize: 0,
      total: 0,
      results: []
    };
  }

  /**
   * For HIPAA reasons, count <= 10 must be displayed as "1-10".
   * This is a strange pattern to bake display logic into the backend,
   * considering the actual count (internalCount) is returned anyway.
   */
  private getHIPAACompliantCount(count: number) {
    if (count <= 10) {
      return "1-10";
    }
    return count.toString();
  }

  private async getClaimCodeListForCcsr(
    ccsrToExpand?: string
  ): Promise<string[]> {
    if (_.isUndefined(ccsrToExpand)) {
      return [];
    }

    const claimCodeList =
      await this.ccsrIcdMappingRepository.getIcdCodesForCcsr([ccsrToExpand]);

    return claimCodeList.map((claimCodeObj) =>
      claimCodeObj.icdCode.toUpperCase()
    );
  }
}
