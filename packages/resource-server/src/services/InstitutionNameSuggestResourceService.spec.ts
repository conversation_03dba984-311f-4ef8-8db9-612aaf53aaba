/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { faker } from "@faker-js/faker";
import {
  createMockInstance,
  generateInstitutionSearchFeatureFlags,
  generateMockElasticsearchResponse
} from "../util/TestUtils";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import {
  InstitutionSearchFeatureFlags,
  featureFlagDefaults,
  institutionSearchFeatureFlagTypes
} from "./InstitutionsResourceService";
import { LDFlagsState } from "launchdarkly-node-server-sdk";
import { when } from "jest-when";
import { ConfigService } from "./ConfigService";
import { ElasticSearchInstitutionsService } from "./ElasticSearchInstitutionsService";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";
import { InstitutionNameSearchResourceService } from "./InstitutionNameSearchResourceService";
import {
  ElasticsearchInstitutionDoc,
  InstitutionNameSuggestResourceService
} from "./InstitutionNameSuggestResourceService";
import {
  SearchHit,
  SearchRequest,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";

const featureFlagsService = createMockInstance(
  FeatureFlagsService as any
) as FeatureFlagsService;
const configService = createMockInstance(ConfigService);
const elasticSearchService = createMockInstance(
  ElasticSearchInstitutionsService
);
const searchAnalyticsTracerService = createMockInstance(
  SearchAnalyticsTracerService
);
const institutionNameSearchService = createMockInstance(
  InstitutionNameSearchResourceService
);

function generateFeatureFlagsState(
  featureFlagValues: Partial<InstitutionSearchFeatureFlags>
) {
  const featureFlags = {
    ...generateInstitutionSearchFeatureFlags(),
    ...featureFlagValues
  };

  const getFlagValue = jest.fn();

  for (const flag of institutionSearchFeatureFlagTypes) {
    const flagKey = featureFlagDefaults[flag].key;
    when(getFlagValue).calledWith(flagKey).mockReturnValue(featureFlags[flag]);
  }

  return {
    getFlagValue
  } as unknown as LDFlagsState;
}

function generateInstitutionDocumentHit(
  overrides: Partial<ElasticsearchInstitutionDoc> = {}
): SearchHit<ElasticsearchInstitutionDoc> {
  return {
    _index: faker.lorem.word(),
    _id: faker.datatype.uuid(),
    _score: faker.datatype.float(),
    _source: {
      id: faker.datatype.uuid(),
      institutionId: faker.datatype.number(),
      masterOrganizationId: faker.datatype.number(),
      groupH1dnOrganizationId: faker.datatype.uuid(),
      name: faker.company.name(),
      person_count: faker.datatype.number(),
      trials_count: faker.datatype.number(),
      "address.region": faker.address.state(),
      "address.region_code": faker.address.stateAbbr(),
      "address.country": faker.address.country(),
      "address.country_code": faker.address.countryCode(),
      "address.city": faker.address.city(),
      website: faker.internet.url(),
      ...overrides
    }
  };
}

beforeEach(() => {
  jest.clearAllMocks();

  featureFlagsService.getAllFlags = jest
    .fn()
    .mockResolvedValue(
      generateFeatureFlagsState(generateInstitutionSearchFeatureFlags())
    );
});

describe("InstitutionNameSuggestResourceService", () => {
  describe("institutionNameSuggestSearch", () => {
    it("should return an empty array if the query is empty or undefined", async () => {
      featureFlagsService.getAllFlags = jest.fn().mockResolvedValue(
        generateFeatureFlagsState({
          enableInstitutionNameSuggest: true
        })
      );

      const nameSuggestService = new InstitutionNameSuggestResourceService(
        configService,
        featureFlagsService,
        elasticSearchService,
        searchAnalyticsTracerService,
        institutionNameSearchService
      );

      const userId = faker.datatype.uuid();
      const projectId = faker.datatype.number().toString();

      expect(
        await nameSuggestService.institutionNameSuggestSearch({
          query: "",
          userId,
          projectId
        })
      ).toEqual({
        total: 0,
        institutions: []
      });

      expect(
        await nameSuggestService.institutionNameSuggestSearch({
          query: undefined,
          userId,
          projectId
        })
      ).toEqual({
        total: 0,
        institutions: []
      });

      expect(
        institutionNameSearchService.buildNameSearchRequest
      ).not.toHaveBeenCalled();
    });

    it("should return an empty array if the query has advanced operators", async () => {
      featureFlagsService.getAllFlags = jest.fn().mockResolvedValue(
        generateFeatureFlagsState({
          enableInstitutionNameSuggest: true
        })
      );

      const nameSuggestService = new InstitutionNameSuggestResourceService(
        configService,
        featureFlagsService,
        elasticSearchService,
        searchAnalyticsTracerService,
        institutionNameSearchService
      );

      const userId = faker.datatype.uuid();
      const projectId = faker.datatype.number().toString();

      expect(
        await nameSuggestService.institutionNameSuggestSearch({
          query: `${faker.lorem.word()} AND ${faker.lorem.word()}`,
          userId,
          projectId
        })
      ).toEqual({
        total: 0,
        institutions: []
      });

      expect(
        await nameSuggestService.institutionNameSuggestSearch({
          query: `${faker.lorem.word()} OR ${faker.lorem.word()}`,
          userId,
          projectId
        })
      ).toEqual({
        total: 0,
        institutions: []
      });

      expect(
        await nameSuggestService.institutionNameSuggestSearch({
          query: `NOT ${faker.lorem.word()}`,
          userId,
          projectId
        })
      ).toEqual({
        total: 0,
        institutions: []
      });

      expect(
        institutionNameSearchService.buildNameSearchRequest
      ).not.toHaveBeenCalled();
    });

    it("should return an empty array if feature flag is off", async () => {
      featureFlagsService.getAllFlags = jest.fn().mockResolvedValue(
        generateFeatureFlagsState({
          enableInstitutionNameSuggest: false
        })
      );

      const nameSuggestService = new InstitutionNameSuggestResourceService(
        configService,
        featureFlagsService,
        elasticSearchService,
        searchAnalyticsTracerService,
        institutionNameSearchService
      );

      const userId = faker.datatype.uuid();
      const projectId = faker.datatype.number().toString();

      expect(
        institutionNameSearchService.buildNameSearchRequest
      ).not.toHaveBeenCalled();
      expect(
        await nameSuggestService.institutionNameSuggestSearch({
          query: `${faker.lorem.word()}`,
          userId,
          projectId
        })
      ).toEqual({
        total: 0,
        institutions: []
      });
    });

    it("should return an empty array if institutionNameSearchService.buildNameSearchRequest returns null/undefined", async () => {
      const featureFlags = generateInstitutionSearchFeatureFlags({
        enableInstitutionNameSuggest: true
      });
      featureFlagsService.getAllFlags = jest
        .fn()
        .mockResolvedValue(generateFeatureFlagsState(featureFlags));

      const nameSuggestService = new InstitutionNameSuggestResourceService(
        configService,
        featureFlagsService,
        elasticSearchService,
        searchAnalyticsTracerService,
        institutionNameSearchService
      );

      const userId = faker.datatype.uuid();
      const projectId = faker.datatype.number().toString();
      const input = {
        query: `${faker.lorem.word()} ${faker.lorem.word()}`,
        userId,
        projectId
      };

      expect(
        await nameSuggestService.institutionNameSuggestSearch(input)
      ).toEqual({
        total: 0,
        institutions: []
      });

      expect(
        institutionNameSearchService.buildNameSearchRequest
      ).toHaveBeenCalledWith(
        {
          ...input,
          paging: {
            limit: 5,
            offset: 0
          },
          accessLevel: "ALL",
          searchType: "Name"
        },
        featureFlags,
        false,
        "iol",
        [],
        []
      );
    });

    it("should query elasticsearch with request built from institutionNameSearchService.buildNameSearchRequest with modified source", async () => {
      const featureFlags = generateInstitutionSearchFeatureFlags({
        enableInstitutionNameSuggest: true
      });
      featureFlagsService.getAllFlags = jest
        .fn()
        .mockResolvedValue(generateFeatureFlagsState(featureFlags));

      const request: SearchRequest = {
        size: 5,
        from: 0,
        query: {
          [faker.lorem.word()]: faker.lorem.word()
        }
      };
      institutionNameSearchService.buildNameSearchRequest.mockResolvedValue(
        request
      );

      const response = generateMockElasticsearchResponse();
      elasticSearchService.query.mockResolvedValue(response);

      const nameSuggestService = new InstitutionNameSuggestResourceService(
        configService,
        featureFlagsService,
        elasticSearchService,
        searchAnalyticsTracerService,
        institutionNameSearchService
      );

      const userId = faker.datatype.uuid();
      const projectId = faker.datatype.number().toString();
      const input = {
        query: `${faker.lorem.word()} ${faker.lorem.word()}`,
        userId,
        projectId
      };

      expect(
        await nameSuggestService.institutionNameSuggestSearch(input)
      ).toEqual({
        total: (response.hits.total! as SearchTotalHits).value!,
        institutions: []
      });

      expect(
        institutionNameSearchService.buildNameSearchRequest
      ).toHaveBeenCalledWith(
        {
          ...input,
          paging: {
            limit: 5,
            offset: 0
          },
          accessLevel: "ALL",
          searchType: "Name"
        },
        featureFlags,
        false,
        "iol",
        [],
        []
      );

      expect(elasticSearchService.query).toHaveBeenCalledWith({
        ...request,
        _source: [
          "id",
          "groupH1dnOrganizationId",
          "masterOrganizationId",
          "name",
          "address",
          "person_count",
          "trials_count",
          "website"
        ]
      });
    });

    it("should adapt elasticsearch response to the name suggest response", async () => {
      const featureFlags = generateInstitutionSearchFeatureFlags({
        enableInstitutionNameSuggest: true
      });
      featureFlagsService.getAllFlags = jest
        .fn()
        .mockResolvedValue(generateFeatureFlagsState(featureFlags));

      const request: SearchRequest = {
        size: 5,
        from: 0,
        query: {
          [faker.lorem.word()]: faker.lorem.word()
        },
        aggs: {
          [faker.lorem.word()]: {
            [faker.lorem.word()]: faker.lorem.word()
          }
        }
      };
      institutionNameSearchService.buildNameSearchRequest.mockResolvedValue(
        request
      );

      const hits = [
        generateInstitutionDocumentHit(),
        generateInstitutionDocumentHit(),
        generateInstitutionDocumentHit(),
        generateInstitutionDocumentHit(),
        generateInstitutionDocumentHit({
          person_count: undefined,
          trials_count: undefined
        })
      ];
      const mockResponse = generateMockElasticsearchResponse(hits);
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const nameSuggestService = new InstitutionNameSuggestResourceService(
        configService,
        featureFlagsService,
        elasticSearchService,
        searchAnalyticsTracerService,
        institutionNameSearchService
      );

      const userId = faker.datatype.uuid();
      const projectId = faker.datatype.number().toString();
      const input = {
        query: `${faker.lorem.word()} ${faker.lorem.word()}`,
        userId,
        projectId
      };

      expect(
        await nameSuggestService.institutionNameSuggestSearch(input)
      ).toEqual({
        total: (mockResponse.hits.total! as SearchTotalHits).value!,
        institutions: hits.map((hit) => ({
          id: hit._source!.id,
          institutionId: hit._source!.institutionId,
          masterOrganizationId: hit._source!.masterOrganizationId,
          groupH1dnOrganizationId: hit._source!.groupH1dnOrganizationId,
          name: hit._source!.name,
          personCount: hit._source!.person_count ?? 0,
          trialCount: hit._source!.trials_count ?? 0,
          address: {
            region: hit._source!["address.region"],
            regionCode: hit._source!["address.region_code"],
            country: hit._source!["address.country"],
            countryCode: hit._source!["address.country_code"],
            city: hit._source!["address.city"]
          },
          website: hit._source!.website
        }))
      });

      expect(
        institutionNameSearchService.buildNameSearchRequest
      ).toHaveBeenCalledWith(
        {
          ...input,
          paging: {
            limit: 5,
            offset: 0
          },
          accessLevel: "ALL",
          searchType: "Name"
        },
        featureFlags,
        false,
        "iol",
        [],
        []
      );

      expect(elasticSearchService.query).toHaveBeenCalledWith({
        ...request,
        _source: [
          "id",
          "groupH1dnOrganizationId",
          "masterOrganizationId",
          "name",
          "address",
          "person_count",
          "trials_count",
          "website"
        ],
        aggs: undefined
      });
    });
  });
});
