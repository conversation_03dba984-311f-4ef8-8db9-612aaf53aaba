import {
  createMockInstance,
  getEmptyKeywordSearchFilters
} from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { ElasticSearchSpellCheckService } from "./ElasticSearchSpellCheckService";
import { SpellSuggesterService } from "./SpellSuggesterService";
import { faker } from "@faker-js/faker";
import { SearchRequest } from "@elastic/elasticsearch/lib/api/types";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";
import {
  FilterInterface,
  KeywordSearchInput,
  WeightedSortBy
} from "@h1nyc/search-sdk";

function generateStandardWeightedSortBy(
  overrides: Partial<WeightedSortBy> = {}
): WeightedSortBy {
  const baseSortBy = {
    publication: faker.datatype.number(),
    microBloggingCount: faker.datatype.number(),
    citation: faker.datatype.number(),
    trial: faker.datatype.number(),
    congress: faker.datatype.number(),
    payment: faker.datatype.number(),
    diagnoses: faker.datatype.number(),
    procedures: faker.datatype.number(),
    prescriptions: faker.datatype.number(),
    referralsReceived: faker.datatype.number({ min: 0.0001 }),
    referralsSent: faker.datatype.number({ min: 0.0001 }),
    grant: faker.datatype.number(),
    patientsDiversityRank: 0,
    h1DefaultRank: 0,
    twitterFollowersCount: 0,
    twitterTweetCount: 0
  };

  return { ...baseSortBy, ...overrides };
}

export function generateFilters(
  overrides: Partial<FilterInterface> = {}
): FilterInterface {
  const baseFilters: FilterInterface = getEmptyKeywordSearchFilters();

  return { ...baseFilters, ...overrides };
}

function generateKeywordSearchInput(
  overrides: Partial<KeywordSearchInput> = {}
): KeywordSearchInput {
  const page = {
    from: faker.datatype.number(),
    size: faker.datatype.number()
  };
  const userId = faker.datatype.uuid();
  const projectId = faker.datatype.string();
  const query = faker.datatype.string();
  const suppliedFilters = generateFilters();
  const language = "eng";
  const sortBy = generateStandardWeightedSortBy();
  const projectFeatures = {
    advancedOperators: true,
    claims: true,
    referrals: true,
    engagementsV2: faker.datatype.boolean(),
    translateTaiwan: true
  };

  const baseInput = {
    userId,
    page,
    projectId,
    query,
    suppliedFilters,
    language,
    sortBy,
    projectFeatures
  };

  return { ...baseInput, ...overrides };
}

function getExpectedPhraseSuggesterRequest(query: string): SearchRequest {
  return {
    index: expect.anything(),
    suggest: {
      suggest_phrase: {
        text: query,
        phrase: {
          max_errors: 0.9,
          field: "trigram",
          gram_size: 3,
          size: 1,
          direct_generator: [
            {
              field: "trigram",
              max_inspections: 10,
              suggest_mode: "missing"
            }
          ],
          collate: {
            query: {
              source: `{"simple_query_string": {"query": "{{suggestion}}","default_operator": "AND","fields": ["trigram"]}}`
            },
            prune: false
          }
        }
      }
    }
  };
}

describe("SpellSuggesterService", () => {
  describe("getSpellCheckSuggestion", () => {
    it("should not suggest for query with email", () => {
      const configService = createMockInstance(ConfigService);
      const elasticSpellCheckService = createMockInstance(
        ElasticSearchSpellCheckService
      );
      const err = new Error("elasticSpellCheckService.query threw an error");
      elasticSpellCheckService.query.mockRejectedValue(err);
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      searchAnalyticsTracerService.sendAnalyticsEvent.mockRejectedValue(
        new Error(
          "searchAnalyticsTracerService.sendAnalyticsEvent threw an error"
        )
      );
      const spellSuggesterService = new SpellSuggesterService(
        configService,
        elasticSpellCheckService,
        searchAnalyticsTracerService
      );

      const queriesWithEmail = [
        `${faker.name.firstName()}@${faker.name.lastName()}.${faker.address.countryCode()}`,
        [
          faker.datatype.string(),
          `${faker.name.firstName()}@${faker.name.lastName()}.${faker.address.countryCode()}`,
          faker.datatype.string()
        ].join(" ")
      ];

      queriesWithEmail.forEach(async (queryWithEmail) => {
        const input = generateKeywordSearchInput({ query: queryWithEmail });

        const suggestion = await spellSuggesterService.getSpellCheckSuggestion(
          input,
          undefined
        );
        expect(suggestion).toBeUndefined();
      });
    });

    it("should not suggest for alphanumeric query", () => {
      const configService = createMockInstance(ConfigService);
      const elasticSpellCheckService = createMockInstance(
        ElasticSearchSpellCheckService
      );
      const err = new Error("elasticSpellCheckService.query threw an error");
      elasticSpellCheckService.query.mockRejectedValue(err);
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      searchAnalyticsTracerService.sendAnalyticsEvent.mockRejectedValue(
        new Error(
          "searchAnalyticsTracerService.sendAnalyticsEvent threw an error"
        )
      );
      const spellSuggesterService = new SpellSuggesterService(
        configService,
        elasticSpellCheckService,
        searchAnalyticsTracerService
      );

      const alphaNumericQueries = [
        `${faker.datatype.number()}`,
        faker.helpers
          .shuffle([
            ...faker.datatype.string().split(""),
            ...`${faker.datatype.number()}`.split(""),
            ...faker.datatype.string().split("")
          ])
          .join(" ")
      ];

      alphaNumericQueries.forEach(async (alphaNumericQuery) => {
        const input = generateKeywordSearchInput({ query: alphaNumericQuery });
        const suggestion = await spellSuggesterService.getSpellCheckSuggestion(
          input,
          undefined
        );
        expect(suggestion).toBeUndefined();
      });
    });

    it("should not suggest for advanced query", () => {
      const configService = createMockInstance(ConfigService);
      const elasticSpellCheckService = createMockInstance(
        ElasticSearchSpellCheckService
      );
      const err = new Error("elasticSpellCheckService.query threw an error");
      elasticSpellCheckService.query.mockRejectedValue(err);
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      searchAnalyticsTracerService.sendAnalyticsEvent.mockRejectedValue(
        new Error(
          "searchAnalyticsTracerService.sendAnalyticsEvent threw an error"
        )
      );
      const spellSuggesterService = new SpellSuggesterService(
        configService,
        elasticSpellCheckService,
        searchAnalyticsTracerService
      );

      const advancedQueries = [
        `NOT ${faker.random.alpha({ count: 10 })}`,
        `${faker.random.alpha({ count: 20 })} AND ${faker.random.alpha({
          count: 20
        })}`,
        `${faker.random.alpha({ count: 20 })} OR ${faker.random.alpha({
          count: 20
        })}`
      ];

      advancedQueries.forEach(async (advancedQuery) => {
        const input = generateKeywordSearchInput({ query: advancedQuery });
        const suggestion = await spellSuggesterService.getSpellCheckSuggestion(
          input,
          undefined
        );
        expect(suggestion).toBeUndefined();
      });
    });

    it("should not suggest for name intent query", async () => {
      const configService = createMockInstance(ConfigService);
      const elasticSpellCheckService = createMockInstance(
        ElasticSearchSpellCheckService
      );
      const err = new Error("elasticSpellCheckService.query threw an error");
      elasticSpellCheckService.query.mockRejectedValue(err);

      const res = {
        hasQueryIntent: function () {
          return true;
        },
        getQueryIntent: function () {
          return {
            hasPersonNameIntent: function () {
              return true;
            },
            getPersonNameIntent: function () {
              return {
                getScore: function () {
                  return 1.0;
                }
              };
            }
          };
        }
      };
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      searchAnalyticsTracerService.sendAnalyticsEvent.mockRejectedValue(
        new Error(
          "searchAnalyticsTracerService.sendAnalyticsEvent threw an error"
        )
      );

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const spellSuggesterService = new SpellSuggesterService(
        configService,
        elasticSpellCheckService,
        searchAnalyticsTracerService
      );

      const input = generateKeywordSearchInput({ query: "John Doe" });
      const suggestion = await spellSuggesterService.getSpellCheckSuggestion(
        input,
        res as any
      );
      expect(suggestion).toBeUndefined();
    });

    it("should call elasticsearch for valid query", async () => {
      const indexName = faker.datatype.uuid();
      const configService = createMockInstance(ConfigService);
      configService.elasticSpellCheckIndex = indexName;

      const elasticSpellCheckService = createMockInstance(
        ElasticSearchSpellCheckService
      );
      elasticSpellCheckService.query.mockResolvedValue({
        took: faker.datatype.number(),
        timed_out: false,
        _shards: {
          total: 1,
          successful: 1,
          skipped: 0,
          failed: 0
        },
        hits: {
          total: {
            value: 0,
            relation: "eq"
          },
          max_score: undefined,
          hits: []
        },
        suggest: undefined
      });
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      searchAnalyticsTracerService.sendAnalyticsEvent.mockRejectedValue(
        new Error(
          "searchAnalyticsTracerService.sendAnalyticsEvent threw an error"
        )
      );
      const spellSuggesterService = new SpellSuggesterService(
        configService,
        elasticSpellCheckService,
        searchAnalyticsTracerService
      );

      const query = faker.random.alpha({ count: 10 });
      const input = generateKeywordSearchInput({ query });
      await spellSuggesterService.getSpellCheckSuggestion(input, undefined);

      expect(elasticSpellCheckService.query).toHaveBeenCalledWith(
        getExpectedPhraseSuggesterRequest(query)
      );
    });

    it("should return undefined if suggester returns empty list", async () => {
      const indexName = faker.datatype.uuid();
      const configService = createMockInstance(ConfigService);
      configService.elasticSpellCheckIndex = indexName;

      const elasticSpellCheckService = createMockInstance(
        ElasticSearchSpellCheckService
      );
      const query = faker.random.alpha({ count: 10 });

      elasticSpellCheckService.query.mockResolvedValue({
        took: 43,
        timed_out: false,
        _shards: {
          total: 5,
          successful: 5,
          skipped: 0,
          failed: 0
        },
        hits: {
          total: {
            value: 0,
            relation: "eq"
          },
          max_score: undefined,
          hits: []
        },
        suggest: {
          suggest_phrase: [
            {
              text: query,
              offset: 0,
              length: query.length,
              options: []
            }
          ]
        }
      });
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      searchAnalyticsTracerService.sendAnalyticsEvent.mockRejectedValue(
        new Error(
          "searchAnalyticsTracerService.sendAnalyticsEvent threw an error"
        )
      );
      const spellSuggesterService = new SpellSuggesterService(
        configService,
        elasticSpellCheckService,
        searchAnalyticsTracerService
      );

      const input = generateKeywordSearchInput({ query });
      const suggestion = await spellSuggesterService.getSpellCheckSuggestion(
        input,
        undefined
      );

      expect(suggestion).toBeUndefined();
      expect(elasticSpellCheckService.query).toHaveBeenCalledWith(
        getExpectedPhraseSuggesterRequest(query)
      );
    });

    it("should return suggestion if suggester returns non empty list", async () => {
      const indexName = faker.datatype.uuid();
      const configService = createMockInstance(ConfigService);
      configService.elasticSpellCheckIndex = indexName;

      const elasticSpellCheckService = createMockInstance(
        ElasticSearchSpellCheckService
      );

      const query = faker.random.alpha({ count: 10 }).toLowerCase();
      const suggestionReturnedForQuery = faker.datatype.string().toLowerCase();

      elasticSpellCheckService.query.mockResolvedValue({
        took: 43,
        timed_out: false,
        _shards: {
          total: 5,
          successful: 5,
          skipped: 0,
          failed: 0
        },
        hits: {
          total: {
            value: 0,
            relation: "eq"
          },
          max_score: undefined,
          hits: []
        },
        suggest: {
          suggest_phrase: [
            {
              text: query,
              offset: 0,
              length: query.length,
              options: [
                {
                  text: suggestionReturnedForQuery,
                  score: faker.datatype.float(1)
                }
              ]
            }
          ]
        }
      });
      const searchAnalyticsTracerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const spellSuggesterService = new SpellSuggesterService(
        configService,
        elasticSpellCheckService,
        searchAnalyticsTracerService
      );
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      const input = generateKeywordSearchInput({ query, userId, projectId });

      const suggestion = await spellSuggesterService.getSpellCheckSuggestion(
        input,
        undefined
      );

      expect(suggestion).toEqual(suggestionReturnedForQuery);
      expect(elasticSpellCheckService.query).toHaveBeenCalledWith(
        getExpectedPhraseSuggesterRequest(query)
      );
      expect(
        searchAnalyticsTracerService.sendAnalyticsEvent
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          event: "search.spellSuggest.suggestion",
          properties: {
            query,
            suggestion,
            projectId
          },
          userId
        })
      );
    });

    describe("character casing", () => {
      it("should keep upper-casing for chars at start of string if chars were not edited", async () => {
        const indexName = faker.datatype.uuid();
        const configService = createMockInstance(ConfigService);
        configService.elasticSpellCheckIndex = indexName;

        const elasticSpellCheckService = createMockInstance(
          ElasticSearchSpellCheckService
        );

        const query = "Gastrointrology";
        const suggestionReturnedForQuery = "gastroenterology";

        const expectedSuggestionAfterCasing = "Gastroenterology";

        elasticSpellCheckService.query.mockResolvedValue({
          took: 43,
          timed_out: false,
          _shards: {
            total: 5,
            successful: 5,
            skipped: 0,
            failed: 0
          },
          hits: {
            total: {
              value: 0,
              relation: "eq"
            },
            max_score: undefined,
            hits: []
          },
          suggest: {
            suggest_phrase: [
              {
                text: query,
                offset: 0,
                length: query.length,
                options: [
                  {
                    text: suggestionReturnedForQuery,
                    score: faker.datatype.float(1)
                  }
                ]
              }
            ]
          }
        });
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const spellSuggesterService = new SpellSuggesterService(
          configService,
          elasticSpellCheckService,
          searchAnalyticsTracerService
        );

        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const input = generateKeywordSearchInput({ query, userId, projectId });

        const suggestion = await spellSuggesterService.getSpellCheckSuggestion(
          input,
          undefined
        );

        expect(suggestion).toEqual(expectedSuggestionAfterCasing);
        expect(elasticSpellCheckService.query).toHaveBeenCalledWith(
          getExpectedPhraseSuggesterRequest(query)
        );
        expect(
          searchAnalyticsTracerService.sendAnalyticsEvent
        ).toHaveBeenCalledWith(
          expect.objectContaining({
            event: "search.spellSuggest.suggestion",
            properties: {
              query,
              suggestion,
              projectId
            },
            userId
          })
        );
      });

      it("should keep upper-casing for chars at end of string if chars were not edited", async () => {
        const indexName = faker.datatype.uuid();
        const configService = createMockInstance(ConfigService);
        configService.elasticSpellCheckIndex = indexName;

        const elasticSpellCheckService = createMockInstance(
          ElasticSearchSpellCheckService
        );

        const suggestionReturnedForQuery = "hepatitis b";
        const query = "hepatetis B";

        const expectedSuggestionAfterCasing = "hepatitis B";

        elasticSpellCheckService.query.mockResolvedValue({
          took: 43,
          timed_out: false,
          _shards: {
            total: 5,
            successful: 5,
            skipped: 0,
            failed: 0
          },
          hits: {
            total: {
              value: 0,
              relation: "eq"
            },
            max_score: undefined,
            hits: []
          },
          suggest: {
            suggest_phrase: [
              {
                text: query,
                offset: 0,
                length: query.length,
                options: [
                  {
                    text: suggestionReturnedForQuery,
                    score: faker.datatype.float(1)
                  }
                ]
              }
            ]
          }
        });
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const spellSuggesterService = new SpellSuggesterService(
          configService,
          elasticSpellCheckService,
          searchAnalyticsTracerService
        );
        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const input = generateKeywordSearchInput({ query, userId, projectId });

        const suggestion = await spellSuggesterService.getSpellCheckSuggestion(
          input,
          undefined
        );

        expect(suggestion).toEqual(expectedSuggestionAfterCasing);
        expect(elasticSpellCheckService.query).toHaveBeenCalledWith(
          getExpectedPhraseSuggesterRequest(query)
        );
        expect(
          searchAnalyticsTracerService.sendAnalyticsEvent
        ).toHaveBeenCalledWith(
          expect.objectContaining({
            event: "search.spellSuggest.suggestion",
            properties: {
              query,
              suggestion,
              projectId
            },
            userId
          })
        );
      });

      it("should keep upper-casing for chars at middle of string if chars were not edited", async () => {
        const indexName = faker.datatype.uuid();
        const configService = createMockInstance(ConfigService);
        configService.elasticSpellCheckIndex = indexName;

        const elasticSpellCheckService = createMockInstance(
          ElasticSearchSpellCheckService
        );

        const query = "mRNA proten";
        const suggestionReturnedForQuery = "mrna protein";

        const expectedSuggestionAfterCasing = "mRNA protein";

        elasticSpellCheckService.query.mockResolvedValue({
          took: 43,
          timed_out: false,
          _shards: {
            total: 5,
            successful: 5,
            skipped: 0,
            failed: 0
          },
          hits: {
            total: {
              value: 0,
              relation: "eq"
            },
            max_score: undefined,
            hits: []
          },
          suggest: {
            suggest_phrase: [
              {
                text: query,
                offset: 0,
                length: query.length,
                options: [
                  {
                    text: suggestionReturnedForQuery,
                    score: faker.datatype.float(1)
                  }
                ]
              }
            ]
          }
        });
        const searchAnalyticsTracerService = createMockInstance(
          SearchAnalyticsTracerService
        );
        const spellSuggesterService = new SpellSuggesterService(
          configService,
          elasticSpellCheckService,
          searchAnalyticsTracerService
        );

        const userId = faker.datatype.string();
        const projectId = faker.datatype.string();
        const input = generateKeywordSearchInput({ query, userId, projectId });

        const suggestion = await spellSuggesterService.getSpellCheckSuggestion(
          input,
          undefined
        );

        expect(suggestion).toEqual(expectedSuggestionAfterCasing);
        expect(elasticSpellCheckService.query).toHaveBeenCalledWith(
          getExpectedPhraseSuggesterRequest(query)
        );
        expect(
          searchAnalyticsTracerService.sendAnalyticsEvent
        ).toHaveBeenCalledWith(
          expect.objectContaining({
            event: "search.spellSuggest.suggestion",
            properties: {
              query,
              suggestion,
              projectId
            },
            userId
          })
        );
      });
    });
  });
});
