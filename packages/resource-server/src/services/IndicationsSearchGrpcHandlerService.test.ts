import { faker } from "@faker-js/faker";
import {
  IndicationSortBy,
  IndicationSource,
  IndicationType
} from "@h1nyc/search-sdk";
import { createMockInstance } from "../util/TestUtils";
import { IndicationsSearchGrpcHandlerService } from "./IndicationsSearchGrpcHandlerService";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";

describe("IndicationsSearchGrpcHandlerService", () => {
  describe("searchByQuery", () => {
    it("should invoke indications service with given args", async () => {
      const indicationsSearchService = createMockInstance(
        IndicationsTreeSearchService
      );
      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];

      indicationsSearchService.searchIndicationsByQuery.mockResolvedValue([
        {
          id: ids[0],
          h1Id: faker.datatype.string(),
          indicationName: indicationNames[0],
          indicationType: IndicationType.ICD,
          matchedSynonyms: [],
          icdCodes: [],
          children: [],
          parentH1Ids: []
        },
        {
          id: ids[1],
          h1Id: faker.datatype.string(),
          indicationName: indicationNames[1],
          indicationType: IndicationType.ICD,
          matchedSynonyms: [],
          icdCodes: [],
          children: [],
          parentH1Ids: []
        }
      ]);

      const indicationsSearchGrpcHandlerService =
        new IndicationsSearchGrpcHandlerService(indicationsSearchService);

      const query = faker.datatype.string();
      const size = faker.datatype.number();
      const projectId = faker.datatype.string();
      const includeDuplicates = faker.datatype.boolean();
      const response = await indicationsSearchGrpcHandlerService.searchByQuery({
        query,
        size,
        indicationSource: [IndicationSource.ALL],
        indicationType: [IndicationType.ICD],
        projectId,
        h1Ids: [],
        includeDuplicates
      });

      expect(
        indicationsSearchService.searchIndicationsByQuery
      ).toHaveBeenCalledWith({
        query,
        size,
        indicationSource: [IndicationSource.ALL],
        indicationType: [IndicationType.ICD],
        projectId,
        h1Ids: [],
        includeDuplicates
      });
      expect(response).toEqual({
        indications: [
          {
            id: ids[0],
            h1Id: expect.anything(),
            indicationName: indicationNames[0],
            indicationType: IndicationType.ICD,
            matchedSynonyms: [],
            icdCodes: [],
            children: [],
            parentH1Ids: []
          },
          {
            id: ids[1],
            h1Id: expect.anything(),
            indicationName: indicationNames[1],
            indicationType: IndicationType.ICD,
            matchedSynonyms: [],
            icdCodes: [],
            children: [],
            parentH1Ids: []
          }
        ]
      });
    });
  });

  describe("searchRootIndications", () => {
    it("should invoke indications service with given args", async () => {
      const indicationsSearchService = createMockInstance(
        IndicationsTreeSearchService
      );
      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];

      indicationsSearchService.searchRootIndications.mockResolvedValue([
        {
          id: ids[0],
          h1Id: faker.datatype.string(),
          indicationName: indicationNames[0],
          indicationType: IndicationType.L1,
          matchedSynonyms: [],
          icdCodes: [],
          children: [],
          parentH1Ids: []
        }
      ]);

      const indicationsSearchGrpcHandlerService =
        new IndicationsSearchGrpcHandlerService(indicationsSearchService);

      const size = faker.datatype.number();
      const response =
        await indicationsSearchGrpcHandlerService.searchRootIndications({
          size,
          indicationSource: [IndicationSource.ALL]
        });

      expect(
        indicationsSearchService.searchRootIndications
      ).toHaveBeenCalledWith({
        size,
        indicationSource: [IndicationSource.ALL]
      });
      expect(response).toEqual({
        indications: [
          {
            id: ids[0],
            h1Id: expect.anything(),
            indicationName: indicationNames[0],
            indicationType: IndicationType.L1,
            matchedSynonyms: [],
            icdCodes: [],
            children: [],
            parentH1Ids: []
          }
        ]
      });
    });
  });

  describe("getSubTrees", () => {
    it("should invoke indications service with given args", async () => {
      const indicationsSearchService = createMockInstance(
        IndicationsTreeSearchService
      );
      const ids = [faker.datatype.string(), faker.datatype.string()];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string()
      ];

      indicationsSearchService.getSubTreesForRoot.mockResolvedValue([
        {
          id: ids[0],
          h1Id: faker.datatype.string(),
          indicationName: indicationNames[0],
          indicationType: IndicationType.L2,
          matchedSynonyms: [],
          icdCodes: [],
          children: [],
          parentH1Ids: []
        },
        {
          id: ids[1],
          h1Id: faker.datatype.string(),
          indicationName: indicationNames[1],
          indicationType: IndicationType.L2,
          matchedSynonyms: [],
          icdCodes: [],
          children: [],
          parentH1Ids: []
        }
      ]);

      const indicationsSearchGrpcHandlerService =
        new IndicationsSearchGrpcHandlerService(indicationsSearchService);

      const rootId = faker.datatype.string();
      const response = await indicationsSearchGrpcHandlerService.getSubTrees({
        rootId,
        indicationSource: [IndicationSource.ALL],
        sortBy: IndicationSortBy.PATIENT_COUNT
      });

      expect(indicationsSearchService.getSubTreesForRoot).toHaveBeenCalledWith({
        rootId,
        indicationSource: [IndicationSource.ALL],
        sortBy: IndicationSortBy.PATIENT_COUNT
      });
      expect(response).toEqual({
        indications: [
          {
            id: ids[0],
            h1Id: expect.anything(),
            indicationName: indicationNames[0],
            indicationType: IndicationType.L2,
            matchedSynonyms: [],
            icdCodes: [],
            children: [],
            parentH1Ids: []
          },
          {
            id: ids[1],
            h1Id: expect.anything(),
            indicationName: indicationNames[1],
            indicationType: IndicationType.L2,
            matchedSynonyms: [],
            icdCodes: [],
            children: [],
            parentH1Ids: []
          }
        ]
      });
    });
  });

  describe("searchIndicationTreesByQuery", () => {
    it("should invoke indications service with given args", async () => {
      const indicationsSearchService = createMockInstance(
        IndicationsTreeSearchService
      );
      const ids = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const indicationNames = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];

      indicationsSearchService.searchIndicationTreesByQuery.mockResolvedValue([
        {
          id: ids[0],
          h1Id: faker.datatype.string(),
          indicationName: indicationNames[0],
          indicationType: IndicationType.L1,
          matchedSynonyms: [],
          icdCodes: [],
          children: [
            {
              id: ids[2],
              h1Id: faker.datatype.string(),
              indicationName: indicationNames[2],
              indicationType: IndicationType.L2,
              matchedSynonyms: [],
              icdCodes: [],
              children: [],
              parentH1Ids: []
            },
            {
              id: ids[3],
              h1Id: faker.datatype.string(),
              indicationName: indicationNames[3],
              indicationType: IndicationType.L2,
              matchedSynonyms: [],
              icdCodes: [],
              children: [],
              parentH1Ids: []
            }
          ],
          parentH1Ids: []
        },
        {
          id: ids[1],
          h1Id: faker.datatype.string(),
          indicationName: indicationNames[1],
          indicationType: IndicationType.L1,
          matchedSynonyms: [],
          icdCodes: [],
          children: [],
          parentH1Ids: []
        }
      ]);

      const indicationsSearchGrpcHandlerService =
        new IndicationsSearchGrpcHandlerService(indicationsSearchService);

      const query = faker.datatype.string();
      const projectId = faker.datatype.string();
      const response =
        await indicationsSearchGrpcHandlerService.searchIndicationTreesByQuery({
          query,
          sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
          indicationSource: [IndicationSource.ALL],
          indicationType: [],
          h1Ids: [],
          projectId
        });

      expect(
        indicationsSearchService.searchIndicationTreesByQuery
      ).toHaveBeenCalledWith({
        query,
        sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
        indicationSource: [IndicationSource.ALL],
        indicationType: [],
        projectId,
        h1Ids: []
      });
      expect(response).toEqual({
        indications: [
          {
            id: ids[0],
            h1Id: expect.anything(),
            indicationName: indicationNames[0],
            indicationType: IndicationType.L1,
            matchedSynonyms: [],
            icdCodes: [],
            children: [
              {
                id: ids[2],
                h1Id: expect.anything(),
                indicationName: indicationNames[2],
                indicationType: IndicationType.L2,
                matchedSynonyms: [],
                icdCodes: [],
                children: [],
                parentH1Ids: []
              },
              {
                id: ids[3],
                h1Id: expect.anything(),
                indicationName: indicationNames[3],
                indicationType: IndicationType.L2,
                matchedSynonyms: [],
                icdCodes: [],
                children: [],
                parentH1Ids: []
              }
            ],
            parentH1Ids: []
          },
          {
            id: ids[1],
            h1Id: expect.anything(),
            indicationName: indicationNames[1],
            indicationType: IndicationType.L1,
            matchedSynonyms: [],
            icdCodes: [],
            children: [],
            parentH1Ids: []
          }
        ]
      });
    });
  });
});
