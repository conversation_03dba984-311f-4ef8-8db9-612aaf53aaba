// @ts-ignore
import franc from "franc-min";

import { ConfigService } from "./ConfigService";
import { Service } from "typedi";
import { LanguageDetector } from "./KeywordSearchResourceServiceRewrite";

const UNDETERMINED = "und";
const ISO_639_3_ENGLISH = "eng";

const userLangSettingsMap = {
  english: "eng",
  chinese_simplified: "cmn",
  chinese_traditional: "cmn",
  japanese: "jpn"
};

export type Language = typeof ENGLISH | typeof JAPANESE | typeof CHINESE;
export const ENGLISH = "eng";
export const JAPANESE = "jpn";
export const CHINESE = "cmn";
export const ALTERNATE_ENGLISH_PREFIX = "en";
export const ALTERNATE_CHINESE_PREFIX = "zh";
export const ALTERNATE_JAPANESE_PREFIX = "ja";

@Service()
export class LanguageDetectService {
  private supportedLanguages: string[];

  constructor(config: ConfigService) {
    this.supportedLanguages = config.searchSupportedLanguages.split(",");
  }

  async determineSupportedLanguage(text: string): Promise<string> {
    const result = franc(text, { minLength: 1, only: this.supportedLanguages });
    return result === UNDETERMINED ? ISO_639_3_ENGLISH : result;
  }

  // TODO: remove this in favor of getLanguageDetector
  /**
   * @deprecated Use getLanguageDetector instead
   */
  async determineSupportedLanguageWithUserPreference(
    text: string,
    userPreferredLang: string | undefined | null
  ): Promise<string> {
    let userLangCode = "und";
    if (userPreferredLang) {
      userLangCode =
        userLangSettingsMap[
          userPreferredLang as
            | "english"
            | "chinese_simplified"
            | "chinese_traditional"
            | "japanese"
        ];
    } else {
      userLangCode = "eng";
    }

    const result = franc(text, {
      minLength: 1,
      only: this.supportedLanguages
    });

    //return english if detection is undefined and user languageCode is not available, else detected lang
    if (!userLangCode) {
      return result === "und" ? "eng" : result;
    }

    /**
     * If chinese is detected, check for user preference of japanese and return the language code.
     * Otherwise return whatever is detected -- if anything is. Return user language setting if that exists
     * otherwise just return english.
     */
    if (result === "cmn" && userLangCode === "jpn") {
      return userLangCode;
    } else if (result !== "und") {
      return result;
    }

    return userLangCode;
  }

  getLanguageDetector(
    userPreferredLanguage: string = ENGLISH
  ): LanguageDetector {
    return (query: string): Language => {
      const detectedLanguage = franc(query, {
        minLength: 1,
        only: [ENGLISH, CHINESE, JAPANESE]
      });

      if (detectedLanguage === CHINESE && userPreferredLanguage === JAPANESE) {
        return JAPANESE;
      }

      return detectedLanguage === UNDETERMINED
        ? ENGLISH
        : (detectedLanguage as Language);
    };
  }

  shouldUseBothCmnAndJpnFields(
    detectedLanguage: Language,
    userPreferredLanguage: string = ENGLISH
  ): boolean {
    if (detectedLanguage === CHINESE && userPreferredLanguage === ENGLISH) {
      return true;
    } else if (
      detectedLanguage === JAPANESE &&
      userPreferredLanguage === ENGLISH
    ) {
      return true;
    } else {
      return false;
    }
  }
}
