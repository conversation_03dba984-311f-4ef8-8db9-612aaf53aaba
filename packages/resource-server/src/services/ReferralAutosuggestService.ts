import { Rpc<PERSON>ethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import {
  ReferralAutosuggestResource,
  RPC_NAMESPACE_REFERRAL_AUTOSUGGEST,
  ReferralType
} from "@h1nyc/search-sdk";
import { createLogger } from "../lib/Logger";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import { Trace } from "../Tracer";
import { Service } from "typedi";
import {
  QueryDslQueryContainer,
  SearchRequest,
  AggregationsTermsAggregateBase
} from "@elastic/elasticsearch/lib/api/types";
import _ from "lodash";

type DocCountBucket = {
  key: string;
  doc_count: number;
};

const NO_RESULTS = 0;

const referralTypePath: Readonly<Record<ReferralType, string>> = {
  Sent: "referralsSent",
  Received: "referralsReceived"
};

@Service()
@RpcService()
export class ReferralAutosuggestService
  extends RpcResourceService
  implements ReferralAutosuggestResource
{
  private readonly logger = createLogger(this);
  private peopleIndex: string;

  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_REFERRAL_AUTOSUGGEST,
      config.searchRedisOptions
    );

    this.peopleIndex = config.elasticPeopleIndex;
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    await this.elasticService.ping();
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.serviceLine.autosuggest")
  async serviceLineSuggest(
    personId: string,
    projectId: string,
    query: string,
    referralType: ReferralType
  ): Promise<Array<string>> {
    const path = referralTypePath[referralType];

    const filter: Array<QueryDslQueryContainer> = [
      projectIdFilter(projectId),
      idsFilter(personId)
    ];

    if (query) {
      filter.push({
        match_phrase: {
          "referralsServiceLine_eng.autocomplete_search": {
            query,
            slop: 5
          }
        }
      });
    }

    const request: SearchRequest = {
      index: this.peopleIndex,
      size: NO_RESULTS,
      query: {
        bool: {
          filter
        }
      },
      aggs: {
        referrals: {
          nested: {
            path
          },
          aggs: {
            filtered: {
              terms: {
                field: `${path}.serviceDescription_eng`,
                size: 100,
                exclude: "_BAD_",
                order: [
                  {
                    _count: "desc"
                  }
                ],
                script: getAggregationsScript(query, personId)
              }
            }
          }
        }
      }
    };

    this.logger.info(
      { data: JSON.stringify(request) },
      `Generated SERVICE LINE AUTOSUGGEST query for project ${projectId} with 
      query ${query}`
    );

    const { aggregations } = await this.elasticService.query(request);

    const {
      referrals: {
        filtered: { buckets }
      }
    } = aggregations as Record<
      "referrals",
      Record<"filtered", AggregationsTermsAggregateBase<DocCountBucket>>
    >;

    return (buckets as DocCountBucket[]).map(_.property("key"));
  }

  @RpcMethod()
  @Trace("h1-search.specialty.autosuggest")
  async specialtySuggest(
    peopleIds: Array<string>,
    projectId: string,
    query: string
  ): Promise<Array<string>> {
    const filter: Array<QueryDslQueryContainer> = [
      projectIdFilter(projectId),
      idsFilter(peopleIds)
    ];

    if (query) {
      filter.push({
        match_phrase: {
          "specialty_eng.autocomplete_search": {
            query,
            slop: 5
          }
        }
      });
    }

    const request: SearchRequest = {
      index: this.peopleIndex,
      size: NO_RESULTS,
      query: {
        bool: {
          filter
        }
      },
      aggs: {
        specialty: {
          terms: {
            field: "specialty_eng",
            size: 100,
            exclude: "_BAD_",
            order: [
              {
                _count: "desc"
              }
            ],
            script: getAggregationsScript(query, peopleIds)
          }
        }
      }
    };

    this.logger.info(
      { data: JSON.stringify(request) },
      `Generated SPECIALTY AUTOSUGGEST query for project ${projectId} with 
      query ${query}`
    );

    const { aggregations } = await this.elasticService.query(request);

    const { buckets } = aggregations!
      .specialty as AggregationsTermsAggregateBase<DocCountBucket>;

    return (buckets as DocCountBucket[]).map(_.property("key"));
  }
}

function projectIdFilter(projectId: string) {
  return {
    term: {
      projectIds: projectId
    }
  };
}

function idsFilter(ids: string | Array<string>) {
  const type = Array.isArray(ids) ? "terms" : "term";

  return {
    [type]: {
      id: ids
    }
  };
}

function getAggregationsScript(query: string, ids: string | Array<string>) {
  if (!query) {
    return undefined;
  }

  return {
    id: "autocomplete",
    params: {
      query,
      id: ids
    }
  };
}
