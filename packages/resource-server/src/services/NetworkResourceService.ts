import {
  BulkNetworkRequest,
  NetworkResource,
  RPC_NAMESPACE_NETWORK,
  NetworkResponse,
  NetworkCollaborator,
  NetworkRequest,
  NetworkCollaboratorRequest,
  NetworkFilterAutocompleteRequest,
  NetworkFilterAggregation,
  BulkNetworkResponse
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import * as _ from "lodash";

import { NetworkQueryBuilder } from "./queryBuilders/NetworkQueryBuilder";
import { QueryParserService } from "./QueryParserService";
import { LanguageDetector } from "./KeywordSearchResourceServiceRewrite";
// @ts-ignore
import franc from "franc-min";
import { Service } from "typedi";
import {
  NetworkResponseAdapterService,
  HCPDocument,
  NestedFilteredMatchingAggregations
} from "./NetworkResponseAdapterService";
import { CHINESE, ENGLISH, JAPANESE, Language } from "./LanguageDetectService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";

export type NetworkFeatureFlags = {
  enableBrazilianClaims: boolean;
};

export const networkFeatureFlagTypes = ["enableBrazilianClaims"] as const;

export type NetworkFeatureFlag = (typeof networkFeatureFlagTypes)[number];

export const featureFlagDefaults: Readonly<
  Record<keyof NetworkFeatureFlags, { key: string; default: boolean }>
> = {
  enableBrazilianClaims: {
    key: "enable-brazilian-claims",
    default: false
  }
};

const STRING_LANGUAGE_TO_TYPE_LANGUAGE: Readonly<Map<string, Language>> =
  new Map([
    ["japanese", JAPANESE],
    ["chinese_simplified", CHINESE],
    ["chinese_traditional", CHINESE],
    ["english", ENGLISH],
    ["eng", ENGLISH],
    ["cmn", CHINESE],
    ["jpn", JAPANESE]
  ]);

@Service()
@RpcService()
export class NetworkResourceService
  extends RpcResourceService
  implements NetworkResource
{
  private readonly logger = createLogger(this);

  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService,
    private queryParserService: QueryParserService,
    private queryBuilder: NetworkQueryBuilder,
    private networkResponseAdapterService: NetworkResponseAdapterService,
    private featureFlagsService: FeatureFlagsService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_NETWORK,
      config.searchRedisOptions
    );
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.network.find-network")
  async findNetwork(
    request: Readonly<NetworkRequest>
  ): Promise<NetworkResponse> {
    const { projectFeatures, language, userId, projectId } = request;
    const queryParserResponse =
      await this.queryParserService.parseQueryWithQueryUnderstandingService(
        request.suppliedFilters.keyword,
        {
          projectSupportsAdvancedOperators: projectFeatures.advancedOperators
        }
      );

    const affiliatedInstitutionIds = await this.getAffiliations(request);

    const typeLanguage =
      STRING_LANGUAGE_TO_TYPE_LANGUAGE.get(language) || ENGLISH;
    const languageDetector = getLanguageDetector(typeLanguage);

    const featureFlags = await this.getFeatureFlagValues({
      userId,
      projectId
    });

    const elasticsearchRequest = this.queryBuilder.buildFindNetworkQuery(
      {
        ...request,
        language: typeLanguage
      },
      affiliatedInstitutionIds,
      queryParserResponse?.parsedQueryTree,
      languageDetector,
      featureFlags
    );

    if (!elasticsearchRequest) {
      return this.networkResponseAdapterService.emptyNetworkResponse();
    }

    this.logger.info(
      { data: elasticsearchRequest },
      "Performing search for collaborator network"
    );

    const { hits, aggregations, took } =
      await this.elasticService.query<HCPDocument>(elasticsearchRequest);

    this.logger.info({ took }, "execution time");

    return await this.networkResponseAdapterService.adaptToNetwork(
      request,
      hits,
      aggregations,
      typeLanguage
    );
  }

  @RpcMethod()
  @Trace("h1-search.network.find-bulk-network")
  async findBulkNetwork(
    request: Readonly<BulkNetworkRequest>
  ): Promise<BulkNetworkResponse[]> {
    const { projectFeatures, language, userId, projectId } = request;
    const queryParserResponse =
      await this.queryParserService.parseQueryWithQueryUnderstandingService(
        request.suppliedFilters.keyword,
        {
          projectSupportsAdvancedOperators: projectFeatures.advancedOperators
        }
      );

    const affiliatedInstitutionIds =
      await this.getAffiliationsForBulkNetworkRequest(request);

    const typeLanguage =
      STRING_LANGUAGE_TO_TYPE_LANGUAGE.get(language) || ENGLISH;
    const languageDetector = getLanguageDetector(typeLanguage);

    const featureFlags = await this.getFeatureFlagValues({
      userId,
      projectId
    });

    const elasticsearchRequest = this.queryBuilder.buildBulkFindNetworkQuery(
      {
        ...request,
        language: typeLanguage
      },
      affiliatedInstitutionIds,
      queryParserResponse?.parsedQueryTree,
      languageDetector,
      featureFlags
    );

    if (!elasticsearchRequest) {
      return [
        {
          totalCollaborators: 0,
          totalPublicationCollaborators: 0,
          totalCongressCollaborators: 0,
          totalTrialCollaborators: 0
        }
      ];
    }

    this.logger.info(
      { data: elasticsearchRequest },
      "Performing search for bulk collaborator network"
    );

    const { aggregations, took } = await this.elasticService.query<HCPDocument>(
      elasticsearchRequest
    );

    this.logger.info({ took }, "execution time");

    return this.networkResponseAdapterService.adaptToBulkNetwork(
      request.personIds,
      aggregations
    );
  }

  async getAffiliations(
    request: Readonly<NetworkRequest | NetworkCollaboratorRequest>
  ) {
    const elasticsearchRequest =
      this.queryBuilder.buildFindAffiliationsQuery(request);

    this.logger.info(
      { data: elasticsearchRequest },
      `Finding affiliations for HCP ${request.personId}`
    );

    const { hits, took } = await this.elasticService.query<HCPDocument>(
      elasticsearchRequest
    );

    this.logger.info({ took }, "execution time");

    return this.networkResponseAdapterService.adaptToAffiliationsList(hits);
  }

  async getAffiliationsForBulkNetworkRequest(
    request: Readonly<BulkNetworkRequest>
  ) {
    const elasticsearchRequest =
      this.queryBuilder.buildFindAffiliationsQueryForBulkNetworkRequest(
        request.personIds
      );

    this.logger.info(
      { data: elasticsearchRequest },
      `Finding affiliations for HCP ${request.personIds}`
    );

    const { hits, took } = await this.elasticService.query<HCPDocument>(
      elasticsearchRequest
    );

    this.logger.info({ took }, "execution time");

    return this.networkResponseAdapterService.adaptToAffiliationsList(hits);
  }

  @RpcMethod()
  @Trace("h1-search.network.find-shared-works")
  async findCollaborator(
    request: Readonly<NetworkCollaboratorRequest>
  ): Promise<NetworkCollaborator> {
    const { suppliedFilters, projectFeatures, language, page } = request;
    const queryParserResponse =
      await this.queryParserService.parseQueryWithQueryUnderstandingService(
        suppliedFilters.keyword,
        {
          projectSupportsAdvancedOperators: projectFeatures.advancedOperators
        }
      );

    const typeLanguage =
      STRING_LANGUAGE_TO_TYPE_LANGUAGE.get(language) || ENGLISH;
    const languageDetector = getLanguageDetector(typeLanguage);

    const affiliatedInstitutionIds = await this.getAffiliations(request);

    const elasticsearchRequest = this.queryBuilder.buildFindCollaboratorQuery(
      request,
      affiliatedInstitutionIds,
      queryParserResponse?.parsedQueryTree,
      languageDetector
    );

    this.logger.info(
      { data: elasticsearchRequest },
      "Performing search for shared works"
    );

    const { hits, took } = await this.elasticService.query<HCPDocument>(
      elasticsearchRequest
    );

    this.logger.info({ took }, "execution time");

    return this.networkResponseAdapterService.adaptToCollaborator(
      request,
      hits,
      page,
      typeLanguage
    );
  }

  @RpcMethod()
  @Trace("h1-search.network.filter-autocomplete")
  async filterAutocomplete(
    request: Readonly<NetworkFilterAutocompleteRequest>
  ): Promise<NetworkFilterAggregation[]> {
    const {
      filterField,
      suppliedFilters,
      projectFeatures,
      language,
      projectId,
      userId
    } = request;
    const queryParserResponse =
      await this.queryParserService.parseQueryWithQueryUnderstandingService(
        suppliedFilters.keyword,
        {
          projectSupportsAdvancedOperators: projectFeatures.advancedOperators
        }
      );

    const languageDetector = getLanguageDetector(language);

    const featureFlags = await this.getFeatureFlagValues({
      userId,
      projectId
    });

    const elasticsearchRequest =
      this.queryBuilder.buildLocationFilterAutocomplete(
        request,
        queryParserResponse?.parsedQueryTree,
        languageDetector,
        featureFlags
      );

    if (!elasticsearchRequest) {
      return [];
    }

    this.logger.info(
      { data: elasticsearchRequest },
      "Performing network location filter autocomplete"
    );

    const { aggregations, took } = await this.elasticService.query(
      elasticsearchRequest
    );

    this.logger.info({ took }, "execution time");

    return this.networkResponseAdapterService.adaptToFilterAggregations(
      aggregations as NestedFilteredMatchingAggregations,
      filterField
    );
  }

  @Trace("h1-search.network.get-feature-flag-values")
  private async getFeatureFlagValues({
    userId,
    projectId
  }: {
    userId?: string | null;
    projectId?: string | null;
  }): Promise<NetworkFeatureFlags> {
    const featureFlags: Partial<NetworkFeatureFlags> = {};

    const user = userId && projectId ? { userId, projectId } : undefined;
    const flagsState = await this.featureFlagsService.getAllFlags(user);

    for (const flag of networkFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as NetworkFeatureFlags;
  }
}

function getLanguageDetector(userPreferredLanguage: string): LanguageDetector {
  return (query: string): Language => {
    const detectedLanguage = franc(query, {
      minLength: 1,
      only: [ENGLISH, CHINESE, JAPANESE]
    });

    if (detectedLanguage === CHINESE && userPreferredLanguage === JAPANESE) {
      return JAPANESE;
    }

    return detectedLanguage === "und"
      ? ENGLISH
      : (detectedLanguage as Language);
  };
}
