import { ClaimCodeService } from "./ClaimCodeService";
import { ClaimCodesRepository } from "@h1nyc/pipeline-repositories";
import { ClaimCode } from "@h1nyc/pipeline-entities";
import { createMockInstance } from "../util/TestUtils";
import { faker } from "@faker-js/faker";

// Mock the logger
jest.mock("../lib/Logger", () => ({
  createLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  })
}));

describe("ClaimCodeService", () => {
  let claimCodeService: ClaimCodeService;
  let mockClaimCodesRepository: jest.Mocked<ClaimCodesRepository>;

  beforeEach(() => {
    mockClaimCodesRepository = createMockInstance(ClaimCodesRepository);
    claimCodeService = new ClaimCodeService(mockClaimCodesRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Helper function to create mock ClaimCode objects
  const createMockClaimCode = (
    overrides: Partial<ClaimCode> = {}
  ): ClaimCode => {
    return {
      id: faker.datatype.number(),
      code: faker.datatype.string(10),
      scheme: faker.datatype.string(),
      schemeType: faker.helpers.arrayElement(["D", "P"]),
      countryDescriptions: [
        {
          Country: faker.address.countryCode(),
          Description: faker.lorem.sentence()
        },
        {
          Country: faker.address.countryCode(),
          Description: faker.lorem.sentence()
        }
      ],
      ...overrides
    } as ClaimCode;
  };

  describe("claimCodeMetadataMapKeyGenerator", () => {
    it("should generate correct key format", () => {
      const code = "A123";
      const country = "US";

      const result = claimCodeService.claimCodeMetadataMapKeyGenerator(
        code,
        country
      );

      expect(result).toBe("A123::US");
    });

    it("should handle empty strings", () => {
      const result = claimCodeService.claimCodeMetadataMapKeyGenerator("", "");

      expect(result).toBe("::");
    });

    it("should handle special characters", () => {
      const code = "A-123.5";
      const country = "US-CA";

      const result = claimCodeService.claimCodeMetadataMapKeyGenerator(
        code,
        country
      );

      expect(result).toBe("A-123.5::US-CA");
    });
  });

  describe("buildClaimCodeToMetadataDictionary", () => {
    it("should build correct metadata dictionary from claim codes", () => {
      const claimCodes = [
        createMockClaimCode({
          code: "A123",
          scheme: "ICD-10",
          countryDescriptions: [
            { Country: "US", Description: "Test diagnosis US" },
            { Country: "CA", Description: "Test diagnosis CA" }
          ]
        }),
        createMockClaimCode({
          code: "B456",
          scheme: "CPT",
          countryDescriptions: [
            { Country: "US", Description: "Test procedure US" }
          ]
        })
      ];

      // Access the private method through bracket notation for testing
      const result = (
        claimCodeService as any
      ).buildClaimCodeToMetadataDictionary(claimCodes);

      expect(result.size).toBe(3);
      expect(result.get("A123::US")).toEqual({
        scheme: "ICD-10",
        description: "Test diagnosis US"
      });
      expect(result.get("A123::CA")).toEqual({
        scheme: "ICD-10",
        description: "Test diagnosis CA"
      });
      expect(result.get("B456::US")).toEqual({
        scheme: "CPT",
        description: "Test procedure US"
      });
    });

    it("should handle empty claim codes array", () => {
      const result = (
        claimCodeService as any
      ).buildClaimCodeToMetadataDictionary([]);

      expect(result.size).toBe(0);
    });

    it("should handle claim codes with no country descriptions", () => {
      const claimCodes = [
        createMockClaimCode({
          code: "A123",
          scheme: "ICD-10",
          countryDescriptions: []
        })
      ];

      const result = (
        claimCodeService as any
      ).buildClaimCodeToMetadataDictionary(claimCodes);

      expect(result.size).toBe(0);
    });
  });

  describe("getDiagnosesClaimCodeMetadataMap", () => {
    it("should fetch diagnoses claim codes and return metadata map", async () => {
      const codesToFilter = ["A123", "B456"];
      const mockClaimCodes = [
        createMockClaimCode({
          code: "A123",
          scheme: "ICD-10",
          schemeType: "D",
          countryDescriptions: [
            { Country: "US", Description: "Diabetes mellitus" },
            { Country: "CA", Description: "Diabetes mellitus" }
          ]
        }),
        createMockClaimCode({
          code: "B456",
          scheme: "ICD-10",
          schemeType: "D",
          countryDescriptions: [{ Country: "US", Description: "Hypertension" }]
        })
      ];

      mockClaimCodesRepository.getClaimCodesBySchemeType.mockResolvedValue(
        mockClaimCodes
      );

      const result = await claimCodeService.getDiagnosesClaimCodeMetadataMap(
        codesToFilter
      );

      expect(
        mockClaimCodesRepository.getClaimCodesBySchemeType
      ).toHaveBeenCalledWith(codesToFilter, "D");
      expect(result.size).toBe(3);
      expect(result.get("A123::US")).toEqual({
        scheme: "ICD-10",
        description: "Diabetes mellitus"
      });
      expect(result.get("A123::CA")).toEqual({
        scheme: "ICD-10",
        description: "Diabetes mellitus"
      });
      expect(result.get("B456::US")).toEqual({
        scheme: "ICD-10",
        description: "Hypertension"
      });
    });

    it("should handle empty codes array", async () => {
      const codesToFilter: string[] = [];
      mockClaimCodesRepository.getClaimCodesBySchemeType.mockResolvedValue([]);

      const result = await claimCodeService.getDiagnosesClaimCodeMetadataMap(
        codesToFilter
      );

      expect(
        mockClaimCodesRepository.getClaimCodesBySchemeType
      ).toHaveBeenCalledWith(codesToFilter, "D");
      expect(result.size).toBe(0);
    });

    it("should handle repository errors", async () => {
      const codesToFilter = ["A123"];
      const error = new Error("Database connection failed");

      mockClaimCodesRepository.getClaimCodesBySchemeType.mockRejectedValue(
        error
      );

      await expect(
        claimCodeService.getDiagnosesClaimCodeMetadataMap(codesToFilter)
      ).rejects.toThrow("Database connection failed");
    });
  });

  describe("getProcedureClaimCodeMetadataMap", () => {
    it("should fetch procedure claim codes and return metadata map", async () => {
      const codesToFilter = ["P123", "P456"];
      const mockClaimCodes = [
        createMockClaimCode({
          code: "P123",
          scheme: "CPT",
          schemeType: "P",
          countryDescriptions: [
            { Country: "US", Description: "Surgical procedure" },
            { Country: "IT", Description: "Procedura chirurgica" }
          ]
        }),
        createMockClaimCode({
          code: "P456",
          scheme: "HCPCS",
          schemeType: "P",
          countryDescriptions: [
            { Country: "US", Description: "Medical device" }
          ]
        })
      ];

      mockClaimCodesRepository.getClaimCodesBySchemeType.mockResolvedValue(
        mockClaimCodes
      );

      const result = await claimCodeService.getProcedureClaimCodeMetadataMap(
        codesToFilter
      );

      expect(
        mockClaimCodesRepository.getClaimCodesBySchemeType
      ).toHaveBeenCalledWith(codesToFilter, "P");
      expect(result.size).toBe(3);
      expect(result.get("P123::US")).toEqual({
        scheme: "CPT",
        description: "Surgical procedure"
      });
      expect(result.get("P123::IT")).toEqual({
        scheme: "CPT",
        description: "Procedura chirurgica"
      });
      expect(result.get("P456::US")).toEqual({
        scheme: "HCPCS",
        description: "Medical device"
      });
    });

    it("should handle empty codes array", async () => {
      const codesToFilter: string[] = [];
      mockClaimCodesRepository.getClaimCodesBySchemeType.mockResolvedValue([]);

      const result = await claimCodeService.getProcedureClaimCodeMetadataMap(
        codesToFilter
      );

      expect(
        mockClaimCodesRepository.getClaimCodesBySchemeType
      ).toHaveBeenCalledWith(codesToFilter, "P");
      expect(result.size).toBe(0);
    });

    it("should handle repository errors", async () => {
      const codesToFilter = ["P123"];
      const error = new Error("Database connection failed");

      mockClaimCodesRepository.getClaimCodesBySchemeType.mockRejectedValue(
        error
      );

      await expect(
        claimCodeService.getProcedureClaimCodeMetadataMap(codesToFilter)
      ).rejects.toThrow("Database connection failed");
    });
  });

  describe("integration tests", () => {
    it("should handle the example from comments - same code different countries", async () => {
      // Test the example mentioned in the comments: 5363 is a diagnoses code in US but a procedure code in Italy
      const diagnosesCodeToFilter = ["5363"];
      const procedureCodeToFilter = ["5363"];

      const mockDiagnosesClaimCodes = [
        createMockClaimCode({
          code: "5363",
          scheme: "ICD-10",
          schemeType: "D",
          countryDescriptions: [
            { Country: "US", Description: "Specific diagnosis" }
          ]
        })
      ];

      const mockProcedureClaimCodes = [
        createMockClaimCode({
          code: "5363",
          scheme: "ICD-9-CM",
          schemeType: "P",
          countryDescriptions: [
            { Country: "IT", Description: "Specific procedure" }
          ]
        })
      ];

      mockClaimCodesRepository.getClaimCodesBySchemeType
        .mockResolvedValueOnce(mockDiagnosesClaimCodes)
        .mockResolvedValueOnce(mockProcedureClaimCodes);

      const diagnosesResult =
        await claimCodeService.getDiagnosesClaimCodeMetadataMap(
          diagnosesCodeToFilter
        );
      const procedureResult =
        await claimCodeService.getProcedureClaimCodeMetadataMap(
          procedureCodeToFilter
        );

      expect(diagnosesResult.get("5363::US")).toEqual({
        scheme: "ICD-10",
        description: "Specific diagnosis"
      });
      expect(procedureResult.get("5363::IT")).toEqual({
        scheme: "ICD-9-CM",
        description: "Specific procedure"
      });

      // Verify they don't interfere with each other
      expect(diagnosesResult.get("5363::IT")).toBeUndefined();
      expect(procedureResult.get("5363::US")).toBeUndefined();
    });
  });
});
