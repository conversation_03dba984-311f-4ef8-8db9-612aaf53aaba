import {
  SearchHit,
  SearchHitsMetadata
} from "@elastic/elasticsearch/lib/api/types";
import {
  AffiliationHighlights,
  FOPersonSearchResponse,
  FOResultCardData,
  HCPLocation,
  PersonCard,
  PersonSearchResponse,
  QueryIntent as QueryIntentEnum,
  SpeakerCongressSession
} from "@h1nyc/search-sdk";
import _, { flatten, pickBy, startsWith, values } from "lodash";
import { Service } from "typedi";
import {
  CongressNestedDocument,
  HCPDocument,
  Location
} from "./KeywordSearchResourceServiceRewrite";
import {
  EMPTY_FILTER_COUNTS,
  EMPTY_RANGES,
  NO_AFFILIATION,
  Page,
  ZERO_MIN_MAX,
  getHIPAACompliantCount,
  lookupInstitutions,
  toAffiliation
} from "./KeywordSearchResponseAdapterService";
import {
  getHitsTotal,
  NameSearchHighConfPersonAdapterService,
  stripLanguageSuffix
} from "./NameSearchHighConfPersonAdapterService";
import { createLogger } from "../lib/Logger";
import { AFFILIATION_HIGHLIGHT_NAME_PREFIX } from "./queryBuilders/DefaultNameSearchBuilder";
import { JAPANESE, Language } from "./LanguageDetectService";
import { AffiliationAdapterService } from "./AffiliationAdapterService";
import { ZERO_SCORED_DOCUMENT_RESULT } from "@h1nyc/search-sdk/dist/";
import { NameSearchInput } from "@h1nyc/search-sdk/dist/interfaces/NameSearchInput";
import { NameSearchFeatureFlags } from "./NameSearchResourceServiceRewrite";

export const UNUSED_NUMBER_ARRAY: Array<number> = [];
const DEFAULT_SCORE = 0;

type CongressSession = Readonly<{
  id: string;
  title_eng: string;
  role: string;
}>;

@Service()
export class NameSearchResponseAdapterService {
  // @ts-ignore
  private readonly logger = createLogger(this);

  constructor(
    private nameSearchHighConfPersonHelper: NameSearchHighConfPersonAdapterService,
    private affiliationsAdapterService: AffiliationAdapterService
  ) {}

  private extractAffiliationHighlight(
    highlight: SearchHit<Record<string, any>>
  ): AffiliationHighlights {
    const affiliationId = _.get(highlight, 'fields["affiliations.id"][0]');
    const institutionId = _.get(
      highlight,
      'fields["affiliations.institution.id"][0]'
    );
    if (affiliationId && institutionId) {
      const city = _.get(
        highlight,
        'highlight["affiliations.institution.address.city"][0]'
      );
      const region = _.get(
        highlight,
        'highlight["affiliations.institution.address.region"][0]'
      );
      const country = _.get(
        highlight,
        'highlight["affiliations.institution.address.country"][0]'
      );

      return {
        affiliationId,
        highlight: {
          institution: {
            institutionId,
            address: {
              city,
              region,
              country
            }
          }
        }
      };
    } else return undefined;
  }

  private extractAffiliationInnerHits(hit: SearchHit<Readonly<HCPDocument>>) {
    const allValidAffiliationsInnerHits = pickBy(hit.inner_hits, (_, key) => {
      return startsWith(key, AFFILIATION_HIGHLIGHT_NAME_PREFIX);
    });

    return flatten(
      values(allValidAffiliationsInnerHits).map(
        (inner_hits) => inner_hits.hits.hits
      )
    );
  }

  adapt(
    queryLang: string,
    page: Readonly<Page>,
    hits: Readonly<SearchHitsMetadata<Readonly<HCPDocument>>>,
    queryIntents: QueryIntentEnum[],
    { claims, referrals }: { claims: boolean; referrals: boolean },
    enableHighConfHCPFeature: boolean,
    featureFlags: NameSearchFeatureFlags,
    input: Readonly<NameSearchInput> | undefined = undefined,
    useBothCmnAndJpnFields = false
  ): PersonSearchResponse {
    const highConfidenceHCP =
      enableHighConfHCPFeature && queryIntents.includes(QueryIntentEnum.PERSON)
        ? this.nameSearchHighConfPersonHelper.getHighConfidenceHcpInfo(hits)
        : { highConfHcpPresent: false, score: 0 };

    const hitsWithSource = hits.hits.filter((doc) => !!doc._source);

    const institutionsKeyedById = lookupInstitutions(hitsWithSource);

    const l1IndicationsResponse = createPeopleToIndicationsMap(
      hits,
      "l1_indications"
    );
    const topL3IndicationsResponse = createPeopleToIndicationsMap(
      hits,
      "l3_indications"
    );

    const res: PersonSearchResponse = {
      total: getHitsTotal(hits.total ?? 0),
      from: page.from,
      normalizedRange: { ...ZERO_MIN_MAX },
      pageSize: page.size,
      ranges: EMPTY_RANGES,
      filterCounts: EMPTY_FILTER_COUNTS,
      queryIntent: queryIntents,
      results: hits.hits.map((hit): PersonCard => {
        const {
          id,
          h1dn_id,
          totalWorks,
          designations,
          trialCount,
          paymentTotal,
          publicationCount,
          citationTotal,
          congressCount,
          microBloggingTotal,
          DRG_diagnosesCount,
          DRG_proceduresCount,
          referralsReceivedCount,
          referralsSentCount,
          presentWorkInstitutionCount,
          num_prescriptions,
          hasCtms,
          projectIds,
          inCtmsNetwork,
          isInactive,
          isIndustry,
          hasSocietyAffiliation,
          trialEnrollmentRate,
          totalPatientDocs,
          isGlobalLeader,
          nationalLeader,
          regionalLeader,
          localLeader,
          DRG_diagnosesUniqueCount,
          DRG_proceduresUniqueCount,
          prescriptions_patient_count
        } = hit._source!;

        const languageSpecificFields = extractLanguageSpecificFields(
          queryLang,
          hit._source,
          useBothCmnAndJpnFields
        );

        const highConfPersonFields = enableHighConfHCPFeature
          ? this.nameSearchHighConfPersonHelper.extractHighConfPersonFields(
              highConfidenceHCP,
              hit
            )
          : undefined;

        const affiliationInnerHits = this.extractAffiliationInnerHits(hit);

        const affiliations = _.defaultTo(hit._source?.affiliations, [])
          .filter((affiliation) => affiliation.type !== NO_AFFILIATION)
          .map((affiliation) =>
            toAffiliation(
              affiliation,
              queryLang as Language,
              institutionsKeyedById
            )
          );

        const isH1dn = !!(!hit._source?.id && hit._source?.h1dn_id);
        const territoryAffiliations =
          hit.inner_hits?.custom_territory_affiliations?.hits.hits;
        const territoryAffiliationNames = territoryAffiliations
          ? territoryAffiliations.map(
              (innerHit) =>
                _.get(
                  innerHit.fields,
                  "affiliations.institution.name.keyword"
                )[0]
            )
          : [];
        const formattedTopAffiliations =
          this.affiliationsAdapterService.searchTopAffiliations(
            affiliations,
            input,
            isH1dn,
            hit._source?.locations,
            territoryAffiliationNames
          );

        const formattedTopTLAffiliations =
          this.affiliationsAdapterService.searchTopTLAffiliations(
            affiliations,
            input,
            isH1dn,
            hit._source?.locations,
            territoryAffiliationNames
          );

        let hasCTMSData: boolean | undefined;
        if (featureFlags.enableCTMSV2) {
          hasCTMSData = inCtmsNetwork ?? hasCtms;
        }
        const uniquePatientToggleFromInput =
          input?.suppliedFilters.claims.showUniquePatients?.value ?? false;

        const congressSessions = extractMatchingCongressSessions(
          input,
          hit._source?.congress
        );

        const diagnosesUniqueCount = getHIPAACompliantCount(
          DRG_diagnosesUniqueCount
        );
        const proceduresUniqueCount = getHIPAACompliantCount(
          DRG_proceduresUniqueCount
        );
        const prescriptionsUniqueCount = getHIPAACompliantCount(
          prescriptions_patient_count
        );

        return {
          ...highConfPersonFields,
          ...languageSpecificFields,
          h1dnId: h1dn_id || null,
          designations: designations || [],
          languageCode: queryLang,
          diagnosesCount: claims
            ? uniquePatientToggleFromInput
              ? totalPatientDocs
              : DRG_diagnosesCount
            : DEFAULT_SCORE,
          proceduresCount: claims ? DRG_proceduresCount : DEFAULT_SCORE,
          referralsReceivedCount: referrals
            ? referralsReceivedCount
            : DEFAULT_SCORE,
          referralsSentCount: referrals ? referralsSentCount : DEFAULT_SCORE,
          socialMediaMentionsTotal: microBloggingTotal || DEFAULT_SCORE,
          affiliations: [],
          formattedTopAffiliations,
          formattedTopTLAffiliations,
          score: hit._score ?? DEFAULT_SCORE,
          countPresentWorkAffiliations: presentWorkInstitutionCount,
          prescriptionsCount: num_prescriptions,
          citationCount: citationTotal,
          congresses: congressCount,
          personId: id || "",
          totalWorks: totalWorks || DEFAULT_SCORE,
          countPublications: publicationCount || DEFAULT_SCORE,
          countClinicalTrials: trialCount || DEFAULT_SCORE,
          sumPayments: paymentTotal || DEFAULT_SCORE,
          grants: DEFAULT_SCORE,
          sumGrants: DEFAULT_SCORE,
          congressesDates: UNUSED_NUMBER_ARRAY,
          paymentDates: UNUSED_NUMBER_ARRAY,
          publicationDates: UNUSED_NUMBER_ARRAY,
          trialDates: UNUSED_NUMBER_ARRAY,
          citationCountAvg: DEFAULT_SCORE,
          infoRequestsResolved: null,
          hasCTMSData,

          tags: [],
          scores: {
            personId: id || "",
            normalizedRange: { ...ZERO_MIN_MAX },
            ...defaultScoreRanges
          },
          affiliationHighlights: affiliationInnerHits
            ?.map(this.extractAffiliationHighlight)
            .filter((highlight) => highlight !== undefined),
          isOutsideUsersSlice: !_.includes(projectIds, input?.projectId),
          l1Indications: l1IndicationsResponse.get(hit._id),
          topL3Indications: topL3IndicationsResponse.get(hit._id),
          locations: extractLocationField(hit._source),
          isInactive,
          isIndustry,
          isSocietyMember: hasSocietyAffiliation,
          trialEnrollmentRate,
          congressSessions,
          isGlobalLeader,
          isNationalLeader: nationalLeader?.value,
          isRegionalLeader: regionalLeader?.value,
          isLocalLeader: localLeader?.value,
          diagnosesUniqueCount,
          proceduresUniqueCount,
          prescriptionsUniqueCount
        };
      })
    };

    return res;
  }

  adaptFOResponse(
    queryLang: string,
    page: Readonly<Page>,
    hits: Readonly<SearchHitsMetadata<Readonly<HCPDocument>>>
  ): FOPersonSearchResponse {
    const res: FOPersonSearchResponse = {
      total: getHitsTotal(hits.total ?? 0),
      from: page.from,
      pageSize: page.size,

      results: hits.hits.map((hit): FOResultCardData => {
        const { id, orcidId, designations, emails } = hit._source!;

        // Result hit could either be Chinese or Japanese KOL. Check matched queries to modify queryLang dynamically for each result
        if (hit.matched_queries?.includes(JAPANESE)) {
          queryLang = JAPANESE;
        }

        const languageSpecificFields = extractLanguageSpecificFields(
          queryLang,
          hit._source,
          false
        );

        return {
          ...languageSpecificFields,
          designations: designations || [],
          emails: emails || [],
          orcidId: orcidId,
          foPersonId: id,
          languageCode: queryLang,
          locations: extractLocationField(hit._source)
        };
      })
    };
    return res;
  }
}

interface languageSpecificFields {
  firstName: string;
  lastName: string;
  middleName: string;
  specialty: string[];
  name: string;
}

const extractLanguageSpecificFields = (
  languageCode: string,
  source: any,
  useBothCmnAndJpnFields: boolean
) => {
  const defaultFields = {
    firstNameEng: source.firstName_eng,
    lastNameEng: source.lastName_eng,
    middleNameEng: source.middleName_eng,
    nameEng: source.name_eng
  };
  const baseFields = [
    "firstName",
    "lastName",
    "middleName",
    "specialty",
    "name"
  ];

  //If we are searching across both fields we would expect to get a match in only one of them.
  // HCPs that have both a japanese and a chinese name will have their japanese name displayed
  if (useBothCmnAndJpnFields) {
    const cmnFields = baseFields.map((prop) => `${prop}_cmn`);
    const jpnFields = baseFields.map((prop) => `${prop}_jpn`);
    const cmnAdditionalFields = _(source)
      .pick(cmnFields)
      .mapKeys((value, key) => stripLanguageSuffix(key))
      .value() as languageSpecificFields;
    const jpnAdditionalFields = _(source)
      .pick(jpnFields)
      .mapKeys((value, key) => stripLanguageSuffix(key))
      .value() as languageSpecificFields;
    if (
      !!jpnAdditionalFields.firstName?.length ||
      !!jpnAdditionalFields.lastName?.length ||
      !!jpnAdditionalFields.name?.length
    ) {
      return { ...defaultFields, ...jpnAdditionalFields };
    } else {
      return { ...defaultFields, ...cmnAdditionalFields };
    }
  }
  const fields = [
    "firstName",
    "lastName",
    "middleName",
    "specialty",
    "name"
  ].map((prop) => `${prop}_${languageCode}`);

  const additionalFields: languageSpecificFields = _(source)
    .pick(fields)
    .mapKeys((value, key) => stripLanguageSuffix(key))
    .value() as languageSpecificFields;
  return { ...defaultFields, ...additionalFields };
};

export const extractLocationField = (source: any) => {
  const hcpLocations = source?.locations?.map(
    (location: Location): HCPLocation | null => {
      const { languageCode } = location;
      if (!languageCode) {
        return null;
      }

      const fields = ["city", "state", "country", "zipCode5"].map(
        (prop) => `${prop}_${languageCode}`
      );

      // Initialize with empty strings
      const locFields: {
        city: string;
        state: string;
        country: string;
        zipCode5: string;
      } = {
        city: "",
        state: "",
        country: "",
        zipCode5: ""
      };

      fields.forEach((field) => {
        const strippedFieldName = stripLanguageSuffix(field);
        locFields[strippedFieldName as keyof typeof locFields] = _.get(
          location,
          field,
          ""
        );
      });

      if (
        !locFields.city &&
        !locFields.state &&
        !locFields.country &&
        !locFields.zipCode5
      ) {
        return null;
      }

      return locFields;
    }
  );
  return (_.compact(hcpLocations) ?? []) as HCPLocation[];
};

export const defaultScoreRanges = {
  publications: ZERO_SCORED_DOCUMENT_RESULT,
  citations: ZERO_SCORED_DOCUMENT_RESULT,
  trials: ZERO_SCORED_DOCUMENT_RESULT,
  payments: ZERO_SCORED_DOCUMENT_RESULT,
  paymentsCount: ZERO_SCORED_DOCUMENT_RESULT,
  grants: ZERO_SCORED_DOCUMENT_RESULT,
  grantsCount: ZERO_SCORED_DOCUMENT_RESULT,
  congresses: ZERO_SCORED_DOCUMENT_RESULT,
  collaborators: ZERO_SCORED_DOCUMENT_RESULT,
  socialMediaMentions: ZERO_SCORED_DOCUMENT_RESULT,
  procedures: {
    maxValue: 100,
    minValue: 1,
    normalizedValue: 0,
    percentile: 0,
    value: 10
  },
  diagnoses: {
    maxValue: 100,
    minValue: 1,
    normalizedValue: 10,
    percentile: 0,
    value: 10
  },
  prescriptions: ZERO_SCORED_DOCUMENT_RESULT,
  referralsReceived: ZERO_SCORED_DOCUMENT_RESULT,
  referralsSent: ZERO_SCORED_DOCUMENT_RESULT,

  h1Score: 0,
  profile: ZERO_SCORED_DOCUMENT_RESULT
};

function createPeopleToIndicationsMap(
  nameSearchQueryHits: Readonly<SearchHitsMetadata<Readonly<HCPDocument>>>,
  fieldName: "l1_indications" | "l3_indications"
): Map<string, string[]> {
  return nameSearchQueryHits.hits.reduce((acc: Map<string, string[]>, hit) => {
    acc.set(
      hit._id,
      hit?.inner_hits?.[fieldName]?.hits.hits.map(
        extractIndicationNameFromInnerHit
      ) || []
    );
    return acc;
  }, new Map<string, string[]>());
}

function extractIndicationNameFromInnerHit(
  indicationInnerHits: SearchHit<Record<string, any>>
) {
  const indicationName =
    _.get(indicationInnerHits, 'fields["indications.indication.keyword"][0]') ??
    "";
  return indicationName;
}

function toSpeakerCongressSession(
  congressSession: CongressSession
): SpeakerCongressSession {
  return {
    id: congressSession.id,
    name: congressSession.title_eng,
    role: congressSession.role
  };
}

function extractMatchingCongressSessions(
  searchInput?: NameSearchInput,
  congressSessions?: CongressNestedDocument[]
) {
  if (!searchInput?.useCongressContributorRanking || !congressSessions) {
    return undefined;
  }

  const congressName = searchInput.suppliedFilters.congresses.name.values[0];

  return congressSessions
    .filter((congressSession) => congressSession.name_eng === congressName)
    .map(toSpeakerCongressSession);
}
