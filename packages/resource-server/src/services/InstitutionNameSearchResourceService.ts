/* eslint-disable  @typescript-eslint/no-non-null-assertion */
/* eslint-disable  @typescript-eslint/no-explicit-any */
import { Service } from "typedi";
import { ElasticSearchInstitutionsService } from "./ElasticSearchInstitutionsService";
import { InstitutionNameSearchQueryBuilder } from "./queryBuilders/InstitutionNameSearchQueryBuilder";
import {
  InstitutionSortOptions,
  InstitutionsSearchInput
} from "@h1nyc/search-sdk";
import _ from "lodash";
import {
  CLAIMS_DESCRIPTION_ANALYZER,
  ElasticsearchInstitutionDoc,
  InstitutionSearchFeatureFlags,
  QueryBuilderArguments,
  RequestType,
  SOURCE_INCLUDES,
  SOURCE_INCLUDES_FOR_BULK_ENTITY_SEARCH,
  buildAggregationsForSearchRequest,
  buildElasticsearchFiltersFromInputFilters,
  buildFilterAggregatesForTopOptions,
  buildInstitutionIdFilter,
  buildRequestForClaims,
  buildRequestForExcludedClaims,
  filtersPresent,
  internalCountFieldForNestedClaimsQuery,
  toNestedClaimsConstantScoreQuery,
  toNestedMatchAllClaimsQuery
} from "./InstitutionsResourceService";
import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/typesWithBodyKey";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import {
  CountRequest,
  SearchRequest,
  SearchResponse
} from "@elastic/elasticsearch/lib/api/types";
import { ConfigService } from "./ConfigService";
import { InstitutionTagResourceClient } from "@h1nyc/account-sdk";
import { buildTermsQuery } from "../util/QueryBuildingUtils";
import { createLogger } from "../lib/Logger";
import { extractClaimsCode } from "./KeywordFilterClauseBuilderService";
import { RulesParserService } from "./RulesParserService";

const EMPTY_SEARCH_RESPONSE = {
  took: 0,
  timed_out: false,
  _shards: {
    failed: 0,
    successful: 0,
    total: 0
  },
  hits: {
    total: 0,
    hits: []
  }
};

@Service()
export class InstitutionNameSearchResourceService {
  private readonly logger = createLogger(this);
  private institutionsIndexName: string;

  constructor(
    config: ConfigService,
    private rulesParserService: RulesParserService,
    private elasticSearchService: ElasticSearchInstitutionsService,
    private institutionTagResourceClient: InstitutionTagResourceClient,
    private institutionNameSearchQueryBuilder: InstitutionNameSearchQueryBuilder
  ) {
    this.institutionsIndexName = config.elasticInstitutionsIndex;
  }

  public async searchInstitutionsByName(
    request: Readonly<InstitutionsSearchInput>,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>,
    isBulkSearch: boolean,
    requestType: RequestType,
    extraFilters: ReadonlyArray<QueryDslQueryContainer>,
    resolvedIndication: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>
  ): Promise<Readonly<SearchResponse<ElasticsearchInstitutionDoc>> | number> {
    if (_.isEmpty(request.query)) {
      return EMPTY_SEARCH_RESPONSE;
    }

    const searchRequest = await this.buildNameSearchRequest(
      request,
      featureFlags,
      isBulkSearch,
      requestType,
      extraFilters,
      resolvedIndication,
      queryUnderstandingServiceResponse
    );

    if (!searchRequest) {
      return EMPTY_SEARCH_RESPONSE;
    }

    if (requestType === "count") {
      this.logger.info({ searchRequest }, "elasticsearch count request");
      const count = await this.elasticSearchService.count(searchRequest);
      return count.count;
    }

    this.logger.info(
      {
        searchRequest
      },
      "elasticsearch institution name search request"
    );

    return await this.elasticSearchService.query<ElasticsearchInstitutionDoc>(
      searchRequest
    );
  }

  public async buildNameSearchRequest(
    input: Readonly<InstitutionsSearchInput>,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>,
    isBulkSearch: boolean,
    requestType: RequestType,
    extraFilters: ReadonlyArray<QueryDslQueryContainer> = [],
    resolvedIndication: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>
  ): Promise<SearchRequest | null> {
    let request = this.buildBaseRequest(
      input,
      featureFlags,
      isBulkSearch,
      extraFilters,
      resolvedIndication
    );

    const isDiversitySort = input.sortBy === InstitutionSortOptions.DIVERSITY;
    if (this.hasTagFilters(input)) {
      const requestWithTags = await this.buildFiltersForTags(
        input,
        request,
        isDiversitySort,
        featureFlags
      );

      if (requestWithTags) {
        request = requestWithTags;
      } else {
        return null;
      }
    }

    if (request) {
      request.aggs = buildAggregationsForSearchRequest(
        input,
        featureFlags,
        queryUnderstandingServiceResponse
      );
    }

    if (
      filtersPresent(_.omit(input.filters?.claimsFilters, "showUniquePatients"))
    ) {
      request = buildRequestForClaims(
        input,
        request,
        featureFlags,
        undefined,
        undefined,
        undefined,
        input.sortBy,
        input.filters?.diversityFilters?.race ?? undefined
      );
    }

    if (filtersPresent(input.filters?.exclusionClaims)) {
      request = buildRequestForExcludedClaims(
        input.filters!.exclusionClaims!,
        request,
        featureFlags
      );
    }

    if (requestType === "count") {
      const parentQuery: QueryDslQueryContainer = {
        has_parent: {
          parent_type: "iol",
          query: {
            bool: {
              filter: request.query
            }
          }
        }
      };
      const claimsFilter = this.buildPatientClaimsFilter(input);
      const countQuery: QueryDslQueryContainer = {
        bool: {
          filter: [parentQuery, ...claimsFilter]
        }
      };
      const countRequest: CountRequest = {
        index: this.institutionsIndexName,
        query: countQuery
      };

      return countRequest;
    }

    return request;
  }

  private addNameSearchQueryToRequest(query: string, request: SearchRequest) {
    const requestWithNameQuery = _.cloneDeep(request);
    const nameSearchQuery =
      this.institutionNameSearchQueryBuilder.buildNameSearchQuery(query);

    if (!nameSearchQuery) {
      return request;
    }

    requestWithNameQuery.query!.function_score!.query!.bool!.must = [
      nameSearchQuery.function_score!.query!
    ];

    requestWithNameQuery.query!.function_score!.functions!.push(
      ...nameSearchQuery.function_score!.functions!
    );

    return requestWithNameQuery;
  }

  private buildBaseRequest(
    input: InstitutionsSearchInput,
    featureFlags: InstitutionSearchFeatureFlags,
    isBulkSearch: boolean,
    extraFilters: ReadonlyArray<QueryDslQueryContainer>,
    resolvedIndication: string[]
  ): SearchRequest {
    const filters = buildElasticsearchFiltersFromInputFilters(
      {
        ...input,
        showAllTypesOfInstitutions: true
      },
      featureFlags,
      extraFilters,
      resolvedIndication
    );

    const query: QueryDslQueryContainer = {
      function_score: {
        boost_mode: "replace",
        score_mode: "sum",
        query: {
          bool: {
            filter: filters
          }
        },
        functions: []
      }
    };

    let request: SearchRequest = {
      index: this.institutionsIndexName,
      track_total_hits: true,
      _source: {
        include: !isBulkSearch
          ? SOURCE_INCLUDES
          : SOURCE_INCLUDES_FOR_BULK_ENTITY_SEARCH
      },
      query,
      aggs: buildFilterAggregatesForTopOptions(
        input.consistentLocationFilter,
        featureFlags
      )
    };

    if (input.exportsSelections?.matchedClaimsDetails) {
      const shoulds: QueryDslQueryContainer[] = [];
      const diagnosesICDFilterApplied =
        input.filters?.claimsFilters?.diagnosesICD;
      const proceduresCPTFilterApplied =
        input.filters?.claimsFilters?.proceduresCPT;

      if (
        diagnosesICDFilterApplied?.length ||
        proceduresCPTFilterApplied?.length
      ) {
        if (diagnosesICDFilterApplied?.length) {
          const diagnosesCountField = internalCountFieldForNestedClaimsQuery(
            input.filters!.claimsFilters!,
            "diagnoses",
            featureFlags
          );
          const diagnosesDetailsQueryArguments: QueryBuilderArguments = {
            path: "diagnoses",
            fields: ["description_eng", "code_eng"],
            searchQuery: diagnosesICDFilterApplied!
              .map(extractClaimsCode)
              .join("|"),
            innerHits: {
              name: "diagnoses_collection",
              _source: true,
              docvalue_fields: [diagnosesCountField],
              sort: [
                {
                  [diagnosesCountField]: {
                    order: "desc"
                  }
                }
              ],
              size: 5
            },
            analyzer: CLAIMS_DESCRIPTION_ANALYZER
          };
          shoulds.push(
            toNestedClaimsConstantScoreQuery(
              diagnosesDetailsQueryArguments,
              featureFlags
            )
          );
        }

        if (proceduresCPTFilterApplied?.length) {
          const proceduresCountField = internalCountFieldForNestedClaimsQuery(
            input.filters!.claimsFilters!,
            "procedures",
            featureFlags
          );
          const procedureDetailsQueryArguments: QueryBuilderArguments = {
            path: "procedures",
            fields: ["description_eng", "code_eng"],
            searchQuery: proceduresCPTFilterApplied!
              .map(extractClaimsCode)
              .join("|"),
            innerHits: {
              name: "procedures_collection",
              _source: true,
              docvalue_fields: [proceduresCountField],
              sort: [
                {
                  [proceduresCountField]: {
                    order: "desc"
                  }
                }
              ],
              size: 5
            },
            analyzer: CLAIMS_DESCRIPTION_ANALYZER
          };
          shoulds.push(
            toNestedClaimsConstantScoreQuery(
              procedureDetailsQueryArguments,
              featureFlags
            )
          );
        }
      } else {
        shoulds.push(...toNestedMatchAllClaimsQuery(featureFlags));
      }

      request.query!.function_score!.query!.bool!.should = shoulds;
    }

    if (input.query) {
      request = this.addNameSearchQueryToRequest(input.query!, request);
    }

    if (input.paging?.limit) {
      request.size = input.paging.limit;
    }
    if (input.paging?.offset) {
      request.from = input.paging.offset;
    }

    return request;
  }

  private async buildFiltersForTags(
    input: InstitutionsSearchInput,
    request: SearchRequest,
    isDiversitySort: boolean,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>
  ): Promise<SearchRequest | null> {
    const inclusionTags = input.filters!.tags;
    const exclusionTags = input.filters!.excludeTags;
    let inclusionTagsFilterQuery: QueryDslQueryContainer | null = null;
    if (inclusionTags) {
      inclusionTagsFilterQuery = await this.buildTagFilterQuery(
        inclusionTags,
        input.filters?.intersectTags ?? false,
        featureFlags
      );
      // handle saved searches with deleted inclusion tags.
      if (inclusionTagsFilterQuery === null) {
        return null;
      }
    }

    let exclusionTagsFilterQuery: QueryDslQueryContainer | null = null;
    if (exclusionTags) {
      exclusionTagsFilterQuery = await this.buildTagFilterQuery(
        exclusionTags,
        false,
        featureFlags
      );
      // handle saved searches with deleted exclusion tags.
      if (exclusionTagsFilterQuery === null) {
        return null;
      }
    }

    const filterQuery: QueryDslQueryContainer = {
      bool: {
        filter: inclusionTagsFilterQuery ?? undefined,
        must_not: exclusionTagsFilterQuery ?? undefined
      }
    };

    //Need to ensure that we understand the structure of the request before adding the filter
    //Diversity sort and non-diversity sort requests have different structures
    if (
      isDiversitySort &&
      request.query?.function_score?.query?.function_score &&
      Array.isArray(
        request.query!.function_score!.query!.function_score!.query!.bool!
          .filter!
      )
    ) {
      request.query!.function_score!.query!.function_score!.query!.bool!.filter!.push(
        filterQuery
      );
      return request;
    }

    if (
      request.query!.function_score &&
      Array.isArray(request.query!.function_score!.query!.bool!.filter!)
    ) {
      request.query!.function_score!.query!.bool!.filter!.push(filterQuery);
    } else if (
      request.query!.bool &&
      Array.isArray(request.query!.bool!.filter!)
    ) {
      request.query!.bool!.filter!.push(filterQuery);
    } else {
      request.query!.function_score! = {
        query: {
          bool: {
            filter: [filterQuery]
          }
        }
      };
    }

    return request;
  }

  private async buildTagFilterQuery(
    tags: string[],
    intersectTags: boolean,
    featureFlags: Readonly<InstitutionSearchFeatureFlags>
  ): Promise<QueryDslQueryContainer | null> {
    let filterQuery: QueryDslQueryContainer | null = null;
    if (featureFlags.enableTagsInElasticsearch) {
      filterQuery = buildTermsQuery("tagIds", tags);
    } else {
      const entities =
        await this.institutionTagResourceClient.getActiveInstitutionTagAssignmentsForTags(
          tags!
        );

      let institutionIds: string[] = [];
      if (intersectTags && tags.length > 1) {
        const institutionIdToTagIdMap = new Map<string, string[]>();
        entities.forEach((entity) => {
          if (entity?.entityId) {
            const tagIds = institutionIdToTagIdMap.get(entity.entityId) ?? [];
            tagIds.push(entity.tagId);
            institutionIdToTagIdMap.set(entity.entityId, tagIds);
          }
        });
        institutionIdToTagIdMap.forEach(
          (tagIds: string[], institutionId: string) => {
            if (tagIds.length === tags.length) {
              institutionIds.push(institutionId);
            }
          }
        );
      } else {
        institutionIds = _.compact(entities.map((obj) => obj.entityId));
      }

      this.logger.info(
        {
          tags,
          entityLength: entities.length,
          institutionIdLength: institutionIds.length
        },
        "Institution tag filter info"
      );

      if (institutionIds.length > 0) {
        filterQuery = buildInstitutionIdFilter(institutionIds);
      } else {
        // this is to handle saved searches with deleted tags
        return null;
      }
    }
    return filterQuery;
  }

  private hasTagFilters(input: InstitutionsSearchInput) {
    return (
      !_.isEmpty(input.filters?.tags) || !_.isEmpty(input.filters?.excludeTags)
    );
  }

  private buildPatientClaimsFilter(input: InstitutionsSearchInput) {
    const filters = this.rulesParserService.parseRulesToEsQueries(
      input.filters
    );
    // if (filters.length && !_.isEmpty(input.filters?.countries)) {
    //   filters.push({
    //     terms: {
    //       "patientClaims.country": input.filters!.countries!
    //     }
    //   });
    // }
    return filters;
  }
}
