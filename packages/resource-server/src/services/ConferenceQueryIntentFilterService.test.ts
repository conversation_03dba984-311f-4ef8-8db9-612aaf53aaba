import { faker } from "@faker-js/faker";
import { FilterInterface, KeywordSearchInput } from "@h1nyc/search-sdk";
import { QueryIntent } from "../proto/query_intent_pb";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import {
  createMockInstance,
  getEmptyKeywordSearchFilters
} from "../util/TestUtils";
import {
  ConferenceQueryIntentFilterService,
  CONGRESS_INTENT_SCORE_THRESHOLD,
  hasGoodConfidenceForConferenceIntent
} from "./ConferenceQueryIntentFilterService";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";

function generateFilters(
  overrides: Partial<FilterInterface> = {}
): FilterInterface {
  const baseFilters: FilterInterface = getEmptyKeywordSearchFilters();

  return { ...baseFilters, ...overrides };
}

function generateKeywordSearchInput(): KeywordSearchInput {
  return {
    projectId: faker.datatype.string(),
    page: {
      from: faker.datatype.number(),
      size: faker.datatype.number()
    },
    suppliedFilters: generateFilters(),
    sortBy: {
      publication: faker.datatype.number(),
      microBloggingCount: faker.datatype.number(),
      citation: faker.datatype.number(),
      trial: faker.datatype.number(),
      congress: faker.datatype.number(),
      payment: faker.datatype.number(),
      diagnoses: faker.datatype.number(),
      procedures: faker.datatype.number(),
      prescriptions: faker.datatype.number(),
      referralsReceived: faker.datatype.number(),
      referralsSent: faker.datatype.number(),
      grant: faker.datatype.number(),
      patientsDiversityRank: faker.datatype.number(),
      h1DefaultRank: faker.datatype.number()
    },
    projectFeatures: {
      advancedOperators: faker.datatype.boolean(),
      claims: faker.datatype.boolean(),
      referrals: faker.datatype.boolean(),
      engagementsV2: faker.datatype.boolean(),
      translateTaiwan: faker.datatype.boolean()
    }
  };
}

function generateQueryUnderstandingServiceResponseForConferenceIntent(
  score: number,
  intentType: QueryIntent.IntentType = QueryIntent.IntentType.CONFERENCE_NAME,
  synonyms = ""
): QueryUnderstandingServiceResponse {
  const quResponse = new QueryUnderstandingServiceResponse();
  const queryIntent = new QueryIntent();
  const intent = new QueryIntent.Intent();
  intent.setIntentType(intentType);
  intent.setScore(score);
  intent.setSynonyms(synonyms);
  queryIntent.setConferenceIntent(intent);
  quResponse.setQueryIntent(queryIntent);
  return quResponse;
}

describe("ConferenceQueryIntentFilterService", () => {
  describe("getQueryIntentFilterResponse", () => {
    it("should not apply any filter and should not return dynamic filters if conference intent score is zero", async () => {
      const queryUnderstandingServiceResponse =
        generateQueryUnderstandingServiceResponseForConferenceIntent(0);

      const configService = createMockInstance(ConfigService);
      const elasticService = createMockInstance(ElasticSearchService);
      const analyticsTrackerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const conferenceQueryIntentFilterService =
        new ConferenceQueryIntentFilterService(
          configService,
          elasticService,
          analyticsTrackerService
        );

      const input = generateKeywordSearchInput();
      input.query = faker.datatype.string();
      const response =
        await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
          queryUnderstandingServiceResponse,
          input,
          []
        );
      expect(response).toMatchObject({
        suggestedFilterValues: {
          congresses: {
            name: [],
            organizerName: []
          }
        }
      });

      expect(analyticsTrackerService.sendAnalyticsEvent).toHaveBeenCalledWith({
        event: "not_detected.congress_intent",
        properties: {
          query: input.query,
          score: 0,
          projectId: input.projectId
        },
        timestamp: expect.anything(),
        userId: input.userId
      });
    });

    it("should not apply any filter and should not return dynamic filters if conference intent score is less than confidence threshold", async () => {
      const queryUnderstandingServiceResponse =
        generateQueryUnderstandingServiceResponseForConferenceIntent(0.5);

      const configService = createMockInstance(ConfigService);
      const elasticService = createMockInstance(ElasticSearchService);
      const analyticsTrackerService = createMockInstance(
        SearchAnalyticsTracerService
      );
      const conferenceQueryIntentFilterService =
        new ConferenceQueryIntentFilterService(
          configService,
          elasticService,
          analyticsTrackerService
        );

      const input = generateKeywordSearchInput();
      input.query = faker.datatype.string();
      const response =
        await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
          queryUnderstandingServiceResponse,
          input,
          []
        );
      expect(response).toMatchObject({
        suggestedFilterValues: {
          congresses: {
            name: [],
            organizerName: []
          }
        }
      });

      expect(analyticsTrackerService.sendAnalyticsEvent).toHaveBeenCalledWith({
        event: "not_detected.congress_intent",
        properties: {
          query: input.query,
          score: 0.5,
          projectId: input.projectId
        },
        timestamp: expect.anything(),
        userId: input.userId
      });
    });

    describe("good confidence", () => {
      describe("conference intent type is organization", () => {
        it("should generate dynamic filters if conference organization aggregation values are present and when score is 1", async () => {
          const queryUnderstandingServiceResponse =
            generateQueryUnderstandingServiceResponseForConferenceIntent(
              1,
              QueryIntent.IntentType.CONFERENCE_ORG
            );

          const configService = createMockInstance(ConfigService);
          const elasticService = createMockInstance(ElasticSearchService);
          const conferenceNames = [
            faker.datatype.string(),
            faker.datatype.string()
          ];
          const orgName = faker.datatype.string();

          elasticService.query.mockReturnValueOnce({
            aggregations: {
              nested: {
                filtered_matching: {
                  org_name: {
                    buckets: [
                      {
                        key: orgName,
                        range: {
                          buckets: [
                            {
                              key: "latest",
                              conf_name: {
                                buckets: [
                                  {
                                    key: conferenceNames[0]
                                  }
                                ]
                              }
                            },
                            {
                              key: "upcoming",
                              conf_name: {
                                buckets: [
                                  {
                                    key: conferenceNames[1]
                                  }
                                ]
                              }
                            }
                          ]
                        }
                      }
                    ]
                  }
                }
              }
            }
          } as any);
          const analyticsTrackerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const conferenceQueryIntentFilterService =
            new ConferenceQueryIntentFilterService(
              configService,
              elasticService,
              analyticsTrackerService
            );

          const input = generateKeywordSearchInput();
          input.query = faker.datatype.string();
          const response =
            await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
              queryUnderstandingServiceResponse,
              input,
              []
            );
          expect(
            response.suggestedFilterValues?.congresses?.organizerName
          ).toEqual([orgName]);
          expect(response.suggestedFilterValues?.congresses?.name).toEqual([
            conferenceNames[1],
            conferenceNames[0]
          ]);
          expect(
            analyticsTrackerService.sendAnalyticsEvent
          ).toHaveBeenCalledWith({
            event: "detected.congress_intent",
            properties: {
              query: input.query,
              score: 1,
              intentType: "org",
              upcomingCongressCount: 1,
              totalCongressCount: 2,
              projectId: input.projectId
            },
            timestamp: expect.anything(),
            userId: input.userId
          });
        });

        it("should generate dynamic filters if conference organization aggregation values are present", async () => {
          const queryUnderstandingServiceResponse =
            generateQueryUnderstandingServiceResponseForConferenceIntent(
              0.85,
              QueryIntent.IntentType.CONFERENCE_ORG
            );

          const configService = createMockInstance(ConfigService);
          const elasticService = createMockInstance(ElasticSearchService);
          const conferenceNames = [
            faker.datatype.string(),
            faker.datatype.string()
          ];
          const orgName = faker.datatype.string();

          elasticService.query.mockReturnValueOnce({
            aggregations: {
              nested: {
                filtered_matching: {
                  org_name: {
                    buckets: [
                      {
                        key: orgName,
                        range: {
                          buckets: [
                            {
                              key: "latest",
                              conf_name: {
                                buckets: [
                                  {
                                    key: conferenceNames[0]
                                  }
                                ]
                              }
                            },
                            {
                              key: "upcoming",
                              conf_name: {
                                buckets: [
                                  {
                                    key: conferenceNames[1]
                                  }
                                ]
                              }
                            }
                          ]
                        }
                      }
                    ]
                  }
                }
              }
            }
          } as any);
          const analyticsTrackerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const conferenceQueryIntentFilterService =
            new ConferenceQueryIntentFilterService(
              configService,
              elasticService,
              analyticsTrackerService
            );

          const input = generateKeywordSearchInput();
          input.query = faker.datatype.string();
          const response =
            await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
              queryUnderstandingServiceResponse,
              input,
              []
            );
          expect(
            response.suggestedFilterValues?.congresses?.organizerName
          ).toEqual([orgName]);
          expect(response.suggestedFilterValues?.congresses?.name).toEqual([
            conferenceNames[1],
            conferenceNames[0]
          ]);
          expect(
            analyticsTrackerService.sendAnalyticsEvent
          ).toHaveBeenCalledWith({
            event: "detected.congress_intent",
            properties: {
              query: input.query,
              score: 0.85,
              intentType: "org",
              upcomingCongressCount: 1,
              totalCongressCount: 2,
              projectId: input.projectId
            },
            timestamp: expect.anything(),
            userId: input.userId
          });
        });

        it("should return empty dynamic filters when aggregation buckets are empty ", async () => {
          const queryUnderstandingServiceResponse =
            generateQueryUnderstandingServiceResponseForConferenceIntent(
              0.85,
              QueryIntent.IntentType.CONFERENCE_ORG
            );

          const configService = createMockInstance(ConfigService);
          const elasticService = createMockInstance(ElasticSearchService);

          elasticService.query.mockReturnValueOnce({
            aggregations: {
              nested: {
                filtered_matching: {
                  org_name: {
                    buckets: []
                  }
                }
              }
            }
          } as any);
          const analyticsTrackerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const conferenceQueryIntentFilterService =
            new ConferenceQueryIntentFilterService(
              configService,
              elasticService,
              analyticsTrackerService
            );

          const input = generateKeywordSearchInput();
          input.query = faker.datatype.string();
          const response =
            await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
              queryUnderstandingServiceResponse,
              input,
              []
            );
          expect(
            response.suggestedFilterValues?.congresses?.organizerName
          ).toEqual([]);
          expect(response.suggestedFilterValues?.congresses?.name).toEqual([]);
          expect(
            analyticsTrackerService.sendAnalyticsEvent
          ).toHaveBeenCalledWith({
            event: "detected.congress_intent",
            properties: {
              query: input.query,
              score: 0.85,
              intentType: "org",
              upcomingCongressCount: 0,
              totalCongressCount: 0,
              projectId: input.projectId
            },
            timestamp: expect.anything(),
            userId: input.userId
          });
        });

        it("should use organization synonyms if present", async () => {
          const queryUnderstandingServiceResponse =
            generateQueryUnderstandingServiceResponseForConferenceIntent(
              0.9,
              QueryIntent.IntentType.CONFERENCE_ORG,
              "(ASCO) OR (American Society of Clinical Oncology)"
            );

          const configService = createMockInstance(ConfigService);
          const elasticService = createMockInstance(ElasticSearchService);
          const conferenceNames = [
            faker.datatype.string(),
            faker.datatype.string()
          ];
          const orgName = faker.datatype.string();

          elasticService.query.mockReturnValueOnce({
            aggregations: {
              nested: {
                filtered_matching: {
                  org_name: {
                    buckets: [
                      {
                        key: orgName,
                        range: {
                          buckets: [
                            {
                              key: "latest",
                              conf_name: {
                                buckets: [
                                  {
                                    key: conferenceNames[0]
                                  }
                                ]
                              }
                            },
                            {
                              key: "upcoming",
                              conf_name: {
                                buckets: [
                                  {
                                    key: conferenceNames[1]
                                  }
                                ]
                              }
                            }
                          ]
                        }
                      }
                    ]
                  }
                }
              }
            }
          } as any);
          const analyticsTrackerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const conferenceQueryIntentFilterService =
            new ConferenceQueryIntentFilterService(
              configService,
              elasticService,
              analyticsTrackerService
            );

          const input = generateKeywordSearchInput();
          input.query = faker.datatype.string();

          await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
            queryUnderstandingServiceResponse,
            input,
            []
          );

          expect(elasticService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              query: expect.objectContaining({
                bool: expect.objectContaining({
                  filter: [
                    {
                      nested: expect.objectContaining({
                        query: expect.objectContaining({
                          bool: expect.objectContaining({
                            filter: [
                              expect.objectContaining({
                                simple_query_string: expect.objectContaining({
                                  query:
                                    "(ASCO) | (American Society of Clinical Oncology)"
                                })
                              })
                            ]
                          })
                        })
                      })
                    }
                  ]
                })
              })
            })
          );
        });

        it("should remove any conference org filter already applied when generating suggestions", async () => {
          const queryUnderstandingServiceResponse =
            generateQueryUnderstandingServiceResponseForConferenceIntent(
              0.85,
              QueryIntent.IntentType.CONFERENCE_ORG
            );

          const configService = createMockInstance(ConfigService);
          const elasticService = createMockInstance(ElasticSearchService);

          elasticService.query.mockReturnValueOnce({
            aggregations: {
              nested: {
                filtered_matching: {
                  org_name: {
                    buckets: []
                  }
                }
              }
            }
          } as any);
          const analyticsTrackerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const conferenceQueryIntentFilterService =
            new ConferenceQueryIntentFilterService(
              configService,
              elasticService,
              analyticsTrackerService
            );

          const input = generateKeywordSearchInput();
          input.query = faker.datatype.string();
          input.suppliedFilters.congresses.organizerName.values = [
            faker.datatype.string(),
            faker.datatype.string()
          ];
          const randomFields = [
            faker.datatype.string(),
            faker.datatype.string()
          ];
          await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
            queryUnderstandingServiceResponse,
            input,
            [
              {
                function_score: {
                  query: {
                    nested: {
                      path: "congress",
                      query: {}
                    }
                  }
                }
              },
              {
                nested: {
                  path: "congress",
                  query: {}
                }
              },
              {
                function_score: {
                  query: {
                    nested: {
                      path: randomFields[0],
                      query: {}
                    }
                  }
                }
              },
              {
                nested: {
                  path: randomFields[1],
                  query: {}
                }
              }
            ]
          );

          expect(elasticService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              query: expect.objectContaining({
                bool: expect.objectContaining({
                  filter: [
                    {
                      nested: expect.objectContaining({
                        query: expect.objectContaining({
                          bool: expect.objectContaining({
                            filter: [
                              expect.objectContaining({
                                simple_query_string: expect.objectContaining({
                                  query: input.query
                                })
                              })
                            ]
                          })
                        })
                      })
                    },
                    {
                      function_score: {
                        query: {
                          nested: {
                            path: randomFields[0],
                            query: {}
                          }
                        }
                      }
                    },
                    {
                      nested: {
                        path: randomFields[1],
                        query: {}
                      }
                    }
                  ]
                })
              })
            })
          );
        });
      });

      describe("conference intent type is name", () => {
        it("should generate dynamic filters if conference name aggregation values are present and when score is 1", async () => {
          const queryUnderstandingServiceResponse =
            generateQueryUnderstandingServiceResponseForConferenceIntent(1);

          const configService = createMockInstance(ConfigService);
          const elasticService = createMockInstance(ElasticSearchService);
          const conferenceNames = [
            faker.datatype.string(),
            faker.datatype.string()
          ];
          const orgName = faker.datatype.string();

          elasticService.query.mockReturnValueOnce({
            aggregations: {
              nested: {
                filtered_matching: {
                  org_name: {
                    buckets: [
                      {
                        key: orgName,
                        range: {
                          buckets: [
                            {
                              key: "latest",
                              conf_name: {
                                buckets: [
                                  {
                                    key: conferenceNames[1]
                                  }
                                ]
                              }
                            },
                            {
                              key: "upcoming",
                              conf_name: {
                                buckets: [
                                  {
                                    key: conferenceNames[0]
                                  }
                                ]
                              }
                            }
                          ]
                        }
                      }
                    ]
                  }
                }
              }
            }
          } as any);
          const analyticsTrackerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const conferenceQueryIntentFilterService =
            new ConferenceQueryIntentFilterService(
              configService,
              elasticService,
              analyticsTrackerService
            );

          const input = generateKeywordSearchInput();
          input.query = faker.datatype.string();
          const response =
            await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
              queryUnderstandingServiceResponse,
              input,
              []
            );
          expect(
            response.suggestedFilterValues?.congresses?.organizerName
          ).toEqual([orgName]);
          expect(response.suggestedFilterValues?.congresses?.name).toEqual([
            conferenceNames[0],
            conferenceNames[1]
          ]);
          expect(
            analyticsTrackerService.sendAnalyticsEvent
          ).toHaveBeenCalledWith({
            event: "detected.congress_intent",
            properties: {
              query: input.query,
              score: 1,
              intentType: "name",
              upcomingCongressCount: 1,
              totalCongressCount: 2,
              projectId: input.projectId
            },
            timestamp: expect.anything(),
            userId: input.userId
          });
        });

        it("should generate dynamic filters if conference name aggregation values are present", async () => {
          const queryUnderstandingServiceResponse =
            generateQueryUnderstandingServiceResponseForConferenceIntent(0.85);

          const configService = createMockInstance(ConfigService);
          const elasticService = createMockInstance(ElasticSearchService);
          const conferenceNames = [
            faker.datatype.string(),
            faker.datatype.string(),
            faker.datatype.string(),
            faker.datatype.string()
          ];
          const orgName = faker.datatype.string();

          elasticService.query.mockReturnValueOnce({
            aggregations: {
              nested: {
                filtered_matching: {
                  org_name: {
                    buckets: [
                      {
                        key: orgName,
                        range: {
                          buckets: [
                            {
                              key: "latest",
                              conf_name: {
                                buckets: [
                                  {
                                    key: conferenceNames[2]
                                  },
                                  {
                                    key: conferenceNames[3]
                                  }
                                ]
                              }
                            },
                            {
                              key: "upcoming",
                              conf_name: {
                                buckets: [
                                  {
                                    key: conferenceNames[0]
                                  },
                                  {
                                    key: conferenceNames[1]
                                  }
                                ]
                              }
                            }
                          ]
                        }
                      }
                    ]
                  }
                }
              }
            }
          } as any);
          const analyticsTrackerService = createMockInstance(
            SearchAnalyticsTracerService
          );

          const conferenceQueryIntentFilterService =
            new ConferenceQueryIntentFilterService(
              configService,
              elasticService,
              analyticsTrackerService
            );

          const input = generateKeywordSearchInput();
          input.query = faker.datatype.string();
          const response =
            await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
              queryUnderstandingServiceResponse,
              input,
              []
            );
          expect(
            response.suggestedFilterValues?.congresses?.organizerName
          ).toEqual([orgName]);
          expect(response.suggestedFilterValues?.congresses?.name).toEqual([
            conferenceNames[0],
            conferenceNames[1]
          ]);
          expect(
            analyticsTrackerService.sendAnalyticsEvent
          ).toHaveBeenCalledWith({
            event: "detected.congress_intent",
            properties: {
              query: input.query,
              score: 0.85,
              intentType: "name",
              upcomingCongressCount: 2,
              totalCongressCount: 2,
              projectId: input.projectId
            },
            timestamp: expect.anything(),
            userId: input.userId
          });
        });

        it("should return empty filter and dynamic filters when aggregation buckets are empty", async () => {
          const queryUnderstandingServiceResponse =
            generateQueryUnderstandingServiceResponseForConferenceIntent(0.85);

          const configService = createMockInstance(ConfigService);
          const elasticService = createMockInstance(ElasticSearchService);
          const orgName = faker.datatype.string();
          elasticService.query.mockReturnValueOnce({
            aggregations: {
              nested: {
                filtered_matching: {
                  org_name: {
                    buckets: [
                      {
                        key: orgName,
                        range: {
                          buckets: [
                            {
                              key: "latest",
                              conf_name: {
                                buckets: []
                              }
                            },
                            {
                              key: "upcoming",
                              conf_name: {
                                buckets: []
                              }
                            }
                          ]
                        }
                      }
                    ]
                  }
                }
              }
            }
          } as any);
          const analyticsTrackerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const conferenceQueryIntentFilterService =
            new ConferenceQueryIntentFilterService(
              configService,
              elasticService,
              analyticsTrackerService
            );

          const input = generateKeywordSearchInput();
          input.query = faker.datatype.string();
          const response =
            await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
              queryUnderstandingServiceResponse,
              input,
              []
            );
          expect(
            response.suggestedFilterValues?.congresses?.organizerName
          ).toEqual([orgName]);
          expect(response.suggestedFilterValues?.congresses?.name).toEqual([]);
          expect(
            analyticsTrackerService.sendAnalyticsEvent
          ).toHaveBeenCalledWith({
            event: "detected.congress_intent",
            properties: {
              query: input.query,
              score: 0.85,
              intentType: "name",
              upcomingCongressCount: 0,
              totalCongressCount: 0,
              projectId: input.projectId
            },
            timestamp: expect.anything(),
            userId: input.userId
          });
        });

        it("should use conference name synonyms if present", async () => {
          const queryUnderstandingServiceResponse =
            generateQueryUnderstandingServiceResponseForConferenceIntent(
              0.9,
              QueryIntent.IntentType.CONFERENCE_NAME,
              "(asco annual meeting 2022) OR (asco OR ((ASCO) OR (American Society of Clinical Oncology))) AND ((annual) AND (meet) AND (2022))"
            );

          const configService = createMockInstance(ConfigService);
          const elasticService = createMockInstance(ElasticSearchService);

          elasticService.query.mockReturnValueOnce({
            aggregations: {
              nested: {
                filtered_matching: {
                  org_name: {
                    buckets: []
                  }
                }
              }
            }
          } as any);
          const analyticsTrackerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const conferenceQueryIntentFilterService =
            new ConferenceQueryIntentFilterService(
              configService,
              elasticService,
              analyticsTrackerService
            );

          const input = generateKeywordSearchInput();
          input.query = faker.datatype.string();
          await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
            queryUnderstandingServiceResponse,
            input,
            []
          );

          expect(elasticService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              query: expect.objectContaining({
                bool: expect.objectContaining({
                  filter: [
                    {
                      nested: expect.objectContaining({
                        query: expect.objectContaining({
                          bool: expect.objectContaining({
                            filter: [
                              expect.objectContaining({
                                simple_query_string: expect.objectContaining({
                                  query:
                                    "(asco annual meeting 2022) | (asco | ((ASCO) | (American Society of Clinical Oncology)))  ((annual)  (meet)  (2022))"
                                })
                              })
                            ]
                          })
                        })
                      })
                    }
                  ]
                })
              })
            })
          );
        });

        it("should remove any conference name filter already applied when generating suggestions", async () => {
          const queryUnderstandingServiceResponse =
            generateQueryUnderstandingServiceResponseForConferenceIntent(
              0.85,
              QueryIntent.IntentType.CONFERENCE_ORG
            );

          const configService = createMockInstance(ConfigService);
          const elasticService = createMockInstance(ElasticSearchService);

          elasticService.query.mockReturnValueOnce({
            aggregations: {
              nested: {
                filtered_matching: {
                  org_name: {
                    buckets: []
                  }
                }
              }
            }
          } as any);
          const analyticsTrackerService = createMockInstance(
            SearchAnalyticsTracerService
          );
          const conferenceQueryIntentFilterService =
            new ConferenceQueryIntentFilterService(
              configService,
              elasticService,
              analyticsTrackerService
            );

          const input = generateKeywordSearchInput();
          input.query = faker.datatype.string();
          input.suppliedFilters.congresses.name.values = [
            faker.datatype.string(),
            faker.datatype.string()
          ];
          const randomFields = [
            faker.datatype.string(),
            faker.datatype.string()
          ];
          await conferenceQueryIntentFilterService.getQueryIntentFilterResponse(
            queryUnderstandingServiceResponse,
            input,
            [
              {
                function_score: {
                  query: {
                    nested: {
                      path: "congress",
                      query: {}
                    }
                  }
                }
              },
              {
                nested: {
                  path: "congress",
                  query: {}
                }
              },
              {
                function_score: {
                  query: {
                    nested: {
                      path: randomFields[0],
                      query: {}
                    }
                  }
                }
              },
              {
                nested: {
                  path: randomFields[1],
                  query: {}
                }
              }
            ]
          );

          expect(elasticService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              query: expect.objectContaining({
                bool: expect.objectContaining({
                  filter: [
                    {
                      nested: expect.objectContaining({
                        query: expect.objectContaining({
                          bool: expect.objectContaining({
                            filter: [
                              expect.objectContaining({
                                simple_query_string: expect.objectContaining({
                                  query: input.query
                                })
                              })
                            ]
                          })
                        })
                      })
                    },
                    {
                      function_score: {
                        query: {
                          nested: {
                            path: randomFields[0],
                            query: {}
                          }
                        }
                      }
                    },
                    {
                      nested: {
                        path: randomFields[1],
                        query: {}
                      }
                    }
                  ]
                })
              })
            })
          );
        });
      });
    });
  });
});

describe("Conference intent confidence", () => {
  it("should return true when congress intent score above the threshold", () => {
    const congressIntentScore = faker.datatype.float({
      min: CONGRESS_INTENT_SCORE_THRESHOLD,
      max: 1.0
    });

    expect(
      hasGoodConfidenceForConferenceIntent(
        generateQueryUnderstandingServiceResponseForConferenceIntent(
          congressIntentScore
        )
      )
    ).toBe(true);
  });

  it("should return false when congress intent score below the threshold", () => {
    const precision = 0.01;
    const congressIntentScore = faker.datatype.float({
      min: 0,
      max: CONGRESS_INTENT_SCORE_THRESHOLD - precision,
      precision
    });

    expect(
      hasGoodConfidenceForConferenceIntent(
        generateQueryUnderstandingServiceResponseForConferenceIntent(
          congressIntentScore
        )
      )
    ).toBe(false);
  });

  it("should return false when query does not have conference intent", () => {
    const quResponse = new QueryUnderstandingServiceResponse();
    expect(hasGoodConfidenceForConferenceIntent(quResponse)).toBe(false);
  });
});
