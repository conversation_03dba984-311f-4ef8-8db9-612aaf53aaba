import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { faker } from "@faker-js/faker";
import { ITree } from "../lib/ParserTypes/types";
import {
  LanguageDetector,
  Location
} from "./KeywordSearchResourceServiceRewrite";
import { ENGLISH, CHINESE, JAPANESE } from "./LanguageDetectService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { NestedPath } from "../util/QueryParsingUtils";

export function generateMockLocation(): Location {
  return {
    city_eng: faker.address.city(),
    state_eng: faker.address.state(),
    zipCode5_eng: faker.address.zipCode(),
    state_cmn: faker.address.state(),
    state_jpn: faker.address.state(),
    languageCode: [ENGLISH, CHINESE, JAPANESE][faker.datatype.number(2)],
    token: faker.datatype.string(),
    zipCode5_cmn: faker.address.zipCode(),
    zipCode5_jpn: faker.address.zipCode(),
    country_eng: faker.address.country(),
    zipCodeFull: faker.address.zipCode(),
    country_cmn: faker.address.country(),
    stateCode: faker.address.stateAbbr(),
    city_cmn: faker.address.city(),
    country_jpn: faker.address.country(),
    city_jpn: faker.address.city()
  };
}

export function generateMockElasticsearchTermQuery(): QueryDslQueryContainer {
  return {
    term: {
      [faker.random.word()]: faker.datatype.string()
    }
  };
}

const parsedQueryTreeToElasticsearchQueriesService =
  new ParsedQueryTreeToElasticsearchQueriesService();

export function mockParseTreeToElasticsearchQueries(
  queriesByPath: Partial<Record<NestedPath, QueryDslQueryContainer>>
) {
  return (
    tree: Readonly<ITree> | Readonly<Array<ITree>> | string,
    fields: Readonly<Array<string>>,
    languageDetector?: LanguageDetector | typeof ENGLISH
  ): ReturnType<typeof parsedQueryTreeToElasticsearchQueriesService.parse> => {
    const path = fields[0].split(".", 2)[0] as NestedPath;

    if (queriesByPath[path]) {
      return queriesByPath[path]!;
    }

    throw new Error(`Unsupported path: ${path}`);
  };
}

export function mockParseTreeToElasticsearchQueriesForString() {
  return (
    tree: Readonly<ITree> | Readonly<Array<ITree>> | string,
    fields: Readonly<Array<string>>,
    languageDetector?: LanguageDetector | typeof ENGLISH
  ): ReturnType<typeof parsedQueryTreeToElasticsearchQueriesService.parse> => {
    if (tree) {
      if (typeof tree === "string")
        return {
          simple_query_string: {
            query: tree,
            default_operator: "AND",
            fields: [...fields],
            flags: "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP"
          }
        };
    }
    throw new Error("Tree is empty");
  };
}
