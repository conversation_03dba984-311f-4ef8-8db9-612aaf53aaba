import {
  ErrorResponseBase,
  SearchHit,
  SearchInnerHitsResult,
  MsearchMultiSearchItem,
  SearchResponse
} from "@elastic/elasticsearch/lib/api/types";
import { faker } from "@faker-js/faker";
import { CitationProfileQueryBuilder } from "../lib/CitationProfile/CitationProfileBuilder";
import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import { HCPNotFoundError } from "./errors/HCPNotFoundError";
import {
  CitationDocument,
  InfluencesResourceService,
  ScopeDocument
} from "./InfluencesResourceService";
import {
  CHINESE,
  ENGLISH,
  JAPANESE,
  Language,
  LanguageDetectService
} from "./LanguageDetectService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { QueryParserService } from "./QueryParserService";

type CitationHitOverrides = {
  language?: Language;
  overrides?: Partial<CitationDocument>;
  innerHitsOverrides?: Array<any>;
};

function generateMockCitationHit(
  hitOverrides: CitationHitOverrides = {
    language: "eng",
    overrides: {}
  }
): SearchHit<CitationDocument> {
  const citationDocument: CitationDocument = {
    institutions: [faker.company.name(), faker.company.name()],
    publicationCount: faker.datatype.number(),
    citationTotal: faker.datatype.number(),
    trialCount: faker.datatype.number(),
    congressCount: faker.datatype.number(),
    paymentTotal: faker.datatype.number(),
    name_eng: faker.name.fullName(),
    name_jpn: faker.name.fullName(),
    name_cmn: faker.name.fullName(),
    id: faker.datatype.uuid()
  };

  const innerHits = [
    generateMockPublication(hitOverrides.language!),
    generateMockPublication(hitOverrides.language!)
  ];

  return {
    _index: faker.datatype.string(),
    _id: faker.datatype.uuid(),
    _score: faker.datatype.number(),
    _source: {
      ...citationDocument,
      ...hitOverrides.overrides
    },
    inner_hits: {
      publications: {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: "eq"
          },
          hits: hitOverrides.innerHitsOverrides || innerHits
        }
      }
    } as Record<string, SearchInnerHitsResult>
  };
}

function generateMockPublication(language: Language = "eng", overrides = {}) {
  const fields = {
    "publications.citationCount": [faker.datatype.number()],
    [`publications.journalName_${language}`]: [faker.datatype.string()],
    "publications.journalName_eng": [faker.datatype.string()],
    "publications.journalName_jpn": [faker.datatype.string()],
    "publications.journalName_cmn": [faker.datatype.string()],
    "publications.microBloggingCount": [faker.datatype.number()],
    [`publications.title_${language}.keyword`]: [faker.datatype.string()],
    "publications.title_eng.keyword": [faker.datatype.string()],
    "publications.title_jpn.keyword": [faker.datatype.string()],
    "publications.title_cmn.keyword": [faker.datatype.string()],
    "publications.datePublished": [faker.datatype.datetime()],
    "publications.id": [faker.datatype.uuid()]
  };

  return {
    _index: faker.datatype.string(),
    _id: faker.datatype.uuid(),
    fields: {
      ...fields,
      ...overrides
    }
  };
}

function generateMockElasticsearchErrorResponse(): ErrorResponseBase {
  return {
    error: {
      root_cause: [
        {
          type: faker.datatype.string(),
          reason: faker.random.words()
        }
      ],
      type: faker.datatype.string(),
      reason: faker.random.words()
    },
    status: 400
  };
}

describe("InfluencesResourceService", () => {
  describe("citationProfile", () => {
    describe("negative tests", () => {
      it("should re-throw any error thrown by elasticService.query", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];
        const langCode = "eng";

        const error = new Error(
          "elasticSearchService.query threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(error);

        await expect(
          influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            langCode
          )
        ).rejects.toThrowError(error);
      });

      it("should throw an error if the HCP cannot be found by id", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];
        const langCode = "eng";

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [] as Array<SearchHit<ScopeDocument>>
          }
        } as SearchResponse);

        await expect(
          influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            langCode
          )
        ).rejects.toThrowError(new HCPNotFoundError(personId));
      });

      it("should re-throw any error thrown by elasticService.msearch", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];
        const langCode = "eng";

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        const error = new Error(
          "elasticSearchService.msearch threw an error for some reason"
        );
        elasticSearchService.msearch.mockRejectedValue(error);

        await expect(
          influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            langCode
          )
        ).rejects.toThrowError(error);
      });

      it("should re-throw an citedResponse error returned by elasticService.msearch", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];
        const langCode = "eng";

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        const errorResponse = generateMockElasticsearchErrorResponse();

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            errorResponse,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        await expect(
          influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            langCode
          )
        ).rejects.toThrow(new Error(errorResponse.error.reason));
      });

      it("should re-throw an citedByResponse error returned by elasticService.msearch", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];
        const langCode = "eng";

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        const errorResponse = generateMockElasticsearchErrorResponse();

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem,
            errorResponse
          ]
        });

        await expect(
          influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            langCode
          )
        ).rejects.toThrow(new Error(errorResponse.error.reason));
      });
    });

    it("full thing", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const citationProfileQueryBuilder = createMockInstance(
        CitationProfileQueryBuilder
      );
      citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
        _source_includes: [faker.database.column(), faker.database.column()],
        size: faker.datatype.number(),
        query: {
          term: {
            [faker.database.column()]: faker.datatype.string()
          }
        }
      });

      const languageDetectService = createMockInstance(LanguageDetectService);
      const queryParserService = createMockInstance(QueryParserService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const parsedQueryTree = faker.datatype.string();
      queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
        {
          parsedQueryTree,
          synonyms: []
        }
      );

      const influencesResourceService = new InfluencesResourceService(
        configService,
        elasticSearchService,
        citationProfileQueryBuilder,
        languageDetectService,
        queryParserService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const projectId = faker.datatype.string();
      const dateRange = {
        min: faker.datatype.datetime().getTime(),
        max: faker.datatype.datetime().getTime()
      };
      const terms = [faker.datatype.string(), faker.datatype.string()];
      const langCode = "eng";

      const englishName = faker.name.fullName();
      const japaneseName = faker.name.fullName();
      const chineseName = faker.name.fullName();

      const institutions = [faker.company.name(), faker.company.name()];

      const citedHit1 = generateMockCitationHit();
      citedHit1._source!.institutions = [...institutions];

      const citedHit2 = generateMockCitationHit();
      citedHit2._source!.institutions = [...institutions];

      const citedByHit1 = generateMockCitationHit();
      citedByHit1._source!.institutions = [...institutions];

      const citedByHit2 = generateMockCitationHit();
      citedByHit2._source!.institutions = [...institutions];

      const citedByPapersQuery1 = {
        _source_includes: [faker.database.column(), faker.database.column()],
        size: faker.datatype.number(),
        query: {
          term: {
            [faker.database.column()]: faker.datatype.string()
          }
        }
      };

      const citedByPapersQuery2 = {
        _source_includes: [faker.database.column(), faker.database.column()],
        size: faker.datatype.number(),
        query: {
          term: {
            [faker.database.column()]: faker.datatype.string()
          }
        }
      };

      citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValueOnce(
        citedByPapersQuery1
      );
      citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValueOnce(
        citedByPapersQuery2
      );

      elasticSearchService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _index: faker.datatype.string(),
              _id: faker.datatype.uuid(),
              _source: {
                name_eng: englishName,
                name_jpn: japaneseName,
                name_cmn: chineseName,
                institutions
              }
            }
          ]
        }
      } as SearchResponse);

      elasticSearchService.msearch.mockResolvedValueOnce({
        took: faker.datatype.number(),
        responses: [
          {
            hits: {
              hits: [citedHit1, citedHit2]
            }
          } as MsearchMultiSearchItem,
          {
            hits: {
              hits: [citedByHit1, citedByHit2]
            }
          } as MsearchMultiSearchItem
        ]
      });

      const queryContainer = {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      };

      parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
        queryContainer
      );

      const response = await influencesResourceService.citationProfile(
        personId,
        projectId,
        dateRange,
        terms,
        langCode
      );

      expect(response).toEqual({
        cited: [
          {
            citationCount: citedHit1._score,
            fullName: citedHit1._source!.name_eng,
            id: citedHit1._source!.id,
            matchedPubs: 0,
            cardMetrics: [
              {
                name: "Publications",
                count: citedHit1._source!.publicationCount,
                path: "publications"
              },
              {
                name: "Citations",
                count: citedHit1._source!.citationTotal,
                path: "publications"
              },
              {
                name: "Clinical Trials",
                count: citedHit1._source!.trialCount,
                path: "clinical-trials"
              },
              {
                name: "Congresses",
                count: citedHit1._source!.congressCount,
                path: "congresses"
              },
              {
                name: "Payments",
                count: citedHit1._source!.paymentTotal,
                path: "payments"
              }
            ],
            publications: [
              {
                citationCount:
                  citedHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedHit1.inner_hits!.publications.hits.hits[0].fields![
                  "publications.id"
                ][0]
              },
              {
                citationCount:
                  citedHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedHit1.inner_hits!.publications.hits.hits[1].fields![
                  "publications.id"
                ][0]
              }
            ],
            sharedMetrics: [
              {
                count: 2,
                name: "Shared Institutions"
              }
            ]
          },
          {
            citationCount: citedHit2._score,
            fullName: citedHit2._source!.name_eng,
            id: citedHit2._source!.id,
            matchedPubs: 0,
            cardMetrics: [
              {
                name: "Publications",
                count: citedHit2._source!.publicationCount,
                path: "publications"
              },
              {
                name: "Citations",
                count: citedHit2._source!.citationTotal,
                path: "publications"
              },
              {
                name: "Clinical Trials",
                count: citedHit2._source!.trialCount,
                path: "clinical-trials"
              },
              {
                name: "Congresses",
                count: citedHit2._source!.congressCount,
                path: "congresses"
              },
              {
                name: "Payments",
                count: citedHit2._source!.paymentTotal,
                path: "payments"
              }
            ],
            publications: [
              {
                citationCount:
                  citedHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedHit2.inner_hits!.publications.hits.hits[0].fields![
                  "publications.id"
                ][0]
              },
              {
                citationCount:
                  citedHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedHit2.inner_hits!.publications.hits.hits[1].fields![
                  "publications.id"
                ][0]
              }
            ],
            sharedMetrics: [
              {
                count: 2,
                name: "Shared Institutions"
              }
            ]
          }
        ],
        citedBy: [
          {
            citationCount: citedByHit1._score,
            fullName: citedByHit1._source!.name_eng,
            id: citedByHit1._source!.id,
            matchedPubs: 0,
            cardMetrics: [
              {
                name: "Publications",
                count: citedByHit1._source!.publicationCount,
                path: "publications"
              },
              {
                name: "Citations",
                count: citedByHit1._source!.citationTotal,
                path: "publications"
              },
              {
                name: "Clinical Trials",
                count: citedByHit1._source!.trialCount,
                path: "clinical-trials"
              },
              {
                name: "Congresses",
                count: citedByHit1._source!.congressCount,
                path: "congresses"
              },
              {
                name: "Payments",
                count: citedByHit1._source!.paymentTotal,
                path: "payments"
              }
            ],
            publications: [
              {
                citationCount:
                  citedByHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedByHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedByHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedByHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedByHit1.inner_hits!.publications.hits.hits[0].fields![
                  "publications.id"
                ][0]
              },
              {
                citationCount:
                  citedByHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedByHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedByHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedByHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedByHit1.inner_hits!.publications.hits.hits[1].fields![
                  "publications.id"
                ][0]
              }
            ],
            sharedMetrics: [
              {
                count: 2,
                name: "Shared Institutions"
              }
            ]
          },
          {
            citationCount: citedByHit2._score,
            fullName: citedByHit2._source!.name_eng,
            id: citedByHit2._source!.id,
            matchedPubs: 0,
            cardMetrics: [
              {
                name: "Publications",
                count: citedByHit2._source!.publicationCount,
                path: "publications"
              },
              {
                name: "Citations",
                count: citedByHit2._source!.citationTotal,
                path: "publications"
              },
              {
                name: "Clinical Trials",
                count: citedByHit2._source!.trialCount,
                path: "clinical-trials"
              },
              {
                name: "Congresses",
                count: citedByHit2._source!.congressCount,
                path: "congresses"
              },
              {
                name: "Payments",
                count: citedByHit2._source!.paymentTotal,
                path: "payments"
              }
            ],
            publications: [
              {
                citationCount:
                  citedByHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedByHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedByHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedByHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedByHit2.inner_hits!.publications.hits.hits[0].fields![
                  "publications.id"
                ][0]
              },
              {
                citationCount:
                  citedByHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedByHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedByHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedByHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedByHit2.inner_hits!.publications.hits.hits[1].fields![
                  "publications.id"
                ][0]
              }
            ],
            sharedMetrics: [
              {
                count: 2,
                name: "Shared Institutions"
              }
            ]
          }
        ],
        fullName: englishName,
        fullName_jpn: japaneseName,
        fullName_cmn: chineseName
      });

      expect(
        citationProfileQueryBuilder.buildCitedByPapersQuery
      ).toHaveBeenNthCalledWith(
        1,
        personId,
        dateRange,
        true,
        projectId,
        queryContainer
      );

      expect(
        citationProfileQueryBuilder.buildCitedByPapersQuery
      ).toHaveBeenNthCalledWith(
        2,
        personId,
        dateRange,
        false,
        projectId,
        queryContainer
      );

      expect(elasticSearchService.query).toHaveBeenNthCalledWith(1, {
        _source_includes: ["name_eng", "name_jpn", "name_cmn", "institutions"],
        index: configService.elasticPeopleIndex,
        query: {
          term: {
            id: personId
          }
        }
      });

      expect(elasticSearchService.msearch).toHaveBeenCalledWith({
        index: configService.elasticPeopleIndex,
        searches: [
          {},
          {
            size: citedByPapersQuery1.size,
            _source: citedByPapersQuery1._source_includes,
            query: citedByPapersQuery1.query
          },
          {},
          {
            size: citedByPapersQuery2.size,
            _source: citedByPapersQuery2._source_includes,
            query: citedByPapersQuery2.query
          }
        ]
      });
    });

    it("should pass queryContainer when returned", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const citationProfileQueryBuilder = createMockInstance(
        CitationProfileQueryBuilder
      );

      const languageDetectService = createMockInstance(LanguageDetectService);
      const queryParserService = createMockInstance(QueryParserService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const influencesResourceService = new InfluencesResourceService(
        configService,
        elasticSearchService,
        citationProfileQueryBuilder,
        languageDetectService,
        queryParserService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const projectId = faker.datatype.string();
      const dateRange = {
        min: faker.datatype.datetime().getTime(),
        max: faker.datatype.datetime().getTime()
      };
      const terms = [faker.datatype.string(), faker.datatype.string()];
      const langCode = "eng";

      const englishName = faker.name.fullName();
      const japaneseName = faker.name.fullName();
      const chineseName = faker.name.fullName();

      const institutions = [faker.company.name(), faker.company.name()];

      const citedHit1 = generateMockCitationHit();
      citedHit1._source!.institutions = [...institutions];

      const citedHit2 = generateMockCitationHit();
      citedHit2._source!.institutions = [...institutions];

      const citedByHit1 = generateMockCitationHit();
      citedByHit1._source!.institutions = [...institutions];

      const citedByHit2 = generateMockCitationHit();
      citedByHit2._source!.institutions = [...institutions];

      const citedByPapersQuery1 = {
        _source_includes: [faker.database.column(), faker.database.column()],
        size: faker.datatype.number(),
        query: {
          term: {
            [faker.database.column()]: faker.datatype.string()
          }
        }
      };

      const citedByPapersQuery2 = {
        _source_includes: [faker.database.column(), faker.database.column()],
        size: faker.datatype.number(),
        query: {
          term: {
            [faker.database.column()]: faker.datatype.string()
          }
        }
      };

      citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValueOnce(
        citedByPapersQuery1
      );
      citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValueOnce(
        citedByPapersQuery2
      );

      elasticSearchService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _index: faker.datatype.string(),
              _id: faker.datatype.uuid(),
              _source: {
                name_eng: englishName,
                name_jpn: japaneseName,
                name_cmn: chineseName,
                institutions
              }
            }
          ]
        }
      } as SearchResponse);

      elasticSearchService.msearch.mockResolvedValueOnce({
        took: faker.datatype.number(),
        responses: [
          {
            hits: {
              hits: [citedHit1, citedHit2]
            }
          } as MsearchMultiSearchItem,
          {
            hits: {
              hits: [citedByHit1, citedByHit2]
            }
          } as MsearchMultiSearchItem
        ]
      });

      const languageDetector = () => {
        return faker.helpers.arrayElement([
          ENGLISH,
          CHINESE,
          JAPANESE
        ]) as Language;
      };
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );

      const parsedQueryTree = faker.datatype.string();
      queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
        {
          parsedQueryTree,
          synonyms: []
        }
      );

      const queryContainer = {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      };

      parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
        queryContainer
      );

      const response = await influencesResourceService.citationProfile(
        personId,
        projectId,
        dateRange,
        terms,
        langCode
      );

      expect(response).toEqual({
        cited: [
          {
            citationCount: citedHit1._score,
            fullName: citedHit1._source!.name_eng,
            id: citedHit1._source!.id,
            matchedPubs: 0,
            cardMetrics: [
              {
                name: "Publications",
                count: citedHit1._source!.publicationCount,
                path: "publications"
              },
              {
                name: "Citations",
                count: citedHit1._source!.citationTotal,
                path: "publications"
              },
              {
                name: "Clinical Trials",
                count: citedHit1._source!.trialCount,
                path: "clinical-trials"
              },
              {
                name: "Congresses",
                count: citedHit1._source!.congressCount,
                path: "congresses"
              },
              {
                name: "Payments",
                count: citedHit1._source!.paymentTotal,
                path: "payments"
              }
            ],
            publications: [
              {
                citationCount:
                  citedHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedHit1.inner_hits!.publications.hits.hits[0].fields![
                  "publications.id"
                ][0]
              },
              {
                citationCount:
                  citedHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedHit1.inner_hits!.publications.hits.hits[1].fields![
                  "publications.id"
                ][0]
              }
            ],
            sharedMetrics: [
              {
                count: 2,
                name: "Shared Institutions"
              }
            ]
          },
          {
            citationCount: citedHit2._score,
            fullName: citedHit2._source!.name_eng,
            id: citedHit2._source!.id,
            matchedPubs: 0,
            cardMetrics: [
              {
                name: "Publications",
                count: citedHit2._source!.publicationCount,
                path: "publications"
              },
              {
                name: "Citations",
                count: citedHit2._source!.citationTotal,
                path: "publications"
              },
              {
                name: "Clinical Trials",
                count: citedHit2._source!.trialCount,
                path: "clinical-trials"
              },
              {
                name: "Congresses",
                count: citedHit2._source!.congressCount,
                path: "congresses"
              },
              {
                name: "Payments",
                count: citedHit2._source!.paymentTotal,
                path: "payments"
              }
            ],
            publications: [
              {
                citationCount:
                  citedHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedHit2.inner_hits!.publications.hits.hits[0].fields![
                  "publications.id"
                ][0]
              },
              {
                citationCount:
                  citedHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedHit2.inner_hits!.publications.hits.hits[1].fields![
                  "publications.id"
                ][0]
              }
            ],
            sharedMetrics: [
              {
                count: 2,
                name: "Shared Institutions"
              }
            ]
          }
        ],
        citedBy: [
          {
            citationCount: citedByHit1._score,
            fullName: citedByHit1._source!.name_eng,
            id: citedByHit1._source!.id,
            matchedPubs: 0,
            cardMetrics: [
              {
                name: "Publications",
                count: citedByHit1._source!.publicationCount,
                path: "publications"
              },
              {
                name: "Citations",
                count: citedByHit1._source!.citationTotal,
                path: "publications"
              },
              {
                name: "Clinical Trials",
                count: citedByHit1._source!.trialCount,
                path: "clinical-trials"
              },
              {
                name: "Congresses",
                count: citedByHit1._source!.congressCount,
                path: "congresses"
              },
              {
                name: "Payments",
                count: citedByHit1._source!.paymentTotal,
                path: "payments"
              }
            ],
            publications: [
              {
                citationCount:
                  citedByHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedByHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedByHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedByHit1.inner_hits!.publications.hits.hits[0].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedByHit1.inner_hits!.publications.hits.hits[0].fields![
                  "publications.id"
                ][0]
              },
              {
                citationCount:
                  citedByHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedByHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedByHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedByHit1.inner_hits!.publications.hits.hits[1].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedByHit1.inner_hits!.publications.hits.hits[1].fields![
                  "publications.id"
                ][0]
              }
            ],
            sharedMetrics: [
              {
                count: 2,
                name: "Shared Institutions"
              }
            ]
          },
          {
            citationCount: citedByHit2._score,
            fullName: citedByHit2._source!.name_eng,
            id: citedByHit2._source!.id,
            matchedPubs: 0,
            cardMetrics: [
              {
                name: "Publications",
                count: citedByHit2._source!.publicationCount,
                path: "publications"
              },
              {
                name: "Citations",
                count: citedByHit2._source!.citationTotal,
                path: "publications"
              },
              {
                name: "Clinical Trials",
                count: citedByHit2._source!.trialCount,
                path: "clinical-trials"
              },
              {
                name: "Congresses",
                count: citedByHit2._source!.congressCount,
                path: "congresses"
              },
              {
                name: "Payments",
                count: citedByHit2._source!.paymentTotal,
                path: "payments"
              }
            ],
            publications: [
              {
                citationCount:
                  citedByHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedByHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedByHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedByHit2.inner_hits!.publications.hits.hits[0].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedByHit2.inner_hits!.publications.hits.hits[0].fields![
                  "publications.id"
                ][0]
              },
              {
                citationCount:
                  citedByHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.citationCount"
                  ][0],
                journal:
                  citedByHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.journalName_eng"
                  ][0],
                socialMediaCount:
                  citedByHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.microBloggingCount"
                  ][0],
                title:
                  citedByHit2.inner_hits!.publications.hits.hits[1].fields![
                    "publications.title_eng.keyword"
                  ][0],
                date: expect.any(String),
                id: citedByHit2.inner_hits!.publications.hits.hits[1].fields![
                  "publications.id"
                ][0]
              }
            ],
            sharedMetrics: [
              {
                count: 2,
                name: "Shared Institutions"
              }
            ]
          }
        ],
        fullName: englishName,
        fullName_jpn: japaneseName,
        fullName_cmn: chineseName
      });

      expect(
        parsedQueryTreeToElasticsearchQueriesService.parse
      ).toHaveBeenCalledWith(
        parsedQueryTree,
        [
          "publications.publicationAbstract",
          "publications.keywords",
          "publications.title"
        ],
        languageDetector
      );

      expect(languageDetectService.getLanguageDetector).toHaveBeenCalledWith(
        langCode
      );
      expect(
        languageDetectService.determineSupportedLanguageWithUserPreference
      ).not.toHaveBeenCalled();

      expect(
        citationProfileQueryBuilder.buildCitedByPapersQuery
      ).toHaveBeenNthCalledWith(
        1,
        personId,
        dateRange,
        true,
        projectId,
        queryContainer
      );

      expect(
        citationProfileQueryBuilder.buildCitedByPapersQuery
      ).toHaveBeenNthCalledWith(
        2,
        personId,
        dateRange,
        false,
        projectId,
        queryContainer
      );

      expect(elasticSearchService.query).toHaveBeenNthCalledWith(1, {
        _source_includes: ["name_eng", "name_jpn", "name_cmn", "institutions"],
        index: configService.elasticPeopleIndex,
        query: {
          term: {
            id: personId
          }
        }
      });

      expect(elasticSearchService.msearch).toHaveBeenCalledWith({
        index: configService.elasticPeopleIndex,
        searches: [
          {},
          {
            size: citedByPapersQuery1.size,
            _source: citedByPapersQuery1._source_includes,
            query: citedByPapersQuery1.query
          },
          {},
          {
            size: citedByPapersQuery2.size,
            _source: citedByPapersQuery2._source_includes,
            query: citedByPapersQuery2.query
          }
        ]
      });
    });

    it("should return 0 sharedMetrics.count when no institutions intersect between scope and publications", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const citationProfileQueryBuilder = createMockInstance(
        CitationProfileQueryBuilder
      );
      citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
        _source_includes: [faker.database.column(), faker.database.column()],
        size: faker.datatype.number(),
        query: {
          term: {
            [faker.database.column()]: faker.datatype.string()
          }
        }
      });

      const languageDetectService = createMockInstance(LanguageDetectService);
      const queryParserService = createMockInstance(QueryParserService);
      const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
        ParsedQueryTreeToElasticsearchQueriesService
      );

      const parsedQueryTree = faker.datatype.string();
      queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
        {
          parsedQueryTree,
          synonyms: []
        }
      );

      const queryContainer = {
        term: {
          [faker.random.word()]: faker.datatype.string()
        }
      };
      parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
        queryContainer
      );

      const influencesResourceService = new InfluencesResourceService(
        configService,
        elasticSearchService,
        citationProfileQueryBuilder,
        languageDetectService,
        queryParserService,
        parsedQueryTreeToElasticsearchQueriesService
      );

      const personId = faker.datatype.uuid();
      const projectId = faker.datatype.string();
      const dateRange = {
        min: faker.datatype.datetime().getTime(),
        max: faker.datatype.datetime().getTime()
      };
      const terms = [faker.datatype.string(), faker.datatype.string()];
      const langCode = "eng";

      const englishName = faker.name.fullName();
      const japaneseName = faker.name.fullName();
      const chineseName = faker.name.fullName();

      const institutions = [faker.company.name(), faker.company.name()];

      const citedHit = generateMockCitationHit();
      citedHit._source!.institutions = institutions.map(
        (institution) => institution + faker.datatype.string()
      );

      const citedByPapersQuery1 = {
        query: {
          term: {
            [faker.database.column()]: faker.datatype.string()
          }
        }
      };

      const citedByPapersQuery2 = {
        query: {
          term: {
            [faker.database.column()]: faker.datatype.string()
          }
        }
      };

      citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValueOnce(
        citedByPapersQuery1
      );
      citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValueOnce(
        citedByPapersQuery2
      );

      elasticSearchService.query.mockResolvedValueOnce({
        hits: {
          hits: [
            {
              _index: faker.datatype.string(),
              _id: faker.datatype.uuid(),
              _source: {
                name_eng: englishName,
                name_jpn: japaneseName,
                name_cmn: chineseName,
                institutions
              }
            }
          ]
        }
      } as SearchResponse);

      elasticSearchService.msearch.mockResolvedValueOnce({
        took: faker.datatype.number(),
        responses: [
          {
            hits: {
              hits: [citedHit]
            }
          } as MsearchMultiSearchItem,
          {
            hits: {
              hits: [] as Array<SearchHit<CitationDocument>>
            }
          } as MsearchMultiSearchItem
        ]
      });

      const response = await influencesResourceService.citationProfile(
        personId,
        projectId,
        dateRange,
        terms,
        langCode
      );

      expect(response).toEqual({
        cited: [
          expect.objectContaining({
            sharedMetrics: [
              {
                count: 0,
                name: "Shared Institutions"
              }
            ]
          })
        ],
        citedBy: [],
        fullName: englishName,
        fullName_jpn: japaneseName,
        fullName_cmn: chineseName
      });
    });

    describe("name translation selection", () => {
      it("should return name_eng value when langCode parameter is not supplied", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        const citedHit = generateMockCitationHit();
        citedHit._source!.institutions = institutions.map(
          (institution) => institution + faker.datatype.string()
        );

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms
        );

        expect(response).toEqual({
          cited: [
            expect.objectContaining({
              fullName: citedHit._source!.name_eng
            })
          ],
          citedBy: [],
          fullName: englishName,
          fullName_jpn: japaneseName,
          fullName_cmn: chineseName
        });
      });

      it("should return name_cmn value when available and langCode parameter is 'cmn'", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        const citedHit = generateMockCitationHit();
        citedHit._source!.institutions = institutions.map(
          (institution) => institution + faker.datatype.string()
        );

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms,
          "cmn"
        );

        expect(response).toEqual({
          cited: [
            expect.objectContaining({
              fullName: citedHit._source!.name_cmn
            })
          ],
          citedBy: [],
          fullName: englishName,
          fullName_jpn: japaneseName,
          fullName_cmn: chineseName
        });
      });

      it("should return name_jpn value when available and langCode parameter is 'jpn'", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        const citedHit = generateMockCitationHit();
        citedHit._source!.institutions = institutions.map(
          (institution) => institution + faker.datatype.string()
        );

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms,
          "jpn"
        );

        expect(response).toEqual({
          cited: [
            expect.objectContaining({
              fullName: citedHit._source!.name_jpn
            })
          ],
          citedBy: [],
          fullName: englishName,
          fullName_jpn: japaneseName,
          fullName_cmn: chineseName
        });
      });

      it("should return name_eng value when langCode parameter is 'cmn' but name_cmn is not available", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        const citedHit = generateMockCitationHit({
          language: "eng",
          overrides: {
            name_cmn: undefined
          }
        });

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms,
          "cmn"
        );

        expect(response).toEqual({
          cited: [
            expect.objectContaining({
              fullName: citedHit._source!.name_eng
            })
          ],
          citedBy: [],
          fullName: englishName,
          fullName_jpn: japaneseName,
          fullName_cmn: chineseName
        });
      });

      it("should return name_eng value when langCode parameter is 'jpn' but name_jpn is not available", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        const citedHit = generateMockCitationHit({
          language: "eng",
          overrides: {
            name_jpn: undefined
          }
        });

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms,
          "jpn"
        );

        expect(response).toEqual({
          cited: [
            expect.objectContaining({
              fullName: citedHit._source!.name_eng
            })
          ],
          citedBy: [],
          fullName: englishName,
          fullName_jpn: japaneseName,
          fullName_cmn: chineseName
        });
      });

      it("should return empty string value when langCode parameter is 'cmn' but neither name_cmn nor name_eng are available", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        const citedHit = generateMockCitationHit({
          language: "eng",
          overrides: {
            name_jpn: undefined,
            name_eng: undefined
          }
        });

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms,
          "jpn"
        );

        expect(response).toEqual({
          cited: [
            expect.objectContaining({
              fullName: ""
            })
          ],
          citedBy: [],
          fullName: englishName,
          fullName_jpn: japaneseName,
          fullName_cmn: chineseName
        });
      });

      it("should return empty string value when langCode parameter is 'jpn' but neither name_jpn nor name_eng are available", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        const citedHit = generateMockCitationHit({
          language: "eng",
          overrides: {
            name_cmn: undefined,
            name_eng: undefined
          }
        });

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms,
          "cmn"
        );

        expect(response).toEqual({
          cited: [
            expect.objectContaining({
              fullName: ""
            })
          ],
          citedBy: [],
          fullName: englishName,
          fullName_jpn: japaneseName,
          fullName_cmn: chineseName
        });
      });

      it("should return empty string when name_eng is not available", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];

        const englishName = faker.name.fullName();
        const japaneseName = faker.name.fullName();
        const chineseName = faker.name.fullName();

        const institutions = [faker.company.name(), faker.company.name()];

        const citedHit = generateMockCitationHit({
          language: "eng",
          overrides: {
            name_eng: undefined
          }
        });

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: englishName,
                  name_jpn: japaneseName,
                  name_cmn: chineseName,
                  institutions
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms
        );

        expect(response).toEqual({
          cited: [
            expect.objectContaining({
              fullName: ""
            })
          ],
          citedBy: [],
          fullName: englishName,
          fullName_jpn: japaneseName,
          fullName_cmn: chineseName
        });
      });
    });

    describe("inner hit publication fields", () => {
      it("should set id to empty string when not in publication innerHit", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];
        const language = "eng";

        const publicationInnerHit = generateMockPublication("eng", {
          "publications.id": undefined
        });

        const citedHit = generateMockCitationHit({
          innerHitsOverrides: [publicationInnerHit]
        });

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: "",
                  name_jpn: "",
                  name_cmn: "",
                  institutions: []
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms,
          language
        );

        expect(response).toEqual(
          expect.objectContaining({
            cited: [
              expect.objectContaining({
                publications: [
                  expect.objectContaining({
                    id: ""
                  })
                ]
              })
            ]
          })
        );
      });

      it("should set citationCount to zero when not in publication innerHit", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];
        const language = "eng";

        const publicationInnerHit = generateMockPublication("eng", {
          "publications.citationCount": undefined
        });

        const citedHit = generateMockCitationHit({
          innerHitsOverrides: [publicationInnerHit]
        });

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: "",
                  name_jpn: "",
                  name_cmn: "",
                  institutions: []
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms,
          language
        );

        expect(response).toEqual(
          expect.objectContaining({
            cited: [
              expect.objectContaining({
                publications: [
                  expect.objectContaining({
                    citationCount: 0
                  })
                ]
              })
            ]
          })
        );
      });

      it("should set socialMediaCount to zero when not in publication innerHit", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];
        const language = "eng";

        const publicationInnerHit = generateMockPublication("eng", {
          "publications.microBloggingCount": undefined
        });

        const citedHit = generateMockCitationHit({
          innerHitsOverrides: [publicationInnerHit]
        });

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: "",
                  name_jpn: "",
                  name_cmn: "",
                  institutions: []
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms,
          language
        );

        expect(response).toEqual(
          expect.objectContaining({
            cited: [
              expect.objectContaining({
                publications: [
                  expect.objectContaining({
                    socialMediaCount: 0
                  })
                ]
              })
            ]
          })
        );
      });

      it("should set date to undefined when not in publication innerHit", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const citationProfileQueryBuilder = createMockInstance(
          CitationProfileQueryBuilder
        );
        citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
          _source_includes: [faker.database.column(), faker.database.column()],
          size: faker.datatype.number(),
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        });

        const languageDetectService = createMockInstance(LanguageDetectService);
        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTreeToElasticsearchQueriesService = createMockInstance(
          ParsedQueryTreeToElasticsearchQueriesService
        );

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree: faker.datatype.string(),
            synonyms: []
          }
        );

        const queryContainer = {
          term: {
            [faker.random.word()]: faker.datatype.string()
          }
        };
        parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
          queryContainer
        );

        const influencesResourceService = new InfluencesResourceService(
          configService,
          elasticSearchService,
          citationProfileQueryBuilder,
          languageDetectService,
          queryParserService,
          parsedQueryTreeToElasticsearchQueriesService
        );

        const personId = faker.datatype.uuid();
        const projectId = faker.datatype.string();
        const dateRange = {
          min: faker.datatype.datetime().getTime(),
          max: faker.datatype.datetime().getTime()
        };
        const terms = [faker.datatype.string(), faker.datatype.string()];
        const language = "eng";

        const publicationInnerHit = generateMockPublication("eng", {
          "publications.datePublished": undefined
        });

        const citedHit = generateMockCitationHit({
          innerHitsOverrides: [publicationInnerHit]
        });

        elasticSearchService.query.mockResolvedValueOnce({
          hits: {
            hits: [
              {
                _index: faker.datatype.string(),
                _id: faker.datatype.uuid(),
                _source: {
                  name_eng: "",
                  name_jpn: "",
                  name_cmn: "",
                  institutions: []
                }
              }
            ]
          }
        } as SearchResponse);

        elasticSearchService.msearch.mockResolvedValueOnce({
          took: faker.datatype.number(),
          responses: [
            {
              hits: {
                hits: [citedHit]
              }
            } as MsearchMultiSearchItem,
            {
              hits: {
                hits: [] as Array<SearchHit<CitationDocument>>
              }
            } as MsearchMultiSearchItem
          ]
        });

        const response = await influencesResourceService.citationProfile(
          personId,
          projectId,
          dateRange,
          terms,
          language
        );

        expect(response).toEqual(
          expect.objectContaining({
            cited: [
              expect.objectContaining({
                publications: [
                  expect.objectContaining({
                    date: undefined
                  })
                ]
              })
            ]
          })
        );
      });

      describe("journal", () => {
        it("should select japanese journal name when requested as language", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          const citationProfileQueryBuilder = createMockInstance(
            CitationProfileQueryBuilder
          );
          citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
            _source_includes: [
              faker.database.column(),
              faker.database.column()
            ],
            size: faker.datatype.number(),
            query: {
              term: {
                [faker.database.column()]: faker.datatype.string()
              }
            }
          });

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const queryParserService = createMockInstance(QueryParserService);
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
            {
              parsedQueryTree: faker.datatype.string(),
              synonyms: []
            }
          );

          const queryContainer = {
            term: {
              [faker.random.word()]: faker.datatype.string()
            }
          };
          parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
            queryContainer
          );

          const influencesResourceService = new InfluencesResourceService(
            configService,
            elasticSearchService,
            citationProfileQueryBuilder,
            languageDetectService,
            queryParserService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const publicationInnerHit = generateMockPublication();

          const citedHit = generateMockCitationHit({
            innerHitsOverrides: [publicationInnerHit]
          });

          elasticSearchService.query.mockResolvedValueOnce({
            hits: {
              hits: [
                {
                  _index: faker.datatype.string(),
                  _id: faker.datatype.uuid(),
                  _source: {
                    name_eng: "",
                    name_jpn: "",
                    name_cmn: "",
                    institutions: []
                  }
                }
              ]
            }
          } as SearchResponse);

          elasticSearchService.msearch.mockResolvedValueOnce({
            took: faker.datatype.number(),
            responses: [
              {
                hits: {
                  hits: [citedHit]
                }
              } as MsearchMultiSearchItem,
              {
                hits: {
                  hits: [] as Array<SearchHit<CitationDocument>>
                }
              } as MsearchMultiSearchItem
            ]
          });

          const personId = faker.datatype.uuid();
          const projectId = faker.datatype.string();
          const dateRange = {
            min: faker.datatype.datetime().getTime(),
            max: faker.datatype.datetime().getTime()
          };
          const terms = [faker.datatype.string(), faker.datatype.string()];
          const language = "jpn";

          const response = await influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            language
          );

          expect(response).toEqual(
            expect.objectContaining({
              cited: [
                expect.objectContaining({
                  publications: [
                    expect.objectContaining({
                      journal:
                        publicationInnerHit.fields[
                          "publications.journalName_jpn"
                        ][0]
                    })
                  ]
                })
              ]
            })
          );
        });

        it("should select chinese journal name when requested as language", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          const citationProfileQueryBuilder = createMockInstance(
            CitationProfileQueryBuilder
          );
          citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
            _source_includes: [
              faker.database.column(),
              faker.database.column()
            ],
            size: faker.datatype.number(),
            query: {
              term: {
                [faker.database.column()]: faker.datatype.string()
              }
            }
          });

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const queryParserService = createMockInstance(QueryParserService);
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
            {
              parsedQueryTree: faker.datatype.string(),
              synonyms: []
            }
          );

          const queryContainer = {
            term: {
              [faker.random.word()]: faker.datatype.string()
            }
          };
          parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
            queryContainer
          );

          const influencesResourceService = new InfluencesResourceService(
            configService,
            elasticSearchService,
            citationProfileQueryBuilder,
            languageDetectService,
            queryParserService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const publicationInnerHit = generateMockPublication();

          const citedHit = generateMockCitationHit({
            innerHitsOverrides: [publicationInnerHit]
          });

          elasticSearchService.query.mockResolvedValueOnce({
            hits: {
              hits: [
                {
                  _index: faker.datatype.string(),
                  _id: faker.datatype.uuid(),
                  _source: {
                    name_eng: "",
                    name_jpn: "",
                    name_cmn: "",
                    institutions: []
                  }
                }
              ]
            }
          } as SearchResponse);

          elasticSearchService.msearch.mockResolvedValueOnce({
            took: faker.datatype.number(),
            responses: [
              {
                hits: {
                  hits: [citedHit]
                }
              } as MsearchMultiSearchItem,
              {
                hits: {
                  hits: [] as Array<SearchHit<CitationDocument>>
                }
              } as MsearchMultiSearchItem
            ]
          });

          const personId = faker.datatype.uuid();
          const projectId = faker.datatype.string();
          const dateRange = {
            min: faker.datatype.datetime().getTime(),
            max: faker.datatype.datetime().getTime()
          };
          const terms = [faker.datatype.string(), faker.datatype.string()];
          const language = "cmn";

          const response = await influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            language
          );

          expect(response).toEqual(
            expect.objectContaining({
              cited: [
                expect.objectContaining({
                  publications: [
                    expect.objectContaining({
                      journal:
                        publicationInnerHit.fields[
                          "publications.journalName_cmn"
                        ][0]
                    })
                  ]
                })
              ]
            })
          );
        });

        it("should select english journal name when japanese is requested but not available", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          const citationProfileQueryBuilder = createMockInstance(
            CitationProfileQueryBuilder
          );
          citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
            _source_includes: [
              faker.database.column(),
              faker.database.column()
            ],
            size: faker.datatype.number(),
            query: {
              term: {
                [faker.database.column()]: faker.datatype.string()
              }
            }
          });

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const queryParserService = createMockInstance(QueryParserService);
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
            {
              parsedQueryTree: faker.datatype.string(),
              synonyms: []
            }
          );

          const queryContainer = {
            term: {
              [faker.random.word()]: faker.datatype.string()
            }
          };
          parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
            queryContainer
          );

          const influencesResourceService = new InfluencesResourceService(
            configService,
            elasticSearchService,
            citationProfileQueryBuilder,
            languageDetectService,
            queryParserService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const publicationInnerHit = generateMockPublication("eng", {
            "publications.journalName_jpn": undefined
          });

          const citedHit = generateMockCitationHit({
            innerHitsOverrides: [publicationInnerHit]
          });

          elasticSearchService.query.mockResolvedValueOnce({
            hits: {
              hits: [
                {
                  _index: faker.datatype.string(),
                  _id: faker.datatype.uuid(),
                  _source: {
                    name_eng: "",
                    name_jpn: "",
                    name_cmn: "",
                    institutions: []
                  }
                }
              ]
            }
          } as SearchResponse);

          elasticSearchService.msearch.mockResolvedValueOnce({
            took: faker.datatype.number(),
            responses: [
              {
                hits: {
                  hits: [citedHit]
                }
              } as MsearchMultiSearchItem,
              {
                hits: {
                  hits: [] as Array<SearchHit<CitationDocument>>
                }
              } as MsearchMultiSearchItem
            ]
          });

          const personId = faker.datatype.uuid();
          const projectId = faker.datatype.string();
          const dateRange = {
            min: faker.datatype.datetime().getTime(),
            max: faker.datatype.datetime().getTime()
          };
          const terms = [faker.datatype.string(), faker.datatype.string()];
          const language = "jpn";

          const response = await influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            language
          );

          expect(response).toEqual(
            expect.objectContaining({
              cited: [
                expect.objectContaining({
                  publications: [
                    expect.objectContaining({
                      journal:
                        publicationInnerHit.fields[
                          "publications.journalName_eng"
                        ][0]
                    })
                  ]
                })
              ]
            })
          );
        });

        it("should select english journal name when chinese is requested but not available", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          const citationProfileQueryBuilder = createMockInstance(
            CitationProfileQueryBuilder
          );
          citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
            _source_includes: [
              faker.database.column(),
              faker.database.column()
            ],
            size: faker.datatype.number(),
            query: {
              term: {
                [faker.database.column()]: faker.datatype.string()
              }
            }
          });

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const queryParserService = createMockInstance(QueryParserService);
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
            {
              parsedQueryTree: faker.datatype.string(),
              synonyms: []
            }
          );

          const queryContainer = {
            term: {
              [faker.random.word()]: faker.datatype.string()
            }
          };
          parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
            queryContainer
          );

          const influencesResourceService = new InfluencesResourceService(
            configService,
            elasticSearchService,
            citationProfileQueryBuilder,
            languageDetectService,
            queryParserService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const publicationInnerHit = generateMockPublication("eng", {
            "publications.journalName_cmn": undefined
          });

          const citedHit = generateMockCitationHit({
            innerHitsOverrides: [publicationInnerHit]
          });

          elasticSearchService.query.mockResolvedValueOnce({
            hits: {
              hits: [
                {
                  _index: faker.datatype.string(),
                  _id: faker.datatype.uuid(),
                  _source: {
                    name_eng: "",
                    name_jpn: "",
                    name_cmn: "",
                    institutions: []
                  }
                }
              ]
            }
          } as SearchResponse);

          elasticSearchService.msearch.mockResolvedValueOnce({
            took: faker.datatype.number(),
            responses: [
              {
                hits: {
                  hits: [citedHit]
                }
              } as MsearchMultiSearchItem,
              {
                hits: {
                  hits: [] as Array<SearchHit<CitationDocument>>
                }
              } as MsearchMultiSearchItem
            ]
          });

          const personId = faker.datatype.uuid();
          const projectId = faker.datatype.string();
          const dateRange = {
            min: faker.datatype.datetime().getTime(),
            max: faker.datatype.datetime().getTime()
          };
          const terms = [faker.datatype.string(), faker.datatype.string()];
          const language = "cmn";

          const response = await influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            language
          );

          expect(response).toEqual(
            expect.objectContaining({
              cited: [
                expect.objectContaining({
                  publications: [
                    expect.objectContaining({
                      journal:
                        publicationInnerHit.fields[
                          "publications.journalName_eng"
                        ][0]
                    })
                  ]
                })
              ]
            })
          );
        });
      });

      describe("title", () => {
        it("should select japanese title when requested as language", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          const citationProfileQueryBuilder = createMockInstance(
            CitationProfileQueryBuilder
          );
          citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
            _source_includes: [
              faker.database.column(),
              faker.database.column()
            ],
            size: faker.datatype.number(),
            query: {
              term: {
                [faker.database.column()]: faker.datatype.string()
              }
            }
          });

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const queryParserService = createMockInstance(QueryParserService);
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
            {
              parsedQueryTree: faker.datatype.string(),
              synonyms: []
            }
          );

          const queryContainer = {
            term: {
              [faker.random.word()]: faker.datatype.string()
            }
          };
          parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
            queryContainer
          );

          const influencesResourceService = new InfluencesResourceService(
            configService,
            elasticSearchService,
            citationProfileQueryBuilder,
            languageDetectService,
            queryParserService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const publicationInnerHit = generateMockPublication();

          const citedHit = generateMockCitationHit({
            innerHitsOverrides: [publicationInnerHit]
          });

          elasticSearchService.query.mockResolvedValueOnce({
            hits: {
              hits: [
                {
                  _index: faker.datatype.string(),
                  _id: faker.datatype.uuid(),
                  _source: {
                    name_eng: "",
                    name_jpn: "",
                    name_cmn: "",
                    institutions: []
                  }
                }
              ]
            }
          } as SearchResponse);

          elasticSearchService.msearch.mockResolvedValueOnce({
            took: faker.datatype.number(),
            responses: [
              {
                hits: {
                  hits: [citedHit]
                }
              } as MsearchMultiSearchItem,
              {
                hits: {
                  hits: [] as Array<SearchHit<CitationDocument>>
                }
              } as MsearchMultiSearchItem
            ]
          });

          const personId = faker.datatype.uuid();
          const projectId = faker.datatype.string();
          const dateRange = {
            min: faker.datatype.datetime().getTime(),
            max: faker.datatype.datetime().getTime()
          };
          const terms = [faker.datatype.string(), faker.datatype.string()];
          const language = "jpn";

          const response = await influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            language
          );

          expect(response).toEqual(
            expect.objectContaining({
              cited: [
                expect.objectContaining({
                  publications: [
                    expect.objectContaining({
                      title:
                        publicationInnerHit.fields[
                          "publications.title_jpn.keyword"
                        ][0]
                    })
                  ]
                })
              ]
            })
          );
        });

        it("should select chinese title when requested as language", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          const citationProfileQueryBuilder = createMockInstance(
            CitationProfileQueryBuilder
          );
          citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
            _source_includes: [
              faker.database.column(),
              faker.database.column()
            ],
            size: faker.datatype.number(),
            query: {
              term: {
                [faker.database.column()]: faker.datatype.string()
              }
            }
          });

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const queryParserService = createMockInstance(QueryParserService);
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
            {
              parsedQueryTree: faker.datatype.string(),
              synonyms: []
            }
          );

          const queryContainer = {
            term: {
              [faker.random.word()]: faker.datatype.string()
            }
          };
          parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
            queryContainer
          );

          const influencesResourceService = new InfluencesResourceService(
            configService,
            elasticSearchService,
            citationProfileQueryBuilder,
            languageDetectService,
            queryParserService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const publicationInnerHit = generateMockPublication();

          const citedHit = generateMockCitationHit({
            innerHitsOverrides: [publicationInnerHit]
          });

          elasticSearchService.query.mockResolvedValueOnce({
            hits: {
              hits: [
                {
                  _index: faker.datatype.string(),
                  _id: faker.datatype.uuid(),
                  _source: {
                    name_eng: "",
                    name_jpn: "",
                    name_cmn: "",
                    institutions: []
                  }
                }
              ]
            }
          } as SearchResponse);

          elasticSearchService.msearch.mockResolvedValueOnce({
            took: faker.datatype.number(),
            responses: [
              {
                hits: {
                  hits: [citedHit]
                }
              } as MsearchMultiSearchItem,
              {
                hits: {
                  hits: [] as Array<SearchHit<CitationDocument>>
                }
              } as MsearchMultiSearchItem
            ]
          });

          const personId = faker.datatype.uuid();
          const projectId = faker.datatype.string();
          const dateRange = {
            min: faker.datatype.datetime().getTime(),
            max: faker.datatype.datetime().getTime()
          };
          const terms = [faker.datatype.string(), faker.datatype.string()];
          const language = "cmn";

          const response = await influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            language
          );

          expect(response).toEqual(
            expect.objectContaining({
              cited: [
                expect.objectContaining({
                  publications: [
                    expect.objectContaining({
                      title:
                        publicationInnerHit.fields[
                          "publications.title_cmn.keyword"
                        ][0]
                    })
                  ]
                })
              ]
            })
          );
        });

        it("should select english title when japanese is requested but not available", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          const citationProfileQueryBuilder = createMockInstance(
            CitationProfileQueryBuilder
          );
          citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
            _source_includes: [
              faker.database.column(),
              faker.database.column()
            ],
            size: faker.datatype.number(),
            query: {
              term: {
                [faker.database.column()]: faker.datatype.string()
              }
            }
          });

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const queryParserService = createMockInstance(QueryParserService);
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
            {
              parsedQueryTree: faker.datatype.string(),
              synonyms: []
            }
          );

          const queryContainer = {
            term: {
              [faker.random.word()]: faker.datatype.string()
            }
          };
          parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
            queryContainer
          );

          const influencesResourceService = new InfluencesResourceService(
            configService,
            elasticSearchService,
            citationProfileQueryBuilder,
            languageDetectService,
            queryParserService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const publicationInnerHit = generateMockPublication("eng", {
            "publications.title_jpn.keyword": undefined
          });

          const citedHit = generateMockCitationHit({
            innerHitsOverrides: [publicationInnerHit]
          });

          elasticSearchService.query.mockResolvedValueOnce({
            hits: {
              hits: [
                {
                  _index: faker.datatype.string(),
                  _id: faker.datatype.uuid(),
                  _source: {
                    name_eng: "",
                    name_jpn: "",
                    name_cmn: "",
                    institutions: []
                  }
                }
              ]
            }
          } as SearchResponse);

          elasticSearchService.msearch.mockResolvedValueOnce({
            took: faker.datatype.number(),
            responses: [
              {
                hits: {
                  hits: [citedHit]
                }
              } as MsearchMultiSearchItem,
              {
                hits: {
                  hits: [] as Array<SearchHit<CitationDocument>>
                }
              } as MsearchMultiSearchItem
            ]
          });

          const personId = faker.datatype.uuid();
          const projectId = faker.datatype.string();
          const dateRange = {
            min: faker.datatype.datetime().getTime(),
            max: faker.datatype.datetime().getTime()
          };
          const terms = [faker.datatype.string(), faker.datatype.string()];
          const language = "jpn";

          const response = await influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            language
          );

          expect(response).toEqual(
            expect.objectContaining({
              cited: [
                expect.objectContaining({
                  publications: [
                    expect.objectContaining({
                      title:
                        publicationInnerHit.fields[
                          "publications.title_eng.keyword"
                        ][0]
                    })
                  ]
                })
              ]
            })
          );
        });

        it("should select english title when chinese is requested but not available", async () => {
          const configService = createMockInstance(ConfigService);
          configService.elasticPeopleIndex = faker.datatype.string();

          const elasticSearchService = createMockInstance(ElasticSearchService);

          const citationProfileQueryBuilder = createMockInstance(
            CitationProfileQueryBuilder
          );
          citationProfileQueryBuilder.buildCitedByPapersQuery.mockReturnValue({
            _source_includes: [
              faker.database.column(),
              faker.database.column()
            ],
            size: faker.datatype.number(),
            query: {
              term: {
                [faker.database.column()]: faker.datatype.string()
              }
            }
          });

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );
          const queryParserService = createMockInstance(QueryParserService);
          const parsedQueryTreeToElasticsearchQueriesService =
            createMockInstance(ParsedQueryTreeToElasticsearchQueriesService);

          queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
            {
              parsedQueryTree: faker.datatype.string(),
              synonyms: []
            }
          );

          const queryContainer = {
            term: {
              [faker.random.word()]: faker.datatype.string()
            }
          };
          parsedQueryTreeToElasticsearchQueriesService.parse.mockReturnValue(
            queryContainer
          );

          const influencesResourceService = new InfluencesResourceService(
            configService,
            elasticSearchService,
            citationProfileQueryBuilder,
            languageDetectService,
            queryParserService,
            parsedQueryTreeToElasticsearchQueriesService
          );

          const publicationInnerHit = generateMockPublication("eng", {
            "publications.title_cmn.keyword": undefined
          });

          const citedHit = generateMockCitationHit({
            innerHitsOverrides: [publicationInnerHit]
          });

          elasticSearchService.query.mockResolvedValueOnce({
            hits: {
              hits: [
                {
                  _index: faker.datatype.string(),
                  _id: faker.datatype.uuid(),
                  _source: {
                    name_eng: "",
                    name_jpn: "",
                    name_cmn: "",
                    institutions: []
                  }
                }
              ]
            }
          } as SearchResponse);

          elasticSearchService.msearch.mockResolvedValueOnce({
            took: faker.datatype.number(),
            responses: [
              {
                hits: {
                  hits: [citedHit]
                }
              } as MsearchMultiSearchItem,
              {
                hits: {
                  hits: [] as Array<SearchHit<CitationDocument>>
                }
              } as MsearchMultiSearchItem
            ]
          });

          const personId = faker.datatype.uuid();
          const projectId = faker.datatype.string();
          const dateRange = {
            min: faker.datatype.datetime().getTime(),
            max: faker.datatype.datetime().getTime()
          };
          const terms = [faker.datatype.string(), faker.datatype.string()];
          const language = "cmn";

          const response = await influencesResourceService.citationProfile(
            personId,
            projectId,
            dateRange,
            terms,
            language
          );

          expect(response).toEqual(
            expect.objectContaining({
              cited: [
                expect.objectContaining({
                  publications: [
                    expect.objectContaining({
                      title:
                        publicationInnerHit.fields[
                          "publications.title_eng.keyword"
                        ][0]
                    })
                  ]
                })
              ]
            })
          );
        });
      });
    });
  });
});
