import { faker } from "@faker-js/faker";
import * as _ from "lodash";

import {
  CollaboratorCountAggregation,
  HCPDocument,
  CollaboratorDocument,
  NetworkResponseAdapterService,
  DocCountBucket
} from "./NetworkResponseAdapterService";
import { NetworkNestedDocPath as NestedDocPath } from "./queryBuilders/NetworkQueryBuilder";
import {
  SearchHit,
  SearchResponse,
  SearchTotalHitsRelation,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import {
  SharedWorksPage,
  SharedTrial,
  SharedCongress,
  SharedPublication,
  NetworkRequest,
  NetworkCollaboratorRequest,
  NetworkFilterAutocompleteField
} from "@h1nyc/search-sdk";
import { ENGLISH, CHINESE, JAPANESE } from "./LanguageDetectService";
import { createMockInstance } from "../util/TestUtils";
import { LocationLabelFormatterService } from "./LocationLabelFormatterService";

const FAKE_NUMBER_MAX = 5; // to keep test execution time down
const LANGUAGE_CODES = [ENGLISH, CHINESE, JAPANESE];
const EMPTY_STRING = "";

function generateMockHCPHit(overrides = {}): SearchHit<HCPDocument> {
  const numAffiliations = faker.datatype.number(FAKE_NUMBER_MAX);
  const hcp = {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid(),
      affiliations: Array.from(new Array(numAffiliations), () => {
        return {
          type: faker.datatype.string(),
          isCurrent: faker.datatype.boolean(),
          institution: {
            name: faker.company.name(),
            id: faker.datatype.uuid(),
            ultimateParentId: faker.datatype.uuid()
          }
        };
      })
    }
  };

  return _.merge(hcp, overrides);
}

function generateMockCollaboratorHitForNetworkRequest(
  overrides = {},
  numSharedAffiliations = faker.datatype.number(FAKE_NUMBER_MAX),
  numLocations = faker.datatype.number(FAKE_NUMBER_MAX)
): SearchHit<CollaboratorDocument> {
  const eq: SearchTotalHitsRelation = "eq";
  const collaborator = {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid()
    },
    inner_hits: {
      sharedAffiliations: {
        hits: {
          total: {
            value: numSharedAffiliations,
            relation: eq
          },
          max_score: faker.datatype.number(),
          hits: Array.from(new Array(numSharedAffiliations), () => {
            return generateMockSharedAffiliation();
          })
        }
      },
      locations: {
        hits: {
          total: {
            value: numLocations,
            relation: eq
          },
          max_score: faker.datatype.number(),
          hits: Array.from(new Array(numLocations), () => {
            return generateMockSharedAffiliation();
          })
        }
      },
      trials: {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: eq
          },
          hits: []
        }
      },
      congress: {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: eq
          },
          hits: []
        }
      },
      publications: {
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: eq
          },
          hits: []
        }
      }
    }
  };

  return _.merge(collaborator, overrides);
}

function generateMockCollaboratorHitForNetworkCollaboratorRequest(
  overrides = {},
  numSharedAffiliations = faker.datatype.number(FAKE_NUMBER_MAX),
  numSharedTrials = faker.datatype.number(FAKE_NUMBER_MAX),
  numSharedCongresses = faker.datatype.number(FAKE_NUMBER_MAX),
  numSharedPublications = faker.datatype.number(FAKE_NUMBER_MAX),
  numSharedEnglishPublications = faker.datatype.number(FAKE_NUMBER_MAX),
  numSharedJapanesePublications = faker.datatype.number(FAKE_NUMBER_MAX),
  numSharedChinesePublications = faker.datatype.number(FAKE_NUMBER_MAX)
): SearchHit<CollaboratorDocument> {
  const eq: SearchTotalHitsRelation = "eq";
  const collaborator = {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid()
    },
    inner_hits: {
      sharedAffiliations: {
        hits: {
          total: {
            value: numSharedAffiliations,
            relation: eq
          },
          hits: Array.from(new Array(numSharedAffiliations), () => {
            return generateMockSharedAffiliation();
          })
        }
      },
      trials: {
        hits: {
          total: {
            value: numSharedTrials,
            relation: eq
          },
          hits: Array.from(new Array(numSharedTrials), () => {
            return generateMockTrialHit();
          })
        }
      },
      congress: {
        hits: {
          total: {
            value: numSharedCongresses,
            relation: eq
          },
          hits: Array.from(new Array(numSharedCongresses), () => {
            return generateMockCongressHit();
          })
        }
      },
      publications: {
        hits: {
          total: {
            value:
              numSharedPublications +
              numSharedEnglishPublications +
              numSharedJapanesePublications +
              numSharedChinesePublications,
            relation: eq
          },
          hits: [
            ...Array.from(new Array(numSharedPublications), () => {
              return generateMockPublicationHit();
            }),
            ...Array.from(new Array(numSharedEnglishPublications), () => {
              return generateMockEnglishPublicationHit();
            }),
            ...Array.from(new Array(numSharedJapanesePublications), () => {
              return generateMockJapanesePublicationHit();
            }),
            ...Array.from(new Array(numSharedChinesePublications), () => {
              return generateMockChinesePublicationHit();
            })
          ]
        }
      }
    }
  };

  return _.merge(collaborator, overrides);
}

function generateMockSharedAffiliation() {
  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _source: {
      type: faker.datatype.string(),
      isCurrent: faker.datatype.boolean(),
      institution: {
        id: faker.datatype.uuid(),
        ultimateParentId: faker.datatype.uuid(),
        name: faker.company.name(),
        nameTranslations: LANGUAGE_CODES.map((languageCode) => {
          return {
            name: faker.company.name(),
            languageCode
          };
        }),
        address: generateMockAddress(),
        addressTranslations: LANGUAGE_CODES.map((languageCode) => {
          return {
            ...generateMockAddress(),
            languageCode
          };
        })
      }
    }
  };
}

function generateMockAddress() {
  return {
    id: faker.datatype.number(),
    street1: faker.address.streetAddress(),
    street2: faker.address.secondaryAddress(),
    street3: faker.random.word(),
    country: faker.address.country(),
    country_code: faker.address.countryCode(),
    city: faker.address.city(),
    region: faker.address.state(),
    region_code: faker.address.stateAbbr(),
    postal_code: faker.address.zipCode()
  };
}

function generateMockPublicationHit() {
  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid(),
      title_eng: faker.datatype.string(),
      title_jpn: EMPTY_STRING,
      title_cmn: EMPTY_STRING,
      datePublished: faker.date.past().toDateString(),
      persons: generateMockPersons()
    },
    fields: {
      "publications.type_eng": [faker.datatype.string()],
      "publications.journalName_eng": [faker.datatype.string()],
      "publications.type_jpn": [],
      "publications.journalName_jpn": [],
      "publications.type_cmn": [],
      "publications.journalName_cmn": []
    }
  };
}

function generateMockEnglishPublicationHit() {
  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid(),
      title_eng: faker.datatype.string(),
      title_jpn: EMPTY_STRING,
      title_cmn: EMPTY_STRING,
      datePublished: faker.date.past().toDateString(),
      persons: generateMockPersons(),
      languageCode: ENGLISH
    },
    fields: {
      "publications.type_eng": [faker.datatype.string()],
      "publications.journalName_eng": [faker.datatype.string()],
      "publications.type_jpn": [],
      "publications.journalName_jpn": [],
      "publications.type_cmn": [],
      "publications.journalName_cmn": []
    }
  };
}

function generateMockJapanesePublicationHit() {
  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid(),
      title_eng: EMPTY_STRING,
      title_jpn: faker.datatype.string(),
      title_cmn: EMPTY_STRING,
      datePublished: faker.date.past().toDateString(),
      persons: generateMockPersons(),
      languageCode: JAPANESE
    },
    fields: {
      "publications.type_eng": [],
      "publications.journalName_eng": [],
      "publications.type_jpn": [faker.datatype.string()],
      "publications.journalName_jpn": [faker.datatype.string()],
      "publications.type_cmn": [],
      "publications.journalName_cmn": []
    }
  };
}

function generateMockChinesePublicationHit() {
  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid(),
      title_eng: EMPTY_STRING,
      title_jpn: EMPTY_STRING,
      title_cmn: faker.datatype.string(),
      datePublished: faker.date.past().toDateString(),
      persons: generateMockPersons(),
      languageCode: CHINESE
    },
    fields: {
      "publications.type_eng": [],
      "publications.journalName_eng": [],
      "publications.type_jpn": [faker.datatype.string()],
      "publications.journalName_jpn": [faker.datatype.string()],
      "publications.type_cmn": [faker.datatype.string()],
      "publications.journalName_cmn": [faker.datatype.string()]
    }
  };
}

function generateMockTrialHit() {
  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid(),
      officialTitle_eng: faker.datatype.string(),
      startDate: faker.date.past().toDateString(),
      phase_eng: faker.datatype.string(),
      status_eng: faker.datatype.string(),
      sponsor_eng: faker.datatype.string(),
      persons: generateMockPersons()
    }
  };
}

function generateMockCongressHit() {
  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid(),
      title_eng: faker.datatype.string(),
      masterInitialDate: faker.date.past().toDateString(),
      persons: generateMockPersons(),
      type: faker.datatype.string()
    },
    fields: {
      "congress.organizer_eng": [faker.datatype.string()],
      "congress.name_eng": [faker.datatype.string()]
    }
  };
}

function generateMockPersons() {
  return Array.from(new Array(faker.datatype.number(FAKE_NUMBER_MAX)), () => {
    return {
      fullName: faker.datatype.string(),
      id: faker.datatype.uuid()
    };
  });
}

function generateMockElasticsearchResponse<T>(
  hits: Array<SearchHit<T>> = [],
  aggs: Record<string, CollaboratorCountAggregation> = {
    ...generateMockAggregation("trials"),
    ...generateMockAggregation("congress"),
    ...generateMockAggregation("publications")
  }
): SearchResponse<T> {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: hits.length,
        relation: "eq"
      },
      max_score: faker.datatype.number(),
      hits
    },
    aggregations: aggs
  };
}

function generateMockAggregation(
  bucket: NestedDocPath,
  peopleDocCount = faker.datatype.number()
): Record<string, CollaboratorCountAggregation> {
  return {
    [bucket]: {
      nested: {
        people: {
          doc_count: peopleDocCount
        }
      }
    }
  };
}

function generateMockAggregationForBulkNetwork(
  personId: string,
  bucket: NestedDocPath,
  peopleDocCount = faker.datatype.number()
): Record<string, any> {
  return {
    [bucket]: {
      doc_count: faker.datatype.number({ min: 1, max: 100 }),
      nested: {
        per_person: {
          buckets: [
            {
              key: personId,
              doc_count: faker.datatype.number({ min: 1, max: 100 }),
              people: {
                doc_count: peopleDocCount
              }
            }
          ]
        }
      }
    }
  };
}

function generateMockTotalCollaboratorsForBulkNetwork(
  personId: string,
  peopleDocCount = faker.datatype.number()
): Record<string, any> {
  return {
    total_collaborators: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: personId,
          doc_count: peopleDocCount
        }
      ]
    }
  };
}

function generateMockAggregates(buckets: Array<DocCountBucket> = []) {
  return {
    doc_count_error_upper_bound: faker.datatype.number(),
    sum_other_doc_count: faker.datatype.number(),
    buckets: buckets as DocCountBucket[]
  };
}

describe("NetworkResponseAdapterService", () => {
  describe("NetworkResponse", () => {
    it("should return a completely empty response when calling emptyNetworkResponse", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );

      const adaptedResponse =
        networkResponseAdapterService.emptyNetworkResponse();

      expect(adaptedResponse).toEqual({
        totalCollaborators: 0,
        totalPublicationCollaborators: 0,
        totalCongressCollaborators: 0,
        totalTrialCollaborators: 0,
        collaborators: []
      });
    });

    it("should return a completely empty response when there are no hits or aggs", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );
      const mockResponse = generateMockElasticsearchResponse<HCPDocument>([], {
        ...generateMockAggregation("trials", 0),
        ...generateMockAggregation("congress", 0),
        ...generateMockAggregation("publications", 0)
      });
      const personId = faker.datatype.uuid();

      const adaptedResponse =
        await networkResponseAdapterService.adaptToNetwork(
          {
            personId
          } as NetworkRequest,
          mockResponse.hits,
          mockResponse.aggregations,
          ENGLISH
        );

      expect(adaptedResponse).toEqual({
        totalCollaborators: 0,
        totalPublicationCollaborators: 0,
        totalCongressCollaborators: 0,
        totalTrialCollaborators: 0,
        collaborators: []
      });
    });

    it("should parse aggregations and hits to return number of publications/trials/congress/total collaborators", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );
      const numSharedTrialCollaborators = faker.datatype.number();
      const numSharedCongressCollaborators = faker.datatype.number();
      const numSharedPublicationCollaborators = faker.datatype.number();
      const collaboratorHits = [
        generateMockCollaboratorHitForNetworkRequest(),
        generateMockCollaboratorHitForNetworkRequest()
      ];
      const mockResponse =
        generateMockElasticsearchResponse<CollaboratorDocument>(
          collaboratorHits,
          {
            ...generateMockAggregation("trials", numSharedTrialCollaborators),
            ...generateMockAggregation(
              "congress",
              numSharedCongressCollaborators
            ),
            ...generateMockAggregation(
              "publications",
              numSharedPublicationCollaborators
            )
          }
        );
      const personId = faker.datatype.uuid();

      const adaptedResponse =
        await networkResponseAdapterService.adaptToNetwork(
          {
            personId
          } as NetworkRequest,
          mockResponse.hits,
          mockResponse.aggregations,
          ENGLISH
        );

      expect(adaptedResponse).toEqual({
        totalCollaborators: collaboratorHits.length,
        totalPublicationCollaborators: numSharedPublicationCollaborators,
        totalCongressCollaborators: numSharedCongressCollaborators,
        totalTrialCollaborators: numSharedTrialCollaborators,
        collaborators: expect.any(Array)
      });
    });

    it("should return collaborators array with entry for each hit and totalCollaborators matching the number of hits", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );
      const personId = faker.datatype.uuid();
      const collaboratorHit1 = generateMockCollaboratorHitForNetworkRequest();
      const collaboratorHit2 = generateMockCollaboratorHitForNetworkRequest();
      const collaboratorHit3 = generateMockCollaboratorHitForNetworkRequest();
      const collaboratorHits = [
        collaboratorHit1,
        collaboratorHit2,
        collaboratorHit3
      ];

      const mockElasticsearchResponse =
        generateMockElasticsearchResponse(collaboratorHits);

      const expectedResponse = {
        totalCollaborators: collaboratorHits.length,
        collaborators: expect.not.arrayContaining([
          expect.objectContaining({
            personId
          })
        ])
      };

      const adaptedResponse =
        await networkResponseAdapterService.adaptToNetwork(
          {
            personId
          } as NetworkRequest,
          mockElasticsearchResponse.hits,
          {},
          ENGLISH
        );

      expect(adaptedResponse).toEqual(
        expect.objectContaining(expectedResponse)
      );
    });

    it("should parse affiliations and locations from inner_hits and map the fields from the elasticsearch response to the correct fields in the adapted response for each collaborator", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );
      const numSharedTrialCollaborators = 1;
      const numSharedCongressCollaborators = 1;
      const numSharedPublicationCollaborators = 1;
      const mockHit = generateMockCollaboratorHitForNetworkRequest({}, 1, 1);
      const collaboratorHits = [mockHit];

      const mockResponse =
        generateMockElasticsearchResponse<CollaboratorDocument>(
          collaboratorHits,
          {
            ...generateMockAggregation("trials", numSharedTrialCollaborators),
            ...generateMockAggregation(
              "congress",
              numSharedCongressCollaborators
            ),
            ...generateMockAggregation(
              "publications",
              numSharedPublicationCollaborators
            )
          }
        );

      const mockAffiliation =
        mockHit.inner_hits!.sharedAffiliations.hits.hits[0]._source!;

      const mockLocation =
        mockHit.inner_hits!.locations.hits.hits[0]._source!.institution.addressTranslations.find(
          (translation: { languageCode: string }) =>
            translation.languageCode === ENGLISH
        );

      const personId = faker.datatype.uuid();

      const adaptedResponse =
        await networkResponseAdapterService.adaptToNetwork(
          {
            personId
          } as NetworkRequest,
          mockResponse.hits,
          mockResponse.aggregations,
          ENGLISH
        );

      const totalSharedTrials = (
        mockHit.inner_hits!.trials.hits.total as SearchTotalHits
      ).value;
      const totalSharedCongresses = (
        mockHit.inner_hits!.congress.hits.total as SearchTotalHits
      ).value;
      const totalSharedPublications = (
        mockHit.inner_hits!.publications.hits.total as SearchTotalHits
      ).value;
      const totalSharedWorks =
        totalSharedTrials + totalSharedCongresses + totalSharedPublications;

      expect(adaptedResponse).toEqual({
        totalCollaborators: collaboratorHits.length,
        totalPublicationCollaborators: numSharedPublicationCollaborators,
        totalCongressCollaborators: numSharedCongressCollaborators,
        totalTrialCollaborators: numSharedTrialCollaborators,
        collaborators: [
          {
            id: expect.any(String),
            personId: mockHit._source!.id,
            totalSharedWorks,
            totalSharedPublications,
            totalSharedCongresses,
            totalSharedTrials,
            sharedAffiliations: [
              {
                id: mockAffiliation.institution.id,
                name: mockAffiliation.institution.nameTranslations.find(
                  (translation: { languageCode: string }) =>
                    translation.languageCode === ENGLISH
                ).name,
                affiliationType: mockAffiliation.type,
                isCurrent: mockAffiliation.isCurrent,
                ultimateParentId: mockAffiliation.institution.ultimateParentId
              }
            ],
            locations: [
              {
                city: mockLocation.city,
                country: mockLocation.country,
                countryCode: mockLocation.country_code,
                postalCode: mockLocation.postal_code,
                region: mockLocation.region,
                regionCode: mockLocation.region_code
              }
            ]
          }
        ]
      });
    });

    it("should not return empty locations", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );
      const numSharedTrialCollaborators = 1;
      const numSharedCongressCollaborators = 1;
      const numSharedPublicationCollaborators = 1;
      const mockHit = generateMockCollaboratorHitForNetworkRequest({}, 1, 2);

      const mockAffiliation =
        mockHit.inner_hits!.sharedAffiliations.hits.hits[0]._source!;
      const mockLocation =
        mockHit.inner_hits!.locations.hits.hits[0]._source!.institution.addressTranslations.find(
          (translation: any) => translation.languageCode === ENGLISH
        );

      mockHit.inner_hits!.locations.hits.hits[1] = {
        _id: faker.datatype.uuid(),
        _index: faker.datatype.string(),
        _source: {
          type: faker.datatype.string(),
          isCurrent: faker.datatype.boolean(),
          institution: {
            id: faker.datatype.uuid(),
            ultimateParentId: faker.datatype.uuid(),
            name: faker.company.name()
          }
        }
      };

      const collaboratorHits = [mockHit];
      const mockResponse =
        generateMockElasticsearchResponse<CollaboratorDocument>(
          collaboratorHits,
          {
            ...generateMockAggregation("trials", numSharedTrialCollaborators),
            ...generateMockAggregation(
              "congress",
              numSharedCongressCollaborators
            ),
            ...generateMockAggregation(
              "publications",
              numSharedPublicationCollaborators
            )
          }
        );

      const personId = faker.datatype.uuid();

      const adaptedResponse =
        await networkResponseAdapterService.adaptToNetwork(
          {
            personId
          } as NetworkRequest,
          mockResponse.hits,
          mockResponse.aggregations,
          ENGLISH
        );

      const totalSharedTrials = (
        mockHit.inner_hits!.trials.hits.total as SearchTotalHits
      ).value;
      const totalSharedCongresses = (
        mockHit.inner_hits!.congress.hits.total as SearchTotalHits
      ).value;
      const totalSharedPublications = (
        mockHit.inner_hits!.publications.hits.total as SearchTotalHits
      ).value;
      const totalSharedWorks =
        totalSharedTrials + totalSharedCongresses + totalSharedPublications;

      expect(adaptedResponse).toEqual({
        totalCollaborators: collaboratorHits.length,
        totalPublicationCollaborators: numSharedPublicationCollaborators,
        totalCongressCollaborators: numSharedCongressCollaborators,
        totalTrialCollaborators: numSharedTrialCollaborators,
        collaborators: [
          {
            id: expect.any(String),
            personId: mockHit._source!.id,
            totalSharedWorks,
            totalSharedPublications,
            totalSharedCongresses,
            totalSharedTrials,
            sharedAffiliations: [
              {
                id: mockAffiliation.institution.id,
                name: mockAffiliation.institution.nameTranslations.find(
                  (translation: any) => translation.languageCode === ENGLISH
                ).name,
                affiliationType: mockAffiliation.type,
                isCurrent: mockAffiliation.isCurrent,
                ultimateParentId: mockAffiliation.institution.ultimateParentId
              }
            ],
            locations: [
              {
                city: mockLocation.city,
                country: mockLocation.country,
                countryCode: mockLocation.country_code,
                postalCode: mockLocation.postal_code,
                region: mockLocation.region,
                regionCode: mockLocation.region_code
              }
            ]
          }
        ]
      });
    });
  });

  describe("BulkNetworkResponse", () => {
    it("should return a completely empty response when there are no hits or aggs", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );
      const personIds = [faker.datatype.uuid()];
      const mockResponse = generateMockElasticsearchResponse<HCPDocument>([], {
        ...generateMockAggregationForBulkNetwork(personIds[0], "trials", 0),
        ...generateMockAggregationForBulkNetwork(personIds[0], "congress", 0),
        ...generateMockAggregationForBulkNetwork(
          personIds[0],
          "publications",
          0
        ),
        ...generateMockTotalCollaboratorsForBulkNetwork(personIds[0], 0)
      });

      const adaptedResponse =
        await networkResponseAdapterService.adaptToBulkNetwork(
          personIds,
          mockResponse.aggregations
        );

      expect(adaptedResponse).toEqual([
        {
          totalCollaborators: 0,
          totalPublicationCollaborators: 0,
          totalCongressCollaborators: 0,
          totalTrialCollaborators: 0
        }
      ]);
    });

    it("should parse aggregations and hits to return number of publications/trials/congress/total collaborators", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );
      const numSharedTrialCollaborators = faker.datatype.number();
      const numSharedCongressCollaborators = faker.datatype.number();
      const numSharedPublicationCollaborators = faker.datatype.number();
      const numSharedTotalCollaborators = faker.datatype.number();
      const collaboratorHits = [
        generateMockCollaboratorHitForNetworkRequest(),
        generateMockCollaboratorHitForNetworkRequest()
      ];
      const personIds = [faker.datatype.uuid()];
      const mockResponse =
        generateMockElasticsearchResponse<CollaboratorDocument>(
          collaboratorHits,
          {
            ...generateMockAggregationForBulkNetwork(
              personIds[0],
              "trials",
              numSharedTrialCollaborators
            ),
            ...generateMockAggregationForBulkNetwork(
              personIds[0],
              "congress",
              numSharedCongressCollaborators
            ),
            ...generateMockAggregationForBulkNetwork(
              personIds[0],
              "publications",
              numSharedPublicationCollaborators
            ),
            ...generateMockTotalCollaboratorsForBulkNetwork(
              personIds[0],
              numSharedTotalCollaborators
            )
          }
        );

      const adaptedResponse =
        await networkResponseAdapterService.adaptToBulkNetwork(
          personIds,
          mockResponse.aggregations
        );

      expect(adaptedResponse).toEqual([
        {
          totalCollaborators: numSharedTotalCollaborators,
          totalPublicationCollaborators: numSharedPublicationCollaborators,
          totalCongressCollaborators: numSharedCongressCollaborators,
          totalTrialCollaborators: numSharedTrialCollaborators
        }
      ]);
    });
  });

  describe("NetworkCollaborator", () => {
    it("should return a completely empty response when calling emptyNetworkCollaborator", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );

      const primaryHcpId = faker.datatype.uuid();
      const collaboratorId = faker.datatype.uuid();

      const adaptedResponse =
        networkResponseAdapterService.emptyNetworkCollaborator(
          primaryHcpId,
          collaboratorId
        );

      expect(adaptedResponse).toEqual({
        id: expect.any(String),
        personId: collaboratorId,
        totalSharedWorks: 0,
        totalSharedTrials: 0,
        totalSharedCongresses: 0,
        totalSharedPublications: 0,
        sharedWorks: [],
        sharedAffiliations: [],
        trialOffset: 0,
        congressOffset: 0,
        publicationOffset: 0
      });
    });

    it("should return an empty collaborator if there are zero hits for all assets", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );

      const collaboratorId = faker.datatype.uuid();
      const mockResponse =
        generateMockElasticsearchResponse<CollaboratorDocument>(
          [
            generateMockCollaboratorHitForNetworkCollaboratorRequest(
              {
                _source: {
                  id: collaboratorId
                }
              },
              0,
              0,
              0,
              0,
              0,
              0,
              0
            )
          ],
          {}
        );

      const adaptedResponse =
        await networkResponseAdapterService.adaptToCollaborator(
          {
            personId: faker.datatype.uuid(),
            collaboratorId
          } as NetworkCollaboratorRequest,
          mockResponse.hits,
          {
            limit: faker.datatype.number({ min: 1 }),
            trialOffset: 0,
            publicationOffset: 0,
            congressOffset: 0
          },
          ENGLISH
        );
      expect(adaptedResponse).toEqual({
        id: expect.anything(),
        personId: collaboratorId,
        totalSharedWorks: 0,
        totalSharedTrials: 0,
        totalSharedCongresses: 0,
        totalSharedPublications: 0,
        sharedWorks: [],
        sharedAffiliations: [],
        trialOffset: 0,
        congressOffset: 0,
        publicationOffset: 0
      });
    });

    it("should parse totals for for each shared work type from inner_hits totals and sum them up to get totalSharedWorks", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );

      const collaboratorId = faker.datatype.uuid();
      const numSharedAffiliations = faker.datatype.number(FAKE_NUMBER_MAX);
      const numSharedTrials = faker.datatype.number(FAKE_NUMBER_MAX);
      const numSharedCongresses = faker.datatype.number(FAKE_NUMBER_MAX);
      const numSharedPublications = faker.datatype.number(FAKE_NUMBER_MAX);
      const numSharedEnglishPublications =
        faker.datatype.number(FAKE_NUMBER_MAX);
      const numSharedJapanesePublications =
        faker.datatype.number(FAKE_NUMBER_MAX);
      const numSharedChinesePublications =
        faker.datatype.number(FAKE_NUMBER_MAX);

      const mockResponse =
        generateMockElasticsearchResponse<CollaboratorDocument>(
          [
            generateMockCollaboratorHitForNetworkCollaboratorRequest(
              {
                _source: {
                  id: collaboratorId
                }
              },
              numSharedAffiliations,
              numSharedTrials,
              numSharedCongresses,
              numSharedPublications,
              numSharedEnglishPublications,
              numSharedJapanesePublications,
              numSharedChinesePublications
            )
          ],
          {}
        );

      const adaptedResponse =
        await networkResponseAdapterService.adaptToCollaborator(
          {
            personId: faker.datatype.uuid(),
            collaboratorId
          } as NetworkCollaboratorRequest,
          mockResponse.hits,
          {
            limit: faker.datatype.number({ min: 1 }),
            trialOffset: 0,
            publicationOffset: 0,
            congressOffset: 0
          },
          ENGLISH
        );

      const totalNumPubs =
        numSharedPublications +
        numSharedEnglishPublications +
        numSharedJapanesePublications +
        numSharedChinesePublications;

      expect(adaptedResponse).toEqual({
        id: expect.any(String),
        personId: collaboratorId,
        totalSharedWorks: numSharedCongresses + numSharedTrials + totalNumPubs,
        totalSharedTrials: numSharedTrials,
        totalSharedCongresses: numSharedCongresses,
        totalSharedPublications: totalNumPubs,
        sharedAffiliations: expect.any(Array),
        sharedWorks: expect.any(Array),
        trialOffset: expect.any(Number),
        congressOffset: expect.any(Number),
        publicationOffset: expect.any(Number)
      });
    });

    it("should parse affiliations and shared works from inner_hits and map the fields from the elasticsearch response to the correct fields in the adapted response", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );

      const collaboratorId = faker.datatype.uuid();
      const numSharedAffiliations = 1;
      const numSharedCongresses = 1;
      const numSharedTrials = 1;
      const numSharedPublications = 1;
      const numSharedEnglishPublications = 0;
      const numSharedJapanesePublications = 0;
      const numSharedChinesePublications = 0;
      const mockHit = generateMockCollaboratorHitForNetworkCollaboratorRequest(
        {
          _source: {
            id: collaboratorId
          }
        },
        numSharedAffiliations,
        numSharedTrials,
        numSharedCongresses,
        numSharedPublications,
        numSharedEnglishPublications,
        numSharedJapanesePublications,
        numSharedChinesePublications
      );
      const mockResponse =
        generateMockElasticsearchResponse<CollaboratorDocument>([mockHit], {});

      const mockAffiliation =
        mockHit.inner_hits!.sharedAffiliations.hits.hits[0]._source!;

      const adaptedResponse =
        await networkResponseAdapterService.adaptToCollaborator(
          {
            personId: faker.datatype.uuid(),
            collaboratorId
          } as NetworkCollaboratorRequest,
          mockResponse.hits,
          {
            limit: faker.datatype.number({ min: 3 }),
            trialOffset: 0,
            publicationOffset: 0,
            congressOffset: 0
          },
          ENGLISH
        );
      expect(adaptedResponse).toEqual({
        id: expect.any(String),
        personId: collaboratorId,
        totalSharedWorks:
          numSharedCongresses + numSharedTrials + numSharedPublications,
        totalSharedTrials: numSharedTrials,
        totalSharedCongresses: numSharedCongresses,
        totalSharedPublications: numSharedPublications,
        trialOffset: 1,
        congressOffset: 1,
        publicationOffset: 1,
        sharedAffiliations: [
          {
            id: mockAffiliation.institution.id,
            name: mockAffiliation.institution.nameTranslations.find(
              (translation: any) => translation.languageCode === ENGLISH
            ).name,
            affiliationType: mockAffiliation.type,
            isCurrent: mockAffiliation.isCurrent,
            ultimateParentId: mockAffiliation.institution.ultimateParentId
          }
        ],
        sharedWorks: expect.arrayContaining([
          {
            id: mockHit.inner_hits!.publications.hits.hits[0]._source!.id,
            title: [
              mockHit.inner_hits!.publications.hits.hits[0]._source!.title_eng
            ],
            date: [
              mockHit.inner_hits!.publications.hits.hits[0]._source!
                .datePublished
            ],
            persons:
              mockHit.inner_hits!.publications.hits.hits[0]._source!.persons.map(
                (person: { id: string }) => {
                  return person.id;
                }
              ),
            type: mockHit.inner_hits!.publications.hits.hits[0].fields![
              "publications.type_eng"
            ],
            name: mockHit.inner_hits!.publications.hits.hits[0].fields![
              "publications.journalName_eng"
            ]
          },
          {
            id: mockHit.inner_hits!.congress.hits.hits[0]._source!.id,
            title: [
              mockHit.inner_hits!.congress.hits.hits[0]._source!.title_eng
            ],
            date: [
              mockHit.inner_hits!.congress.hits.hits[0]._source!
                .masterInitialDate
            ],
            persons:
              mockHit.inner_hits!.congress.hits.hits[0]._source!.persons.map(
                (person: { id: string }) => {
                  return person.id;
                }
              ),
            type: [mockHit.inner_hits!.congress.hits.hits[0]._source!.type],
            organizer:
              mockHit.inner_hits!.congress.hits.hits[0].fields![
                "congress.organizer_eng"
              ],
            name: mockHit.inner_hits!.congress.hits.hits[0].fields![
              "congress.name_eng"
            ]
          },
          {
            id: mockHit.inner_hits!.trials.hits.hits[0]._source!.id,
            title: [
              mockHit.inner_hits!.trials.hits.hits[0]._source!.officialTitle_eng
            ],
            date: [mockHit.inner_hits!.trials.hits.hits[0]._source!.startDate],
            persons:
              mockHit.inner_hits!.trials.hits.hits[0]._source!.persons.map(
                (person: { id: string }) => {
                  return person.id;
                }
              ),
            sponsor: [
              mockHit.inner_hits!.trials.hits.hits[0]._source!.sponsor_eng
            ],
            phase: [mockHit.inner_hits!.trials.hits.hits[0]._source!.phase_eng],
            status: [
              mockHit.inner_hits!.trials.hits.hits[0]._source!.status_eng
            ]
          }
        ])
      });
    });

    it("should return an array of shared works of length equal to the page size when number of shared work hits exceeds it", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );

      const numSharedAffiliations = faker.datatype.number(FAKE_NUMBER_MAX);
      const numSharedTrials = faker.datatype.number({
        min: 2,
        max: FAKE_NUMBER_MAX
      });
      const numSharedCongresses = faker.datatype.number({
        min: 2,
        max: FAKE_NUMBER_MAX
      });
      const numSharedPublications = faker.datatype.number({
        min: 2,
        max: FAKE_NUMBER_MAX
      });

      const page: SharedWorksPage = {
        limit: faker.datatype.number({ min: 1, max: 5 }),
        trialOffset: 0,
        publicationOffset: 0,
        congressOffset: 0
      };

      const collaboratorId = faker.datatype.uuid();
      const mockResponse =
        generateMockElasticsearchResponse<CollaboratorDocument>(
          [
            generateMockCollaboratorHitForNetworkCollaboratorRequest(
              {
                _source: {
                  id: collaboratorId
                }
              },
              numSharedAffiliations,
              numSharedTrials,
              numSharedCongresses,
              numSharedPublications,
              0,
              0,
              0
            )
          ],
          {}
        );

      const adaptedResponse =
        await networkResponseAdapterService.adaptToCollaborator(
          {
            personId: faker.datatype.uuid(),
            collaboratorId
          } as NetworkCollaboratorRequest,
          mockResponse.hits,
          page,
          ENGLISH
        );

      expect(adaptedResponse).toEqual({
        id: expect.any(String),
        personId: collaboratorId,
        totalSharedWorks:
          numSharedCongresses + numSharedTrials + numSharedPublications,
        totalSharedTrials: numSharedTrials,
        totalSharedCongresses: numSharedCongresses,
        totalSharedPublications: numSharedPublications,
        sharedAffiliations: expect.any(Array),
        sharedWorks: expect.any(Array),
        trialOffset: expect.any(Number),
        congressOffset: expect.any(Number),
        publicationOffset: expect.any(Number)
      });
      expect(adaptedResponse.sharedWorks).toHaveLength(page.limit);
    });

    it("should return the array of shared works sorted in descending order by date", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );

      const numSharedAffiliations = faker.datatype.number(FAKE_NUMBER_MAX);
      const numSharedTrials = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });
      const numSharedCongresses = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });
      const numSharedPublications = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });
      const numSharedEnglishPublications = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });
      const numSharedChinesePublications = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });
      const numSharedJapanesePublications = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });

      const page: SharedWorksPage = {
        limit: faker.datatype.number({ min: 1, max: 10 }),
        trialOffset: 0,
        publicationOffset: 0,
        congressOffset: 0
      };

      const collaboratorId = faker.datatype.uuid();
      const mockResponse =
        generateMockElasticsearchResponse<CollaboratorDocument>(
          [
            generateMockCollaboratorHitForNetworkCollaboratorRequest(
              {
                _source: {
                  id: collaboratorId
                }
              },
              numSharedAffiliations,
              numSharedTrials,
              numSharedCongresses,
              numSharedPublications,
              numSharedEnglishPublications,
              numSharedChinesePublications,
              numSharedJapanesePublications
            )
          ],
          {}
        );

      const adaptedResponse =
        await networkResponseAdapterService.adaptToCollaborator(
          {
            personId: faker.datatype.uuid(),
            collaboratorId
          } as NetworkCollaboratorRequest,
          mockResponse.hits,
          page,
          ENGLISH
        );

      const totalNumPubs =
        numSharedPublications +
        numSharedEnglishPublications +
        numSharedJapanesePublications +
        numSharedChinesePublications;

      expect(adaptedResponse).toEqual({
        id: expect.any(String),
        personId: collaboratorId,
        totalSharedWorks: numSharedCongresses + numSharedTrials + totalNumPubs,
        totalSharedTrials: numSharedTrials,
        totalSharedCongresses: numSharedCongresses,
        totalSharedPublications: totalNumPubs,
        sharedAffiliations: expect.any(Array),
        sharedWorks: expect.any(Array),
        trialOffset: expect.any(Number),
        congressOffset: expect.any(Number),
        publicationOffset: expect.any(Number)
      });
      expect(adaptedResponse.sharedWorks).toStrictEqual(
        [...adaptedResponse.sharedWorks!].sort((sharedWorkA, sharedWorkB) => {
          return (
            Date.parse(sharedWorkB.date[0]) - Date.parse(sharedWorkA.date[0])
          );
        })
      );
    });

    it("should return the correct offset for each type depending on the number of shared works of that type being returned and the offsets requested", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );

      const numSharedAffiliations = faker.datatype.number(FAKE_NUMBER_MAX);
      const numSharedTrials = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });
      const numSharedCongresses = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });
      const numSharedPublications = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });
      const numSharedEnglishPublications = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });
      const numSharedChinesePublications = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });
      const numSharedJapanesePublications = faker.datatype.number({
        min: 1,
        max: FAKE_NUMBER_MAX
      });

      const page: SharedWorksPage = {
        limit: faker.datatype.number({ min: 1, max: 10 }),
        trialOffset: faker.datatype.number(),
        publicationOffset: faker.datatype.number(),
        congressOffset: faker.datatype.number()
      };

      const collaboratorId = faker.datatype.uuid();
      const mockResponse =
        generateMockElasticsearchResponse<CollaboratorDocument>(
          [
            generateMockCollaboratorHitForNetworkCollaboratorRequest(
              {
                _source: {
                  id: collaboratorId
                }
              },
              numSharedAffiliations,
              numSharedTrials,
              numSharedCongresses,
              numSharedPublications,
              numSharedEnglishPublications,
              numSharedChinesePublications,
              numSharedJapanesePublications
            )
          ],
          {}
        );

      const adaptedResponse =
        await networkResponseAdapterService.adaptToCollaborator(
          {
            personId: faker.datatype.uuid(),
            collaboratorId
          } as NetworkCollaboratorRequest,
          mockResponse.hits,
          page,
          ENGLISH
        );

      let numTrialsReturned = 0;
      let numCongressesReturned = 0;
      let numPublicationsReturned = 0;

      adaptedResponse.sharedWorks!.forEach(
        (sharedWork: SharedTrial | SharedCongress | SharedPublication) => {
          if ("organizer" in sharedWork) {
            numCongressesReturned++;
          } else if ("sponsor" in sharedWork) {
            numTrialsReturned++;
          } else {
            numPublicationsReturned++;
          }
        }
      );

      const totalNumPubs =
        numSharedPublications +
        numSharedEnglishPublications +
        numSharedJapanesePublications +
        numSharedChinesePublications;

      expect(adaptedResponse).toEqual({
        id: expect.any(String),
        personId: collaboratorId,
        totalSharedWorks: numSharedCongresses + numSharedTrials + totalNumPubs,
        totalSharedTrials: numSharedTrials,
        totalSharedCongresses: numSharedCongresses,
        totalSharedPublications: totalNumPubs,
        sharedAffiliations: expect.any(Array),
        sharedWorks: expect.any(Array),
        trialOffset: page.trialOffset + numTrialsReturned,
        congressOffset: page.congressOffset + numCongressesReturned,
        publicationOffset: page.publicationOffset + numPublicationsReturned
      });
    });

    describe("multi-lang", () => {
      it("should return values from jpn publication fields for publications with a jpn languageCode", async () => {
        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        const networkResponseAdapterService = new NetworkResponseAdapterService(
          locationLabelFormatterService
        );

        const numSharedAffiliations = 1;
        const numSharedCongresses = 1;
        const numSharedTrials = 1;
        const numSharedPublications = 0;
        const numSharedEnglishPublications = 0;
        const numSharedJapanesePublications = 1;
        const numSharedChinesePublications = 0;
        const mockHit =
          generateMockCollaboratorHitForNetworkCollaboratorRequest(
            {},
            numSharedAffiliations,
            numSharedTrials,
            numSharedCongresses,
            numSharedPublications,
            numSharedEnglishPublications,
            numSharedJapanesePublications,
            numSharedChinesePublications
          );
        const mockResponse =
          generateMockElasticsearchResponse<CollaboratorDocument>(
            [mockHit],
            {}
          );

        const mockAffiliation =
          mockHit.inner_hits!.sharedAffiliations.hits.hits[0]._source!;
        const collaboratorId = mockHit._source?.id;

        const adaptedResponse =
          await networkResponseAdapterService.adaptToCollaborator(
            {
              personId: faker.datatype.uuid(),
              collaboratorId
            } as NetworkCollaboratorRequest,
            mockResponse.hits,
            {
              limit: faker.datatype.number({ min: 3 }),
              trialOffset: 0,
              publicationOffset: 0,
              congressOffset: 0
            },
            ENGLISH
          );

        expect(adaptedResponse).toEqual({
          id: expect.any(String),
          personId: collaboratorId,
          totalSharedWorks:
            numSharedCongresses +
            numSharedTrials +
            numSharedJapanesePublications,
          totalSharedTrials: numSharedTrials,
          totalSharedCongresses: numSharedCongresses,
          totalSharedPublications: numSharedJapanesePublications,
          trialOffset: 1,
          congressOffset: 1,
          publicationOffset: 1,
          sharedAffiliations: [
            {
              id: mockAffiliation.institution.id,
              name: mockAffiliation.institution.nameTranslations.find(
                (translation: any) => translation.languageCode === ENGLISH
              ).name,
              affiliationType: mockAffiliation.type,
              isCurrent: mockAffiliation.isCurrent,
              ultimateParentId: mockAffiliation.institution.ultimateParentId
            }
          ],
          sharedWorks: expect.arrayContaining([
            {
              id: mockHit.inner_hits!.publications.hits.hits[0]._source!.id,
              title: [
                mockHit.inner_hits!.publications.hits.hits[0]._source!.title_jpn
              ],
              date: [
                mockHit.inner_hits!.publications.hits.hits[0]._source!
                  .datePublished
              ],
              persons:
                mockHit.inner_hits!.publications.hits.hits[0]._source!.persons.map(
                  (person: { id: string }) => {
                    return person.id;
                  }
                ),
              type: mockHit.inner_hits!.publications.hits.hits[0].fields![
                "publications.type_jpn"
              ],
              name: mockHit.inner_hits!.publications.hits.hits[0].fields![
                "publications.journalName_jpn"
              ]
            },
            {
              id: mockHit.inner_hits!.congress.hits.hits[0]._source!.id,
              title: [
                mockHit.inner_hits!.congress.hits.hits[0]._source!.title_eng
              ],
              date: [
                mockHit.inner_hits!.congress.hits.hits[0]._source!
                  .masterInitialDate
              ],
              persons:
                mockHit.inner_hits!.congress.hits.hits[0]._source!.persons.map(
                  (person: { id: string }) => {
                    return person.id;
                  }
                ),
              type: [mockHit.inner_hits!.congress.hits.hits[0]._source!.type],
              organizer:
                mockHit.inner_hits!.congress.hits.hits[0].fields![
                  "congress.organizer_eng"
                ],
              name: mockHit.inner_hits!.congress.hits.hits[0].fields![
                "congress.name_eng"
              ]
            },
            {
              id: mockHit.inner_hits!.trials.hits.hits[0]._source!.id,
              title: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!
                  .officialTitle_eng
              ],
              date: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.startDate
              ],
              persons:
                mockHit.inner_hits!.trials.hits.hits[0]._source!.persons.map(
                  (person: { id: string }) => {
                    return person.id;
                  }
                ),
              sponsor: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.sponsor_eng
              ],
              phase: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.phase_eng
              ],
              status: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.status_eng
              ]
            }
          ])
        });
      });

      it("should return values from cmn publication fields for publications with a cmn languageCode", async () => {
        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        const networkResponseAdapterService = new NetworkResponseAdapterService(
          locationLabelFormatterService
        );

        const numSharedAffiliations = 1;
        const numSharedCongresses = 1;
        const numSharedTrials = 1;
        const numSharedPublications = 0;
        const numSharedEnglishPublications = 0;
        const numSharedJapanesePublications = 0;
        const numSharedChinesePublications = 1;
        const mockHit =
          generateMockCollaboratorHitForNetworkCollaboratorRequest(
            {},
            numSharedAffiliations,
            numSharedTrials,
            numSharedCongresses,
            numSharedPublications,
            numSharedEnglishPublications,
            numSharedJapanesePublications,
            numSharedChinesePublications
          );
        const mockResponse =
          generateMockElasticsearchResponse<CollaboratorDocument>(
            [mockHit],
            {}
          );

        const mockAffiliation =
          mockHit.inner_hits!.sharedAffiliations.hits.hits[0]._source!;
        const collaboratorId = mockHit._source?.id;

        const adaptedResponse =
          await networkResponseAdapterService.adaptToCollaborator(
            {
              personId: faker.datatype.uuid(),
              collaboratorId
            } as NetworkCollaboratorRequest,
            mockResponse.hits,
            {
              limit: faker.datatype.number({ min: 3 }),
              trialOffset: 0,
              publicationOffset: 0,
              congressOffset: 0
            },
            ENGLISH
          );

        expect(adaptedResponse).toEqual({
          id: expect.any(String),
          personId: collaboratorId,
          totalSharedWorks:
            numSharedCongresses +
            numSharedTrials +
            numSharedChinesePublications,
          totalSharedTrials: numSharedTrials,
          totalSharedCongresses: numSharedCongresses,
          totalSharedPublications: numSharedChinesePublications,
          trialOffset: 1,
          congressOffset: 1,
          publicationOffset: 1,
          sharedAffiliations: [
            {
              id: mockAffiliation.institution.id,
              name: mockAffiliation.institution.nameTranslations.find(
                (translation: any) => translation.languageCode === ENGLISH
              ).name,
              affiliationType: mockAffiliation.type,
              isCurrent: mockAffiliation.isCurrent,
              ultimateParentId: mockAffiliation.institution.ultimateParentId
            }
          ],
          sharedWorks: expect.arrayContaining([
            {
              id: mockHit.inner_hits!.publications.hits.hits[0]._source!.id,
              title: [
                mockHit.inner_hits!.publications.hits.hits[0]._source!.title_cmn
              ],
              date: [
                mockHit.inner_hits!.publications.hits.hits[0]._source!
                  .datePublished
              ],
              persons:
                mockHit.inner_hits!.publications.hits.hits[0]._source!.persons.map(
                  (person: { id: string }) => {
                    return person.id;
                  }
                ),
              type: mockHit.inner_hits!.publications.hits.hits[0].fields![
                "publications.type_cmn"
              ],
              name: mockHit.inner_hits!.publications.hits.hits[0].fields![
                "publications.journalName_cmn"
              ]
            },
            {
              id: mockHit.inner_hits!.congress.hits.hits[0]._source!.id,
              title: [
                mockHit.inner_hits!.congress.hits.hits[0]._source!.title_eng
              ],
              date: [
                mockHit.inner_hits!.congress.hits.hits[0]._source!
                  .masterInitialDate
              ],
              persons:
                mockHit.inner_hits!.congress.hits.hits[0]._source!.persons.map(
                  (person: { id: string }) => {
                    return person.id;
                  }
                ),
              type: [mockHit.inner_hits!.congress.hits.hits[0]._source!.type],
              organizer:
                mockHit.inner_hits!.congress.hits.hits[0].fields![
                  "congress.organizer_eng"
                ],
              name: mockHit.inner_hits!.congress.hits.hits[0].fields![
                "congress.name_eng"
              ]
            },
            {
              id: mockHit.inner_hits!.trials.hits.hits[0]._source!.id,
              title: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!
                  .officialTitle_eng
              ],
              date: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.startDate
              ],
              persons:
                mockHit.inner_hits!.trials.hits.hits[0]._source!.persons.map(
                  (person: { id: string }) => {
                    return person.id;
                  }
                ),
              sponsor: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.sponsor_eng
              ],
              phase: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.phase_eng
              ],
              status: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.status_eng
              ]
            }
          ])
        });
      });

      it("should return values from eng publication fields for publications with an eng languageCode", async () => {
        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        const networkResponseAdapterService = new NetworkResponseAdapterService(
          locationLabelFormatterService
        );

        const numSharedAffiliations = 1;
        const numSharedCongresses = 1;
        const numSharedTrials = 1;
        const numSharedPublications = 0;
        const numSharedEnglishPublications = 1;
        const numSharedJapanesePublications = 0;
        const numSharedChinesePublications = 0;
        const mockHit =
          generateMockCollaboratorHitForNetworkCollaboratorRequest(
            {},
            numSharedAffiliations,
            numSharedTrials,
            numSharedCongresses,
            numSharedPublications,
            numSharedEnglishPublications,
            numSharedJapanesePublications,
            numSharedChinesePublications
          );
        const mockResponse =
          generateMockElasticsearchResponse<CollaboratorDocument>(
            [mockHit],
            {}
          );

        const mockAffiliation =
          mockHit.inner_hits!.sharedAffiliations.hits.hits[0]._source!;

        const collaboratorId = mockHit._source?.id;
        const adaptedResponse =
          await networkResponseAdapterService.adaptToCollaborator(
            {
              personId: faker.datatype.uuid(),
              collaboratorId
            } as NetworkCollaboratorRequest,
            mockResponse.hits,
            {
              limit: faker.datatype.number({ min: 3 }),
              trialOffset: 0,
              publicationOffset: 0,
              congressOffset: 0
            },
            ENGLISH
          );

        expect(adaptedResponse).toEqual({
          id: expect.any(String),
          personId: collaboratorId,
          totalSharedWorks:
            numSharedCongresses +
            numSharedTrials +
            numSharedEnglishPublications,
          totalSharedTrials: numSharedTrials,
          totalSharedCongresses: numSharedCongresses,
          totalSharedPublications: numSharedEnglishPublications,
          trialOffset: 1,
          congressOffset: 1,
          publicationOffset: 1,
          sharedAffiliations: [
            {
              id: mockAffiliation.institution.id,
              name: mockAffiliation.institution.nameTranslations.find(
                (translation: any) => translation.languageCode === ENGLISH
              ).name,
              affiliationType: mockAffiliation.type,
              isCurrent: mockAffiliation.isCurrent,
              ultimateParentId: mockAffiliation.institution.ultimateParentId
            }
          ],
          sharedWorks: expect.arrayContaining([
            {
              id: mockHit.inner_hits!.publications.hits.hits[0]._source!.id,
              title: [
                mockHit.inner_hits!.publications.hits.hits[0]._source!.title_eng
              ],
              date: [
                mockHit.inner_hits!.publications.hits.hits[0]._source!
                  .datePublished
              ],
              persons:
                mockHit.inner_hits!.publications.hits.hits[0]._source!.persons.map(
                  (person: { id: string }) => {
                    return person.id;
                  }
                ),
              type: mockHit.inner_hits!.publications.hits.hits[0].fields![
                "publications.type_eng"
              ],
              name: mockHit.inner_hits!.publications.hits.hits[0].fields![
                "publications.journalName_eng"
              ]
            },
            {
              id: mockHit.inner_hits!.congress.hits.hits[0]._source!.id,
              title: [
                mockHit.inner_hits!.congress.hits.hits[0]._source!.title_eng
              ],
              date: [
                mockHit.inner_hits!.congress.hits.hits[0]._source!
                  .masterInitialDate
              ],
              persons:
                mockHit.inner_hits!.congress.hits.hits[0]._source!.persons.map(
                  (person: { id: string }) => {
                    return person.id;
                  }
                ),
              type: [mockHit.inner_hits!.congress.hits.hits[0]._source!.type],
              organizer:
                mockHit.inner_hits!.congress.hits.hits[0].fields![
                  "congress.organizer_eng"
                ],
              name: mockHit.inner_hits!.congress.hits.hits[0].fields![
                "congress.name_eng"
              ]
            },
            {
              id: mockHit.inner_hits!.trials.hits.hits[0]._source!.id,
              title: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!
                  .officialTitle_eng
              ],
              date: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.startDate
              ],
              persons:
                mockHit.inner_hits!.trials.hits.hits[0]._source!.persons.map(
                  (person: { id: string }) => {
                    return person.id;
                  }
                ),
              sponsor: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.sponsor_eng
              ],
              phase: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.phase_eng
              ],
              status: [
                mockHit.inner_hits!.trials.hits.hits[0]._source!.status_eng
              ]
            }
          ])
        });
      });
    });
  });

  describe("Affiliations", () => {
    it("should adapt affiliations hits to a list of affiliation ids", async () => {
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const networkResponseAdapterService = new NetworkResponseAdapterService(
        locationLabelFormatterService
      );

      const mockHCP = generateMockHCPHit();
      const mockResponse = generateMockElasticsearchResponse([mockHCP]);

      const adaptedResponse =
        networkResponseAdapterService.adaptToAffiliationsList(
          mockResponse.hits
        );
      expect(adaptedResponse).toEqual([
        ...mockHCP._source!.affiliations!.map((affiliation) => {
          return affiliation.institution.id;
        })
      ]);
    });
  });

  describe("NetworkFilterAggregation", () => {
    describe("location filterFields", () => {
      it("COUNTRY: should expand bucket keys using country expander", () => {
        const expandedKey1 = faker.datatype.string();
        const expandedKey2 = faker.datatype.string();

        const bucketValues: Array<DocCountBucket> = [
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            people: {
              doc_count: faker.datatype.number()
            }
          },
          {
            key: faker.datatype.string(),
            doc_count: 0,
            people: {
              doc_count: 0
            }
          },
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            people: {
              doc_count: faker.datatype.number()
            }
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };

        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        locationLabelFormatterService.country.mockReturnValueOnce(expandedKey1);
        locationLabelFormatterService.country.mockReturnValueOnce(expandedKey2);

        const networkResponseAdapterService = new NetworkResponseAdapterService(
          locationLabelFormatterService
        );

        const filterField = NetworkFilterAutocompleteField.COUNTRY;

        const buckets = networkResponseAdapterService.adaptToFilterAggregations(
          aggregations,
          filterField
        );

        expect(buckets).toEqual([
          {
            id: expandedKey1,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0].people
                .doc_count
          },
          {
            id: expandedKey2,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2].people
                .doc_count
          }
        ]);

        expect(locationLabelFormatterService.city).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.region).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.postalCode).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.country).toHaveBeenCalled();
      });

      it("REGION: should expand bucket keys using region expander", () => {
        const expandedKey1 = faker.datatype.string();
        const expandedKey2 = faker.datatype.string();

        const bucketValues: Array<DocCountBucket> = [
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            people: {
              doc_count: faker.datatype.number()
            }
          },
          {
            key: faker.datatype.string(),
            doc_count: 0,
            people: {
              doc_count: 0
            }
          },
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            people: {
              doc_count: faker.datatype.number()
            }
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };

        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        locationLabelFormatterService.region.mockReturnValueOnce(expandedKey1);
        locationLabelFormatterService.region.mockReturnValueOnce(expandedKey2);

        const networkResponseAdapterService = new NetworkResponseAdapterService(
          locationLabelFormatterService
        );

        const filterField = NetworkFilterAutocompleteField.REGION;

        const buckets = networkResponseAdapterService.adaptToFilterAggregations(
          aggregations,
          filterField
        );

        expect(buckets).toEqual([
          {
            id: expandedKey1,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0].people
                .doc_count
          },
          {
            id: expandedKey2,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2].people
                .doc_count
          }
        ]);

        expect(locationLabelFormatterService.city).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.region).toHaveBeenCalled();
        expect(locationLabelFormatterService.postalCode).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.country).not.toHaveBeenCalled();
      });

      it("CITY: should expand bucket keys using city expander", () => {
        const expandedKey1 = faker.datatype.string();
        const expandedKey2 = faker.datatype.string();

        const bucketValues: Array<DocCountBucket> = [
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            people: {
              doc_count: faker.datatype.number()
            }
          },
          {
            key: faker.datatype.string(),
            doc_count: 0,
            people: {
              doc_count: 0
            }
          },
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            people: {
              doc_count: faker.datatype.number()
            }
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };

        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        locationLabelFormatterService.city.mockReturnValueOnce(expandedKey1);
        locationLabelFormatterService.city.mockReturnValueOnce(expandedKey2);

        const networkResponseAdapterService = new NetworkResponseAdapterService(
          locationLabelFormatterService
        );

        const filterField = NetworkFilterAutocompleteField.CITY;

        const buckets = networkResponseAdapterService.adaptToFilterAggregations(
          aggregations,
          filterField
        );

        expect(buckets).toEqual([
          {
            id: expandedKey1,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0].people
                .doc_count
          },
          {
            id: expandedKey2,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2].people
                .doc_count
          }
        ]);

        expect(locationLabelFormatterService.city).toHaveBeenCalled();
        expect(locationLabelFormatterService.region).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.postalCode).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.country).not.toHaveBeenCalled();
      });

      it("POSTAL_CODE: should expand bucket keys using postalCode expander", () => {
        const expandedKey1 = faker.datatype.string();
        const expandedKey2 = faker.datatype.string();

        const bucketValues: Array<DocCountBucket> = [
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            people: {
              doc_count: faker.datatype.number()
            }
          },
          {
            key: faker.datatype.string(),
            doc_count: 0,
            people: {
              doc_count: 0
            }
          },
          {
            key: faker.datatype.string(),
            doc_count: faker.datatype.number(),
            people: {
              doc_count: faker.datatype.number()
            }
          }
        ];
        const aggregations = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates(bucketValues)
            }
          }
        };

        const locationLabelFormatterService = createMockInstance(
          LocationLabelFormatterService
        );
        locationLabelFormatterService.postalCode.mockReturnValueOnce(
          expandedKey1
        );
        locationLabelFormatterService.postalCode.mockReturnValueOnce(
          expandedKey2
        );

        const networkResponseAdapterService = new NetworkResponseAdapterService(
          locationLabelFormatterService
        );

        const filterField = NetworkFilterAutocompleteField.POSTAL_CODE;

        const buckets = networkResponseAdapterService.adaptToFilterAggregations(
          aggregations,
          filterField
        );

        expect(buckets).toEqual([
          {
            id: expandedKey1,
            count:
              aggregations.nested.filtered_matching.matching.buckets[0].people
                .doc_count
          },
          {
            id: expandedKey2,
            count:
              aggregations.nested.filtered_matching.matching.buckets[2].people
                .doc_count
          }
        ]);

        expect(locationLabelFormatterService.city).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.region).not.toHaveBeenCalled();
        expect(locationLabelFormatterService.postalCode).toHaveBeenCalled();
        expect(locationLabelFormatterService.country).not.toHaveBeenCalled();
      });
    });
  });
});
