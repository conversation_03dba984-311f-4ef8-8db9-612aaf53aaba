import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";

import { DiversityResourceService } from "./DiversityResourceService";
import { ElasticSearchService } from "./ElasticSearchService";

const mockData = {
  hits: {
    hits: [
      {
        _index: "people",
        _type: "_doc",
        version: "1",
        _seq_no: 52924,
        _primary_term: 1,
        found: true,
        _source: {
          id: "2087109",
          patientsDiversityCount: 553,
          patientsDiversity: [
            {
              sex: [
                {
                  count: 4810,
                  value: "male"
                },
                {
                  count: 9699,
                  value: "female"
                }
              ],
              races_eng: [
                {
                  race: "Hispanic",
                  count: 117
                },
                {
                  race: "Black,Non-Hispanic",
                  count: 244
                },
                {
                  race: "Asian Pacific Islander",
                  count: 22
                },
                {
                  race: "White,Non-Hispanic",
                  count: 158
                },
                {
                  race: "Not Identified",
                  count: 15
                }
              ],
              age: [
                {
                  count: 1127,
                  range: "age_0_18"
                },
                {
                  count: 5201,
                  range: "age_19_39"
                },
                {
                  count: 4116,
                  range: "age_40_59"
                },
                {
                  count: 3475,
                  range: "age_60_79"
                },
                {
                  count: 554,
                  range: "age_80_plus"
                }
              ]
            }
          ],
          firstName_eng: "Sylvia",
          providerDiversity: [
            {
              sex: ["Female"],
              races_eng: ["Asian"]
            }
          ],
          lastName_eng: "Hsu"
        }
      },
      {
        _index: "people",
        _type: "_doc",
        version: "1",
        _seq_no: 52924,
        _primary_term: 1,
        found: true,
        _source: {
          id: "288633",
          patientsDiversityCount: 207,
          patientsDiversity: [
            {
              sex: [
                {
                  count: 97,
                  value: "male"
                },
                {
                  count: 33,
                  value: "female"
                }
              ],
              races_eng: [
                {
                  race: "Black,Non-Hispanic",
                  count: 46
                },
                {
                  race: "White,Non-Hispanic",
                  count: 157
                }
              ],
              age: [
                {
                  count: 4,
                  range: "age_19_39"
                },
                {
                  count: 19,
                  range: "age_40_59"
                },
                {
                  count: 87,
                  range: "age_60_79"
                },
                {
                  count: 20,
                  range: "age_80_plus"
                }
              ]
            }
          ],
          firstName_eng: "Tian",
          providerDiversity: [
            {
              sex: ["Female"],
              races_eng: ["Asian"]
            }
          ],
          lastName_eng: "Zhang"
        }
      },
      {
        _index: "people",
        _type: "_doc",
        version: "1",
        _seq_no: 52924,
        _primary_term: 1,
        found: true,
        _source: {
          id: "234831",
          patientsDiversityCount: 365,
          patientsDiversity: [
            {
              sex: [
                {
                  count: 338,
                  value: "male"
                },
                {
                  count: 297,
                  value: "female"
                }
              ],
              races_eng: [
                {
                  race: "Hispanic",
                  count: 20
                },
                {
                  race: "Black,Non-Hispanic",
                  count: 18
                },
                {
                  race: "Asian Pacific Islander",
                  count: 14
                },
                {
                  race: "White,Non-Hispanic",
                  count: 302
                }
              ],
              age: [
                {
                  count: 18,
                  range: "age_0_18"
                },
                {
                  count: 97,
                  range: "age_19_39"
                },
                {
                  count: 106,
                  range: "age_40_59"
                },
                {
                  count: 312,
                  range: "age_60_79"
                },
                {
                  count: 102,
                  range: "age_80_plus"
                }
              ]
            }
          ],
          firstName_eng: "Joohi",
          providerDiversity: [
            {
              sex: ["Female"],
              races_eng: ["Asian"]
            }
          ],
          lastName_eng: "Jimenez-shahed"
        }
      }
    ]
  }
};

const missingDiversityData = {
  hits: {
    hits: [
      {
        _index: "people",
        _type: "_doc",
        version: "1",
        _seq_no: 52924,
        _primary_term: 1,
        found: true,
        _source: {
          id: "12345",
          random: "not needed data"
        }
      }
    ]
  }
};

const emptyResponse = {
  docs: [{ _index: "people", _type: "_doc", _id: "asdfssss", found: false }]
};

describe.skip("getDiversity()", () => {
  it("returns empty array when no Diversity are returned", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue([
      emptyResponse
    ]);

    const diversityResourceService = new DiversityResourceService(
      configService,
      elasticsearchService
    );

    const resp = await diversityResourceService.getDiversity(["asdfssss"]);

    expect(resp).toEqual([]);
  });

  it("returns empty array when data returns undefined", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(undefined);

    const diversityResourceService = new DiversityResourceService(
      configService,
      elasticsearchService
    );

    const resp = await diversityResourceService.getDiversity(["123456"]);

    expect(resp).toEqual([]);
  });

  it("returns default values if any of the data is empty or missing", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(
      missingDiversityData
    );

    const diversityResourceService = new DiversityResourceService(
      configService,
      elasticsearchService
    );

    const resp = await diversityResourceService.getDiversity(["12345"]);

    expect(resp[0]).toEqual(
      expect.objectContaining({
        id: "12345",
        firstName: "",
        lastName: "",
        patientsDiversityCount: 0,
        patientDiversity: {
          races: [],
          age: [],
          sex: []
        },
        providerDiversity: {
          races: [],
          languagesSpoken: [],
          sex: []
        }
      })
    );
  });

  it("should return the appropriate data when valid IDs are used", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.getSignedElasticRequest.mockResolvedValue(mockData);

    const diversityResourceService = new DiversityResourceService(
      configService,
      elasticsearchService
    );

    const peopleIds = ["288633", "234831", "2087109"];
    const resp = await diversityResourceService.getDiversity(peopleIds);
    expect(elasticsearchService.query).toHaveBeenCalled();

    expect(resp[1]).toEqual(
      expect.objectContaining({
        id: "288633",
        firstName: "Tian",
        lastName: "Zhang",
        patientsDiversityCount: 207,
        patientDiversity: {
          races: [
            {
              count: 46,
              race: "Black,Non-Hispanic"
            },
            {
              count: 157,
              race: "White,Non-Hispanic"
            }
          ],
          age: [
            {
              count: 4,
              range: "age_19_39"
            },
            {
              count: 19,
              range: "age_40_59"
            },
            {
              count: 87,
              range: "age_60_79"
            },
            {
              count: 20,
              range: "age_80_plus"
            }
          ],
          sex: [
            {
              value: "male",
              count: 97
            },
            {
              value: "female",
              count: 33
            }
          ]
        },
        providerDiversity: {
          races: ["Asian"],
          languagesSpoken: [],
          sex: ["Female"]
        }
      })
    );

    expect(resp[0]).toEqual(
      expect.objectContaining({
        id: "2087109",
        firstName: "Sylvia",
        lastName: "Hsu",
        patientsDiversityCount: 553,
        patientDiversity: {
          races: [
            {
              count: 117,
              race: "Hispanic"
            },
            {
              count: 244,
              race: "Black,Non-Hispanic"
            },
            {
              count: 22,
              race: "Asian Pacific Islander"
            },
            {
              count: 158,
              race: "White,Non-Hispanic"
            },
            {
              count: 15,
              race: "Not Identified"
            }
          ],
          age: [
            {
              count: 1127,
              range: "age_0_18"
            },
            {
              count: 5201,
              range: "age_19_39"
            },
            {
              count: 4116,
              range: "age_40_59"
            },
            {
              count: 3475,
              range: "age_60_79"
            },
            {
              count: 554,
              range: "age_80_plus"
            }
          ],
          sex: [
            {
              value: "male",
              count: 4810
            },
            {
              value: "female",
              count: 9699
            }
          ]
        },
        providerDiversity: {
          races: ["Asian"],
          languagesSpoken: [],
          sex: ["Female"]
        }
      })
    );

    expect(resp[2]).toEqual(
      expect.objectContaining({
        id: "234831",
        firstName: "Joohi",
        lastName: "Jimenez-shahed",
        patientsDiversityCount: 365,
        patientDiversity: {
          races: [
            {
              count: 20,
              race: "Hispanic"
            },
            {
              count: 18,
              race: "Black,Non-Hispanic"
            },
            {
              count: 14,
              race: "Asian Pacific Islander"
            },
            {
              count: 302,
              race: "White,Non-Hispanic"
            }
          ],
          age: [
            {
              count: 18,
              range: "age_0_18"
            },
            {
              count: 97,
              range: "age_19_39"
            },
            {
              count: 106,
              range: "age_40_59"
            },
            {
              count: 312,
              range: "age_60_79"
            },
            {
              count: 102,
              range: "age_80_plus"
            }
          ],
          sex: [
            {
              value: "male",
              count: 338
            },
            {
              value: "female",
              count: 297
            }
          ]
        },
        providerDiversity: {
          races: ["Asian"],
          languagesSpoken: [],
          sex: ["Female"]
        }
      })
    );
  });
});
