import {
  QueryDslQueryContainer,
  SearchRequest,
  SearchResponse
} from "@elastic/elasticsearch/lib/api/types";
import {
  AppName,
  SavedTerritoryResourceClient,
  UserResourceClient
} from "@h1nyc/account-sdk";
import {
  createEmptyFilters,
  FOPersonSearchResponse,
  NameSearchRewriteResource,
  PersonSearchResponse,
  RPC_NAMESPACE_NAME_SEARCH_REWRITE
} from "@h1nyc/search-sdk";
import { NameSearchInput } from "@h1nyc/search-sdk/dist/interfaces/NameSearchInput";

import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import _ from "lodash";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  HCPDocument,
  MAXIMUM_QUERY_TOKENS
} from "./KeywordSearchResourceServiceRewrite";
import {
  CHINESE,
  ENGLISH,
  JAPANESE,
  Language,
  LanguageDetectService
} from "./LanguageDetectService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { Service } from "typedi";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { getQueryIntents } from "../lib/query_understanding/QueryUnderstandingResponseAnalyzer";
import { KeywordFilterClauseBuilderService } from "./KeywordFilterClauseBuilderService";
import NameSearchBuilderFactory from "./queryBuilders/NameSearchBuilderFactory";
import CalculateMinimumNameVariations from "./queryBuilders/CalculateMinimumNameVariations";
import {
  DEFAULT_NAME_VARIATION_MATCH_COUNT,
  getProjectIDFilter
} from "./queryBuilders/DefaultNameSearchBuilder";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";
import { NameSearchResponseAdapterService } from "./NameSearchResponseAdapterService";
import { coerceStringLanguageToTypeLanguage } from "./KeywordAutocompleteResourceService";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { UserOnboardingDataService } from "./UserOnboardingDataService";
import { getHitsTotal } from "./NameSearchHighConfPersonAdapterService";
import {
  EMPTY_FILTER_COUNTS,
  EMPTY_RANGES,
  ZERO_MIN_MAX
} from "./KeywordSearchResponseAdapterService";

export type NameSearchFeatureFlags = {
  enableHighConfHCPFeature: boolean;
  enableQueryIntent: boolean;
  enableIntentBasedSearchQuery: boolean;
  enableNameSearchPersonalisation: boolean;
  enableCTMSV2: boolean;
  enableResultsOutsideUsersSlice: boolean;
  enableTagsInElasticsearch: boolean;
  enableUniquePatientCountForClaims: boolean;
  enableBrazilianClaims: boolean;
  disableUniquePatientCountForOnlyProcedures: boolean;
  enableNestedIndicationFilter: boolean;
  enableCcsrExclusionForMatchedCounts: boolean;
  enableLocationFilterRegionRollup: boolean;
  enableNewGlobalLeaderTier: boolean;
  enableExUSGlobalLeader: boolean;
  enableCountrySpecificNonIndicationLeaderFilters: boolean;
};

export const nameSearchFeatureFlagTypes = [
  "enableHighConfHCPFeature",
  "enableQueryIntent",
  "enableIntentBasedSearchQuery",
  "enableNameSearchPersonalisation",
  "enableCTMSV2",
  "enableResultsOutsideUsersSlice",
  "enableTagsInElasticsearch",
  "enableUniquePatientCountForClaims",
  "enableBrazilianClaims",
  "disableUniquePatientCountForOnlyProcedures",
  "enableNestedIndicationFilter",
  "enableCcsrExclusionForMatchedCounts",
  "enableLocationFilterRegionRollup",
  "enableNewGlobalLeaderTier",
  "enableExUSGlobalLeader",
  "enableCountrySpecificNonIndicationLeaderFilters"
] as const;
export type NameSearchFeatureFlag = (typeof nameSearchFeatureFlagTypes)[number];

export const featureFlagDefaults: Readonly<
  Record<keyof NameSearchFeatureFlags, { key: string; default: boolean }>
> = {
  enableHighConfHCPFeature: {
    key: "search.enable-high-conf-hcp",
    default: false
  },
  enableQueryIntent: {
    key: "search.enable-query-intent",
    default: false
  },
  enableIntentBasedSearchQuery: {
    key: "intent-based-search-query",
    default: false
  },
  enableNameSearchPersonalisation: {
    key: "search.enable-name-search-personalisation",
    default: false
  },
  enableCTMSV2: {
    key: "enable-ctms-v2",
    default: false
  },
  enableTagsInElasticsearch: {
    key: "search.enable-tags-in-elasticsearch",
    default: false
  },
  enableResultsOutsideUsersSlice: {
    key: "enable-results-outside-users-slice",
    default: false
  },
  enableUniquePatientCountForClaims: {
    key: "search.enable-unique-patient-count-for-claims",
    default: false
  },
  enableBrazilianClaims: {
    key: "enable-brazilian-claims",
    default: false
  },
  disableUniquePatientCountForOnlyProcedures: {
    key: "search.disable-unique-patient-count-for-only-procedures",
    default: false
  },
  enableNestedIndicationFilter: {
    key: "search.enable-nested-indication-filter",
    default: false
  },
  enableCcsrExclusionForMatchedCounts: {
    key: "enable-ccsr-exclusion-for-matched-counts",
    default: false
  },
  enableLocationFilterRegionRollup: {
    key: "enable-location-filter-region-rollup",
    default: false
  },
  enableNewGlobalLeaderTier: {
    key: "enable-new-global-leader-tier",
    default: false
  },
  enableExUSGlobalLeader: {
    key: "enable-ex-us-global-leader",
    default: false
  },
  enableCountrySpecificNonIndicationLeaderFilters: {
    key: "enable-country-specific-non-indication-leader",
    default: false
  }
};

const HAS_ADVANCED_OPERATORS = /(\sAND\s|\sOR\s|NOT\s)/;
const DEFAULT_PROJECT_ID = "1";
const SEARCH_TYPE = "dfs_query_then_fetch";

const FO_SOURCE_FIELDS: Readonly<Array<string>> = [
  "firstName_eng",
  "firstName_cmn",
  "firstName_jpn",
  "lastName_eng",
  "lastName_cmn",
  "lastName_jpn",
  "middleName_eng",
  "middleName_cmn",
  "middleName_jpn",
  "id",
  "orcidId",
  "designations",
  "specialty_eng",
  "specialty_cmn",
  "specialty_jpn",
  "emails",
  "locations"
];

@Service()
@RpcService()
export class NameSearchResourceServiceRewrite
  extends RpcResourceService
  implements NameSearchRewriteResource
{
  private readonly logger = createLogger(this);
  private peopleIndex: string;
  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService,
    private languageDetectService: LanguageDetectService,
    private userClient: UserResourceClient,
    private featureFlagsService: FeatureFlagsService,
    private keywordFilterClauseBuilderService: KeywordFilterClauseBuilderService,
    private nameSearchBuilderFactory: NameSearchBuilderFactory,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient,
    private calculateMinimumNameVariations: CalculateMinimumNameVariations,
    private searchAnalyticsTracerService: SearchAnalyticsTracerService,
    private nameSearchResponseAdapterService: NameSearchResponseAdapterService,
    private userOnboardingDataService: UserOnboardingDataService,
    private savedTerritoryService: SavedTerritoryResourceClient
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_NAME_SEARCH_REWRITE,
      config.searchRedisOptions
    );
    this.peopleIndex = config.elasticPeopleIndex;
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  private truncateQuery(query?: string) {
    if (query) {
      return query.trim().split(/\s+/).slice(0, MAXIMUM_QUERY_TOKENS).join(" ");
    }
    return query;
  }

  private sanitizeQuery(query?: string) {
    return query?.replace(/[´]/g, "'");
  }

  @RpcMethod()
  @Trace("h1-search.name.rewrite.query")
  async runNameSearchRewrite(
    input: Readonly<NameSearchInput>
  ): Promise<PersonSearchResponse> {
    const { userId, projectId, projectFeatures, page } = input;
    const { from, size } = page;

    if (HAS_ADVANCED_OPERATORS.test(input.query || "")) {
      this.logger.info(
        { query: input.query },
        "Query has advanced operators. Skipping name search..."
      );
      return {
        total: 0,
        from,
        normalizedRange: { ...ZERO_MIN_MAX },
        pageSize: page.size,
        ranges: EMPTY_RANGES,
        filterCounts: EMPTY_FILTER_COUNTS,
        queryIntent: [],
        results: []
      };
    }

    const query = this.sanitizeQuery(this.truncateQuery(input.query)) || "";

    const { searchMultiLanguage } = projectFeatures;
    const nameSearchFeatureFlags = await this.getFeatureFlagValues(input);
    const {
      enableQueryIntent,
      enableHighConfHCPFeature,
      enableResultsOutsideUsersSlice
    } = nameSearchFeatureFlags;

    let queryLang = ENGLISH;
    // TODO : searchMultiLanguage check should be removed from name search flow
    const userLanguage = await this.getUsersPreferredLanguage(input);
    const languageDetector =
      this.languageDetectService.getLanguageDetector(userLanguage);
    if (searchMultiLanguage) {
      if (!query) {
        queryLang = userLanguage;
      } else {
        queryLang = languageDetector(query);
      }
    }
    const useCmnAndJpnFields =
      this.languageDetectService.shouldUseBothCmnAndJpnFields(
        queryLang as Language,
        userLanguage
      );
    const onboardingData =
      await this.userOnboardingDataService.getOnboardingData(userId, projectId);

    const nameSearchBuilder =
      this.nameSearchBuilderFactory.getNameSearchBuilder(queryLang as Language);
    const territoryIds = input.suppliedFilters?.territory?.values;
    const territories = territoryIds?.length
      ? await this.savedTerritoryService.getByIds(territoryIds)
      : [];
    const filters: QueryDslQueryContainer[] =
      await this.keywordFilterClauseBuilderService.buildForSearchRequest(
        input,
        languageDetector,
        nameSearchFeatureFlags,
        undefined,
        {
          skipProjectIdFilter: true
        },
        territories
      );
    const congressFilters: QueryDslQueryContainer[] =
      await this.keywordFilterClauseBuilderService.buildForSearchRequest(
        {
          ...input,
          suppliedFilters: {
            ...createEmptyFilters(projectId).filters,
            congresses: {
              ...input.suppliedFilters.congresses
            }
          }
        },
        languageDetector,
        nameSearchFeatureFlags,
        undefined,
        {
          skipProjectIdFilter: true
        },
        territories
      );

    const filtersWithProjectSlicing = filters.concat(
      getProjectIDFilter(projectId)
    );

    const filtersWithoutProjectSlicing = filters.concat(
      this.getProjectIdFilterClauseToRemoveSlicing(projectId)
    );

    let searchRequest: SearchRequest;

    let queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined;

    if (query.length === 0) {
      searchRequest = nameSearchBuilder.createNameSearchBodyNoKeyWord({
        from,
        size,
        filter: filtersWithProjectSlicing,
        language: queryLang as Language,
        featureFlags: nameSearchFeatureFlags,
        useCmnAndJpnFields
      });
    } else {
      // Find number of name variations to match for first/middle/last name (not applicable for cmn and jpn)
      const numberOfNameVariationsToMatch =
        queryLang === ENGLISH
          ? await this.calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
              this.elasticService,
              this.peopleIndex,
              query
            )
          : DEFAULT_NAME_VARIATION_MATCH_COUNT;
      queryUnderstandingServiceResponse =
        await this.queryUnderstandingServiceClient.analyze(
          query,
          queryLang as Language
        );
      searchRequest = nameSearchBuilder.createNameSearchBody({
        query,
        from,
        size,
        numberOfNameVariationsToMatch,
        filter: enableResultsOutsideUsersSlice
          ? filtersWithoutProjectSlicing
          : filtersWithProjectSlicing,
        language: queryLang as Language,
        featureFlags: nameSearchFeatureFlags,
        onboardingData,
        queryUnderstandingServiceResponse,
        projectId,
        useCmnAndJpnFields,
        useCongressContributorRanking: input.useCongressContributorRanking,
        congressFilters
      });
    }

    searchRequest.index = this.peopleIndex;
    searchRequest.search_type = SEARCH_TYPE;

    this.logger.info(
      { data: searchRequest },
      `Generated NAME query for project ${projectId} with 
      query ${query}`
    );

    const response: SearchResponse<HCPDocument> =
      await this.elasticService.query<HCPDocument>(searchRequest);

    const { hits } = response;

    const queryIntents = enableQueryIntent
      ? getQueryIntents(query, queryUnderstandingServiceResponse)
      : [];

    const res: PersonSearchResponse =
      this.nameSearchResponseAdapterService.adapt(
        queryLang,
        page,
        hits,
        queryIntents,
        projectFeatures,
        enableHighConfHCPFeature,
        nameSearchFeatureFlags,
        input,
        useCmnAndJpnFields
      );

    if (res.total === 0) {
      this.searchAnalyticsTracerService.sendAnalyticsEvent({
        event: "zero.name_search.results",
        properties: {
          query,
          projectId,
          input,
          queryIntents: queryIntents,
          language: queryLang
        },
        timestamp: new Date(),
        userId
      });
    }

    const data: Record<string, any> = {};
    data.nameSearchResultCount = res.total;
    data.nameSearchquery = input.query;
    data.nameSearchInput = input;
    data.nameSearchResults = hits.hits.map((hit) => {
      const name = [
        hit._source?.firstName_eng,
        hit._source?.middleName_eng,
        hit._source?.lastName_eng
      ]
        .filter((part) => !!part)
        .join(" ");

      return {
        _id: hit._id,
        id: hit._source?.id,
        score: hit._score,
        name
      };
    });
    this.logger.info({ data }, "Name Search Results");

    return res;
  }

  @RpcMethod()
  @Trace("h1-search.name.FOQuery")
  async runFONameSearch(
    input: Readonly<NameSearchInput>
  ): Promise<FOPersonSearchResponse> {
    let queryLang = ENGLISH;

    const languageDetector = this.languageDetectService.getLanguageDetector();
    if (input.lastName) {
      queryLang = languageDetector(input.lastName);
    } else if (input.firstName) {
      queryLang = languageDetector(input.firstName);
    }
    const nameSearchFeatureFlags = await this.getFeatureFlagValues(input);
    const territoryIds = input.suppliedFilters?.territory?.values;
    const territories = territoryIds?.length
      ? await this.savedTerritoryService.getByIds(territoryIds)
      : [];
    const filters: QueryDslQueryContainer[] =
      await this.keywordFilterClauseBuilderService.buildForSearchRequest(
        input,
        languageDetector,
        nameSearchFeatureFlags,
        undefined,
        undefined,
        territories
      );

    const { firstName, middleName, lastName, email, hcpId } = input;
    const isNameFieldPresent = !!(firstName || middleName || lastName);

    let response: SearchResponse<HCPDocument>;
    const isEmailOrHcpIdPresent = !!(email || hcpId);
    if (isEmailOrHcpIdPresent && isNameFieldPresent) {
      const combinedResponse: [
        SearchResponse<HCPDocument>,
        SearchResponse<HCPDocument>
      ] = await Promise.all([
        this.elasticService.query<HCPDocument>(
          this.getFoEmailOrHcpIdSearchRequest(filters, email, hcpId)
        ),
        this.elasticService.query<HCPDocument>(
          this.getFoNameSearchRequest(input, filters)
        )
      ]);
      const emailAndHcpIdResponse: SearchResponse<HCPDocument> =
        combinedResponse[0];
      const nameQueryResponse: SearchResponse<HCPDocument> =
        combinedResponse[1];

      if (getHitsTotal(emailAndHcpIdResponse.hits.total ?? 0)) {
        response = emailAndHcpIdResponse;
      } else {
        response = nameQueryResponse;
      }
    } else if (isEmailOrHcpIdPresent) {
      response = await this.elasticService.query<HCPDocument>(
        this.getFoEmailOrHcpIdSearchRequest(filters, email, hcpId)
      );
    } else {
      response = await this.elasticService.query<HCPDocument>(
        this.getFoNameSearchRequest(input, filters)
      );
    }

    const { hits } = response;

    const res: FOPersonSearchResponse =
      this.nameSearchResponseAdapterService.adaptFOResponse(
        queryLang,
        input.page,
        hits
      );

    return res;
  }

  private getFoNameSearchRequest(
    input: NameSearchInput,
    filters: QueryDslQueryContainer[]
  ) {
    const foNameQuery: SearchRequest = {
      index: this.peopleIndex,
      search_type: "query_then_fetch",
      query: this.nameSearchBuilderFactory
        .getFoNameSearchBuilder()
        .getNameQuery(input, filters),
      _source_includes: [...FO_SOURCE_FIELDS],
      from: input.page.from,
      size: input.page.size
    };
    return foNameQuery;
  }

  private getFoEmailOrHcpIdSearchRequest(
    filters: QueryDslQueryContainer[],
    email?: string,
    hcpId?: string
  ) {
    const foEmailQuery: SearchRequest = {
      index: this.peopleIndex,
      search_type: "query_then_fetch",
      query: this.nameSearchBuilderFactory
        .getFoNameSearchBuilder()
        .getEmailOrHcpIdQuery(filters, email, hcpId),
      _source_includes: [...FO_SOURCE_FIELDS]
    };
    return foEmailQuery;
  }

  @Trace("h1-search.name.search.getUsersPreferredLanguage")
  private async getUsersPreferredLanguage({
    userId = "",
    projectId,
    language
  }: NameSearchInput): Promise<Language> {
    const suppliedLanguage = coerceStringLanguageToTypeLanguage(language);

    if (suppliedLanguage) {
      return suppliedLanguage;
    }

    try {
      const usersPreferredLanguage =
        await this.userClient.getUsersPreferredLanguage(userId, projectId);
      this.logger.debug(
        `users preferred language: ${usersPreferredLanguage?.language}`
      );

      switch (usersPreferredLanguage?.language) {
        case "japanese":
        case JAPANESE:
          return JAPANESE;
        case "chinese_simplified":
        case "chinese_traditional":
        case CHINESE:
          return CHINESE;
        case "english":
        case ENGLISH:
        default:
          return ENGLISH;
      }
    } catch (err) {
      this.logger.error(
        { err },
        "Error thrown fetching users preferred language"
      );
      return ENGLISH;
    }
  }

  private getProjectIdFilterClauseToRemoveSlicing(
    projectId: string
  ): QueryDslQueryContainer {
    return {
      bool: {
        must: {
          terms: {
            projectIds: [DEFAULT_PROJECT_ID, projectId]
          }
        },
        must_not: {
          term: {
            otherProjectsPersonIn: projectId
          }
        }
      }
    };
  }

  private async getFeatureFlagValues({
    userId,
    projectId,
    appName
  }: {
    userId?: string;
    projectId: string;
    appName?: AppName;
  }): Promise<NameSearchFeatureFlags> {
    const featureFlags: Partial<NameSearchFeatureFlags> = {};
    const user = userId ? { userId, projectId, appName } : undefined;
    const flagsState = await this.featureFlagsService.getAllFlags(user);

    for (const flag of nameSearchFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as NameSearchFeatureFlags;
  }
}
