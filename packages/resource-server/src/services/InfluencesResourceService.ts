import {
  InfluencesResource,
  RPC_NAMESPACE_INFLUENCES,
  CitationProfileResponse,
  CitationProfile,
  CitationProfilePublication
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";

import { CitationProfileQueryBuilder } from "../lib/CitationProfile/CitationProfileBuilder";
import { Service } from "typedi";
import {
  SearchHit,
  MsearchMultisearchBody,
  MsearchMultisearchHeader,
  MsearchRequest,
  QueryDslQueryContainer,
  SearchRequest,
  SearchResponse,
  MsearchMultiSearchItem,
  ErrorResponseBase
} from "@elastic/elasticsearch/lib/api/types";
import _ from "lodash";
import {
  CHINESE,
  JAPANESE,
  Language,
  LanguageDetectService
} from "./LanguageDetectService";
import { HCPNotFoundError } from "./errors/HCPNotFoundError";
import { QueryParserService } from "./QueryParserService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { estypes } from "@elastic/elasticsearch";

const NO_NAME_TRANSLATION_AVAILABLE = "";
const NO_JOURNAL_NAME = "";
const NO_PUBLICATION_TITLE = "";
const NO_PUBLICATION_ID = "";
const HEADER: Readonly<MsearchMultisearchHeader> = {};
const FIRST_ELEMENT = 0;

function isErrorResponse(
  response: Readonly<estypes.MsearchResponseItem>
): response is ErrorResponseBase {
  return !!(response as ErrorResponseBase).error;
}

export type ScopeDocument = {
  name_eng: string;
  name_jpn: string;
  name_cmn: string;
  institutions: Array<string>;
};

export type CitationDocument = {
  id: string;
  institutions: Array<string>;
  name_eng: string;
  name_jpn: string;
  name_cmn: string;
  publicationCount: number;
  citationTotal: number;
  trialCount: number;
  congressCount: number;
  paymentTotal: number;
};

function getHCPScopeQuery(index: string, personId: string): SearchRequest {
  return {
    index,
    _source_includes: ["name_eng", "name_jpn", "name_cmn", "institutions"],
    query: {
      term: {
        id: personId
      }
    }
  };
}

function getHCPScopeProperties(response: SearchResponse<ScopeDocument>) {
  if (_.isEmpty(response.hits.hits)) {
    return null;
  }

  const firstHit = response.hits.hits[0];

  return {
    name_eng: firstHit._source?.name_eng || "",
    name_jpn: firstHit._source?.name_jpn || "",
    name_cmn: firstHit._source?.name_cmn || "",
    institutions: firstHit._source?.institutions || []
  };
}

@Service()
@RpcService()
export class InfluencesResourceService
  extends RpcResourceService
  implements InfluencesResource
{
  private readonly logger = createLogger(this);
  private peopleIndex: string;

  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService,
    private citationProfileQueryBuilder: CitationProfileQueryBuilder,
    private languageDetectService: LanguageDetectService,
    private queryParserService: QueryParserService,
    private parsedQueryTreeToElasticsearchQueriesService: ParsedQueryTreeToElasticsearchQueriesService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_INFLUENCES,
      config.searchRedisOptions
    );

    this.peopleIndex = config.elasticPeopleIndex;
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.influences.citation-profile")
  async citationProfile(
    personId: string,
    projectId: string,
    dateRange: {
      min: number;
      max: number | null;
    },
    suppliedTerms?: Array<string>,
    langCode = "eng"
  ): Promise<CitationProfileResponse> {
    const hcpScopeQuery = getHCPScopeQuery(this.peopleIndex, personId);

    this.logger.debug(
      { data: hcpScopeQuery },
      "Performing citation profile scope query"
    );

    const hcpScope = getHCPScopeProperties(
      await this.elasticService.query<ScopeDocument>(hcpScopeQuery)
    );

    if (!hcpScope) {
      throw new HCPNotFoundError(personId);
    }

    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      suppliedTerms,
      langCode as Language
    );

    const citedPapersQuery =
      this.citationProfileQueryBuilder.buildCitedByPapersQuery(
        personId,
        dateRange,
        true,
        projectId,
        terms
      );

    this.logger.debug(
      { data: citedPapersQuery },
      "Performing citation profile cited query"
    );

    const citedByPapersQuery =
      this.citationProfileQueryBuilder.buildCitedByPapersQuery(
        personId,
        dateRange,
        false,
        projectId,
        terms
      );

    this.logger.debug(
      { data: citedByPapersQuery },
      "Performing citation profile citedBy query"
    );

    const multiSearchRequest: MsearchRequest = {
      index: this.peopleIndex,
      searches: [
        HEADER,
        toMultiSearchBody(citedPapersQuery),
        HEADER,
        toMultiSearchBody(citedByPapersQuery)
      ]
    };

    const {
      responses: [citedResponse, citedByResponse]
    } = await this.elasticService.msearch<CitationDocument>(multiSearchRequest);

    if (isErrorResponse(citedResponse)) {
      this.logger.error(citedResponse);
      throw new Error(citedResponse.error.reason);
    }

    if (isErrorResponse(citedByResponse)) {
      this.logger.error(citedByResponse);
      throw new Error(citedByResponse.error.reason);
    }

    const boundHitToCitationProfile = hitToCitationProfile(
      hcpScope.institutions,
      langCode
    );

    return {
      cited: (
        citedResponse as MsearchMultiSearchItem<CitationDocument>
      ).hits.hits.map(boundHitToCitationProfile),
      citedBy: (
        citedByResponse as MsearchMultiSearchItem<CitationDocument>
      ).hits.hits.map(boundHitToCitationProfile),
      fullName: hcpScope.name_eng,
      fullName_jpn: hcpScope.name_jpn,
      fullName_cmn: hcpScope.name_cmn
    };
  }

  private async figureOutWhichTermsAndLanguageToUse(
    terms: Array<string> | undefined,
    usersLanguage: Language | undefined
  ): Promise<QueryDslQueryContainer | undefined> {
    if (_.isEmpty(terms)) {
      return undefined;
    }

    const languageDetector =
      this.languageDetectService.getLanguageDetector(usersLanguage);

    const queryParserResponse =
      await this.queryParserService.parseQueryWithQueryUnderstandingService(
        terms![0],
        {
          projectSupportsAdvancedOperators: true
        }
      );

    const parsedQueryTree = queryParserResponse!.parsedQueryTree!;

    return this.parsedQueryTreeToElasticsearchQueriesService.parse(
      parsedQueryTree,
      [
        "publications.publicationAbstract",
        "publications.keywords",
        "publications.title"
      ],
      languageDetector
    );
  }
}

function toMultiSearchBody(
  searchRequest: SearchRequest
): MsearchMultisearchBody {
  return {
    size: searchRequest.size,
    _source: searchRequest._source_includes,
    query: searchRequest.query
  };
}

function hitToCitationProfile(
  targetPersonLocations: Array<string>,
  language: string
) {
  return (hit: SearchHit<CitationDocument>): CitationProfile => {
    const institutions = _.intersection(
      targetPersonLocations,
      hit._source?.institutions
    );

    const citationProfile = {
      citationCount: hit._score!,
      fullName: getPreferredNameTranslation(hit._source!, language),
      id: hit._source!.id,
      matchedPubs: 0,
      cardMetrics: [
        {
          name: "Publications",
          count: hit._source!.publicationCount,
          path: "publications"
        },
        {
          name: "Citations",
          count: hit._source!.citationTotal,
          path: "publications"
        },
        {
          name: "Clinical Trials",
          count: hit._source!.trialCount,
          path: "clinical-trials"
        },
        {
          name: "Congresses",
          count: hit._source!.congressCount,
          path: "congresses"
        },
        {
          name: "Payments",
          count: hit._source!.paymentTotal,
          path: "payments"
        }
      ],
      publications: hit.inner_hits!.publications.hits.hits.map(
        innerHitToCitationPub(language)
      ),
      sharedMetrics: [
        {
          count: institutions.length,
          name: "Shared Institutions"
        }
      ]
    };

    return citationProfile;
  };
}

function getPreferredNameTranslation(
  citationDocument: CitationDocument,
  language: string
): string {
  if (language === CHINESE && citationDocument.name_cmn) {
    return citationDocument.name_cmn;
  }
  if (language === JAPANESE && citationDocument.name_jpn) {
    return citationDocument.name_jpn;
  }
  if (citationDocument.name_eng) {
    return citationDocument.name_eng;
  }

  return NO_NAME_TRANSLATION_AVAILABLE;
}

function getFieldValueOrDefault<T>(object: Record<string, T>) {
  const firstTruthyValue = Object.keys(object)
    .map((key) => {
      const originalValue = object[key];

      if (Array.isArray(originalValue)) {
        return originalValue[0];
      } else {
        return originalValue;
      }
    })
    .find(Boolean);

  return firstTruthyValue;
}

function innerHitToCitationPub(langCode: string) {
  return (hit: SearchHit<Record<string, any>>): CitationProfilePublication => {
    const id = _.get(
      hit.fields,
      ["publications.id", FIRST_ELEMENT],
      NO_PUBLICATION_ID
    );
    const citationCount = _.get(
      hit.fields,
      ["publications.citationCount", FIRST_ELEMENT],
      0
    );
    const socialMediaCount = _.get(
      hit.fields,
      ["publications.microBloggingCount", FIRST_ELEMENT],
      0
    );
    const date = _.get(hit.fields, [
      "publications.datePublished",
      FIRST_ELEMENT
    ]);

    const journal =
      getFieldValueOrDefault(
        _.pick(hit.fields, [
          `publications.journalName_${langCode}`,
          "publications.journalName_eng",
          "publications.journalName_jpn",
          "publications.journalName_cmn"
        ])
      ) || NO_JOURNAL_NAME;

    const title =
      getFieldValueOrDefault(
        _.pick(hit.fields, [
          `publications.title_${langCode}.keyword`,
          "publications.title_eng.keyword",
          "publications.title_jpn.keyword",
          "publications.title_cmn.keyword"
        ])
      ) || NO_PUBLICATION_TITLE;

    return {
      id,
      citationCount,
      socialMediaCount,
      journal,
      title,
      date: date ? date.toString() : undefined
    };
  };
}
