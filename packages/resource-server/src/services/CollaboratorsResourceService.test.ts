import { faker } from "@faker-js/faker";
import * as _ from "lodash";

import { CollaboratorsResourceService } from "./CollaboratorsResourceService";
import { ElasticSearchService } from "./ElasticSearchService";
import { ConfigService } from "./ConfigService";
import { createMockInstance } from "../util/TestUtils";
import { QueryParserService } from "./QueryParserService";
import { FindCollaboratorsQueryBuilder } from "./queryBuilders/FindCollaboratorsQueryBuilder";
import {
  CountResponse,
  SearchHit,
  SearchResponse,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import {
  ProjectFeatures,
  ProjectFeaturesResourceClient
} from "@h1nyc/account-sdk";
import { ProjectIdRequiredError } from "./errors/ProjectIdRequiredError";

function generateMockCollaboratorHit(overrides = {}) {
  const collaborator = {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid(),
      congressCount: faker.datatype.number(),
      publicationCount: faker.datatype.number(),
      trialCount: faker.datatype.number()
    }
  };

  return _.merge(collaborator, overrides);
}

function generateMockElasticsearchResponse<T>(
  hits: Array<SearchHit<T>> = []
): SearchResponse<T> {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: faker.datatype.number(),
        relation: "eq"
      },
      max_score: faker.datatype.number(),
      hits
    }
  };
}

describe("CollaboratorsResourceService", () => {
  describe("hasCollaborators", () => {
    it("should return true if count > 0", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const projectFeaturesResourceClient = createMockInstance(
        ProjectFeaturesResourceClient
      );

      const elasticSearchService = createMockInstance(ElasticSearchService);
      elasticSearchService.count.mockResolvedValue({
        count: faker.datatype.number() + 1
      } as CountResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const findCollaboratorsQueryBuilder = createMockInstance(
        FindCollaboratorsQueryBuilder
      );

      const collaboratorsResourceService = new CollaboratorsResourceService(
        configService,
        projectFeaturesResourceClient,
        elasticSearchService,
        queryParserService,
        findCollaboratorsQueryBuilder
      );

      const personId = faker.datatype.uuid();
      const projectId = faker.datatype.string();

      const hasCollaborators =
        await collaboratorsResourceService.hasCollaborators(
          personId,
          projectId
        );

      expect(hasCollaborators).toEqual({
        exists: true
      });
      expect(elasticSearchService.count).toHaveBeenCalledWith({
        index: configService.elasticPeopleIndex,
        query: {
          bool: {
            filter: [
              expect.termQuery("collaboratorIds", personId),
              expect.termQuery("projectIds", projectId)
            ],
            must_not: [expect.termQuery("id", personId)]
          }
        }
      });

      expect(
        projectFeaturesResourceClient.getProjectFeatures
      ).not.toHaveBeenCalled();
      expect(
        queryParserService.parseQueryWithQueryUnderstandingService
      ).not.toHaveBeenCalled();
      expect(findCollaboratorsQueryBuilder.build).not.toHaveBeenCalled();
    });

    it("should return false if count = 0", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const projectFeaturesResourceClient = createMockInstance(
        ProjectFeaturesResourceClient
      );

      const elasticSearchService = createMockInstance(ElasticSearchService);
      elasticSearchService.count.mockResolvedValue({
        count: 0
      } as CountResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const findCollaboratorsQueryBuilder = createMockInstance(
        FindCollaboratorsQueryBuilder
      );

      const collaboratorsResourceService = new CollaboratorsResourceService(
        configService,
        projectFeaturesResourceClient,
        elasticSearchService,
        queryParserService,
        findCollaboratorsQueryBuilder
      );

      const personId = faker.datatype.uuid();
      const projectId = faker.datatype.string();

      const hasCollaborators =
        await collaboratorsResourceService.hasCollaborators(
          personId,
          projectId
        );

      expect(hasCollaborators).toEqual({
        exists: false
      });
      expect(elasticSearchService.count).toHaveBeenCalledWith({
        index: configService.elasticPeopleIndex,
        query: {
          bool: {
            filter: [
              expect.termQuery("collaboratorIds", personId),
              expect.termQuery("projectIds", projectId)
            ],
            must_not: [expect.termQuery("id", personId)]
          }
        }
      });

      expect(
        queryParserService.parseQueryWithQueryUnderstandingService
      ).not.toHaveBeenCalled();
      expect(findCollaboratorsQueryBuilder.build).not.toHaveBeenCalled();
    });

    it("should re-throw an error thrown by elasticSearchService", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const projectFeaturesResourceClient = createMockInstance(
        ProjectFeaturesResourceClient
      );

      const elasticSearchService = createMockInstance(ElasticSearchService);
      const elasticSearchError = new Error(
        "elasticsearch threw an error for some reason"
      );
      elasticSearchService.count.mockRejectedValue(elasticSearchError);

      const queryParserService = createMockInstance(QueryParserService);
      const findCollaboratorsQueryBuilder = createMockInstance(
        FindCollaboratorsQueryBuilder
      );

      const collaboratorsResourceService = new CollaboratorsResourceService(
        configService,
        projectFeaturesResourceClient,
        elasticSearchService,
        queryParserService,
        findCollaboratorsQueryBuilder
      );

      const personId = faker.datatype.uuid();
      const projectId = faker.datatype.string();

      try {
        await collaboratorsResourceService.hasCollaborators(
          personId,
          projectId
        );
        fail("an error should have been thrown");
      } catch (err) {
        expect(err).toEqual(elasticSearchError);
      }

      expect(
        queryParserService.parseQueryWithQueryUnderstandingService
      ).not.toHaveBeenCalled();
      expect(findCollaboratorsQueryBuilder.build).not.toHaveBeenCalled();
    });
  });

  describe("findCollaborators", () => {
    describe("negative tests", () => {
      it("should throw a ProjectIdRequiredError error when projectId is not supplied", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const projectFeaturesResourceClient = createMockInstance(
          ProjectFeaturesResourceClient
        );
        const projectFeaturesResourceClientError = new Error(
          "projectFeaturesResourceClient.getProjectFeatures threw an error for some reason"
        );
        projectFeaturesResourceClient.getProjectFeatures.mockRejectedValue(
          projectFeaturesResourceClientError
        );

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const queryParserService = createMockInstance(QueryParserService);
        const queryParserServiceError = new Error(
          "queryParserService.parseQueryWithQueryUnderstandingService threw an error for some reason"
        );
        queryParserService.parseQueryWithQueryUnderstandingService.mockRejectedValue(
          queryParserServiceError
        );

        const findCollaboratorsQueryBuilder = createMockInstance(
          FindCollaboratorsQueryBuilder
        );

        const collaboratorsResourceService = new CollaboratorsResourceService(
          configService,
          projectFeaturesResourceClient,
          elasticSearchService,
          queryParserService,
          findCollaboratorsQueryBuilder
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const terms: Array<string> = [
          faker.datatype.string(),
          faker.datatype.string()
        ];

        await expect(
          collaboratorsResourceService.findCollaborators(
            personId,
            dateRange,
            page,
            terms,
            undefined
          )
        ).rejects.toThrowError(new ProjectIdRequiredError());
      });

      it("should re-throw an error thrown by projectFeaturesResourceClient.getProjectFeatures", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const projectFeaturesResourceClient = createMockInstance(
          ProjectFeaturesResourceClient
        );
        const projectFeaturesResourceClientError = new Error(
          "projectFeaturesResourceClient.getProjectFeatures threw an error for some reason"
        );
        projectFeaturesResourceClient.getProjectFeatures.mockRejectedValue(
          projectFeaturesResourceClientError
        );

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const queryParserService = createMockInstance(QueryParserService);
        const queryParserServiceError = new Error(
          "queryParserService.parseQueryWithQueryUnderstandingService threw an error for some reason"
        );
        queryParserService.parseQueryWithQueryUnderstandingService.mockRejectedValue(
          queryParserServiceError
        );

        const findCollaboratorsQueryBuilder = createMockInstance(
          FindCollaboratorsQueryBuilder
        );

        const collaboratorsResourceService = new CollaboratorsResourceService(
          configService,
          projectFeaturesResourceClient,
          elasticSearchService,
          queryParserService,
          findCollaboratorsQueryBuilder
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const terms: Array<string> = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        const projectId = faker.datatype.string();

        try {
          await collaboratorsResourceService.findCollaborators(
            personId,
            dateRange,
            page,
            terms,
            projectId
          );
          fail("an error should have been thrown");
        } catch (err) {
          expect(err).toEqual(projectFeaturesResourceClientError);
        }
      });

      it("should re-throw an error thrown by queryParserService.parseQueryWithQueryUnderstandingService", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const projectFeaturesResourceClient = createMockInstance(
          ProjectFeaturesResourceClient
        );
        projectFeaturesResourceClient.getProjectFeatures.mockResolvedValue({
          advancedOperatorSearch: faker.datatype.boolean()
        } as ProjectFeatures);

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const queryParserService = createMockInstance(QueryParserService);
        const queryParserServiceError = new Error(
          "queryParserService.parseQueryWithQueryUnderstandingService threw an error for some reason"
        );
        queryParserService.parseQueryWithQueryUnderstandingService.mockRejectedValue(
          queryParserServiceError
        );

        const findCollaboratorsQueryBuilder = createMockInstance(
          FindCollaboratorsQueryBuilder
        );

        const collaboratorsResourceService = new CollaboratorsResourceService(
          configService,
          projectFeaturesResourceClient,
          elasticSearchService,
          queryParserService,
          findCollaboratorsQueryBuilder
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const terms: Array<string> = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        const projectId = faker.datatype.string();

        try {
          await collaboratorsResourceService.findCollaborators(
            personId,
            dateRange,
            page,
            terms,
            projectId
          );
          fail("an error should have been thrown");
        } catch (err) {
          expect(err).toEqual(queryParserServiceError);
        }
      });

      it("should re-throw an error thrown by elasticSearchService.query", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const projectFeaturesResourceClient = createMockInstance(
          ProjectFeaturesResourceClient
        );
        projectFeaturesResourceClient.getProjectFeatures.mockResolvedValue({
          advancedOperatorSearch: faker.datatype.boolean()
        } as ProjectFeatures);

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchServiceError = new Error(
          "elasticSearchService.query threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(elasticSearchServiceError);

        const queryParserService = createMockInstance(QueryParserService);
        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          undefined
        );

        const findCollaboratorsQueryBuilder = createMockInstance(
          FindCollaboratorsQueryBuilder
        );

        const collaboratorsResourceService = new CollaboratorsResourceService(
          configService,
          projectFeaturesResourceClient,
          elasticSearchService,
          queryParserService,
          findCollaboratorsQueryBuilder
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const terms: Array<string> = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        const projectId = faker.datatype.string();

        try {
          await collaboratorsResourceService.findCollaborators(
            personId,
            dateRange,
            page,
            terms,
            projectId
          );
          fail("an error should have been thrown");
        } catch (err) {
          expect(err).toEqual(elasticSearchServiceError);
        }
      });
    });

    it("elasticsearch response with hits should return total hits and converted collaborators", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const projectFeaturesResourceClient = createMockInstance(
        ProjectFeaturesResourceClient
      );
      const advancedOperatorSearch = faker.datatype.boolean();
      projectFeaturesResourceClient.getProjectFeatures.mockResolvedValue({
        advancedOperatorSearch
      } as ProjectFeatures);

      const elasticSearchService = createMockInstance(ElasticSearchService);
      const collaboratorHit1 = generateMockCollaboratorHit();
      const collaboratorHit2 = generateMockCollaboratorHit();
      const collaboratorHit3 = generateMockCollaboratorHit();
      const elasticSearchResponse = generateMockElasticsearchResponse([
        collaboratorHit1,
        collaboratorHit2,
        collaboratorHit3
      ]);

      elasticSearchService.query.mockResolvedValue(elasticSearchResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const parsedQueryTree = faker.datatype.string();
      queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
        {
          parsedQueryTree,
          synonyms: []
        }
      );

      const findCollaboratorsQueryBuilder = createMockInstance(
        FindCollaboratorsQueryBuilder
      );
      const builtQuery = {
        query: {
          term: {
            [faker.database.column()]: faker.datatype.string()
          }
        }
      };
      findCollaboratorsQueryBuilder.build.mockReturnValue(builtQuery);

      const collaboratorsResourceService = new CollaboratorsResourceService(
        configService,
        projectFeaturesResourceClient,
        elasticSearchService,
        queryParserService,
        findCollaboratorsQueryBuilder
      );

      const personId = faker.datatype.uuid();
      const dateRange = {
        min: faker.datatype.number(),
        max: faker.datatype.number()
      };
      const page = {
        limit: faker.datatype.number(),
        offset: faker.datatype.number()
      };
      const terms: Array<string> = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const projectId = faker.datatype.string();

      const collaborators =
        await collaboratorsResourceService.findCollaborators(
          personId,
          dateRange,
          page,
          terms,
          projectId
        );

      expect(collaborators).toEqual({
        total: (elasticSearchResponse.hits.total as SearchTotalHits).value,
        collaborators: [
          {
            personId: collaboratorHit1._source.id,
            count: collaboratorHit1._score,
            totalCount:
              collaboratorHit1._source.congressCount +
              collaboratorHit1._source.publicationCount +
              collaboratorHit1._source.trialCount
          },
          {
            personId: collaboratorHit2._source.id,
            count: collaboratorHit2._score,
            totalCount:
              collaboratorHit2._source.congressCount +
              collaboratorHit2._source.publicationCount +
              collaboratorHit2._source.trialCount
          },
          {
            personId: collaboratorHit3._source.id,
            count: collaboratorHit3._score,
            totalCount:
              collaboratorHit3._source.congressCount +
              collaboratorHit3._source.publicationCount +
              collaboratorHit3._source.trialCount
          }
        ]
      });

      expect(
        projectFeaturesResourceClient.getProjectFeatures
      ).toHaveBeenCalledWith(projectId);
      expect(
        queryParserService.parseQueryWithQueryUnderstandingService
      ).toHaveBeenCalledWith(terms[0], {
        projectSupportsAdvancedOperators: advancedOperatorSearch
      });
      expect(findCollaboratorsQueryBuilder.build).toHaveBeenCalledWith(
        {
          personId,
          projectId,
          size: page.limit,
          minDate: dateRange.min,
          query: terms[0]
        },
        parsedQueryTree
      );
      expect(elasticSearchService.query).toHaveBeenCalledWith(builtQuery);
    });

    it("elasticsearch response with empty hits should return total hits and empty collaborators", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const projectFeaturesResourceClient = createMockInstance(
        ProjectFeaturesResourceClient
      );
      projectFeaturesResourceClient.getProjectFeatures.mockResolvedValue({
        advancedOperatorSearch: faker.datatype.boolean()
      } as ProjectFeatures);

      const elasticSearchService = createMockInstance(ElasticSearchService);
      const elasticSearchResponse = generateMockElasticsearchResponse();
      elasticSearchService.query.mockResolvedValue(elasticSearchResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const parsedQueryTree = faker.datatype.string();
      queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
        {
          parsedQueryTree,
          synonyms: []
        }
      );

      const findCollaboratorsQueryBuilder = createMockInstance(
        FindCollaboratorsQueryBuilder
      );
      const builtQuery = {
        query: {
          term: {
            [faker.database.column()]: faker.datatype.string()
          }
        }
      };
      findCollaboratorsQueryBuilder.build.mockReturnValue(builtQuery);

      const collaboratorsResourceService = new CollaboratorsResourceService(
        configService,
        projectFeaturesResourceClient,
        elasticSearchService,
        queryParserService,
        findCollaboratorsQueryBuilder
      );

      const personId = faker.datatype.uuid();
      const dateRange = {
        min: faker.datatype.number(),
        max: faker.datatype.number()
      };
      const page = {
        limit: faker.datatype.number(),
        offset: faker.datatype.number()
      };
      const terms: Array<string> = [
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const projectId = faker.datatype.string();

      const collaborators =
        await collaboratorsResourceService.findCollaborators(
          personId,
          dateRange,
          page,
          terms,
          projectId
        );

      expect(collaborators).toEqual({
        total: (elasticSearchResponse.hits.total as SearchTotalHits).value,
        collaborators: []
      });
    });

    describe("anomalous request parameter values", () => {
      it("should treat missing dateRange.min as not supplied", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const projectFeaturesResourceClient = createMockInstance(
          ProjectFeaturesResourceClient
        );
        projectFeaturesResourceClient.getProjectFeatures.mockResolvedValue({
          advancedOperatorSearch: faker.datatype.boolean()
        } as ProjectFeatures);

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchResponse = generateMockElasticsearchResponse();

        elasticSearchService.query.mockResolvedValue(elasticSearchResponse);

        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTree = faker.datatype.string();
        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree,
            synonyms: []
          }
        );

        const findCollaboratorsQueryBuilder = createMockInstance(
          FindCollaboratorsQueryBuilder
        );

        const collaboratorsResourceService = new CollaboratorsResourceService(
          configService,
          projectFeaturesResourceClient,
          elasticSearchService,
          queryParserService,
          findCollaboratorsQueryBuilder
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const terms = [faker.random.word(), faker.random.word()];
        const projectId = faker.datatype.string();

        await collaboratorsResourceService.findCollaborators(
          personId,
          dateRange,
          page,
          terms,
          projectId
        );

        expect(findCollaboratorsQueryBuilder.build).toHaveBeenCalledWith(
          {
            personId,
            projectId,
            size: page.limit,
            minDate: undefined,
            query: terms[0]
          },
          parsedQueryTree
        );
      });

      it("should call queryParserService with undefined query when request terms is undefined or empty", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const projectFeaturesResourceClient = createMockInstance(
          ProjectFeaturesResourceClient
        );
        projectFeaturesResourceClient.getProjectFeatures.mockResolvedValue({
          advancedOperatorSearch: faker.datatype.boolean()
        } as ProjectFeatures);

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchResponse = generateMockElasticsearchResponse();

        elasticSearchService.query.mockResolvedValue(elasticSearchResponse);

        const queryParserService = createMockInstance(QueryParserService);
        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          undefined
        );

        const findCollaboratorsQueryBuilder = createMockInstance(
          FindCollaboratorsQueryBuilder
        );

        const collaboratorsResourceService = new CollaboratorsResourceService(
          configService,
          projectFeaturesResourceClient,
          elasticSearchService,
          queryParserService,
          findCollaboratorsQueryBuilder
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const projectId = faker.datatype.string();

        for (const terms of [undefined, []]) {
          await collaboratorsResourceService.findCollaborators(
            personId,
            dateRange,
            page,
            terms,
            projectId
          );

          expect(
            queryParserService.parseQueryWithQueryUnderstandingService
          ).toHaveBeenCalledWith(undefined, expect.any(Object));
        }
      });
    });

    describe("response parsing anomalies", () => {
      it("should return collaborator's count as 0 when _score is null", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const projectFeaturesResourceClient = createMockInstance(
          ProjectFeaturesResourceClient
        );
        projectFeaturesResourceClient.getProjectFeatures.mockResolvedValue({
          advancedOperatorSearch: faker.datatype.boolean()
        } as ProjectFeatures);

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const collaboratorHitWithNullScore = generateMockCollaboratorHit({
          _score: null
        });
        const elasticSearchResponse = generateMockElasticsearchResponse([
          collaboratorHitWithNullScore
        ]);
        elasticSearchService.query.mockResolvedValue(elasticSearchResponse);

        const queryParserService = createMockInstance(QueryParserService);
        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          undefined
        );

        const findCollaboratorsQueryBuilder = createMockInstance(
          FindCollaboratorsQueryBuilder
        );

        const collaboratorsResourceService = new CollaboratorsResourceService(
          configService,
          projectFeaturesResourceClient,
          elasticSearchService,
          queryParserService,
          findCollaboratorsQueryBuilder
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const terms: Array<string> = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        const projectId = faker.datatype.string();

        const collaborators =
          await collaboratorsResourceService.findCollaborators(
            personId,
            dateRange,
            page,
            terms,
            projectId
          );

        expect(collaborators).toEqual({
          total: (elasticSearchResponse.hits.total as SearchTotalHits).value,
          collaborators: [
            {
              personId: collaboratorHitWithNullScore._source.id,
              count: 0,
              totalCount: expect.any(Number)
            }
          ]
        });
      });

      it("should treat null congress/publication/trial count values as 0", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const projectFeaturesResourceClient = createMockInstance(
          ProjectFeaturesResourceClient
        );
        projectFeaturesResourceClient.getProjectFeatures.mockResolvedValue({
          advancedOperatorSearch: faker.datatype.boolean()
        } as ProjectFeatures);

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const collaboratorHitWithNullCongressCount =
          generateMockCollaboratorHit({
            _source: {
              congressCount: null
            }
          });
        const collaboratorHitWithNullPublicationCount =
          generateMockCollaboratorHit({
            _source: {
              publicationCount: null
            }
          });
        const collaboratorHitWithNullTrialCount = generateMockCollaboratorHit({
          _source: {
            trialCount: null
          }
        });
        const collaboratorHitWithAllNullCounts = generateMockCollaboratorHit({
          _source: {
            congressCount: null,
            publicationCount: null,
            trialCount: null
          }
        });
        const elasticSearchResponse = generateMockElasticsearchResponse([
          collaboratorHitWithNullCongressCount,
          collaboratorHitWithNullPublicationCount,
          collaboratorHitWithNullTrialCount,
          collaboratorHitWithAllNullCounts
        ]);

        elasticSearchService.query.mockResolvedValue(elasticSearchResponse);

        const queryParserService = createMockInstance(QueryParserService);
        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          undefined
        );

        const findCollaboratorsQueryBuilder = createMockInstance(
          FindCollaboratorsQueryBuilder
        );

        const collaboratorsResourceService = new CollaboratorsResourceService(
          configService,
          projectFeaturesResourceClient,
          elasticSearchService,
          queryParserService,
          findCollaboratorsQueryBuilder
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const terms: Array<string> = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        const projectId = faker.datatype.string();

        const collaborators =
          await collaboratorsResourceService.findCollaborators(
            personId,
            dateRange,
            page,
            terms,
            projectId
          );

        expect(collaborators).toEqual({
          total: (elasticSearchResponse.hits.total as SearchTotalHits).value,
          collaborators: [
            {
              personId: collaboratorHitWithNullCongressCount._source.id,
              count: collaboratorHitWithNullCongressCount._score,
              totalCount:
                collaboratorHitWithNullCongressCount._source.publicationCount +
                collaboratorHitWithNullCongressCount._source.trialCount
            },
            {
              personId: collaboratorHitWithNullPublicationCount._source.id,
              count: collaboratorHitWithNullPublicationCount._score,
              totalCount:
                collaboratorHitWithNullPublicationCount._source.congressCount +
                collaboratorHitWithNullPublicationCount._source.trialCount
            },
            {
              personId: collaboratorHitWithNullTrialCount._source.id,
              count: collaboratorHitWithNullTrialCount._score,
              totalCount:
                collaboratorHitWithNullTrialCount._source.congressCount +
                collaboratorHitWithNullTrialCount._source.publicationCount
            },
            {
              personId: collaboratorHitWithAllNullCounts._source.id,
              count: collaboratorHitWithAllNullCounts._score,
              totalCount: 0
            }
          ]
        });
      });

      it("should return hits.hits.total-1 and collaborators w/o self if hits contains personId being queried for", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const projectFeaturesResourceClient = createMockInstance(
          ProjectFeaturesResourceClient
        );
        projectFeaturesResourceClient.getProjectFeatures.mockResolvedValue({
          advancedOperatorSearch: faker.datatype.boolean()
        } as ProjectFeatures);

        const personId = faker.datatype.uuid();

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const collaboratorHit1 = generateMockCollaboratorHit();
        const collaboratorHit2 = generateMockCollaboratorHit({
          _source: {
            id: personId
          }
        });
        const collaboratorHit3 = generateMockCollaboratorHit();
        const elasticSearchResponse = generateMockElasticsearchResponse([
          collaboratorHit1,
          collaboratorHit2,
          collaboratorHit3
        ]);

        elasticSearchService.query.mockResolvedValue(elasticSearchResponse);

        const queryParserService = createMockInstance(QueryParserService);
        const parsedQueryTree = faker.datatype.string();
        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree,
            synonyms: []
          }
        );

        const findCollaboratorsQueryBuilder = createMockInstance(
          FindCollaboratorsQueryBuilder
        );
        const builtQuery = {
          query: {
            term: {
              [faker.database.column()]: faker.datatype.string()
            }
          }
        };
        findCollaboratorsQueryBuilder.build.mockReturnValue(builtQuery);

        const collaboratorsResourceService = new CollaboratorsResourceService(
          configService,
          projectFeaturesResourceClient,
          elasticSearchService,
          queryParserService,
          findCollaboratorsQueryBuilder
        );

        const dateRange = {
          min: faker.datatype.number(),
          max: faker.datatype.number()
        };
        const page = {
          limit: faker.datatype.number(),
          offset: faker.datatype.number()
        };
        const terms: Array<string> = [
          faker.datatype.string(),
          faker.datatype.string()
        ];
        const projectId = faker.datatype.string();

        const collaborators =
          await collaboratorsResourceService.findCollaborators(
            personId,
            dateRange,
            page,
            terms,
            projectId
          );

        expect(collaborators).toEqual({
          total:
            (elasticSearchResponse.hits.total as SearchTotalHits).value - 1,
          collaborators: [
            {
              personId: collaboratorHit1._source.id,
              count: collaboratorHit1._score,
              totalCount:
                collaboratorHit1._source.congressCount +
                collaboratorHit1._source.publicationCount +
                collaboratorHit1._source.trialCount
            },
            {
              personId: collaboratorHit3._source.id,
              count: collaboratorHit3._score,
              totalCount:
                collaboratorHit3._source.congressCount +
                collaboratorHit3._source.publicationCount +
                collaboratorHit3._source.trialCount
            }
          ]
        });
      });
    });
  });
});
