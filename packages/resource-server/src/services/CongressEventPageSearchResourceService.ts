import {
  SessionsSearchByCongressInput,
  SessionsSearchByCongressResponse,
  CongressEventPageSearchResource,
  RPC_NAMESPACE_CONGRESS_EVENT_PAGE_SEARCH,
  SearchIndicationsByQueryInput,
  IndicationType,
  IndicationSource,
  IndicationSortBy,
  GetSpeakerIdsByRoleInput,
  SessionsSearchFilterAutocompleteInput,
  CongressSearchFilterAggregation
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { Service } from "typedi";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { createLogger } from "../lib/Logger";
import {
  QueryDslQueryContainer,
  SearchRequest,
  SearchResponse,
  SearchTotalHits,
  Sort
} from "@elastic/elasticsearch/lib/api/types";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import {
  buildMatchPhraseQuery,
  buildTermsQuery
} from "../util/QueryBuildingUtils";
import {
  CHINESE,
  ENGLISH,
  JAPANESE,
  Language,
  LanguageDetectService
} from "./LanguageDetectService";
import {
  ALL_ANDs,
  ALL_ORs,
  ALL_UNICODE_DOUBLE_QUOTES,
  ASCII_DOUBLE_QUOTES,
  HCPDocument,
  MAXIMUM_QUERY_TOKENS,
  OR
} from "./KeywordSearchResourceServiceRewrite";
import {
  featureFlagDefaults,
  NameSearchFeatureFlags,
  nameSearchFeatureFlagTypes
} from "./NameSearchResourceServiceRewrite";
import { AppName, UserResourceClient } from "@h1nyc/account-sdk";
import { sha1 } from "object-hash";
import { UserOnboardingDataService } from "./UserOnboardingDataService";
import NameSearchBuilderFactory from "./queryBuilders/NameSearchBuilderFactory";
import { ElasticSearchService } from "./ElasticSearchService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { ElasticSearchCongressSessionsService } from "./ElasticSearchCongressSessionsService";
import Redis from "ioredis";
import CalculateMinimumNameVariations from "./queryBuilders/CalculateMinimumNameVariations";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { DEFAULT_NAME_VARIATION_MATCH_COUNT } from "./queryBuilders/DefaultNameSearchBuilder";
import _ from "lodash";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";
import { ElasticSearchCongressService } from "./ElasticsearchCongressService";
import { CongressSearchResponseAdapterService } from "./CongressSearchResponseAdapterService";

export const SOURCE_INCLUDES: string[] = [
  "h1_session_id",
  "h1_conference_id",
  "h1_series_id",
  "name",
  "description",
  "indication",
  "url",
  "translations",
  "h1_person_ids",
  "speakers",
  "filters.start_date",
  "filters.end_date",
  "filters.session_type",
  "sub_sessions"
];

const ALL_ASSETS_OPTIONAL = 0;
const AT_LEAST_ONE_ASSET = 1;
const SECONDS = "ex";
const EXPIRATION_PERIOD = 600;
const HAS_ADVANCED_OPERATORS = /(\sAND\s|\sOR\s|NOT\s)/;
const NAME_SEARCH_OFFSET = 0;
const NAME_SEARCH_SIZE = 15;
const SEARCH_TYPE = "dfs_query_then_fetch";
const SORT: Sort = [
  "_score",
  { "filters.start_date": "asc" },
  { speaker_count: "desc" }
];

function getHitsTotal(total: number | SearchTotalHits | undefined) {
  if (total === undefined) {
    return 0;
  }

  if (typeof total === "number") {
    return total;
  }

  return total.value;
}

const EMPTY_STRING = "";
const IGNORE_HITS = 0;

export type ElasticsearchCongressSessionDoc = {
  h1_session_id: string;
  h1_conference_id: string;
  h1_series_id: string;
  name: string;
  description: string;
  speakers?: Speaker[];
  translations: {
    name: string;
    description: string;
    language_code: string;
    session_type: string;
  }[];
  filters?: {
    session_type?: string;
    start_date?: number;
    end_date?: number;
  };
  sub_sessions: Omit<
    ElasticsearchCongressSessionDoc,
    "sub_sessions" | "h1_series_id" | "h1_conference_id"
  >[];
};

type Speaker = {
  h1_person_id?: string;
  name: string;
  role: string;
};

export enum SessionsSearchFilterAutocompleteTypes {
  TYPE = "type"
}

const fieldToMatchForFilterAutocompleteType = {
  [SessionsSearchFilterAutocompleteTypes.TYPE]:
    "session_type.autocomplete_search"
};

const fieldToAggregateForFilterAutocompleteType = {
  [SessionsSearchFilterAutocompleteTypes.TYPE]: "filters.session_type"
};

@Service()
@RpcService()
export class CongressEventPageSearchResourceService
  extends RpcResourceService
  implements CongressEventPageSearchResource
{
  private readonly logger = createLogger(this);
  private congressSessionsIndexName: string;
  private congressIndexName: string;
  private peopleIndexName: string;
  private redisClient;

  constructor(
    config: ConfigService,
    private elasticSearchCongressSessionsService: ElasticSearchCongressSessionsService,
    private congressSearchResponseAdapterService: CongressSearchResponseAdapterService,
    private queryUnderstandingServiceClient: QueryUnderstandingServiceClient,
    private calculateMinimumNameVariations: CalculateMinimumNameVariations,
    private elasticSearchCongressService: ElasticSearchCongressService,
    private indicationsTreeSearchService: IndicationsTreeSearchService,
    private userOnboardingDataService: UserOnboardingDataService,
    private nameSearchBuilderFactory: NameSearchBuilderFactory,
    private elasticSearchPeopleService: ElasticSearchService,
    private languageDetectService: LanguageDetectService,
    private featureFlagsService: FeatureFlagsService,
    private userClient: UserResourceClient
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_CONGRESS_EVENT_PAGE_SEARCH,
      config.searchRedisOptions
    );

    this.congressSessionsIndexName = config.elasticCongressSessionsIndex;
    this.congressIndexName = config.elasticCongressIndex;
    this.peopleIndexName = config.elasticPeopleIndex;
    this.redisClient = new Redis(config.searchCacheRedisOptions);
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  @Trace("h1-search.congress.searchSessionsByCongress")
  async searchSessionsByCongress(
    input: Readonly<SessionsSearchByCongressInput>
  ): Promise<SessionsSearchByCongressResponse> {
    const idsOfMatchingPeople = await this.findPeopleMatchingQuery(input);
    const indicationsToEnhanceSearchQuery =
      await this.getIndicationsToEnhanceSearchQuery(input);
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);

    const request = await this.buildSearchRequest(
      input,
      idsOfMatchingPeople,
      queryUnderstandingServiceResponse,
      indicationsToEnhanceSearchQuery
    );

    this.logger.info({ request }, "elastic search request");

    const results =
      await this.elasticSearchCongressSessionsService.query<ElasticsearchCongressSessionDoc>(
        request
      );

    this.logger.info(
      { took: results.took, total: getHitsTotal(results.hits.total) },
      "execution time"
    );

    return this.adaptToSessionsSearchResponse(results);
  }

  @RpcMethod()
  @Trace("h1-search.congress.getSpeakerIdsByRole")
  async getSpeakerIdsByRole(
    input: GetSpeakerIdsByRoleInput
  ): Promise<string[]> {
    const request: SearchRequest = {
      _source: ["speakers"],
      index: this.congressIndexName,
      size: 1,
      query: {
        term: {
          h1_conference_id: input.congressId
        }
      }
    };

    const results = await this.elasticSearchCongressService.query<
      Pick<ElasticsearchCongressSessionDoc, "speakers">
    >(request);

    if (!results.hits.hits.length) {
      return [];
    }

    const speakers = results.hits.hits[0]._source?.speakers ?? [];

    if (input.roles.length === 0) {
      return _.compact(speakers.map(this.toSpeakerId));
    }

    return _.compact(
      speakers
        .filter((speaker) => this.speakerHasMatchingRole(speaker, input.roles))
        .map(this.toSpeakerId)
    );
  }

  @RpcMethod()
  @Trace("h1-search.congress.getIndicationsForCongress")
  async getIndicationsForCongress(congressId: string): Promise<string[]> {
    const request: SearchRequest = {
      _source: ["indication"],
      index: this.congressIndexName,
      size: 1,
      query: {
        term: {
          h1_conference_id: congressId
        }
      }
    };

    const results = await this.elasticSearchCongressService.query<{
      indication: string[];
    }>(request);

    if (!results.hits.hits.length) {
      return [];
    }

    return _.compact(results.hits.hits[0]._source?.indication ?? []);
  }

  @RpcMethod()
  @Trace("h1-search.congress.autocompleteSessionTypes")
  async autocompleteSessionTypes(
    input: SessionsSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]> {
    return this.executeAutocompleteQuery(
      SessionsSearchFilterAutocompleteTypes.TYPE,
      _.omit(input, "filters.type.sessionTypes")
    );
  }

  private async executeAutocompleteQuery(
    filterType: SessionsSearchFilterAutocompleteTypes,
    input: SessionsSearchFilterAutocompleteInput
  ): Promise<CongressSearchFilterAggregation[]> {
    const queryUnderstandingServiceResponse =
      await this.retrieveQueryUnderstandingServiceResponse(input);

    const idsOfMatchingPeople: string[] = [];
    if (input.globalQuery) {
      idsOfMatchingPeople.push(...(await this.findPeopleMatchingQuery(input)));
    }

    const indicationsToEnhanceSearchQuery =
      await this.getIndicationsToEnhanceSearchQuery(input);

    return this.autocomplete(
      filterType,
      input,
      idsOfMatchingPeople,
      indicationsToEnhanceSearchQuery,
      queryUnderstandingServiceResponse
    );
  }

  private async autocomplete(
    filterType: SessionsSearchFilterAutocompleteTypes,
    input: Readonly<SessionsSearchFilterAutocompleteInput>,
    idsOfMatchingPeople: string[],
    indicationsToEnhanceSearchQuery?: string,
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>
  ) {
    this.logger.info({ input }, "sessions search filter autocomplete");

    const phraseQuery = this.buildMatchPhraseForAutocomplete(filterType, input);

    const request = await this.buildSearchRequest(
      input,
      idsOfMatchingPeople,
      queryUnderstandingServiceResponse,
      indicationsToEnhanceSearchQuery
    );

    if (input.filterQuery && phraseQuery) {
      request.query!.bool!.must = [phraseQuery];
    }

    request.size = IGNORE_HITS;
    request._source = false;
    request.track_total_hits = false;
    request.aggs = this.buildAggregationForFilterAutocomplete(
      filterType,
      input
    );
    request.sort = undefined;

    this.logger.info({ query: request }, "elasticsearch request");

    const data = await this.elasticSearchCongressSessionsService.query<never>(
      request
    );

    this.logger.info(
      { took: data.took, total: data.hits.total },
      "execution time"
    );

    return this.congressSearchResponseAdapterService.adaptToCongressSearchFilterAggregations(
      data,
      filterType
    );
  }

  private buildMatchPhraseForAutocomplete(
    filterType: SessionsSearchFilterAutocompleteTypes,
    input: SessionsSearchFilterAutocompleteInput
  ) {
    return buildMatchPhraseQuery(
      fieldToMatchForFilterAutocompleteType[filterType],
      input.filterQuery ?? ""
    );
  }

  private buildAggregationForFilterAutocomplete(
    filterType: SessionsSearchFilterAutocompleteTypes,
    input: SessionsSearchFilterAutocompleteInput
  ) {
    return {
      [filterType]: {
        filter: {
          bool: {
            filter: this.buildFilterForAggregation(filterType, input)
          }
        },
        aggs: {
          filtered_matching: {
            terms: {
              field: fieldToAggregateForFilterAutocompleteType[filterType],
              size: input.count
            }
          }
        }
      }
    };
  }

  private buildFilterForAggregation(
    filterType: SessionsSearchFilterAutocompleteTypes,
    input: SessionsSearchFilterAutocompleteInput
  ): QueryDslQueryContainer[] {
    return input.filterQuery
      ? [this.buildMatchPhraseForAutocomplete(filterType, input)]
      : [];
  }

  private toSpeakerId(speaker: Speaker) {
    return speaker.h1_person_id;
  }

  private speakerHasMatchingRole(speaker: Speaker, roles: string[]) {
    return roles.includes(speaker.role);
  }

  private async buildSearchRequest(
    input: Readonly<SessionsSearchByCongressInput>,
    idsOfMatchingPeople: string[],
    queryUnderstandingServiceResponse?: Readonly<QueryUnderstandingServiceResponse>,
    indicationsToEnhanceSearchQuery?: string
  ): Promise<SearchRequest> {
    const hasPersonNameIntent = this.hasPersonNameIntent(
      queryUnderstandingServiceResponse
    );
    const filters = await this.buildElasticsearchFiltersFromInputFilters(input);

    const searchQuery =
      this.getSynonymizedQuery(
        input.query,
        queryUnderstandingServiceResponse
      ) ?? EMPTY_STRING;
    const queryIndicationSeparator = searchQuery?.length ? "|" : EMPTY_STRING;
    const searchQueryWithIndications = searchQuery?.concat(
      indicationsToEnhanceSearchQuery
        ? queryIndicationSeparator + indicationsToEnhanceSearchQuery
        : EMPTY_STRING
    );
    const shoulds: QueryDslQueryContainer[] = [];

    if (searchQueryWithIndications?.length) {
      shoulds.push(
        {
          simple_query_string: {
            query: searchQueryWithIndications,
            fields: ["name", "name.phonetic", "description", "indication"],
            default_operator: "and"
          }
        },
        {
          nested: {
            path: "sub_sessions",
            query: {
              simple_query_string: {
                query: searchQueryWithIndications,
                fields: ["name", "name.phonetic", "description", "indication"],
                default_operator: "and"
              }
            }
          }
        }
      );
    }

    if (idsOfMatchingPeople.length) {
      shoulds.push({
        terms: {
          h1_person_ids: idsOfMatchingPeople,
          boost: hasPersonNameIntent ? 10 : 1
        }
      });
    }

    const query: QueryDslQueryContainer = {
      bool: {
        should: shoulds.length ? shoulds : undefined,
        minimum_should_match: shoulds.length
          ? AT_LEAST_ONE_ASSET
          : ALL_ASSETS_OPTIONAL,
        filter: filters
      }
    };

    const request: SearchRequest = {
      index: this.congressSessionsIndexName,
      track_total_hits: true,
      _source: {
        include: SOURCE_INCLUDES
      },
      query,
      sort: SORT
    };

    if (input.paging?.limit != null && input.paging.limit >= 0) {
      request.size = input.paging.limit;
    }
    if (input.paging?.offset) {
      request.from = input.paging.offset;
    }

    return request;
  }

  private adaptToSessionsSearchResponse(
    results: SearchResponse<ElasticsearchCongressSessionDoc>
  ): SessionsSearchByCongressResponse {
    return {
      total: getHitsTotal(results.hits.total),
      sessions: _.compact(
        results.hits.hits.map((hit) => {
          const _source = hit._source!;

          return {
            id: _source.h1_session_id,
            name: _source.name,
            sessionType: _source.filters?.session_type,
            description: _source.description,
            startDate: _source.filters?.start_date,
            endDate: _source.filters?.end_date,
            speakers:
              _source.speakers?.map((speaker) => ({
                id: speaker.h1_person_id,
                name: speaker.name,
                role: speaker.role
              })) ?? [],
            translations: _source.translations.map((translation) => ({
              name: translation.name,
              description: translation.description,
              languageCode: translation.language_code,
              sessionType: translation.session_type
            })),
            subsessions: _source.sub_sessions?.map((subsession) => ({
              id: subsession.h1_session_id,
              name: subsession.name,
              sessionType: subsession.filters?.session_type,
              description: subsession.description,
              startDate: subsession.filters?.start_date,
              endDate: subsession.filters?.end_date,
              speakers:
                subsession.speakers?.map((speaker) => ({
                  id: speaker.h1_person_id,
                  name: speaker.name,
                  role: speaker.role
                })) ?? [],
              translations: subsession.translations.map((translation) => ({
                name: translation.name,
                description: translation.description,
                languageCode: translation.language_code,
                sessionType: translation.session_type
              }))
            }))
          };
        })
      )
    };
  }

  private async findPeopleMatchingQuery(
    input: Readonly<SessionsSearchByCongressInput>
  ): Promise<string[]> {
    if (!input.query || input.query.trim().length === 0) {
      return [];
    }

    const cachedMatchingPeopleIds = await this.getMatchingPeopleIdsFromCache(
      input.query,
      input.userId,
      input.projectId
    );

    if (cachedMatchingPeopleIds) {
      return cachedMatchingPeopleIds;
    }

    const { userId, projectId } = input;

    if (HAS_ADVANCED_OPERATORS.test(input.query)) {
      this.logger.info(
        { query: input.query },
        "Query has advanced operators. Skipping people name search..."
      );
      return [];
    }

    const nameSearchFeatureFlags = await this.getNameSearchFeatureFlagValues({
      userId,
      projectId
    });

    const query = this.sanitizeQuery(this.truncateQuery(input.query)) || "";

    const userLanguage = await this.getUsersPreferredLanguage(input);
    const languageDetector =
      this.languageDetectService.getLanguageDetector(userLanguage);
    const queryLang = languageDetector(query);

    const useCmnAndJpnFields =
      this.languageDetectService.shouldUseBothCmnAndJpnFields(
        queryLang as Language,
        userLanguage
      );
    const onboardingData =
      await this.userOnboardingDataService.getOnboardingData(userId, projectId);

    const nameSearchBuilder =
      this.nameSearchBuilderFactory.getNameSearchBuilder(queryLang as Language);

    const numberOfNameVariationsToMatch =
      queryLang === ENGLISH
        ? await this.calculateMinimumNameVariations.calculateMinimumFieldsToMatch(
            this.elasticSearchPeopleService,
            this.peopleIndexName,
            query
          )
        : DEFAULT_NAME_VARIATION_MATCH_COUNT;
    const queryUnderstandingServiceResponse =
      await this.queryUnderstandingServiceClient.analyze(
        query,
        queryLang as Language
      );
    const searchRequest = nameSearchBuilder.createNameSearchBody({
      sourceOverride: ["id"],
      query,
      from: NAME_SEARCH_OFFSET,
      size: NAME_SEARCH_SIZE,
      numberOfNameVariationsToMatch,
      filter: [
        {
          nested: {
            path: "congress",
            query: {
              term: {
                "congress.h1ConferenceId": input.congressId
              }
            }
          }
        }
      ],
      language: queryLang as Language,
      featureFlags: nameSearchFeatureFlags,
      onboardingData,
      queryUnderstandingServiceResponse,
      projectId,
      useCmnAndJpnFields
    });

    searchRequest.index = this.peopleIndexName;
    searchRequest.search_type = SEARCH_TYPE;

    this.logger.info(
      {
        searchRequest
      },
      "elasticsearch people name search request"
    );

    const response: SearchResponse<HCPDocument> =
      await this.elasticSearchPeopleService.query<HCPDocument>(searchRequest);

    const matchingPeopleIds = _.compact(
      _.map(response.hits.hits, (hit) => hit._source?.id)
    );

    this.addMatchingPeopleIdsToCache(
      input.query,
      input.userId,
      input.projectId,
      matchingPeopleIds
    );

    this.logger.info(
      {
        matchingPeopleIds
      },
      "people name search results"
    );

    return matchingPeopleIds;
  }

  private async getUsersPreferredLanguage({
    userId,
    projectId
  }: SessionsSearchByCongressInput): Promise<Language> {
    try {
      const usersPreferredLanguage =
        await this.userClient.getUsersPreferredLanguage(userId, projectId);
      this.logger.debug(
        `users preferred language: ${usersPreferredLanguage?.language}`
      );

      switch (usersPreferredLanguage?.language) {
        case "japanese":
        case JAPANESE:
          return JAPANESE;
        case "chinese_simplified":
        case "chinese_traditional":
        case CHINESE:
          return CHINESE;
        case "english":
        case ENGLISH:
        default:
          return ENGLISH;
      }
    } catch (err) {
      this.logger.error(
        { err },
        "Error thrown fetching users preferred language"
      );
      return ENGLISH;
    }
  }

  private async getMatchingPeopleIdsFromCache(
    query: string,
    userId: string,
    projectId: string
  ): Promise<string[] | null> {
    const key = sha1({ query, userId, projectId, entity: "matchingPeopleIds" });
    const raw = await this.getFromCache(key);
    if (raw) {
      const entities = JSON.parse(raw) as string[];
      this.logger.debug(entities, "matching people cache hit");
      return entities;
    }
    this.logger.debug(null, "matching people cache miss");
    return null;
  }

  private addMatchingPeopleIdsToCache(
    query: string,
    userId: string,
    projectId: string,
    matchingPeopleIds: Readonly<string[]>
  ) {
    const key = sha1({ query, userId, projectId, entity: "matchingPeopleIds" });
    this.addToCache(key, JSON.stringify(matchingPeopleIds), EXPIRATION_PERIOD);
  }

  private async getFromCache(key: string) {
    try {
      return await this.redisClient.get(key);
    } catch (err) {
      this.logger.error(err, "Error connecting redis");
      return null;
    }
  }

  private addToCache(
    key: string,
    value: string,
    ttl: number = EXPIRATION_PERIOD
  ) {
    try {
      this.redisClient.set(key, value, SECONDS, ttl);
    } catch (err) {
      this.logger.error(err, "Error connecting redis");
    }
  }

  private async buildElasticsearchFiltersFromInputFilters({
    congressId,
    filters: inputFilters
  }: SessionsSearchByCongressInput): Promise<QueryDslQueryContainer[]> {
    const esFilters: QueryDslQueryContainer[] = [
      {
        term: {
          h1_conference_id: congressId
        }
      }
    ];

    if (!inputFilters) {
      return esFilters;
    }

    if (inputFilters.dateRange) {
      const congressMinStartDate = {
        gte: inputFilters.dateRange!.min
      };
      esFilters.push({
        range: {
          "filters.start_date": congressMinStartDate
        }
      });

      if (inputFilters.dateRange!.max) {
        const congressMaxStartDate = {
          lte: inputFilters.dateRange!.max
        };
        esFilters.push({
          range: {
            "filters.start_date": congressMaxStartDate
          }
        });
      }
    }

    if (inputFilters.type?.sessionTypes) {
      esFilters.push(
        buildTermsQuery("filters.session_type", inputFilters.type.sessionTypes)
      );
    }

    return esFilters;
  }

  private getSynonymizedQuery(
    query?: string,
    queryUnderstandingServiceResponse?: QueryUnderstandingServiceResponse
  ): string | undefined {
    let searchQuery = query;
    if (
      queryUnderstandingServiceResponse &&
      queryUnderstandingServiceResponse.getAugmentedQuery()
    ) {
      searchQuery = this.convertQueryToSimpleQueryStringSyntax(
        queryUnderstandingServiceResponse.getAugmentedQuery()
      );
    }
    return searchQuery;
  }

  private convertQueryToSimpleQueryStringSyntax(query: string): string {
    const orRegex = /\sOR\s/g;
    const andRegex = /\sAND\s/g;
    const ALL_UNICODE_DOUBLE_QUOTES = /[“”]/g;
    const ASCII_DOUBLE_QUOTES = '"';

    const simpleQueryString = query
      .replace(orRegex, " | ")
      .replace(andRegex, " + ")
      .replace(ALL_UNICODE_DOUBLE_QUOTES, ASCII_DOUBLE_QUOTES);
    return simpleQueryString;
  }

  private hasPersonNameIntent(
    queryUnderstandingServiceResponse:
      | QueryUnderstandingServiceResponse
      | undefined
  ): boolean {
    if (
      queryUnderstandingServiceResponse?.hasQueryIntent &&
      queryUnderstandingServiceResponse.getQueryIntent()?.hasPersonNameIntent &&
      queryUnderstandingServiceResponse
        .getQueryIntent()
        ?.getPersonNameIntent()
        ?.getScore() === 1.0
    ) {
      return true;
    } else {
      return false;
    }
  }

  private async resolveInputQueryToIndications(
    input: SessionsSearchByCongressInput
  ): Promise<string[]> {
    if (input.query) {
      const indicationSearchInput: SearchIndicationsByQueryInput = {
        query: input.query,
        indicationType: [IndicationType.L3],
        size: 5,
        indicationSource: [IndicationSource.ALL],
        sortBy: IndicationSortBy.HCP_COMMUNITY_SIZE,
        projectId: input.projectId
      };
      const indicationNodes =
        await this.indicationsTreeSearchService.searchIndicationsByQuery(
          indicationSearchInput
        );
      const resolvedIndications: string[] = indicationNodes?.map((n) =>
        n.indicationName.trim().toLowerCase()
      );
      if (resolvedIndications) {
        return resolvedIndications;
      }
    }

    return [];
  }

  private async getIndicationSynonymsFromQueryUnderstandingService(
    indications: string[],
    or: boolean,
    country = "us"
  ): Promise<[string, string]> {
    const queryUnderstandingServiceResponse =
      await this.queryUnderstandingServiceClient.getIndicationSynonymsAndIcdCodes(
        indications,
        "eng",
        or,
        country
      );

    return [
      queryUnderstandingServiceResponse
        .getIndicationsParsedQuery()
        .replace(ALL_ORs, OR)
        .replace(ALL_ANDs, EMPTY_STRING)
        .replace(ALL_UNICODE_DOUBLE_QUOTES, ASCII_DOUBLE_QUOTES),
      queryUnderstandingServiceResponse.getIndicationsIcdCodesQuery()
    ];
  }

  private async getIndicationsToEnhanceSearchQuery(
    input: Readonly<SessionsSearchByCongressInput>
  ) {
    const indications = await this.resolveInputQueryToIndications(input);

    let indicationsParsedQuery;

    if (indications?.length) {
      [indicationsParsedQuery] =
        await this.getIndicationSynonymsFromQueryUnderstandingService(
          indications,
          true
        );
    }

    return indicationsParsedQuery;
  }

  private async retrieveQueryUnderstandingServiceResponse(
    input: Readonly<SessionsSearchByCongressInput>
  ): Promise<QueryUnderstandingServiceResponse | undefined> {
    let queryUnderstandingServiceResponse;
    let queryToAnalyze;

    if ("query" in input) {
      queryToAnalyze = input.query;
    }

    if (queryToAnalyze) {
      try {
        queryUnderstandingServiceResponse =
          await this.queryUnderstandingServiceClient.analyze(
            queryToAnalyze,
            "eng"
          );

        this.logger.info(
          {
            query: queryToAnalyze,
            response: queryUnderstandingServiceResponse.toObject()
          },
          "query understanding service"
        );
      } catch (error) {
        this.logger.error(
          { input, error },
          "Could not retrieve query understanding"
        );
      }
    }

    return queryUnderstandingServiceResponse;
  }

  private truncateQuery(query?: string) {
    if (query) {
      return query.trim().split(/\s+/).slice(0, MAXIMUM_QUERY_TOKENS).join(" ");
    }
    return query;
  }

  private sanitizeQuery(query?: string) {
    return query?.replace(/[´]/g, "'");
  }

  private async getNameSearchFeatureFlagValues({
    userId,
    projectId,
    appName
  }: {
    userId?: string;
    projectId: string;
    appName?: AppName;
  }): Promise<NameSearchFeatureFlags> {
    const featureFlags: Partial<NameSearchFeatureFlags> = {};
    const user = userId ? { userId, projectId, appName } : undefined;
    const flagsState = await this.featureFlagsService.getAllFlags(user);

    for (const flag of nameSearchFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as NameSearchFeatureFlags;
  }
}
