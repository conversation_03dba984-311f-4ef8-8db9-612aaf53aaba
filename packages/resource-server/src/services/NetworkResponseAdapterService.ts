import {
  NetworkResponse,
  NetworkCollaborator,
  SharedWork,
  NetworkAffiliation,
  NetworkLocation,
  SharedTrial,
  SharedCongress,
  SharedPublication,
  SharedWorksPage,
  NetworkRequest,
  NetworkCollaboratorRequest,
  NetworkFilterAggregation,
  NetworkFilterAutocompleteField,
  BulkNetworkResponse
} from "@h1nyc/search-sdk";
import * as _ from "lodash";

import {
  networkNestedDocTypes as nestedDocTypes,
  NetworkNestedDocPath as NestedDocPath,
  availableMultiLangSourceFields
} from "./queryBuilders/NetworkQueryBuilder";
import {
  AggregationsAggregate,
  SearchHit,
  SearchHitsMetadata,
  SearchInnerHitsResult,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import { Service } from "typedi";
import { ENGLISH, Language } from "./LanguageDetectService";
import { createHash } from "crypto";
import { AggregationsTermsAggregateBase } from "@elastic/elasticsearch/lib/api/types";
import { LocationLabelFormatterService } from "./LocationLabelFormatterService";
import { getRequestedTranslation } from "./KeywordSearchResponseAdapterService";

export type CollaboratorCountAggregation = {
  nested?: {
    people: {
      doc_count: number;
    };
  };
};

export type CollaboratorIdsAggregation = {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: {
    key: string;
    doc_count: number;
  }[];
};

export type BulkCollaboratorCountAggregation = {
  nested?: {
    per_person?: {
      buckets: {
        key: string;
        doc_count: number;
        people: {
          doc_count: number;
        };
      }[];
    };
  };
};

export type TotalCollaboratorAggregation = {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: {
    key: string;
    doc_count: number;
  }[];
};

export type HCPDocument = {
  id: string;
  affiliations: Affiliation[];
};

export type CollaboratorDocument = Omit<HCPDocument, "affiliations">;

type Collaborator = {
  id: string;
};

type Translation = {
  languageCode: Language;
};

type Name = {
  name: string;
};

export type NameTranslation = Name & Translation;

export type Address = {
  city: string;
  country: string;
  country_code: string;
  postal_code: string;
  region: string;
  region_code: string;
};

export type AddressTranslation = Address & Translation;

export type Institution = {
  id: string;
  name: string;
  address?: Address;
  nameTranslations?: NameTranslation[];
  addressTranslations?: AddressTranslation[];
  ultimateParentId?: string;
};

export type Affiliation = {
  institution: Institution;
  isCurrent: boolean;
  type?: string;
};

const AFFILIATIONS = "affiliations";

const totalCollaboratorTypes: Readonly<
  Record<NestedDocPath, Readonly<keyof NetworkResponse>>
> = {
  publications: "totalPublicationCollaborators",
  congress: "totalCongressCollaborators",
  trials: "totalTrialCollaborators"
};

type RetrievalMethods = Readonly<
  keyof {
    docvalueFields: Readonly<Record<SharedWorkKeys, string>>;
    sourceFields: Readonly<Record<SharedWorkKeys, string>>;
  }
>;

type SharedWorkKeys = Readonly<keyof SharedWork>;
const sharedWorkFacets: Readonly<Array<SharedWorkKeys>> = [
  "id",
  "title",
  "date",
  "persons"
];

type CongressKeys = Readonly<keyof SharedCongress>;
type CongressFacetTypes = Readonly<Record<CongressKeys, string>>;
const congressFacets: Readonly<Array<CongressKeys>> = [
  ...sharedWorkFacets,
  "name",
  "organizer",
  "type"
];
const congressFacetFields: Readonly<
  Record<RetrievalMethods, Partial<CongressFacetTypes>>
> = {
  sourceFields: {
    id: "id",
    title: "title_eng",
    date: "masterInitialDate",
    persons: "persons",
    type: "type"
  },
  docvalueFields: {
    organizer: "organizer_eng",
    name: "name_eng"
  }
};

type TrialKeys = Readonly<keyof SharedTrial>;
type TrialFacetTypes = Readonly<Record<TrialKeys, string>>;
const trialFacets: Readonly<Array<TrialKeys>> = [
  ...sharedWorkFacets,
  "phase",
  "status",
  "sponsor"
];
const trialFacetFields: Readonly<
  Record<RetrievalMethods, Partial<TrialFacetTypes>>
> = {
  sourceFields: {
    id: "id",
    title: "officialTitle_eng",
    date: "startDate",
    phase: "phase_eng",
    status: "status_eng",
    sponsor: "sponsor_eng",
    persons: "persons"
  },
  docvalueFields: {}
};

type PublicationKeys = Readonly<keyof SharedPublication>;
type PublicationFacetTypes = Readonly<Record<PublicationKeys, string>>;
const publicationFacets: Readonly<Array<PublicationKeys>> = [
  ...sharedWorkFacets,
  "name",
  "type"
];
const publicationFacetFields: Readonly<
  Record<RetrievalMethods, Partial<PublicationFacetTypes>>
> = {
  sourceFields: {
    id: "id",
    title: "title",
    date: "datePublished",
    persons: "persons"
  },
  docvalueFields: {
    type: "type",
    name: "journalName"
  }
};

const EMPTY_VALUE: string[] = [];

export type DocCountBucket = {
  key: string;
  doc_count: number;
  people: {
    doc_count: number;
  };
};

export type NestedFilteredMatchingAggregations = Record<
  "nested",
  Record<
    "filtered_matching",
    Record<"matching", AggregationsTermsAggregateBase<DocCountBucket>>
  >
>;

@Service()
export class NetworkResponseAdapterService {
  constructor(
    private locationLabelFormatterService: LocationLabelFormatterService
  ) {}

  emptyNetworkResponse(): Readonly<NetworkResponse> {
    return {
      totalCollaborators: 0,
      totalPublicationCollaborators: 0,
      totalCongressCollaborators: 0,
      totalTrialCollaborators: 0,
      collaborators: []
    };
  }

  emptyNetworkCollaborator(
    primaryHcpId: string,
    collaboratorId: string
  ): Readonly<Required<Omit<NetworkCollaborator, "locations">>> {
    return {
      id: this.generateUniqueId(primaryHcpId, collaboratorId),
      personId: collaboratorId,
      totalSharedWorks: 0,
      totalSharedTrials: 0,
      totalSharedCongresses: 0,
      totalSharedPublications: 0,
      sharedAffiliations: [],
      sharedWorks: [],
      trialOffset: 0,
      congressOffset: 0,
      publicationOffset: 0
    };
  }

  async adaptToNetwork(
    { personId }: NetworkRequest,
    hits: Readonly<SearchHitsMetadata<Readonly<CollaboratorDocument>>>,
    aggregations: Readonly<Record<string, AggregationsAggregate>> | undefined,
    language: Language
  ): Promise<Readonly<NetworkResponse>> {
    const totalCollaborators = this.getTotal(hits.total);
    const collaborators = hits.hits.map(
      (hit: SearchHit<CollaboratorDocument>) =>
        this.elasticsearchHitToCollaborator(hit, personId, language)
    );

    const {
      totalPublicationCollaborators,
      totalCongressCollaborators,
      totalTrialCollaborators
    } = this.parseAggregations(aggregations);

    return {
      totalPublicationCollaborators,
      totalCongressCollaborators,
      totalTrialCollaborators,
      totalCollaborators,
      collaborators
    };
  }

  async adaptToBulkNetwork(
    personIds: string[],
    aggregations: Readonly<Record<string, AggregationsAggregate>> | undefined
  ): Promise<BulkNetworkResponse[]> {
    // Get totals for each personId from aggregations
    const personTotals = this.parseAggregationsForBulkRequest(
      personIds,
      aggregations
    );

    return personIds.map((personId) => {
      const totals = personTotals[personId] || {};

      return {
        totalPublicationCollaborators:
          totals.totalPublicationCollaborators ?? 0,
        totalCongressCollaborators: totals.totalCongressCollaborators ?? 0,
        totalTrialCollaborators: totals.totalTrialCollaborators ?? 0,
        totalCollaborators: totals.totalCollaborators ?? 0
      };
    });
  }

  async adaptToCollaborator(
    request: Readonly<NetworkCollaboratorRequest>,
    hits: Readonly<SearchHitsMetadata<Readonly<CollaboratorDocument>>>,
    page: Readonly<SharedWorksPage>,
    language: Language
  ): Promise<Readonly<NetworkCollaborator>> {
    return this.elasticsearchHitsToNetworkCollaborator(
      request,
      hits,
      page,
      language
    );
  }

  adaptToAffiliationsList(
    hits: Readonly<SearchHitsMetadata<Readonly<HCPDocument>>>
  ) {
    if (hits.hits.length) {
      const person = hits.hits[0]._source;
      if (person && person[AFFILIATIONS]) {
        return person[AFFILIATIONS].map(
          this.extractInstitutionIdFromAffiliation
        );
      }
    }

    return [];
  }

  adaptToFilterAggregations(
    aggregations: Readonly<NestedFilteredMatchingAggregations>,
    filterField: NetworkFilterAutocompleteField
  ): Array<NetworkFilterAggregation> {
    const nonEmptyAggregations = (
      aggregations.nested.filtered_matching.matching.buckets as DocCountBucket[]
    )
      .map(this.docCountBucketToAggregation)
      .filter(this.positive);

    return nonEmptyAggregations.map((bucket) => ({
      id: this.formatBucketId(filterField, bucket.id),
      count: bucket.count
    }));
  }

  private formatBucketId(
    filterField: NetworkFilterAutocompleteField,
    value: string
  ): string {
    switch (filterField) {
      case NetworkFilterAutocompleteField.POSTAL_CODE:
        return this.locationLabelFormatterService.postalCode(value);
      case NetworkFilterAutocompleteField.CITY:
        return this.locationLabelFormatterService.city(value);
      case NetworkFilterAutocompleteField.REGION:
        return this.locationLabelFormatterService.region(value);
      case NetworkFilterAutocompleteField.COUNTRY:
        return this.locationLabelFormatterService.country(value);
      default:
        return value;
    }
  }

  private elasticsearchHitsToNetworkCollaborator(
    { personId, collaboratorId }: NetworkCollaboratorRequest,
    hits: SearchHitsMetadata<CollaboratorDocument>,
    page: SharedWorksPage,
    language: Language
  ): Required<Omit<NetworkCollaborator, "locations">> {
    if (hits.hits.length === 0) {
      return this.emptyNetworkCollaborator(personId, collaboratorId);
    }

    const hit = hits.hits[0];
    const sharedWorks = this.elasticsearchInnerHitsToSharedWorks(hit).slice(
      0,
      page.limit
    );
    const totalSharedPublications = this.getTotal(
      hit.inner_hits?.publications?.hits.total
    );
    const totalSharedTrials = this.getTotal(hit.inner_hits?.trials?.hits.total);
    const totalSharedCongresses = this.getTotal(
      hit.inner_hits?.congress?.hits.total
    );
    const sharedAffiliations = [];
    if (hit.inner_hits?.sharedAffiliations) {
      const affiliations = hit.inner_hits.sharedAffiliations.hits.hits.map(
        (affiliation) => {
          return this.elasticsearchInnerHitToAffiliation(
            affiliation as SearchHit<Affiliation>,
            language
          );
        }
      );

      sharedAffiliations.push(...affiliations);
    }

    let trialOffset = page.trialOffset;
    let congressOffset = page.congressOffset;
    let publicationOffset = page.publicationOffset;

    sharedWorks.forEach((sharedWork) => {
      if (this.isTrial(sharedWork)) {
        trialOffset++;
      } else if (this.isCongress(sharedWork)) {
        congressOffset++;
      } else if (this.isPublication(sharedWork)) {
        publicationOffset++;
      }
    });

    return {
      id: this.generateUniqueId(personId, collaboratorId),
      personId: collaboratorId,
      sharedAffiliations,
      sharedWorks,
      totalSharedCongresses,
      totalSharedTrials,
      totalSharedPublications,
      totalSharedWorks:
        totalSharedPublications + totalSharedTrials + totalSharedCongresses,
      trialOffset,
      congressOffset,
      publicationOffset
    };
  }

  private elasticsearchInnerHitsToSharedWorks(
    hit: SearchHit<CollaboratorDocument>
  ): Array<SharedTrial | SharedCongress | SharedPublication> {
    if (!hit.inner_hits) {
      return [];
    }

    const sharedWorks: Array<SharedTrial | SharedCongress | SharedPublication> =
      [];
    const innerHits = hit.inner_hits as Record<
      NestedDocPath,
      SearchInnerHitsResult
    >;

    sharedWorks.push(
      ...innerHits["publications"].hits.hits.map((hit) => {
        return this.elasticsearchInnerHitToSharedPublication(hit);
      }),
      ...innerHits["congress"].hits.hits.map((hit) => {
        return this.elasticsearchInnerHitToSharedCongress(hit);
      }),
      ...innerHits["trials"].hits.hits.map((hit) => {
        return this.elasticsearchInnerHitToSharedTrial(hit);
      })
    );

    sharedWorks.sort((sharedWork1, sharedWork2) => {
      return Date.parse(sharedWork2.date[0]) - Date.parse(sharedWork1.date[0]);
    });

    return sharedWorks;
  }

  private elasticsearchInnerHitToSharedCongress(hit: Record<string, any>) {
    const sharedWork = {} as SharedCongress;

    for (const facet of congressFacets) {
      const sourceField = congressFacetFields.sourceFields[facet];
      const docvalueField = congressFacetFields.docvalueFields[facet];

      if (facet === "id") {
        sharedWork[facet] = hit._source[sourceField!];
        continue;
      }

      sharedWork[facet] = this.extractValueForCongressFacet(
        hit,
        facet,
        sourceField,
        docvalueField,
        ENGLISH
      );
    }

    return sharedWork;
  }

  private elasticsearchInnerHitToSharedPublication(hit: Record<string, any>) {
    const sharedWork = {} as SharedPublication;

    const languageCodeToAppendToFields: Language =
      hit._source["languageCode"] || ENGLISH;

    for (const facet of publicationFacets) {
      const sourceField = publicationFacetFields.sourceFields[facet];
      const docvalueField = publicationFacetFields.docvalueFields[facet];

      if (facet === "id") {
        sharedWork[facet] = hit._source[sourceField!];
        continue;
      }

      sharedWork[facet] = this.extractValueForPublicationFacet(
        hit,
        facet,
        sourceField,
        docvalueField,
        languageCodeToAppendToFields
      );
    }

    return sharedWork;
  }

  private elasticsearchInnerHitToSharedTrial(hit: Record<string, any>) {
    const sharedWork = {} as SharedTrial;

    for (const facet of trialFacets) {
      const sourceField = trialFacetFields.sourceFields[facet];
      const docvalueField = trialFacetFields.docvalueFields[facet];

      if (facet === "id") {
        sharedWork[facet] = hit._source[sourceField!];
        continue;
      }

      sharedWork[facet] = this.extractValueForTrialFacet(
        hit,
        facet,
        sourceField,
        docvalueField,
        ENGLISH
      );
    }

    return sharedWork;
  }

  private extractValueForTrialFacet = this.extractValueForFacet.bind(
    this,
    "trials"
  );
  private extractValueForCongressFacet = this.extractValueForFacet.bind(
    this,
    "congress"
  );
  private extractValueForPublicationFacet = this.extractValueForFacet.bind(
    this,
    "publications"
  );

  private extractValueForFacet(
    path: NestedDocPath,
    hit: Record<string, any>,
    facet: TrialKeys | CongressKeys | PublicationKeys,
    sourceField: string | undefined,
    docvalueField: string | undefined,
    languageCodeToAppendToFields: Language
  ): string[] {
    let value;
    if (sourceField && hit._source) {
      const sourceFieldWithLangCode =
        this.appendLanguageCodeIfMultiLangAvailable(
          path,
          sourceField,
          languageCodeToAppendToFields
        );

      if (facet === "persons") {
        value = hit._source[sourceFieldWithLangCode].map(
          this.extractCollaboratorId
        );
      } else {
        value = hit._source[sourceFieldWithLangCode];
      }
    } else if (docvalueField && hit.fields) {
      const docvalueFieldWithLangCode =
        this.appendLanguageCodeIfMultiLangAvailable(
          path,
          docvalueField,
          languageCodeToAppendToFields
        );
      value = hit.fields[`${path}.${docvalueFieldWithLangCode}`];
    }

    if (value) {
      if (_.isArray(value)) {
        return value;
      }
      return [value];
    }

    return EMPTY_VALUE;
  }

  private elasticsearchHitToCollaborator(
    hit: SearchHit<CollaboratorDocument>,
    primaryHcpId: string,
    language: Language
  ): NetworkCollaborator {
    const uniqueId = this.generateUniqueId(primaryHcpId, hit._source!.id);

    if (!hit.inner_hits) {
      return {
        id: uniqueId,
        personId: hit._source!.id,
        totalSharedWorks: 0,
        totalSharedCongresses: 0,
        totalSharedPublications: 0,
        totalSharedTrials: 0,
        sharedAffiliations: [],
        locations: []
      };
    }

    const publicationsCount = this.getTotal(
      hit.inner_hits.publications?.hits.total
    );
    const trialsCount = this.getTotal(hit.inner_hits.trials?.hits.total);
    const congressCount = this.getTotal(hit.inner_hits.congress?.hits.total);

    const locations = hit.inner_hits.locations.hits.hits.reduce(
      (locations: NetworkLocation[], hit) => {
        locations.push(
          ...this.elasticsearchInnerHitToLocation(
            hit as SearchHit<Affiliation>,
            language
          )
        );
        return locations;
      },
      []
    );

    const sharedAffiliations = [];
    if (hit.inner_hits.sharedAffiliations) {
      const affiliations = hit.inner_hits.sharedAffiliations.hits.hits.map(
        (affiliation) => {
          return this.elasticsearchInnerHitToAffiliation(
            affiliation as SearchHit<Affiliation>,
            language
          );
        }
      );

      sharedAffiliations.push(...affiliations);
    }

    return {
      id: uniqueId,
      personId: hit._source!.id,
      totalSharedWorks: publicationsCount + trialsCount + congressCount,
      totalSharedCongresses: congressCount,
      totalSharedPublications: publicationsCount,
      totalSharedTrials: trialsCount,
      locations,
      sharedAffiliations
    };
  }

  private elasticsearchInnerHitToAffiliation(
    hit: SearchHit<Affiliation>,
    language: Language
  ): NetworkAffiliation {
    const id = this.extractInstitutionIdFromAffiliation(hit._source!);
    const name = this.extractInstitutionNameFromAffiliation(
      hit._source!,
      language
    );
    const type = hit._source!.type;
    const isCurrent = hit._source!.isCurrent;
    const ultimateParentId = hit._source?.institution.ultimateParentId;
    return {
      id: String(id),
      name: name,
      affiliationType: type ?? null,
      isCurrent: isCurrent,
      ultimateParentId: ultimateParentId ?? null
    };
  }

  private elasticsearchInnerHitToLocation(
    hit: SearchHit<Affiliation>,
    language: Language
  ): NetworkLocation[] {
    const { country, country_code, region, region_code, postal_code, city } =
      this.extractInstitutionAddressFromAffiliation(hit._source!, language) ??
      {};

    const location = {
      country: country ?? null,
      countryCode: country_code ?? null,
      region: region ?? null,
      regionCode: region_code ?? null,
      postalCode: postal_code ?? null,
      city: city ?? null
    };

    const isEmpty = Object.values(location).every((field) => field === null);

    if (!isEmpty) {
      return [location];
    }

    return [];
  }

  private parseAggregations(
    aggregations: Readonly<Record<string, AggregationsAggregate> | undefined>
  ) {
    const totals = { ...this.emptyNetworkResponse() };

    if (aggregations) {
      for (const path of nestedDocTypes) {
        const aggregation = aggregations[path] as CollaboratorCountAggregation;
        if (aggregation) {
          (totals[totalCollaboratorTypes[path]] as number) =
            aggregation.nested?.people.doc_count ?? 0;
        }
      }
    }

    return totals;
  }

  private parseAggregationsForBulkRequest(
    personIds: string[],
    aggregations: Readonly<Record<string, AggregationsAggregate> | undefined>
  ) {
    const personTotals: Record<
      string,
      ReturnType<typeof this.emptyNetworkResponse>
    > = {};

    if (aggregations) {
      for (const personId of personIds) {
        if (!personTotals[personId]) {
          personTotals[personId] = { ...this.emptyNetworkResponse() };
        }
      }

      for (const path of nestedDocTypes) {
        const aggregation = aggregations[
          path
        ] as BulkCollaboratorCountAggregation;

        if (aggregation?.nested?.per_person?.buckets) {
          for (const bucket of aggregation.nested.per_person.buckets) {
            const bucketKey = bucket.key; // ID from the bucket

            if (personIds.includes(bucketKey)) {
              const docCount = bucket.people?.doc_count ?? 0;

              personTotals[bucketKey] = {
                ...personTotals[bucketKey],
                [totalCollaboratorTypes[path]]: docCount
              };
            }
          }
        }
      }

      this.parseTotalCollaborators(aggregations, personIds, personTotals);
    }

    return personTotals;
  }

  private parseTotalCollaborators(
    aggregations: Record<string, AggregationsAggregate>,
    personIds: string[],
    personTotals: Record<string, Readonly<NetworkResponse>>
  ) {
    const totalAggs = aggregations[
      "total_collaborators"
    ] as TotalCollaboratorAggregation;

    if (totalAggs) {
      for (const bucket of totalAggs.buckets) {
        const bucketKey = bucket.key; // ID from the bucket

        if (personIds.includes(bucketKey)) {
          const docCount = bucket?.doc_count ?? 0;

          personTotals[bucketKey] = {
            ...personTotals[bucketKey],
            ["totalCollaborators"]: docCount
          };
        }
      }
    }
  }

  private appendLanguageCodeIfMultiLangAvailable(
    path: NestedDocPath,
    field: string,
    languageCode: Language
  ) {
    if (availableMultiLangSourceFields[path].has(field)) {
      return `${field}_${languageCode}`;
    }

    return field;
  }

  private extractCollaboratorId(person: Collaborator) {
    return person.id;
  }

  private extractInstitutionIdFromAffiliation(affiliation: Affiliation) {
    return affiliation.institution.id;
  }

  private extractInstitutionNameFromAffiliation(
    affiliation: Affiliation,
    language: Language
  ) {
    const nameTranslations = affiliation.institution.nameTranslations;
    const translatedName = getRequestedTranslation(nameTranslations, language);
    return translatedName?.name ?? affiliation.institution.name;
  }

  private extractInstitutionAddressFromAffiliation(
    affiliation: Affiliation,
    language: Language
  ) {
    const addressTranslations = affiliation.institution.addressTranslations;
    const translatedAddress = addressTranslations?.find(
      (translation) => translation.languageCode === language
    );
    return translatedAddress ?? affiliation.institution.address;
  }

  private getTotal(total: number | SearchTotalHits | undefined): number {
    if (total) {
      return typeof total === "number" ? total : total.value;
    }
    return 0;
  }

  private isTrial(sharedWork: SharedWork): sharedWork is SharedTrial {
    return "sponsor" in sharedWork;
  }

  private isCongress(sharedWork: SharedWork): sharedWork is SharedCongress {
    return "organizer" in sharedWork;
  }

  private isPublication(
    sharedWork: SharedWork
  ): sharedWork is SharedPublication {
    return "type" in sharedWork;
  }

  private generateUniqueId(
    primaryHcpId: string,
    collaboratorId: string
  ): string {
    const shasum = createHash("sha256");
    return shasum.update(`${primaryHcpId}:${collaboratorId}`).digest("hex");
  }

  private docCountBucketToAggregation(
    bucket: DocCountBucket
  ): NetworkFilterAggregation {
    return {
      id: bucket.key,
      count: bucket.people.doc_count ?? 0
    };
  }

  private positive(bucket: NetworkFilterAggregation) {
    return bucket.count > 0;
  }
}
