import { faker } from "@faker-js/faker";
import { HCPNotFoundError } from "./HCPNotFoundError";

describe("HCPNotFoundError", () => {
  it("should return the id supplied and an error message", () => {
    const hcpId = faker.datatype.uuid();
    const error = new HCPNotFoundError(hcpId);

    expect(error.hcpId).toEqual(hcpId);
    expect(error.message).toEqual(`Unable to find HCP ID: ${hcpId}`);
  });
});
