{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://dp.h1.co/schema/v1/latest.schema.json", "type": "object", "additionalProperties": false, "properties": {"version": {"const": 1}, "h1_clinical_trial_id": {"type": "string"}, "identifiers": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"h1_clinical_trial_id": {"type": "string"}, "external_uuid": {"type": "string"}, "collection_source_id": {"type": "integer"}}, "required": ["external_uuid", "collection_source_id"]}}, "person": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"study_person_hash": {"type": "string"}, "h1_clinical_trial_id": {"type": "string"}, "h1_person_id": {"type": "integer"}, "external_uuid": {"type": "string"}, "name": {"type": "string"}, "collection_source_id": {"type": "integer"}, "roleHistory": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"hash_or_id": {"type": "string"}, "study_person_role_hash": {"type": "string"}, "study_person_hash": {"type": "string"}, "h1_clinical_trial_id": {"type": "string"}, "role": {"type": "string"}, "start_date": {"type": "string"}, "end_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "affiliation": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}}, "required": ["study_person_role_hash"]}}}, "required": ["study_person_hash", "external_uuid"]}}, "facility": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"study_organization_hash": {"type": "string"}, "h1_clinical_trial_id": {"type": "string"}, "external_uuid": {"type": "string"}, "name": {"type": "string"}, "city": {"type": "array", "items": {"type": "string"}}, "state": {"type": "array", "items": {"type": "string"}}, "zip": {"type": "string"}, "country": {"type": "array", "items": {"type": "string"}}, "h1_organization_id": {"type": "integer"}, "collection_source_id": {"type": "integer"}, "statusHistory": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"hash_or_id": {"type": "string"}, "facility_hash": {"type": "string"}, "h1_clinical_trial_id": {"type": "string"}, "study_organization_hash": {"type": "string"}, "start_date": {"type": "string"}, "end_date": {"type": "string"}, "status": {"type": "string"}, "collection_source_id": {"type": "integer"}}, "required": ["facility_hash"]}}, "personHistory": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"hash_or_id": {"type": "string"}, "facility_person_hash": {"type": "string"}, "h1_clinical_trial_id": {"type": "string"}, "study_organization_hash": {"type": "string"}, "study_person_hash": {"type": "string"}, "role": {"type": "string"}, "start_date": {"type": "string"}, "end_date": {"type": "string"}, "collection_source_id": {"type": "integer"}}, "required": ["facility_person_hash", "study_person_hash"]}}}, "required": ["study_organization_hash", "external_uuid"]}}, "effective_date": {"type": "string"}, "study": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "h1_clinical_trial_id": {"type": "string"}, "nlm_download_date_description": {"type": "string"}, "results_first_submitted_date": {"type": "string"}, "results_first_submitted_qc_date": {"type": "string"}, "results_first_posted_date": {"type": "string"}, "results_first_posted_date_type": {"type": "string"}, "disposition_first_submitted_qc_date": {"type": "string"}, "disposition_first_posted_date": {"type": "string"}, "disposition_first_submitted_date": {"type": "string"}, "disposition_first_posted_date_type": {"type": "string"}, "study_first_submitted_date": {"type": "string"}, "last_update_submitted_date": {"type": "string"}, "last_update_posted_date_type": {"type": "string"}, "start_month_year": {"type": "string"}, "start_date": {"type": "string"}, "start_date_type": {"type": "string"}, "verification_month_year": {"type": "string"}, "verification_date": {"type": "string"}, "completion_month_year": {"type": "string"}, "completion_date": {"type": "string"}, "completion_date_type": {"type": "string"}, "primary_completion_month_year": {"type": "string"}, "primary_completion_date": {"type": "string"}, "primary_completion_date_type": {"type": "string"}, "study_first_submitted_qc_date": {"type": "string"}, "study_first_posted_date": {"type": "string"}, "study_first_posted_date_type": {"type": "string"}, "last_update_submitted_qc_date": {"type": "string"}, "target_duration": {"type": "string"}, "acronym": {"type": "string"}, "baseline_population": {"type": "string"}, "last_known_status": {"type": "string"}, "phase": {"type": "string"}, "limitations_and_caveats": {"type": "string"}, "number_of_arms": {"type": "integer"}, "why_stopped": {"type": "string"}, "expanded_access_type_individual": {"type": "boolean"}, "expanded_access_type_intermediate": {"type": "boolean"}, "expanded_access_type_treatment": {"type": "boolean"}, "is_unapproved_device": {"type": "boolean"}, "is_ppsd": {"type": "boolean"}, "is_us_export": {"type": "boolean"}, "biospec_retention": {"type": "string"}, "biospec_description": {"type": "string"}, "plan_to_share_ipd_description": {"type": "string"}, "ipd_time_frame": {"type": "string"}, "ipd_access_criteria": {"type": "string"}, "ipd_url": {"type": "string"}, "study_type": {"type": "string"}, "brief_title": {"type": "string"}, "official_title": {"type": "string"}, "overall_status": {"type": "string"}, "enrollment": {"type": "integer"}, "enrollment_type": {"type": "string"}, "source": {"type": "string"}, "number_of_groups": {"type": "integer"}, "has_expanded_access": {"type": "boolean"}, "has_dmc": {"type": "boolean"}, "is_fda_regulated_drug": {"type": "boolean"}, "is_fda_regulated_device": {"type": "boolean"}, "plan_to_share_ipd": {"type": "string"}, "brief_summaries": {"type": "array", "items": {"type": "string"}}, "browse_conditions": {"type": "array", "items": {"type": "string"}}, "browse_interventions": {"type": "array", "items": {"type": "string"}}, "conditions": {"type": "array", "items": {"type": "string"}}, "detailed_descriptions": {"type": "array", "items": {"type": "string"}}, "keywords": {"type": "array", "items": {"type": "string"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "idInformation": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "id_type": {"type": "string"}, "id_value": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "id_information_hash": {"type": "string"}}, "required": ["external_uuid", "id_information_hash"]}}, "sponsor": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "agency_class": {"type": "string"}, "lead_or_collaborator": {"type": "string"}, "is_responsible_party": {"type": "boolean"}, "is_organization": {"type": "boolean"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "sponsor_hash": {"type": "string"}, "study_organization_hash": {"type": "string"}, "study_person_hash": {"type": "string"}}, "required": ["external_uuid", "sponsor_hash"]}}, "designOutcome": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "outcome_type": {"type": "string"}, "measure": {"type": "string"}, "time_frame": {"type": "string"}, "description": {"type": "string"}, "population": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "design_outcome_hash": {"type": "string"}}, "required": ["external_uuid", "design_outcome_hash"]}}, "reference": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "pmid": {"type": "integer"}, "reference_type": {"type": "string"}, "citation": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "study_reference_hash": {"type": "string"}}, "required": ["external_uuid", "study_reference_hash"]}}, "link": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "url": {"type": "string"}, "description": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "link_hash": {"type": "string"}}, "required": ["external_uuid", "link_hash"]}}, "pendingResult": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "event": {"type": "string"}, "event_date_description": {"type": "string"}, "event_date": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "pending_result_hash": {"type": "string"}}, "required": ["external_uuid", "pending_result_hash"]}}, "resultAgreement": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "pi_employee": {"type": "string"}, "agreement": {"type": "string"}, "restriction_type": {"type": "string"}, "other_details": {"type": "string"}, "restrictive_agreement": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "result_agreement_hash": {"type": "string"}}, "required": ["external_uuid", "result_agreement_hash"]}}, "designGroup": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "group_type": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "design_group_hash": {"type": "string"}, "intervention": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "intervention_hash": {"type": "string"}, "design_group_hash": {"type": "string"}, "design_group_intervention_hash": {"type": "string"}}, "required": ["external_uuid", "intervention_hash", "design_group_intervention_hash"]}}}, "required": ["external_uuid", "design_group_hash"]}}, "intervention": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "nct_id": {"type": "string"}, "intervention_type": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "intervention_other_names": {"type": "array", "items": {"type": "string"}}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "intervention_hash": {"type": "string"}}, "required": ["external_uuid", "intervention_hash"]}}, "resultGroup": {"type": "array", "items": {"type": "object", "required": ["external_uuid", "result_group_hash"], "properties": {"external_uuid": {"type": "string"}, "ctgov_group_code": {"type": "string"}, "result_type": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "result_group_hash": {"type": "string"}, "dropWithdrawal": {"type": "array", "items": {"type": "object", "required": ["external_uuid", "drop_withdrawal_hash"], "properties": {"external_uuid": {"type": "string"}, "ctgov_group_code": {"type": "string"}, "period": {"type": "string"}, "reason": {"type": "string"}, "count": {"type": "integer"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "result_group_hash": {"type": "string"}, "drop_withdrawal_hash": {"type": "string"}}, "additionalProperties": false}}, "milestone": {"type": "array", "items": {"type": "object", "required": ["external_uuid", "milestone_hash"], "properties": {"external_uuid": {"type": "string"}, "ctgov_group_code": {"type": "string"}, "title": {"type": "string"}, "period": {"type": "string"}, "description": {"type": "string"}, "count": {"type": "integer"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "result_group_hash": {"type": "string"}, "milestone_hash": {"type": "string"}}, "additionalProperties": false}}, "baselineMeasurement": {"type": "array", "items": {"type": "object", "required": ["external_uuid", "baseline_measurement_hash"], "properties": {"external_uuid": {"type": "string"}, "ctgov_group_code": {"type": "string"}, "classification": {"type": "string"}, "category": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "units": {"type": "string"}, "param_type": {"type": "string"}, "param_value": {"type": "string"}, "param_value_num": {"type": "number"}, "dispersion_type": {"type": "string"}, "dispersion_value": {"type": "string"}, "dispersion_value_num": {"type": "number"}, "dispersion_lower_limit": {"type": "number"}, "dispersion_upper_limit": {"type": "number"}, "explanation_of_na": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "result_group_hash": {"type": "string"}, "baseline_measurement_hash": {"type": "string"}}, "additionalProperties": false}}, "baselineCount": {"type": "array", "items": {"type": "object", "required": ["external_uuid", "baseline_count_hash"], "properties": {"external_uuid": {"type": "string"}, "ctgov_group_code": {"type": "string"}, "units": {"type": "string"}, "scope": {"type": "string"}, "count": {"type": "integer"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "result_group_hash": {"type": "string"}, "baseline_count_hash": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}}, "outcome": {"type": "array", "items": {"type": "object", "required": ["external_uuid", "outcome_hash"], "properties": {"external_uuid": {"type": "string"}, "outcome_type": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "time_frame": {"type": "string"}, "population": {"type": "string"}, "anticipated_posting_date": {"type": "string"}, "anticipated_posting_month_year": {"type": "string"}, "units": {"type": "string"}, "units_analyzed": {"type": "string"}, "dispersion_type": {"type": "string"}, "param_type": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "outcome_hash": {"type": "string"}, "analysis": {"type": "array", "items": {"type": "object", "required": ["external_uuid", "outcome_analysis_hash"], "properties": {"external_uuid": {"type": "string"}, "non_inferiority_type": {"type": "string"}, "non_inferiority_description": {"type": "string"}, "param_type": {"type": "string"}, "param_value": {"type": "number"}, "dispersion_type": {"type": "string"}, "dispersion_value": {"type": "number"}, "p_value_modifier": {"type": "number"}, "p_value": {"type": "number"}, "ci_n_sides": {"type": "string"}, "ci_percent": {"type": "number"}, "ci_lower_limit": {"type": "number"}, "ci_upper_limit": {"type": "number"}, "ci_upper_limit_na_comment": {"type": "string"}, "p_value_description": {"type": "string"}, "method": {"type": "string"}, "method_description": {"type": "string"}, "estimate_description": {"type": "string"}, "groups_description": {"type": "string"}, "other_analysis_description": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "outcome_hash": {"type": "string"}, "outcome_analysis_hash": {"type": "string"}, "group": {"type": "array", "items": {"type": "object", "required": ["external_uuid", "result_group_hash", "outcome_analysis_group_hash"], "properties": {"external_uuid": {"type": "string"}, "ctgov_group_code": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "outcome_analysis_hash": {"type": "string"}, "result_group_hash": {"type": "string"}, "outcome_analysis_group_hash": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}}, "count": {"type": "array", "items": {"type": "object", "required": ["external_uuid", "result_group_hash", "outcome_count_hash"], "properties": {"external_uuid": {"type": "string"}, "ctgov_group_code": {"type": "string"}, "scope": {"type": "string"}, "units": {"type": "string"}, "count": {"type": "integer"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "outcome_hash": {"type": "string"}, "result_group_hash": {"type": "string"}, "outcome_count_hash": {"type": "string"}}, "additionalProperties": false}}, "measurement": {"type": "array", "items": {"type": "object", "required": ["external_uuid", "result_group_hash", "outcome_measurement_hash"], "properties": {"external_uuid": {"type": "string"}, "ctgov_group_code": {"type": "string"}, "classification": {"type": "string"}, "category": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "units": {"type": "string"}, "param_type": {"type": "string"}, "param_value": {"type": "string"}, "param_value_num": {"type": "number"}, "dispersion_type": {"type": "string"}, "dispersion_value": {"type": "string"}, "dispersion_value_num": {"type": "number"}, "dispersion_lower_limit": {"type": "number"}, "dispersion_upper_limit": {"type": "number"}, "explanation_of_na": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}, "outcome_hash": {"type": "string"}, "result_group_hash": {"type": "string"}, "outcome_measurement_hash": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}}, "eligibility": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "population": {"type": "string"}, "sampling_method": {"type": "string"}, "gender": {"type": "string"}, "gender_based": {"type": "boolean"}, "gender_description": {"type": "string"}, "minimum_age": {"type": "string"}, "maximum_age": {"type": "string"}, "healthy_volunteers": {"type": "string"}, "criteria": {"type": "string"}, "inclusion_criteria_parsed": {"type": "string"}, "exclusion_criteria_parsed": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}}, "required": ["external_uuid"]}}, "design": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"external_uuid": {"type": "string"}, "observational_model": {"type": "string"}, "primary_purpose": {"type": "string"}, "intervention_model": {"type": "string"}, "allocation": {"type": "string"}, "masking": {"type": "string"}, "intervention_model_description": {"type": "string"}, "masking_description": {"type": "string"}, "subject_masked": {"type": "boolean"}, "caregiver_masked": {"type": "boolean"}, "investigator_masked": {"type": "boolean"}, "outcomes_assessor_masked": {"type": "boolean"}, "time_perspective": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}}, "required": ["external_uuid"]}}, "participantFlow": {"type": "array", "items": {"type": "object", "required": ["external_uuid"], "properties": {"external_uuid": {"type": "string"}, "recruitment_details": {"type": "string"}, "pre_assignment_details": {"type": "string"}, "effective_date": {"type": "string"}, "collection_source_id": {"type": "integer"}, "h1_clinical_trial_id": {"type": "string"}}, "additionalProperties": false}}, "latest": {"type": "array", "items": {"type": "object", "required": ["effective_date"], "additionalProperties": false, "properties": {"h1_clinical_trial_id": {"type": "string"}, "effective_date": {"type": "string"}}}}}, "required": ["external_uuid"]}}, "required": ["identifiers", "effective_date", "study", "version"]}