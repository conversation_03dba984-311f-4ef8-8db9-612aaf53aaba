import {
  RuleComb<PERSON>Enum,
  RuleFieldEnum,
  RuleOperatorEnum,
  RuleOrRuleGroup,
  RuleTypeEnum,
  TermsQueryType
} from "@h1nyc/search-sdk";
import { RulesOptimizerService } from "./RulesOptimizerService";
import { faker } from "@faker-js/faker";

describe("RulesOptimizerService", () => {
  it("should return root unchanged if passed value is rule", () => {
    const rulesOptimizerService = new RulesOptimizerService();
    const rule: RuleOrRuleGroup = {
      type: RuleTypeEnum.RULE,
      rule: {
        field: RuleFieldEnum.PROCEDURE_CODE,
        value: [faker.datatype.string()],
        operator: RuleOperatorEnum.EQUAL
      }
    };

    const returnedRule = rulesOptimizerService.optimizeRules(rule);

    expect(returnedRule).toEqual(rule);
  });

  describe("OR combinator", () => {
    it("should return same root if ruleGroup has only one rule", () => {
      const rulesOptimizerService = new RulesOptimizerService();
      const value = faker.datatype.string();
      const not = faker.datatype.boolean();
      const ruleGroup: RuleOrRuleGroup = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.OR,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [value],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          ]
        }
      };

      const returnedRuleGroup = rulesOptimizerService.optimizeRules(ruleGroup);

      expect(returnedRuleGroup).toEqual({
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.OR,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [value],
                operator: RuleOperatorEnum.EQUAL,
                termsQueryType: TermsQueryType.TERMS
              }
            }
          ]
        }
      });
    });

    it("should return same root if ruleGroup has more than one rule but for different fields", () => {
      const rulesOptimizerService = new RulesOptimizerService();
      const values = [faker.datatype.string(), faker.datatype.string()];
      const not = faker.datatype.boolean();
      const ruleGroup: RuleOrRuleGroup = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.OR,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [values[1]],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          ]
        }
      };

      const returnedRuleGroup = rulesOptimizerService.optimizeRules(ruleGroup);

      expect(returnedRuleGroup).toEqual({
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.OR,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.EQUAL,
                termsQueryType: TermsQueryType.TERMS
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [values[1]],
                operator: RuleOperatorEnum.EQUAL,
                termsQueryType: TermsQueryType.TERMS
              }
            }
          ]
        }
      });
    });

    it("should return optimized root if ruleGroup has more than one rule for same field", () => {
      const rulesOptimizerService = new RulesOptimizerService();
      const values = [faker.datatype.string(), faker.datatype.string()];
      const not = faker.datatype.boolean();
      const ruleGroup: RuleOrRuleGroup = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.OR,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[1]],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          ]
        }
      };

      const returnedRuleGroup = rulesOptimizerService.optimizeRules(ruleGroup);

      expect(returnedRuleGroup).toEqual({
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.OR,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0], values[1]],
                operator: RuleOperatorEnum.EQUAL,
                termsQueryType: TermsQueryType.TERMS
              }
            }
          ]
        }
      });
    });

    it("should return optimized root if ruleGroup has more than one rule for same field in multilevel", () => {
      const rulesOptimizerService = new RulesOptimizerService();
      const values = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const not = faker.datatype.boolean();
      const ruleGroup: RuleOrRuleGroup = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.OR,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[1]],
                operator: RuleOperatorEnum.EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [values[2]],
                operator: RuleOperatorEnum.NOT_EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE_GROUP,
              ruleGroup: {
                combinator: RuleCombinatorEnum.OR,
                rules: [
                  {
                    type: RuleTypeEnum.RULE,
                    rule: {
                      field: RuleFieldEnum.GENDER,
                      value: [values[3]],
                      operator: RuleOperatorEnum.NOT_EQUAL
                    }
                  },
                  {
                    type: RuleTypeEnum.RULE,
                    rule: {
                      field: RuleFieldEnum.GENDER,
                      value: [values[4]],
                      operator: RuleOperatorEnum.NOT_EQUAL
                    }
                  },
                  {
                    type: RuleTypeEnum.RULE,
                    rule: {
                      field: RuleFieldEnum.GENDER,
                      value: [values[5]],
                      operator: RuleOperatorEnum.NOT_EQUAL
                    }
                  }
                ]
              }
            }
          ]
        }
      };

      const returnedRuleGroup = rulesOptimizerService.optimizeRules(ruleGroup);

      expect(returnedRuleGroup).toEqual({
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.OR,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0], values[1]],
                operator: RuleOperatorEnum.EQUAL,
                termsQueryType: TermsQueryType.TERMS
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [values[2]],
                operator: RuleOperatorEnum.NOT_EQUAL,
                termsQueryType: TermsQueryType.TERMS_SET
              }
            },
            {
              type: RuleTypeEnum.RULE_GROUP,
              ruleGroup: {
                combinator: RuleCombinatorEnum.OR,
                rules: [
                  {
                    type: RuleTypeEnum.RULE,
                    rule: {
                      field: RuleFieldEnum.GENDER,
                      value: [values[3], values[4], values[5]],
                      operator: RuleOperatorEnum.NOT_EQUAL,
                      termsQueryType: TermsQueryType.TERMS_SET
                    }
                  }
                ]
              }
            }
          ]
        }
      });
    });
  });

  describe("AND combinator", () => {
    it("should return same root if ruleGroup has only one rule", () => {
      const rulesOptimizerService = new RulesOptimizerService();
      const value = faker.datatype.string();
      const not = faker.datatype.boolean();
      const ruleGroup: RuleOrRuleGroup = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.AND,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [value],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          ]
        }
      };

      const returnedRuleGroup = rulesOptimizerService.optimizeRules(ruleGroup);

      expect(returnedRuleGroup).toEqual({
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.AND,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [value],
                operator: RuleOperatorEnum.EQUAL,
                termsQueryType: TermsQueryType.TERMS_SET
              }
            }
          ]
        }
      });
    });

    it("should return same root if ruleGroup has more than one rule but for different fields", () => {
      const rulesOptimizerService = new RulesOptimizerService();
      const values = [faker.datatype.string(), faker.datatype.string()];
      const not = faker.datatype.boolean();
      const ruleGroup: RuleOrRuleGroup = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.AND,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [values[1]],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          ]
        }
      };

      const returnedRuleGroup = rulesOptimizerService.optimizeRules(ruleGroup);

      expect(returnedRuleGroup).toEqual({
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.AND,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.EQUAL,
                termsQueryType: TermsQueryType.TERMS_SET
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [values[1]],
                operator: RuleOperatorEnum.EQUAL,
                termsQueryType: TermsQueryType.TERMS_SET
              }
            }
          ]
        }
      });
    });

    it("should return optimized root if ruleGroup has more than one rule for same field", () => {
      const rulesOptimizerService = new RulesOptimizerService();
      const values = [faker.datatype.string(), faker.datatype.string()];
      const not = faker.datatype.boolean();
      const ruleGroup: RuleOrRuleGroup = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.AND,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[1]],
                operator: RuleOperatorEnum.EQUAL
              }
            }
          ]
        }
      };

      const returnedRuleGroup = rulesOptimizerService.optimizeRules(ruleGroup);

      expect(returnedRuleGroup).toEqual({
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.AND,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0], values[1]],
                operator: RuleOperatorEnum.EQUAL,
                termsQueryType: TermsQueryType.TERMS_SET
              }
            }
          ]
        }
      });
    });

    it("should return optimized root if ruleGroup has more than one rule for same field in multilevel", () => {
      const rulesOptimizerService = new RulesOptimizerService();
      const values = [
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string(),
        faker.datatype.string()
      ];
      const not = faker.datatype.boolean();
      const ruleGroup: RuleOrRuleGroup = {
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.AND,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0]],
                operator: RuleOperatorEnum.EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[1]],
                operator: RuleOperatorEnum.EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [values[2]],
                operator: RuleOperatorEnum.NOT_EQUAL
              }
            },
            {
              type: RuleTypeEnum.RULE_GROUP,
              ruleGroup: {
                combinator: RuleCombinatorEnum.AND,
                rules: [
                  {
                    type: RuleTypeEnum.RULE,
                    rule: {
                      field: RuleFieldEnum.GENDER,
                      value: [values[3]],
                      operator: RuleOperatorEnum.NOT_EQUAL
                    }
                  },
                  {
                    type: RuleTypeEnum.RULE,
                    rule: {
                      field: RuleFieldEnum.GENDER,
                      value: [values[4]],
                      operator: RuleOperatorEnum.NOT_EQUAL
                    }
                  },
                  {
                    type: RuleTypeEnum.RULE,
                    rule: {
                      field: RuleFieldEnum.GENDER,
                      value: [values[5]],
                      operator: RuleOperatorEnum.NOT_EQUAL
                    }
                  }
                ]
              }
            }
          ]
        }
      };

      const returnedRuleGroup = rulesOptimizerService.optimizeRules(ruleGroup);

      expect(returnedRuleGroup).toEqual({
        type: RuleTypeEnum.RULE_GROUP,
        ruleGroup: {
          combinator: RuleCombinatorEnum.AND,
          not,
          rules: [
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.DIAGNOSES_CODE,
                value: [values[0], values[1]],
                operator: RuleOperatorEnum.EQUAL,
                termsQueryType: TermsQueryType.TERMS_SET
              }
            },
            {
              type: RuleTypeEnum.RULE,
              rule: {
                field: RuleFieldEnum.PROCEDURE_CODE,
                value: [values[2]],
                operator: RuleOperatorEnum.NOT_EQUAL,
                termsQueryType: TermsQueryType.TERMS
              }
            },
            {
              type: RuleTypeEnum.RULE_GROUP,
              ruleGroup: {
                combinator: RuleCombinatorEnum.AND,
                rules: [
                  {
                    type: RuleTypeEnum.RULE,
                    rule: {
                      field: RuleFieldEnum.GENDER,
                      value: [values[3], values[4], values[5]],
                      operator: RuleOperatorEnum.NOT_EQUAL,
                      termsQueryType: TermsQueryType.TERMS
                    }
                  }
                ]
              }
            }
          ]
        }
      });
    });
  });
});
