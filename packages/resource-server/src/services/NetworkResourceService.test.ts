import { faker } from "@faker-js/faker";
import * as _ from "lodash";

import {
  Affiliation,
  CollaboratorCountAggregation,
  CollaboratorIdsAggregation,
  DocCountBucket,
  HCPDocument,
  NetworkResponseAdapterService
} from "./NetworkResponseAdapterService";
import {
  NetworkFeatureFlags,
  NetworkResourceService,
  featureFlagDefaults,
  networkFeatureFlagTypes
} from "./NetworkResourceService";
import { ElasticSearchService } from "./ElasticSearchService";
import { ConfigService } from "./ConfigService";
import { createMockInstance } from "../util/TestUtils";
import { QueryParserService } from "./QueryParserService";
import {
  NetworkQueryBuilder,
  NetworkNestedDocPath as NestedDocPath
} from "./queryBuilders/NetworkQueryBuilder";
import {
  SearchHit,
  SearchResponse,
  AggregationsTermsAggregateBase
} from "@elastic/elasticsearch/lib/api/types";
import {
  CollaborationTypeFilter,
  LocationFilter,
  NetworkFilterAutocompleteField,
  NetworkFilters
} from "@h1nyc/search-sdk";
import { ENGLISH } from "./LanguageDetectService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { LDFlagsState } from "launchdarkly-node-server-sdk";
import { when } from "jest-when";

function generateMockHCPHit(overrides = {}): SearchHit<HCPDocument> {
  const numAffiliations = faker.datatype.number(5);
  const hcp = {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: {
      id: faker.datatype.uuid(),
      affiliations: Array.from(new Array(numAffiliations), () => {
        return {
          type: faker.datatype.string(),
          isCurrent: faker.datatype.boolean(),
          institution: {
            id: faker.datatype.uuid(),
            ultimateParentId: faker.datatype.uuid(),
            name: faker.company.name()
          }
        };
      })
    }
  };

  return _.merge(hcp, overrides);
}

function generateMockElasticsearchResponse<T>(
  hits: Array<SearchHit<T>> = [],
  aggs: Record<
    string,
    CollaboratorCountAggregation | CollaboratorIdsAggregation
  > = {
    ...generateMockAggregation("trials"),
    ...generateMockAggregation("congress"),
    ...generateMockAggregation("publications")
  }
): SearchResponse<T> {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: hits.length,
        relation: "eq"
      },
      max_score: faker.datatype.number(),
      hits
    },
    aggregations: aggs
  };
}

function generateMockAggregation(
  bucket: NestedDocPath,
  peopleDocCount = faker.datatype.number()
): Record<string, CollaboratorCountAggregation> {
  return {
    [bucket]: {
      nested: {
        people: {
          doc_count: peopleDocCount
        }
      }
    }
  };
}

function generateMockTotalCollaboratorAggregation(
  peopleDocCount = faker.datatype.number()
): Record<string, CollaboratorIdsAggregation> {
  return {
    total_collaborators: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: faker.datatype.string(),
          doc_count: peopleDocCount
        }
      ]
    }
  };
}

function generateMockAggregates(
  buckets: Array<DocCountBucket> = []
): AggregationsTermsAggregateBase<DocCountBucket> {
  return {
    doc_count_error_upper_bound: faker.datatype.number(),
    sum_other_doc_count: faker.datatype.number(),
    buckets
  };
}

export function generateNetworkFilters(
  overrides: NetworkFilters = {},
  collaborationTypeOverrides: CollaborationTypeFilter = {},
  locationOverrides: LocationFilter = {}
): NetworkFilters {
  const filters: NetworkFilters = {
    collaborationType: {
      trials: true,
      congresses: true,
      publications: true,
      ...collaborationTypeOverrides
    },
    keyword: "",
    collaboratorLocation: locationOverrides
  };

  return {
    ...filters,
    ...overrides
  };
}

const ENGLISH_FULL = "english";
const LOCATION_FILTER_AUTOCOMPLETE_FIELDS = [
  NetworkFilterAutocompleteField.CITY,
  NetworkFilterAutocompleteField.COUNTRY,
  NetworkFilterAutocompleteField.REGION,
  NetworkFilterAutocompleteField.POSTAL_CODE
];

const featureFlagsService = createMockInstance(
  FeatureFlagsService as any
) as FeatureFlagsService;

function generateFeatureFlagsState(featureFlagValues: NetworkFeatureFlags) {
  const getFlagValue = jest.fn();

  for (const flag of networkFeatureFlagTypes) {
    const flagKey = featureFlagDefaults[flag].key;
    when(getFlagValue)
      .calledWith(flagKey)
      .mockReturnValue(featureFlagValues[flag]);
  }

  return {
    getFlagValue
  } as unknown as LDFlagsState;
}

const FEATURE_FLAG_DEFAULTS: NetworkFeatureFlags = {
  enableBrazilianClaims: true
};

beforeEach(() => {
  featureFlagsService.getAllFlags = jest
    .fn()
    .mockResolvedValue(generateFeatureFlagsState(FEATURE_FLAG_DEFAULTS));
});

describe("NetworkResourceService", () => {
  describe("findNetwork", () => {
    describe("negative tests", () => {
      it("should re-throw an error thrown by queryParserService.parseQueryWithQueryUnderstandingService", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchError = new Error(
          "elasticsearch threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(elasticSearchError);

        const queryParserService = createMockInstance(QueryParserService);

        const queryParserServiceError = new Error(
          "queryParserService.parseQueryWithQueryUnderstandingService threw an error for some reason"
        );
        queryParserService.parseQueryWithQueryUnderstandingService.mockRejectedValue(
          queryParserServiceError
        );

        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        try {
          await networkResourceService.findNetwork({
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH_FULL
          });
          fail("an error should have been thrown");
        } catch (err) {
          expect(err).toEqual(queryParserServiceError);
        }
      });

      it("should re-throw an error thrown by elasticSearchService.query", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchError = new Error(
          "elasticsearch threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(elasticSearchError);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        try {
          await networkResourceService.findNetwork({
            personId,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH_FULL
          });
          fail("an error should have been thrown");
        } catch (err) {
          expect(err).toEqual(elasticSearchError);
        }
      });
    });

    it("elasticsearch response with empty hits should return 0 total hits and empty collaborators", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockResponse = generateMockElasticsearchResponse([], {
        ...generateMockAggregation("trials", 0),
        ...generateMockAggregation("congress", 0),
        ...generateMockAggregation("publications", 0)
      });
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      queryBuilder.buildFindNetworkQuery.mockReturnValue({});

      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters();
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      const network = await networkResourceService.findNetwork({
        personId,
        dateRange,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH_FULL
      });

      expect(network).toEqual(
        networkResponseAdapterService.emptyNetworkResponse()
      );
      expect(
        queryParserService.parseQueryWithQueryUnderstandingService
      ).toHaveBeenCalled();
      expect(queryBuilder.buildFindNetworkQuery).toHaveBeenCalled();
    });

    it("should return an empty response if all collaboration types should be filtered out", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockResponse = generateMockElasticsearchResponse();
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      queryBuilder.buildFindNetworkQuery.mockReturnValue(null);
      queryBuilder.buildFindAffiliationsQuery.mockReturnValue({});

      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );
      networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue([]);

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: false,
          congresses: false,
          publications: false
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      const request = {
        personId,
        dateRange,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH_FULL
      };

      const network = await networkResourceService.findNetwork(request);

      expect(network).toEqual(
        networkResponseAdapterService.emptyNetworkResponse()
      );
      expect(
        queryParserService.parseQueryWithQueryUnderstandingService
      ).toHaveBeenCalled();
      expect(queryBuilder.buildFindNetworkQuery).toHaveBeenCalledWith(
        {
          ...request,
          language: ENGLISH
        },
        [],
        undefined,
        expect.any(Function),
        FEATURE_FLAG_DEFAULTS
      );
    });

    it("should use the query builder to build the findNetwork query and then use the adapter service to adapt the response to a NetworkResponse object", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockHCP = generateMockHCPHit();
      const mockResponse = generateMockElasticsearchResponse([mockHCP]);
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      queryBuilder.buildFindNetworkQuery.mockReturnValue({});
      queryBuilder.buildFindAffiliationsQuery.mockReturnValue({});

      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );
      networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue([]);

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters();
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      const request = {
        personId,
        dateRange,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH_FULL
      };

      await networkResourceService.findNetwork(request);

      expect(networkResponseAdapterService.adaptToNetwork).toHaveBeenCalledWith(
        request,
        mockResponse.hits,
        mockResponse.aggregations,
        ENGLISH
      );
      expect(queryBuilder.buildFindNetworkQuery).toHaveBeenCalledWith(
        {
          ...request,
          language: ENGLISH
        },
        [],
        undefined,
        expect.any(Function),
        FEATURE_FLAG_DEFAULTS
      );
    });

    it("should get affiliation IDs for the current HCP and use the IDs to build the findNetwork query", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockHCP = generateMockHCPHit();
      const mockResponse = generateMockElasticsearchResponse([mockHCP]);
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      const parsedQueryTree = faker.datatype.string();
      queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
        {
          parsedQueryTree,
          synonyms: []
        }
      );

      queryBuilder.buildFindNetworkQuery.mockReturnValue({});
      queryBuilder.buildFindAffiliationsQuery.mockReturnValue({});

      const affiliationIds = mockHCP._source!.affiliations!.map(
        (affiliation: Affiliation) => {
          return affiliation.institution.id;
        }
      );
      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );
      networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue(
        affiliationIds
      );

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters();
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      const request = {
        personId,
        dateRange,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH_FULL
      };

      await networkResourceService.findNetwork(request);

      expect(
        networkResponseAdapterService.adaptToAffiliationsList
      ).toHaveBeenCalledWith(mockResponse.hits);
      expect(queryBuilder.buildFindNetworkQuery).toHaveBeenCalledWith(
        {
          ...request,
          language: ENGLISH
        },
        affiliationIds,
        parsedQueryTree,
        expect.any(Function),
        FEATURE_FLAG_DEFAULTS
      );
    });

    describe("anomalous request parameter values", () => {
      it("should treat missing dateRange.min as not supplied", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const mockResponse = generateMockElasticsearchResponse([], {
          ...generateMockAggregation("trials", 0),
          ...generateMockAggregation("congress", 0),
          ...generateMockAggregation("publications", 0)
        });
        elasticSearchService.query.mockResolvedValue(mockResponse);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        const parsedQueryTree = faker.datatype.string();
        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree,
            synonyms: []
          }
        );

        queryBuilder.buildFindNetworkQuery.mockReturnValue({});

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );
        networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue(
          []
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH_FULL
        };

        await networkResourceService.findNetwork(request);

        expect(queryBuilder.buildFindNetworkQuery).toHaveBeenCalledWith(
          {
            ...request,
            language: ENGLISH
          },
          [],
          parsedQueryTree,
          expect.any(Function),
          FEATURE_FLAG_DEFAULTS
        );
      });

      it("should treat missing filters as not supplied", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const mockResponse = generateMockElasticsearchResponse([], {
          ...generateMockAggregation("trials", 0),
          ...generateMockAggregation("congress", 0),
          ...generateMockAggregation("publications", 0)
        });
        elasticSearchService.query.mockResolvedValue(mockResponse);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        const parsedQueryTree = faker.datatype.string();
        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree,
            synonyms: []
          }
        );

        queryBuilder.buildFindNetworkQuery.mockReturnValue({});

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );
        networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue(
          []
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = {};
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH_FULL
        };

        await networkResourceService.findNetwork(request);

        expect(queryBuilder.buildFindNetworkQuery).toHaveBeenCalledWith(
          {
            ...request,
            language: ENGLISH
          },
          [],
          parsedQueryTree,
          expect.any(Function),
          FEATURE_FLAG_DEFAULTS
        );
      });

      it("should call queryParserService with empty string query when request supplied keyword is empty", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const mockResponse = generateMockElasticsearchResponse();
        elasticSearchService.query.mockResolvedValue(mockResponse);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          undefined
        );

        queryBuilder.buildFindNetworkQuery.mockReturnValue({});

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const request = {
          personId,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH_FULL
        };

        await networkResourceService.findNetwork(request);

        expect(
          queryParserService.parseQueryWithQueryUnderstandingService
        ).toHaveBeenCalledWith("", {
          projectSupportsAdvancedOperators: true
        });
      });
    });
  });

  describe("findBulkNetwork", () => {
    describe("negative tests", () => {
      it("should re-throw an error thrown by queryParserService.parseQueryWithQueryUnderstandingService", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchError = new Error(
          "elasticsearch threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(elasticSearchError);

        const queryParserService = createMockInstance(QueryParserService);

        const queryParserServiceError = new Error(
          "queryParserService.parseQueryWithQueryUnderstandingService threw an error for some reason"
        );
        queryParserService.parseQueryWithQueryUnderstandingService.mockRejectedValue(
          queryParserServiceError
        );

        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personIds = [faker.datatype.uuid(), faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        try {
          await networkResourceService.findBulkNetwork({
            personIds,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH_FULL
          });
          fail("an error should have been thrown");
        } catch (err) {
          expect(err).toEqual(queryParserServiceError);
        }
      });

      it("should re-throw an error thrown by elasticSearchService.query", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchError = new Error(
          "elasticsearch threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(elasticSearchError);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personIds = [faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        try {
          await networkResourceService.findBulkNetwork({
            personIds,
            dateRange,
            page,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH_FULL
          });
          fail("an error should have been thrown");
        } catch (err) {
          expect(err).toEqual(elasticSearchError);
        }
      });
    });

    it("elasticsearch response with empty hits should return 0 total hits and empty collaborators", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockResponse = generateMockElasticsearchResponse([], {
        ...generateMockAggregation("trials", 0),
        ...generateMockAggregation("congress", 0),
        ...generateMockAggregation("publications", 0),
        ...generateMockTotalCollaboratorAggregation(0)
      });
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      queryBuilder.buildFindNetworkQuery.mockReturnValue({});

      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personIds = [faker.datatype.uuid()];
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters();
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      const network = await networkResourceService.findBulkNetwork({
        personIds,
        dateRange,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH_FULL
      });

      expect(network).toEqual([
        {
          totalCollaborators: 0,
          totalCongressCollaborators: 0,
          totalPublicationCollaborators: 0,
          totalTrialCollaborators: 0
        }
      ]);
      expect(
        queryParserService.parseQueryWithQueryUnderstandingService
      ).toHaveBeenCalled();
      expect(queryBuilder.buildBulkFindNetworkQuery).toHaveBeenCalled();
    });

    it("should return an empty response if all collaboration types should be filtered out", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockResponse = generateMockElasticsearchResponse();
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      queryBuilder.buildFindNetworkQuery.mockReturnValue(null);
      queryBuilder.buildFindAffiliationsQuery.mockReturnValue({});

      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );
      networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue([]);

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personIds = [faker.datatype.uuid()];
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters({
        collaborationType: {
          trials: false,
          congresses: false,
          publications: false
        }
      });
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      const request = {
        personIds,
        dateRange,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH_FULL
      };

      const network = await networkResourceService.findBulkNetwork(request);

      expect(network).toEqual([
        {
          totalCollaborators: 0,
          totalCongressCollaborators: 0,
          totalPublicationCollaborators: 0,
          totalTrialCollaborators: 0
        }
      ]);
      expect(
        queryParserService.parseQueryWithQueryUnderstandingService
      ).toHaveBeenCalled();
      expect(queryBuilder.buildBulkFindNetworkQuery).toHaveBeenCalledWith(
        {
          ...request,
          language: ENGLISH
        },
        [],
        undefined,
        expect.any(Function),
        FEATURE_FLAG_DEFAULTS
      );
    });

    it("should use the query builder to build the findBulkNetwork query and then use the adapter service to adapt the response to a NetworkResponse object", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockHCP = generateMockHCPHit();
      const mockResponse = generateMockElasticsearchResponse([mockHCP]);
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      queryBuilder.buildBulkFindNetworkQuery.mockReturnValue({});
      queryBuilder.buildFindAffiliationsQuery.mockReturnValue({});

      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );
      networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue([]);

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personIds = [faker.datatype.uuid()];
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters();
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      const request = {
        personIds,
        dateRange,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH_FULL
      };

      await networkResourceService.findBulkNetwork(request);

      expect(
        networkResponseAdapterService.adaptToBulkNetwork
      ).toHaveBeenCalledWith(personIds, mockResponse.aggregations);
      expect(queryBuilder.buildBulkFindNetworkQuery).toHaveBeenCalledWith(
        {
          ...request,
          language: ENGLISH
        },
        [],
        undefined,
        expect.any(Function),
        FEATURE_FLAG_DEFAULTS
      );
    });

    it("should get affiliation IDs for the current HCP and use the IDs to build the findBulkNetwork query", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockHCP = generateMockHCPHit();
      const mockResponse = generateMockElasticsearchResponse([mockHCP]);
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      const parsedQueryTree = faker.datatype.string();
      queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
        {
          parsedQueryTree,
          synonyms: []
        }
      );

      queryBuilder.buildBulkFindNetworkQuery.mockReturnValue({});
      queryBuilder.buildFindAffiliationsQuery.mockReturnValue({});

      const affiliationIds = mockHCP._source!.affiliations!.map(
        (affiliation: Affiliation) => {
          return affiliation.institution.id;
        }
      );
      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );
      networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue(
        affiliationIds
      );

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personIds = [faker.datatype.uuid()];
      const dateRange = {};
      const page = {
        offset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters();
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      const request = {
        personIds,
        dateRange,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH_FULL
      };

      await networkResourceService.findBulkNetwork(request);

      expect(
        networkResponseAdapterService.adaptToAffiliationsList
      ).toHaveBeenCalledWith(mockResponse.hits);
      expect(queryBuilder.buildBulkFindNetworkQuery).toHaveBeenCalledWith(
        {
          ...request,
          language: ENGLISH
        },
        affiliationIds,
        parsedQueryTree,
        expect.any(Function),
        FEATURE_FLAG_DEFAULTS
      );
    });

    describe("anomalous request parameter values", () => {
      it("should treat missing dateRange.min as not supplied", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const mockResponse = generateMockElasticsearchResponse([], {
          ...generateMockAggregation("trials", 0),
          ...generateMockAggregation("congress", 0),
          ...generateMockAggregation("publications", 0),
          ...generateMockTotalCollaboratorAggregation(0)
        });
        elasticSearchService.query.mockResolvedValue(mockResponse);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        const parsedQueryTree = faker.datatype.string();
        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree,
            synonyms: []
          }
        );

        queryBuilder.buildBulkFindNetworkQuery.mockReturnValue({});

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );
        networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue(
          []
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personIds = [faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const request = {
          personIds,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH_FULL
        };

        await networkResourceService.findBulkNetwork(request);

        expect(queryBuilder.buildBulkFindNetworkQuery).toHaveBeenCalledWith(
          {
            ...request,
            language: ENGLISH
          },
          [],
          parsedQueryTree,
          expect.any(Function),
          FEATURE_FLAG_DEFAULTS
        );
      });

      it("should treat missing filters as not supplied", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const mockResponse = generateMockElasticsearchResponse([], {
          ...generateMockAggregation("trials", 0),
          ...generateMockAggregation("congress", 0),
          ...generateMockAggregation("publications", 0),
          ...generateMockTotalCollaboratorAggregation(0)
        });
        elasticSearchService.query.mockResolvedValue(mockResponse);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        const parsedQueryTree = faker.datatype.string();
        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          {
            parsedQueryTree,
            synonyms: []
          }
        );

        queryBuilder.buildBulkFindNetworkQuery.mockReturnValue({});

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );
        networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue(
          []
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personIds = [faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = {};
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const request = {
          personIds,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH_FULL
        };

        await networkResourceService.findBulkNetwork(request);

        expect(queryBuilder.buildBulkFindNetworkQuery).toHaveBeenCalledWith(
          {
            ...request,
            language: ENGLISH
          },
          [],
          parsedQueryTree,
          expect.any(Function),
          FEATURE_FLAG_DEFAULTS
        );
      });

      it("should call queryParserService with empty string query when request supplied keyword is empty", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const mockResponse = generateMockElasticsearchResponse();
        elasticSearchService.query.mockResolvedValue(mockResponse);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
          undefined
        );

        queryBuilder.buildBulkFindNetworkQuery.mockReturnValue({});

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personIds = [faker.datatype.uuid()];
        const dateRange = {};
        const page = {
          offset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        const request = {
          personIds,
          dateRange,
          page,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH_FULL
        };

        await networkResourceService.findBulkNetwork(request);

        expect(
          queryParserService.parseQueryWithQueryUnderstandingService
        ).toHaveBeenCalledWith("", {
          projectSupportsAdvancedOperators: true
        });
      });
    });
  });

  describe("findCollaborator", () => {
    describe("negative tests", () => {
      it("should re-throw an error thrown by queryParserService.parseQueryWithQueryUnderstandingService", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchError = new Error(
          "elasticsearch threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(elasticSearchError);

        const queryParserService = createMockInstance(QueryParserService);

        const queryParserServiceError = new Error(
          "queryParserService.parseQueryWithQueryUnderstandingService threw an error for some reason"
        );
        queryParserService.parseQueryWithQueryUnderstandingService.mockRejectedValue(
          queryParserServiceError
        );

        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personId = faker.datatype.uuid();
        const collaboratorId = faker.datatype.uuid();
        const page = {
          trialOffset: faker.datatype.number(),
          congressOffset: faker.datatype.number(),
          publicationOffset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = generateNetworkFilters();
        const projectFeatures = {
          advancedOperators: true
        };

        try {
          await networkResourceService.findCollaborator({
            personId,
            collaboratorId,
            page,
            suppliedFilters,
            projectFeatures,
            language: ENGLISH_FULL
          });
          fail("an error should have been thrown");
        } catch (err) {
          expect(err).toEqual(queryParserServiceError);
        }
      });

      it("should re-throw an error thrown by elasticSearchService.query", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchError = new Error(
          "elasticsearch threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(elasticSearchError);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personId = faker.datatype.uuid();
        const collaboratorId = faker.datatype.uuid();
        const page = {
          trialOffset: faker.datatype.number(),
          congressOffset: faker.datatype.number(),
          publicationOffset: faker.datatype.number(),
          limit: faker.datatype.number()
        };
        const suppliedFilters = generateNetworkFilters();
        const projectFeatures = {
          advancedOperators: true
        };

        try {
          await networkResourceService.findCollaborator({
            personId,
            collaboratorId,
            page,
            suppliedFilters,
            projectFeatures,
            language: ENGLISH_FULL
          });
          fail("an error should have been thrown");
        } catch (err) {
          expect(err).toEqual(elasticSearchError);
        }
      });
    });

    it("should get affiliation IDs for the current HCP and use the IDs to build the findCollaborator query", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockHCP = generateMockHCPHit();
      const mockResponse = generateMockElasticsearchResponse([mockHCP]);
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      const parsedQueryTree = faker.datatype.string();
      queryParserService.parseQueryWithQueryUnderstandingService.mockResolvedValue(
        {
          parsedQueryTree,
          synonyms: []
        }
      );

      queryBuilder.buildFindCollaboratorQuery.mockReturnValue({});
      queryBuilder.buildFindAffiliationsQuery.mockReturnValue({});

      const affiliationIds = mockHCP._source!.affiliations!.map(
        (affiliation: Affiliation) => {
          return affiliation.institution.id;
        }
      );
      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );
      networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue(
        affiliationIds
      );

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personId = faker.datatype.uuid();
      const collaboratorId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        trialOffset: faker.datatype.number(),
        congressOffset: faker.datatype.number(),
        publicationOffset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters();
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      const request = {
        personId,
        collaboratorId,
        dateRange,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH_FULL
      };

      await networkResourceService.findCollaborator(request);

      expect(
        networkResponseAdapterService.adaptToAffiliationsList
      ).toHaveBeenCalledWith(mockResponse.hits);
      expect(queryBuilder.buildFindCollaboratorQuery).toHaveBeenCalledWith(
        request,
        affiliationIds,
        parsedQueryTree,
        expect.any(Function)
      );
    });

    it("should use the query builder to build the findCollaborator query and then use the adapter service to adapt the response to a collaborator", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockHCP = generateMockHCPHit();
      const mockResponse = generateMockElasticsearchResponse([mockHCP]);
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      queryBuilder.buildFindCollaboratorQuery.mockReturnValue({});
      queryBuilder.buildFindAffiliationsQuery.mockReturnValue({});

      const collaboratorId = faker.datatype.uuid();
      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );
      networkResponseAdapterService.adaptToAffiliationsList.mockReturnValue([]);
      networkResponseAdapterService.adaptToCollaborator.mockResolvedValue({
        id: faker.datatype.uuid(),
        personId: collaboratorId,
        totalSharedWorks: 0,
        totalSharedTrials: 0,
        totalSharedCongresses: 0,
        totalSharedPublications: 0,
        sharedAffiliations: [],
        sharedWorks: [],
        trialOffset: 0,
        congressOffset: 0,
        publicationOffset: 0
      });

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const page = {
        trialOffset: faker.datatype.number(),
        congressOffset: faker.datatype.number(),
        publicationOffset: faker.datatype.number(),
        limit: faker.datatype.number()
      };
      const suppliedFilters = generateNetworkFilters();
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      const request = {
        personId,
        collaboratorId,
        dateRange,
        page,
        suppliedFilters,
        projectId,
        projectFeatures,
        language: ENGLISH_FULL
      };

      await networkResourceService.findCollaborator(request);

      expect(
        networkResponseAdapterService.adaptToCollaborator
      ).toHaveBeenCalledWith(request, mockResponse.hits, page, ENGLISH);
      expect(queryBuilder.buildFindCollaboratorQuery).toHaveBeenCalledWith(
        request,
        [],
        undefined,
        expect.any(Function)
      );
    });
  });

  describe("filterAutocomplete", () => {
    describe("negative tests", () => {
      it("should re-throw an error thrown by queryParserService.parseQueryWithQueryUnderstandingService", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchError = new Error(
          "elasticsearch threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(elasticSearchError);

        const queryParserService = createMockInstance(QueryParserService);

        const queryParserServiceError = new Error(
          "queryParserService.parseQueryWithQueryUnderstandingService threw an error for some reason"
        );
        queryParserService.parseQueryWithQueryUnderstandingService.mockRejectedValue(
          queryParserServiceError
        );

        const queryBuilder = createMockInstance(NetworkQueryBuilder);
        queryBuilder.buildLocationFilterAutocomplete.mockReturnValue({});

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        for (const filterField of Object.values(
          NetworkFilterAutocompleteField
        )) {
          const filterFieldValue = Number(filterField);
          if (isNaN(filterFieldValue)) {
            continue;
          }

          try {
            await networkResourceService.filterAutocomplete({
              personId,
              dateRange,
              filterField: filterFieldValue,
              filterValue: "",
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH_FULL
            });
            fail("an error should have been thrown");
          } catch (err) {
            expect(err).toEqual(queryParserServiceError);
          }
        }
      });

      it("should re-throw an error thrown by elasticSearchService.query", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);
        const elasticSearchError = new Error(
          "elasticsearch threw an error for some reason"
        );
        elasticSearchService.query.mockRejectedValue(elasticSearchError);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);
        queryBuilder.buildLocationFilterAutocomplete.mockReturnValue({});

        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        for (const filterField of Object.values(
          NetworkFilterAutocompleteField
        )) {
          const filterFieldValue = Number(filterField);
          if (isNaN(filterFieldValue)) {
            continue;
          }

          try {
            await networkResourceService.filterAutocomplete({
              personId,
              dateRange,
              filterField: filterFieldValue,
              filterValue: "",
              suppliedFilters,
              projectId,
              projectFeatures,
              language: ENGLISH_FULL
            });
            fail("an error should have been thrown");
          } catch (err) {
            expect(err).toEqual(elasticSearchError);
          }
        }
      });

      it("should return an empty response if NetworkQueryBuilder.buildLocationFilterAutocomplete returns null", async () => {
        const configService = createMockInstance(ConfigService);
        configService.elasticPeopleIndex = faker.datatype.string();

        const elasticSearchService = createMockInstance(ElasticSearchService);

        const mockAggs = {
          nested: {
            filtered_matching: {
              matching: generateMockAggregates()
            }
          }
        };
        const mockResponse: SearchResponse = {
          took: faker.datatype.number(),
          timed_out: false,
          _shards: {
            total: faker.datatype.number(),
            successful: faker.datatype.number(),
            skipped: faker.datatype.number(),
            failed: faker.datatype.number()
          },
          hits: {
            total: {
              value: faker.datatype.number(),
              relation: "eq"
            },
            max_score: 0,
            hits: []
          },
          aggregations: mockAggs
        };
        elasticSearchService.query.mockResolvedValue(mockResponse);

        const queryParserService = createMockInstance(QueryParserService);
        const queryBuilder = createMockInstance(NetworkQueryBuilder);

        queryBuilder.buildLocationFilterAutocomplete.mockReturnValue(null);

        const collaboratorId = faker.datatype.uuid();
        const networkResponseAdapterService = createMockInstance(
          NetworkResponseAdapterService
        );

        const networkResourceService = new NetworkResourceService(
          configService,
          elasticSearchService,
          queryParserService,
          queryBuilder,
          networkResponseAdapterService,
          featureFlagsService
        );

        const personId = faker.datatype.uuid();
        const dateRange = {};
        const suppliedFilters = generateNetworkFilters();
        const projectId = faker.datatype.string();
        const projectFeatures = {
          advancedOperators: true
        };

        for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
          const request = {
            personId,
            collaboratorId,
            dateRange,
            suppliedFilters,
            projectId,
            projectFeatures,
            language: ENGLISH_FULL,
            filterField,
            filterValue: faker.random.word()
          };

          const response = await networkResourceService.filterAutocomplete(
            request
          );

          expect(response).toEqual([]);
          expect(
            networkResponseAdapterService.adaptToFilterAggregations
          ).not.toHaveBeenCalled();
          expect(
            queryBuilder.buildLocationFilterAutocomplete
          ).toHaveBeenCalledWith(
            request,
            undefined,
            expect.any(Function),
            FEATURE_FLAG_DEFAULTS
          );
        }
      });
    });

    it("should use the query builder to build the filterAutocomplete query and then use the adapter service to adapt the response to filter aggregations", async () => {
      const configService = createMockInstance(ConfigService);
      configService.elasticPeopleIndex = faker.datatype.string();

      const elasticSearchService = createMockInstance(ElasticSearchService);

      const mockAggs = {
        nested: {
          filtered_matching: {
            matching: generateMockAggregates()
          }
        }
      };
      const mockResponse: SearchResponse = {
        took: faker.datatype.number(),
        timed_out: false,
        _shards: {
          total: faker.datatype.number(),
          successful: faker.datatype.number(),
          skipped: faker.datatype.number(),
          failed: faker.datatype.number()
        },
        hits: {
          total: {
            value: faker.datatype.number(),
            relation: "eq"
          },
          max_score: 0,
          hits: []
        },
        aggregations: mockAggs
      };
      elasticSearchService.query.mockResolvedValue(mockResponse);

      const queryParserService = createMockInstance(QueryParserService);
      const queryBuilder = createMockInstance(NetworkQueryBuilder);

      queryBuilder.buildLocationFilterAutocomplete.mockReturnValue({});

      const collaboratorId = faker.datatype.uuid();
      const networkResponseAdapterService = createMockInstance(
        NetworkResponseAdapterService
      );

      const networkResourceService = new NetworkResourceService(
        configService,
        elasticSearchService,
        queryParserService,
        queryBuilder,
        networkResponseAdapterService,
        featureFlagsService
      );

      const personId = faker.datatype.uuid();
      const dateRange = {};
      const suppliedFilters = generateNetworkFilters();
      const projectId = faker.datatype.string();
      const projectFeatures = {
        advancedOperators: true
      };

      for (const filterField of LOCATION_FILTER_AUTOCOMPLETE_FIELDS) {
        const request = {
          personId,
          collaboratorId,
          dateRange,
          suppliedFilters,
          projectId,
          projectFeatures,
          language: ENGLISH_FULL,
          filterField,
          filterValue: faker.random.word()
        };

        await networkResourceService.filterAutocomplete(request);

        expect(
          networkResponseAdapterService.adaptToFilterAggregations
        ).toHaveBeenCalledWith(mockAggs, filterField);
        expect(
          queryBuilder.buildLocationFilterAutocomplete
        ).toHaveBeenCalledWith(
          request,
          undefined,
          expect.any(Function),
          FEATURE_FLAG_DEFAULTS
        );
      }
    });
  });
});
