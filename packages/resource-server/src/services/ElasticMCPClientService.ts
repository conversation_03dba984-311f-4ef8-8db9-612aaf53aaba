import { Service } from "typedi";
import { AzureClientOptions, AzureOpenAI } from "openai";
import { ChatCompletionMessageParam } from "openai/resources/index";

import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";

import { CLIENT_CONFIG } from "../lib/MCPClient";
import { ConfigService } from "./ConfigService";
import { OpenAIChatAdaptor } from "./OpenAIChatAdaptorService";

const readEnvVars = (): Record<string, string> => {
  const env: Record<string, string> = {};

  for (const key in process.env) {
    const val = process.env[key];
    if (!val) continue;
    env[key] = val;
  }

  return env;
};

@Service()
export class ElasticMCPClientService {
  private readonly client: Client;
  private openAiAdaptor: OpenAIChatAdaptor;
  private llm: AzureOpenAI;
  private model: string;

  constructor(config: ConfigService) {
    this.client = new Client({
      name: CLIENT_CONFIG.name,
      version: CLIENT_CONFIG.version
    });

    const opts: AzureClientOptions = {
      endpoint: config.azureOpenAiEndpoint,
      apiKey: config.azureOpenAiApiKey,
      deployment: config.azureOpenAiDeploymentName,
      apiVersion: config.azureOpenAiApiVersion
    };

    this.llm = new AzureOpenAI(opts);
    this.model = config.azureOpenAiDeploymentName;
  }

  async getClient() {
    const envVars = readEnvVars();
    const transport = new StdioClientTransport({
      command: "ts-node",
      args: ["--transpile-only", "src/mcp.ts"],
      env: envVars
    });
    await this.client.connect(transport);
    return this.client;
  }

  async queryChatCompletion(query: string, personId: string) {
    const client = await this.getClient();
    this.openAiAdaptor = new OpenAIChatAdaptor(client);

    const tools = await this.openAiAdaptor.listTools();

    const messages: ChatCompletionMessageParam[] = [
      {
        role: "system",
        content: `You are a helpful assistant that can use tools to answer questions. Never use markdown, reply with plain text only. 
          You have access to the following tools: ${tools
            .map(
              (tool) => `${tool.function.name}: ${tool.function.description}`
            )
            .join(", ")}.

                        **reply guidelines:**
                        Follow the following guidelines strictly.
                        *Always reply in way that you are explaining to a user of a health technology company.
                        *Always reply in the same language that user has inputed
                        *Never ask question back to the user.
                        *Always try to reply in most natural language as possible.
                        *Always try to make sense of information and reply in most natural language as possible.
                        *Always query one single document from elastic search.
                        *Always include only those fields in the source that you will be using to reply.
                        *Always call get_mappings before get_profile_document to make sure you include right fields in the source.
                        *Always try to keep the context limited to HCP, people, person, Doctor. Event you will find everything in index starting with people_base.
                        *PersonId will be provided in the form PersonId: <10872034>
                        *Never reply with personId to the user but always find the name of the person and reply with that. Your response should not mention the word PersonId
                        `
      },
      {
        role: "user",
        content: query + ` PersonId: <${personId}>`
      }
    ];

    let isDone = false;
    while (!isDone) {
      const response = await this.llm.chat.completions.create({
        model: this.model,
        messages: messages,
        tools: tools,
        max_tokens: 800,
        parallel_tool_calls: false
      });

      const toolMessage = await this.openAiAdaptor.callTool(response);

      messages.push(response.choices[0].message);
      messages.push(...toolMessage);

      isDone = toolMessage.length === 0;
    }

    await this.client.close();
    return messages[messages.length - 1];
  }
}
