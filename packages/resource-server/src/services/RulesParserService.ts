import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import {
  RuleCombinatorEnum,
  RuleFieldEnum,
  RuleOperatorEnum,
  RuleGroupType,
  RuleType,
  RuleOrRuleGroup,
  RuleTypeEnum,
  TermsQueryType,
  InclusionExclusionRule
} from "@h1nyc/search-sdk";
import { Service } from "typedi";
import { RulesOptimizerService } from "./RulesOptimizerService";

const ruleFieldEnumToIndexPropertyMap = {
  [RuleFieldEnum.DIAGNOSES_CODE]: "patientClaims.diagnosisIcdCode",
  [RuleFieldEnum.DIAGNOSES_INDICATION]: "patientClaims.diagnosisIndications",
  [RuleFieldEnum.PROCEDURE_CODE]: "patientClaims.procedureCode",
  [RuleFieldEnum.AGE]: "patientClaims.age",
  [RuleFieldEnum.GENDER]: "patientClaims.gender",
  [RuleFieldEnum.DIVERSITY]: "patientClaims.diversity",
  [RuleFieldEnum.ETHNICITY]: "patientClaims.ethnicity",
  [RuleFieldEnum.GENERIC_DRUG]: "patientClaims.genericDrugNames",
  [RuleFieldEnum.CCSR]: "patientClaims.ccsrDescriptions",
  [RuleFieldEnum.CCSR_PX]: "patientClaims.ccsrPxDescriptions"
};

const ruleFieldEnumToIndexPropertyMapV2 = {
  [RuleFieldEnum.DIAGNOSES_CODE]: "diagnosis_codes",
  [RuleFieldEnum.DIAGNOSES_INDICATION]: "indications",
  [RuleFieldEnum.PROCEDURE_CODE]: "procedure_codes",
  [RuleFieldEnum.AGE]: "age",
  [RuleFieldEnum.GENDER]: "gender",
  [RuleFieldEnum.DIVERSITY]: "race",
  [RuleFieldEnum.ETHNICITY]: "ethnicity",
  [RuleFieldEnum.GENERIC_DRUG]: "generic_drug_name",
  [RuleFieldEnum.CCSR]: "ccsr_dx_description",
  [RuleFieldEnum.CCSR_PX]: "ccsr_px_description"
};

interface PatientSuppliedClaimsFilter {
  patientClaimsFilter?: RuleOrRuleGroup | null;
  patientClaimsFilterV2?: InclusionExclusionRule | null;
}

export type ExtractedClaimsCodesFromIE = {
  diagnosesCodes: string[];
  proceduresCodes: string[];
  ccsrDxDescriptions: string[];
  ccsrPxDescriptions: string[];
};

type RuleParserFeatureFlags = {
  usePatientIndexForCount: boolean;
};

@Service()
export class RulesParserService {
  constructor(private rulesOptimizerService: RulesOptimizerService) {}

  private isRuleGroupType(rule: RuleOrRuleGroup) {
    return rule.type === RuleTypeEnum.RULE_GROUP;
  }

  private toTermsFilter(
    fieldName: string,
    values: string[]
  ): QueryDslQueryContainer {
    return {
      terms: {
        [fieldName]: values
      }
    };
  }

  private toTermsSetFilter(
    fieldName: string,
    values: string[]
  ): QueryDslQueryContainer {
    return {
      terms_set: {
        [fieldName]: {
          terms: values,
          minimum_should_match_script: {
            source: `${values.length}`
          }
        }
      }
    };
  }

  private parseRuleGroupRecursively(
    root: RuleGroupType,
    featureFlags?: RuleParserFeatureFlags
  ): QueryDslQueryContainer {
    if (!root.rules.length) {
      throw new Error("Rule group must contain at least one rule");
    }
    const parsedRules: QueryDslQueryContainer[] = [];
    for (let index = 0; index < root.rules.length; index++) {
      const rule = root.rules[index];
      if (this.isRuleGroupType(rule)) {
        const groupedRules: QueryDslQueryContainer =
          this.parseRuleGroupRecursively(rule.ruleGroup!, featureFlags);
        parsedRules.push(groupedRules);
      } else {
        const parsedRule: QueryDslQueryContainer = this.parseRule(
          rule.rule!,
          featureFlags
        );
        parsedRules.push(parsedRule);
      }
    }

    let query: QueryDslQueryContainer;
    if (
      root.combinator === RuleCombinatorEnum.AND ||
      root.combinator === RuleCombinatorEnum.ALL
    ) {
      // must_clause
      query = {
        bool: {
          must: parsedRules
        }
      };
    } else {
      //  should_clause
      query = {
        bool: {
          should: parsedRules,
          minimum_should_match: 1
        }
      };
    }

    if (root.not) {
      return {
        bool: {
          must_not: [query]
        }
      };
    }

    return query;
  }

  private parseRuleGroupRecursivelyForClaimCodes(
    root: RuleGroupType
  ): [string[], string[], string[], string[]] {
    if (!root.rules.length) {
      throw new Error("Rule group must contain at least one rule");
    }
    return root.rules.reduce(
      (acc, rule) => {
        const claimCodes = this.isRuleGroupType(rule)
          ? this.parseRuleGroupRecursivelyForClaimCodes(rule.ruleGroup!)
          : this.parseRuleForClaimCodes(rule.rule!);

        return [
          acc[0].concat(claimCodes[0]),
          acc[1].concat(claimCodes[1]),
          acc[2].concat(claimCodes[2]),
          acc[3].concat(claimCodes[3])
        ];
      },
      [[], [], [], []] as [string[], string[], string[], string[]]
    );
  }

  private parseRuleGroupRecursivelyForGenericDrugNames(
    root: RuleGroupType
  ): string[] {
    if (!root.rules.length) {
      throw new Error("Rule group must contain at least one rule");
    }
    return root.rules.reduce((acc, rule) => {
      const genericDrugNames = this.isRuleGroupType(rule)
        ? this.parseRuleGroupRecursivelyForGenericDrugNames(rule.ruleGroup!)
        : this.parseRuleForGenericDrugNames(rule.rule!);

      return acc.concat(genericDrugNames);
    }, [] as string[]);
  }

  private parseRule(
    rule: RuleType,
    featureFlags?: RuleParserFeatureFlags
  ): QueryDslQueryContainer {
    const operator = rule.operator;
    const fieldToSearch = featureFlags?.usePatientIndexForCount
      ? ruleFieldEnumToIndexPropertyMapV2[rule.field]
      : ruleFieldEnumToIndexPropertyMap[rule.field];

    let filter: QueryDslQueryContainer;
    if (
      rule.termsQueryType === TermsQueryType.TERMS_SET &&
      rule.value.length > 1
    ) {
      filter = this.toTermsSetFilter(fieldToSearch, rule.value);
    } else {
      filter = this.toTermsFilter(fieldToSearch, rule.value);
    }

    if (operator === RuleOperatorEnum.EQUAL) {
      return filter;
    } else if (operator === RuleOperatorEnum.NOT_EQUAL) {
      return {
        bool: {
          must_not: [filter]
        }
      };
    } else if (operator === RuleOperatorEnum.OR) {
      return filter;
    } else {
      throw new Error(`RuleOperatorEnum(${operator}) not supported`);
    }
  }

  private parseRuleForClaimCodes(
    rule: RuleType
  ): [string[], string[], string[], string[]] {
    if (!rule) {
      return [[], [], [], []];
    }

    const diagnosisCodes: string[] = [];
    const procedureCodes: string[] = [];
    const ccsrDxDescriptions: string[] = [];
    const ccsrPxDescriptions: string[] = [];
    const operator = rule.operator;
    if (rule.value.length >= 1 && operator !== RuleOperatorEnum.NOT_EQUAL) {
      switch (rule.field) {
        case RuleFieldEnum.DIAGNOSES_CODE:
          diagnosisCodes.push(...rule.value);
          break;
        case RuleFieldEnum.PROCEDURE_CODE:
          procedureCodes.push(...rule.value);
          break;
        case RuleFieldEnum.CCSR:
          ccsrDxDescriptions.push(...rule.value);
          break;
        case RuleFieldEnum.CCSR_PX:
          ccsrPxDescriptions.push(...rule.value);
          break;
        default:
          break;
      }
    }
    return [
      diagnosisCodes,
      procedureCodes,
      ccsrDxDescriptions,
      ccsrPxDescriptions
    ];
  }

  private parseRuleForGenericDrugNames(rule: RuleType): string[] {
    const genericDrugNames: string[] = [];
    if (rule.field == RuleFieldEnum.GENERIC_DRUG) {
      genericDrugNames.push(...rule.value);
    }
    return genericDrugNames;
  }

  private isInclusionExclusionRule(
    root: RuleOrRuleGroup | InclusionExclusionRule
  ): root is InclusionExclusionRule {
    if ("inclusionCriteria" in root || "exclusionCriteria" in root) {
      return true;
    }

    return false;
  }

  private parseRuleOrRuleGroup(
    root?: RuleOrRuleGroup,
    featureFlags?: RuleParserFeatureFlags
  ) {
    if (!root) {
      return [];
    }

    if (this.isRuleGroupType(root)) {
      return [this.parseRuleGroupRecursively(root.ruleGroup!, featureFlags)];
    }

    return root.rule ? [this.parseRule(root.rule!, featureFlags)] : [];
  }
  private parseRuleOrRuleGroupForClaimCodes(
    root?: RuleOrRuleGroup
  ): [string[], string[], string[], string[]] {
    if (!root) {
      return [[], [], [], []];
    }

    if (this.isRuleGroupType(root)) {
      return this.parseRuleGroupRecursivelyForClaimCodes(root.ruleGroup!);
    }
    const parsedRule = this.parseRuleForClaimCodes(root.rule!);
    return [parsedRule[0], parsedRule[1], parsedRule[2], parsedRule[3]];
  }

  private parseRuleOrRuleGroupForGenericDrugNames(
    root?: RuleOrRuleGroup
  ): string[] {
    if (!root) {
      return [];
    }

    if (this.isRuleGroupType(root)) {
      return this.parseRuleGroupRecursivelyForGenericDrugNames(root.ruleGroup!);
    }
    return this.parseRuleForGenericDrugNames(root.rule!);
  }

  parseRulesToEsQueries(
    filters?: PatientSuppliedClaimsFilter,
    featureFlags?: RuleParserFeatureFlags
  ) {
    const root = filters?.patientClaimsFilterV2 ?? filters?.patientClaimsFilter;

    if (!root) {
      return [];
    }

    if (this.isInclusionExclusionRule(root)) {
      if (!(root.inclusionCriteria || root.exclusionCriteria)) {
        return [];
      }

      const inclusionCriteria = this.parseRuleOrRuleGroup(
        root.inclusionCriteria,
        featureFlags
      );

      const exclusionCriteria = this.parseRuleOrRuleGroup(
        root.exclusionCriteria,
        featureFlags
      );

      return [
        {
          bool: {
            must: inclusionCriteria.length ? inclusionCriteria : undefined,
            must_not: exclusionCriteria.length ? exclusionCriteria : undefined
          }
        }
      ];
    }

    const optimizedRoot = this.rulesOptimizerService.optimizeRules(root);
    return this.parseRuleOrRuleGroup(optimizedRoot, featureFlags);
  }

  extractClaimsAndProcedureCodesFromRules(
    filters?: PatientSuppliedClaimsFilter
  ): ExtractedClaimsCodesFromIE {
    const root = filters?.patientClaimsFilterV2 ?? filters?.patientClaimsFilter;
    const emptyCodes = {
      diagnosesCodes: [],
      proceduresCodes: [],
      ccsrDxDescriptions: [],
      ccsrPxDescriptions: []
    };
    if (!root) {
      return emptyCodes;
    }

    if (this.isInclusionExclusionRule(root)) {
      const [
        diagnosesCodes,
        proceduresCodes,
        ccsrDxDescriptions,
        ccsrPxDescriptions
      ] = this.parseRuleOrRuleGroupForClaimCodes(root.inclusionCriteria);
      return {
        diagnosesCodes,
        proceduresCodes,
        ccsrDxDescriptions,
        ccsrPxDescriptions
      };
    } else {
      const [
        diagnosesCodes,
        proceduresCodes,
        ccsrDxDescriptions,
        ccsrPxDescriptions
      ] = this.parseRuleOrRuleGroupForClaimCodes(root);
      return {
        diagnosesCodes,
        proceduresCodes,
        ccsrDxDescriptions,
        ccsrPxDescriptions
      };
    }
  }

  extractGenericDrugNamesFromRules(
    filters?: PatientSuppliedClaimsFilter
  ): string[] {
    const root = filters?.patientClaimsFilterV2 ?? filters?.patientClaimsFilter;
    if (!root) {
      return [];
    }

    if (this.isInclusionExclusionRule(root)) {
      const drugsIncluded = this.parseRuleOrRuleGroupForGenericDrugNames(
        root.inclusionCriteria
      );
      const drugsExcluded = this.parseRuleOrRuleGroupForGenericDrugNames(
        root.exclusionCriteria
      );

      return [...drugsIncluded, ...drugsExcluded];
    }

    return this.parseRuleOrRuleGroupForGenericDrugNames(root);
  }
}
