import { Service } from "typedi";
import { createLogger } from "../lib/Logger";
import Redis from "ioredis";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import {
  GetParsedUserOnboardingDataResponse,
  OnboardingSubmissionResourceClient,
  ParsedUserOnboardingData
} from "@h1nyc/account-sdk";
import { sha1 } from "object-hash";
import {
  LocationLabelFormatterService,
  UNITED_STATES
} from "./LocationLabelFormatterService";
import { FeatureFlagsService, LDUserInput } from "@h1nyc/systems-feature-flags";

const SECONDS = "ex";
// 1 week
const EXPIRATION_PERIOD = 60 * 60 * 24 * 7;
export const service = "UserOnboardingDataService";

export interface OnboardingData {
  indications: string[];
  countries: string[];
  states: string[];
}

export type UserOnboardingDataServiceFeatureFlags = {
  enableCachingForUserOnboardingDataService: boolean;
};

export const userOnboardingDataServiceFeatureFlagTypes = [
  "enableCachingForUserOnboardingDataService"
] as const;
export type UserOnboardingDataServiceFeatureFlag =
  (typeof userOnboardingDataServiceFeatureFlagTypes)[number];

export const featureFlagDefaults: Readonly<
  Record<
    keyof UserOnboardingDataServiceFeatureFlags,
    { key: string; default: boolean }
  >
> = {
  enableCachingForUserOnboardingDataService: {
    key: "search.enable-caching-for-user-onboarding-service",
    default: false
  }
};

@Service()
export class UserOnboardingDataService {
  private readonly logger = createLogger(this);
  private redisClient;

  constructor(
    config: ConfigService,
    private onboardingSubmissionResourceClient: OnboardingSubmissionResourceClient,
    private locationLabelFormatterService: LocationLabelFormatterService,
    private featureFlagsService: FeatureFlagsService
  ) {
    this.redisClient = new Redis(config.searchCacheRedisOptions);
  }

  // TODO : Do a cache warmup during service startup
  @Trace("h1-search.getOnboardingData")
  async getOnboardingData(
    userId: string,
    projectId: string
  ): Promise<OnboardingData> {
    const featureFlags = await this.getFeatureFlagValues({ userId, projectId });

    if (featureFlags.enableCachingForUserOnboardingDataService) {
      const onboardingDataFromCache = await this.getFromCache(
        userId,
        projectId
      );
      if (onboardingDataFromCache) {
        return onboardingDataFromCache;
      }
    }

    const indications: string[] = [];
    const countries: string[] = [];
    const states: string[] = [];
    try {
      const parsedUserOnboardingDataResponse: GetParsedUserOnboardingDataResponse =
        await this.onboardingSubmissionResourceClient.getParsedUserOnboardingData(
          { userId, projectId }
        );

      const onboardingSubmissions: ParsedUserOnboardingData[] =
        parsedUserOnboardingDataResponse.submissions;

      onboardingSubmissions.forEach((submission) => {
        if (submission.parsed.keywords) {
          indications.push(
            ...submission.parsed.keywords.map((kw) =>
              kw.indicationName.toLowerCase().trim()
            )
          );
        }

        // Input from onboarding is country name and value needed during searching is countryCode
        if (submission.parsed.countries) {
          countries.push(
            ...submission.parsed.countries.map((country) =>
              this.locationLabelFormatterService.getCountryCode(country)
            )
          );
        }

        // Input from onboarding is region/state name and value needed during searching is countryCode|regionCode as that is how data is structured in the index for region filtering.
        // Currently states are only enabled for US during onboarding flow and hence we are passing a static country code here.
        if (submission.parsed.states) {
          states.push(
            ...submission.parsed.states.map((state) =>
              this.locationLabelFormatterService.getRegionCodeAndCountryCode(
                UNITED_STATES,
                state
              )
            )
          );
        }
      });

      this.logger.info({ indications, countries, states }, "Onboarding data");
    } catch (err) {
      this.logger.error({ err }, "Error thrown fetching user Onboarding data");
    }

    const onboardingData = {
      indications,
      countries,
      states
    };

    await this.addToCache(userId, projectId, onboardingData);

    return onboardingData;
  }

  private async getFromCache(
    userId: string,
    projectId: string
  ): Promise<OnboardingData | null> {
    const key = sha1({ userId, projectId, service });
    const raw = await this.redisClient.get(key);
    if (raw) {
      const onboardingData = JSON.parse(raw) as OnboardingData;
      this.logger.debug(onboardingData, "onboarding user data cache hit");
      return onboardingData;
    }
    this.logger.debug(null, "onboarding user data cache miss");
    return null;
  }

  // TODO : Find if we need to have projectId in the key
  private async addToCache(
    userId: string,
    projectId: string,
    onboardingData: Readonly<OnboardingData>
  ) {
    const key = sha1({ userId, projectId, service });
    return this.redisClient.set(
      key,
      JSON.stringify(onboardingData),
      SECONDS,
      EXPIRATION_PERIOD
    );
  }

  private async getFeatureFlagValues(
    ldUser: LDUserInput
  ): Promise<UserOnboardingDataServiceFeatureFlags> {
    const featureFlags: Partial<UserOnboardingDataServiceFeatureFlags> = {};

    const flagsState = await this.featureFlagsService.getAllFlags(ldUser);

    for (const flag of userOnboardingDataServiceFeatureFlagTypes) {
      featureFlags[flag] =
        flagsState.getFlagValue(featureFlagDefaults[flag].key) ??
        featureFlagDefaults[flag].default;
    }

    return featureFlags as UserOnboardingDataServiceFeatureFlags;
  }
}
