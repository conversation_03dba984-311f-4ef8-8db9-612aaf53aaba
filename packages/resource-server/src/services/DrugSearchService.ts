import {
  AutocompleteGenericDrug,
  AutocompleteGenericDrugInput,
  DrugValue,
  DrugValueSearchInput,
  DrugValues,
  GenericDrug,
  GenericDrugSearchInput,
  GenericDrugs,
  GrpcDrugTypeEnum
} from "@h1nyc/search-sdk";
import { Service } from "typedi";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  SearchCompletionSuggestOption,
  SearchRequest
} from "@elastic/elasticsearch/lib/api/types";
import { getHitsTotal } from "./NameSearchHighConfPersonAdapterService";
import { ConfigService } from "./ConfigService";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";

enum DrugTypeEnum {
  ACTIVE_INGREDIENT = "ACTIVE_INGREDIENT",
  DRUG_CLASS = "DRUG_CLASS",
  GENERIC_NAME = "GENERIC_NAME",
  BRAND_NAME = "BRAND_NAME"
}

export const DrugTypeToGrpcDrugTypeMap: Record<DrugTypeEnum, GrpcDrugTypeEnum> =
  {
    [DrugTypeEnum.ACTIVE_INGREDIENT]: GrpcDrugTypeEnum.ACTIVE_INGREDIENT,
    [DrugTypeEnum.DRUG_CLASS]: GrpcDrugTypeEnum.DRUG_CLASS,
    [DrugTypeEnum.GENERIC_NAME]: GrpcDrugTypeEnum.GENERIC_NAME,
    [DrugTypeEnum.BRAND_NAME]: GrpcDrugTypeEnum.BRAND_NAME
  };

export const GrpcDrugTypeToIndexFieldMap: Record<
  GrpcDrugTypeEnum,
  string | undefined
> = {
  [GrpcDrugTypeEnum.ACTIVE_INGREDIENT]: "active_ingredient",
  [GrpcDrugTypeEnum.DRUG_CLASS]: "drug_class",
  [GrpcDrugTypeEnum.GENERIC_NAME]: "generic_name",
  [GrpcDrugTypeEnum.BRAND_NAME]: "brand_name",
  [GrpcDrugTypeEnum.UNRECOGNIZED]: undefined
};

export const GrpcDrugTypeToDrugTypeMap: Record<
  GrpcDrugTypeEnum,
  DrugTypeEnum | undefined
> = {
  [GrpcDrugTypeEnum.ACTIVE_INGREDIENT]: DrugTypeEnum.ACTIVE_INGREDIENT,
  [GrpcDrugTypeEnum.DRUG_CLASS]: DrugTypeEnum.DRUG_CLASS,
  [GrpcDrugTypeEnum.GENERIC_NAME]: DrugTypeEnum.GENERIC_NAME,
  [GrpcDrugTypeEnum.BRAND_NAME]: DrugTypeEnum.BRAND_NAME,
  [GrpcDrugTypeEnum.UNRECOGNIZED]: undefined
};

interface DrugValueDoc {
  value: string;
  property: DrugTypeEnum;
}

interface GenericDrugDoc {
  generic_name: string;
  brand_name: string[];
  drug_class: string[];
  active_ingredient: string[];
  patient_count: number;
  indications_and_usage: string;
}

@Service()
export class DrugSearchService {
  private readonly logger = createLogger(this);

  constructor(
    private configService: ConfigService,
    private elasticSearchService: ElasticSearchService
  ) {}

  private buildDrugSuggestionRequest(
    query: string,
    size: number
  ): SearchRequest {
    return {
      index: this.configService.elasticDrugValueIndex,
      _source: ["value", "property"],
      size,
      query: {
        bool: {
          filter: [
            {
              multi_match: {
                query,
                type: "phrase_prefix",
                operator: "AND",
                fields: ["value.saut", "value.saut._2gram", "value.saut._3gram"]
              }
            },
            {
              term: {
                has_patients: true
              }
            }
          ]
        }
      },
      sort: {
        _script: {
          type: "number",
          script: {
            lang: "painless",
            source: `
              double score = 0.0; 
              if (doc['property'].value == 'DRUG_CLASS') { 
                score = 1.0; 
              } else if (doc['property'].value == 'ACTIVE_INGREDIENT') { 
                score = 2.0; 
              } else if (doc['property'].value == 'GENERIC_NAME') {
                score = 3.0; 
              }  else { 
                score = 4.0; 
              } 
              
              return score;`
          },
          order: "asc"
        }
      }
    };
  }

  private buildGenericDrugSuggestionRequest(
    query: string,
    size: number
  ): SearchRequest {
    return {
      index: this.configService.elasticGenericDrugIndex,
      _source: ["generic_name", "indications_and_usage"],
      suggest: {
        autocomplete: {
          prefix: query,
          completion: {
            field: "generic_name.autocomplete_search",
            size,
            contexts: {
              has_patients: "true"
            }
          }
        }
      }
    };
  }

  @Trace("h1-search.drug.search-drug-values")
  async searchDrugValues(request: DrugValueSearchInput): Promise<DrugValues> {
    const { searchText, size } = request;
    const searchRequest = this.buildDrugSuggestionRequest(searchText, size);

    const response = await this.elasticSearchService.query<DrugValueDoc>(
      searchRequest
    );

    const values: DrugValue[] = response.hits.hits.map((hit) => {
      return {
        value: hit._source!.value,
        type: DrugTypeToGrpcDrugTypeMap[hit._source!.property]
      };
    });

    this.logger.info({ searchText, size }, "searchDrugValues input");

    return {
      values
    };
  }

  @Trace("h1-search.drug.search-generic-drugs")
  async searchGenericDrugs(
    request: GenericDrugSearchInput
  ): Promise<GenericDrugs> {
    const { value, type, size, from } = request;
    const field = GrpcDrugTypeToIndexFieldMap[type];

    if (!field) {
      throw new Error(`Unrecognized drug type: ${type}`);
    }

    const searchRequest: SearchRequest = {
      index: this.configService.elasticGenericDrugIndex,
      from,
      size,
      query: {
        bool: {
          filter: [
            {
              term: {
                [field]: value
              }
            },
            {
              range: {
                patient_count: {
                  gt: 0
                }
              }
            }
          ]
        }
      },
      sort: [
        {
          patient_count: {
            order: "desc"
          }
        }
      ]
    };

    const response = await this.elasticSearchService.query<GenericDrugDoc>(
      searchRequest
    );

    const genericDrugs: GenericDrug[] = response.hits.hits.map((doc) => {
      return {
        genericName: doc._source!.generic_name,
        brandName: doc._source!.brand_name,
        drugClass: doc._source!.drug_class,
        activeIngredient: doc._source!.active_ingredient,
        patientCount: doc._source!.patient_count,
        indicationsAndUsage: doc._source!.indications_and_usage
      };
    });

    const total = getHitsTotal(response.hits.total ?? 0);
    this.logger.info(
      { value, field, from, size, total },
      "searchGenericDrugs input"
    );

    return {
      total,
      genericDrugs
    };
  }

  @Trace("h1-search.drug.autocomplete-generic-drugs")
  async autocompleteGenericDrugs(request: AutocompleteGenericDrugInput) {
    const { searchText, size } = request;
    const searchRequest = this.buildGenericDrugSuggestionRequest(
      searchText,
      size
    );

    const { suggest } = await this.elasticSearchService.query(searchRequest);

    const suggestData = suggest?.["autocomplete"]?.[0];

    let genericDrugs: AutocompleteGenericDrug[] = [];
    const options =
      suggestData?.options as SearchCompletionSuggestOption<GenericDrugDoc>[];
    if (suggestData && options.length) {
      genericDrugs = options.map((option) => {
        return {
          genericName: option._source!.generic_name,
          indicationsAndUsage: option._source!.indications_and_usage
        };
      });
    }

    this.logger.info({ searchText, size }, "autocompleteGenericDrugs input");

    return {
      genericDrugs
    };
  }
}
