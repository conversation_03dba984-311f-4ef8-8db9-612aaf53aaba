import "reflect-metadata";
import { Service, Container } from "typedi";
import { ConfigService } from "./ConfigService";
import {
  LanguageCode,
  QueryUnderstandingServiceRequest,
  QueryUnderstandingServiceRequestForIndications,
  QueryUnderstandingServiceResponse,
  QueryUnderstandingServiceResponseForIndications
} from "../proto/query_understanding_service_pb";
import { QueryUnderstandingClient } from "../proto/query_understanding_service_grpc_pb";
import { credentials } from "@grpc/grpc-js";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { Language } from "./LanguageDetectService";

const languageNameToGrpcEnumType: Record<Language, LanguageCode> = {
  eng: LanguageCode.ENGLISH,
  jpn: LanguageCode.JAPANESE,
  cmn: LanguageCode.CHINESE
};
// Query understanding service client is a gRPC client that invokes the request to the query understaning gRPC server.
// We are creating one client for every new search request therefore the service is marked as transient. We can improve
// this later using some client pooling technique so that the same client can be reused for making requests.
@Service()
export class QueryUnderstandingServiceClient {
  private readonly logger = createLogger(this);
  private client: QueryUnderstandingClient;

  constructor(private configService: ConfigService) {
    this.client = new QueryUnderstandingClient(
      `${this.configService.queryUnderstandingServiceHost}:${this.configService.queryUnderstandingServicePort}`,
      credentials.createInsecure()
    );

    this.logger.info(
      {
        address: `${this.configService.queryUnderstandingServiceHost}:${this.configService.queryUnderstandingServicePort}`
      },
      "QueryUnderstandingServiceClient init"
    );
  }

  /**
   * Implements the gRPC function that sends request to the remote query understanding gRPC server
   * @param request query understanding service request proto object
   * @returns a promoise containing the response in case of success and gRPC error in case of failure
   */
  private sendRequest(request: QueryUnderstandingServiceRequest) {
    return new Promise<QueryUnderstandingServiceResponse>((resolve, reject) => {
      this.client.getQueryUnderstanding(request, (err, reply) => {
        if (err) reject(err);
        else resolve(reply);
      });
    });
  }
  private sendRequestForEntities(request: QueryUnderstandingServiceRequest) {
    return new Promise<QueryUnderstandingServiceResponse>((resolve, reject) => {
      this.client.getEntities(request, (err, reply) => {
        if (err) reject(err);
        else resolve(reply);
      });
    });
  }

  private sendRequestForIndications(
    request: QueryUnderstandingServiceRequestForIndications
  ) {
    return new Promise<QueryUnderstandingServiceResponseForIndications>(
      (resolve, reject) => {
        this.client.getIndicationSynonymsAndIcdCodes(request, (err, reply) => {
          if (err) reject(err);
          else resolve(reply);
        });
      }
    );
  }

  @Trace("h1-search.qus-client.analyze")
  async analyze(query: string, languageCode: Language) {
    try {
      const queryUnderstandingServiceClient = Container.get(
        QueryUnderstandingServiceClient
      );
      const request = new QueryUnderstandingServiceRequest();
      request.setQuery(query);
      request.setLanguagecode(languageNameToGrpcEnumType[languageCode]);
      return queryUnderstandingServiceClient.sendRequest(request);
    } catch (error) {
      this.logger.error(
        "Error invoking query understanding gRPC server: " + error
      );
      throw error;
    }
  }

  @Trace("h1-search.qus-client.indications")
  async getIndicationSynonymsAndIcdCodes(
    indications: string[],
    languageCode: Language,
    or: boolean,
    country = "us"
  ) {
    try {
      const queryUnderstandingServiceClient = Container.get(
        QueryUnderstandingServiceClient
      );
      const request = new QueryUnderstandingServiceRequestForIndications();
      request.setIndicationsList(indications);
      request.setLanguagecode(languageNameToGrpcEnumType[languageCode]);
      request.setOr(or);
      request.setCountry(country);
      return queryUnderstandingServiceClient.sendRequestForIndications(request);
    } catch (error) {
      this.logger.error(
        "Error invoking query understanding gRPC server: " + error
      );
      throw error;
    }
  }

  @Trace("h1-search.qus-client.getEntitiesForQuery")
  async getEntitiesForQuery(query: string, languageCode: Language) {
    try {
      const queryUnderstandingServiceClient = Container.get(
        QueryUnderstandingServiceClient
      );
      const request = new QueryUnderstandingServiceRequest();
      request.setQuery(query);
      request.setLanguagecode(languageNameToGrpcEnumType[languageCode]);
      return queryUnderstandingServiceClient.sendRequestForEntities(request);
    } catch (error) {
      this.logger.error(
        "Error invoking query understanding gRPC server: " + error
      );
      throw error;
    }
  }
}
