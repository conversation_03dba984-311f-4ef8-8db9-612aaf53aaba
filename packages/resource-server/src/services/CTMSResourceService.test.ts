import { createMockInstance } from "../util/TestUtils";

import { CTMSResourceService } from "./CTMSResourceService";
import { ElasticCTMSFacilityAggsService } from "./ElasticSearchCTMSFacilityAggsService";
import { ElasticCTMSInvestigatorAggsService } from "./ElasticSearchCTMSInvestigatorAggsService";
import { faker } from "@faker-js/faker";
import { ConfigService } from "./ConfigService";

const mockData = {
  took: 2,
  timed_out: false,
  _shards: {
    total: 5,
    successful: 5,
    skipped: 0,
    failed: 0
  },
  hits: {
    total: {
      value: 1,
      relation: "eq"
    },
    max_score: null,
    hits: []
  },
  aggregations: {
    investigators: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: "Duis labore enim eu",
          doc_count: 1
        },
        {
          key: "do Excepteur exercitation",
          doc_count: 1
        },
        {
          key: "ipsum culpa Excepteur",
          doc_count: 1
        },
        {
          key: "minim ea cillum",
          doc_count: 1
        },
        {
          key: "nisi amet dolore minim",
          doc_count: 1
        }
      ]
    },
    activation: {
      value: 9
    },
    avg_recruitment_period_early_pct: {
      value: 8.1364328e7
    },
    avg_recruitment_period_on_time_pct: {
      value: 2.0482464e7
    },
    indications: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: "deserunt",
          doc_count: 1
        },
        {
          key: "sint",
          doc_count: 1
        }
      ]
    },
    avg_patient_recruitment_period: {
      value: -4.07144735e7
    },
    avg_patient_retention_pct: {
      value: 1.4218441e7
    },
    avg_recruitment_period_late_pct: {
      value: -4.421536190625e7
    },
    avg_protocol_violations_per_participant: {
      value: 2.1846003e7
    },
    times_used_as_trial: {
      value: -4.55770055e7
    },
    therapeutic_area: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: "consectetur Excepteur",
          doc_count: 1
        },
        {
          key: "in reprehenderit irure sint cillum",
          doc_count: 1
        }
      ]
    },
    avg_patient_recruitment_performance_pct: {
      value: -3.7367793e7
    },
    phases: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: "id",
          doc_count: 1
        },
        {
          key: "occaecat tempor",
          doc_count: 1
        }
      ]
    },
    h1_organization_ids: {}
  }
};

const investigatorMockData = {
  took: 1,
  timed_out: false,
  _shards: {
    total: 5,
    successful: 5,
    skipped: 0,
    failed: 0
  },
  hits: {
    total: {
      value: 2,
      relation: "eq"
    },
    max_score: 1.0,
    hits: [
      {
        _index: "ci_dp.ct.ctms.investigator-aggregation.v1",
        _type: "_doc",
        _id: "7",
        _score: 1.0,
        _source: {
          h1_person_id: "do Excepteur exercitation",
          times_used_as_trial: -95874909,
          last_name: "aliquip tempor veniam deserunt ullamco",
          first_name: "minim commodo deserunt labore Lorem"
        }
      },
      {
        _index: "ci_dp.ct.ctms.investigator-aggregation.v1",
        _type: "_doc",
        _id: "7",
        _score: 1.0,
        _source: {
          h1_person_id: "do Excepteur exercitation",
          times_used_as_trial: -95874909,
          last_name: "aliquip tempor veniam deserunt ullamco",
          first_name: "minim commodo deserunt labore Lorem"
        }
      },
      {
        _index: "ci_dp.ct.ctms.investigator-aggregation.v1",
        _type: "_doc",
        _id: "7",
        _score: 1.0,
        _source: {
          h1_person_id: "ipsum culpa Excepteur",
          times_used_as_trial: -95874909,
          last_name: "aliquip tempor",
          first_name: "minim commodo"
        }
      }
    ]
  },
  aggregations: {
    h1_person_ids: {}
  }
};

const mockEmptyResp = {
  took: 2,
  timed_out: false,
  _shards: {
    total: 5,
    successful: 5,
    skipped: 0,
    failed: 0
  },
  hits: {
    total: {
      value: 0,
      relation: "eq"
    },
    max_score: null,
    hits: []
  },
  aggregations: {
    investigators: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: []
    },
    avg_recruitment_period_early_pct: {
      value: null
    },
    avg_recruitment_period_on_time_pct: {
      value: null
    },
    indications: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: []
    },
    activation: {
      value: null
    },
    avg_patient_recruitment_period: {
      value: null
    },
    avg_patient_retention_pct: {
      value: null
    },
    avg_recruitment_period_late_pct: {
      value: null
    },
    avg_protocol_violations_per_participant: {
      value: null
    },
    times_used_as_trial: {
      value: null
    },
    therapeutic_area: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: []
    },
    avg_patient_recruitment_performance_pct: {
      value: null
    },
    phases: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: []
    }
  }
};

const mockInvestigatorAggsResponse = {
  took: 2,
  timed_out: false,
  _shards: {
    total: 5,
    successful: 5,
    skipped: 0,
    failed: 0
  },
  hits: {
    total: {
      value: 1,
      relation: "eq"
    },
    max_score: null,
    hits: []
  },
  aggregations: {
    activation: {
      value: 9
    },
    avg_recruitment_period_early_pct: {
      value: 8.1364328e7
    },
    avg_recruitment_period_on_time_pct: {
      value: 2.0482464e7
    },
    indications: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: "deserunt",
          doc_count: 1
        },
        {
          key: "sint",
          doc_count: 1
        }
      ]
    },
    avg_patient_recruitment_period: {
      value: -4.07144735e7
    },
    avg_patient_retention_pct: {
      value: 1.4218441e7
    },
    avg_recruitment_period_late_pct: {
      value: -4.421536190625e7
    },
    avg_protocol_violations_per_participant: {
      value: 2.1846003e7
    },
    times_used_as_trial: {
      value: -4.55770055e7
    },
    therapeutic_area: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: "consectetur Excepteur",
          doc_count: 1
        },
        {
          key: "in reprehenderit irure sint cillum",
          doc_count: 1
        }
      ]
    },
    avg_patient_recruitment_performance_pct: {
      value: -3.7367793e7
    },
    phases: {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: "id",
          doc_count: 1
        },
        {
          key: "occaecat tempor",
          doc_count: 1
        }
      ]
    },
    h1_person_ids: {}
  }
};

afterEach(() => {
  jest.clearAllMocks();
});

describe("ctmsFacilityAggregations()", () => {
  it("should call elasticsearch facilty and investigtor services ", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockData
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      investigatorMockData
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.ctmsFacilityAggregation({
      institutionId: "123"
    });

    expect(
      elasticCTMSFacilityAggsService.getSignedElasticRequest
    ).toBeCalledTimes(1);
    expect(
      elasticCTMSInvestigatorAggsService.getSignedElasticRequest
    ).toBeCalledTimes(1);
  });

  it("should build correct search query based on input filters", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockData
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      investigatorMockData
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const input = {
      institutionId: "123",
      filters: {
        phase: [faker.datatype.string(), faker.datatype.string()],
        indication: [faker.datatype.string(), faker.datatype.string()],
        therapeutic_area: [faker.datatype.string(), faker.datatype.string()]
      }
    };

    await ctmsResourceService.ctmsFacilityAggregation(input);
    const expectedFaciltyQuery = {
      query: {
        bool: {
          must: [
            {
              term: {
                "h1_organization_id.keyword": "123"
              }
            },
            {
              bool: {
                filter: [
                  {
                    terms: {
                      "indication.keyword": input.filters.indication
                    }
                  },
                  {
                    terms: {
                      "phase.keyword": input.filters.phase
                    }
                  },
                  {
                    terms: {
                      "therapeutic_area.keyword": input.filters.therapeutic_area
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggregations: {
        avg_recruitment_period_on_time_pct: {
          avg: {
            field: "avg_recruitment_period_on_time_pct"
          }
        },
        avg_patient_retention_pct: {
          avg: {
            field: "avg_patient_retention_pct"
          }
        },
        avg_protocol_violations_per_participant: {
          avg: {
            field: "avg_protocol_violations_per_participant"
          }
        },
        avg_recruitment_period_late_pct: {
          avg: {
            field: "avg_recruitment_period_late_pct"
          }
        },
        times_used_as_trial: {
          avg: {
            field: "times_used_as_trial"
          }
        },
        avg_patient_recruitment_performance_pct: {
          avg: {
            field: "avg_patient_recruitment_performance_pct"
          }
        },
        avg_recruitment_period_early_pct: {
          avg: {
            field: "avg_recruitment_period_early_pct"
          }
        },
        avg_patient_recruitment_period: {
          avg: {
            field: "avg_patient_recruitment_period"
          }
        },
        activation: {
          avg: {
            field: "activation"
          }
        },
        phases: {
          terms: {
            field: "phase.keyword"
          }
        },
        indications: {
          terms: {
            field: "indication.keyword"
          }
        },
        therapeutic_area: {
          terms: {
            field: "therapeutic_area.keyword"
          }
        },
        investigators: {
          terms: {
            field: "investigator.h1_person_id.keyword"
          }
        }
      }
    };
    const expectInvestigatorQuery = {
      _source: [
        "h1_person_id",
        "first_name",
        "last_name",
        "times_used_as_trial"
      ],
      query: {
        terms: {
          "h1_person_id.keyword": [
            "Duis labore enim eu",
            "do Excepteur exercitation",
            "ipsum culpa Excepteur",
            "minim ea cillum",
            "nisi amet dolore minim"
          ]
        }
      }
    };

    expect(
      elasticCTMSFacilityAggsService.getSignedElasticRequest
    ).toBeCalledWith(expectedFaciltyQuery);
    expect(
      elasticCTMSInvestigatorAggsService.getSignedElasticRequest
    ).toBeCalledWith(expectInvestigatorQuery);
  });

  it("should correctly map and return results from the elasticsearch response", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockData
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      investigatorMockData
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.ctmsFacilityAggregation({
      institutionId: "123"
    });

    const expectedClientResponse = {
      activation: 9,
      avg_recruitment_period_on_time_pct: 20482464,
      avg_patient_retention_pct: 14218441,
      avg_protocol_violations_per_participant: 21846003,
      avg_recruitment_period_late_pct: -44215361.90625,
      times_used_as_trial: -45577005.5,
      avg_patient_recruitment_performance_pct: -37367793,
      avg_recruitment_period_early_pct: 81364328,
      avg_patient_recruitment_period: -40714473.5,
      phase: [
        { value: "id", count: 1 },
        { value: "occaecat tempor", count: 1 }
      ],
      therapeutic_area: [
        { value: "consectetur Excepteur", count: 1 },
        { value: "in reprehenderit irure sint cillum", count: 1 }
      ],
      indication: [
        { value: "deserunt", count: 1 },
        { value: "sint", count: 1 }
      ],
      investigators: [
        { value: "Duis labore enim eu", count: 1 },
        { value: "do Excepteur exercitation", count: 1 },
        { value: "ipsum culpa Excepteur", count: 1 },
        { value: "minim ea cillum", count: 1 },
        { value: "nisi amet dolore minim", count: 1 }
      ],
      h1LinkedInvestigators: [
        {
          h1_person_id: "do Excepteur exercitation",
          times_used_as_trial: -95874909,
          last_name: "aliquip tempor veniam deserunt ullamco",
          first_name: "minim commodo deserunt labore Lorem"
        },
        {
          h1_person_id: "ipsum culpa Excepteur",
          times_used_as_trial: -95874909,
          last_name: "aliquip tempor",
          first_name: "minim commodo"
        }
      ]
    };

    expect(resp).toEqual(expectedClientResponse);
  });

  it("should correctly build results for empty search response", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockEmptyResp
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.ctmsFacilityAggregation({
      institutionId: "123"
    });

    const expectedClientResponse = {
      activation: undefined,
      avg_recruitment_period_on_time_pct: undefined,
      avg_patient_retention_pct: undefined,
      avg_protocol_violations_per_participant: undefined,
      avg_recruitment_period_late_pct: undefined,
      times_used_as_trial: undefined,
      avg_patient_recruitment_performance_pct: undefined,
      avg_recruitment_period_early_pct: undefined,
      avg_patient_recruitment_period: undefined,
      phase: [],
      therapeutic_area: [],
      indication: [],
      investigators: [],
      h1LinkedInvestigators: []
    };

    expect(resp).toEqual(expectedClientResponse);
  });
});

describe("ctmsFacilityInitialOptions", () => {
  const mockInitialOptions = {
    took: 2,
    timed_out: false,
    _shards: {
      total: 5,
      successful: 5,
      skipped: 0,
      failed: 0
    },
    hits: {
      total: {
        value: 1,
        relation: "eq"
      },
      max_score: null,
      hits: []
    },
    aggregations: {
      indications: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "deserunt",
            doc_count: 1
          },
          {
            key: "sint",
            doc_count: 1
          }
        ]
      },
      therapeutic_area: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "consectetur Excepteur",
            doc_count: 1
          },
          {
            key: "in reprehenderit irure sint cillum",
            doc_count: 1
          }
        ]
      },
      phases: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "id",
            doc_count: 1
          },
          {
            key: "occaecat tempor",
            doc_count: 1
          }
        ]
      }
    }
  };
  it("should call search service", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockInitialOptions
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.ctmsFacilityInitialFilterOptions("123");
    expect(elasticCTMSFacilityAggsService.getSignedElasticRequest).toBeCalled();
  });

  it("should call search service with correct query", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockInitialOptions
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.ctmsFacilityInitialFilterOptions("123");
    expect(
      elasticCTMSFacilityAggsService.getSignedElasticRequest
    ).toBeCalledWith({
      query: {
        bool: {
          filter: {
            term: {
              "h1_organization_id.keyword": "123"
            }
          }
        }
      },
      size: 0,
      aggs: {
        phases: {
          terms: {
            field: "phase.keyword",
            order: {
              _count: "desc"
            }
          }
        },
        indications: {
          terms: {
            field: "indication.keyword",
            order: {
              _count: "desc"
            }
          }
        },
        therapeutic_area: {
          terms: {
            field: "therapeutic_area.keyword",
            order: {
              _count: "desc"
            }
          }
        }
      }
    });
  });

  it("should return correct response from search results", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockInitialOptions
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.ctmsFacilityInitialFilterOptions(
      "123"
    );
    expect(resp).toEqual({
      phase: [
        { value: "id", count: 1 },
        { value: "occaecat tempor", count: 1 }
      ],
      therapeutic_area: [
        { value: "consectetur Excepteur", count: 1 },
        { value: "in reprehenderit irure sint cillum", count: 1 }
      ],
      indication: [
        { value: "deserunt", count: 1 },
        { value: "sint", count: 1 }
      ]
    });
  });
});

describe("hasCTMSData", () => {
  const mockFacilityCountResponse = {
    count: 3,
    _shards: { total: 377, successful: 377, skipped: 0, failed: 0 }
  };

  const mockEmptyFacilityResponse = {
    count: 0,
    _shards: { total: 377, successful: 377, skipped: 0, failed: 0 }
  };

  it("should call search service", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.count.mockResolvedValue(
      mockFacilityCountResponse
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.hasCTMSData("123");
    expect(elasticCTMSFacilityAggsService.count).toBeCalled();
  });

  it("should call search service with correct query", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.count.mockResolvedValue(
      mockFacilityCountResponse
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.hasCTMSData("123");
    expect(elasticCTMSFacilityAggsService.count).toBeCalledWith({
      query: {
        term: {
          "h1_organization_id.keyword": {
            value: "123"
          }
        }
      }
    });
  });

  it("should return true when institution has ctms data", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.count.mockResolvedValue(
      mockFacilityCountResponse
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.hasCTMSData("123");
    expect(resp).toEqual(true);
  });

  it("should return false when institution has no ctms data", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.count.mockResolvedValue(
      mockEmptyFacilityResponse
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.hasCTMSData("123");
    expect(resp).toEqual(false);
  });
});

describe("getAllCTMSFacilityIds", () => {
  it("should call search service", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockData
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.getAllCTMSFacilityIds();
    expect(elasticCTMSFacilityAggsService.getSignedElasticRequest).toBeCalled();
  });

  it("should call search service with correct query", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockData
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.getAllCTMSFacilityIds();
    expect(
      elasticCTMSFacilityAggsService.getSignedElasticRequest
    ).toBeCalledWith({
      size: 0,
      aggs: {
        h1_organization_ids: {
          terms: { field: "h1_organization_id.keyword", size: 10000 }
        }
      }
    });
  });

  it("should return all unique iolIds that has ctms data", async () => {
    mockData.aggregations["h1_organization_ids"] = {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: "1234",
          doc_count: 1
        },
        {
          key: "5678",
          doc_count: 1
        }
      ]
    };

    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockData
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.getAllCTMSFacilityIds();
    expect(resp).toEqual(["1234", "5678"]);
  });

  it("should return empty array when institution has no ctms data", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    mockData.aggregations["h1_organization_ids"] = {};
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockData
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.getAllCTMSFacilityIds();
    expect(resp).toEqual([]);
  });
});

describe("getAllCTMSPersonIds", () => {
  it("should call search service", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      investigatorMockData
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.getAllCTMSPersonIds();
    expect(
      elasticCTMSInvestigatorAggsService.getSignedElasticRequest
    ).toBeCalled();
  });

  it("should call search service with correct query", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      investigatorMockData
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.getAllCTMSPersonIds();
    expect(
      elasticCTMSInvestigatorAggsService.getSignedElasticRequest
    ).toBeCalledWith({
      size: 0,
      aggs: {
        h1_person_ids: {
          terms: { field: "h1_person_id.keyword", size: 10000 }
        }
      }
    });
  });

  it("should return all unique personIds that has ctms data", async () => {
    investigatorMockData.aggregations["h1_person_ids"] = {
      doc_count_error_upper_bound: 0,
      sum_other_doc_count: 0,
      buckets: [
        {
          key: "1234",
          doc_count: 1
        },
        {
          key: "5678",
          doc_count: 1
        }
      ]
    };

    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      investigatorMockData
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.getAllCTMSPersonIds();
    expect(resp).toEqual(["1234", "5678"]);
  });

  it("should return empty array when investigator has no ctms data", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );

    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    investigatorMockData.aggregations["h1_person_ids"] = {};
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      investigatorMockData
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.getAllCTMSPersonIds();
    expect(resp).toEqual([]);
  });
});

describe("ctmsInvestigatorAggregations()", () => {
  it("should call investigtor aggregation elasticsearch services ", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      mockInvestigatorAggsResponse
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.ctmsInvestigatorAggregation({
      personId: faker.datatype.string()
    });

    expect(
      elasticCTMSInvestigatorAggsService.getSignedElasticRequest
    ).toBeCalledTimes(1);
  });

  it("should build correct search query based on input filters", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      mockInvestigatorAggsResponse
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const input = {
      personId: faker.datatype.string(),
      filters: {
        phase: [faker.datatype.string(), faker.datatype.string()],
        indication: [faker.datatype.string(), faker.datatype.string()],
        therapeutic_area: [faker.datatype.string(), faker.datatype.string()]
      }
    };

    await ctmsResourceService.ctmsInvestigatorAggregation(input);
    const expectedQuery = {
      query: {
        bool: {
          must: [
            {
              term: {
                "h1_person_id.keyword": input.personId
              }
            },
            {
              bool: {
                filter: [
                  {
                    terms: {
                      "indication.keyword": input.filters.indication
                    }
                  },
                  {
                    terms: {
                      "phase.keyword": input.filters.phase
                    }
                  },
                  {
                    terms: {
                      "therapeutic_area.keyword": input.filters.therapeutic_area
                    }
                  }
                ]
              }
            }
          ]
        }
      },
      aggregations: {
        avg_recruitment_period_on_time_pct: {
          avg: {
            field: "avg_recruitment_period_on_time_pct"
          }
        },
        avg_patient_retention_pct: {
          avg: {
            field: "avg_patient_retention_pct"
          }
        },
        avg_protocol_violations_per_participant: {
          avg: {
            field: "avg_protocol_violations_per_participant"
          }
        },
        avg_recruitment_period_late_pct: {
          avg: {
            field: "avg_recruitment_period_late_pct"
          }
        },
        times_used_as_trial: {
          avg: {
            field: "times_used_as_trial"
          }
        },
        avg_patient_recruitment_performance_pct: {
          avg: {
            field: "avg_patient_recruitment_performance_pct"
          }
        },
        avg_recruitment_period_early_pct: {
          avg: {
            field: "avg_recruitment_period_early_pct"
          }
        },
        avg_patient_recruitment_period: {
          avg: {
            field: "avg_patient_recruitment_period"
          }
        },
        activation: {
          avg: {
            field: "activation"
          }
        },
        phases: {
          terms: {
            field: "phase.keyword"
          }
        },
        indications: {
          terms: {
            field: "indication.keyword"
          }
        },
        therapeutic_area: {
          terms: {
            field: "therapeutic_area.keyword"
          }
        }
      }
    };

    expect(
      elasticCTMSInvestigatorAggsService.getSignedElasticRequest
    ).toBeCalledWith(expectedQuery);
  });

  it("should correctly map and return results from the elasticsearch response", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      mockInvestigatorAggsResponse
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.ctmsInvestigatorAggregation({
      personId: faker.datatype.string()
    });

    const expectedClientResponse = {
      avg_recruitment_period_on_time_pct: 20482464,
      avg_patient_retention_pct: 14218441,
      avg_protocol_violations_per_participant: 21846003,
      avg_recruitment_period_late_pct: -44215361.90625,
      times_used_as_trial: -45577005.5,
      avg_patient_recruitment_performance_pct: -37367793,
      avg_recruitment_period_early_pct: 81364328,
      avg_patient_recruitment_period: -40714473.5,
      phase: [
        { value: "id", count: 1 },
        { value: "occaecat tempor", count: 1 }
      ],
      therapeutic_area: [
        { value: "consectetur Excepteur", count: 1 },
        { value: "in reprehenderit irure sint cillum", count: 1 }
      ],
      indication: [
        { value: "deserunt", count: 1 },
        { value: "sint", count: 1 }
      ]
    };

    expect(resp).toEqual(expectedClientResponse);
  });

  it("should correctly build results for empty search response", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      mockEmptyResp
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.ctmsInvestigatorAggregation({
      personId: faker.datatype.string()
    });

    const expectedClientResponse = {
      avg_recruitment_period_on_time_pct: undefined,
      avg_patient_retention_pct: undefined,
      avg_protocol_violations_per_participant: undefined,
      avg_recruitment_period_late_pct: undefined,
      times_used_as_trial: undefined,
      avg_patient_recruitment_performance_pct: undefined,
      avg_recruitment_period_early_pct: undefined,
      avg_patient_recruitment_period: undefined,
      phase: [],
      therapeutic_area: [],
      indication: []
    };

    expect(resp).toEqual(expectedClientResponse);
  });
});

describe("ctmsInvestigatorInitialOptions", () => {
  const mockInitialOptions = {
    took: 2,
    timed_out: false,
    _shards: {
      total: 5,
      successful: 5,
      skipped: 0,
      failed: 0
    },
    hits: {
      total: {
        value: 1,
        relation: "eq"
      },
      max_score: null,
      hits: []
    },
    aggregations: {
      indications: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "deserunt",
            doc_count: 1
          },
          {
            key: "sint",
            doc_count: 1
          }
        ]
      },
      therapeutic_area: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "consectetur Excepteur",
            doc_count: 1
          },
          {
            key: "in reprehenderit irure sint cillum",
            doc_count: 1
          }
        ]
      },
      phases: {
        doc_count_error_upper_bound: 0,
        sum_other_doc_count: 0,
        buckets: [
          {
            key: "id",
            doc_count: 1
          },
          {
            key: "occaecat tempor",
            doc_count: 1
          }
        ]
      }
    }
  };
  it("should call search service", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockData
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      mockInitialOptions
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.ctmsInvestigatorInitialFilterOptions("123");
    expect(
      elasticCTMSInvestigatorAggsService.getSignedElasticRequest
    ).toBeCalled();
  });

  it("should call search service with correct query", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockData
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      mockInitialOptions
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.ctmsInvestigatorInitialFilterOptions("123");
    expect(
      elasticCTMSInvestigatorAggsService.getSignedElasticRequest
    ).toBeCalledWith({
      query: {
        bool: {
          filter: {
            term: {
              "h1_person_id.keyword": "123"
            }
          }
        }
      },
      size: 0,
      aggs: {
        phases: {
          terms: {
            field: "phase.keyword",
            order: {
              _count: "desc"
            }
          }
        },
        indications: {
          terms: {
            field: "indication.keyword",
            order: {
              _count: "desc"
            }
          }
        },
        therapeutic_area: {
          terms: {
            field: "therapeutic_area.keyword",
            order: {
              _count: "desc"
            }
          }
        }
      }
    });
  });

  it("should return correct response from search results", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    elasticCTMSFacilityAggsService.getSignedElasticRequest.mockResolvedValue(
      mockData
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.getSignedElasticRequest.mockResolvedValue(
      mockInitialOptions
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.ctmsInvestigatorInitialFilterOptions(
      "123"
    );
    expect(resp).toEqual({
      phase: [
        { value: "id", count: 1 },
        { value: "occaecat tempor", count: 1 }
      ],
      therapeutic_area: [
        { value: "consectetur Excepteur", count: 1 },
        { value: "in reprehenderit irure sint cillum", count: 1 }
      ],
      indication: [
        { value: "deserunt", count: 1 },
        { value: "sint", count: 1 }
      ]
    });
  });
});

describe("personHasCTMSData", () => {
  const mockCountResponse = {
    count: 3,
    _shards: { total: 377, successful: 377, skipped: 0, failed: 0 }
  };

  const mockEmptyCountResponse = {
    count: 0,
    _shards: { total: 377, successful: 377, skipped: 0, failed: 0 }
  };

  it("should call search service", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.count.mockResolvedValue(
      mockCountResponse
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.personHasCTMSData("123");
    expect(elasticCTMSInvestigatorAggsService.count).toBeCalled();
  });

  it("should call search service with correct query", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.count.mockResolvedValue(
      mockCountResponse
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    await ctmsResourceService.personHasCTMSData("123");
    expect(elasticCTMSInvestigatorAggsService.count).toBeCalledWith({
      query: {
        term: {
          "h1_person_id.keyword": {
            value: "123"
          }
        }
      }
    });
  });

  it("should return true when person has ctms data", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.count.mockResolvedValue(
      mockCountResponse
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.personHasCTMSData("123");
    expect(resp).toEqual(true);
  });

  it("should return false when person has no ctms data", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticCTMSFacilityAggsService = createMockInstance(
      ElasticCTMSFacilityAggsService
    );
    const elasticCTMSInvestigatorAggsService = createMockInstance(
      ElasticCTMSInvestigatorAggsService
    );
    elasticCTMSInvestigatorAggsService.count.mockResolvedValue(
      mockEmptyCountResponse
    );

    const ctmsResourceService = new CTMSResourceService(
      configService,
      elasticCTMSFacilityAggsService,
      elasticCTMSInvestigatorAggsService
    );

    const resp = await ctmsResourceService.personHasCTMSData("123");
    expect(resp).toEqual(false);
  });
});
