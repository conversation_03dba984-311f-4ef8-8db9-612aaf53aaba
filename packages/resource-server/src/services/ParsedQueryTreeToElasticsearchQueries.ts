import {
  QueryDslBoolQuery,
  QueryDslQueryContainer
} from "@elastic/elasticsearch/lib/api/types";
import { LanguageDetector } from "./KeywordSearchResourceServiceRewrite";
import {
  ArrayClause,
  ITree,
  SingleChildClause,
  TerminalClause
} from "../lib/ParserTypes/types";
import { Service } from "typedi";
import { ENGLISH, Language } from "./LanguageDetectService";
import _ from "lodash";

export const ALL_FLAGS_EXCEPT_WHITESPACE =
  "AND|ESCAPE|FUZZY|NEAR|NOT|OR|PHRASE|PRECEDENCE|PREFIX|SLOP";

type LanguageCodeAppender = (val: string) => string;

function appendLanguageCodeToField(languageCode: Language, field: string) {
  return `${field}_${languageCode}`;
}

const APPEND_ENGLISH_LANGUAGE_CODE = appendLanguageCodeToField.bind(
  null,
  "eng"
);
const DO_NOT_APPEND_LANGUAGE_CODE = _.identity;

function getLanguageCodeAppender(
  query: string,
  languageDetector?: LanguageDetector | typeof ENGLISH
): LanguageCodeAppender {
  if (languageDetector === ENGLISH) {
    return APPEND_ENGLISH_LANGUAGE_CODE;
  }

  if (_.isFunction(languageDetector)) {
    const languageCode = languageDetector(query);
    return appendLanguageCodeToField.bind(null, languageCode);
  }

  return DO_NOT_APPEND_LANGUAGE_CODE;
}

@Service()
export class ParsedQueryTreeToElasticsearchQueriesService {
  parse(
    tree: Readonly<ITree> | Readonly<Array<ITree>> | string,
    fields: Readonly<Array<string>>,
    languageDetector?: LanguageDetector | typeof ENGLISH
  ): Readonly<QueryDslQueryContainer> {
    if (isString(tree)) {
      const languageCodeAppender = getLanguageCodeAppender(
        tree,
        languageDetector
      );

      return {
        simple_query_string: {
          query: tree,
          default_operator: "AND",
          fields: fields.map(languageCodeAppender),
          flags: ALL_FLAGS_EXCEPT_WHITESPACE
        }
      };
    }

    if (isTerminalClause(tree as ITree)) {
      return toSimpleQueryString(
        tree as TerminalClause,
        fields,
        languageDetector
      );
    }

    if (isArrayClause(tree as ITree)) {
      return this.collectIncludesAndExcludesToBoolQuery(
        tree as ArrayClause,
        fields,
        languageDetector
      );
    }

    return this.groupToBoolQuery(
      tree as SingleChildClause,
      fields,
      languageDetector
    );
  }

  private mergeTerminalClausesToSimpleQueryString(
    clauses: TerminalClause[],
    languageQualifiedFieldsString: string,
    operator: string
  ) {
    const mergedExpression = clauses
      .map((subtree) => {
        if (subtree[0] === "Exactly") {
          return `("${subtree[1]}")`;
        } else {
          return `(${subtree[1]})`;
        }
      })
      .join(operator);

    return {
      simple_query_string: {
        query: mergedExpression,
        default_operator: "AND",
        fields: JSON.parse(languageQualifiedFieldsString),
        flags: ALL_FLAGS_EXCEPT_WHITESPACE
      }
    } as QueryDslQueryContainer;
  }

  private mergeTerminalClausesByLanguage(
    subtrees: ITree[],
    fields: Readonly<Array<string>>,
    operation: "And" | "Or",
    languageDetector?: LanguageDetector | typeof ENGLISH
  ): QueryDslQueryContainer[] {
    let operator = " | ";
    if (operation === "And") {
      operator = " + ";
    }

    const terminalClauses = subtrees
      .filter(including)
      .map((subtree) => subtree[1])
      .filter((subtree) => isTerminalClause(subtree as ITree));

    const groupedByLang = _.groupBy(
      terminalClauses,
      (terminalClause: TerminalClause) => {
        const languageCodeAppender = getLanguageCodeAppender(
          terminalClause[1],
          languageDetector
        );
        const languageQualifiedFields = fields.map(languageCodeAppender);

        return JSON.stringify(languageQualifiedFields);
      }
    );

    return _.values(
      _.mapValues(groupedByLang, (value, languageQualifiedFieldsString) =>
        this.mergeTerminalClausesToSimpleQueryString(
          value as TerminalClause[],
          languageQualifiedFieldsString,
          operator
        )
      )
    );
  }

  private collectIncludesAndExcludesToBoolQuery(
    tree: ArrayClause,
    fields: Readonly<Array<string>>,
    languageDetector?: LanguageDetector | typeof ENGLISH
  ): QueryDslQueryContainer {
    const [operation, subtrees] = tree as ArrayClause;

    const mergedTerminalClauses = this.mergeTerminalClausesByLanguage(
      subtrees,
      fields,
      operation,
      languageDetector
    );

    let includeClauses = subtrees
      .filter(including)
      .filter((subtree) => !isTerminalClause(subtree[1] as ITree))
      .map((subtree) => this.parse(subtree[1], fields, languageDetector));

    if (mergedTerminalClauses.length) {
      includeClauses = [...mergedTerminalClauses, ...includeClauses];
    }

    const excludeClauses = subtrees
      .filter(excluding)
      .map((subtree) => this.parse(subtree[1], fields, languageDetector));

    const query: QueryDslBoolQuery = {};

    if (excludeClauses.length > 0) {
      query.must_not = excludeClauses;
    }

    if (includeClauses.length > 0) {
      if (operation === "And") {
        query.must = includeClauses;
      } else if (operation === "Or") {
        query.should = includeClauses;
        query.minimum_should_match = 1;
      }
    }

    return { bool: query };
  }

  private groupToBoolQuery(
    [type, subtree]: SingleChildClause,
    fields: Readonly<Array<string>>,
    languageDetector?: LanguageDetector | typeof ENGLISH
  ) {
    if (type !== "Group") {
      throw new Error(`Unhandled ${type} ITree: ${JSON.stringify(subtree)}`);
    }

    return this.parse(subtree, fields, languageDetector);
  }
}

function isString(
  tree: Readonly<ITree> | Readonly<Array<ITree>> | string
): tree is string {
  return typeof tree === "string";
}

function isTerminalClause(tree: ITree): tree is TerminalClause {
  // TODO: there's probably a more typescripty way to do this
  return ["Exactly", "Text"].includes((tree as TerminalClause)[0]);
}

function toSimpleQueryString(
  [type, query]: TerminalClause,
  fields: Readonly<Array<string>>,
  languageDetector?: LanguageDetector | typeof ENGLISH
): QueryDslQueryContainer {
  const languageCodeAppender = getLanguageCodeAppender(query, languageDetector);
  const languageQualifiedFields = fields.map(languageCodeAppender);

  if (type === "Exactly") {
    return {
      simple_query_string: {
        query: `"${query}"`,
        fields: languageQualifiedFields
      }
    };
  } else {
    return {
      simple_query_string: {
        query,
        default_operator: "AND",
        fields: languageQualifiedFields,
        flags: "PHRASE"
      }
    };
  }
}

function isArrayClause(tree: ITree): tree is ArrayClause {
  // TODO: there's probably a more typescripty way to do this
  return ["And", "Or"].includes((tree as TerminalClause)[0]);
}

function including(tree: ITree) {
  return tree[0] === "Including";
}

function excluding(tree: ITree) {
  return tree[0] === "Excluding";
}
