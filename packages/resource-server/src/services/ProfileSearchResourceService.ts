import { get } from "lodash";
import { Rpc<PERSON>ethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import {
  GenericSearchEnum,
  GenericSearchResultInterface,
  ProfileFilterValue,
  ProfilePublicationMetrics,
  ProfileSearchResource,
  RPC_NAMESPACE_PROFILE_SEARCH,
  ProfileTrialMetrics,
  TrialIdsForPerson,
  PublicationIdsForPerson,
  CongressIdsForPerson,
  ProfileCongressMetrics,
  AllIdsForPerson,
  ProfileAllMetrics,
  ProfileTermsAndFilters,
  ProfileSortedIdsParams,
  MetricSummaryOptions,
  GetTweetsForPersonInput,
  GetTweetsForPersonResponse,
  GetPaginatedTrialsForPersonInput,
  GetPaginatedTrialsForPersonResponse,
  PersonAiResponse
} from "@h1nyc/search-sdk";
import { ConfigService } from "./ConfigService";
import { ElasticSearchService } from "./ElasticSearchService";
import { createLogger } from "../lib/Logger";
import {
  buildProfileMetricsQuery,
  buildProfileSearchQuery,
  buildSortedProfileSearchQuery,
  ProfileMetricsResponse,
  ProfileSearchAsset,
  ProfileSearchIdsResponse,
  buildAllProfileMetricsQuery,
  buildPaginatedProfileSearchQueryForAsset
} from "../lib/ProfileSearchBuilder";
import { ProjectFeaturesResourceClient } from "@h1nyc/account-sdk";
import { buildGenericProfileSearchQuery } from "../lib/GenericProfileSearchBuilder/GenericProfileSearchBuilder";
import {
  ProfileSearch,
  DocType
} from "../lib/GenericProfileSearchBuilder/typedef";
import {
  extractProfileCongressMetrics,
  extractProfileDiagnosisMetrics,
  extractProfilePaymentMetrics,
  extractProfileProcedureMetrics,
  extractProfilePublicationMetrics,
  extractProfileTrialMetrics
} from "../lib/ProfileSearchExtractor";
import { UserToken } from "@h1nyc/account-sdk";
import {
  CHINESE,
  ENGLISH,
  JAPANESE,
  LanguageDetectService
} from "./LanguageDetectService";
import { Service } from "typedi";
import { ParsedQueryTreeToElasticsearchQueriesService } from "./ParsedQueryTreeToElasticsearchQueries";
import { QueryParserService } from "./QueryParserService";
import {
  QueryDslQueryContainer,
  SortCombinations
} from "@elastic/elasticsearch/lib/api/types";
import {
  HCPDocument,
  LanguageDetector
} from "./KeywordSearchResourceServiceRewrite";
import { estypes } from "@elastic/elasticsearch";
import * as _ from "lodash";
import { extractLocationField } from "./NameSearchResponseAdapterService";
import { HCPLocation } from "@h1nyc/search-sdk";
import { buildTermQuery } from "../util/QueryBuildingUtils";
import { ElasticMCPClientService } from "./ElasticMCPClientService";

enum MultiLangSupport {
  AVAILABLE,
  ENGLISH_ONLY,
  UNSUPPORTED
}

const MULTIPLE_LANGUAGES_NOT_APPLICABLE = undefined;

const publicationsQueryFieldBaseNames = [
  "publications.keywords",
  "publications.publicationAbstract",
  "publications.title"
];
const publicationsQueryFields = [
  ...publicationsQueryFieldBaseNames.map((field) => `${field}_${ENGLISH}`),
  ...publicationsQueryFieldBaseNames.map((field) => `${field}_${CHINESE}`),
  ...publicationsQueryFieldBaseNames.map((field) => `${field}_${JAPANESE}`)
];

export const ASSET_FIELDS_TO_QUERY: Readonly<
  Record<
    ProfileSearchAsset,
    { fields: Array<string>; multiLangSupport: MultiLangSupport }
  >
> = {
  [ProfileSearchAsset.Publications]: {
    fields: publicationsQueryFields,
    multiLangSupport: MultiLangSupport.UNSUPPORTED
  },
  [ProfileSearchAsset.ClinicalTrials]: {
    fields: [
      "trials.officialTitle",
      "trials.briefTitle",
      "trials.conditions",
      "trials.interventions",
      "trials.keywords",
      "trials.summary"
    ],
    multiLangSupport: MultiLangSupport.ENGLISH_ONLY
  },
  [ProfileSearchAsset.Congresses]: {
    fields: [
      "congress.title_eng",
      "congress.keywords_eng",
      "congress.organizer_eng.search",
      "congress.name_eng.search"
    ],
    multiLangSupport: MultiLangSupport.UNSUPPORTED
  },
  [ProfileSearchAsset.Payments]: {
    fields: ["payments.associatedDrugOrDevice", "payments.payerCompany"],
    multiLangSupport: MultiLangSupport.UNSUPPORTED
  },
  [ProfileSearchAsset.DrgDiagnoses]: {
    fields: ["DRG_diagnoses.codeAndDescription"],
    multiLangSupport: MultiLangSupport.ENGLISH_ONLY
  },
  [ProfileSearchAsset.DrgProcedures]: {
    fields: ["DRG_procedures.codeAndDescription"],
    multiLangSupport: MultiLangSupport.ENGLISH_ONLY
  },
  [ProfileSearchAsset.Tweets]: {
    fields: ["tweets.text"],
    multiLangSupport: MultiLangSupport.UNSUPPORTED
  }
};

const TWEETS_SORT_FIELDS = ["tweets.createdAt", "tweets.id"];
const TWEETS_SORT_DIRECTION = "desc";
const TWEETS_NESTED_PATH = "tweets";

type REFERENCED_TWEET_TYPE = "replied_to" | "quoted" | "retweeted";

interface PersonTweetsResponse {
  hits: {
    hits: {
      _source?: {
        id: string; // ther HCP personId
      };
      inner_hits?: {
        tweets: {
          hits: {
            total: {
              value: number;
            };
            hits: {
              _source: {
                id: string; // id of the tweet
                text: string; // text of the tweet
                referencedTweets: {
                  id: string;
                  type: REFERENCED_TWEET_TYPE;
                }[];
                createdAt: Date;
              };
            }[];
          };
        };
      };
    }[];
  };
}

export type HCPDocumentWithOnlyLocations = Pick<HCPDocument, "locations">;
@Service()
@RpcService()
export class ProfileSearchResourceService
  extends RpcResourceService
  implements ProfileSearchResource
{
  private readonly logger = createLogger(this);
  private peopleIndex: string;

  constructor(
    config: ConfigService,
    private elasticService: ElasticSearchService,
    private projectFeaturesResourceClient: ProjectFeaturesResourceClient,
    private languageDetectService: LanguageDetectService,
    private queryParserService: QueryParserService,
    private parsedQueryTreeToElasticsearchQueriesService: ParsedQueryTreeToElasticsearchQueriesService,
    private elasticMCPClientService: ElasticMCPClientService
  ) {
    super(config.searchRpcSigningKey, RPC_NAMESPACE_PROFILE_SEARCH, {
      host: config.searchRedisHost,
      port: config.searchRedisPort,
      username: config.searchRedisUsername,
      password: config.searchRedisPassword
    });

    this.peopleIndex = config.elasticPeopleIndex;
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    this.elasticService.getSignedElasticRequest({}, this.peopleIndex);
    return true;
  }

  /**
   * @deprecated Use getPublicationIdsForPeople instead
   */
  @RpcMethod()
  async getPublicationIds(
    personId: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[],
    userLanguage?: string
  ): Promise<string[]> {
    const res = await this.getPublicationIdsForPeople(
      [personId],
      terms,
      filterValues,
      userLanguage
    );

    return res[0]?.publicationIds ?? [];
  }

  /**
   * Gets HCP Tweets for a given HCP.
   * @param input input used to fetch tweets for an HCP.
   * @param input.personId required string HCP personId to fetch tweets for.
   * @param input.projectId required string projectID to fetch HCP tweets in.
   * @param input.suppliedTerms optional string array of keyword search terms.
   * @param input.userLanguage optional string user language. (Try to provide this if possible). Will be looked up if not provided
   * @param input.page pagination info for request.
   * @param input.page.limit limits the number of returned HCP tweets.
   * @param input.page.offset offset for the HCP tweets. Used in pagination.
   * @returns paginated array of tweets for the given personId and input
   */
  @RpcMethod()
  async getTweetsForPerson({
    personId,
    projectId,
    page,
    suppliedTerms,
    userLanguage
  }: GetTweetsForPersonInput): Promise<GetTweetsForPersonResponse> {
    const query = await this.buildTweetsQuery(
      suppliedTerms,
      userLanguage,
      personId,
      projectId,
      page
    );

    const req = {
      _source: ["id"],
      query
    };

    this.logger.debug(
      { data: JSON.stringify(req) },
      "Performing profile tweet search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<PersonTweetsResponse>(
        req,
        this.peopleIndex
      );

    const total = data.hits.hits[0]?.inner_hits?.tweets.hits.total.value || 0;
    const tweets = (data.hits.hits[0]?.inner_hits?.tweets.hits.hits || []).map(
      (tweetDoc) => ({
        id: tweetDoc._source.id,
        text: tweetDoc._source.text,
        isRetweet: tweetDoc._source.referencedTweets.some(
          (referenceTweet) => referenceTweet.type === "retweeted"
        ),
        isQuote: tweetDoc._source.referencedTweets.some(
          (referenceTweet) => referenceTweet.type === "quoted"
        ),
        isReply: tweetDoc._source.referencedTweets.some(
          (referenceTweet) => referenceTweet.type === "replied_to"
        ),
        date: tweetDoc._source.createdAt
      })
    );

    this.logger.info(
      `Profile tweets search found ${tweets.length} for person ${personId} in project ${projectId}`
    );

    return {
      personId,
      total,
      tweets
    };
  }

  @RpcMethod()
  async getPublicationIdsForPeople(
    peopleIds: string[],
    suppliedTerms?: string[],
    filterValues?: ProfileFilterValue[],
    userLanguage?: string
  ): Promise<PublicationIdsForPerson[]> {
    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      suppliedTerms,
      [ProfileSearchAsset.Publications],
      userLanguage
    );

    const req = buildProfileSearchQuery(
      peopleIds,
      [ProfileSearchAsset.Publications],
      terms,
      filterValues
    );

    this.logger.debug(
      { data: JSON.stringify(req) },
      "Performing profile search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<ProfileSearchIdsResponse>(
        req,
        this.peopleIndex
      );

    if (!data.aggregations.people?.buckets.length) {
      this.logger.info(`Publications search failed to get results`);
      return [];
    }

    const result = data.aggregations.people.buckets.map((bucket) => {
      const personId = bucket.key;

      const publicationIds =
        bucket.publications?.publicationFilters.buckets[0].ids.buckets.map(
          ({ key }) => key
        ) ?? [];

      return { personId, publicationIds };
    });

    this.logger.info(
      `Publication search returned publications for ${result.length} people`
    );

    return result ?? [];
  }

  /**
   * @deprecated Use getPublicationMetricsForPeople instead
   */
  @RpcMethod()
  async getPublicationMetrics(
    personId: string,
    projectId?: string,
    terms?: string[],
    filterValues?: ProfileFilterValue[],
    usersLanguage?: string
  ): Promise<ProfilePublicationMetrics | null> {
    const res = await this.getPublicationMetricsForPeople(
      [personId],
      projectId,
      terms,
      filterValues,
      usersLanguage
    );

    return res[0] ?? null;
  }

  @RpcMethod()
  async getPublicationMetricsForPeople(
    peopleIds: string[],
    projectId?: string,
    suppliedTerms?: string[],
    filterValues?: ProfileFilterValue[],
    usersLanguage?: string
  ): Promise<ProfilePublicationMetrics[]> {
    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      suppliedTerms,
      [ProfileSearchAsset.Publications],
      usersLanguage
    );

    const req = buildProfileMetricsQuery(
      peopleIds,
      [ProfileSearchAsset.Publications],
      projectId,
      terms,
      filterValues
    );

    this.logger.debug({ data: req }, "Performing profile metrics search");

    const data =
      await this.elasticService.getSignedElasticRequest<ProfileMetricsResponse>(
        req,
        this.peopleIndex
      );

    if (!data.aggregations.people?.buckets.length) {
      this.logger.info(`Publications search failed to get results`);
      return [];
    }

    const metricsForPeople = data.aggregations.people.buckets
      .map((bucket) => {
        return extractProfilePublicationMetrics(bucket);
      })
      // remove any null values
      .flatMap((metric) => (metric ? [metric] : []));

    this.logger.info(
      `Calculated publication metrics for ${metricsForPeople.length} people`
    );

    return metricsForPeople;
  }

  @RpcMethod()
  async getTrialIdsForPeople(
    peopleIds: string[],
    suppliedTerms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<TrialIdsForPerson[]> {
    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      suppliedTerms,
      [ProfileSearchAsset.ClinicalTrials],
      undefined
    );

    const req = buildProfileSearchQuery(
      peopleIds,
      [ProfileSearchAsset.ClinicalTrials],
      terms,
      filterValues
    );

    this.logger.debug(
      { data: JSON.stringify(req) },
      "Performing profile search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<ProfileSearchIdsResponse>(
        req,
        this.peopleIndex
      );

    if (!data.aggregations.people?.buckets.length) {
      this.logger.info(`Trials search failed to get results`);
      return [];
    }

    const result = data.aggregations.people.buckets.map((bucket) => {
      const personId = bucket.key;

      const trialIds =
        bucket.trials?.trialFilters.buckets[0].ids.buckets.map(
          ({ key }) => key
        ) ?? [];

      return { personId, trialIds };
    });

    this.logger.info(
      `Trials search returned trials for ${result.length} people`
    );

    return result ?? [];
  }

  @RpcMethod()
  async getPaginatedTrialsForPerson({
    personId,
    projectId,
    page,
    terms: suppliedTerms,
    filters
  }: GetPaginatedTrialsForPersonInput): Promise<GetPaginatedTrialsForPersonResponse> {
    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      suppliedTerms,
      [ProfileSearchAsset.ClinicalTrials],
      undefined
    );

    const query = buildPaginatedProfileSearchQueryForAsset(
      personId,
      projectId,
      ProfileSearchAsset.ClinicalTrials,
      terms,
      filters,
      page
    );

    if (!query) {
      this.logger.info(`Trials profile search failed to get results`);
      return {
        totalCount: 0,
        h1Trials: [],
        ctmsTrials: []
      };
    }

    this.logger.debug(
      { data: query },
      "Performing paginated trials profile search"
    );

    const data = await this.elasticService.query({
      index: this.peopleIndex,
      size: 1,
      _source: false,
      query
    });

    this.logger.debug({ took: data.took }, "execution time");

    let totalCount = 0;
    const h1Trials = [];
    const ctmsTrials = [];

    if (data.hits?.hits?.length && data.hits.hits[0].inner_hits) {
      totalCount = this.extractTotalValue(
        data.hits.hits[0].inner_hits.trials.hits.total!
      );
      for (const hit of data.hits.hits[0].inner_hits.trials.hits.hits) {
        const source: string[] = _.get(hit, "fields['trials.source']", []);
        const trialId: string[] = _.get(hit, "fields['trials.id']", []);
        const affiliatedBy: string[] = _.get(
          hit,
          "fields['trials.affiliatedBy']",
          []
        ).map((affiliation) =>
          /**
           * Due to spelling issues, we need to support "principle investigator" until the
           * next data refresh. After that, this mapping can be removed
           */
          affiliation === "principle investigator"
            ? "principal investigator"
            : affiliation
        );

        if (_.isEmpty(trialId)) {
          continue;
        }

        if (!_.isEmpty(source)) {
          if (source[0] === "h1") {
            h1Trials.push({ id: trialId[0], affiliatedBy });
          } else {
            ctmsTrials.push({ id: trialId[0], affiliatedBy });
          }
        } else {
          h1Trials.push({ id: trialId[0], affiliatedBy });
        }
      }
    }

    return {
      totalCount,
      h1Trials,
      ctmsTrials
    };
  }

  @RpcMethod()
  async getTrialMetricsForPeople(
    peopleIds: string[],
    projectId?: string,
    suppliedTerms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<ProfileTrialMetrics[]> {
    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      suppliedTerms,
      [ProfileSearchAsset.ClinicalTrials],
      undefined
    );

    const req = buildProfileMetricsQuery(
      peopleIds,
      [ProfileSearchAsset.ClinicalTrials],
      projectId,
      terms,
      filterValues
    );

    this.logger.debug({ data: req }, "Performing profile metrics search");

    const data =
      await this.elasticService.getSignedElasticRequest<ProfileMetricsResponse>(
        req,
        this.peopleIndex
      );

    if (!data.aggregations.people?.buckets.length) {
      this.logger.info(`Trials search failed to get results`);
      return [];
    }

    const metricsForPeople = data.aggregations.people.buckets
      .map((bucket) => {
        return extractProfileTrialMetrics(bucket);
      })
      // remove any null values
      .flatMap((metric) => (metric ? [metric] : []));

    this.logger.info(
      `Calculated trial metrics for ${metricsForPeople.length} people`
    );

    return metricsForPeople;
  }

  @RpcMethod()
  async getCongressIdsForPeople(
    peopleIds: string[],
    suppliedTerms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<CongressIdsForPerson[]> {
    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      suppliedTerms,
      [ProfileSearchAsset.Congresses],
      undefined
    );

    const req = buildProfileSearchQuery(
      peopleIds,
      [ProfileSearchAsset.Congresses],
      terms,
      filterValues
    );

    this.logger.debug(
      { data: JSON.stringify(req) },
      "Performing profile search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<ProfileSearchIdsResponse>(
        req,
        this.peopleIndex
      );

    if (!data.aggregations.people?.buckets.length) {
      this.logger.info(`Congresses search failed to get results`);
      return [];
    }

    const result = data.aggregations.people.buckets.map((bucket) => {
      const personId = bucket.key;

      const congressIds =
        bucket.congresses?.congressFilters.buckets[0].ids.buckets.map(
          ({ key }) => key
        ) ?? [];

      return { personId, congressIds };
    });

    this.logger.info(`Retrieved congresses for ${result} results`);

    return result ?? [];
  }

  @RpcMethod()
  async getCongressMetricsForPeople(
    peopleIds: string[],
    projectId?: string,
    suppliedTerms?: string[],
    filterValues?: ProfileFilterValue[]
  ): Promise<ProfileCongressMetrics[]> {
    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      suppliedTerms,
      [ProfileSearchAsset.Congresses],
      undefined
    );

    const req = buildProfileMetricsQuery(
      peopleIds,
      [ProfileSearchAsset.Congresses],
      projectId,
      terms,
      filterValues
    );

    this.logger.debug(
      { data: req },
      "Performing profile congress metrics search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<ProfileMetricsResponse>(
        req,
        this.peopleIndex
      );

    if (!data.aggregations.people?.buckets.length) {
      this.logger.info(`Congresses search failed to get results`);
      return [];
    }

    const metricsForPeople = data.aggregations.people.buckets
      .map((bucket) => {
        return extractProfileCongressMetrics(bucket);
      })
      // remove any null values
      .flatMap((metric) => (metric ? [metric] : []));

    this.logger.info(
      `Calculated congress metrics for ${metricsForPeople.length} people`
    );

    return metricsForPeople;
  }

  @RpcMethod()
  async getAllMetricsForPeople(
    peopleIds: string[],
    _user: UserToken,
    termsAndFilters?: ProfileTermsAndFilters,
    languageCode?: string
  ): Promise<ProfileAllMetrics[]> {
    return this.getMetricSummaryForPeople({
      peopleIds,
      projectId: _user.projectId,
      termsAndFilters,
      languageCode
    });
  }

  @RpcMethod()
  async getMetricSummaryForPeople(
    opts: MetricSummaryOptions
  ): Promise<ProfileAllMetrics[]> {
    const {
      peopleIds,
      projectId,
      termsAndFilters,
      languageCode: usersLanguage
    } = opts;

    const profileSearchAssets = [
      ProfileSearchAsset.ClinicalTrials,
      ProfileSearchAsset.Congresses,
      ProfileSearchAsset.DrgDiagnoses,
      ProfileSearchAsset.DrgProcedures,
      ProfileSearchAsset.Payments,
      ProfileSearchAsset.Publications
    ];

    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      termsAndFilters?.terms,
      profileSearchAssets,
      usersLanguage
    );

    const req = buildAllProfileMetricsQuery(
      peopleIds,
      profileSearchAssets,
      projectId,
      {
        terms,
        filters: opts.termsAndFilters?.filters
      }
    );

    this.logger.debug({ data: req }, "Performing profile all metrics search");

    const data =
      await this.elasticService.getSignedElasticRequest<ProfileMetricsResponse>(
        req,
        this.peopleIndex
      );

    if (!data.aggregations.people?.buckets.length) {
      this.logger.info(`All metrics search failed to get results`);
      return [];
    }

    const metricsForPeople = data.aggregations.people.buckets.map((bucket) => {
      const publicationMetrics = extractProfilePublicationMetrics(bucket);
      const congressMetrics = extractProfileCongressMetrics(bucket);
      const trialMetrics = extractProfileTrialMetrics(bucket);
      const diagnosisMetrics = extractProfileDiagnosisMetrics(bucket);
      const procedureMetrics = extractProfileProcedureMetrics(bucket);
      const paymentMetrics = extractProfilePaymentMetrics(bucket);

      return {
        publicationMetrics,
        congressMetrics,
        trialMetrics,
        diagnosisMetrics,
        procedureMetrics,
        paymentMetrics
      };
    });

    this.logger.info(
      `Calculated all metrics for ${metricsForPeople.length} people`
    );

    return metricsForPeople;
  }

  @RpcMethod()
  async getSortedIdsForPeople(
    peopleIds: string[],
    params: ProfileSortedIdsParams,
    usersLanguage?: string
  ): Promise<AllIdsForPerson[]> {
    const profileSearchAssets = [
      ProfileSearchAsset.Publications,
      ProfileSearchAsset.ClinicalTrials,
      ProfileSearchAsset.Congresses
    ];

    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      params.termsAndFilters?.terms,
      profileSearchAssets,
      usersLanguage
    );

    const req = buildSortedProfileSearchQuery(
      peopleIds,
      profileSearchAssets,
      params.size,
      {
        terms,
        filters: params.termsAndFilters?.filters
      }
    );

    this.logger.debug(
      { data: JSON.stringify(req) },
      "Performing profile search"
    );

    const data =
      await this.elasticService.getSignedElasticRequest<ProfileSearchIdsResponse>(
        req,
        this.peopleIndex
      );

    if (!data.aggregations.people?.buckets.length) {
      this.logger.info(`All Ids search failed to get results`);
      return [];
    }

    const result = data.aggregations.people.buckets.map((bucket) => {
      const personId = bucket.key;

      const trialIds =
        bucket.trials?.trialFilters.buckets[0].ids.buckets.map(
          ({ key }) => key
        ) ?? [];

      const congressIds =
        bucket.congresses?.congressFilters.buckets[0].ids.buckets.map(
          ({ key }) => key
        ) ?? [];

      const publicationIds =
        bucket.publications?.publicationFilters.buckets[0].ids.buckets.map(
          ({ key }) => key
        ) ?? [];

      return { personId, trialIds, congressIds, publicationIds };
    });

    this.logger.info(`All search returned results for ${result.length} people`);

    return result ?? [];
  }

  @RpcMethod()
  async getAIResponseForPerson(
    personId: string,
    query: string
  ): Promise<PersonAiResponse> {
    this.logger.info({ personId, query }, "AI response request received");

    try {
      const chat = await this.elasticMCPClientService.queryChatCompletion(
        query,
        personId
      );

      this.logger.info({ personId }, "AI response generated successfully");
      return { aiResponse: chat.content as string };
    } catch (error) {
      this.logger.error(
        {
          personId,
          query,
          error: error instanceof Error ? error.message : String(error)
        },
        "Error generating AI response"
      );

      // Return a graceful error message instead of throwing
      return {
        aiResponse:
          "I'm sorry, I couldn't generate a response at this time. Please try again later."
      };
    }
  }

  @RpcMethod()
  async getLocationsForPerson(
    personId: string,
    projectId: string
  ): Promise<HCPLocation[]> {
    const data = await this.elasticService.query<HCPDocumentWithOnlyLocations>({
      _source: ["locations"],
      index: this.peopleIndex,
      query: {
        bool: {
          filter: [
            buildTermQuery("id", personId),
            buildTermQuery("projectIds", projectId)
          ]
        }
      }
    });

    return extractLocationField(
      data.hits.hits[0]?._source ?? {
        locations: []
      }
    );
  }

  @RpcMethod()
  async profileSearch(
    personId: string,
    queries: string[],
    projectId: string
  ): Promise<GenericSearchResultInterface[]> {
    const features =
      await this.projectFeaturesResourceClient.getProjectFeatures(projectId);
    const req = buildGenericProfileSearchQuery(personId, queries, features);

    this.logger.debug({ data: req }, "Performing generic profile search");
    const results = [] as any;

    if (req) {
      const data =
        await this.elasticService.getSignedElasticRequest<ProfileSearch>(
          req,
          "people"
        );

      const pushUnique = (item: DocType) => {
        const exists = (results as DocType[]).find(
          (r) => r.id === item.id && r.type === item.type
        );

        if (!exists) {
          results.push(item);
        }
      };

      data.aggregations.payments.paymentsFilters.buckets.forEach((bucket) => {
        bucket.paymentValues.buckets.forEach((e) => {
          pushUnique({
            id: e.key,
            type: GenericSearchEnum.PAYMENTS,
            internalCount: e.amount.value
          });
        });
      });

      data.aggregations.congress.congressFilters.buckets.forEach((bucket) => {
        bucket.congressIds.buckets.forEach((e) => {
          pushUnique({ id: e.key, type: GenericSearchEnum.CONGRESS });
        });
      });

      data.aggregations.trials.trialsFilters.buckets.forEach((bucket) => {
        bucket.trialsIds.buckets.forEach((e) => {
          pushUnique({ id: e.key, type: GenericSearchEnum.CLINICALTRIAL });
        });
      });

      data.aggregations.publications.publicationFilters.buckets.forEach(
        (bucket) => {
          bucket.publicationsIds.buckets.forEach((e) => {
            pushUnique({ id: e.key, type: GenericSearchEnum.PUBLICATION });
          });
        }
      );

      if (features?.claims) {
        data.aggregations.diagnoses?.diagnosesFilters.buckets.forEach(
          (bucket) => {
            bucket.diagnosesIds.buckets.forEach((e) => {
              pushUnique({
                id: e.key,
                type: GenericSearchEnum.DIAGNOSES,
                internalCount: e.internalCount.value
              });
            });
          }
        );

        // "procedures.procedureCode" is defined in the elasticsearch mappings as a "text" value rather than a "keyword",
        // which means that we cannot use a bucket query to break down the totals by procedureCode.  However, it appears
        // like the front-end does not even use these values broken down by code, so can possibly change diagnoses and
        // the others above to be totals rather than bucketed breakdowns by ID.
        let procedureCount = 0;

        data.aggregations.procedures?.proceduresFilters.buckets.forEach((b) => {
          procedureCount += b.internalCount.value;
        });

        pushUnique({
          id: "TOTAL",
          type: GenericSearchEnum.PROCEDURES,
          internalCount: procedureCount
        });
      }
    }

    return results;
  }

  // TODO: this should probably be renamed
  private async figureOutWhichTermsAndLanguageToUse(
    terms: Array<string> | undefined,
    profileSearchAssets: Array<ProfileSearchAsset>,
    usersLanguage: string = ENGLISH
  ): Promise<
    Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>> | undefined
  > {
    if (Array.isArray(terms) && terms.length) {
      const queryParserResponse =
        await this.queryParserService.parseQueryWithQueryUnderstandingService(
          terms[0],
          {
            projectSupportsAdvancedOperators: true
          }
        );

      const parsedQueryTree = queryParserResponse!.parsedQueryTree!;

      return profileSearchAssets.reduce((acc, profileSearchAsset) => {
        const { fields, multiLangSupport } =
          ASSET_FIELDS_TO_QUERY[profileSearchAsset];

        const languageDetector = this.getLanguageDetector(
          multiLangSupport,
          usersLanguage
        );

        acc[profileSearchAsset] =
          this.parsedQueryTreeToElasticsearchQueriesService.parse(
            parsedQueryTree,
            fields,
            languageDetector
          );
        return acc;
      }, {} as Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>>);
    }

    return undefined;
  }

  private getLanguageDetector(
    multiLangSupport: MultiLangSupport,
    usersLanguage: string
  ):
    | LanguageDetector
    | typeof ENGLISH
    | typeof MULTIPLE_LANGUAGES_NOT_APPLICABLE {
    switch (multiLangSupport) {
      case MultiLangSupport.AVAILABLE:
        return this.languageDetectService.getLanguageDetector(usersLanguage);
      case MultiLangSupport.ENGLISH_ONLY:
        return ENGLISH;
      default:
        return MULTIPLE_LANGUAGES_NOT_APPLICABLE;
    }
  }

  private async getTweetsNestedInnerQuery(
    suppliedTerms: string[] | undefined,
    userLanguage: string | undefined
  ): Promise<QueryDslQueryContainer> {
    const terms = await this.figureOutWhichTermsAndLanguageToUse(
      suppliedTerms,
      [ProfileSearchAsset.Tweets],
      userLanguage
    );

    if (!terms) {
      return { match_all: {} };
    }

    const tweetsContainer = get(terms, ProfileSearchAsset.Tweets);

    // I don't believe this is possible. So there isn't a test for it
    if (!tweetsContainer) {
      return {
        match_all: {}
      };
    }

    return {
      bool: {
        must: [tweetsContainer]
      }
    };
  }

  private async buildTweetsQuery(
    suppliedTerms: string[] | undefined,
    userLanguage: string | undefined,
    personId: string,
    projectId: string,
    page: {
      limit: number;
      offset: number;
    }
  ): Promise<QueryDslQueryContainer> {
    const innerNestedQuery = await this.getTweetsNestedInnerQuery(
      suppliedTerms,
      userLanguage
    );

    return {
      bool: {
        must: [
          {
            terms: {
              id: [personId]
            }
          },
          {
            terms: {
              projectIds: [projectId]
            }
          },
          {
            nested: {
              path: TWEETS_NESTED_PATH,
              query: innerNestedQuery,
              inner_hits: {
                from: page.offset,
                size: page.limit,
                sort: TWEETS_SORT_FIELDS.map((field) => ({
                  [field]: TWEETS_SORT_DIRECTION
                })) as SortCombinations[]
              }
            }
          }
        ]
      }
    };
  }

  private extractTotalValue(total: number | estypes.SearchTotalHits): number {
    if (typeof total === "number") {
      return total;
    }
    return total.value;
  }
}
