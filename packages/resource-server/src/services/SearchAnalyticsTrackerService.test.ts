import { faker } from "@faker-js/faker";
import {
  AnalyticsResourceClient,
  analyticsTrackArgs
} from "@h1nyc/account-sdk";
import { createMockInstance } from "../util/TestUtils";
import { SearchAnalyticsTracerService } from "./SearchAnalyticsTrackerService";

describe("SearchAnalyticsTrackerService", () => {
  describe("sendAnalyticsEvent", () => {
    it("should track values exactly how it is passed", async () => {
      const analyticsClient = createMockInstance(AnalyticsResourceClient);
      const searchAnalyticsTrackerService = new SearchAnalyticsTracerService(
        analyticsClient
      );

      const metrics: analyticsTrackArgs = {
        event: faker.datatype.string(),
        userId: faker.datatype.string(),
        properties: {
          someMetric: faker.datatype.string()
        }
      };
      await searchAnalyticsTrackerService.sendAnalyticsEvent(metrics);
      expect(analyticsClient.analyticsTrack).toHaveBeenCalledWith(metrics);
    });

    it("should silently log error when client throws an error", async () => {
      const analyticsClient = createMockInstance(AnalyticsResourceClient);
      analyticsClient.analyticsTrack.mockImplementation(() => {
        throw new Error("analyticsTrack threw an error");
      });
      const searchAnalyticsTrackerService = new SearchAnalyticsTracerService(
        analyticsClient
      );
      const loggerSpy = jest.spyOn(
        searchAnalyticsTrackerService["logger"],
        "error"
      );

      const metrics: analyticsTrackArgs = {
        event: faker.datatype.string(),
        userId: faker.datatype.string(),
        properties: {
          someMetric: faker.datatype.string()
        }
      };
      await searchAnalyticsTrackerService.sendAnalyticsEvent(metrics);
      expect(analyticsClient.analyticsTrack).toHaveBeenCalledWith(metrics);
      expect(analyticsClient.analyticsTrack).toThrowError();
      expect(loggerSpy).toHaveBeenCalled();
    });
  });
});
