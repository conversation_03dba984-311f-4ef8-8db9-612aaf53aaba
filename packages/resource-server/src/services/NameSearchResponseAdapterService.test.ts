import { faker } from "@faker-js/faker";
import {
  FOPersonSearchResponse,
  FOResultCardData,
  PersonCard,
  PersonSearchResponse,
  QueryIntent as QueryIntentEnum
} from "@h1nyc/search-sdk";
import { NameSearchInput } from "@h1nyc/search-sdk/dist/interfaces/NameSearchInput";
import { createMockInstance } from "../util/TestUtils";
import { HCPDocument, Location } from "./KeywordSearchResourceServiceRewrite";
import { generateMockElasticsearchHit } from "./KeywordSearchResourceServiceRewrite.test";
import {
  EMPTY_FILTER_COUNTS,
  EMPTY_RANGES,
  getHIPAACompliantCount,
  ZERO_MIN_MAX
} from "./KeywordSearchResponseAdapterService";
import { ENGLISH, CHINESE, JAPANESE } from "./LanguageDetectService";
import {
  NameSearchHighConfPersonAdapterService,
  stripLanguageSuffix
} from "./NameSearchHighConfPersonAdapterService";
import {
  generateNameSearchInput,
  projectId
} from "./NameSearchResourceServiceRewrite.test";
import {
  UNUSED_NUMBER_ARRAY,
  defaultScoreRanges,
  NameSearchResponseAdapterService
} from "./NameSearchResponseAdapterService";

import {
  SearchHit,
  SearchHitsMetadata,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import _ from "lodash";
import { AffiliationAdapterService } from "./AffiliationAdapterService";
import { NameSearchFeatureFlags } from "./NameSearchResourceServiceRewrite";
import { HCPLocation } from "@h1nyc/search-sdk";

const esHits = [
  generateMockElasticsearchHit(projectId),
  generateMockElasticsearchHit(projectId)
];

function generatePersonCard(
  hit: SearchHit<HCPDocument>,
  langSuffix: string,
  projectId: string
): PersonCard {
  return {
    personId: hit._source?.id || "",
    h1dnId: hit._source?.h1dn_id || "",
    name: _.get(hit._source, `name_${langSuffix}`, ""),
    firstName: _.get(hit._source, `firstName_${langSuffix}`, ""),
    middleName: _.get(hit._source, `middleName_${langSuffix}`, ""),
    lastName: _.get(hit._source, `lastName_${langSuffix}`, ""),
    nameEng: hit._source?.name_eng || "",
    firstNameEng: hit._source?.firstName_eng || "",
    middleNameEng: hit._source?.middleName_eng || "",
    lastNameEng: hit._source?.lastName_eng || "",
    score: 0,
    countPublications: hit._source?.publicationCount || 0,
    countPresentWorkAffiliations: hit._source?.presentWorkInstitutionCount || 0,
    countClinicalTrials: hit._source?.trialCount || 0,
    socialMediaMentionsTotal: hit._source?.microBloggingTotal || 0,
    referralsReceivedCount: hit._source?.referralsReceivedCount || 0,
    referralsSentCount: hit._source?.referralsSentCount || 0,
    sumPayments: hit._source?.paymentTotal || 0,
    congresses: hit._source?.congressCount || 0,
    grants: 0,
    sumGrants: 0,
    specialty: _.get(hit._source, `specialty_${langSuffix}`, []),
    congressesDates: UNUSED_NUMBER_ARRAY,
    paymentDates: UNUSED_NUMBER_ARRAY,
    publicationDates: UNUSED_NUMBER_ARRAY,
    trialDates: UNUSED_NUMBER_ARRAY,
    citationCount: hit._source?.citationTotal || 0,
    citationCountAvg: 0,
    infoRequestsResolved: null,
    tags: [],
    diagnosesCount: hit._source?.DRG_diagnosesCount,
    proceduresCount: hit._source?.DRG_proceduresCount,
    affiliations: [],
    languageCode: langSuffix,
    designations: hit._source?.designations,
    totalWorks: hit._source?.totalWorks || 0,
    scores: {
      personId: hit._source?.id || "",
      normalizedRange: { ...ZERO_MIN_MAX },
      ...defaultScoreRanges
    },
    isOutsideUsersSlice: !_.includes(hit._source?.projectIds, projectId),
    l1Indications: [],
    topL3Indications: [],
    locations: extractLocationField(hit._source!),
    isInactive: hit._source?.isInactive,
    isIndustry: hit._source?.isIndustry,
    isSocietyMember: hit._source?.hasSocietyAffiliation,
    isGlobalLeader: hit._source!.isGlobalLeader,
    isNationalLeader: hit._source!.nationalLeader?.value,
    isRegionalLeader: hit._source!.regionalLeader?.value,
    isLocalLeader: hit._source!.localLeader?.value,
    trialEnrollmentRate: hit._source?.trialEnrollmentRate,
    diagnosesUniqueCount: getHIPAACompliantCount(
      hit._source?.DRG_diagnosesUniqueCount
    ),
    proceduresUniqueCount: getHIPAACompliantCount(
      hit._source?.DRG_proceduresUniqueCount
    ),
    prescriptionsUniqueCount: getHIPAACompliantCount(
      hit._source?.prescriptions_patient_count
    )
  };
}

function generateFOPersonCard(
  hit: SearchHit<HCPDocument>,
  langSuffix: string
): FOResultCardData {
  return {
    foPersonId: hit._source?.id || "",
    firstName: _.get(hit._source, `firstName_${langSuffix}`, ""),
    middleName: _.get(hit._source, `middleName_${langSuffix}`, ""),
    lastName: _.get(hit._source, `lastName_${langSuffix}`, ""),
    firstNameEng: hit._source?.firstName_eng || "",
    middleNameEng: hit._source?.middleName_eng || "",
    lastNameEng: hit._source?.lastName_eng || "",
    specialty: _.get(hit._source, `specialty_${langSuffix}`, []),
    orcidId: hit._source?.orcidId || undefined,
    emails: hit._source?.emails || [],
    languageCode: langSuffix,
    locations: extractLocationField(hit._source!),
    designations: hit._source?.designations,
    name: _.get(hit._source, `name_${langSuffix}`, ""),
    nameEng: hit._source?.name_eng || ""
  };
}

export function generateMockPersonSearchResponse(
  input: NameSearchInput,
  results: Array<PersonCard> = [],
  hitsLength?: number
): PersonSearchResponse {
  return {
    from: input.page.from,
    queryIntent: [],
    pageSize: input.page.size,
    results,
    total: hitsLength || esHits.length,
    ranges: EMPTY_RANGES,
    normalizedRange: { ...ZERO_MIN_MAX },
    filterCounts: EMPTY_FILTER_COUNTS
  };
}

export function generateMockFOPersonSearchResponse(
  input: NameSearchInput,
  results: Array<FOResultCardData> = [],
  hitsLength?: number
): FOPersonSearchResponse {
  return {
    from: input.page.from,
    pageSize: input.page.size,
    results,
    total: hitsLength || esHits.length
  };
}

function extractLocationField(
  source: Pick<HCPDocument, "locations">
): HCPLocation[] {
  const hcpLocations = source?.locations?.map(
    (location: Location): HCPLocation => {
      const { languageCode } = location;
      const fields = ["city", "state", "country", "zipCode5"].map(
        (prop) => `${prop}_${languageCode}`
      );

      const locFields: {
        city: string;
        state: string;
        country: string;
        zipCode5: string;
      } = _(location)
        .pick(fields)
        .mapKeys((value, key) => stripLanguageSuffix(key))
        .value() as {
        city: string;
        state: string;
        country: string;
        zipCode5: string;
      };

      return locFields;
    }
  );
  return hcpLocations ?? [];
}

function generateNameSearchFeatureFlags(
  overrides?: Partial<NameSearchFeatureFlags>
): NameSearchFeatureFlags {
  const flags: NameSearchFeatureFlags = {
    enableCTMSV2: false,
    enableHighConfHCPFeature: false,
    enableQueryIntent: false,
    enableIntentBasedSearchQuery: false,
    enableNameSearchPersonalisation: false,
    enableResultsOutsideUsersSlice: false,
    enableTagsInElasticsearch: false,
    enableUniquePatientCountForClaims: false,
    enableBrazilianClaims: true,
    disableUniquePatientCountForOnlyProcedures: false,
    enableNestedIndicationFilter: false,
    enableCcsrExclusionForMatchedCounts: false,
    enableLocationFilterRegionRollup: false,
    enableNewGlobalLeaderTier: false,
    enableExUSGlobalLeader: false,
    enableCountrySpecificNonIndicationLeaderFilters: false
  };

  return { ...flags, ...overrides };
}

describe("NameSearchResponseAdapterService", () => {
  describe("Language specific fields", () => {
    it("should use _eng fields when queryLang is 'eng'", async () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHits.length
        } as SearchTotalHits,
        hits: esHits
      };

      const queryLang = ENGLISH;
      const queryIntents: QueryIntentEnum[] = [];
      const enableHighConfHCPFeature = false;

      const response = nameSearchResponseAdapterService.adapt(
        queryLang,
        input.page,
        hits,
        queryIntents,
        input.projectFeatures,
        enableHighConfHCPFeature,
        generateNameSearchFeatureFlags(),
        input,
        false
      );

      const personCards = [
        {
          ...generatePersonCard(esHits[0], queryLang, input.projectId),
          affiliationHighlights: []
        },
        {
          ...generatePersonCard(esHits[1], queryLang, input.projectId),
          affiliationHighlights: []
        }
      ];
      const personSearchResponse = generateMockPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });

    it("should use _cmn fields when queryLang is 'cmn'", async () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHits.length
        } as SearchTotalHits,
        hits: esHits
      };

      const queryLang = CHINESE;
      const queryIntents: QueryIntentEnum[] = [];
      const enableHighConfHCPFeature = false;

      const response = nameSearchResponseAdapterService.adapt(
        queryLang,
        input.page,
        hits,
        queryIntents,
        input.projectFeatures,
        enableHighConfHCPFeature,
        generateNameSearchFeatureFlags(),
        input,
        false
      );

      const personCards = [
        {
          ...generatePersonCard(esHits[0], queryLang, input.projectId),
          affiliationHighlights: []
        },
        {
          ...generatePersonCard(esHits[1], queryLang, input.projectId),
          affiliationHighlights: []
        }
      ];
      const personSearchResponse = generateMockPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });

    it("should use _jpn fields when queryLang is 'jpn'", async () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHits.length
        } as SearchTotalHits,
        hits: esHits
      };

      const queryLang = JAPANESE;
      const queryIntents: QueryIntentEnum[] = [];
      const enableHighConfHCPFeature = false;

      const response = nameSearchResponseAdapterService.adapt(
        queryLang,
        input.page,
        hits,
        queryIntents,
        input.projectFeatures,
        enableHighConfHCPFeature,
        generateNameSearchFeatureFlags(),
        input,
        false
      );

      const personCards = [
        {
          ...generatePersonCard(esHits[0], queryLang, input.projectId),
          affiliationHighlights: []
        },
        {
          ...generatePersonCard(esHits[1], queryLang, input.projectId),
          affiliationHighlights: []
        }
      ];
      const personSearchResponse = generateMockPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });

    it("should use _jpn and _cmn fields when queryLang is 'jpn' and useCmnAndJpnFields is true", async () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();
      const esHitsWithoutJapaneseNames: SearchHit<HCPDocument>[] = esHits.map(
        (hit) => {
          return {
            ...hit,
            _source: {
              ...hit._source!,
              name_jpn: "",
              lastName_jpn: "",
              firstName_jpn: ""
            }
          };
        }
      );
      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHitsWithoutJapaneseNames.length
        } as SearchTotalHits,
        hits: esHitsWithoutJapaneseNames
      };

      const queryLang = JAPANESE;
      const queryIntents: QueryIntentEnum[] = [];
      const enableHighConfHCPFeature = false;

      const response = nameSearchResponseAdapterService.adapt(
        queryLang,
        input.page,
        hits,
        queryIntents,
        input.projectFeatures,
        enableHighConfHCPFeature,
        generateNameSearchFeatureFlags(),
        input,
        true
      );

      const personCards = [
        {
          ...generatePersonCard(esHits[0], CHINESE, input.projectId),
          languageCode: JAPANESE,
          affiliationHighlights: []
        },
        {
          ...generatePersonCard(esHits[1], CHINESE, input.projectId),
          languageCode: JAPANESE,
          affiliationHighlights: []
        }
      ];
      const personSearchResponse = generateMockPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });

    it("should use _jpn and _cmn fields when queryLang is 'cmn' and useCmnAndJpnFields is true", async () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();
      const esHitsWithoutChineseNames: SearchHit<HCPDocument>[] = esHits.map(
        (hit) => {
          return {
            ...hit,
            _source: {
              ...hit._source!,
              name_cmn: "",
              lastName_cmn: "",
              firstName_cmn: ""
            }
          };
        }
      );

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHitsWithoutChineseNames.length
        } as SearchTotalHits,
        hits: esHitsWithoutChineseNames
      };

      const queryLang = CHINESE;
      const queryIntents: QueryIntentEnum[] = [];
      const enableHighConfHCPFeature = false;

      const response = nameSearchResponseAdapterService.adapt(
        queryLang,
        input.page,
        hits,
        queryIntents,
        input.projectFeatures,
        enableHighConfHCPFeature,
        generateNameSearchFeatureFlags(),
        input,
        true
      );

      const personCards = [
        {
          ...generatePersonCard(esHits[0], JAPANESE, input.projectId),
          languageCode: CHINESE,
          affiliationHighlights: []
        },
        {
          ...generatePersonCard(esHits[1], JAPANESE, input.projectId),
          languageCode: CHINESE,
          affiliationHighlights: []
        }
      ];
      const personSearchResponse = generateMockPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });
  });

  describe("Highlighting", () => {
    describe("Affiliation highlights", () => {
      it("should set affiliationHighlights as empty array if ES response does not have any match", async () => {
        const nameSearchHighConfPersonAdapterService = createMockInstance(
          NameSearchHighConfPersonAdapterService
        );
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );

        const nameSearchResponseAdapterService =
          new NameSearchResponseAdapterService(
            nameSearchHighConfPersonAdapterService,
            affiliationAdapterService
          );

        const input = generateNameSearchInput();
        const mockHits: SearchHit<HCPDocument>[] = [
          {
            ...generateMockElasticsearchHit(),
            inner_hits: {
              affiliations_0: {
                hits: {
                  total: {
                    value: 0,
                    relation: "eq"
                  },
                  hits: []
                }
              }
            }
          },
          {
            ...generateMockElasticsearchHit(),
            inner_hits: {
              affiliations_0: {
                hits: {
                  total: {
                    value: 0,
                    relation: "eq"
                  },
                  hits: []
                }
              }
            }
          }
        ];

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: esHits.length
          } as SearchTotalHits,
          hits: mockHits
        };

        const queryLang = ENGLISH;
        const queryIntents: QueryIntentEnum[] = [];
        const enableHighConfHCPFeature = false;

        const response = nameSearchResponseAdapterService.adapt(
          queryLang,
          input.page,
          hits,
          queryIntents,
          input.projectFeatures,
          enableHighConfHCPFeature,
          generateNameSearchFeatureFlags(),
          input,
          false
        );

        const personCards: PersonCard[] = [
          {
            ...generatePersonCard(mockHits[0], queryLang, input.projectId),
            affiliationHighlights: []
          },
          {
            ...generatePersonCard(mockHits[1], queryLang, input.projectId),
            affiliationHighlights: []
          }
        ];
        const personSearchResponse = generateMockPersonSearchResponse(
          input,
          personCards
        );
        expect(response).toEqual(personSearchResponse);
      });

      it("should set affiliationHighlights as empty array if affiliation id is not present", async () => {
        const nameSearchHighConfPersonAdapterService = createMockInstance(
          NameSearchHighConfPersonAdapterService
        );
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        // nameSearchHighConfPersonAdapterService.getHitsTotal.mockReturnValue(1);

        const nameSearchResponseAdapterService =
          new NameSearchResponseAdapterService(
            nameSearchHighConfPersonAdapterService,
            affiliationAdapterService
          );

        const input = generateNameSearchInput();
        const mockHits: SearchHit<HCPDocument>[] = [
          {
            ...generateMockElasticsearchHit(),
            inner_hits: {
              affiliations_0: {
                hits: {
                  total: {
                    value: 1,
                    relation: "eq"
                  },
                  hits: [
                    {
                      _index: "people",
                      _id: faker.datatype.string(),
                      _nested: {
                        field: "affiliations",
                        offset: 0
                      },
                      _score: faker.datatype.number(),
                      fields: {
                        "affiliations.id": [],
                        "affiliations.institution.id": [faker.datatype.string()]
                      },
                      highlight: {
                        "affiliations.institution.address.city": [
                          faker.datatype.string()
                        ],
                        "affiliations.institution.address.region": [
                          faker.datatype.string()
                        ],
                        "affiliations.institution.address.country": [
                          faker.datatype.string()
                        ]
                      }
                    }
                  ]
                }
              }
            }
          }
        ];

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: mockHits.length
          } as SearchTotalHits,
          hits: mockHits
        };

        const queryLang = ENGLISH;
        const queryIntents: QueryIntentEnum[] = [];
        const enableHighConfHCPFeature = false;

        const response = nameSearchResponseAdapterService.adapt(
          queryLang,
          input.page,
          hits,
          queryIntents,
          input.projectFeatures,
          enableHighConfHCPFeature,
          generateNameSearchFeatureFlags(),
          input,
          false
        );

        const personCards: PersonCard[] = [
          {
            ...generatePersonCard(mockHits[0], queryLang, input.projectId),
            affiliationHighlights: []
          }
        ];
        const personSearchResponse = generateMockPersonSearchResponse(
          input,
          personCards,
          1
        );
        expect(response).toEqual(personSearchResponse);
      });

      it("should set affiliationHighlights as empty array if institution id is not present", async () => {
        const nameSearchHighConfPersonAdapterService = createMockInstance(
          NameSearchHighConfPersonAdapterService
        );
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        // nameSearchHighConfPersonAdapterService.getHitsTotal.mockReturnValue(1);

        const nameSearchResponseAdapterService =
          new NameSearchResponseAdapterService(
            nameSearchHighConfPersonAdapterService,
            affiliationAdapterService
          );

        const input = generateNameSearchInput();
        const mockHits: SearchHit<HCPDocument>[] = [
          {
            ...generateMockElasticsearchHit(),
            inner_hits: {
              affiliations_0: {
                hits: {
                  total: {
                    value: 1,
                    relation: "eq"
                  },
                  hits: [
                    {
                      _index: "people",
                      _id: faker.datatype.string(),
                      _nested: {
                        field: "affiliations",
                        offset: 0
                      },
                      _score: faker.datatype.number(),
                      fields: {
                        "affiliations.id": [faker.datatype.string()],
                        "affiliations.institution.id": []
                      },
                      highlight: {
                        "affiliations.institution.address.city": [
                          faker.datatype.string()
                        ],
                        "affiliations.institution.address.region": [
                          faker.datatype.string()
                        ],
                        "affiliations.institution.address.country": [
                          faker.datatype.string()
                        ]
                      }
                    }
                  ]
                }
              }
            }
          }
        ];

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: mockHits.length
          } as SearchTotalHits,
          hits: mockHits
        };

        const queryLang = ENGLISH;
        const queryIntents: QueryIntentEnum[] = [];
        const enableHighConfHCPFeature = false;

        const response = nameSearchResponseAdapterService.adapt(
          queryLang,
          input.page,
          hits,
          queryIntents,
          input.projectFeatures,
          enableHighConfHCPFeature,
          generateNameSearchFeatureFlags(),
          input,
          false
        );

        const personCards: PersonCard[] = [
          {
            ...generatePersonCard(mockHits[0], queryLang, input.projectId),
            affiliationHighlights: []
          }
        ];
        const personSearchResponse = generateMockPersonSearchResponse(
          input,
          personCards,
          1
        );
        expect(response).toEqual(personSearchResponse);
      });

      it("should set affiliationHighlights if there are valid highlight hits", async () => {
        const nameSearchHighConfPersonAdapterService = createMockInstance(
          NameSearchHighConfPersonAdapterService
        );
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        // nameSearchHighConfPersonAdapterService.getHitsTotal.mockReturnValue(1);

        const nameSearchResponseAdapterService =
          new NameSearchResponseAdapterService(
            nameSearchHighConfPersonAdapterService,
            affiliationAdapterService
          );
        const affiliationId = faker.datatype.string();
        const institutionId = faker.datatype.string();
        const city = faker.datatype.string();
        const region = faker.datatype.string();
        const country = faker.datatype.string();

        const input = generateNameSearchInput();
        const mockHits: SearchHit<HCPDocument>[] = [
          {
            ...generateMockElasticsearchHit(),
            inner_hits: {
              affiliations_0: {
                hits: {
                  total: {
                    value: 1,
                    relation: "eq"
                  },
                  hits: [
                    {
                      _index: "people",
                      _id: faker.datatype.string(),
                      _nested: {
                        field: "affiliations",
                        offset: 0
                      },
                      _score: faker.datatype.number(),
                      fields: {
                        "affiliations.id": [affiliationId],
                        "affiliations.institution.id": [institutionId]
                      },
                      highlight: {
                        "affiliations.institution.address.city": [city],
                        "affiliations.institution.address.region": [region],
                        "affiliations.institution.address.country": [country]
                      }
                    }
                  ]
                }
              }
            }
          }
        ];

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: mockHits.length
          } as SearchTotalHits,
          hits: mockHits
        };

        const queryLang = ENGLISH;
        const queryIntents: QueryIntentEnum[] = [];
        const enableHighConfHCPFeature = false;

        const response = nameSearchResponseAdapterService.adapt(
          queryLang,
          input.page,
          hits,
          queryIntents,
          input.projectFeatures,
          enableHighConfHCPFeature,
          generateNameSearchFeatureFlags(),
          input,
          false
        );

        const personCards: PersonCard[] = [
          {
            ...generatePersonCard(mockHits[0], queryLang, input.projectId),
            affiliationHighlights: [
              {
                affiliationId,
                highlight: {
                  institution: {
                    institutionId,
                    address: {
                      city,
                      region,
                      country
                    }
                  }
                }
              }
            ]
          }
        ];
        const personSearchResponse = generateMockPersonSearchResponse(
          input,
          personCards,
          1
        );
        expect(response).toEqual(personSearchResponse);
      });

      it("should set affiliationHighlights if there are valid highlights for multiple locations", async () => {
        const nameSearchHighConfPersonAdapterService = createMockInstance(
          NameSearchHighConfPersonAdapterService
        );
        const affiliationAdapterService = createMockInstance(
          AffiliationAdapterService
        );
        // nameSearchHighConfPersonAdapterService.getHitsTotal.mockReturnValue(1);

        const nameSearchResponseAdapterService =
          new NameSearchResponseAdapterService(
            nameSearchHighConfPersonAdapterService,
            affiliationAdapterService
          );
        const affiliationId1 = faker.datatype.string();
        const institutionId1 = faker.datatype.string();
        const city1 = faker.datatype.string();
        const region1 = faker.datatype.string();
        const country1 = faker.datatype.string();

        const affiliationId2 = faker.datatype.string();
        const institutionId2 = faker.datatype.string();
        const city2 = faker.datatype.string();
        const region2 = faker.datatype.string();
        const country2 = faker.datatype.string();

        const input = generateNameSearchInput();
        const mockHits: SearchHit<HCPDocument>[] = [
          {
            ...generateMockElasticsearchHit(),
            inner_hits: {
              affiliations_0: {
                hits: {
                  total: {
                    value: 1,
                    relation: "eq"
                  },
                  hits: [
                    {
                      _index: "people",
                      _id: faker.datatype.string(),
                      _nested: {
                        field: "affiliations",
                        offset: 0
                      },
                      _score: faker.datatype.number(),
                      fields: {
                        "affiliations.id": [affiliationId1],
                        "affiliations.institution.id": [institutionId1]
                      },
                      highlight: {
                        "affiliations.institution.address.city": [city1],
                        "affiliations.institution.address.region": [region1],
                        "affiliations.institution.address.country": [country1]
                      }
                    }
                  ]
                }
              },
              affiliations_1: {
                hits: {
                  total: {
                    value: 1,
                    relation: "eq"
                  },
                  hits: [
                    {
                      _index: "people",
                      _id: faker.datatype.string(),
                      _nested: {
                        field: "affiliations",
                        offset: 0
                      },
                      _score: faker.datatype.number(),
                      fields: {
                        "affiliations.id": [affiliationId2],
                        "affiliations.institution.id": [institutionId2]
                      },
                      highlight: {
                        "affiliations.institution.address.city": [city2],
                        "affiliations.institution.address.region": [region2],
                        "affiliations.institution.address.country": [country2]
                      }
                    }
                  ]
                }
              }
            }
          }
        ];

        const hits: SearchHitsMetadata<HCPDocument> = {
          total: {
            value: mockHits.length
          } as SearchTotalHits,
          hits: mockHits
        };

        const queryLang = ENGLISH;
        const queryIntents: QueryIntentEnum[] = [];
        const enableHighConfHCPFeature = false;

        const response = nameSearchResponseAdapterService.adapt(
          queryLang,
          input.page,
          hits,
          queryIntents,
          input.projectFeatures,
          enableHighConfHCPFeature,
          generateNameSearchFeatureFlags(),
          input,
          false
        );

        const personCards: PersonCard[] = [
          {
            ...generatePersonCard(mockHits[0], queryLang, input.projectId),
            affiliationHighlights: [
              {
                affiliationId: affiliationId1,
                highlight: {
                  institution: {
                    institutionId: institutionId1,
                    address: {
                      city: city1,
                      region: region1,
                      country: country1
                    }
                  }
                }
              },
              {
                affiliationId: affiliationId2,
                highlight: {
                  institution: {
                    institutionId: institutionId2,
                    address: {
                      city: city2,
                      region: region2,
                      country: country2
                    }
                  }
                }
              }
            ]
          }
        ];
        const personSearchResponse = generateMockPersonSearchResponse(
          input,
          personCards,
          1
        );
        expect(response).toEqual(personSearchResponse);
      });
    });
  });

  describe("Adapt FO response", () => {
    it("should use _eng fields when queryLang is 'eng'", () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );
      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHits.length
        } as SearchTotalHits,
        hits: esHits
      };

      const queryLang = ENGLISH;

      const response = nameSearchResponseAdapterService.adaptFOResponse(
        queryLang,
        input.page,
        hits
      );

      const personCards = [
        generateFOPersonCard(esHits[0], queryLang),
        generateFOPersonCard(esHits[1], queryLang)
      ];
      const personSearchResponse = generateMockFOPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });
    it("should use _cmn fields when queryLang is 'cmn'", () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );
      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHits.length
        } as SearchTotalHits,
        hits: esHits
      };

      const queryLang = CHINESE;

      const response = nameSearchResponseAdapterService.adaptFOResponse(
        queryLang,
        input.page,
        hits
      );

      const personCards = [
        generateFOPersonCard(esHits[0], queryLang),
        generateFOPersonCard(esHits[1], queryLang)
      ];
      const personSearchResponse = generateMockFOPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });
    it("should use _jpn fields when queryLang is 'jpn'", () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );

      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );
      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHits.length
        } as SearchTotalHits,
        hits: esHits
      };

      const queryLang = JAPANESE;

      const response = nameSearchResponseAdapterService.adaptFOResponse(
        queryLang,
        input.page,
        hits
      );

      const personCards = [
        generateFOPersonCard(esHits[0], queryLang),
        generateFOPersonCard(esHits[1], queryLang)
      ];
      const personSearchResponse = generateMockFOPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });
  });

  describe("Response Parsing", () => {
    it("should return hasCTMSData in the response, using the value of inCtmsNetwork, if the enable-ctms-v2 flag is enabled", () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHits.length
        } as SearchTotalHits,
        hits: esHits
      };

      const queryLang = ENGLISH;
      const queryIntents: QueryIntentEnum[] = [];
      const enableHighConfHCPFeature = false;

      const response = nameSearchResponseAdapterService.adapt(
        queryLang,
        input.page,
        hits,
        queryIntents,
        input.projectFeatures,
        enableHighConfHCPFeature,
        generateNameSearchFeatureFlags({
          enableCTMSV2: true
        }),
        input,
        false
      );

      const personCards = [
        {
          ...generatePersonCard(esHits[0], queryLang, input.projectId),
          affiliationHighlights: [],
          hasCTMSData:
            esHits[0]._source?.inCtmsNetwork ?? esHits[0]._source?.hasCtms
        },
        {
          ...generatePersonCard(esHits[1], queryLang, input.projectId),
          affiliationHighlights: [],
          hasCTMSData:
            esHits[1]._source?.inCtmsNetwork ?? esHits[1]._source?.hasCtms
        }
      ];
      const personSearchResponse = generateMockPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });

    it("should return totalPatient docs when showUniquePatient is True", () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();
      input.suppliedFilters.claims.showUniquePatients = { value: true };

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHits.length
        } as SearchTotalHits,
        hits: esHits
      };

      const queryLang = ENGLISH;
      const queryIntents: QueryIntentEnum[] = [];
      const enableHighConfHCPFeature = false;

      const response = nameSearchResponseAdapterService.adapt(
        queryLang,
        input.page,
        hits,
        queryIntents,
        input.projectFeatures,
        enableHighConfHCPFeature,
        generateNameSearchFeatureFlags(),
        input,
        false
      );

      //override the values of diagnosesCount to represent the totalPatientDocs
      const personCards = [
        {
          ...generatePersonCard(esHits[0], queryLang, input.projectId),
          affiliationHighlights: [],
          diagnosesCount: esHits[0]._source?.totalPatientDocs
        },
        {
          ...generatePersonCard(esHits[1], queryLang, input.projectId),
          affiliationHighlights: [],
          diagnosesCount: esHits[1]._source?.totalPatientDocs
        }
      ];

      const personSearchResponse = generateMockPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });
    it("should set isOutsideUsersSlice as true, if first result is outside user's slice", () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();

      const hitsIncludingOutsideSlice = [
        generateMockElasticsearchHit(faker.datatype.string()),
        esHits[0]
      ];

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: hitsIncludingOutsideSlice.length
        } as SearchTotalHits,
        hits: hitsIncludingOutsideSlice
      };

      const queryLang = ENGLISH;
      const queryIntents: QueryIntentEnum[] = [];
      const enableHighConfHCPFeature = false;

      const response = nameSearchResponseAdapterService.adapt(
        queryLang,
        input.page,
        hits,
        queryIntents,
        input.projectFeatures,
        enableHighConfHCPFeature,
        generateNameSearchFeatureFlags(),
        input,
        false
      );

      expect(response.results[0].isOutsideUsersSlice).toEqual(true);
    });

    it("should return isSocietyMember in the response, using the value of hasSocietyAffiliation", () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput();

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: esHits.length
        } as SearchTotalHits,
        hits: esHits
      };

      const queryLang = ENGLISH;
      const queryIntents: QueryIntentEnum[] = [];
      const enableHighConfHCPFeature = false;

      const response = nameSearchResponseAdapterService.adapt(
        queryLang,
        input.page,
        hits,
        queryIntents,
        input.projectFeatures,
        enableHighConfHCPFeature,
        generateNameSearchFeatureFlags(),
        input,
        false
      );

      const personCards = [
        {
          ...generatePersonCard(esHits[0], queryLang, input.projectId),
          affiliationHighlights: [],
          hasCTMSData: undefined,
          isSocietyMember: esHits[0]._source?.hasSocietyAffiliation
        },
        {
          ...generatePersonCard(esHits[1], queryLang, input.projectId),
          affiliationHighlights: [],
          hasCTMSData: undefined,
          isSocietyMember: esHits[1]._source?.hasSocietyAffiliation
        }
      ];
      const personSearchResponse = generateMockPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });

    it("should return correct congressSessions when useCongressContributorRanking is true", () => {
      const nameSearchHighConfPersonAdapterService = createMockInstance(
        NameSearchHighConfPersonAdapterService
      );
      const affiliationAdapterService = createMockInstance(
        AffiliationAdapterService
      );

      const nameSearchResponseAdapterService =
        new NameSearchResponseAdapterService(
          nameSearchHighConfPersonAdapterService,
          affiliationAdapterService
        );

      const input = generateNameSearchInput({
        useCongressContributorRanking: true
      });
      input.suppliedFilters.congresses.name.values = [faker.datatype.string()];

      const hits: SearchHitsMetadata<HCPDocument> = {
        total: {
          value: 2
        } as SearchTotalHits,
        hits: [
          generateMockElasticsearchHit(projectId, {
            congress: [
              {
                id: faker.datatype.string(),
                name_eng: input.suppliedFilters.congresses.name.values[0],
                role: faker.datatype.string(),
                title_eng: faker.datatype.string()
              },
              {
                id: faker.datatype.string(),
                name_eng: faker.datatype.string(),
                role: faker.datatype.string(),
                title_eng: faker.datatype.string()
              }
            ]
          }),
          generateMockElasticsearchHit(projectId)
        ]
      };

      const queryLang = ENGLISH;
      const queryIntents: QueryIntentEnum[] = [];
      const enableHighConfHCPFeature = false;

      const response = nameSearchResponseAdapterService.adapt(
        queryLang,
        input.page,
        hits,
        queryIntents,
        input.projectFeatures,
        enableHighConfHCPFeature,
        generateNameSearchFeatureFlags(),
        input,
        false
      );

      const personCards = [
        {
          ...generatePersonCard(hits.hits[0], queryLang, input.projectId),
          affiliationHighlights: [],
          congressSessions: [
            {
              id: hits.hits[0]._source?.congress?.[0].id ?? "",
              name: hits.hits[0]._source?.congress?.[0].title_eng ?? "",
              role: hits.hits[0]._source?.congress?.[0].role ?? ""
            }
          ]
        },
        {
          ...generatePersonCard(hits.hits[1], queryLang, input.projectId),
          affiliationHighlights: [],
          congressSessions: []
        }
      ];

      const personSearchResponse = generateMockPersonSearchResponse(
        input,
        personCards
      );
      expect(response).toEqual(personSearchResponse);
    });
  });
});
