import {
  CTMSResource,
  RPC_NAMESPACE_CTMS,
  CTMSFacilityAggregationInput,
  CTMSFacilityAggregation,
  AggregationStats,
  CTMSFacilityAggregationResponse,
  CTMSInvestigatorAggregation,
  CTMSFacilityInitialFilterOptions,
  CTMSInvestigatorInitialFilterOptions,
  CTMSInvestigatorAggregationInput,
  CTMSFilters,
  CTMSInvestigatorAggregationResponse
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { createLogger } from "../lib/Logger";
import { Trace } from "../Tracer";
import { ConfigService } from "./ConfigService";
import { ElasticCTMSFacilityAggsService } from "./ElasticSearchCTMSFacilityAggsService";
import { ElasticCTMSInvestigatorAggsService } from "./ElasticSearchCTMSInvestigatorAggsService";
import {
  QueryDslQueryContainer,
  AggregationsSimpleValueAggregate,
  AggregationsTermsAggregateBase,
  CountRequest,
  SearchRequest
} from "@elastic/elasticsearch/lib/api/types";
import * as _ from "lodash";
import { Service } from "typedi";

export interface DocCountBucket {
  key: string;
  doc_count: number;
}

interface CTMSAggregation {
  avg_recruitment_period_on_time_pct: AggregationsSimpleValueAggregate;
  avg_patient_retention_pct: AggregationsSimpleValueAggregate;
  avg_protocol_violations_per_participant: AggregationsSimpleValueAggregate;
  avg_recruitment_period_late_pct: AggregationsSimpleValueAggregate;
  times_used_as_trial: AggregationsSimpleValueAggregate;
  avg_patient_recruitment_performance_pct: AggregationsSimpleValueAggregate;
  avg_recruitment_period_early_pct: AggregationsSimpleValueAggregate;
  avg_patient_recruitment_period: AggregationsSimpleValueAggregate;
  activation: AggregationsSimpleValueAggregate;
  phases: AggregationsTermsAggregateBase<DocCountBucket>;
  indications: AggregationsTermsAggregateBase<DocCountBucket>;
  therapeutic_area: AggregationsTermsAggregateBase<DocCountBucket>;
  investigators: AggregationsTermsAggregateBase<DocCountBucket>;
  h1_organization_ids: AggregationsTermsAggregateBase<DocCountBucket>;
}

interface FacilityHits {
  total: { value: number };
  max_score: number;
  hits: {
    _source: CTMSFacilityAggregation;
  }[];
}
interface InvestigatorHits {
  total: { value: number };
  max_score: number;
  hits: {
    _source: CTMSInvestigatorAggregation;
  }[];
}

interface CTMSPersonAggregation {
  h1_person_ids: AggregationsTermsAggregateBase<DocCountBucket>;
  avg_recruitment_period_on_time_pct: AggregationsSimpleValueAggregate;
  avg_patient_retention_pct: AggregationsSimpleValueAggregate;
  avg_protocol_violations_per_participant: AggregationsSimpleValueAggregate;
  avg_recruitment_period_late_pct: AggregationsSimpleValueAggregate;
  times_used_as_trial: AggregationsSimpleValueAggregate;
  avg_patient_recruitment_performance_pct: AggregationsSimpleValueAggregate;
  avg_recruitment_period_early_pct: AggregationsSimpleValueAggregate;
  avg_patient_recruitment_period: AggregationsSimpleValueAggregate;
  activation: AggregationsSimpleValueAggregate;
  phases: AggregationsTermsAggregateBase<DocCountBucket>;
  indications: AggregationsTermsAggregateBase<DocCountBucket>;
  therapeutic_area: AggregationsTermsAggregateBase<DocCountBucket>;
}

export interface FacilityRootObject {
  took: number;
  timed_out: boolean;
  hits: FacilityHits;
  aggregations?: CTMSAggregation;
}

export interface InvestigatorFacilityRootObject {
  took: number;
  timed_out: boolean;
  hits: InvestigatorHits;
  aggregations?: CTMSPersonAggregation;
}

export interface InitialOptionsObject {
  took: number;
  timed_out: boolean;
  hits: {
    total: { value: number };
    max_score: number;
    hits: [];
  };
  aggregations: {
    phases: AggregationsTermsAggregateBase<DocCountBucket>;
    indications: AggregationsTermsAggregateBase<DocCountBucket>;
    therapeutic_area: AggregationsTermsAggregateBase<DocCountBucket>;
  };
}

@Service()
@RpcService()
export class CTMSResourceService
  extends RpcResourceService
  implements CTMSResource
{
  private readonly logger = createLogger(this);

  constructor(
    config: ConfigService,
    private elasticFacilitySearchService: ElasticCTMSFacilityAggsService,
    private elasticInvestigatorService: ElasticCTMSInvestigatorAggsService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_CTMS,
      config.searchRedisOptions
    );
  }

  @RpcMethod()
  async isReady(): Promise<boolean> {
    return true;
  }

  @RpcMethod()
  async hasCTMSData(institutionId: string): Promise<boolean> {
    const request: CountRequest = {
      query: {
        term: {
          "h1_organization_id.keyword": {
            value: institutionId
          }
        }
      }
    };
    const results = await this.elasticFacilitySearchService.count(request);

    return results.count > 0;
  }

  @RpcMethod()
  async personHasCTMSData(personId: string): Promise<boolean> {
    const request: CountRequest = {
      query: {
        term: {
          "h1_person_id.keyword": {
            value: personId
          }
        }
      }
    };
    const results = await this.elasticInvestigatorService.count(request);

    return results.count > 0;
  }

  @RpcMethod()
  @Trace("h1-search.ctms-facility-aggregations")
  async ctmsFacilityAggregation(
    input: CTMSFacilityAggregationInput
  ): Promise<CTMSFacilityAggregationResponse> {
    const request = this.buildRequestQuery(input);

    this.logger.info(
      { query: JSON.stringify(request) },
      "CTMS Facility elasticsearch request"
    );

    const facilityResults =
      await this.elasticFacilitySearchService.getSignedElasticRequest<FacilityRootObject>(
        request
      );
    this.logger.debug(
      { took: facilityResults.took, total: facilityResults.hits.total },
      "total execution time"
    );

    const aggregations = facilityResults.aggregations;
    const result = this.mapResults(aggregations);
    const personIds = result.investigators?.map((val) => val.value);

    if (!_.isEmpty(personIds)) {
      // **
      // get h1 person info from ctms investigator index.
      const personQuery = {
        _source: [
          "h1_person_id",
          "first_name",
          "last_name",
          "times_used_as_trial"
        ],
        query: {
          terms: {
            "h1_person_id.keyword": _.uniq(personIds)
          }
        }
      };
      this.logger.info(
        { query: JSON.stringify(personQuery) },
        "CTMS Investigator elasticsearch request"
      );

      const linkedPersons =
        await this.elasticInvestigatorService.getSignedElasticRequest<InvestigatorFacilityRootObject>(
          personQuery
        );

      const linkedInvestigators = linkedPersons.hits.hits.map(
        (hit) => hit._source
      );

      // **
      // Filter unique person data based on personId.
      result.h1LinkedInvestigators = _.uniqWith(
        linkedInvestigators,
        (arr1Val, arr2Val) => {
          return arr1Val.h1_person_id === arr2Val.h1_person_id;
        }
      );
    } else {
      result.h1LinkedInvestigators = [];
    }
    return result;
  }

  @RpcMethod()
  @Trace("h1-search.ctms-facility-aggregations")
  async ctmsFacilityInitialFilterOptions(
    institutionId: string
  ): Promise<CTMSFacilityInitialFilterOptions> {
    const query: SearchRequest = {
      query: {
        bool: {
          filter: {
            term: {
              "h1_organization_id.keyword": institutionId
            }
          }
        }
      },
      size: 0,
      aggs: {
        phases: {
          terms: {
            field: "phase.keyword",
            order: {
              _count: "desc"
            }
          }
        },
        indications: {
          terms: {
            field: "indication.keyword",
            order: {
              _count: "desc"
            }
          }
        },
        therapeutic_area: {
          terms: {
            field: "therapeutic_area.keyword",
            order: {
              _count: "desc"
            }
          }
        }
      }
    };

    this.logger.info(
      { query: JSON.stringify(query) },
      "CTMS Facility elasticsearch initial filter options request"
    );
    const results =
      await this.elasticFacilitySearchService.getSignedElasticRequest<InitialOptionsObject>(
        query
      );

    return {
      phase: this.extractAggregationFromBucket(results.aggregations.phases),
      therapeutic_area: this.extractAggregationFromBucket(
        results.aggregations?.therapeutic_area
      ),
      indication: this.extractAggregationFromBucket(
        results.aggregations?.indications
      )
    };
  }

  @RpcMethod()
  @Trace("h1-search.ctms-facility-aggregations")
  async getAllCTMSFacilityIds(): Promise<string[]> {
    const maxUniqueIdsToGet = 10000;
    const request = {
      size: 0,
      aggs: {
        h1_organization_ids: {
          terms: {
            field: "h1_organization_id.keyword",
            size: maxUniqueIdsToGet
          }
        }
      }
    };
    this.logger.info(
      { query: JSON.stringify(request) },
      "CTMS Facility elasticsearch get all CTMS Facilities"
    );
    try {
      const data =
        await this.elasticFacilitySearchService.getSignedElasticRequest<FacilityRootObject>(
          request
        );
      const aggs = this.extractAggregationFromBucket(
        data.aggregations?.h1_organization_ids
      );
      // **
      // returns all unique iolIds.
      return _.compact(aggs.map((agg) => agg.value));
    } catch (error) {
      this.logger.error({ request, error }, "Error getting ctms data", error);
    }
    return [];
  }

  @RpcMethod()
  @Trace("h1-search.ctms-facility-aggregations")
  async getAllCTMSPersonIds(): Promise<string[]> {
    const maxUniqueIdsToGet = 10000;
    const request = {
      size: 0,
      aggs: {
        h1_person_ids: {
          terms: {
            field: "h1_person_id.keyword",
            size: maxUniqueIdsToGet
          }
        }
      }
    };
    this.logger.info(
      { query: JSON.stringify(request) },
      "CTMS Investigator elasticsearch getting all CTMS Investigators"
    );
    try {
      const data =
        await this.elasticInvestigatorService.getSignedElasticRequest<InvestigatorFacilityRootObject>(
          request
        );
      const aggs = this.extractAggregationFromBucket(
        data.aggregations?.h1_person_ids
      );
      // **
      // returns all unique iolIds.
      return _.compact(aggs.map((agg) => agg.value));
    } catch (error) {
      this.logger.error({ request, error }, "Error getting ctms data", error);
    }
    return [];
  }

  @RpcMethod()
  @Trace("h1-search.ctms-investigator-aggregations")
  async ctmsInvestigatorAggregation(
    input: CTMSInvestigatorAggregationInput
  ): Promise<CTMSInvestigatorAggregationResponse> {
    const request = this.buildInvestigatorRequestQuery(input);

    this.logger.info(
      { query: JSON.stringify(request) },
      "CTMS Investigator elasticsearch request"
    );

    const results =
      await this.elasticInvestigatorService.getSignedElasticRequest<InvestigatorFacilityRootObject>(
        request
      );
    this.logger.debug(
      { took: results.took, total: results.hits.total },
      "total execution time"
    );

    const aggregations = results.aggregations;
    const result = this.mapInvestigatorResults(aggregations);

    return result;
  }

  @RpcMethod()
  @Trace("h1-search.ctms-investigator-aggregations")
  async ctmsInvestigatorInitialFilterOptions(
    personId: string
  ): Promise<CTMSInvestigatorInitialFilterOptions> {
    const query: SearchRequest = {
      query: {
        bool: {
          filter: {
            term: {
              "h1_person_id.keyword": personId
            }
          }
        }
      },
      size: 0,
      aggs: {
        phases: {
          terms: {
            field: "phase.keyword",
            order: {
              _count: "desc"
            }
          }
        },
        indications: {
          terms: {
            field: "indication.keyword",
            order: {
              _count: "desc"
            }
          }
        },
        therapeutic_area: {
          terms: {
            field: "therapeutic_area.keyword",
            order: {
              _count: "desc"
            }
          }
        }
      }
    };

    this.logger.info(
      { query: JSON.stringify(query) },
      "CTMS Investigator elasticsearch initial filter options request"
    );
    const results =
      await this.elasticInvestigatorService.getSignedElasticRequest<InitialOptionsObject>(
        query
      );

    return {
      phase: this.extractAggregationFromBucket(results.aggregations.phases),
      therapeutic_area: this.extractAggregationFromBucket(
        results.aggregations?.therapeutic_area
      ),
      indication: this.extractAggregationFromBucket(
        results.aggregations?.indications
      )
    };
  }

  private mapResults(
    aggregations: CTMSAggregation | undefined
  ): CTMSFacilityAggregationResponse {
    return {
      activation: aggregations?.activation.value || undefined,
      avg_recruitment_period_on_time_pct:
        aggregations?.avg_recruitment_period_on_time_pct.value || undefined,
      avg_patient_retention_pct:
        aggregations?.avg_patient_retention_pct.value || undefined,
      avg_protocol_violations_per_participant:
        aggregations?.avg_protocol_violations_per_participant.value ||
        undefined,
      avg_recruitment_period_late_pct:
        aggregations?.avg_recruitment_period_late_pct.value || undefined,
      times_used_as_trial: aggregations?.times_used_as_trial.value || undefined,
      avg_patient_recruitment_performance_pct:
        aggregations?.avg_patient_recruitment_performance_pct.value ||
        undefined,
      avg_recruitment_period_early_pct:
        aggregations?.avg_recruitment_period_early_pct.value || undefined,
      avg_patient_recruitment_period:
        aggregations?.avg_patient_recruitment_period.value || undefined,
      phase: this.extractAggregationFromBucket(aggregations?.phases),
      therapeutic_area: this.extractAggregationFromBucket(
        aggregations?.therapeutic_area
      ),
      indication: this.extractAggregationFromBucket(aggregations?.indications),
      investigators: this.extractAggregationFromBucket(
        aggregations?.investigators
      )
    };
  }
  private mapInvestigatorResults(
    aggregations: CTMSPersonAggregation | undefined
  ): CTMSInvestigatorAggregationResponse {
    return {
      avg_recruitment_period_on_time_pct:
        aggregations?.avg_recruitment_period_on_time_pct.value || undefined,
      avg_patient_retention_pct:
        aggregations?.avg_patient_retention_pct.value || undefined,
      avg_protocol_violations_per_participant:
        aggregations?.avg_protocol_violations_per_participant.value ||
        undefined,
      avg_recruitment_period_late_pct:
        aggregations?.avg_recruitment_period_late_pct.value || undefined,
      times_used_as_trial: aggregations?.times_used_as_trial.value || undefined,
      avg_patient_recruitment_performance_pct:
        aggregations?.avg_patient_recruitment_performance_pct.value ||
        undefined,
      avg_recruitment_period_early_pct:
        aggregations?.avg_recruitment_period_early_pct.value || undefined,
      avg_patient_recruitment_period:
        aggregations?.avg_patient_recruitment_period.value || undefined,
      phase: this.extractAggregationFromBucket(aggregations?.phases),
      therapeutic_area: this.extractAggregationFromBucket(
        aggregations?.therapeutic_area
      ),
      indication: this.extractAggregationFromBucket(aggregations?.indications)
    };
  }
  private extractAggregationFromBucket(
    agg: AggregationsTermsAggregateBase<DocCountBucket> | undefined
  ): AggregationStats[] {
    if (!agg || !agg.buckets) {
      return [];
    }
    return (agg.buckets as DocCountBucket[]).map((bucket) => {
      return {
        value: bucket.key,
        count: bucket.doc_count
      };
    });
  }
  /**
   * Builds elastic search request to obtain facility level aggregations.
   * @param input user input must contain the institution id and may contain other optional filters.
   * @returns elastic search request
   */
  private buildRequestQuery(input: CTMSFacilityAggregationInput) {
    let query: QueryDslQueryContainer;
    const musts: QueryDslQueryContainer[] = [
      {
        term: {
          "h1_organization_id.keyword": input.institutionId
        }
      }
    ];
    if (input.filters) {
      const ctmsInstitutionFilters = this.buildFilters(input.filters);
      query = {
        bool: {
          filter: ctmsInstitutionFilters
        }
      };
    } else {
      // if there are no filters or search query then match all
      query = {
        match_all: {}
      };
    }
    musts.push(query);
    const aggregations = this.buildAggregationQuery();
    const request = {
      query: {
        bool: {
          must: musts
        }
      },
      aggregations
    };

    return request;
  }

  private buildFilters(filters: CTMSFilters): QueryDslQueryContainer[] {
    if (!filters) {
      return [];
    }
    const esFilters: QueryDslQueryContainer[] = [];

    if (filters.indication?.length) {
      esFilters.push({
        terms: {
          "indication.keyword": filters.indication
        }
      });
    }

    if (filters.phase?.length) {
      esFilters.push({
        terms: {
          "phase.keyword": filters.phase
        }
      });
    }

    if (filters.therapeutic_area?.length) {
      esFilters.push({
        terms: {
          "therapeutic_area.keyword": filters.therapeutic_area
        }
      });
    }

    if (filters.dateRange) {
      const startDate = {
        gte: filters.dateRange!.min,
        format: "epoch_second"
      };
      esFilters.push({
        range: {
          effective_date: startDate
        }
      });

      if (filters.dateRange!.max) {
        const endDate = {
          lte: filters.dateRange!.max,
          format: "epoch_second"
        };
        esFilters.push({
          range: {
            effective_date: endDate
          }
        });
      }
    }
    return esFilters;
  }

  private buildInvestigatorRequestQuery(
    input: CTMSInvestigatorAggregationInput
  ) {
    const musts: QueryDslQueryContainer[] = [
      {
        term: {
          "h1_person_id.keyword": input.personId
        }
      }
    ];

    let query: QueryDslQueryContainer;

    if (input.filters) {
      const ctmsInvestigatorFilters = this.buildFilters(input.filters);
      query = {
        bool: {
          filter: ctmsInvestigatorFilters
        }
      };
    } else {
      // if there are no filters or search query then match all
      query = {
        match_all: {}
      };
    }
    musts.push(query);
    const aggregations = this.buildInvestigatorAggregationQuery();
    const request = {
      query: {
        bool: {
          must: musts
        }
      },
      aggregations
    };

    return request;
  }

  private buildAggregationQuery() {
    return {
      avg_recruitment_period_on_time_pct: {
        avg: {
          field: "avg_recruitment_period_on_time_pct"
        }
      },
      avg_patient_retention_pct: {
        avg: {
          field: "avg_patient_retention_pct"
        }
      },
      avg_protocol_violations_per_participant: {
        avg: {
          field: "avg_protocol_violations_per_participant"
        }
      },
      avg_recruitment_period_late_pct: {
        avg: {
          field: "avg_recruitment_period_late_pct"
        }
      },
      times_used_as_trial: {
        avg: {
          field: "times_used_as_trial"
        }
      },
      avg_patient_recruitment_performance_pct: {
        avg: {
          field: "avg_patient_recruitment_performance_pct"
        }
      },
      avg_recruitment_period_early_pct: {
        avg: {
          field: "avg_recruitment_period_early_pct"
        }
      },
      avg_patient_recruitment_period: {
        avg: {
          field: "avg_patient_recruitment_period"
        }
      },
      activation: {
        avg: {
          field: "activation"
        }
      },
      phases: {
        terms: {
          field: "phase.keyword"
        }
      },
      indications: {
        terms: {
          field: "indication.keyword"
        }
      },
      therapeutic_area: {
        terms: {
          field: "therapeutic_area.keyword"
        }
      },
      investigators: {
        terms: {
          field: "investigator.h1_person_id.keyword"
        }
      }
    };
  }
  private buildInvestigatorAggregationQuery() {
    return {
      avg_recruitment_period_on_time_pct: {
        avg: {
          field: "avg_recruitment_period_on_time_pct"
        }
      },
      avg_patient_retention_pct: {
        avg: {
          field: "avg_patient_retention_pct"
        }
      },
      avg_protocol_violations_per_participant: {
        avg: {
          field: "avg_protocol_violations_per_participant"
        }
      },
      avg_recruitment_period_late_pct: {
        avg: {
          field: "avg_recruitment_period_late_pct"
        }
      },
      times_used_as_trial: {
        avg: {
          field: "times_used_as_trial"
        }
      },
      avg_patient_recruitment_performance_pct: {
        avg: {
          field: "avg_patient_recruitment_performance_pct"
        }
      },
      avg_recruitment_period_early_pct: {
        avg: {
          field: "avg_recruitment_period_early_pct"
        }
      },
      avg_patient_recruitment_period: {
        avg: {
          field: "avg_patient_recruitment_period"
        }
      },
      activation: {
        avg: {
          field: "activation"
        }
      },
      phases: {
        terms: {
          field: "phase.keyword"
        }
      },
      indications: {
        terms: {
          field: "indication.keyword"
        }
      },
      therapeutic_area: {
        terms: {
          field: "therapeutic_area.keyword"
        }
      }
    };
  }
}
