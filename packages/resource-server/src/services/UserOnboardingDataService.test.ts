import { faker } from "@faker-js/faker";
import {
  GetParsedUserOnboardingDataResponse,
  OnboardingSubmission,
  OnboardingSubmissionResourceClient,
  ParsedUserOnboardingData
} from "@h1nyc/account-sdk";
import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import {
  LocationLabelFormatterService,
  UNITED_STATES
} from "./LocationLabelFormatterService";
import {
  featureFlagDefaults,
  OnboardingData,
  service,
  UserOnboardingDataService,
  UserOnboardingDataServiceFeatureFlags
} from "./UserOnboardingDataService";
jest.mock("ioredis", () => jest.requireActual("ioredis-mock"));
jest.mock("object-hash");
import Redis from "ioredis-mock";
import objectHash from "object-hash";
import { FeatureFlagsService, LDUserInput } from "@h1nyc/systems-feature-flags";
import { LDFlagsState } from "launchdarkly-node-server-sdk";

let redis = new Redis();

beforeEach(() => {
  jest.resetAllMocks();
});

afterEach((done) => {
  if (redis) {
    redis.flushall().then(() => done());
  }
});

function mockFeatureFlagsService(
  overrides: Partial<UserOnboardingDataServiceFeatureFlags> = {}
) {
  const values = Object.entries(featureFlagDefaults).reduce(
    (acc, [key, value]) => {
      if (overrides[key as keyof UserOnboardingDataServiceFeatureFlags]) {
        acc[value.key] =
          overrides[key as keyof UserOnboardingDataServiceFeatureFlags]!;
      } else {
        acc[value.key] = value.default;
      }
      return acc;
    },
    {} as Record<string, boolean>
  );

  const featureFlagsService = createMockInstance(
    FeatureFlagsService as any
  ) as jest.Mocked<FeatureFlagsService>;

  featureFlagsService.getFlag.mockImplementation(
    (flagName: string): Promise<boolean> => {
      return Promise.resolve(values[flagName]);
    }
  );

  featureFlagsService.getAllFlags.mockImplementation(
    (_user?: LDUserInput): Promise<LDFlagsState> => {
      return Promise.resolve({
        valid: true,
        getFlagValue: jest.fn().mockImplementation((flagName: string) => {
          return values[flagName];
        }),
        getFlagReason: jest.fn(),
        allValues: jest.fn(),
        toJSON: jest.fn()
      });
    }
  );

  return featureFlagsService;
}

function getMockOnboardingdata(): OnboardingData {
  return {
    indications: [faker.datatype.string()],
    countries: [faker.address.country()],
    states: [faker.address.state()]
  };
}

function getMockedRawOnboardingSubmission(): OnboardingSubmission {
  return {
    id: faker.datatype.uuid(),
    projectId: faker.datatype.string(),
    userId: faker.datatype.string(),
    schemaId: null,
    data: {},
    completed: faker.datatype.boolean(),
    skipped: faker.datatype.boolean(),
    createdAt: faker.datatype.datetime(),
    updatedAt: faker.datatype.datetime(),
    firstReminderEmailSent: faker.datatype.datetime()
  };
}

function getMockedParsedUserOnboardingSubmision(
  projectId: string,
  onboardingData: OnboardingData
): ParsedUserOnboardingData {
  return {
    projectId,
    completed: faker.datatype.boolean(),
    raw: getMockedRawOnboardingSubmission(),
    parsed: {
      keywords: [
        {
          id: faker.datatype.uuid(),
          indicationName: onboardingData.indications[0]
        }
      ],
      countries: onboardingData.countries,
      states: onboardingData.states
    }
  };
}

function getMockedParsedUserOnboardingResponse(
  userId: string,
  projectId: string,
  onboardingData: OnboardingData
): GetParsedUserOnboardingDataResponse {
  return {
    userId,
    submissions: [
      getMockedParsedUserOnboardingSubmision(projectId, onboardingData)
    ]
  };
}

describe("UserOnboardingDataService", () => {
  describe("get user onboarding data", () => {
    it("should get onboarding data from account service and return onboardingData response converting all indications to lower case", async () => {
      const onboardingSubmissionResourceClient = createMockInstance(
        OnboardingSubmissionResourceClient
      );
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const configService = createMockInstance(ConfigService);

      const mockOnboardingData = getMockOnboardingdata();
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();
      onboardingSubmissionResourceClient.getParsedUserOnboardingData.mockResolvedValue(
        getMockedParsedUserOnboardingResponse(
          userId,
          projectId,
          mockOnboardingData
        )
      );
      const featureFlagsService = mockFeatureFlagsService();

      const hashedValue = faker.datatype.string();
      objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);

      redis = new Redis({
        data: {}
      });

      const userOnboardingDataService = new UserOnboardingDataService(
        configService,
        onboardingSubmissionResourceClient,
        locationLabelFormatterService,
        featureFlagsService
      );

      const onboardingData = await userOnboardingDataService.getOnboardingData(
        userId,
        projectId
      );

      expect(onboardingData.indications[0]).toEqual(
        mockOnboardingData.indications[0].toLowerCase()
      );

      expect(
        onboardingSubmissionResourceClient.getParsedUserOnboardingData
      ).toHaveBeenCalledWith({ userId, projectId });
      expect(locationLabelFormatterService.getCountryCode).toHaveBeenCalledWith(
        mockOnboardingData.countries[0]
      );
      expect(
        locationLabelFormatterService.getRegionCodeAndCountryCode
      ).toHaveBeenCalledWith(UNITED_STATES, mockOnboardingData.states[0]);
    });

    it("should get onboarding data from cache in case of cache hit when enableCachingForUserOnboardingDataService is ON", async () => {
      const onboardingSubmissionResourceClient = createMockInstance(
        OnboardingSubmissionResourceClient
      );
      const locationLabelFormatterService = createMockInstance(
        LocationLabelFormatterService
      );
      const configService = createMockInstance(ConfigService);

      const mockOnboardingData = getMockOnboardingdata();
      const userId = faker.datatype.string();
      const projectId = faker.datatype.string();

      const featureFlagsService = mockFeatureFlagsService({
        enableCachingForUserOnboardingDataService: true
      });

      const hashedValue = faker.datatype.string();
      objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);

      redis = new Redis({
        data: {
          [hashedValue]: JSON.stringify(mockOnboardingData)
        }
      });

      const userOnboardingDataService = new UserOnboardingDataService(
        configService,
        onboardingSubmissionResourceClient,
        locationLabelFormatterService,
        featureFlagsService
      );

      await expect(
        userOnboardingDataService.getOnboardingData(userId, projectId)
      ).resolves.toEqual(mockOnboardingData);

      expect(objectHash.sha1).toHaveBeenCalledWith({
        userId,
        projectId,
        service
      });

      expect(
        onboardingSubmissionResourceClient.getParsedUserOnboardingData
      ).not.toHaveBeenCalled();
      expect(
        locationLabelFormatterService.getCountryCode
      ).not.toHaveBeenCalled();
      expect(
        locationLabelFormatterService.getRegionCodeAndCountryCode
      ).not.toHaveBeenCalled();
    });
  });
});
