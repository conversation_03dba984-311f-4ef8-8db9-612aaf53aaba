import { createServer, Server } from "nice-grpc";
import { Service } from "typedi";
import { createLogger } from "../lib/Logger";
import {
  DrugSearchServiceDefinition,
  IndicationsSearchServiceDefinition,
  PatientStatsServiceDefinition
} from "@h1nyc/search-sdk";
import { ConfigService } from "./ConfigService";
import { IndicationsSearchGrpcHandlerService } from "./IndicationsSearchGrpcHandlerService";
import { DrugSearchGrpcHandlerService } from "./DrugSearchGrpcHandlerService";
import { PatientStatsGrpcHandlerService } from "./PatientStatsGrpcHandlerService";

@Service()
export class SearchGrpcServerService {
  private server: Server;
  private port: string;
  private readonly logger = createLogger(this);

  constructor(
    configService: ConfigService,
    private indicationsSearchGrpcHandlerService: IndicationsSearchGrpcHandlerService,
    private drugSearchGrpcHandlerService: DrugSearchGrpcHandlerService,
    private patientStatsGrpcHandlerService: PatientStatsGrpcHandlerService
  ) {
    this.port = configService.searchGrpcServerPort;
    this.server = createServer();
    this.registerHandlers();
  }

  private registerHandlers() {
    this.server.add(
      IndicationsSearchServiceDefinition,
      this.indicationsSearchGrpcHandlerService
    );

    this.server.add(
      DrugSearchServiceDefinition,
      this.drugSearchGrpcHandlerService
    );

    this.server.add(
      PatientStatsServiceDefinition,
      this.patientStatsGrpcHandlerService
    );

    this.logger.info("Registered search gRPC handlers successfully");
  }

  startServer() {
    return this.server.listen(`0.0.0.0:${this.port}`);
  }

  stopServer() {
    return this.server.shutdown();
  }
}
