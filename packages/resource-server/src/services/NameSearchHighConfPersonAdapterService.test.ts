import {
  SearchHit,
  SearchHitsMetadata,
  SearchInnerHitsResult,
  SearchTotalHits
} from "@elastic/elasticsearch/lib/api/types";
import { Congress, DocumentType, Publication, Trial } from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";
import _ from "lodash";
import { HCPDocument } from "./KeywordSearchResourceServiceRewrite";
import { NameSearchHighConfPersonAdapterService } from "./NameSearchHighConfPersonAdapterService";

const ENGLISH = "eng";
const CHINESE = "cmn";
const JAPANESE = "jpn";
const topResultScore = 150;

function getMockSource() {
  const source: HCPDocument = {
    id: faker.datatype.uuid(),
    h1dn_id: faker.datatype.uuid(),
    name_eng: `${faker.name.firstName()} ${faker.name.lastName()}`,
    name_cmn: `${faker.name.firstName()} ${faker.name.lastName()}`,
    name_jpn: `${faker.name.firstName()} ${faker.name.lastName()}`,
    firstName_eng: faker.name.firstName(),
    firstName_cmn: faker.name.firstName(),
    firstName_jpn: faker.name.firstName(),
    middleName_eng: faker.name.middleName(),
    middleName_cmn: faker.name.middleName(),
    middleName_jpn: faker.name.middleName(),
    lastName_eng: faker.name.lastName(),
    lastName_cmn: faker.name.lastName(),
    lastName_jpn: faker.name.lastName(),
    designations: [],
    emails: [faker.datatype.string(), faker.datatype.string()],
    specialty_eng: [],
    specialty_cmn: [],
    specialty_jpn: [],
    citationTotal: faker.datatype.number(),
    congressCount: faker.datatype.number(),
    paymentTotal: faker.datatype.number(),
    referralsSentCount: faker.datatype.number(),
    referralsReceivedCount: faker.datatype.number(),
    microBloggingTotal: faker.datatype.number(),
    trialCount: faker.datatype.number(),
    trialEnrollmentRate: faker.datatype.number(),
    presentWorkInstitutionCount: faker.datatype.number(),
    publicationCount: faker.datatype.number(),
    totalWorks: faker.datatype.number(),
    DRG_diagnosesCount: faker.datatype.number(),
    DRG_proceduresCount: faker.datatype.number(),
    patientsDiversityCount: faker.datatype.number(),
    patientsDiversity: [],
    patientsDiversityPercentile: faker.datatype.number(),
    patientsDiversityRatio: {
      asian: faker.datatype.number(),
      pacificIslander: faker.datatype.number(),
      asianPacificIslander: faker.datatype.number(),
      blackNonHispanic: faker.datatype.number(),
      hispanic: faker.datatype.number(),
      whiteNonHispanic: faker.datatype.number(),
      americanIndianOrAlaskaNative: faker.datatype.number(),
      mixedBrazil: faker.datatype.number(),
      indigenousBrazil: faker.datatype.number(),
      notIdentified: faker.datatype.number()
    },
    providerDiversity: [],
    totalPatientCount: faker.datatype.number(),
    projectIds: []
  };

  return source;
}

function generateMockHit(
  _score: number,
  overrides: Partial<HCPDocument> = {}
): SearchHit<HCPDocument> {
  const source: HCPDocument = getMockSource();

  const innerHits: Record<string, SearchInnerHitsResult> = {
    publications: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "publications.id": [faker.datatype.uuid()],
              "publications.abstract": [faker.datatype.string()],
              "publications.citationCount": [faker.datatype.number],
              "publications.PMID": [faker.datatype.uuid()],
              "publications.journalName_cmn": [faker.datatype.string()],
              "publications.journalName_eng": [faker.datatype.string()],
              "publications.journalName_jpn": [faker.datatype.string()],
              "publications.title_cmn.keyword": [faker.datatype.string()],
              "publications.title_eng.keyword": [faker.datatype.string()],
              "publications.title_jpn.keyword": [faker.datatype.string()],
              "publications.datePublished": [1652255820000],
              "publications.languageCode": [ENGLISH]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "publications.id": [faker.datatype.uuid()],
              "publications.abstract": [faker.datatype.string()],
              "publications.citationCount": [faker.datatype.number],
              "publications.PMID": [faker.datatype.uuid()],
              "publications.journalName_cmn": [faker.datatype.string()],
              "publications.journalName_eng": [faker.datatype.string()],
              "publications.journalName_jpn": [faker.datatype.string()],
              "publications.title_cmn.keyword": [faker.datatype.string()],
              "publications.title_eng.keyword": [faker.datatype.string()],
              "publications.title_jpn.keyword": [faker.datatype.string()],
              "publications.datePublished": [1644566220000],
              "publications.languageCode": [JAPANESE]
            }
          }
        ]
      }
    },

    trials: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: [
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "trials.id": [faker.datatype.uuid()],
              "trials.status_cmn": [faker.datatype.string()],
              "trials.status_eng": [faker.datatype.string()],
              "trials.status_jpn": [faker.datatype.string()],
              "trials.phase_eng": [faker.datatype.string()],
              "trials.phase_cmn": [faker.datatype.string()],
              "trials.phase_jpn": [faker.datatype.string()],
              "trials.briefTitle_eng.keyword": [faker.datatype.string()],
              "trials.briefTitle_cmn.keyword": [faker.datatype.string()],
              "trials.briefTitle_jpn.keyword": [faker.datatype.string()],
              "trials.officialTitle_eng.keyword": [faker.datatype.string()],
              "trials.officialTitle_cmn.keyword": [faker.datatype.string()],
              "trials.officialTitle_jpn.keyword": [faker.datatype.string()],
              "trials.summary": [faker.datatype.string()],
              "trials.eligibilityCriteria": [faker.datatype.string()],
              "trials.startDate": [1646985420000],
              "trials.languageCode": [ENGLISH]
            }
          },
          {
            _id: faker.datatype.string(),
            _index: faker.datatype.string(),
            fields: {
              "trials.id": [faker.datatype.uuid()],
              "trials.status_cmn": [faker.datatype.string()],
              "trials.status_eng": [faker.datatype.string()],
              "trials.status_jpn": [faker.datatype.string()],
              "trials.phase_eng": [faker.datatype.string()],
              "trials.phase_cmn": [faker.datatype.string()],
              "trials.phase_jpn": [faker.datatype.string()],
              "trials.briefTitle_eng.keyword": [faker.datatype.string()],
              "trials.briefTitle_cmn.keyword": [faker.datatype.string()],
              "trials.briefTitle_jpn.keyword": [faker.datatype.string()],
              "trials.officialTitle_eng.keyword": [faker.datatype.string()],
              "trials.officialTitle_cmn.keyword": [faker.datatype.string()],
              "trials.officialTitle_jpn.keyword": [faker.datatype.string()],
              "trials.summary": [faker.datatype.string()],
              "trials.eligibilityCriteria": [faker.datatype.string()],
              "trials.startDate": [1615449420000],
              "trials.languageCode": [CHINESE]
            }
          }
        ]
      }
    },
    congress: {
      hits: {
        total: {
          value: faker.datatype.number(),
          relation: "eq"
        },
        hits: []
      }
    }
  };

  return {
    _id: faker.datatype.string(),
    _index: faker.datatype.string(),
    _score: _score,
    _source: { ...source, ...overrides },
    inner_hits: innerHits
  };
}

function generateMockHitWithoutInnerHits(
  _score: number,
  overrides: Partial<HCPDocument> = {}
): SearchHit<HCPDocument> {
  const source: HCPDocument = getMockSource();

  const innerHits: Record<string, SearchInnerHitsResult> = {
    publications: {
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        hits: []
      }
    },

    trials: {
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        hits: []
      }
    },
    congress: {
      hits: {
        total: {
          value: 0,
          relation: "eq"
        },
        hits: []
      }
    }
  };

  return {
    _id: faker.datatype.string(),
    _index: faker.datatype.string(),
    _score: _score,
    _source: { ...source, ...overrides },
    inner_hits: innerHits
  };
}

function getTrialObject(hit: SearchHit<HCPDocument>, index: number): Trial {
  return {
    id: hit.inner_hits!.trials.hits.hits[index].fields!["trials.id"][0],
    phase: "",
    phase_eng:
      hit.inner_hits!.trials.hits.hits[index].fields!["trials.phase_eng"][0],
    phase_cmn:
      hit.inner_hits!.trials.hits.hits[index].fields!["trials.phase_cmn"][0],
    phase_jpn:
      hit.inner_hits!.trials.hits.hits[index].fields!["trials.phase_jpn"][0],
    startDate:
      hit.inner_hits!.trials.hits.hits[index].fields!["trials.startDate"][0],
    briefTitle: "",
    briefTitle_eng:
      hit.inner_hits!.trials.hits.hits[index].fields![
        "trials.briefTitle_eng.keyword"
      ][0],
    briefTitle_cmn:
      hit.inner_hits!.trials.hits.hits[index].fields![
        "trials.briefTitle_cmn.keyword"
      ][0],
    briefTitle_jpn:
      hit.inner_hits!.trials.hits.hits[index].fields![
        "trials.briefTitle_jpn.keyword"
      ][0],
    officialTitle: "",
    officialTitle_eng:
      hit.inner_hits!.trials.hits.hits[index].fields![
        "trials.officialTitle_eng.keyword"
      ][0],
    officialTitle_cmn:
      hit.inner_hits!.trials.hits.hits[index].fields![
        "trials.officialTitle_cmn.keyword"
      ][0],
    officialTitle_jpn:
      hit.inner_hits!.trials.hits.hits[index].fields![
        "trials.officialTitle_jpn.keyword"
      ][0],
    status: "",
    status_eng:
      hit.inner_hits!.trials.hits.hits[index].fields!["trials.status_eng"][0],
    status_cmn:
      hit.inner_hits!.trials.hits.hits[index].fields!["trials.status_cmn"][0],
    status_jpn:
      hit.inner_hits!.trials.hits.hits[index].fields!["trials.status_jpn"][0],
    primaryCompletionDate: 0,
    languageCode:
      hit.inner_hits!.trials.hits.hits[index].fields!["trials.languageCode"][0],
    interventions: [],
    completionDate: 0,
    persons: [],
    keywords: [],
    conditions: [],
    summary:
      hit.inner_hits!.trials.hits.hits[index].fields!["trials.summary"][0],
    eligibilityCriteria:
      hit.inner_hits!.trials.hits.hits[index].fields![
        "trials.eligibilityCriteria"
      ][0],
    documentType: DocumentType.CLINICALTRIAL
  };
}

function getPublicationObject(
  hit: SearchHit<HCPDocument>,
  index: number
): Publication {
  return {
    id: hit.inner_hits!.publications.hits.hits[index].fields![
      "publications.id"
    ][0],
    title: "",
    title_eng:
      hit.inner_hits!.publications.hits.hits[index].fields![
        "publications.title_eng.keyword"
      ][0],
    title_cmn:
      hit.inner_hits!.publications.hits.hits[index].fields![
        "publications.title_cmn.keyword"
      ][0],
    title_jpn:
      hit.inner_hits!.publications.hits.hits[index].fields![
        "publications.title_jpn.keyword"
      ][0],
    datePublished:
      hit.inner_hits!.publications.hits.hits[index].fields![
        "publications.datePublished"
      ][0],
    journalName: "",
    journalName_eng:
      hit.inner_hits!.publications.hits.hits[index].fields![
        "publications.journalName_eng"
      ][0],
    journalName_cmn:
      hit.inner_hits!.publications.hits.hits[index].fields![
        "publications.journalName_cmn"
      ][0],
    journalName_jpn:
      hit.inner_hits!.publications.hits.hits[index].fields![
        "publications.journalName_jpn"
      ][0],
    PMID: hit.inner_hits!.publications.hits.hits[index].fields![
      "publications.PMID"
    ][0],
    languageCode:
      hit.inner_hits!.publications.hits.hits[index].fields![
        "publications.languageCode"
      ][0],
    abstract:
      hit.inner_hits!.publications.hits.hits[index].fields![
        "publications.abstract"
      ][0],
    substances: [],
    citationCount:
      hit.inner_hits!.publications.hits.hits[index].fields![
        "publications.citationCount"
      ][0],
    keywords: [],
    persons: [],
    type: [],
    documentType: DocumentType.PUBLICATION
  };
}

function getPublicationActivity(
  hit: SearchHit<HCPDocument>,
  index: number
): any {
  const languageCode =
    hit.inner_hits!.publications.hits.hits[index].fields![
      "publications.languageCode"
    ][0];

  return {
    document: {
      ...getPublicationObject(hit, index),
      title:
        hit.inner_hits!.publications.hits.hits[index].fields![
          `publications.title_${languageCode}.keyword`
        ][0],
      journalName:
        hit.inner_hits!.publications.hits.hits[index].fields![
          `publications.journalName_${languageCode}`
        ][0],
      documentType: DocumentType.PUBLICATION
    }
  };
}

function getTrialActivity(hit: SearchHit<HCPDocument>, index: number): any {
  const languageCode =
    hit.inner_hits!.trials.hits.hits[index].fields!["trials.languageCode"][0];

  return {
    document: {
      ...getTrialObject(hit, index),
      officialTitle:
        hit.inner_hits!.trials.hits.hits[index].fields![
          `trials.officialTitle_${languageCode}.keyword`
        ][0],
      briefTitle:
        hit.inner_hits!.trials.hits.hits[index].fields![
          `trials.briefTitle_${languageCode}.keyword`
        ][0],
      status:
        hit.inner_hits!.trials.hits.hits[index].fields![
          `trials.status_${languageCode}`
        ][0],
      documentType: DocumentType.CLINICALTRIAL
    }
  };
}

describe("NameSearchHighConfPersonHelper", () => {
  it("checks that high conf person should be present", () => {
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();
    const hit1 = generateMockHit(topResultScore);
    const hit2 = generateMockHit(30);

    const hits: SearchHitsMetadata<HCPDocument> = {
      total: {
        value: faker.datatype.number()
      } as SearchTotalHits,
      hits: [hit1, hit2]
    };

    const highConfHcp =
      nameSearchHighConfidencePersonAdapterService.getHighConfidenceHcpInfo(
        hits
      );
    expect(highConfHcp).toEqual({
      highConfHcpPresent: true,
      score: topResultScore
    });
  });

  it("checks that high conf person should be present when there is only one match", () => {
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();
    const hit1 = generateMockHit(topResultScore);

    const hits: SearchHitsMetadata<HCPDocument> = {
      total: {
        value: faker.datatype.number()
      } as SearchTotalHits,
      hits: [hit1]
    };

    const highConfHcp =
      nameSearchHighConfidencePersonAdapterService.getHighConfidenceHcpInfo(
        hits
      );
    expect(highConfHcp).toEqual({
      highConfHcpPresent: true,
      score: topResultScore
    });
  });

  it("checks that high conf person should not be present", () => {
    const topResultScore = 40;
    const hit1 = generateMockHit(topResultScore);
    const hit2 = generateMockHit(30);
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();
    const hits: SearchHitsMetadata<HCPDocument> = {
      total: {
        value: faker.datatype.number()
      } as SearchTotalHits,
      hits: [hit1, hit2]
    };

    const highConfHcp =
      nameSearchHighConfidencePersonAdapterService.getHighConfidenceHcpInfo(
        hits
      );
    expect(highConfHcp).toEqual({
      highConfHcpPresent: false,
      score: topResultScore
    });
  });

  it("extracts and checks trial data from inner hits", () => {
    const topResultScore = 150;
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();
    const hit = generateMockHit(topResultScore);
    const trials1 = getTrialObject(hit, 0);
    const trials2 = getTrialObject(hit, 1);
    const trials = nameSearchHighConfidencePersonAdapterService.getTrialsData(
      hit.inner_hits!.trials
    );
    expect(trials).toEqual([trials1, trials2]);
  });

  it("extracts and checks publication data from inner hits", () => {
    const hit = generateMockHit(topResultScore);
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();
    const publication1 = getPublicationObject(hit, 0);
    const publication2 = getPublicationObject(hit, 1);
    const publications =
      nameSearchHighConfidencePersonAdapterService.getPublicationsData(
        hit.inner_hits!.publications
      );
    expect(publications).toEqual([publication1, publication2]);
  });

  it("extracts and checks congress data from inner hits", () => {
    const topResultScore = 150;
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();
    const hit = generateMockHit(topResultScore);
    const congress =
      nameSearchHighConfidencePersonAdapterService.getCongressData(
        hit.inner_hits!.congress
      );
    expect(congress).toEqual([]);
  });

  it("extracts and checks publication activites", () => {
    const hit = generateMockHit(topResultScore);
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();
    const publication1 = getPublicationObject(hit, 0);
    const publication2 = getPublicationObject(hit, 1);
    const publications = [publication1, publication2];
    const pubActivities =
      nameSearchHighConfidencePersonAdapterService.getPublicationActivities(
        publications
      );

    expect(pubActivities).toEqual([
      {
        document: {
          ...publication1,
          title: publication1.title_eng,
          journalName: publication1.journalName_eng,
          documentType: DocumentType.PUBLICATION
        }
      },
      {
        document: {
          ...publication2,
          title: publication2.title_jpn,
          journalName: publication2.journalName_jpn,
          documentType: DocumentType.PUBLICATION
        }
      }
    ]);
  });

  it("extracts and checks trial activites", () => {
    const hit = generateMockHit(topResultScore);
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();
    const trials1 = getTrialObject(hit, 0);
    const trials2 = getTrialObject(hit, 1);
    const trials = [trials1, trials2];
    const trialsActivities =
      nameSearchHighConfidencePersonAdapterService.getTrialActivities(trials);

    expect(trialsActivities).toEqual([
      {
        document: {
          ...trials1,
          officialTitle: trials1.officialTitle_eng,
          briefTitle: trials1.briefTitle_eng,
          status: trials1.status_eng,
          documentType: DocumentType.CLINICALTRIAL
        }
      },
      {
        document: {
          ...trials2,
          officialTitle: trials2.officialTitle_cmn,
          briefTitle: trials2.briefTitle_cmn,
          status: trials2.status_cmn,
          documentType: DocumentType.CLINICALTRIAL
        }
      }
    ]);
  });

  it("extracts and checks congress activites", () => {
    const congress: Congress[] = [];
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();
    const trialsActivities =
      nameSearchHighConfidencePersonAdapterService.getCongressActivities(
        congress
      );
    expect(trialsActivities).toEqual([]);
  });

  it("sort recent activites", () => {
    const hit = generateMockHit(topResultScore);
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();
    const activity1 = getPublicationActivity(hit, 0);
    const activity2 = getPublicationActivity(hit, 1);
    const activity3 = getTrialActivity(hit, 0);
    const activity4 = getTrialActivity(hit, 1);

    const recentActivities = [activity1, activity2, activity3, activity4];

    const sortedActivities = _.orderBy(
      recentActivities,
      (e) => {
        const date =
          nameSearchHighConfidencePersonAdapterService.customActivitySort(e);
        return +date;
      },
      "desc"
    );

    expect(sortedActivities).toEqual([
      activity1,
      activity3,
      activity2,
      activity4
    ]);
  });

  it("extracts high conf person fields", () => {
    const hit1 = generateMockHit(topResultScore);
    const hit2 = generateMockHit(30);
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();

    const hits: SearchHitsMetadata<HCPDocument> = {
      total: {
        value: faker.datatype.number()
      } as SearchTotalHits,
      hits: [hit1, hit2]
    };

    const highConfHcp =
      nameSearchHighConfidencePersonAdapterService.getHighConfidenceHcpInfo(
        hits
      );
    const activity1 = getPublicationActivity(hit1, 0);
    const activity2 = getPublicationActivity(hit1, 1);
    const activity3 = getTrialActivity(hit1, 0);
    const activity4 = getTrialActivity(hit1, 1);

    const sortedRecentActivites = [activity1, activity3, activity2, activity4];

    const highConfFieldsResult1 =
      nameSearchHighConfidencePersonAdapterService.extractHighConfPersonFields(
        highConfHcp,
        hit1
      );
    const highConfFieldsResult2 =
      nameSearchHighConfidencePersonAdapterService.extractHighConfPersonFields(
        highConfHcp,
        hit2
      );

    expect(highConfFieldsResult1).toEqual({
      recentActivities: sortedRecentActivites
    });

    expect(highConfFieldsResult2).toEqual(undefined);
  });

  it("expects undefined for high conf person fields when there are no activities present", () => {
    const hit1 = generateMockHitWithoutInnerHits(topResultScore);
    const hit2 = generateMockHit(30);
    const nameSearchHighConfidencePersonAdapterService =
      new NameSearchHighConfPersonAdapterService();

    const hits: SearchHitsMetadata<HCPDocument> = {
      total: {
        value: faker.datatype.number()
      } as SearchTotalHits,
      hits: [hit1, hit2]
    };

    const highConfHcp =
      nameSearchHighConfidencePersonAdapterService.getHighConfidenceHcpInfo(
        hits
      );

    const highConfFieldsResult =
      nameSearchHighConfidencePersonAdapterService.extractHighConfPersonFields(
        highConfHcp,
        hit1
      );

    expect(highConfFieldsResult).toEqual(undefined);
  });
});
