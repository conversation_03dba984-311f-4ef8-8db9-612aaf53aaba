import { ElasticSearchService } from "./ElasticSearchService";
import { ReferralAutosuggestService } from "./ReferralAutosuggestService";

import { ReferralType } from "@h1nyc/search-sdk";
import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { faker } from "@faker-js/faker";
import {
  AggregationsAggregate,
  SearchResponse
} from "@elastic/elasticsearch/lib/api/types";
import _ from "lodash";

function generateBuckets() {
  return _.range(0, faker.datatype.number({ min: 1, max: 10 })).map((i) => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number()
  }));
}

type SpecialtyAggregate = Record<"specialty", AggregationsAggregate>;
type ReferralsAggregate = Record<
  "referrals",
  Record<"filtered", AggregationsAggregate>
>;

function generateMockElasticsearchResponse(
  aggregations: SpecialtyAggregate | ReferralsAggregate
): SearchResponse<never> {
  return {
    took: faker.datatype.number(),
    timed_out: faker.datatype.boolean(),
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: faker.datatype.number(),
      hits: []
    },
    aggregations
  };
}

describe("isReady", () => {
  it("should call elasticsearchService.ping", async () => {
    const configService = createMockInstance(ConfigService);
    const elasticsearchService = createMockInstance(ElasticSearchService);

    const referralAutosuggestService = new ReferralAutosuggestService(
      configService,
      elasticsearchService
    );

    await expect(referralAutosuggestService.isReady()).resolves.toBeTruthy();

    expect(elasticsearchService.ping).toHaveBeenCalled();
  });
});

describe("specialtyAutosuggest", () => {
  it("should filter on query when not an empty string", async () => {
    const buckets = generateBuckets();

    const response = generateMockElasticsearchResponse({
      specialty: {
        buckets
      }
    });

    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.query.mockResolvedValue(response);

    const referralAutosuggestService = new ReferralAutosuggestService(
      configService,
      elasticsearchService
    );

    const peopleIds = [faker.datatype.uuid(), faker.datatype.uuid()];
    const projectId = faker.datatype.uuid();
    const query = faker.datatype.string();

    await expect(
      referralAutosuggestService.specialtySuggest(peopleIds, projectId, query)
    ).resolves.toEqual(buckets.map(_.property("key")));

    expect(elasticsearchService.query).toHaveBeenCalledWith({
      index: configService.elasticPeopleIndex,
      size: 0,
      query: {
        bool: {
          filter: [
            expect.termQuery("projectIds", projectId),
            expect.termsQuery("id", peopleIds),
            {
              match_phrase: {
                "specialty_eng.autocomplete_search": {
                  query,
                  slop: 5
                }
              }
            }
          ]
        }
      },
      aggs: {
        specialty: {
          terms: {
            field: "specialty_eng",
            size: 100,
            exclude: "_BAD_",
            order: [
              {
                _count: "desc"
              }
            ],
            script: {
              id: "autocomplete",
              params: {
                query,
                id: peopleIds
              }
            }
          }
        }
      }
    });
  });

  it("should not filter on query when an empty string", async () => {
    const buckets = generateBuckets();

    const response = generateMockElasticsearchResponse({
      specialty: {
        buckets
      }
    });

    const configService = createMockInstance(ConfigService);
    configService.elasticPeopleIndex = faker.datatype.string();

    const elasticsearchService = createMockInstance(ElasticSearchService);
    elasticsearchService.query.mockResolvedValue(response);

    const referralAutosuggestService = new ReferralAutosuggestService(
      configService,
      elasticsearchService
    );

    const peopleIds = [faker.datatype.uuid(), faker.datatype.uuid()];
    const projectId = faker.datatype.uuid();
    const query = "";

    await expect(
      referralAutosuggestService.specialtySuggest(peopleIds, projectId, query)
    ).resolves.toEqual(buckets.map(_.property("key")));

    expect(elasticsearchService.query).toHaveBeenCalledWith({
      index: configService.elasticPeopleIndex,
      size: 0,
      query: {
        bool: {
          filter: [
            expect.termQuery("projectIds", projectId),
            expect.termsQuery("id", peopleIds)
          ]
        }
      },
      aggs: {
        specialty: {
          terms: {
            field: "specialty_eng",
            size: 100,
            exclude: "_BAD_",
            order: [
              {
                _count: "desc"
              }
            ]
          }
        }
      }
    });
  });
});

describe("serviceLineSuggest", () => {
  for (const referralType of [ReferralType.Sent, ReferralType.Received]) {
    it(`referrals${referralType}: should filter on query when not an empty string`, async () => {
      const buckets = generateBuckets();

      const response = generateMockElasticsearchResponse({
        referrals: {
          filtered: {
            buckets
          }
        }
      });

      const configService = createMockInstance(ConfigService);
      const elasticsearchService = createMockInstance(ElasticSearchService);
      elasticsearchService.query.mockResolvedValue(response);

      const referralAutosuggestService = new ReferralAutosuggestService(
        configService,
        elasticsearchService
      );

      const personId = faker.datatype.uuid();
      const projectId = faker.datatype.uuid();
      const query = faker.datatype.string();

      await expect(
        referralAutosuggestService.serviceLineSuggest(
          personId,
          projectId,
          query,
          referralType
        )
      ).resolves.toEqual(buckets.map(_.property("key")));

      expect(elasticsearchService.query).toHaveBeenCalledWith({
        index: configService.elasticPeopleIndex,
        size: 0,
        query: {
          bool: {
            filter: [
              expect.termQuery("projectIds", projectId),
              expect.termQuery("id", personId),
              {
                match_phrase: {
                  "referralsServiceLine_eng.autocomplete_search": {
                    query,
                    slop: 5
                  }
                }
              }
            ]
          }
        },
        aggs: {
          referrals: {
            nested: {
              path: `referrals${referralType}`
            },
            aggs: {
              filtered: {
                terms: {
                  field: `referrals${referralType}.serviceDescription_eng`,
                  size: 100,
                  exclude: "_BAD_",
                  order: [
                    {
                      _count: "desc"
                    }
                  ],
                  script: {
                    id: "autocomplete",
                    params: {
                      query,
                      id: personId
                    }
                  }
                }
              }
            }
          }
        }
      });
    });

    it(`referrals${referralType}: should not filter on query when an empty string`, async () => {
      const buckets = generateBuckets();

      const response = generateMockElasticsearchResponse({
        referrals: {
          filtered: {
            buckets
          }
        }
      });

      const configService = createMockInstance(ConfigService);
      const elasticsearchService = createMockInstance(ElasticSearchService);
      elasticsearchService.query.mockResolvedValue(response);

      const referralAutosuggestService = new ReferralAutosuggestService(
        configService,
        elasticsearchService
      );

      const personId = faker.datatype.uuid();
      const projectId = faker.datatype.uuid();
      const query = "";

      await expect(
        referralAutosuggestService.serviceLineSuggest(
          personId,
          projectId,
          query,
          referralType
        )
      ).resolves.toEqual(buckets.map(_.property("key")));

      expect(elasticsearchService.query).toHaveBeenCalledWith({
        index: configService.elasticPeopleIndex,
        size: 0,
        query: {
          bool: {
            filter: [
              expect.termQuery("projectIds", projectId),
              expect.termQuery("id", personId)
            ]
          }
        },
        aggs: {
          referrals: {
            nested: {
              path: `referrals${referralType}`
            },
            aggs: {
              filtered: {
                terms: {
                  field: `referrals${referralType}.serviceDescription_eng`,
                  size: 100,
                  exclude: "_BAD_",
                  order: [
                    {
                      _count: "desc"
                    }
                  ]
                }
              }
            }
          }
        }
      });
    });
  }
});
