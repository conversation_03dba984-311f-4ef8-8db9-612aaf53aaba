import { Service } from "typedi";
import { ConfigService } from "./ConfigService";
import { ElasticCommonSearchService } from "./ElasticCommonSearchService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";

@Service()
export class ElasticSearchSpellCheckService extends ElasticCommonSearchService {
  constructor(config: ConfigService, featureFlagService: FeatureFlagsService) {
    super(
      config.elasticsearchClientOptions,
      config.elasticsearchClientOptionsV2,
      featureFlagService,
      config.elasticSpellCheckIndex
    );
  }
}
