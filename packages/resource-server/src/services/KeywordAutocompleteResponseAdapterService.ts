import {
  KeywordFilterAggregation,
  KeywordFilterAutocompleteFilterField
} from "@h1nyc/search-sdk";
import { AggregationsTermsAggregateBase } from "@elastic/elasticsearch/lib/api/types";
import _, { toLower } from "lodash";
import { Service } from "typedi";

import { LocationLabelFormatterService } from "./LocationLabelFormatterService";
import {
  ASIAN_RACE_CATEGORY,
  PACIFIC_ISLANDER_RACE_CATEGORY
} from "./KeywordSearchResponseAdapterService";
import {
  CcsrIcdMappingRepository,
  CcsrPxMappingRepository
} from "@h1nyc/pipeline-repositories";
import { extractClaimsCode } from "./KeywordFilterClauseBuilderService";
import { KeywordFilterAutocompleteInput } from "@h1nyc/search-sdk";
import { CcsrIcdMapping, CcsrPxMapping } from "@h1nyc/pipeline-entities";

export type DocCountBucket = {
  key: string;
  doc_count: number;
  by_congressId?: {
    buckets: DocCountBucket[];
  };
  matching_desc?: {
    buckets: DocCountBucket[];
  };
  icd_size?: {
    buckets: DocCountBucket[];
  };
  locations_in_region?: {
    buckets: FilteredDocCountBucket[];
  };
  regions_in?: {
    buckets: DocCountBucket[];
  };
};

export type FilteredMatchingBucket = {
  doc_count: number;
  matching_desc?: {
    buckets: DocCountBucket[];
  };
};

export type FilteredDocCountBucket = DocCountBucket & {
  matching: {
    doc_count?: number;
    value?: number;
  };
};

type AggregateBucket = {
  id: string;
  count: number;
  uniqueId?: string;
  codeAndDescription?: string;
  icdSize?: string;
  regionsIn?: string[];
  locationsInRegion?: AggregateBucket[];
};

type MatchingAggregations = Record<
  "matching",
  AggregationsTermsAggregateBase<DocCountBucket>
>;
type NestedMatchingAggregations = Record<
  "nested",
  Record<"matching", AggregationsTermsAggregateBase<DocCountBucket>>
>;
type NestedFilteredMatchingAggregations = Record<
  "nested",
  Record<
    "filtered_matching",
    Record<"matching", AggregationsTermsAggregateBase<DocCountBucket>>
  >
>;
type NestedFilteredMatchingLocationAndRegionAggregations = Record<
  "nested",
  Record<
    "filtered_matching",
    {
      filtered_matching_regions?: Record<
        "matching_regions",
        AggregationsTermsAggregateBase<FilteredDocCountBucket>
      >;
      filtered_matching_locations: Record<
        "matching_locations",
        AggregationsTermsAggregateBase<FilteredDocCountBucket>
      >;
    }
  >
>;
type NestedFilteredMatchingCcsrClaimsAggregations = {
  nested: {
    filtered_matching: {
      buckets: Record<string, FilteredMatchingBucket>;
    };
  };
};

export type SupportedAggregations =
  | MatchingAggregations
  | NestedMatchingAggregations
  | NestedFilteredMatchingAggregations
  | NestedFilteredMatchingLocationAndRegionAggregations
  | NestedFilteredMatchingCcsrClaimsAggregations;

const PACIFIC = ["California", "Oregon", "Washington", "Alaska", "Hawaii"];
const MOUNTAIN = [
  "Arizona",
  "Colorado",
  "Idaho",
  "Montana",
  "Nevada",
  "Utah",
  "Wyoming",
  "New Mexico"
];
const WEST_NORTH_CENTRAL = [
  "Iowa",
  "Kansas",
  "Minnesota",
  "Missouri",
  "Nebraska",
  "North Dakota",
  "South Dakota"
];
const EAST_NORTH_CENTRAL = [
  "Illinois",
  "Indiana",
  "Michigan",
  "Ohio",
  "Wisconsin"
];
const SOUTH_ATLANTIC = [
  "Delaware",
  "Florida",
  "Georgia",
  "Maryland",
  "North Carolina",
  "South Carolina",
  "Virginia",
  "West Virginia",
  "District Of Columbia"
];
const MIDDLE_ATLANTIC = ["New Jersey", "New York", "Pennsylvania"];
const NEW_ENGLAND = [
  "Connecticut",
  "Maine",
  "Massachusetts",
  "New Hampshire",
  "Rhode Island",
  "Vermont"
];
const WEST_SOUTH_CENTRAL = ["Arkansas", "Louisiana", "Oklahoma", "Texas"];
const EAST_SOUTH_CENTRAL = ["Alabama", "Kentucky", "Mississippi", "Tennessee"];
export const REGION_TO_STATES = new Map([
  ["Pacific", PACIFIC],
  ["Mountain", MOUNTAIN],
  ["West North Central", WEST_NORTH_CENTRAL],
  ["West South Central", WEST_SOUTH_CENTRAL],
  ["East North Central", EAST_NORTH_CENTRAL],
  ["East South Central", EAST_SOUTH_CENTRAL],
  ["South Atlantic", SOUTH_ATLANTIC],
  ["Middle Atlantic", MIDDLE_ATLANTIC],
  ["New England", NEW_ENGLAND],
  ["Northeast", [...NEW_ENGLAND, ...MIDDLE_ATLANTIC]],
  ["Midwest", [...WEST_NORTH_CENTRAL, ...EAST_NORTH_CENTRAL]],
  ["South", [...WEST_SOUTH_CENTRAL, ...EAST_SOUTH_CENTRAL, ...SOUTH_ATLANTIC]],
  ["West", [...PACIFIC, ...MOUNTAIN]]
]);
export const VALID_STATE_CODES = new Set([
  "AL",
  "AK",
  "AZ",
  "AR",
  "CA",
  "CO",
  "CT",
  "DC",
  "DE",
  "FL",
  "GA",
  "HI",
  "ID",
  "IL",
  "IN",
  "IA",
  "KS",
  "KY",
  "LA",
  "ME",
  "MD",
  "MA",
  "MI",
  "MN",
  "MS",
  "MO",
  "MT",
  "NE",
  "NV",
  "NH",
  "NJ",
  "NM",
  "NY",
  "NC",
  "ND",
  "OH",
  "OK",
  "OR",
  "PA",
  "RI",
  "SC",
  "SD",
  "TN",
  "TX",
  "UT",
  "VT",
  "VA",
  "WA",
  "WV",
  "WI",
  "WY"
]);

@Service()
export class KeywordAutocompleteResponseAdapterService {
  constructor(
    private locationLabelFormatterService: LocationLabelFormatterService,
    private ccsrIcdMappingRepository: CcsrIcdMappingRepository,
    private ccsrPxMappingRepository: CcsrPxMappingRepository
  ) {}

  async adapt(
    aggregations: Readonly<SupportedAggregations>,
    filterField: KeywordFilterAutocompleteFilterField,
    ccsrClaimCodesToInclude: string[],
    query?: string,
    input?: KeywordFilterAutocompleteInput,
    usersPreferredLanguage?: string
  ): Promise<Array<KeywordFilterAggregation>> {
    if (
      filterField === KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES ||
      filterField === KeywordFilterAutocompleteFilterField.CCSR_PROCEDURES
    ) {
      const claimCodeBuckets = findBucketsForCcsrClaims(
        aggregations as NestedFilteredMatchingCcsrClaimsAggregations
      );
      const claimCodesAggregations: KeywordFilterAggregation[] = [];
      if (!!ccsrClaimCodesToInclude.length && !!claimCodeBuckets) {
        ccsrClaimCodesToInclude.forEach((icdCode) => {
          const bucket = claimCodeBuckets[icdCode];
          const count = bucket.doc_count;
          const descriptionExists = count > 0;
          const id = descriptionExists
            ? bucket.matching_desc?.buckets[0].key ?? icdCode
            : icdCode;
          claimCodesAggregations.push({
            id,
            count
          });
        });
      }

      return claimCodesAggregations;
    }
    let nonEmptyAggregations = findBuckets(aggregations)
      .map(docCountBucketToAggregation)
      .filter(postiveCount);

    if (
      filterField ===
      KeywordFilterAutocompleteFilterField.DIVERSITY_PATIENT_DIVERSITY
    ) {
      nonEmptyAggregations = nonEmptyAggregations.filter(
        supportedRaceCategories
      );
    }

    if (
      filterField === KeywordFilterAutocompleteFilterField.BIOMARKERS &&
      query
    ) {
      nonEmptyAggregations = nonEmptyAggregations.filter((biomarker) =>
        biomarker.id.toLowerCase().includes(query.toLowerCase())
      );
    }

    const claimCodes = nonEmptyAggregations.map((bucket) =>
      extractClaimsCode(bucket.id)
    );
    const ccsrDescriptions = await this.extractCcsrForClaimCodes(
      claimCodes,
      filterField
    );
    if (
      isNestedFilteredMatchingAggregations(aggregations) ||
      isNestedFilteredMatchingLocationAndRegionAggregations(aggregations)
    ) {
      nonEmptyAggregations = nonEmptyAggregations.map((bucket) => ({
        id: this.formatBucketId(filterField, bucket, usersPreferredLanguage),
        count: bucket.count,
        uniqueId: bucket.uniqueId,
        ccsr: ccsrDescriptions.get(extractClaimsCode(bucket.id.toLowerCase())),
        ccsrIcdSize: bucket.icdSize,
        regionsIn: this.formatRegionsIn(bucket, filterField),
        locationsInRegion: this.formatLocationsInRegion(
          bucket,
          filterField,
          usersPreferredLanguage
        )
      }));
      if (
        input?.suppliedFilters.peopleIds?.length &&
        (filterField === KeywordFilterAutocompleteFilterField.DIAGNOSES ||
          filterField === KeywordFilterAutocompleteFilterField.PROCEDURES)
      ) {
        const ccsrToAddToAutoComplete =
          await this.extractCcsrBucketsForClaimCodesWithSize(
            claimCodes,
            filterField
          );
        return nonEmptyAggregations.concat(ccsrToAddToAutoComplete);
      }
      return nonEmptyAggregations;
    }

    return nonEmptyAggregations;
  }

  // Temp fix for data issue with incorrect regions
  private formatRegionsIn(
    bucket: AggregateBucket,
    filterField: KeywordFilterAutocompleteFilterField
  ) {
    if (
      !bucket.regionsIn ||
      filterField === KeywordFilterAutocompleteFilterField.COUNTRY
    ) {
      return bucket.regionsIn;
    }

    const locationTokens = bucket.id.split("|");
    const countryCode = locationTokens[0];
    const regionCode = locationTokens[1];
    if (
      countryCode.toLowerCase() !== "us" ||
      !VALID_STATE_CODES.has(regionCode.toUpperCase())
    ) {
      return [];
    }

    return bucket.regionsIn;
  }

  private formatLocationsInRegion(
    bucket: AggregateBucket,
    filterField: KeywordFilterAutocompleteFilterField,
    usersPreferredLanguage?: string
  ) {
    const validLocationsInRegion = REGION_TO_STATES.get(bucket.id);
    const locationsInRegion = bucket.locationsInRegion?.map((bucket) => ({
      ...bucket,
      id: this.formatBucketId(filterField, bucket, usersPreferredLanguage)
    }));

    if (!locationsInRegion) {
      return undefined;
    }

    // Temp fix for data issue with duplicate locations
    const locationsInRegionMap = new Map<string, AggregateBucket>();
    locationsInRegion.forEach((bucket) => {
      const existingBucket = locationsInRegionMap.get(bucket.id);
      if (existingBucket) {
        existingBucket.count += bucket.count;
      } else {
        locationsInRegionMap.set(bucket.id, bucket);
      }
    });

    const locationBuckets = Array.from(locationsInRegionMap.values());

    if (validLocationsInRegion) {
      return locationBuckets.filter((bucket) =>
        validLocationsInRegion.includes(bucket.id)
      );
    }

    return locationBuckets;
  }

  private formatBucketId(
    filterField: KeywordFilterAutocompleteFilterField,
    bucket: AggregateBucket,
    usersPreferredLanguage?: string
  ): string {
    switch (filterField) {
      case KeywordFilterAutocompleteFilterField.POSTAL_CODE:
        return this.locationLabelFormatterService.postalCodeWithoutCountyAndDistrict(
          bucket.id
        );
      case KeywordFilterAutocompleteFilterField.CITY:
        return this.locationLabelFormatterService.city(bucket.id);
      case KeywordFilterAutocompleteFilterField.REGION:
        return this.locationLabelFormatterService.region(bucket.id);
      case KeywordFilterAutocompleteFilterField.COUNTRY:
        return this.locationLabelFormatterService.country(
          bucket.id,
          usersPreferredLanguage
        );
      case KeywordFilterAutocompleteFilterField.CCSR_DIAGNOSES:
        return bucket.codeAndDescription ?? "";
      default:
        return bucket.id;
    }
  }
  private async extractCcsrForClaimCodes(
    claimCodes: string[],
    field: KeywordFilterAutocompleteFilterField
  ): Promise<Map<string, string[]>> {
    const claimCcsrMap = new Map<string, string[]>();
    if (!claimCodes.length) return claimCcsrMap;

    const isDiagnosis =
      field === KeywordFilterAutocompleteFilterField.DIAGNOSES;
    const isProcedure =
      field === KeywordFilterAutocompleteFilterField.PROCEDURES;
    if (!isDiagnosis && !isProcedure) return claimCcsrMap;

    const normalizedClaimCodes = claimCodes.map(toLower);
    const ccsrDescriptions = isDiagnosis
      ? await this.ccsrIcdMappingRepository.getCcsrForIcd(normalizedClaimCodes)
      : await this.ccsrPxMappingRepository.getCcsrForProcedureCodesWithSize(
          normalizedClaimCodes
        );

    if (!ccsrDescriptions) return claimCcsrMap;

    for (const item of ccsrDescriptions) {
      const key = isDiagnosis
        ? (item as CcsrIcdMapping).icdCode
        : (item as CcsrPxMapping).procedureCode;
      const ccsrList = claimCcsrMap.get(key) ?? [];
      ccsrList.push(item.ccsr);
      claimCcsrMap.set(key, ccsrList);
    }

    return claimCcsrMap;
  }

  private async extractCcsrBucketsForClaimCodesWithSize(
    claimCodes: string[],
    field: KeywordFilterAutocompleteFilterField
  ): Promise<KeywordFilterAggregation[]> {
    if (!claimCodes.length) return [];

    const isDiagnosis =
      field === KeywordFilterAutocompleteFilterField.DIAGNOSES;
    const isProcedure =
      field === KeywordFilterAutocompleteFilterField.PROCEDURES;
    if (!isDiagnosis && !isProcedure) return [];

    const normalizedClaimCodes = claimCodes.map(toLower);
    const ccsrDescriptions = isDiagnosis
      ? await this.ccsrIcdMappingRepository.getCcsrForIcd(normalizedClaimCodes)
      : await this.ccsrPxMappingRepository.getCcsrForProcedureCodesWithSize(
          normalizedClaimCodes
        );

    if (!ccsrDescriptions) return [];

    const ccsrMap = new Map<string, string>();
    for (const { ccsr, claimSize } of ccsrDescriptions) {
      ccsrMap.set(ccsr, claimSize.toString());
    }

    return Array.from(ccsrMap.entries()).map(([ccsr, claimSize]) => ({
      id: ccsr,
      count: 0,
      ccsrIcdSize: claimSize
    }));
  }
}

function isNestedFilteredMatchingLocationAndRegionAggregations(
  agg: SupportedAggregations
): agg is NestedFilteredMatchingLocationAndRegionAggregations {
  return !!(agg as NestedFilteredMatchingLocationAndRegionAggregations).nested
    ?.filtered_matching?.filtered_matching_locations?.matching_locations;
}

function isNestedFilteredMatchingAggregations(
  agg: SupportedAggregations
): agg is NestedFilteredMatchingAggregations {
  return !!(agg as NestedFilteredMatchingAggregations).nested?.filtered_matching
    ?.matching;
}
function isNestedFilteredFiltersAggregations(
  agg: NestedFilteredMatchingCcsrClaimsAggregations
): agg is NestedFilteredMatchingCcsrClaimsAggregations {
  return !!(agg as NestedFilteredMatchingCcsrClaimsAggregations).nested
    ?.filtered_matching?.buckets;
}

function isNestedMatchingAggregations(
  agg: SupportedAggregations
): agg is NestedMatchingAggregations {
  return !!(agg as NestedMatchingAggregations).nested?.matching;
}

function isFilteredDocCountBucket(
  bucket: FilteredDocCountBucket | DocCountBucket
): bucket is FilteredDocCountBucket {
  return !!(bucket as FilteredDocCountBucket).matching;
}

function postiveCount(bucket: AggregateBucket) {
  return bucket.count > 0;
}

function docCountBucketToAggregation(
  bucket: DocCountBucket | FilteredDocCountBucket
): AggregateBucket {
  let count;

  if (isFilteredDocCountBucket(bucket)) {
    count = bucket.matching.doc_count ?? bucket.matching.value;
  } else {
    count = bucket.doc_count;
  }

  const uniqueIdentifier = bucket.by_congressId?.buckets[0].key.split("-")[0];
  const codeAndDescription = bucket.matching_desc?.buckets[0].key;
  const icdSize = bucket.icd_size?.buckets[0].key;
  const regionsIn = bucket.regions_in?.buckets.map((bucket) => bucket.key);
  const locationsInRegion = bucket.locations_in_region?.buckets.map(
    docCountBucketToAggregation
  );

  return {
    id: bucket.key,
    count: count ?? 0,
    uniqueId: uniqueIdentifier,
    codeAndDescription,
    icdSize,
    regionsIn,
    locationsInRegion
  };
}
function findBucketsForCcsrClaims(
  aggregations: NestedFilteredMatchingCcsrClaimsAggregations
): Record<string, FilteredMatchingBucket> {
  if (isNestedFilteredFiltersAggregations(aggregations)) {
    return aggregations.nested.filtered_matching.buckets as Record<
      string,
      FilteredMatchingBucket
    >;
  } else return {};
}
function findBuckets(aggregations: SupportedAggregations) {
  if (isNestedFilteredMatchingLocationAndRegionAggregations(aggregations)) {
    const matchingRegions = (aggregations.nested.filtered_matching
      .filtered_matching_regions?.matching_regions?.buckets ??
      []) as FilteredDocCountBucket[];
    const matchingLocations = (aggregations.nested.filtered_matching
      .filtered_matching_locations?.matching_locations?.buckets ??
      []) as FilteredDocCountBucket[];

    return [...matchingLocations, ...matchingRegions];
  }

  if (isNestedFilteredMatchingAggregations(aggregations)) {
    return aggregations.nested.filtered_matching.matching
      .buckets as DocCountBucket[];
  }

  if (isNestedMatchingAggregations(aggregations)) {
    return aggregations.nested.matching.buckets as DocCountBucket[];
  }

  return (aggregations as MatchingAggregations).matching
    .buckets as DocCountBucket[];
}

function supportedRaceCategories(bucket: AggregateBucket) {
  const unsupportedRaceCategories = [
    ASIAN_RACE_CATEGORY,
    PACIFIC_ISLANDER_RACE_CATEGORY
  ];

  return !unsupportedRaceCategories.includes(bucket.id);
}
