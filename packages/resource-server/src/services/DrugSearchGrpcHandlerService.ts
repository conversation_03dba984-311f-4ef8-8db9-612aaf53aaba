import {
  DrugSearchServiceImplementation,
  DrugValueSearchInput,
  DrugValues,
  GenericDrugSearchInput,
  GenericDrugs
} from "@h1nyc/search-sdk";
import { DrugSearchService } from "./DrugSearchService";
import { Service } from "typedi";
import { AutocompleteGenericDrugInput } from "@h1nyc/search-sdk/dist/grpc/proto/drug_search";

@Service()
export class DrugSearchGrpcHandlerService
  implements DrugSearchServiceImplementation
{
  constructor(private drugSearchService: DrugSearchService) {}

  searchDrugValues(request: DrugValueSearchInput): Promise<DrugValues> {
    return this.drugSearchService.searchDrugValues(request);
  }

  searchGenericDrugs(request: GenericDrugSearchInput): Promise<GenericDrugs> {
    return this.drugSearchService.searchGenericDrugs(request);
  }

  autocompleteGenericDrugs(request: AutocompleteGenericDrugInput) {
    return this.drugSearchService.autocompleteGenericDrugs(request);
  }
}
