jest.mock("ioredis");
jest.mock("object-hash");
import Redis from "ioredis-mock";
import objectHash from "object-hash";

import { faker } from "@faker-js/faker";
import {
  CongressSearchFilterAutocompleteInput,
  CongressSearchInput,
  CongressSearchSortOrder,
  IndicationNode,
  IndicationType
} from "@h1nyc/search-sdk";
import {
  createMockInstance,
  generateMockElasticsearchResponse,
  generateMockElasticsearchResponseWithAggregations,
  mockQueryUnderstandingServerResponse
} from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { ElasticSearchCongressService } from "./ElasticsearchCongressService";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";
import { CongressSearchResourceService } from "./CongressSearchResourceService";
import { CongressSearchResponseAdapterService } from "./CongressSearchResponseAdapterService";
import CalculateMinimumNameVariations from "./queryBuilders/CalculateMinimumNameVariations";
import { UserOnboardingDataService } from "./UserOnboardingDataService";
import NameSearchBuilderFactory from "./queryBuilders/NameSearchBuilderFactory";
import { ElasticSearchService } from "./ElasticSearchService";
import {
  CHINESE,
  ENGLISH,
  JAPANESE,
  Language,
  LanguageDetectService
} from "./LanguageDetectService";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { UserResourceClient } from "@h1nyc/account-sdk";
import { FollowRecordResourceClient } from "@h1nyc/notifications-sdk";
import {
  featureFlagDefaults,
  NameSearchFeatureFlags,
  nameSearchFeatureFlagTypes
} from "./NameSearchResourceServiceRewrite";
import { when } from "jest-when";
import { LDFlagsState } from "launchdarkly-node-server-sdk";
import DefaultNameSearchBuilder from "./queryBuilders/DefaultNameSearchBuilder";
import { generateMockOnboardingData } from "./NameSearchResourceServiceRewrite.test";
import { QueryUnderstandingServiceResponseForIndications } from "../proto/query_understanding_service_pb";
import { TagsHelperService } from "./TagsHelperService";
import { MsearchResponse } from "@elastic/elasticsearch/lib/api/types";

const FEATURE_FLAG_DEFAULTS: NameSearchFeatureFlags = {
  enableHighConfHCPFeature: false,
  enableQueryIntent: false,
  enableIntentBasedSearchQuery: false,
  enableNameSearchPersonalisation: false,
  enableCTMSV2: false,
  enableResultsOutsideUsersSlice: false,
  enableTagsInElasticsearch: false,
  enableBrazilianClaims: true,
  enableUniquePatientCountForClaims: false,
  disableUniquePatientCountForOnlyProcedures: false,
  enableNestedIndicationFilter: false,
  enableCcsrExclusionForMatchedCounts: false,
  enableLocationFilterRegionRollup: false,
  enableNewGlobalLeaderTier: false,
  enableExUSGlobalLeader: false,
  enableCountrySpecificNonIndicationLeaderFilters: false
};

const generateNameSearchFeatureFlags = (
  overrides: Partial<NameSearchFeatureFlags> = {}
): NameSearchFeatureFlags => {
  return {
    ...FEATURE_FLAG_DEFAULTS,
    ...overrides
  };
};

function generateFeatureFlagsState(
  featureFlagValues: Partial<NameSearchFeatureFlags> = {}
) {
  const featureFlags = {
    ...generateNameSearchFeatureFlags(),
    ...featureFlagValues
  };

  const getFlagValue = jest.fn();

  for (const flag of nameSearchFeatureFlagTypes) {
    const flagKey = featureFlagDefaults[flag].key;
    when(getFlagValue).calledWith(flagKey).mockReturnValue(featureFlags[flag]);
  }

  return {
    getFlagValue
  } as unknown as LDFlagsState;
}

function generateMockPersonHit() {
  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: { id: faker.datatype.uuid() }
  };
}

function getUTCTimestampMidnight() {
  const todayMidnightUtc = new Date(
    Date.UTC(
      new Date().getUTCFullYear(),
      new Date().getUTCMonth(),
      new Date().getUTCDate(),
      0,
      0,
      0,
      0
    )
  );
  return todayMidnightUtc.getTime();
}

function getUTCTimestampMidnightTomorrow() {
  const tomorrowMidnightUtc = new Date(
    Date.UTC(
      new Date().getUTCFullYear(),
      new Date().getUTCMonth(),
      new Date().getUTCDate() + 1,
      0,
      0,
      0,
      0
    )
  );
  return tomorrowMidnightUtc.getTime();
}

let redis = new Redis();

beforeEach(() => {
  jest.resetAllMocks();
});

afterEach((done) => {
  if (redis) {
    redis.flushall().then(() => done());
  }
});

describe("CongressSearchResourceService", () => {
  describe("search", () => {
    describe("elasticsearch query building", () => {
      describe("input query", () => {
        it("should include should clauses for each of the main search paths", async () => {
          const input: CongressSearchInput = {
            query: faker.datatype.string(),
            userId: faker.datatype.string(),
            projectId: faker.datatype.string(),
            paging: {
              offset: faker.datatype.number(),
              limit: faker.datatype.number()
            }
          };

          const configService = createMockInstance(ConfigService);
          configService.elasticCongressIndex = faker.datatype.string();

          const queryUnderstandingServiceClient = createMockInstance(
            QueryUnderstandingServiceClient
          );
          const queryUnderstandingServerResponse =
            mockQueryUnderstandingServerResponse(input.query!, false);
          queryUnderstandingServiceClient.analyze.mockResolvedValue(
            queryUnderstandingServerResponse
          );

          const elasticSearchCongressService = createMockInstance(
            ElasticSearchCongressService
          );
          elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
          elasticSearchCongressService.query.mockResolvedValue(
            generateMockElasticsearchResponseWithAggregations()
          );

          const indicationsTreeSearchService = createMockInstance(
            IndicationsTreeSearchService
          );

          const congressSearchResponseAdapterService = createMockInstance(
            CongressSearchResponseAdapterService
          );

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );

          const nameSearchBuilderFactory = createMockInstance(
            NameSearchBuilderFactory
          );

          nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
            DefaultNameSearchBuilder.getInstance()
          );

          const matchingPeople = Array.from({ length: 10 }, () =>
            generateMockPersonHit()
          );
          const elasticSearchPeopleService =
            createMockInstance(ElasticSearchService);
          elasticSearchPeopleService.query.mockResolvedValue(
            generateMockElasticsearchResponse(matchingPeople)
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );

          const languageDetector = () => {
            return faker.helpers.arrayElement([
              ENGLISH,
              CHINESE,
              JAPANESE
            ]) as Language;
          };
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );

          const featureFlagsService = createMockInstance(
            FeatureFlagsService as any
          ) as FeatureFlagsService;
          featureFlagsService.getAllFlags = jest
            .fn()
            .mockResolvedValue(generateFeatureFlagsState());

          const userClient = createMockInstance(UserResourceClient);
          const followRecordClient = createMockInstance(
            FollowRecordResourceClient
          );

          const tagsHelperService = createMockInstance(TagsHelperService);
          tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);

          const hashedValue = faker.datatype.string();
          objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);

          redis = new Redis({
            data: {
              [hashedValue]: "[]"
            }
          });

          const congressSearchResourceService =
            new CongressSearchResourceService(
              configService,
              congressSearchResponseAdapterService,
              queryUnderstandingServiceClient,
              calculateMinimumNameVariations,
              indicationsTreeSearchService,
              elasticSearchCongressService,
              userOnboardingDataService,
              nameSearchBuilderFactory,
              elasticSearchPeopleService,
              followRecordClient,
              languageDetectService,
              featureFlagsService,
              tagsHelperService,
              userClient
            );

          await congressSearchResourceService.search(input);

          expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              index: configService.elasticCongressIndex,
              track_total_hits: true,
              _source: {
                include: [
                  "h1_conference_id",
                  "h1_series_id",
                  "name",
                  "series_name",
                  "description",
                  "society",
                  "indication",
                  "url",
                  "translations",
                  "addresses",
                  "h1_person_ids",
                  "speakers",
                  "filters.congress_type",
                  "filters.start_date",
                  "filters.end_date"
                ]
              },
              query: {
                function_score: {
                  query: {
                    bool: {
                      filter: [],
                      minimum_should_match: 0,
                      must: {
                        bool: {
                          should: [
                            {
                              simple_query_string: {
                                default_operator: "and",
                                fields: [
                                  "name^10",
                                  "series_name^10",
                                  "society",
                                  "indication"
                                ],
                                query: input.query
                              }
                            }
                          ],
                          minimum_should_match: 1
                        }
                      }
                    }
                  },
                  functions: [
                    {
                      filter: {
                        bool: {
                          filter: [
                            {
                              range: {
                                "filters.start_date": {
                                  lt: expect.anything()
                                }
                              }
                            },
                            {
                              range: {
                                "filters.end_date": {
                                  gte: expect.anything()
                                }
                              }
                            }
                          ]
                        }
                      },
                      weight: 1000
                    },
                    {
                      filter: {
                        range: {
                          "filters.start_date": {
                            gt: expect.anything()
                          }
                        }
                      },
                      weight: 500
                    }
                  ],
                  score_mode: "sum",
                  boost_mode: "replace"
                }
              },
              from: input.paging?.offset,
              size: input.paging?.limit,
              sort: [
                "_score",
                {
                  _script: {
                    type: "number",
                    order: "asc",
                    script: {
                      lang: "painless",
                      source:
                        "\n            long ts = doc['filters.start_date'].value.toInstant().toEpochMilli(); \n            long now = params.now; \n            return ts >= now ? 0 : 1;\n          ",
                      params: {
                        now: expect.any(Number)
                      }
                    }
                  }
                },
                {
                  _script: {
                    type: "number",
                    order: "asc",
                    script: {
                      lang: "painless",
                      source:
                        "\n            long ts = doc['filters.end_date'].value.toInstant().toEpochMilli(); \n            long now = params.now;\n            if (ts >= now) {\n              // future: sort by ts ↑ (earliest future first)\n              return ts;\n            } else {\n              // past: return negative ts so that more‐recent past (larger ts)\n              // becomes more‐negative → appears earlier when ordering ↑\n              return -ts;\n            }\n          ",
                      params: {
                        now: expect.any(Number)
                      }
                    }
                  }
                },
                {
                  speaker_count: "desc"
                }
              ]
            })
          );

          const actualQuerySort = elasticSearchCongressService.query.mock
            .calls[0][0].sort as any;

          const expectedFutureCongressSortTimestamp =
            getUTCTimestampMidnightTomorrow();
          const expectedPastCongressSortTimestamp = getUTCTimestampMidnight();
          const actualFutureCongressSortTimestamp =
            actualQuerySort[1]._script.script.params.now;
          const actualPastCongressSortTimestamp =
            actualQuerySort[2]._script.script.params.now;
          expect(actualFutureCongressSortTimestamp).toBeGreaterThanOrEqual(
            expectedFutureCongressSortTimestamp - 1000
          ); // allow 1s skew
          expect(actualFutureCongressSortTimestamp).toBeLessThanOrEqual(
            expectedFutureCongressSortTimestamp + 1000
          );
          expect(actualPastCongressSortTimestamp).toBeGreaterThanOrEqual(
            expectedPastCongressSortTimestamp - 1000
          ); // allow 1s skew
          expect(actualPastCongressSortTimestamp).toBeLessThanOrEqual(
            expectedPastCongressSortTimestamp + 1000
          );
        });

        describe("filters", () => {
          it("should include filters in elasticsearch request when defined in input", async () => {
            const input: CongressSearchInput = {
              query: faker.datatype.string(),
              projectId: faker.datatype.string(),
              userId: faker.datatype.string(),
              filters: {
                location: {
                  countries: [
                    faker.address.countryCode(),
                    faker.address.countryCode()
                  ],
                  excludeCountries: [
                    faker.address.countryCode(),
                    faker.address.countryCode()
                  ],
                  regions: [
                    faker.address.stateAbbr(),
                    faker.address.stateAbbr()
                  ],
                  excludeRegions: [
                    faker.address.stateAbbr(),
                    faker.address.stateAbbr()
                  ],
                  cities: [faker.address.city(), faker.address.city()],
                  excludeCities: [faker.address.city(), faker.address.city()],
                  postalCodes: [
                    faker.address.zipCode(),
                    faker.address.zipCode()
                  ],
                  excludePostalCodes: [
                    faker.address.zipCode(),
                    faker.address.zipCode()
                  ],
                  isVirtual: true
                },
                dateRange: {
                  min: faker.datatype.number(),
                  max: faker.datatype.number()
                },
                type: {
                  congressTypes: [
                    faker.datatype.string(),
                    faker.datatype.string()
                  ]
                },
                society: {
                  societyNames: [
                    faker.datatype.string(),
                    faker.datatype.string()
                  ]
                },
                indication: {
                  indications: [
                    faker.datatype.string(),
                    faker.datatype.string()
                  ]
                },
                series: {
                  seriesNames: [
                    faker.datatype.string(),
                    faker.datatype.string()
                  ]
                }
              },
              paging: {
                offset: faker.datatype.number(),
                limit: faker.datatype.number()
              }
            };

            const configService = createMockInstance(ConfigService);
            configService.elasticCongressIndex = faker.datatype.string();

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServerResponse =
              mockQueryUnderstandingServerResponse(input.query!, false);

            const quResponseIndiations: QueryUnderstandingServiceResponseForIndications =
              jest.createMockFromModule(
                "../proto/query_understanding_service_pb"
              );
            const indicationsParsedQuery = faker.datatype.string();
            quResponseIndiations.getIndicationsParsedQuery = jest.fn(
              () => indicationsParsedQuery
            );
            quResponseIndiations.getIndicationsIcdCodesQuery = jest.fn(() =>
              faker.datatype.string()
            );
            queryUnderstandingServiceClient.getIndicationSynonymsAndIcdCodes.mockResolvedValue(
              quResponseIndiations
            );
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServerResponse
            );

            const elasticSearchCongressService = createMockInstance(
              ElasticSearchCongressService
            );
            elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
            elasticSearchCongressService.query.mockResolvedValue(
              generateMockElasticsearchResponseWithAggregations()
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );
            const mockIndications: IndicationNode[] = [
              {
                id: faker.datatype.string(),
                indicationName: faker.datatype.string(),
                h1Id: "",
                parentH1Ids: [],
                indicationType: IndicationType.L1,
                matchedSynonyms: [],
                icdCodes: [],
                children: []
              }
            ];
            indicationsTreeSearchService.searchIndicationsByQuery.mockResolvedValue(
              mockIndications
            );

            const congressSearchResponseAdapterService = createMockInstance(
              CongressSearchResponseAdapterService
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const userOnboardingDataService = createMockInstance(
              UserOnboardingDataService
            );
            userOnboardingDataService.getOnboardingData.mockResolvedValue(
              generateMockOnboardingData()
            );

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
              DefaultNameSearchBuilder.getInstance()
            );

            const matchingPeople = Array.from({ length: 10 }, () =>
              generateMockPersonHit()
            );
            const elasticSearchPeopleService =
              createMockInstance(ElasticSearchService);
            elasticSearchPeopleService.query.mockResolvedValue(
              generateMockElasticsearchResponse(matchingPeople)
            );

            const languageDetectService = createMockInstance(
              LanguageDetectService
            );

            const languageDetector = () => {
              return faker.helpers.arrayElement([
                ENGLISH,
                CHINESE,
                JAPANESE
              ]) as Language;
            };
            languageDetectService.getLanguageDetector.mockReturnValue(
              languageDetector
            );

            const featureFlagsService = createMockInstance(
              FeatureFlagsService as any
            ) as FeatureFlagsService;
            featureFlagsService.getAllFlags = jest
              .fn()
              .mockResolvedValue(generateFeatureFlagsState());

            const userClient = createMockInstance(UserResourceClient);
            const followRecordClient = createMockInstance(
              FollowRecordResourceClient
            );

            const tagsHelperService = createMockInstance(TagsHelperService);
            tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);

            const congressSearchResourceService =
              new CongressSearchResourceService(
                configService,
                congressSearchResponseAdapterService,
                queryUnderstandingServiceClient,
                calculateMinimumNameVariations,
                indicationsTreeSearchService,
                elasticSearchCongressService,
                userOnboardingDataService,
                nameSearchBuilderFactory,
                elasticSearchPeopleService,
                followRecordClient,
                languageDetectService,
                featureFlagsService,
                tagsHelperService,
                userClient
              );

            await congressSearchResourceService.search(input);

            const searchQueryWithIndications = `${input.query}|${indicationsParsedQuery}`;

            expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
              expect.objectContaining({
                index: configService.elasticCongressIndex,
                track_total_hits: true,
                _source: {
                  include: expect.any(Array)
                },
                query: {
                  function_score: {
                    query: {
                      bool: {
                        filter: [
                          {
                            bool: {
                              should: [
                                {
                                  terms: {
                                    "filters.country":
                                      input?.filters?.location?.countries
                                  }
                                },
                                {
                                  terms: {
                                    "filters.country_level_regions":
                                      input?.filters?.location?.countries
                                  }
                                }
                              ]
                            }
                          },
                          {
                            bool: {
                              must_not: [
                                {
                                  terms: {
                                    "filters.country":
                                      input?.filters?.location?.excludeCountries
                                  }
                                },
                                {
                                  terms: {
                                    "filters.country_level_regions":
                                      input?.filters?.location?.excludeCountries
                                  }
                                }
                              ]
                            }
                          },
                          {
                            bool: {
                              should: [
                                {
                                  terms: {
                                    "filters.region":
                                      input?.filters?.location?.regions
                                  }
                                },
                                {
                                  terms: {
                                    "filters.state_level_regions":
                                      input?.filters?.location?.regions
                                  }
                                }
                              ]
                            }
                          },
                          {
                            bool: {
                              must_not: [
                                {
                                  terms: {
                                    "filters.region":
                                      input?.filters?.location?.excludeRegions
                                  }
                                },
                                {
                                  terms: {
                                    "filters.state_level_regions":
                                      input?.filters?.location?.excludeRegions
                                  }
                                }
                              ]
                            }
                          },
                          {
                            bool: {
                              should: [
                                {
                                  terms: {
                                    "filters.city":
                                      input?.filters?.location?.cities
                                  }
                                },
                                {
                                  terms: {
                                    "filters.city_level_regions":
                                      input?.filters?.location?.cities
                                  }
                                }
                              ]
                            }
                          },
                          {
                            bool: {
                              must_not: [
                                {
                                  terms: {
                                    "filters.city":
                                      input?.filters?.location?.excludeCities
                                  }
                                },
                                {
                                  terms: {
                                    "filters.city_level_regions":
                                      input?.filters?.location?.excludeCities
                                  }
                                }
                              ]
                            }
                          },
                          {
                            regexp: {
                              "filters.postal_code": {
                                value: `.+\\|(${input?.filters?.location?.postalCodes
                                  ?.filter((value) => !!value)
                                  .join("|")})`
                              }
                            }
                          },
                          {
                            bool: {
                              must_not: {
                                regexp: {
                                  "filters.postal_code": {
                                    value: `.+\\|(${input?.filters?.location?.excludePostalCodes
                                      ?.filter((value) => !!value)
                                      .join("|")})`
                                  }
                                }
                              }
                            }
                          },
                          {
                            term: {
                              "filters.is_virtual": true
                            }
                          },
                          {
                            terms: {
                              "filters.indication":
                                input.filters?.indication?.indications
                            }
                          },
                          {
                            range: {
                              "filters.end_date": {
                                gte: input.filters?.dateRange?.min
                              }
                            }
                          },
                          {
                            range: {
                              "filters.start_date": {
                                lte: input.filters?.dateRange?.max
                              }
                            }
                          },
                          {
                            terms: {
                              "filters.series":
                                input.filters?.series?.seriesNames
                            }
                          },
                          {
                            terms: {
                              "filters.society":
                                input.filters?.society?.societyNames
                            }
                          },
                          {
                            terms: {
                              "filters.congress_type":
                                input.filters?.type?.congressTypes
                            }
                          }
                        ],
                        minimum_should_match: 0,
                        must: {
                          bool: {
                            should: [
                              {
                                simple_query_string: {
                                  default_operator: "and",
                                  fields: [
                                    "name^10",
                                    "series_name^10",
                                    "society",
                                    "indication"
                                  ],
                                  query: searchQueryWithIndications
                                }
                              }
                            ],
                            minimum_should_match: 1
                          }
                        }
                      }
                    },
                    functions: [
                      {
                        filter: {
                          bool: {
                            filter: [
                              {
                                range: {
                                  "filters.start_date": {
                                    lt: expect.anything()
                                  }
                                }
                              },
                              {
                                range: {
                                  "filters.end_date": {
                                    gte: expect.anything()
                                  }
                                }
                              }
                            ]
                          }
                        },
                        weight: 1000
                      },
                      {
                        filter: {
                          range: {
                            "filters.start_date": {
                              gt: expect.anything()
                            }
                          }
                        },
                        weight: 500
                      }
                    ],
                    score_mode: "sum",
                    boost_mode: "replace"
                  }
                },
                from: input.paging?.offset,
                size: input.paging?.limit,
                sort: expect.any(Array)
              })
            );
          });

          it("should not query elasticsearch if isFollowing is true and user has no followed congress series", async () => {
            const input: CongressSearchInput = {
              query: faker.datatype.string(),
              userId: faker.datatype.string(),
              projectId: faker.datatype.string(),
              paging: {
                offset: faker.datatype.number(),
                limit: faker.datatype.number()
              },
              filters: {
                following: {
                  isFollowing: true
                }
              }
            };

            const configService = createMockInstance(ConfigService);
            configService.elasticCongressIndex = faker.datatype.string();

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServerResponse =
              mockQueryUnderstandingServerResponse(input.query!, false);
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServerResponse
            );

            const elasticSearchService = createMockInstance(
              ElasticSearchCongressService
            );

            const indicationTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const congressSearchResponseAdapterService = createMockInstance(
              CongressSearchResponseAdapterService
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const userOnboardingDataService = createMockInstance(
              UserOnboardingDataService
            );
            userOnboardingDataService.getOnboardingData.mockResolvedValue(
              generateMockOnboardingData()
            );

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );

            const followRecordClient = createMockInstance(
              FollowRecordResourceClient
            );
            followRecordClient.getPaginatedFollowRecordIds.mockResolvedValue({
              total: 0,
              items: []
            });

            const languageDetectService = createMockInstance(
              LanguageDetectService
            );

            const languageDetector = () => {
              return faker.helpers.arrayElement([
                ENGLISH,
                CHINESE,
                JAPANESE
              ]) as Language;
            };
            languageDetectService.getLanguageDetector.mockReturnValue(
              languageDetector
            );

            const featureFlagsService = createMockInstance(
              FeatureFlagsService as any
            ) as FeatureFlagsService;
            featureFlagsService.getAllFlags = jest
              .fn()
              .mockResolvedValue(generateFeatureFlagsState());

            const userClient = createMockInstance(UserResourceClient);

            const tagsHelperService = createMockInstance(TagsHelperService);
            tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);

            const congressSearchResourceService =
              new CongressSearchResourceService(
                configService,
                congressSearchResponseAdapterService,
                queryUnderstandingServiceClient,
                calculateMinimumNameVariations,
                indicationTreeSearchService,
                elasticSearchService,
                userOnboardingDataService,
                nameSearchBuilderFactory,
                elasticSearchService,
                followRecordClient,
                languageDetectService,
                featureFlagsService,
                tagsHelperService,
                userClient
              );

            const response = await congressSearchResourceService.search(input);

            expect(response).toEqual(
              expect.objectContaining({
                total: 0,
                congresses: []
              })
            );
            expect(elasticSearchService.query).not.toHaveBeenCalled();
            expect(
              congressSearchResponseAdapterService.adaptToCongressSearchFilterAggregations
            ).not.toHaveBeenCalled();
            expect(
              congressSearchResponseAdapterService.adaptToCongressSearchResponse
            ).not.toHaveBeenCalled();
          });
        });

        describe("sort options", () => {
          it("should apply chronological sort when passed in", async () => {
            const input: CongressSearchInput = {
              query: faker.datatype.string(),
              userId: faker.datatype.string(),
              projectId: faker.datatype.string(),
              paging: {
                offset: faker.datatype.number(),
                limit: faker.datatype.number()
              },
              sortOrder: CongressSearchSortOrder.CHRONOLOGICAL
            };

            const configService = createMockInstance(ConfigService);
            configService.elasticCongressIndex = faker.datatype.string();

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServerResponse =
              mockQueryUnderstandingServerResponse(input.query!, false);
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServerResponse
            );

            const elasticSearchCongressService = createMockInstance(
              ElasticSearchCongressService
            );
            elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
            elasticSearchCongressService.query.mockResolvedValue(
              generateMockElasticsearchResponseWithAggregations()
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const congressSearchResponseAdapterService = createMockInstance(
              CongressSearchResponseAdapterService
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const userOnboardingDataService = createMockInstance(
              UserOnboardingDataService
            );
            userOnboardingDataService.getOnboardingData.mockResolvedValue(
              generateMockOnboardingData()
            );

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
              DefaultNameSearchBuilder.getInstance()
            );

            const matchingPeople = Array.from({ length: 10 }, () =>
              generateMockPersonHit()
            );
            const elasticSearchPeopleService =
              createMockInstance(ElasticSearchService);
            elasticSearchPeopleService.query.mockResolvedValue(
              generateMockElasticsearchResponse(matchingPeople)
            );

            const languageDetectService = createMockInstance(
              LanguageDetectService
            );

            const languageDetector = () => {
              return faker.helpers.arrayElement([
                ENGLISH,
                CHINESE,
                JAPANESE
              ]) as Language;
            };
            languageDetectService.getLanguageDetector.mockReturnValue(
              languageDetector
            );

            const featureFlagsService = createMockInstance(
              FeatureFlagsService as any
            ) as FeatureFlagsService;
            featureFlagsService.getAllFlags = jest
              .fn()
              .mockResolvedValue(generateFeatureFlagsState());

            const userClient = createMockInstance(UserResourceClient);
            const followRecordClient = createMockInstance(
              FollowRecordResourceClient
            );

            const tagsHelperService = createMockInstance(TagsHelperService);
            tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);

            const hashedValue = faker.datatype.string();
            objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);

            redis = new Redis({
              data: {
                [hashedValue]: "[]"
              }
            });

            const congressSearchResourceService =
              new CongressSearchResourceService(
                configService,
                congressSearchResponseAdapterService,
                queryUnderstandingServiceClient,
                calculateMinimumNameVariations,
                indicationsTreeSearchService,
                elasticSearchCongressService,
                userOnboardingDataService,
                nameSearchBuilderFactory,
                elasticSearchPeopleService,
                followRecordClient,
                languageDetectService,
                featureFlagsService,
                tagsHelperService,
                userClient
              );

            await congressSearchResourceService.search(input);

            expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
              expect.objectContaining({
                index: configService.elasticCongressIndex,
                track_total_hits: true,
                _source: expect.any(Object),
                query: expect.any(Object),
                from: input.paging?.offset,
                size: input.paging?.limit,
                sort: [
                  { "filters.start_date": "asc" },
                  {
                    speaker_count: "desc"
                  }
                ]
              })
            );
          });

          it("should apply reverse chronological sort when passed in", async () => {
            const input: CongressSearchInput = {
              query: faker.datatype.string(),
              userId: faker.datatype.string(),
              projectId: faker.datatype.string(),
              paging: {
                offset: faker.datatype.number(),
                limit: faker.datatype.number()
              },
              sortOrder: CongressSearchSortOrder.REVERSE_CHRONOLOGICAL
            };

            const configService = createMockInstance(ConfigService);
            configService.elasticCongressIndex = faker.datatype.string();

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServerResponse =
              mockQueryUnderstandingServerResponse(input.query!, false);
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServerResponse
            );

            const elasticSearchCongressService = createMockInstance(
              ElasticSearchCongressService
            );
            elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
            elasticSearchCongressService.query.mockResolvedValue(
              generateMockElasticsearchResponseWithAggregations()
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const congressSearchResponseAdapterService = createMockInstance(
              CongressSearchResponseAdapterService
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const userOnboardingDataService = createMockInstance(
              UserOnboardingDataService
            );
            userOnboardingDataService.getOnboardingData.mockResolvedValue(
              generateMockOnboardingData()
            );

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
              DefaultNameSearchBuilder.getInstance()
            );

            const matchingPeople = Array.from({ length: 10 }, () =>
              generateMockPersonHit()
            );
            const elasticSearchPeopleService =
              createMockInstance(ElasticSearchService);
            elasticSearchPeopleService.query.mockResolvedValue(
              generateMockElasticsearchResponse(matchingPeople)
            );

            const languageDetectService = createMockInstance(
              LanguageDetectService
            );

            const languageDetector = () => {
              return faker.helpers.arrayElement([
                ENGLISH,
                CHINESE,
                JAPANESE
              ]) as Language;
            };
            languageDetectService.getLanguageDetector.mockReturnValue(
              languageDetector
            );

            const featureFlagsService = createMockInstance(
              FeatureFlagsService as any
            ) as FeatureFlagsService;
            featureFlagsService.getAllFlags = jest
              .fn()
              .mockResolvedValue(generateFeatureFlagsState());

            const userClient = createMockInstance(UserResourceClient);
            const followRecordClient = createMockInstance(
              FollowRecordResourceClient
            );

            const tagsHelperService = createMockInstance(TagsHelperService);
            tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);

            const hashedValue = faker.datatype.string();
            objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);

            redis = new Redis({
              data: {
                [hashedValue]: "[]"
              }
            });

            const congressSearchResourceService =
              new CongressSearchResourceService(
                configService,
                congressSearchResponseAdapterService,
                queryUnderstandingServiceClient,
                calculateMinimumNameVariations,
                indicationsTreeSearchService,
                elasticSearchCongressService,
                userOnboardingDataService,
                nameSearchBuilderFactory,
                elasticSearchPeopleService,
                followRecordClient,
                languageDetectService,
                featureFlagsService,
                tagsHelperService,
                userClient
              );

            await congressSearchResourceService.search(input);

            expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
              expect.objectContaining({
                index: configService.elasticCongressIndex,
                track_total_hits: true,
                _source: expect.any(Object),
                query: expect.any(Object),
                from: input.paging?.offset,
                size: input.paging?.limit,
                sort: [
                  { "filters.start_date": "desc" },
                  {
                    speaker_count: "desc"
                  }
                ]
              })
            );
          });

          it("should apply H1 Ranking sort when passed in", async () => {
            const input: CongressSearchInput = {
              query: faker.datatype.string(),
              userId: faker.datatype.string(),
              projectId: faker.datatype.string(),
              paging: {
                offset: faker.datatype.number(),
                limit: faker.datatype.number()
              },
              sortOrder: CongressSearchSortOrder.H1_RANKING
            };

            const configService = createMockInstance(ConfigService);
            configService.elasticCongressIndex = faker.datatype.string();

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServerResponse =
              mockQueryUnderstandingServerResponse(input.query!, false);
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServerResponse
            );

            const elasticSearchCongressService = createMockInstance(
              ElasticSearchCongressService
            );
            elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
            elasticSearchCongressService.query.mockResolvedValue(
              generateMockElasticsearchResponseWithAggregations()
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );

            const congressSearchResponseAdapterService = createMockInstance(
              CongressSearchResponseAdapterService
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const userOnboardingDataService = createMockInstance(
              UserOnboardingDataService
            );
            userOnboardingDataService.getOnboardingData.mockResolvedValue(
              generateMockOnboardingData()
            );

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
              DefaultNameSearchBuilder.getInstance()
            );

            const matchingPeople = Array.from({ length: 10 }, () =>
              generateMockPersonHit()
            );
            const elasticSearchPeopleService =
              createMockInstance(ElasticSearchService);
            elasticSearchPeopleService.query.mockResolvedValue(
              generateMockElasticsearchResponse(matchingPeople)
            );

            const languageDetectService = createMockInstance(
              LanguageDetectService
            );

            const languageDetector = () => {
              return faker.helpers.arrayElement([
                ENGLISH,
                CHINESE,
                JAPANESE
              ]) as Language;
            };
            languageDetectService.getLanguageDetector.mockReturnValue(
              languageDetector
            );

            const featureFlagsService = createMockInstance(
              FeatureFlagsService as any
            ) as FeatureFlagsService;
            featureFlagsService.getAllFlags = jest
              .fn()
              .mockResolvedValue(generateFeatureFlagsState());

            const userClient = createMockInstance(UserResourceClient);
            const followRecordClient = createMockInstance(
              FollowRecordResourceClient
            );

            const tagsHelperService = createMockInstance(TagsHelperService);
            tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);

            const hashedValue = faker.datatype.string();
            objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);

            redis = new Redis({
              data: {
                [hashedValue]: "[]"
              }
            });

            const congressSearchResourceService =
              new CongressSearchResourceService(
                configService,
                congressSearchResponseAdapterService,
                queryUnderstandingServiceClient,
                calculateMinimumNameVariations,
                indicationsTreeSearchService,
                elasticSearchCongressService,
                userOnboardingDataService,
                nameSearchBuilderFactory,
                elasticSearchPeopleService,
                followRecordClient,
                languageDetectService,
                featureFlagsService,
                tagsHelperService,
                userClient
              );

            await congressSearchResourceService.search(input);

            expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
              expect.objectContaining({
                index: configService.elasticCongressIndex,
                track_total_hits: true,
                _source: expect.any(Object),
                query: expect.any(Object),
                from: input.paging?.offset,
                size: input.paging?.limit,
                sort: [
                  "_score",
                  {
                    _script: {
                      type: "number",
                      order: "asc",
                      script: {
                        lang: "painless",
                        source:
                          "\n            long ts = doc['filters.start_date'].value.toInstant().toEpochMilli(); \n            long now = params.now; \n            return ts >= now ? 0 : 1;\n          ",
                        params: {
                          now: expect.any(Number)
                        }
                      }
                    }
                  },
                  {
                    _script: {
                      type: "number",
                      order: "asc",
                      script: {
                        lang: "painless",
                        source:
                          "\n            long ts = doc['filters.end_date'].value.toInstant().toEpochMilli(); \n            long now = params.now;\n            if (ts >= now) {\n              // future: sort by ts ↑ (earliest future first)\n              return ts;\n            } else {\n              // past: return negative ts so that more‐recent past (larger ts)\n              // becomes more‐negative → appears earlier when ordering ↑\n              return -ts;\n            }\n          ",
                        params: {
                          now: expect.any(Number)
                        }
                      }
                    }
                  },
                  {
                    speaker_count: "desc"
                  }
                ]
              })
            );
          });
        });
      });
    });
  });

  describe("congressCountForIndications()", () => {
    it("should return counts for each indication", async () => {
      const input: CongressSearchInput = {
        query: faker.datatype.string(),
        projectId: faker.datatype.string(),
        userId: faker.datatype.string()
      };

      const indications = [faker.datatype.string(), faker.datatype.string()];
      const counts = [faker.datatype.number(), faker.datatype.number()];

      const configService = createMockInstance(ConfigService);
      configService.elasticCongressIndex = faker.datatype.string();

      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      mockQueryUnderstandingServerResponse(input.query!, false);
      const qusWithIndicationIntent =
        new QueryUnderstandingServiceResponseForIndications();
      queryUnderstandingServiceClient.getIndicationSynonymsAndIcdCodes.mockResolvedValue(
        qusWithIndicationIntent
      );

      const elasticSearchService = createMockInstance(
        ElasticSearchCongressService
      );
      elasticSearchService.mget.mockResolvedValue({ docs: [] });
      elasticSearchService.query.mockResolvedValue(
        generateMockElasticsearchResponseWithAggregations()
      );
      elasticSearchService.msearch.mockResolvedValue({
        responses: [
          {
            hits: {
              total: {
                value: counts[0]
              }
            }
          },
          {
            hits: {
              total: {
                value: counts[1]
              }
            }
          }
        ]
      } as MsearchResponse);

      const indicationTreeSearchService = createMockInstance(
        IndicationsTreeSearchService
      );

      const congressSearchResponseAdapterService = createMockInstance(
        CongressSearchResponseAdapterService
      );

      const calculateMinimumNameVariations = createMockInstance(
        CalculateMinimumNameVariations
      );

      const userOnboardingDataService = createMockInstance(
        UserOnboardingDataService
      );
      userOnboardingDataService.getOnboardingData.mockResolvedValue(
        generateMockOnboardingData()
      );

      const nameSearchBuilderFactory = createMockInstance(
        NameSearchBuilderFactory
      );

      nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
        DefaultNameSearchBuilder.getInstance()
      );

      const followRecordClient = createMockInstance(FollowRecordResourceClient);
      followRecordClient.getPaginatedFollowRecordIds.mockResolvedValue({
        total: 0,
        items: []
      });

      const languageDetectService = createMockInstance(LanguageDetectService);

      const languageDetector = () => {
        return faker.helpers.arrayElement([
          ENGLISH,
          CHINESE,
          JAPANESE
        ]) as Language;
      };
      languageDetectService.getLanguageDetector.mockReturnValue(
        languageDetector
      );

      const featureFlagsService = createMockInstance(
        FeatureFlagsService as any
      ) as FeatureFlagsService;
      featureFlagsService.getAllFlags = jest
        .fn()
        .mockResolvedValue(generateFeatureFlagsState());

      const userClient = createMockInstance(UserResourceClient);

      const tagsHelperService = createMockInstance(TagsHelperService);
      tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);

      const congressSearchResourceService = new CongressSearchResourceService(
        configService,
        congressSearchResponseAdapterService,
        queryUnderstandingServiceClient,
        calculateMinimumNameVariations,
        indicationTreeSearchService,
        elasticSearchService,
        userOnboardingDataService,
        nameSearchBuilderFactory,
        elasticSearchService,
        followRecordClient,
        languageDetectService,
        featureFlagsService,
        tagsHelperService,
        userClient
      );

      const result =
        await congressSearchResourceService.congressCountForIndications(
          input,
          indications
        );

      expect(result).toEqual({
        [indications[0]]: counts[0],
        [indications[1]]: counts[1]
      });
    });
  });

  describe("autocompleteCongressTypes", () => {
    describe("elasticsearch query building", () => {
      describe("input query", () => {
        it("should include should clauses for each of the main search paths", async () => {
          const input: CongressSearchFilterAutocompleteInput = {
            globalQuery: faker.datatype.string(),
            filterQuery: faker.datatype.string(),
            userId: faker.datatype.string(),
            projectId: faker.datatype.string(),
            count: faker.datatype.number()
          };

          const configService = createMockInstance(ConfigService);
          configService.elasticCongressIndex = faker.datatype.string();

          const queryUnderstandingServiceClient = createMockInstance(
            QueryUnderstandingServiceClient
          );
          const queryUnderstandingServerResponse =
            mockQueryUnderstandingServerResponse(input.globalQuery!, false);
          queryUnderstandingServiceClient.analyze.mockResolvedValue(
            queryUnderstandingServerResponse
          );

          const elasticSearchCongressService = createMockInstance(
            ElasticSearchCongressService
          );
          elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
          elasticSearchCongressService.query.mockResolvedValue(
            generateMockElasticsearchResponseWithAggregations()
          );

          const indicationsTreeSearchService = createMockInstance(
            IndicationsTreeSearchService
          );

          const congressSearchResponseAdapterService = createMockInstance(
            CongressSearchResponseAdapterService
          );

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );

          const nameSearchBuilderFactory = createMockInstance(
            NameSearchBuilderFactory
          );

          nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
            DefaultNameSearchBuilder.getInstance()
          );

          const matchingPeople = Array.from({ length: 10 }, () =>
            generateMockPersonHit()
          );
          const elasticSearchPeopleService =
            createMockInstance(ElasticSearchService);
          elasticSearchPeopleService.query.mockResolvedValue(
            generateMockElasticsearchResponse(matchingPeople)
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );

          const languageDetector = () => {
            return faker.helpers.arrayElement([
              ENGLISH,
              CHINESE,
              JAPANESE
            ]) as Language;
          };
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );

          const featureFlagsService = createMockInstance(
            FeatureFlagsService as any
          ) as FeatureFlagsService;
          featureFlagsService.getAllFlags = jest
            .fn()
            .mockResolvedValue(generateFeatureFlagsState());

          const userClient = createMockInstance(UserResourceClient);
          const followRecordClient = createMockInstance(
            FollowRecordResourceClient
          );

          const tagsHelperService = createMockInstance(TagsHelperService);
          tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);

          const hashedValue = faker.datatype.string();
          objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);

          redis = new Redis({
            data: {
              [hashedValue]: "[]"
            }
          });

          const congressSearchResourceService =
            new CongressSearchResourceService(
              configService,
              congressSearchResponseAdapterService,
              queryUnderstandingServiceClient,
              calculateMinimumNameVariations,
              indicationsTreeSearchService,
              elasticSearchCongressService,
              userOnboardingDataService,
              nameSearchBuilderFactory,
              elasticSearchPeopleService,
              followRecordClient,
              languageDetectService,
              featureFlagsService,
              tagsHelperService,
              userClient
            );

          await congressSearchResourceService.autocompleteCongressTypes(input);

          expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              index: configService.elasticCongressIndex,
              track_total_hits: false,
              _source: false,
              size: 0,
              query: {
                bool: {
                  filter: [],
                  minimum_should_match: 0,
                  must: {
                    bool: {
                      should: [
                        {
                          simple_query_string: {
                            default_operator: "and",
                            fields: [
                              "name^10",

                              "series_name^10",
                              "society",
                              "indication"
                            ],
                            query: input.globalQuery
                          }
                        }
                      ],
                      must: [
                        {
                          match_phrase: {
                            "congress_type.autocomplete_search": {
                              query: input.filterQuery
                            }
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  }
                }
              },
              aggs: {
                type: {
                  aggs: {
                    filtered_matching: {
                      terms: {
                        field: "filters.congress_type",
                        shard_size: 100,
                        size: input.count
                      }
                    }
                  },
                  filter: {
                    bool: {
                      filter: [
                        {
                          match_phrase: {
                            "congress_type.autocomplete_search": {
                              query: input.filterQuery
                            }
                          }
                        }
                      ]
                    }
                  }
                }
              }
            })
          );
        });

        describe("filters", () => {
          it("should include filters in elasticsearch request when defined in input", async () => {
            const input: CongressSearchFilterAutocompleteInput = {
              globalQuery: faker.datatype.string(),
              filterQuery: faker.datatype.string(),
              userId: faker.datatype.string(),
              projectId: faker.datatype.string(),
              count: faker.datatype.number(),
              filters: {
                location: {
                  countries: [
                    faker.address.countryCode(),
                    faker.address.countryCode()
                  ],
                  excludeCountries: [
                    faker.address.countryCode(),
                    faker.address.countryCode()
                  ],
                  regions: [
                    faker.address.stateAbbr(),
                    faker.address.stateAbbr()
                  ],
                  excludeRegions: [
                    faker.address.stateAbbr(),
                    faker.address.stateAbbr()
                  ],
                  cities: [faker.address.city(), faker.address.city()],
                  excludeCities: [faker.address.city(), faker.address.city()],
                  postalCodes: [
                    faker.address.zipCode(),
                    faker.address.zipCode()
                  ],
                  excludePostalCodes: [
                    faker.address.zipCode(),
                    faker.address.zipCode()
                  ],
                  isVirtual: true
                },
                dateRange: {
                  min: faker.datatype.number(),
                  max: faker.datatype.number()
                },
                type: {
                  congressTypes: [
                    faker.datatype.string(),
                    faker.datatype.string()
                  ]
                },
                society: {
                  societyNames: [
                    faker.datatype.string(),
                    faker.datatype.string()
                  ]
                },
                indication: {
                  indications: [
                    faker.datatype.string(),
                    faker.datatype.string()
                  ]
                },
                series: {
                  seriesNames: [
                    faker.datatype.string(),
                    faker.datatype.string()
                  ]
                }
              }
            };

            const configService = createMockInstance(ConfigService);
            configService.elasticCongressIndex = faker.datatype.string();

            const queryUnderstandingServiceClient = createMockInstance(
              QueryUnderstandingServiceClient
            );
            const queryUnderstandingServerResponse =
              mockQueryUnderstandingServerResponse(input.globalQuery!, false);

            const quResponseIndiations: QueryUnderstandingServiceResponseForIndications =
              jest.createMockFromModule(
                "../proto/query_understanding_service_pb"
              );
            const indicationsParsedQuery = faker.datatype.string();
            quResponseIndiations.getIndicationsParsedQuery = jest.fn(
              () => indicationsParsedQuery
            );
            quResponseIndiations.getIndicationsIcdCodesQuery = jest.fn(() =>
              faker.datatype.string()
            );
            queryUnderstandingServiceClient.getIndicationSynonymsAndIcdCodes.mockResolvedValue(
              quResponseIndiations
            );
            queryUnderstandingServiceClient.analyze.mockResolvedValue(
              queryUnderstandingServerResponse
            );

            const elasticSearchCongressService = createMockInstance(
              ElasticSearchCongressService
            );
            elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
            elasticSearchCongressService.query.mockResolvedValue(
              generateMockElasticsearchResponseWithAggregations()
            );

            const indicationsTreeSearchService = createMockInstance(
              IndicationsTreeSearchService
            );
            const mockIndications: IndicationNode[] = [
              {
                id: faker.datatype.string(),
                indicationName: faker.datatype.string(),
                h1Id: "",
                parentH1Ids: [],
                indicationType: IndicationType.L1,
                matchedSynonyms: [],
                icdCodes: [],
                children: []
              }
            ];
            indicationsTreeSearchService.searchIndicationsByQuery.mockResolvedValue(
              mockIndications
            );

            const congressSearchResponseAdapterService = createMockInstance(
              CongressSearchResponseAdapterService
            );

            const calculateMinimumNameVariations = createMockInstance(
              CalculateMinimumNameVariations
            );

            const userOnboardingDataService = createMockInstance(
              UserOnboardingDataService
            );
            userOnboardingDataService.getOnboardingData.mockResolvedValue(
              generateMockOnboardingData()
            );

            const nameSearchBuilderFactory = createMockInstance(
              NameSearchBuilderFactory
            );

            nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
              DefaultNameSearchBuilder.getInstance()
            );

            const matchingPeople = Array.from({ length: 10 }, () =>
              generateMockPersonHit()
            );
            const elasticSearchPeopleService =
              createMockInstance(ElasticSearchService);
            elasticSearchPeopleService.query.mockResolvedValue(
              generateMockElasticsearchResponse(matchingPeople)
            );

            const languageDetectService = createMockInstance(
              LanguageDetectService
            );

            const languageDetector = () => {
              return faker.helpers.arrayElement([
                ENGLISH,
                CHINESE,
                JAPANESE
              ]) as Language;
            };
            languageDetectService.getLanguageDetector.mockReturnValue(
              languageDetector
            );

            const featureFlagsService = createMockInstance(
              FeatureFlagsService as any
            ) as FeatureFlagsService;
            featureFlagsService.getAllFlags = jest
              .fn()
              .mockResolvedValue(generateFeatureFlagsState());

            const userClient = createMockInstance(UserResourceClient);
            const followRecordClient = createMockInstance(
              FollowRecordResourceClient
            );

            const tagsHelperService = createMockInstance(TagsHelperService);
            tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);

            const congressSearchResourceService =
              new CongressSearchResourceService(
                configService,
                congressSearchResponseAdapterService,
                queryUnderstandingServiceClient,
                calculateMinimumNameVariations,
                indicationsTreeSearchService,
                elasticSearchCongressService,
                userOnboardingDataService,
                nameSearchBuilderFactory,
                elasticSearchPeopleService,
                followRecordClient,
                languageDetectService,
                featureFlagsService,
                tagsHelperService,
                userClient
              );

            await congressSearchResourceService.autocompleteCongressTypes(
              input
            );

            expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
              expect.objectContaining({
                index: configService.elasticCongressIndex,
                track_total_hits: false,
                _source: false,
                size: 0,
                query: {
                  bool: {
                    filter: [
                      {
                        bool: {
                          should: [
                            {
                              terms: {
                                "filters.country":
                                  input?.filters?.location?.countries
                              }
                            },
                            {
                              terms: {
                                "filters.country_level_regions":
                                  input?.filters?.location?.countries
                              }
                            }
                          ]
                        }
                      },
                      {
                        bool: {
                          must_not: [
                            {
                              terms: {
                                "filters.country":
                                  input?.filters?.location?.excludeCountries
                              }
                            },
                            {
                              terms: {
                                "filters.country_level_regions":
                                  input?.filters?.location?.excludeCountries
                              }
                            }
                          ]
                        }
                      },
                      {
                        bool: {
                          should: [
                            {
                              terms: {
                                "filters.region":
                                  input?.filters?.location?.regions
                              }
                            },
                            {
                              terms: {
                                "filters.state_level_regions":
                                  input?.filters?.location?.regions
                              }
                            }
                          ]
                        }
                      },
                      {
                        bool: {
                          must_not: [
                            {
                              terms: {
                                "filters.region":
                                  input?.filters?.location?.excludeRegions
                              }
                            },
                            {
                              terms: {
                                "filters.state_level_regions":
                                  input?.filters?.location?.excludeRegions
                              }
                            }
                          ]
                        }
                      },
                      {
                        bool: {
                          should: [
                            {
                              terms: {
                                "filters.city": input?.filters?.location?.cities
                              }
                            },
                            {
                              terms: {
                                "filters.city_level_regions":
                                  input?.filters?.location?.cities
                              }
                            }
                          ]
                        }
                      },
                      {
                        bool: {
                          must_not: [
                            {
                              terms: {
                                "filters.city":
                                  input?.filters?.location?.excludeCities
                              }
                            },
                            {
                              terms: {
                                "filters.city_level_regions":
                                  input?.filters?.location?.excludeCities
                              }
                            }
                          ]
                        }
                      },
                      {
                        regexp: {
                          "filters.postal_code": {
                            value: `.+\\|(${input?.filters?.location?.postalCodes
                              ?.filter((value) => !!value)
                              .join("|")})`
                          }
                        }
                      },
                      {
                        bool: {
                          must_not: {
                            regexp: {
                              "filters.postal_code": {
                                value: `.+\\|(${input?.filters?.location?.excludePostalCodes
                                  ?.filter((value) => !!value)
                                  .join("|")})`
                              }
                            }
                          }
                        }
                      },
                      {
                        term: {
                          "filters.is_virtual": true
                        }
                      },
                      {
                        terms: {
                          "filters.indication":
                            input.filters?.indication?.indications
                        }
                      },
                      {
                        range: {
                          "filters.end_date": {
                            gte: input.filters?.dateRange?.min
                          }
                        }
                      },
                      {
                        range: {
                          "filters.start_date": {
                            lte: input.filters?.dateRange?.max
                          }
                        }
                      },
                      {
                        terms: {
                          "filters.series": input.filters?.series?.seriesNames
                        }
                      },
                      {
                        terms: {
                          "filters.society":
                            input.filters?.society?.societyNames
                        }
                      }
                    ],
                    minimum_should_match: 0,
                    must: expect.any(Object)
                  }
                },
                aggs: expect.anything()
              })
            );
          });
        });
      });
    });
  });

  describe("autocompleteSocietyNames", () => {
    describe("elasticsearch query building", () => {
      describe("input query", () => {
        it("should include should clauses for each of the main search paths", async () => {
          const input: CongressSearchFilterAutocompleteInput = {
            globalQuery: faker.datatype.string(),
            filterQuery: faker.datatype.string(),
            userId: faker.datatype.string(),
            projectId: faker.datatype.string(),
            count: faker.datatype.number()
          };

          const configService = createMockInstance(ConfigService);
          configService.elasticCongressIndex = faker.datatype.string();

          const queryUnderstandingServiceClient = createMockInstance(
            QueryUnderstandingServiceClient
          );
          const queryUnderstandingServerResponse =
            mockQueryUnderstandingServerResponse(input.globalQuery!, false);
          queryUnderstandingServiceClient.analyze.mockResolvedValue(
            queryUnderstandingServerResponse
          );

          const elasticSearchCongressService = createMockInstance(
            ElasticSearchCongressService
          );
          elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
          elasticSearchCongressService.query.mockResolvedValue(
            generateMockElasticsearchResponseWithAggregations()
          );

          const indicationsTreeSearchService = createMockInstance(
            IndicationsTreeSearchService
          );

          const congressSearchResponseAdapterService = createMockInstance(
            CongressSearchResponseAdapterService
          );

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );

          const nameSearchBuilderFactory = createMockInstance(
            NameSearchBuilderFactory
          );

          nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
            DefaultNameSearchBuilder.getInstance()
          );

          const matchingPeople = Array.from({ length: 10 }, () =>
            generateMockPersonHit()
          );
          const elasticSearchPeopleService =
            createMockInstance(ElasticSearchService);
          elasticSearchPeopleService.query.mockResolvedValue(
            generateMockElasticsearchResponse(matchingPeople)
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );

          const languageDetector = () => {
            return faker.helpers.arrayElement([
              ENGLISH,
              CHINESE,
              JAPANESE
            ]) as Language;
          };
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );
          const featureFlagsService = createMockInstance(
            FeatureFlagsService as any
          ) as FeatureFlagsService;
          featureFlagsService.getAllFlags = jest
            .fn()
            .mockResolvedValue(generateFeatureFlagsState());
          const userClient = createMockInstance(UserResourceClient);
          const followRecordClient = createMockInstance(
            FollowRecordResourceClient
          );
          const tagsHelperService = createMockInstance(TagsHelperService);
          tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);
          const hashedValue = faker.datatype.string();
          objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);
          redis = new Redis({
            data: {
              [hashedValue]: "[]"
            }
          });
          const congressSearchResourceService =
            new CongressSearchResourceService(
              configService,
              congressSearchResponseAdapterService,
              queryUnderstandingServiceClient,
              calculateMinimumNameVariations,
              indicationsTreeSearchService,
              elasticSearchCongressService,
              userOnboardingDataService,
              nameSearchBuilderFactory,
              elasticSearchPeopleService,
              followRecordClient,
              languageDetectService,
              featureFlagsService,
              tagsHelperService,
              userClient
            );
          await congressSearchResourceService.autocompleteSocietyNames(input);
          expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              index: configService.elasticCongressIndex,
              track_total_hits: false,
              _source: false,
              size: 0,
              query: {
                bool: {
                  filter: [],
                  minimum_should_match: 0,
                  must: {
                    bool: {
                      should: [
                        {
                          simple_query_string: {
                            default_operator: "and",
                            fields: [
                              "name^10",

                              "series_name^10",
                              "society",
                              "indication"
                            ],
                            query: input.globalQuery
                          }
                        }
                      ],
                      must: [
                        {
                          match_phrase: {
                            "society.autocomplete_search": {
                              query: input.filterQuery
                            }
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  }
                }
              },
              aggs: {
                society: {
                  aggs: {
                    filtered_matching: {
                      terms: {
                        field: "filters.society",
                        shard_size: 100,
                        size: input.count
                      }
                    }
                  },
                  filter: {
                    bool: {
                      filter: [
                        {
                          match_phrase: {
                            "society.autocomplete_search": {
                              query: input.filterQuery
                            }
                          }
                        }
                      ]
                    }
                  }
                }
              }
            })
          );
        });
      });
    });
  });

  describe("autocompleteSeriesNames", () => {
    describe("elasticsearch query building", () => {
      describe("input query", () => {
        it("should include should clauses for each of the main search paths", async () => {
          const input: CongressSearchFilterAutocompleteInput = {
            globalQuery: faker.datatype.string(),
            filterQuery: faker.datatype.string(),
            userId: faker.datatype.string(),
            projectId: faker.datatype.string(),
            count: faker.datatype.number()
          };

          const configService = createMockInstance(ConfigService);
          configService.elasticCongressIndex = faker.datatype.string();

          const queryUnderstandingServiceClient = createMockInstance(
            QueryUnderstandingServiceClient
          );
          const queryUnderstandingServerResponse =
            mockQueryUnderstandingServerResponse(input.globalQuery!, false);
          queryUnderstandingServiceClient.analyze.mockResolvedValue(
            queryUnderstandingServerResponse
          );

          const elasticSearchCongressService = createMockInstance(
            ElasticSearchCongressService
          );
          elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
          elasticSearchCongressService.query.mockResolvedValue(
            generateMockElasticsearchResponseWithAggregations()
          );

          const indicationsTreeSearchService = createMockInstance(
            IndicationsTreeSearchService
          );

          const congressSearchResponseAdapterService = createMockInstance(
            CongressSearchResponseAdapterService
          );

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );

          const nameSearchBuilderFactory = createMockInstance(
            NameSearchBuilderFactory
          );

          nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
            DefaultNameSearchBuilder.getInstance()
          );

          const matchingPeople = Array.from({ length: 10 }, () =>
            generateMockPersonHit()
          );
          const elasticSearchPeopleService =
            createMockInstance(ElasticSearchService);
          elasticSearchPeopleService.query.mockResolvedValue(
            generateMockElasticsearchResponse(matchingPeople)
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );

          const languageDetector = () => {
            return faker.helpers.arrayElement([
              ENGLISH,
              CHINESE,
              JAPANESE
            ]) as Language;
          };
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );
          const featureFlagsService = createMockInstance(
            FeatureFlagsService as any
          ) as FeatureFlagsService;
          featureFlagsService.getAllFlags = jest
            .fn()
            .mockResolvedValue(generateFeatureFlagsState());
          const userClient = createMockInstance(UserResourceClient);
          const followRecordClient = createMockInstance(
            FollowRecordResourceClient
          );
          const tagsHelperService = createMockInstance(TagsHelperService);
          tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);
          const hashedValue = faker.datatype.string();
          objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);
          redis = new Redis({
            data: {
              [hashedValue]: "[]"
            }
          });
          const congressSearchResourceService =
            new CongressSearchResourceService(
              configService,
              congressSearchResponseAdapterService,
              queryUnderstandingServiceClient,
              calculateMinimumNameVariations,
              indicationsTreeSearchService,
              elasticSearchCongressService,
              userOnboardingDataService,
              nameSearchBuilderFactory,
              elasticSearchPeopleService,
              followRecordClient,
              languageDetectService,
              featureFlagsService,
              tagsHelperService,
              userClient
            );
          await congressSearchResourceService.autocompleteSeriesNames(input);
          expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              index: configService.elasticCongressIndex,
              track_total_hits: false,
              _source: false,
              size: 0,
              query: {
                bool: {
                  filter: [],
                  minimum_should_match: 0,
                  must: {
                    bool: {
                      should: [
                        {
                          simple_query_string: {
                            default_operator: "and",
                            fields: [
                              "name^10",

                              "series_name^10",
                              "society",
                              "indication"
                            ],
                            query: input.globalQuery
                          }
                        }
                      ],
                      must: [
                        {
                          match_phrase: {
                            "series_name.autocomplete_search": {
                              query: input.filterQuery
                            }
                          }
                        }
                      ],
                      minimum_should_match: 1
                    }
                  }
                }
              },
              aggs: {
                series: {
                  aggs: {
                    filtered_matching: {
                      terms: {
                        field: "filters.series",
                        shard_size: 100,
                        size: input.count
                      }
                    }
                  },
                  filter: {
                    bool: {
                      filter: [
                        {
                          match_phrase: {
                            "series_name.autocomplete_search": {
                              query: input.filterQuery
                            }
                          }
                        }
                      ]
                    }
                  }
                }
              }
            })
          );
        });
      });
    });
  });

  describe("autocompletePostalCodes", () => {
    describe("elasticsearch query building", () => {
      describe("input query", () => {
        it("should include should clauses for each of the main search paths", async () => {
          const input: CongressSearchFilterAutocompleteInput = {
            globalQuery: faker.datatype.string(),
            filterQuery: faker.datatype.string(),
            userId: faker.datatype.string(),
            projectId: faker.datatype.string(),
            count: faker.datatype.number()
          };

          const configService = createMockInstance(ConfigService);
          configService.elasticCongressIndex = faker.datatype.string();

          const queryUnderstandingServiceClient = createMockInstance(
            QueryUnderstandingServiceClient
          );
          const queryUnderstandingServerResponse =
            mockQueryUnderstandingServerResponse(input.globalQuery!, false);
          queryUnderstandingServiceClient.analyze.mockResolvedValue(
            queryUnderstandingServerResponse
          );

          const elasticSearchCongressService = createMockInstance(
            ElasticSearchCongressService
          );
          elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
          elasticSearchCongressService.query.mockResolvedValue(
            generateMockElasticsearchResponseWithAggregations()
          );

          const indicationsTreeSearchService = createMockInstance(
            IndicationsTreeSearchService
          );

          const congressSearchResponseAdapterService = createMockInstance(
            CongressSearchResponseAdapterService
          );

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );

          const nameSearchBuilderFactory = createMockInstance(
            NameSearchBuilderFactory
          );

          nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
            DefaultNameSearchBuilder.getInstance()
          );

          const matchingPeople = Array.from({ length: 10 }, () =>
            generateMockPersonHit()
          );
          const elasticSearchPeopleService =
            createMockInstance(ElasticSearchService);
          elasticSearchPeopleService.query.mockResolvedValue(
            generateMockElasticsearchResponse(matchingPeople)
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );

          const languageDetector = () => {
            return faker.helpers.arrayElement([
              ENGLISH,
              CHINESE,
              JAPANESE
            ]) as Language;
          };
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );
          const featureFlagsService = createMockInstance(
            FeatureFlagsService as any
          ) as FeatureFlagsService;
          featureFlagsService.getAllFlags = jest
            .fn()
            .mockResolvedValue(generateFeatureFlagsState());
          const userClient = createMockInstance(UserResourceClient);
          const followRecordClient = createMockInstance(
            FollowRecordResourceClient
          );
          const tagsHelperService = createMockInstance(TagsHelperService);
          tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);
          const hashedValue = faker.datatype.string();
          objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);
          redis = new Redis({
            data: {
              [hashedValue]: "[]"
            }
          });
          const congressSearchResourceService =
            new CongressSearchResourceService(
              configService,
              congressSearchResponseAdapterService,
              queryUnderstandingServiceClient,
              calculateMinimumNameVariations,
              indicationsTreeSearchService,
              elasticSearchCongressService,
              userOnboardingDataService,
              nameSearchBuilderFactory,
              elasticSearchPeopleService,
              followRecordClient,
              languageDetectService,
              featureFlagsService,
              tagsHelperService,
              userClient
            );

          await congressSearchResourceService.autocompletePostalCodes(input);

          expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              index: configService.elasticCongressIndex,
              track_total_hits: false,
              _source: false,
              query: {
                bool: {
                  minimum_should_match: 0,
                  filter: [],
                  must: {
                    bool: {
                      should: [
                        {
                          simple_query_string: {
                            query: input.globalQuery,
                            fields: [
                              "name^10",

                              "series_name^10",
                              "society",
                              "indication"
                            ],
                            default_operator: "and"
                          }
                        }
                      ],
                      minimum_should_match: 1,
                      must: [
                        {
                          nested: {
                            path: "addresses",
                            query: {
                              multi_match: {
                                type: "phrase_prefix",
                                query: input.filterQuery,
                                fields: [
                                  "addresses.postal_code",
                                  "addresses.city_level_regions"
                                ]
                              }
                            }
                          }
                        }
                      ]
                    }
                  }
                }
              },
              size: 0,
              aggs: {
                postal_code: {
                  filter: {
                    bool: {
                      filter: [
                        {
                          nested: {
                            path: "addresses",
                            query: {
                              multi_match: {
                                type: "phrase_prefix",
                                query: input.filterQuery,
                                fields: ["addresses.postal_code"]
                              }
                            }
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    filtered_matching: {
                      terms: {
                        field: "filters.postal_code",
                        size: input.count,
                        shard_size: 100
                      },
                      aggs: {
                        regions_in: {
                          terms: {
                            field: "filters.city_level_regions",
                            exclude: "",
                            shard_size: 100
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          );
        });
      });
    });
  });

  describe("autocompleteCities", () => {
    describe("elasticsearch query building", () => {
      describe("input query", () => {
        it("should include should clauses for each of the main search paths", async () => {
          const input: CongressSearchFilterAutocompleteInput = {
            globalQuery: faker.datatype.string(),
            filterQuery: faker.datatype.string(),
            userId: faker.datatype.string(),
            projectId: faker.datatype.string(),
            count: faker.datatype.number()
          };

          const configService = createMockInstance(ConfigService);
          configService.elasticCongressIndex = faker.datatype.string();

          const queryUnderstandingServiceClient = createMockInstance(
            QueryUnderstandingServiceClient
          );
          const queryUnderstandingServerResponse =
            mockQueryUnderstandingServerResponse(input.globalQuery!, false);
          queryUnderstandingServiceClient.analyze.mockResolvedValue(
            queryUnderstandingServerResponse
          );

          const elasticSearchCongressService = createMockInstance(
            ElasticSearchCongressService
          );
          elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
          elasticSearchCongressService.query.mockResolvedValue(
            generateMockElasticsearchResponseWithAggregations()
          );

          const indicationsTreeSearchService = createMockInstance(
            IndicationsTreeSearchService
          );

          const congressSearchResponseAdapterService = createMockInstance(
            CongressSearchResponseAdapterService
          );

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );

          const nameSearchBuilderFactory = createMockInstance(
            NameSearchBuilderFactory
          );

          nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
            DefaultNameSearchBuilder.getInstance()
          );

          const matchingPeople = Array.from({ length: 10 }, () =>
            generateMockPersonHit()
          );
          const elasticSearchPeopleService =
            createMockInstance(ElasticSearchService);
          elasticSearchPeopleService.query.mockResolvedValue(
            generateMockElasticsearchResponse(matchingPeople)
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );

          const languageDetector = () => {
            return faker.helpers.arrayElement([
              ENGLISH,
              CHINESE,
              JAPANESE
            ]) as Language;
          };
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );
          const featureFlagsService = createMockInstance(
            FeatureFlagsService as any
          ) as FeatureFlagsService;
          featureFlagsService.getAllFlags = jest
            .fn()
            .mockResolvedValue(generateFeatureFlagsState());
          const userClient = createMockInstance(UserResourceClient);
          const followRecordClient = createMockInstance(
            FollowRecordResourceClient
          );
          const tagsHelperService = createMockInstance(TagsHelperService);
          tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);
          const hashedValue = faker.datatype.string();
          objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);
          redis = new Redis({
            data: {
              [hashedValue]: "[]"
            }
          });
          const congressSearchResourceService =
            new CongressSearchResourceService(
              configService,
              congressSearchResponseAdapterService,
              queryUnderstandingServiceClient,
              calculateMinimumNameVariations,
              indicationsTreeSearchService,
              elasticSearchCongressService,
              userOnboardingDataService,
              nameSearchBuilderFactory,
              elasticSearchPeopleService,
              followRecordClient,
              languageDetectService,
              featureFlagsService,
              tagsHelperService,
              userClient
            );

          await congressSearchResourceService.autocompleteCities(input);

          expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              index: configService.elasticCongressIndex,
              track_total_hits: false,
              _source: false,
              query: {
                bool: {
                  minimum_should_match: 0,
                  filter: [],
                  must: {
                    bool: {
                      should: [
                        {
                          simple_query_string: {
                            query: input.globalQuery,
                            fields: [
                              "name^10",

                              "series_name^10",
                              "society",
                              "indication"
                            ],
                            default_operator: "and"
                          }
                        }
                      ],
                      minimum_should_match: 1,
                      must: [
                        {
                          nested: {
                            path: "addresses",
                            query: {
                              multi_match: {
                                type: "phrase_prefix",
                                query: input.filterQuery,
                                fields: [
                                  "addresses.city",
                                  "addresses.city_level_regions"
                                ]
                              }
                            }
                          }
                        }
                      ]
                    }
                  }
                }
              },
              size: 0,
              aggs: {
                city: {
                  filter: {
                    bool: {
                      filter: [
                        {
                          nested: {
                            path: "addresses",
                            query: {
                              multi_match: {
                                type: "phrase_prefix",
                                query: input.filterQuery,
                                fields: ["addresses.city"]
                              }
                            }
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    filtered_matching: {
                      terms: {
                        field: "filters.city",
                        size: input.count,
                        shard_size: 100
                      },
                      aggs: {
                        regions_in: {
                          terms: {
                            field: "filters.city_level_regions",
                            exclude: "",
                            shard_size: 100
                          }
                        }
                      }
                    }
                  }
                },
                city_level_regions: {
                  terms: {
                    field: "filters.city_level_regions",
                    exclude: "",
                    include: expect.any(String),
                    shard_size: 100
                  },
                  aggs: {
                    locations_in_region: {
                      terms: {
                        field: "filters.city",
                        size: 200
                      }
                    }
                  }
                }
              }
            })
          );
        });
      });
    });
  });

  describe("autocompleteRegions", () => {
    describe("elasticsearch query building", () => {
      describe("input query", () => {
        it("should include should clauses for each of the main search paths", async () => {
          const input: CongressSearchFilterAutocompleteInput = {
            globalQuery: faker.datatype.string(),
            filterQuery: faker.datatype.string(),
            userId: faker.datatype.string(),
            projectId: faker.datatype.string(),
            count: faker.datatype.number()
          };

          const configService = createMockInstance(ConfigService);
          configService.elasticCongressIndex = faker.datatype.string();

          const queryUnderstandingServiceClient = createMockInstance(
            QueryUnderstandingServiceClient
          );
          const queryUnderstandingServerResponse =
            mockQueryUnderstandingServerResponse(input.globalQuery!, false);
          queryUnderstandingServiceClient.analyze.mockResolvedValue(
            queryUnderstandingServerResponse
          );

          const elasticSearchCongressService = createMockInstance(
            ElasticSearchCongressService
          );
          elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
          elasticSearchCongressService.query.mockResolvedValue(
            generateMockElasticsearchResponseWithAggregations()
          );

          const indicationsTreeSearchService = createMockInstance(
            IndicationsTreeSearchService
          );

          const congressSearchResponseAdapterService = createMockInstance(
            CongressSearchResponseAdapterService
          );

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );

          const nameSearchBuilderFactory = createMockInstance(
            NameSearchBuilderFactory
          );

          nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
            DefaultNameSearchBuilder.getInstance()
          );

          const matchingPeople = Array.from({ length: 10 }, () =>
            generateMockPersonHit()
          );
          const elasticSearchPeopleService =
            createMockInstance(ElasticSearchService);
          elasticSearchPeopleService.query.mockResolvedValue(
            generateMockElasticsearchResponse(matchingPeople)
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );

          const languageDetector = () => {
            return faker.helpers.arrayElement([
              ENGLISH,
              CHINESE,
              JAPANESE
            ]) as Language;
          };
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );
          const featureFlagsService = createMockInstance(
            FeatureFlagsService as any
          ) as FeatureFlagsService;
          featureFlagsService.getAllFlags = jest
            .fn()
            .mockResolvedValue(generateFeatureFlagsState());
          const userClient = createMockInstance(UserResourceClient);
          const followRecordClient = createMockInstance(
            FollowRecordResourceClient
          );
          const tagsHelperService = createMockInstance(TagsHelperService);
          tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);
          const hashedValue = faker.datatype.string();
          objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);
          redis = new Redis({
            data: {
              [hashedValue]: "[]"
            }
          });
          const congressSearchResourceService =
            new CongressSearchResourceService(
              configService,
              congressSearchResponseAdapterService,
              queryUnderstandingServiceClient,
              calculateMinimumNameVariations,
              indicationsTreeSearchService,
              elasticSearchCongressService,
              userOnboardingDataService,
              nameSearchBuilderFactory,
              elasticSearchPeopleService,
              followRecordClient,
              languageDetectService,
              featureFlagsService,
              tagsHelperService,
              userClient
            );

          await congressSearchResourceService.autocompleteRegions(input);

          expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              index: configService.elasticCongressIndex,
              track_total_hits: false,
              _source: false,
              query: {
                bool: {
                  minimum_should_match: 0,
                  filter: [],
                  must: {
                    bool: {
                      should: [
                        {
                          simple_query_string: {
                            query: input.globalQuery,
                            fields: [
                              "name^10",
                              "series_name^10",
                              "society",
                              "indication"
                            ],
                            default_operator: "and"
                          }
                        }
                      ],
                      minimum_should_match: 1,
                      must: [
                        {
                          nested: {
                            path: "addresses",
                            query: {
                              multi_match: {
                                type: "phrase_prefix",
                                query: input.filterQuery,
                                fields: [
                                  "addresses.region",
                                  "addresses.region_code",
                                  "addresses.state_level_regions"
                                ]
                              }
                            }
                          }
                        }
                      ]
                    }
                  }
                }
              },
              size: 0,
              aggs: {
                region: {
                  filter: {
                    bool: {
                      filter: [
                        {
                          nested: {
                            path: "addresses",
                            query: {
                              multi_match: {
                                type: "phrase_prefix",
                                query: input.filterQuery,
                                fields: [
                                  "addresses.region",
                                  "addresses.region_code"
                                ]
                              }
                            }
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    filtered_matching: {
                      terms: {
                        field: "filters.region",
                        size: input.count,
                        shard_size: 100
                      },
                      aggs: {
                        regions_in: {
                          terms: {
                            field: "filters.state_level_regions",
                            exclude: "",
                            shard_size: 100
                          }
                        }
                      }
                    }
                  }
                },
                state_level_regions: {
                  terms: {
                    field: "filters.state_level_regions",
                    exclude: "",
                    include: expect.any(String),
                    shard_size: 100
                  },
                  aggs: {
                    locations_in_region: {
                      terms: {
                        field: "filters.region",
                        size: 200
                      }
                    }
                  }
                }
              }
            })
          );
        });
      });
    });
  });

  describe("autocompleteCountries", () => {
    describe("elasticsearch query building", () => {
      describe("input query", () => {
        it("should include should clauses for each of the main search paths", async () => {
          const input: CongressSearchFilterAutocompleteInput = {
            globalQuery: faker.datatype.string(),
            filterQuery: faker.datatype.string(),
            userId: faker.datatype.string(),
            projectId: faker.datatype.string(),
            count: faker.datatype.number()
          };

          const configService = createMockInstance(ConfigService);
          configService.elasticCongressIndex = faker.datatype.string();

          const queryUnderstandingServiceClient = createMockInstance(
            QueryUnderstandingServiceClient
          );
          const queryUnderstandingServerResponse =
            mockQueryUnderstandingServerResponse(input.globalQuery!, false);
          queryUnderstandingServiceClient.analyze.mockResolvedValue(
            queryUnderstandingServerResponse
          );

          const elasticSearchCongressService = createMockInstance(
            ElasticSearchCongressService
          );
          elasticSearchCongressService.mget.mockResolvedValue({ docs: [] });
          elasticSearchCongressService.query.mockResolvedValue(
            generateMockElasticsearchResponseWithAggregations()
          );

          const indicationsTreeSearchService = createMockInstance(
            IndicationsTreeSearchService
          );

          const congressSearchResponseAdapterService = createMockInstance(
            CongressSearchResponseAdapterService
          );

          const calculateMinimumNameVariations = createMockInstance(
            CalculateMinimumNameVariations
          );

          const userOnboardingDataService = createMockInstance(
            UserOnboardingDataService
          );
          userOnboardingDataService.getOnboardingData.mockResolvedValue(
            generateMockOnboardingData()
          );

          const nameSearchBuilderFactory = createMockInstance(
            NameSearchBuilderFactory
          );

          nameSearchBuilderFactory.getNameSearchBuilder.mockReturnValue(
            DefaultNameSearchBuilder.getInstance()
          );

          const matchingPeople = Array.from({ length: 10 }, () =>
            generateMockPersonHit()
          );
          const elasticSearchPeopleService =
            createMockInstance(ElasticSearchService);
          elasticSearchPeopleService.query.mockResolvedValue(
            generateMockElasticsearchResponse(matchingPeople)
          );

          const languageDetectService = createMockInstance(
            LanguageDetectService
          );

          const languageDetector = () => {
            return faker.helpers.arrayElement([
              ENGLISH,
              CHINESE,
              JAPANESE
            ]) as Language;
          };
          languageDetectService.getLanguageDetector.mockReturnValue(
            languageDetector
          );
          const featureFlagsService = createMockInstance(
            FeatureFlagsService as any
          ) as FeatureFlagsService;
          featureFlagsService.getAllFlags = jest
            .fn()
            .mockResolvedValue(generateFeatureFlagsState());
          const userClient = createMockInstance(UserResourceClient);
          const followRecordClient = createMockInstance(
            FollowRecordResourceClient
          );
          const tagsHelperService = createMockInstance(TagsHelperService);
          tagsHelperService.getEntitiesInProjectsByTags.mockResolvedValue([]);
          const hashedValue = faker.datatype.string();
          objectHash.sha1 = jest.fn().mockReturnValue(hashedValue);
          redis = new Redis({
            data: {
              [hashedValue]: "[]"
            }
          });
          const congressSearchResourceService =
            new CongressSearchResourceService(
              configService,
              congressSearchResponseAdapterService,
              queryUnderstandingServiceClient,
              calculateMinimumNameVariations,
              indicationsTreeSearchService,
              elasticSearchCongressService,
              userOnboardingDataService,
              nameSearchBuilderFactory,
              elasticSearchPeopleService,
              followRecordClient,
              languageDetectService,
              featureFlagsService,
              tagsHelperService,
              userClient
            );

          await congressSearchResourceService.autocompleteCountries(input);

          expect(elasticSearchCongressService.query).toHaveBeenCalledWith(
            expect.objectContaining({
              index: configService.elasticCongressIndex,
              track_total_hits: false,
              _source: false,
              query: {
                bool: {
                  minimum_should_match: 0,
                  filter: [],
                  must: {
                    bool: {
                      should: [
                        {
                          simple_query_string: {
                            query: input.globalQuery,
                            fields: [
                              "name^10",

                              "series_name^10",
                              "society",
                              "indication"
                            ],
                            default_operator: "and"
                          }
                        }
                      ],
                      minimum_should_match: 1,
                      must: [
                        {
                          nested: {
                            path: "addresses",
                            query: {
                              multi_match: {
                                type: "phrase_prefix",
                                query: input.filterQuery,
                                fields: [
                                  "addresses.country",
                                  "addresses.country_code",
                                  "addresses.country_level_regions"
                                ]
                              }
                            }
                          }
                        }
                      ]
                    }
                  }
                }
              },
              size: 0,
              aggs: {
                country: {
                  filter: {
                    bool: {
                      filter: [
                        {
                          nested: {
                            path: "addresses",
                            query: {
                              multi_match: {
                                type: "phrase_prefix",
                                query: input.filterQuery,
                                fields: [
                                  "addresses.country",
                                  "addresses.country_code"
                                ]
                              }
                            }
                          }
                        }
                      ]
                    }
                  },
                  aggs: {
                    filtered_matching: {
                      terms: {
                        field: "filters.country",
                        size: input.count,
                        shard_size: 100
                      },
                      aggs: {
                        regions_in: {
                          terms: {
                            field: "filters.country_level_regions",
                            exclude: "",
                            shard_size: 100
                          }
                        }
                      }
                    }
                  }
                },
                country_level_regions: {
                  terms: {
                    field: "filters.country_level_regions",
                    exclude: "",
                    include: expect.any(String),
                    shard_size: 100
                  },
                  aggs: {
                    locations_in_region: {
                      terms: {
                        field: "filters.country",
                        size: 200
                      }
                    }
                  }
                }
              }
            })
          );
        });
      });
    });
  });
});
