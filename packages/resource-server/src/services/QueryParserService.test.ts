import { faker } from "@faker-js/faker";
import { QueryUnderstandingServiceResponse } from "../proto/query_understanding_service_pb";
import { Synonym } from "../proto/synonym_pb";
import { createMockInstance } from "../util/TestUtils";
import { ConfigService } from "./ConfigService";
import { QueryParserService } from "./QueryParserService";
import { QueryUnderstandingServiceClient } from "./QueryUnderstandingServiceClient";
import { TwitterQueryParserService } from "./TwitterQueryParserService";
import _ from "lodash";
import { BothQueryParseResult } from "../lib/ParserTypes/types";

const synonym: Synonym = new Synonym();
const synScore: Synonym.SynScore = new Synonym.SynScore();

function generateMockSynonym(
  query: ReturnType<typeof synonym.getOrig>,
  synonyms: Array<ReturnType<typeof synScore.getSyn>>
): Synonym {
  const contextFreeSynonyms = synonyms.map((synonym) => {
    return {
      getSyn() {
        return synonym;
      }
    };
  });

  return {
    getOrig() {
      return query;
    },
    getContextFreeSynList() {
      return contextFreeSynonyms;
    }
  } as Synonym;
}

describe("parseQueryWithQueryUnderstandingService", () => {
  describe("nothing to do", () => {
    it("undefined, empty string, and entirely-whitespace query values should return undefined", async () => {
      const configService = createMockInstance(ConfigService);

      const twitterQueryParserService = createMockInstance(
        TwitterQueryParserService
      );

      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const queryParserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      const queries = [undefined, "", " \t "];
      const options = {
        projectSupportsAdvancedOperators: faker.datatype.boolean()
      };

      for (const query of queries) {
        const parsedQuery =
          await queryParserService.parseQueryWithQueryUnderstandingService(
            query,
            options
          );
        expect(parsedQuery).toBeUndefined();
      }

      expect(twitterQueryParserService.parseQuery).not.toHaveBeenCalled();
      expect(queryUnderstandingServiceClient.analyze).not.toHaveBeenCalled();
    });

    it("projectSupportsAdvancedOperators false should return query as-is", async () => {
      const configService = createMockInstance(ConfigService);

      const twitterQueryParserService = createMockInstance(
        TwitterQueryParserService
      );

      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const queryParserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      const query = faker.datatype.string();
      const options = {
        projectSupportsAdvancedOperators: false
      };

      const parsedQuery =
        await queryParserService.parseQueryWithQueryUnderstandingService(
          query,
          options
        );

      expect(parsedQuery).toEqual({
        parsedQueryTree: query,
        synonyms: []
      });
      expect(twitterQueryParserService.parseQuery).not.toHaveBeenCalled();
      expect(queryUnderstandingServiceClient.analyze).not.toHaveBeenCalled();
    });

    it("query w/o advanced operators should just return parsed query w/o calling query understanding server", async () => {
      const configService = createMockInstance(ConfigService);

      const twitterQueryParserService = createMockInstance(
        TwitterQueryParserService
      );
      const twitterParsedValue = faker.datatype.string();
      twitterQueryParserService.parseQuery.mockReturnValue(twitterParsedValue);

      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const queryParserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      const queryTerm1 = faker.random.word();
      const queryTerm2 = faker.random.word();

      const query = `${queryTerm1} AND ${queryTerm2}`;
      const options = {
        projectSupportsAdvancedOperators: true
      };

      const parsedQuery =
        await queryParserService.parseQueryWithQueryUnderstandingService(
          query,
          options
        );

      expect(parsedQuery).toEqual({
        parsedQueryTree: twitterParsedValue,
        synonyms: []
      });
      expect(twitterQueryParserService.parseQuery).toHaveBeenCalledWith(
        `${queryTerm1}   ${queryTerm2}`
      );
      expect(queryUnderstandingServiceClient.analyze).not.toHaveBeenCalled();
    });

    it("query understanding service enabled + doesn't have advanced operators should return ", async () => {
      const query = faker.random.word();
      const querySynonym1 = faker.random.word();
      const querySynonym2 = faker.random.word();
      const querySynonym3 = faker.random.word();

      const configService = createMockInstance(ConfigService);

      const twitterQueryParserService = createMockInstance(
        TwitterQueryParserService
      );
      const twitterParsedValue = faker.datatype.string();
      twitterQueryParserService.parseQuery.mockReturnValue(twitterParsedValue);

      const synonymThatMatchesQuery1 = generateMockSynonym(query, [
        querySynonym1
      ]);
      const synonymThatDoesntMatchQuery = generateMockSynonym(
        query + faker.datatype.string(),
        [querySynonym2]
      );
      const synonymThatHasEmptyContextFreeSynonyms = generateMockSynonym(
        query,
        []
      );
      const synonymThatHasUndefinedFirstContextFreeSynonym =
        generateMockSynonym(query, [undefined]);
      const synonymThatHasEmptyStringFirstContextFreeSynonym =
        generateMockSynonym(query, [""]);
      const synonymThatMatchesQuery2 = generateMockSynonym(query, [
        querySynonym3
      ]);

      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );
      const queryUnderstandingServiceResponse = {
        getAugmentedQuery: () => {
          return "a AND b OR c";
        },
        getUnigramSynonymList: () => {
          return [
            synonymThatMatchesQuery1,
            synonymThatDoesntMatchQuery,
            synonymThatHasEmptyContextFreeSynonyms,
            synonymThatHasUndefinedFirstContextFreeSynonym,
            synonymThatHasEmptyStringFirstContextFreeSynonym,
            synonymThatMatchesQuery2
          ];
        }
      } as QueryUnderstandingServiceResponse;
      queryUnderstandingServiceClient.analyze.mockResolvedValue(
        queryUnderstandingServiceResponse
      );

      const queryParserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      const options = {
        projectSupportsAdvancedOperators: true
      };

      const parsedQuery =
        await queryParserService.parseQueryWithQueryUnderstandingService(
          query,
          options
        );

      expect(parsedQuery).toEqual({
        parsedQueryTree: "a  b | c",
        queryUnderstandingServiceResponse,
        synonyms: [querySynonym1, querySynonym3].sort()
      });
      expect(twitterQueryParserService.parseQuery).not.toHaveBeenCalled();
      expect(queryUnderstandingServiceClient.analyze).toHaveBeenCalledWith(
        query,
        "eng"
      );
    });
  });
});

describe("testTheParserClass", () => {
  it("tests an AND query", async () => {
    const andQueryTestResponse = [
      "And",
      [
        ["Including", ["Text", "cancer"]],
        ["Including", ["Text", "oncology"]]
      ]
    ];

    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );
    twitterQueryParserService.parseQuery.mockReturnValue(andQueryTestResponse);

    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );

    const structure = queryParserService.parseQuery("cancer AND oncology");
    const expected = [
      "And",
      [
        ["Including", ["Text", "cancer"]],
        ["Including", ["Text", "oncology"]]
      ]
    ];
    expect(structure).toEqual(expected);
  });

  it("tests an AND query with quotes", async () => {
    const andQueryWithQuotesResponse = [
      "And",
      [
        ["Including", ["Text", "cancer"]],
        ["Including", ["Exactly", "oncology treatment"]]
      ]
    ];

    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );
    twitterQueryParserService.parseQuery.mockReturnValue(
      andQueryWithQuotesResponse
    );
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );

    const structure = queryParserService.parseQuery(
      'cancer AND "oncology treatment"'
    );
    const expected = [
      "And",
      [
        ["Including", ["Text", "cancer"]],
        ["Including", ["Exactly", "oncology treatment"]]
      ]
    ];
    expect(structure).toEqual(expected);
  });

  it("tests an AND and an Or query with no grouping", async () => {
    const andQueryWithOrNoParensResponse = [
      "And",
      [
        ["Including", ["Text", "cancer"]],
        [
          "Including",
          [
            "Or",
            [
              ["Including", ["Text", "oncology"]],
              ["Including", ["Text", "treatment"]]
            ]
          ]
        ]
      ]
    ];

    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );
    twitterQueryParserService.parseQuery.mockReturnValue(
      andQueryWithOrNoParensResponse
    );
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );

    const structure = queryParserService.parseQuery(
      "cancer AND oncology OR treatment"
    );
    const expected = [
      "And",
      [
        ["Including", ["Text", "cancer"]],
        [
          "Including",
          [
            "Or",
            [
              ["Including", ["Text", "oncology"]],
              ["Including", ["Text", "treatment"]]
            ]
          ]
        ]
      ]
    ];
    expect(structure).toEqual(expected);
  });

  it("test an OR query", async () => {
    const orQueryTestResponse = [
      "And",
      [
        [
          "Including",
          [
            "Or",
            [
              ["Including", ["Text", "cancer"]],
              ["Including", ["Text", "oncology"]]
            ]
          ]
        ]
      ]
    ];

    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );
    twitterQueryParserService.parseQuery.mockReturnValue(orQueryTestResponse);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );

    const struct2 = queryParserService.parseQuery("cancer OR oncology");
    const expected = [
      "And",
      [
        [
          "Including",
          [
            "Or",
            [
              ["Including", ["Text", "cancer"]],
              ["Including", ["Text", "oncology"]]
            ]
          ]
        ]
      ]
    ];
    expect(struct2).toEqual(expected);
  });

  it("test a NOT query", async () => {
    const notQueryTestResponse = [
      "And",
      [
        ["Including", ["Text", "cancer"]],
        ["Excluding", ["Text", "oncology"]]
      ]
    ];

    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );
    twitterQueryParserService.parseQuery.mockReturnValue(notQueryTestResponse);
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );

    const struct2 = queryParserService.parseQuery("cancer NOT oncology");
    const expected = [
      "And",
      [
        ["Including", ["Text", "cancer"]],
        ["Excluding", ["Text", "oncology"]]
      ]
    ];
    expect(struct2).toEqual(expected);
  });

  it("tests a nested query", async () => {
    const nestedQueryTestResponse = [
      "And",
      [
        ["Including", ["Text", "cancer"]],
        [
          "Including",
          [
            "Group",
            [
              "And",
              [
                [
                  "Including",
                  [
                    "Or",
                    [
                      ["Including", ["Text", "oncology"]],
                      [
                        "Including",
                        [
                          "Group",
                          [
                            "And",
                            [
                              ["Including", ["Text", "diabetes"]],
                              ["Including", ["Text", "sclerosis"]]
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]
    ];

    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );
    twitterQueryParserService.parseQuery.mockReturnValue(
      nestedQueryTestResponse
    );
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );

    const struct3 = queryParserService.parseQuery(
      "cancer (oncology OR (diabetes sclerosis))"
    );
    const expected = [
      "And",
      [
        ["Including", ["Text", "cancer"]],
        [
          "Including",
          [
            "Group",
            [
              "And",
              [
                [
                  "Including",
                  [
                    "Or",
                    [
                      ["Including", ["Text", "oncology"]],
                      [
                        "Including",
                        [
                          "Group",
                          [
                            "And",
                            [
                              ["Including", ["Text", "diabetes"]],
                              ["Including", ["Text", "sclerosis"]]
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]
    ];
    expect(struct3).toEqual(expected);
  });

  it("tests a deeply nested query", async () => {
    const deeplyNestedQueryTestResponse = [
      "And",
      [
        [
          "Including",
          [
            "Or",
            [
              [
                "Including",
                [
                  "Group",
                  [
                    "And",
                    [
                      ["Including", ["Text", "cancer"]],
                      ["Including", ["Text", "oncology"]]
                    ]
                  ]
                ]
              ],
              [
                "Including",
                [
                  "Group",
                  [
                    "And",
                    [
                      [
                        "Including",
                        [
                          "Or",
                          [
                            ["Including", ["Text", "test"]],
                            ["Including", ["Text", "test2"]],
                            [
                              "Including",
                              [
                                "Group",
                                [
                                  "And",
                                  [
                                    ["Including", ["Text", "test3"]],
                                    ["Including", ["Text", "test4"]]
                                  ]
                                ]
                              ]
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]
    ];

    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );
    twitterQueryParserService.parseQuery.mockReturnValue(
      deeplyNestedQueryTestResponse
    );
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );

    const structure = queryParserService.parseQuery(
      "(cancer AND oncology) OR (test OR test2 OR (test3 AND test4))"
    );
    const expected = [
      "And",
      [
        [
          "Including",
          [
            "Or",
            [
              [
                "Including",
                [
                  "Group",
                  [
                    "And",
                    [
                      ["Including", ["Text", "cancer"]],
                      ["Including", ["Text", "oncology"]]
                    ]
                  ]
                ]
              ],
              [
                "Including",
                [
                  "Group",
                  [
                    "And",
                    [
                      [
                        "Including",
                        [
                          "Or",
                          [
                            ["Including", ["Text", "test"]],
                            ["Including", ["Text", "test2"]],
                            [
                              "Including",
                              [
                                "Group",
                                [
                                  "And",
                                  [
                                    ["Including", ["Text", "test3"]],
                                    ["Including", ["Text", "test4"]]
                                  ]
                                ]
                              ]
                            ]
                          ]
                        ]
                      ]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]
    ];
    expect(structure).toEqual(expected);
  });

  it("test a nested query with a NOT query", async () => {
    const nestedQueryWithNotClauseResponse = [
      "And",
      [
        [
          "Including",
          [
            "Group",
            [
              "And",
              [
                ["Including", ["Text", "cancer"]],
                ["Including", ["Text", "diabetes"]]
              ]
            ]
          ]
        ],
        [
          "Excluding",
          [
            "Group",
            [
              "And",
              [
                [
                  "Including",
                  [
                    "Or",
                    [
                      ["Including", ["Text", "oncology"]],
                      ["Including", ["Text", "math"]]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]
    ];

    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );
    twitterQueryParserService.parseQuery.mockReturnValue(
      nestedQueryWithNotClauseResponse
    );
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );

    const struct2 = queryParserService.parseQuery(
      "(cancer AND diabetes) NOT (oncology OR math)"
    );
    const expected = [
      "And",
      [
        [
          "Including",
          [
            "Group",
            [
              "And",
              [
                ["Including", ["Text", "cancer"]],
                ["Including", ["Text", "diabetes"]]
              ]
            ]
          ]
        ],
        [
          "Excluding",
          [
            "Group",
            [
              "And",
              [
                [
                  "Including",
                  [
                    "Or",
                    [
                      ["Including", ["Text", "oncology"]],
                      ["Including", ["Text", "math"]]
                    ]
                  ]
                ]
              ]
            ]
          ]
        ]
      ]
    ];
    expect(struct2).toEqual(expected);
  });

  describe("both operator", () => {
    it("should parse the query for BOTH operator and return proper response", () => {
      const configService = createMockInstance(ConfigService);
      const twitterQueryParserService = createMockInstance(
        TwitterQueryParserService
      );
      twitterQueryParserService.parseQuery.mockReturnValue({});
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const queryParserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      const response = queryParserService.parseBothQuery(
        "BOTH( Cardiology WITH diabetes )"
      );
      const expected = { usedBoth: true, terms: ["Cardiology", "diabetes"] };
      expect(response).toEqual(expected);
    });

    it("should parse nested queries for BOTH operator", () => {
      const configService = createMockInstance(ConfigService);
      const twitterQueryParserService = createMockInstance(
        TwitterQueryParserService
      );
      twitterQueryParserService.parseQuery.mockReturnValue({});
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const queryParserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      const response = queryParserService.parseBothQuery(
        "BOTH( Cardiology WITH diabetes WITH ( cancer OR neoplasm ) WITH (diabetes or GLP-4) )"
      );
      const expected = {
        usedBoth: true,
        terms: [
          "Cardiology",
          "diabetes",
          "( cancer OR neoplasm )",
          "(diabetes or GLP-4)"
        ]
      };
      expect(response).toEqual(expected);
    });

    it("should return usedBoth as false when paranthesis are not balanced", () => {
      const configService = createMockInstance(ConfigService);
      const twitterQueryParserService = createMockInstance(
        TwitterQueryParserService
      );
      twitterQueryParserService.parseQuery.mockReturnValue({});
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const queryParserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      const response = queryParserService.parseBothQuery(
        "BOTH( Cardiology WITH (diabetes OR GLP-1 )"
      );
      const expected = { usedBoth: false, terms: [] };
      expect(response).toEqual(expected);
    });

    it("should return usedBoth as false when BOTH operator not found", () => {
      const configService = createMockInstance(ConfigService);
      const twitterQueryParserService = createMockInstance(
        TwitterQueryParserService
      );
      twitterQueryParserService.parseQuery.mockReturnValue({});
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const queryParserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      const response = queryParserService.parseBothQuery(
        "( Cardiology WITH diabetes )"
      );
      const expected = { usedBoth: false, terms: [] };
      expect(response).toEqual(expected);
    });

    it("should rewrite query to OR expression from BothParsedQuery", () => {
      const configService = createMockInstance(ConfigService);
      const twitterQueryParserService = createMockInstance(
        TwitterQueryParserService
      );
      twitterQueryParserService.parseQuery.mockReturnValue({});
      const queryUnderstandingServiceClient = createMockInstance(
        QueryUnderstandingServiceClient
      );

      const queryParserService = new QueryParserService(
        configService,
        twitterQueryParserService,
        queryUnderstandingServiceClient
      );

      const bothParseResponse: BothQueryParseResult = {
        usedBoth: true,
        terms: ["Cardiology", "(diabetes OR GLP-1)", "cancer"]
      };
      const response =
        queryParserService.rewriteBothQueryToOrExpression(bothParseResponse);
      expect(response).toEqual(
        "(Cardiology) OR (diabetes OR GLP-1) OR (cancer)"
      );
    });
  });
});

describe("test helper functions", () => {
  describe("test rewriteQuery", () => {
    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );

    it("`36415 AND sick`", () => {
      const query = "35416 AND sick";
      expect(queryParserService.rewriteQuery(query)).toEqual("35416   sick");
    });

    it("`36415 and sick`", () => {
      const query = "35416 and sick";
      expect(queryParserService.rewriteQuery(query)).toEqual("35416 and sick");
    });

    it("`老年痴呆症 OR dementia`", () => {
      const query = "老年痴呆症 OR dementia";
      expect(queryParserService.rewriteQuery(query)).toEqual(
        "老年痴呆症 OR dementia"
      );
    });

    it("`老年痴呆症 AND dementia`", () => {
      const query = "老年痴呆症 AND dementia";
      expect(queryParserService.rewriteQuery(query)).toEqual(
        "老年痴呆症   dementia"
      );
    });

    it("`老年痴呆症 NOT dementia`", () => {
      const query = "老年痴呆症 NOT dementia";
      expect(queryParserService.rewriteQuery(query)).toEqual(
        "老年痴呆症 ~dementia"
      );
    });

    it("`dementia OR 老年痴呆症`", () => {
      const query = "dementia OR 老年痴呆症";
      expect(queryParserService.rewriteQuery(query)).toEqual(
        "dementia OR 老年痴呆症"
      );
    });

    it("`老年痴呆症 or dementia`", () => {
      const query = "老年痴呆症 or dementia";
      expect(queryParserService.rewriteQuery(query)).toEqual(
        "老年痴呆症 or dementia"
      );
    });

    it("`老年痴呆症 and dementia`", () => {
      const query = "老年痴呆症 and dementia";
      expect(queryParserService.rewriteQuery(query)).toEqual(
        "老年痴呆症 and dementia"
      );
    });

    it("`老年痴呆症 not dementia`", () => {
      const query = "老年痴呆症 not dementia";
      expect(queryParserService.rewriteQuery(query)).toEqual(
        "老年痴呆症 not dementia"
      );
    });

    it("`a NOTb`", () => {
      const query = "a NOTb";
      expect(queryParserService.rewriteQuery(query)).toEqual("a NOTb");
    });

    it("`'a' AND \"b\"`", () => {
      const query = "'a' AND \"b\"";
      expect(queryParserService.rewriteQuery(query)).toEqual("'a'   \"b\"");
    });

    it("`Asthma OR “Exercise-Induced Bronchoconstriction”`", () => {
      const query = "Asthma OR “Exercise-Induced Bronchoconstriction”";
      expect(queryParserService.rewriteQuery(query)).toEqual(
        'Asthma OR "Exercise-Induced Bronchoconstriction"'
      );
    });

    it('`Asthma OR "Exercise-Induced Bronchoconstriction"`', () => {
      const query = 'Asthma OR "Exercise-Induced Bronchoconstriction"';
      expect(queryParserService.rewriteQuery(query)).toEqual(
        'Asthma OR "Exercise-Induced Bronchoconstriction"'
      );
    });

    it('`Asthma OR "Exercise-Induced Bronchoconstriction"`', () => {
      const query = 'Asthma OR "Exercise-Induced Bronchoconstriction"';
      expect(queryParserService.rewriteQuery(query)).toEqual(
        'Asthma OR "Exercise-Induced Bronchoconstriction"'
      );
    });

    it('`Asthma OR "Exercise-Induced Bronchoconstriction”`', () => {
      const query = 'Asthma OR "Exercise-Induced Bronchoconstriction”';
      expect(queryParserService.rewriteQuery(query)).toEqual(
        'Asthma OR "Exercise-Induced Bronchoconstriction"'
      );
    });
  });

  describe("test groupTerms", () => {
    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );
    const queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    const queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );

    it("(AB)", () => {
      const query = "(AB)";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("(AB)");
    });

    it("A AND B", () => {
      const query = "A AND B";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("A AND B");
    });

    it("A OR B", () => {
      const query = "A OR B";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("A OR B");
    });

    it("NOT A", () => {
      const query = "NOT A";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("NOT A");
    });

    it("a AND (ハッピー OR (癌))", () => {
      const query = "a AND (ハッピー OR (癌))";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("a AND (ハッピー OR (癌))");
    });

    it("A B C D E AND F G H I J K", () => {
      const query = "A B C D E AND F G H I J K";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("(A B C D E) AND (F G H I J K)");
    });

    it("A B C D E OR F G H I J K", () => {
      const query = "A B C D E OR F G H I J K";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("(A B C D E) OR (F G H I J K)");
    });

    it("NOT A B C D E", () => {
      const query = "NOT A B C D E";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("NOT (A B C D E)");
    });

    it("NOT (A B C D E)", () => {
      const query = "NOT (A B C D E)";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("NOT ((A B C D E))");
    });

    it('(A OR B) AND ("C D" AND NOT E F)', () => {
      const query = '(A OR B) AND ("C D" AND NOT E F)';
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual('(A OR B) AND (("C D") AND NOT (E F))');
    });

    it("(A OR B) AND (“C D” AND NOT E F)", () => {
      const query = "(A OR B) AND (“C D” AND NOT E F)";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("(A OR B) AND ((“C D”) AND NOT (E F))");
    });

    it('("A" AND (B C OR (D AND NOT E) OR "F G"))', () => {
      const query = '("A" AND (B C OR (D AND NOT E) OR "F G"))';
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual('("A" AND ((B C) OR (D AND NOT E) OR ("F G")))');
    });

    it('"Colonoscopy" AND (artificial intelligence OR "AI")', () => {
      const query = '"Colonoscopy" AND (artificial intelligence OR "AI")';
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual(
        '"Colonoscopy" AND ((artificial intelligence) OR "AI")'
      );
    });

    it('"a-b" AND "(cd)" OR "-e" NOT "f"', () => {
      const query = '"a-b" AND "(cd)" OR "-e" NOT "f"';
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual('"a-b" AND "(cd)" OR "-e" NOT "f"');
    });

    it('"Colonoscopy"', () => {
      const query = '"Colonoscopy"';
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual('"Colonoscopy"');
    });

    it('"artificial intelligence"', () => {
      const query = '"artificial intelligence"';
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual('"artificial intelligence"');
    });

    it("artificial intelligence", () => {
      const query = "artificial intelligence";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("artificial intelligence");
    });

    it("NOT artificial intelligence", () => {
      const query = "NOT artificial intelligence";
      const newQuery = queryParserService.groupTerms(query);
      expect(newQuery).toEqual("NOT (artificial intelligence)");
    });

    describe("should handle parenthesis in query", () => {
      it("A(B) AND C", () => {
        const query = "A(B) AND C";
        const newQuery = queryParserService.groupTerms(query);
        expect(newQuery).toEqual("A(B) AND C");
      });

      it('("Alpha-1 antitrypsin deficiency" OR "Alpha1-antitrypsin deficiency" OR "Alpha-1-antitrypsin deficiency" OR "Alpha(1)-antitrypsin deficiency" OR "Alpha-1 antiprotease deficiency" OR "α-1 antitrypsin deficiency" OR "α1-antitrypsin deficiency" OR "AAT deficiency" OR "A1AT deficiency")', () => {
        const query =
          '("Alpha-1 antitrypsin deficiency" OR "Alpha1-antitrypsin deficiency" OR "Alpha-1-antitrypsin deficiency" OR "Alpha(1)-antitrypsin deficiency" OR "Alpha-1 antiprotease deficiency" OR "α-1 antitrypsin deficiency" OR "α1-antitrypsin deficiency" OR "AAT deficiency" OR "A1AT deficiency")';
        const newQuery = queryParserService.groupTerms(query);
        expect(newQuery).toEqual(
          '(("Alpha-1 antitrypsin deficiency") OR ("Alpha1-antitrypsin deficiency") OR ("Alpha-1-antitrypsin deficiency") OR ("Alpha(1)-antitrypsin deficiency") OR ("Alpha-1 antiprotease deficiency") OR ("α-1 antitrypsin deficiency") OR ("α1-antitrypsin deficiency") OR ("AAT deficiency") OR ("A1AT deficiency"))'
        );
      });
    });
  });
});

describe("getKeywordSynonyms", () => {
  const getMockQueryUnderstandingResponse = (
    keyword: string,
    expectedSynonyms: string[]
  ) => {
    const queryUnderstandingServiceResponse: QueryUnderstandingServiceResponse =
      new QueryUnderstandingServiceResponse();
    for (const expectedSynonym of expectedSynonyms) {
      const synonym = new Synonym();
      synonym.setOrig(keyword);
      const synAndScore = new Synonym.SynScore();
      synAndScore.setSyn(expectedSynonym);
      synAndScore.setScore(1.0);
      synonym.addContextFreeSyn(synAndScore);
      queryUnderstandingServiceResponse.addUnigramSynonym(synonym);
    }
    return queryUnderstandingServiceResponse;
  };

  let queryParserService: QueryParserService;
  let queryUnderstandingServiceClient: QueryUnderstandingServiceClient;

  beforeEach(() => {
    const configService = createMockInstance(ConfigService);
    const twitterQueryParserService = createMockInstance(
      TwitterQueryParserService
    );

    queryUnderstandingServiceClient = createMockInstance(
      QueryUnderstandingServiceClient
    );

    queryParserService = new QueryParserService(
      configService,
      twitterQueryParserService,
      queryUnderstandingServiceClient
    );
  });

  it("calls queryUnderstandingServiceClient as expected, returns synonyms", async () => {
    const keyword = faker.datatype.string();
    const synonyms = _.sortBy([
      faker.datatype.string(),
      faker.datatype.string()
    ]);

    jest
      .spyOn(queryUnderstandingServiceClient, "analyze")
      .mockResolvedValueOnce(
        getMockQueryUnderstandingResponse(keyword, synonyms)
      );

    const resp = await queryParserService.getKeywordSynonyms(keyword);

    expect(resp).toEqual(synonyms);
  });

  it("handles empty keyword input", async () => {
    await expect(queryParserService.getKeywordSynonyms("")).rejects.toEqual(
      new Error("Expected non-empty keyword")
    );
  });
});
