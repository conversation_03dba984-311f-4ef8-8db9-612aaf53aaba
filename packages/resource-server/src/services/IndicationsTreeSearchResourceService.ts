import {
  IndicationNode,
  IndicationsGetSubTreesInput,
  IndicationsTreeSearchResource,
  RPC_NAMESPACE_INDICATIONS_TREE_SEARCH,
  SearchIndicationsByQueryInput,
  SearchIndicationTreesByQueryInput,
  SearchRootIndicationsInput
} from "@h1nyc/search-sdk";
import { RpcMethod, RpcResourceService, RpcService } from "@h1nyc/systems-rpc";
import { Service } from "typedi";
import { ConfigService } from "./ConfigService";
import { IndicationsTreeSearchService } from "./IndicationsTreeSearchService";
import { Trace } from "../Tracer";

@Service()
@RpcService()
export class IndicationsTreeSearchResourceService
  extends RpcResourceService
  implements IndicationsTreeSearchResource
{
  constructor(
    config: ConfigService,
    private indicationsTreeSearchService: IndicationsTreeSearchService
  ) {
    super(
      config.searchRpcSigningKey,
      RPC_NAMESPACE_INDICATIONS_TREE_SEARCH,
      config.searchRedisOptions
    );
  }

  @RpcMethod()
  @Trace("h1-search.indicationsTree.searchRootIndications")
  searchRootIndications(
    input: SearchRootIndicationsInput
  ): Promise<IndicationNode[]> {
    return this.indicationsTreeSearchService.searchRootIndications(input);
  }

  @RpcMethod()
  @Trace("h1-search.indicationsTree.searchByQuery")
  searchByQuery(
    input: SearchIndicationsByQueryInput
  ): Promise<IndicationNode[]> {
    return this.indicationsTreeSearchService.searchIndicationsByQuery(input);
  }

  @RpcMethod()
  @Trace("h1-search.indicationsTree.getSubTrees")
  getSubTrees(input: IndicationsGetSubTreesInput): Promise<IndicationNode[]> {
    return this.indicationsTreeSearchService.getSubTreesForRoot(input);
  }

  @RpcMethod()
  @Trace("h1-search.indicationsTree.searchIndicationTreesByQuery")
  searchIndicationTreesByQuery(
    input: SearchIndicationTreesByQueryInput
  ): Promise<IndicationNode[]> {
    return this.indicationsTreeSearchService.searchIndicationTreesByQuery(
      input
    );
  }
}
