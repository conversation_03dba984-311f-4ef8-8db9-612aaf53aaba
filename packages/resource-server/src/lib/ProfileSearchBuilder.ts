import {
  ProfileFilterValue,
  SortDirection,
  PublicationFields,
  TrialFields,
  CongressFields,
  ProfileFilters
} from "@h1nyc/search-sdk";
import { PublicationProfileAsset } from "./ProfileQueryBuilder/PublicationProfileAsset";
import { TrialProfileAsset } from "./ProfileQueryBuilder/TrialProfileAsset";
import { CongressProfileAsset } from "./ProfileQueryBuilder/CongressProfileAsset";
import { PaymentProfileAsset } from "./ProfileQueryBuilder/PaymentProfileAsset";
import { DrgDiagnosisProfileAsset } from "./ProfileQueryBuilder/DrgDiagnosisProfileAsset";
import { DrgProcedureProfileAsset } from "./ProfileQueryBuilder/DrgProcedureProfileAsset";
import {
  QueryDslQueryContainer,
  SearchRequest
} from "@elastic/elasticsearch/lib/api/types";
import _ from "lodash";
import { Page } from "@h1nyc/account-sdk/dist/interfaces/Pagination";

export enum ProfileSearchAsset {
  Publications = "Publications",
  ClinicalTrials = "ClinicalTrials",
  Congresses = "Congresses",
  Payments = "Payments",
  DrgDiagnoses = "DrgDiagnoses",
  DrgProcedures = "DrgProcedures",
  Tweets = "Tweets"
}

interface AssetIdsResponse {
  buckets: {
    key: string;
    doc_count: number;
  }[];
}

interface AssetDocCountResponse {
  doc_count: number;
}

interface PublicationYearTotalsResponse {
  buckets: {
    key_as_string: string;
    doc_count: number;
  }[];
}

interface PublicationCitationCountResponse {
  value: number;
}

interface PublicationMicroBloggingCountResponse {
  value: number;
}

interface PublicationGuidelineCountResponse {
  doc_count: number;
}

interface PublicationAggregationsResponse {
  yearTotals: PublicationYearTotalsResponse;
  citationCount: PublicationCitationCountResponse;
  microBloggingCount: PublicationMicroBloggingCountResponse;
  guidelineCount: PublicationGuidelineCountResponse;
}

interface InternalCountResponse {
  value: number;
}

interface TotalPaymentsAmountResponse {
  value: number;
}

interface PaymentAggregationsResponse {
  totalPaymentsAmount: TotalPaymentsAmountResponse;
}

interface DiagnosisAggregationsResponse {
  internalCount: InternalCountResponse;
}

interface ProcedureAggregationsResponse {
  internalCount: InternalCountResponse;
}

export interface PublicationAggregationsBucket
  extends PublicationAggregationsResponse {
  doc_count: number;
}

export interface PaymentAggregationsBucket extends PaymentAggregationsResponse {
  doc_count: number;
}

interface TrialStatusCountResponse {
  doc_count: number;
}

interface TrialYearTotalsResponse {
  buckets: {
    key_as_string: string;
    doc_count: number;
  }[];
}

interface TrialAggregationsResponse {
  yearTotals: TrialYearTotalsResponse;
  inProgressCount: TrialStatusCountResponse;
  terminatedCount: TrialStatusCountResponse;
  completedCount: TrialStatusCountResponse;
  topConditions: AssetIdsResponse;
  topInterventions: AssetIdsResponse;
  ids: AssetIdsResponse;
}

export interface TrialAggregationsBucket extends TrialAggregationsResponse {
  doc_count: number;
}

export interface CongressAggregationsBucket {
  doc_count: number;
  sessionsTotal: AssetDocCountResponse;
  postersTotal: AssetDocCountResponse;
  topCongresses: AssetIdsResponse;
}

export interface DiagnosisAggregationsBucket
  extends DiagnosisAggregationsResponse {
  doc_count: number;
}

export interface ProcedureAggregationsBucket
  extends ProcedureAggregationsResponse {
  doc_count: number;
}

export interface ProfileSearchIdsResponse {
  aggregations: {
    people?: {
      buckets: {
        key: string;
        publications?: {
          doc_count: number;
          publicationFilters: {
            buckets: {
              doc_count: number;
              ids: AssetIdsResponse;
            }[];
          };
        };
        trials?: {
          doc_count: number;
          trialFilters: {
            buckets: {
              doc_count: number;
              ids: AssetIdsResponse;
            }[];
          };
        };
        congresses?: {
          doc_count: number;
          congressFilters: {
            buckets: {
              doc_count: number;
              ids: AssetIdsResponse;
            }[];
          };
        };
        payments?: {
          doc_count: number;
          paymentFilters: {
            buckets: {
              doc_count: number;
              ids: AssetIdsResponse;
            }[];
          };
        };
      }[];
    };
  };
}

export interface Bucket {
  key: string;
  publications?:
    | PublicationAggregationsBucket
    | {
        publicationFilters: {
          buckets: PublicationAggregationsBucket[];
        };
      };
  trials?:
    | TrialAggregationsBucket
    | {
        trialFilters: {
          buckets: TrialAggregationsBucket[];
        };
      };
  congresses?:
    | CongressAggregationsBucket
    | {
        congressFilters: {
          buckets: CongressAggregationsBucket[];
        };
      };
  diagnoses?:
    | DiagnosisAggregationsBucket
    | {
        diagnosisFilters: {
          buckets: DiagnosisAggregationsBucket[];
        };
      };
  procedures?:
    | ProcedureAggregationsBucket
    | {
        procedureFilters: {
          buckets: ProcedureAggregationsBucket[];
        };
      };
  payments?:
    | PaymentAggregationsBucket
    | {
        paymentFilters: {
          buckets: PaymentAggregationsBucket[];
        };
      };
}

export interface ProfileMetricsResponse {
  aggregations: {
    people?: {
      buckets: Bucket[];
    };
  };
}

export function buildSortedProfileSearchQuery(
  peopleIds: string[],
  assets: ProfileSearchAsset[],
  size: number,
  termsAndFilters?: {
    terms?: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>>;
    filters?: ProfileFilters;
  }
) {
  const aggs = assets.reduce((acc, asset) => {
    const terms: QueryDslQueryContainer | undefined = _.get(termsAndFilters, [
      "terms",
      asset
    ]);

    switch (asset) {
      case ProfileSearchAsset.Publications:
        return {
          ...acc,
          ...new PublicationProfileAsset().buildIdsAggregation(
            terms,
            termsAndFilters?.filters?.publicationFiltersValues,
            size,
            {
              direction: SortDirection.Desc,
              sortBy: PublicationFields.datePublished
            }
          )
        };
      case ProfileSearchAsset.ClinicalTrials:
        return {
          ...acc,
          ...new TrialProfileAsset().buildIdsAggregation(
            terms,
            termsAndFilters?.filters?.trialFiltersValues,
            size,
            { direction: SortDirection.Desc, sortBy: TrialFields.startDate }
          )
        };
      case ProfileSearchAsset.Congresses:
        return {
          ...acc,
          ...new CongressProfileAsset().buildIdsAggregation(
            terms,
            termsAndFilters?.filters?.congressFiltersValues,
            size,
            {
              direction: SortDirection.Desc,
              sortBy: CongressFields.masterInitialDate
            }
          )
        };

      default:
        return acc;
    }
  }, {});

  return {
    _source: "_none",
    size: 0,
    query: {
      bool: {
        filter: {
          terms: {
            id: peopleIds
          }
        }
      }
    },
    aggs: {
      people: {
        terms: {
          field: "id",
          size: peopleIds.length
        },
        aggs
      }
    }
  };
}

function buildPeopleIdTermsFilter(peopleIds: string[]) {
  return {
    terms: {
      id: peopleIds
    }
  };
}

function buildProjectIdFilter(projectId: string) {
  return {
    term: {
      projectIds: projectId
    }
  };
}

export function buildProfileSearchQuery(
  peopleIds: string[],
  assets: ProfileSearchAsset[],
  suppliedTerms?: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>>,
  filters?: ProfileFilterValue[],
  languageCode?: string
) {
  const aggs = assets.reduce((acc, asset) => {
    const terms = _.get(suppliedTerms, asset);

    switch (asset) {
      case ProfileSearchAsset.Publications:
        return {
          ...acc,
          ...new PublicationProfileAsset().buildIdsAggregation(
            terms,
            filters,
            10000,
            {}
          )
        };
      case ProfileSearchAsset.ClinicalTrials:
        return {
          ...acc,
          ...new TrialProfileAsset().buildIdsAggregation(terms, filters)
        };

      case ProfileSearchAsset.Congresses:
        return {
          ...acc,
          ...new CongressProfileAsset().buildIdsAggregation(terms, filters)
        };
      case ProfileSearchAsset.Payments:
        return {
          ...acc,
          ...new PaymentProfileAsset().buildIdsAggregation(terms, filters)
        };

      default:
        return acc;
    }
  }, {});

  return {
    _source: "_none",
    size: 0,
    query: {
      bool: {
        filter: {
          terms: {
            id: peopleIds
          }
        }
      }
    },
    aggs: {
      people: {
        terms: {
          field: "id",
          size: peopleIds.length
        },
        aggs
      }
    }
  };
}

export function buildProfileMetricsQuery(
  peopleIds: string[],
  assets: ProfileSearchAsset[],
  projectId?: string,
  suppliedTerms?: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>>,
  filters: ProfileFilterValue[] = [],
  languageCode?: string
): SearchRequest {
  const aggs = assets.reduce((acc, asset) => {
    const terms = _.get(suppliedTerms, asset);

    switch (asset) {
      case ProfileSearchAsset.Publications:
        return {
          ...acc,
          ...new PublicationProfileAsset().buildMetricsAggregation(
            terms,
            filters
          )
        };
      case ProfileSearchAsset.ClinicalTrials:
        return {
          ...acc,
          ...new TrialProfileAsset().buildMetricsAggregation(terms, filters)
        };

      case ProfileSearchAsset.Congresses:
        return {
          ...acc,
          ...new CongressProfileAsset().buildMetricsAggregation(terms, filters)
        };

      default:
        return acc;
    }
  }, {});

  const filtersToUse: QueryDslQueryContainer[] = [];
  filtersToUse.push(buildPeopleIdTermsFilter(peopleIds));
  if (projectId) {
    filtersToUse.push(buildProjectIdFilter(projectId));
  }
  return {
    _source: "_none",
    size: 0,
    query: {
      bool: {
        filter: filtersToUse
      }
    },
    aggs: {
      people: {
        terms: {
          field: "id",
          size: peopleIds.length
        },
        aggs
      }
    }
  };
}

export function buildAllProfileMetricsQuery(
  peopleIds: string[],
  assets: ProfileSearchAsset[],
  projectId?: string,
  termsAndFilters?: {
    terms?: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>>;
    filters?: ProfileFilters;
  },
  languageCode?: string
) {
  const aggs = assets.reduce((acc, asset) => {
    const terms: QueryDslQueryContainer | undefined = _.get(termsAndFilters, [
      "terms",
      asset
    ]);

    switch (asset) {
      case ProfileSearchAsset.Publications:
        return {
          ...acc,
          ...new PublicationProfileAsset().buildMetricsAggregation(
            terms,
            termsAndFilters?.filters?.publicationFiltersValues
          )
        };
      case ProfileSearchAsset.ClinicalTrials:
        return {
          ...acc,
          ...new TrialProfileAsset().buildMetricsAggregation(
            terms,
            termsAndFilters?.filters?.trialFiltersValues
          )
        };
      case ProfileSearchAsset.Congresses:
        return {
          ...acc,
          ...new CongressProfileAsset().buildMetricsAggregation(
            terms,
            termsAndFilters?.filters?.congressFiltersValues
          )
        };
      case ProfileSearchAsset.DrgProcedures:
        return {
          ...acc,
          ...new DrgProcedureProfileAsset().buildMetricsAggregation(terms)
        };
      case ProfileSearchAsset.DrgDiagnoses:
        return {
          ...acc,
          ...new DrgDiagnosisProfileAsset().buildMetricsAggregation(terms)
        };
      case ProfileSearchAsset.Payments:
        return {
          ...acc,
          ...new PaymentProfileAsset().buildMetricsAggregation(
            terms,
            termsAndFilters?.filters?.paymentsFilterValues
          )
        };
      default:
        return acc;
    }
  }, {});

  const filtersToUse = [];
  filtersToUse.push(buildPeopleIdTermsFilter(peopleIds));
  if (projectId) {
    filtersToUse.push(buildProjectIdFilter(projectId));
  }

  return {
    _source: "_none",
    size: 0,
    query: {
      bool: {
        filter: filtersToUse
      }
    },
    aggs: {
      people: {
        terms: {
          field: "id",
          size: peopleIds.length
        },
        aggs
      }
    }
  };
}

export function buildPaginatedProfileSearchQueryForAsset(
  personId: string,
  projectId: string,
  asset: ProfileSearchAsset,
  suppliedTerms?: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>>,
  filters?: ProfileFilterValue[],
  page?: Page
): QueryDslQueryContainer | null {
  switch (asset) {
    case ProfileSearchAsset.ClinicalTrials:
      return new TrialProfileAsset().buildPaginatedTrialsQuery(
        personId,
        projectId,
        _.get(suppliedTerms, ProfileSearchAsset.ClinicalTrials),
        filters,
        page
      );

    default:
      return null;
  }
}
