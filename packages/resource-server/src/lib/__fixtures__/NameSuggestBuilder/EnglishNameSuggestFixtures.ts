import {
  getAllWordPermutationEnd,
  reverseQueryTermOrder
} from "../../NameSuggestBuilder/EnglishNameSuggestBuilderWithProminence";

const BOOST_SAUT_QUERY = 1.0;
const BOOST_SAUT_OTHER_PERMUTATIONS = 0.1;
const BOOST_SAUT_REVERSE_QUERY = 0.1;

export const prominenceQueryResultWithFuzzyMatch = {
  _source: [
    "id",
    "h1dn_id",
    "name_eng",
    "specialty_eng",
    "designations",
    "affiliations",
    "locations",
    "indications"
  ],
  from: 0,
  size: 5,
  query: {
    bool: {
      should: [
        {
          function_score: {
            query: {
              bool: {
                should: [
                  {
                    multi_match: {
                      query: "Elias Jabour",
                      type: "bool_prefix",
                      operator: "AND",
                      boost: 1.0,
                      fields: [
                        "name_eng.autocomplete_search",
                        "firstLastName_eng.autocomplete_search._2gram",
                        "firstLastName_eng.autocomplete_search._3gram"
                      ]
                    }
                  },
                  {
                    multi_match: {
                      query: "<PERSON><PERSON><PERSON> Elias",
                      type: "bool_prefix",
                      operator: "AND",
                      boost: 0.1,
                      fields: [
                        "name_eng.autocomplete_search",
                        "firstLastName_eng.autocomplete_search._2gram",
                        "firstLastName_eng.autocomplete_search._3gram"
                      ]
                    }
                  },
                  {
                    multi_match: {
                      query: "Elias Jabour",
                      operator: "AND",
                      fields: ["name_eng", "firstLastName_eng^2"]
                    }
                  },
                  {
                    multi_match: {
                      query: "Elias Jabour",
                      operator: "AND",
                      type: "cross_fields",
                      fields: [
                        "firstName_eng",
                        "middleName_eng.initial",
                        "lastName_eng"
                      ]
                    }
                  }
                ],
                minimum_should_match: 1
              }
            },
            functions: [
              {
                script_score: {
                  script: {
                    source:
                      "3*Math.tanh(doc['presentWorkInstitutionCount'].value)"
                  }
                }
              },
              {
                script_score: {
                  script: {
                    source: "0.5*Math.log(doc['publicationCount'].value + 1)"
                  }
                }
              },
              {
                script_score: {
                  script: {
                    source: "0.35*Math.log(doc['trialCount'].value + 1)"
                  }
                }
              },
              {
                script_score: {
                  script: {
                    source: "0.15*Math.log(doc['congressCount'].value + 1)"
                  }
                }
              }
            ],
            boost_mode: "sum",
            score_mode: "sum"
          }
        },
        {
          function_score: {
            query: {
              bool: {
                must: [
                  {
                    match: {
                      firstLastName_eng: {
                        query: "Elias Jabour",
                        prefix_length: 1,
                        operator: "OR",
                        fuzziness: "AUTO:4,7",
                        boost: 0.01,
                        minimum_should_match: "2<-1 4<-2"
                      }
                    }
                  }
                ]
              }
            },
            functions: [
              {
                script_score: {
                  script: {
                    source:
                      "0.1*Math.tanh(doc['presentWorkInstitutionCount'].value)"
                  }
                }
              },
              {
                script_score: {
                  script: {
                    source: "0.3*Math.log(doc['publicationCount'].value + 1)"
                  }
                }
              },
              {
                script_score: {
                  script: {
                    source: "0.1*Math.log(doc['trialCount'].value + 1)"
                  }
                }
              },
              {
                script_score: {
                  script: {
                    source: "0.1*Math.log(doc['congressCount'].value + 1)"
                  }
                }
              }
            ],
            boost_mode: "sum",
            score_mode: "sum"
          }
        }
      ],
      minimum_should_match: 1,
      filter: [
        {
          term: {
            projectIds: "abc123"
          }
        }
      ]
    }
  },
  sort: [
    {
      _score: "desc"
    },
    {
      _script: {
        type: "number",
        script: {
          lang: "painless",
          source: "doc['lastName_eng.sort'].value.length()"
        },
        order: "asc"
      }
    },
    {
      "lastName_eng.sort": "asc"
    },
    {
      _script: {
        type: "number",
        script: {
          lang: "painless",
          source: "doc['firstName_eng.sort'].value.length()"
        },
        order: "asc"
      }
    },
    {
      "firstName_eng.sort": "asc"
    }
  ]
};

export const generateProminenceQueryWithPermutationFlagEnabled = (
  query: string,
  projectId: string
) => {
  const allPermutations = getAllWordPermutationEnd(query);

  const esQuery = {
    _source: [
      "id",
      "h1dn_id",
      "name_eng",
      "specialty_eng",
      "designations",
      "affiliations",
      "locations",
      "indications"
    ],
    from: 0,
    size: 5,
    query: {
      bool: {
        should: [
          {
            function_score: {
              query: {
                bool: {
                  should: [
                    ...allPermutations.map((permutation) =>
                      permutation === query
                        ? {
                            multi_match: {
                              query: permutation,
                              type: "bool_prefix",
                              operator: "AND",
                              boost: BOOST_SAUT_QUERY,
                              fields: [
                                "name_eng.autocomplete_search",
                                "firstLastName_eng.autocomplete_search._2gram",
                                "firstLastName_eng.autocomplete_search._3gram"
                              ]
                            }
                          }
                        : {
                            query: permutation,
                            type: "bool_prefix",
                            operator: "AND",
                            boost: BOOST_SAUT_OTHER_PERMUTATIONS,
                            fields: [
                              "name_eng.autocomplete_search",
                              "firstLastName_eng.autocomplete_search._2gram",
                              "firstLastName_eng.autocomplete_search._3gram"
                            ]
                          }
                    ),
                    {
                      multi_match: {
                        query: query,
                        operator: "AND",
                        fields: ["name_eng", "firstLastName_eng^2"]
                      }
                    },
                    {
                      multi_match: {
                        query: query,
                        operator: "AND",
                        type: "cross_fields",
                        fields: [
                          "firstName_eng",
                          "middleName_eng.initial",
                          "lastName_eng"
                        ]
                      }
                    }
                  ],
                  minimum_should_match: 1
                }
              },
              functions: [
                {
                  script_score: {
                    script: {
                      source:
                        "3*Math.tanh(doc['presentWorkInstitutionCount'].value)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.5*Math.log(doc['publicationCount'].value + 1)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.35*Math.log(doc['trialCount'].value + 1)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.15*Math.log(doc['congressCount'].value + 1)"
                    }
                  }
                }
              ],
              boost_mode: "sum",
              score_mode: "sum"
            }
          },
          {
            function_score: {
              query: {
                bool: {
                  must: [
                    {
                      match: {
                        firstLastName_eng: {
                          query: query,
                          prefix_length: 1,
                          operator: "OR",
                          fuzziness: "AUTO:4,7",
                          boost: 0.01,
                          minimum_should_match: "2<-1 4<-2"
                        }
                      }
                    }
                  ]
                }
              },
              functions: [
                {
                  script_score: {
                    script: {
                      source:
                        "0.1*Math.tanh(doc['presentWorkInstitutionCount'].value)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.3*Math.log(doc['publicationCount'].value + 1)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.1*Math.log(doc['trialCount'].value + 1)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.1*Math.log(doc['congressCount'].value + 1)"
                    }
                  }
                }
              ],
              boost_mode: "sum",
              score_mode: "sum"
            }
          }
        ],
        minimum_should_match: 1,
        filter: [
          {
            term: {
              projectIds: projectId
            }
          }
        ]
      }
    },
    sort: [
      {
        _score: "desc"
      },
      {
        _script: {
          type: "number",
          script: {
            lang: "painless",
            source: "doc['lastName_eng.sort'].value.length()"
          },
          order: "asc"
        }
      },
      {
        "lastName_eng.sort": "asc"
      },
      {
        _script: {
          type: "number",
          script: {
            lang: "painless",
            source: "doc['firstName_eng.sort'].value.length()"
          },
          order: "asc"
        }
      },
      {
        "firstName_eng.sort": "asc"
      }
    ]
  };

  return esQuery;
};

export const generateProminenceQueryWithPermutationFlagDisabled = (
  query: string,
  projectId: string
) => {
  const esQuery = {
    _source: [
      "id",
      "h1dn_id",
      "name_eng",
      "specialty_eng",
      "designations",
      "affiliations",
      "locations",
      "indications"
    ],
    from: 0,
    size: 5,
    query: {
      bool: {
        should: [
          {
            function_score: {
              query: {
                bool: {
                  should: [
                    {
                      multi_match: {
                        query: query,
                        type: "bool_prefix",
                        operator: "AND",
                        boost: BOOST_SAUT_QUERY,
                        fields: [
                          "name_eng.autocomplete_search",
                          "firstLastName_eng.autocomplete_search._2gram",
                          "firstLastName_eng.autocomplete_search._3gram"
                        ]
                      }
                    },
                    {
                      multi_match: {
                        query: reverseQueryTermOrder(query),
                        type: "bool_prefix",
                        operator: "AND",
                        boost: BOOST_SAUT_REVERSE_QUERY,
                        fields: [
                          "name_eng.autocomplete_search",
                          "firstLastName_eng.autocomplete_search._2gram",
                          "firstLastName_eng.autocomplete_search._3gram"
                        ]
                      }
                    },
                    {
                      multi_match: {
                        query: query,
                        operator: "AND",
                        fields: ["name_eng", "firstLastName_eng^2"]
                      }
                    },
                    {
                      multi_match: {
                        query: query,
                        operator: "AND",
                        type: "cross_fields",
                        fields: [
                          "firstName_eng",
                          "middleName_eng.initial",
                          "lastName_eng"
                        ]
                      }
                    }
                  ],
                  minimum_should_match: 1
                }
              },
              functions: [
                {
                  script_score: {
                    script: {
                      source:
                        "3*Math.tanh(doc['presentWorkInstitutionCount'].value)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.5*Math.log(doc['publicationCount'].value + 1)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.35*Math.log(doc['trialCount'].value + 1)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.15*Math.log(doc['congressCount'].value + 1)"
                    }
                  }
                }
              ],
              boost_mode: "sum",
              score_mode: "sum"
            }
          },
          {
            function_score: {
              query: {
                bool: {
                  must: [
                    {
                      match: {
                        firstLastName_eng: {
                          query: query,
                          prefix_length: 1,
                          operator: "OR",
                          fuzziness: "AUTO:4,7",
                          boost: 0.01,
                          minimum_should_match: "2<-1 4<-2"
                        }
                      }
                    }
                  ]
                }
              },
              functions: [
                {
                  script_score: {
                    script: {
                      source:
                        "0.1*Math.tanh(doc['presentWorkInstitutionCount'].value)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.3*Math.log(doc['publicationCount'].value + 1)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.1*Math.log(doc['trialCount'].value + 1)"
                    }
                  }
                },
                {
                  script_score: {
                    script: {
                      source: "0.1*Math.log(doc['congressCount'].value + 1)"
                    }
                  }
                }
              ],
              boost_mode: "sum",
              score_mode: "sum"
            }
          }
        ],
        minimum_should_match: 1,
        filter: [
          {
            term: {
              projectIds: projectId
            }
          }
        ]
      }
    },
    sort: [
      {
        _score: "desc"
      },
      {
        _script: {
          type: "number",
          script: {
            lang: "painless",
            source: "doc['lastName_eng.sort'].value.length()"
          },
          order: "asc"
        }
      },
      {
        "lastName_eng.sort": "asc"
      },
      {
        _script: {
          type: "number",
          script: {
            lang: "painless",
            source: "doc['firstName_eng.sort'].value.length()"
          },
          order: "asc"
        }
      },
      {
        "firstName_eng.sort": "asc"
      }
    ]
  };

  return esQuery;
};
