export const nameSearchResult = {
  track_total_hits: true,
  _source: [
    "id",
    "h1dn_id",
    "name_jpn",
    "specialty_jpn",
    "designations",
    "affiliations",
    "locations",
    "indications"
  ],
  from: 0,
  size: 5,
  sort: [
    { "lastName_jpn.sort": { order: "asc" } },
    { "firstName_jpn.sort": { order: "asc" } }
  ],
  query: {
    bool: {
      should: [{ prefix: { name_jpn: "鈴木" } }],
      minimum_should_match: 1,
      filter: [{ term: { projectIds: "abc123" } }]
    }
  }
};
