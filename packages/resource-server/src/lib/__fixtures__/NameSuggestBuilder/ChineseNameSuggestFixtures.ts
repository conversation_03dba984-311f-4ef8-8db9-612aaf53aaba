export const nameSearchResult = {
  track_total_hits: true,
  _source: [
    "id",
    "h1dn_id",
    "name_cmn",
    "specialty_cmn",
    "designations",
    "affiliations",
    "locations",
    "indications"
  ],
  from: 0,
  size: 5,
  sort: [
    { "lastName_cmn.sort": { order: "asc" } },
    { "firstName_cmn.sort": { order: "asc" } }
  ],
  query: {
    bool: {
      should: [{ prefix: { name_cmn: "金" } }],
      minimum_should_match: 1,
      filter: [{ term: { projectIds: "abc123" } }]
    }
  }
};
