import * as _ from "lodash";

import {
  QueryDslQueryContainer,
  SearchRequest
} from "@elastic/elasticsearch/lib/api/types";
import { Service } from "typedi";

@Service()
export class CitationProfileQueryBuilder {
  buildCitedByPapersQuery(
    personId: string,
    dateRange: { min: number; max: number | null },
    isCitedByPapers: boolean,
    projectId: string,
    terms?: QueryDslQueryContainer
  ): SearchRequest {
    const nestedCitationField = isCitedByPapers
      ? "peopleCited"
      : "citedByPeople";
    const innerFunctionMust: Array<QueryDslQueryContainer> = [
      {
        term: {
          [`publications.${nestedCitationField}`]: personId
        }
      },
      buildPublicationDateYearPathRangeFilter(
        dateRange.min,
        dateRange.max ?? undefined
      )
    ];

    if (terms) {
      innerFunctionMust.push(terms);
    }

    const field = isCitedByPapers ? "peoplesCited" : "citedByPeoples";
    const filters: Array<QueryDslQueryContainer> = [
      {
        term: {
          projectIds: projectId
        }
      },
      {
        term: {
          [field]: personId
        }
      },
      {
        bool: {
          must_not: {
            term: {
              id: personId
            }
          }
        }
      }
    ];

    return {
      _source_includes: [
        "id",
        "institutions",
        "name_eng",
        "name_jpn",
        "name_cmn",
        "microBloggingTotal",
        "publicationCount",
        "citationTotal",
        "trialCount",
        "congressCount",
        "paymentTotal"
      ],
      size: 10,
      query: {
        bool: {
          filter: filters,
          must: [
            {
              nested: {
                path: "publications",
                score_mode: "sum",
                inner_hits: {
                  size: 3,
                  _source: false,
                  docvalue_fields: [
                    "publications.id",
                    "publications.title_eng.keyword",
                    "publications.title_jpn.keyword",
                    "publications.title_cmn.keyword",
                    "publications.microBloggingCount",
                    "publications.citationCount",
                    "publications.datePublished",
                    "publications.journalName_eng",
                    "publications.journalName_jpn",
                    "publications.journalName_cmn"
                  ]
                },
                query: {
                  constant_score: {
                    filter: {
                      bool: {
                        must: innerFunctionMust
                      }
                    }
                  }
                }
              }
            }
          ]
        }
      }
    };
  }
}

function buildPublicationDateYearPathRangeFilter(
  minDate: number,
  maxDate?: number
): QueryDslQueryContainer {
  const withinPublicationDateRange: QueryDslQueryContainer = {
    range: {
      ["publications.datePublished"]: {
        gte: minDate,
        lte: maxDate
      }
    }
  };

  const noPublicationDate = {
    bool: {
      must_not: {
        exists: {
          field: "publications.datePublished"
        }
      }
    }
  };

  return {
    bool: {
      should: [withinPublicationDateRange, noPublicationDate],
      minimum_should_match: 1
    }
  };
}
