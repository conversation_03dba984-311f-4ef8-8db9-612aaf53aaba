import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { faker } from "@faker-js/faker";
import { CitationProfileQueryBuilder } from "./CitationProfileBuilder";

describe("buildCitedByPapersQuery", () => {
  describe("term types", () => {
    it("no terms", () => {
      const personId = faker.datatype.uuid();
      const dateRange = {
        min: faker.datatype.datetime().getTime(),
        max: null
      };
      const isCitedByPapers = false;
      const projectId = faker.datatype.string();
      const terms = undefined;

      const citationProfileQueryBuilder = new CitationProfileQueryBuilder();

      const query = citationProfileQueryBuilder.buildCitedByPapersQuery(
        personId,
        dateRange,
        isCitedByPapers,
        projectId,
        terms
      );

      expect(query).toEqual({
        _source_includes: [
          "id",
          "institutions",
          "name_eng",
          "name_jpn",
          "name_cmn",
          "microBloggingTotal",
          "publicationCount",
          "citationTotal",
          "trialCount",
          "congressCount",
          "paymentTotal"
        ],
        size: 10,
        query: {
          bool: {
            filter: [
              {
                term: {
                  projectIds: projectId
                }
              },
              {
                term: {
                  citedByPeoples: personId
                }
              },
              {
                bool: {
                  must_not: {
                    term: {
                      id: personId
                    }
                  }
                }
              }
            ],
            must: [
              {
                nested: {
                  path: "publications",
                  score_mode: "sum",
                  inner_hits: {
                    size: 3,
                    _source: false,
                    docvalue_fields: [
                      "publications.id",
                      "publications.title_eng.keyword",
                      "publications.title_jpn.keyword",
                      "publications.title_cmn.keyword",
                      "publications.microBloggingCount",
                      "publications.citationCount",
                      "publications.datePublished",
                      "publications.journalName_eng",
                      "publications.journalName_jpn",
                      "publications.journalName_cmn"
                    ]
                  },
                  query: {
                    constant_score: {
                      filter: {
                        bool: {
                          must: [
                            {
                              term: {
                                "publications.citedByPeople": personId
                              }
                            },
                            {
                              bool: {
                                minimum_should_match: 1,
                                should: [
                                  {
                                    range: {
                                      "publications.datePublished": {
                                        gte: dateRange.min
                                      }
                                    }
                                  },
                                  {
                                    bool: {
                                      must_not: {
                                        exists: {
                                          field: "publications.datePublished"
                                        }
                                      }
                                    }
                                  }
                                ]
                              }
                            }
                          ]
                        }
                      }
                    }
                  }
                }
              }
            ]
          }
        }
      });
    });

    it("should build query with supplied queryContainer instead of terms", async () => {
      const personId = faker.datatype.uuid();
      const dateRange = {
        min: faker.datatype.datetime().getTime(),
        max: null
      };
      const isCitedByPapers = faker.datatype.boolean();
      const projectId = faker.datatype.string();
      const terms: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };

      const citationProfileQueryBuilder = new CitationProfileQueryBuilder();

      const query = citationProfileQueryBuilder.buildCitedByPapersQuery(
        personId,
        dateRange,
        isCitedByPapers,
        projectId,
        terms
      );

      expect(query).toEqual(
        expect.objectContaining({
          query: {
            bool: expect.objectContaining({
              must: [
                {
                  nested: expect.objectContaining({
                    query: {
                      constant_score: {
                        filter: {
                          bool: {
                            must: expect.arrayContaining([terms])
                          }
                        }
                      }
                    }
                  })
                }
              ]
            })
          }
        })
      );
    });
  });

  describe("date range filter", () => {
    it("dateRange.max supplied should add range less-than-or-equal value", async () => {
      const personId = faker.datatype.uuid();
      const dateRange = {
        min: faker.datatype.datetime().getTime(),
        max: faker.datatype.datetime().getTime()
      };
      const isCitedByPapers = faker.datatype.boolean();
      const projectId = faker.datatype.string();
      const terms = undefined;

      const citationProfileQueryBuilder = new CitationProfileQueryBuilder();

      const query = citationProfileQueryBuilder.buildCitedByPapersQuery(
        personId,
        dateRange,
        isCitedByPapers,
        projectId,
        terms
      );

      expect(query).toEqual(
        expect.objectContaining({
          query: {
            bool: expect.objectContaining({
              must: [
                {
                  nested: expect.objectContaining({
                    query: {
                      constant_score: {
                        filter: {
                          bool: {
                            must: expect.arrayContaining([
                              {
                                bool: {
                                  minimum_should_match: 1,
                                  should: [
                                    {
                                      range: {
                                        "publications.datePublished": {
                                          gte: dateRange.min,
                                          lte: dateRange.max
                                        }
                                      }
                                    },
                                    {
                                      bool: {
                                        must_not: {
                                          exists: {
                                            field: "publications.datePublished"
                                          }
                                        }
                                      }
                                    }
                                  ]
                                }
                              }
                            ])
                          }
                        }
                      }
                    }
                  })
                }
              ]
            })
          }
        })
      );
    });
  });

  describe("isCitedByPapers variations", () => {
    it("true should use peoplesCited/peopleCited fields", () => {
      const personId = faker.datatype.uuid();
      const dateRange = {
        min: faker.datatype.datetime().getTime(),
        max: null
      };
      const isCitedByPapers = true;
      const projectId = faker.datatype.string();
      const terms = undefined;

      const citationProfileQueryBuilder = new CitationProfileQueryBuilder();

      const query = citationProfileQueryBuilder.buildCitedByPapersQuery(
        personId,
        dateRange,
        isCitedByPapers,
        projectId,
        terms
      );

      expect(query).toEqual(
        expect.objectContaining({
          query: {
            bool: {
              filter: expect.arrayContaining([
                {
                  term: {
                    peoplesCited: personId
                  }
                }
              ]),
              must: [
                {
                  nested: expect.objectContaining({
                    query: {
                      constant_score: {
                        filter: {
                          bool: {
                            must: expect.arrayContaining([
                              {
                                term: {
                                  "publications.peopleCited": personId
                                }
                              }
                            ])
                          }
                        }
                      }
                    }
                  })
                }
              ]
            }
          }
        })
      );
    });

    it("false should use citedByPeoples/citedByPeople fields", () => {
      const personId = faker.datatype.uuid();
      const dateRange = {
        min: faker.datatype.datetime().getTime(),
        max: null
      };
      const isCitedByPapers = false;
      const projectId = faker.datatype.string();
      const terms = undefined;

      const citationProfileQueryBuilder = new CitationProfileQueryBuilder();

      const query = citationProfileQueryBuilder.buildCitedByPapersQuery(
        personId,
        dateRange,
        isCitedByPapers,
        projectId,
        terms
      );

      expect(query).toEqual(
        expect.objectContaining({
          query: {
            bool: {
              filter: expect.arrayContaining([
                {
                  term: {
                    citedByPeoples: personId
                  }
                }
              ]),
              must: [
                {
                  nested: expect.objectContaining({
                    query: {
                      constant_score: {
                        filter: {
                          bool: {
                            must: expect.arrayContaining([
                              {
                                term: {
                                  "publications.citedByPeople": personId
                                }
                              }
                            ])
                          }
                        }
                      }
                    }
                  })
                }
              ]
            }
          }
        })
      );
    });
  });
});
