import { buildNameSuggestQuery } from "./ChineseNameSuggestBuilder";
import { nameSearchResult } from "../__fixtures__/NameSuggestBuilder/ChineseNameSuggestFixtures";
import {
  featureFlagDefaults,
  NameSuggestFeatureFlags
} from "../../services/NameSuggestResourceService";

function mockFeatureFlagsValue(
  overrides: Partial<NameSuggestFeatureFlags> = {}
) {
  return Object.entries(featureFlagDefaults).reduce((acc, [key, value]) => {
    if (typeof overrides[key as keyof NameSuggestFeatureFlags] !== undefined) {
      acc[key] = overrides[key as keyof NameSuggestFeatureFlags]!;
    } else {
      acc[key] = value.default;
    }
    return acc;
  }, {} as Record<string, boolean>);
}

describe("ChineseNameSuggestBuilder", () => {
  let featureFlags: NameSuggestFeatureFlags;

  beforeAll(() => {
    featureFlags = mockFeatureFlagsValue() as NameSuggestFeatureFlags;
  });
  it("should build a one term query", async () => {
    const input = {
      query: ["金"],
      limit: 5,
      offset: 0
    };
    const projectId = "abc123";
    const actual = await buildNameSuggestQuery({
      input,
      projectId,
      featureFlags
    });
    expect(actual).toEqual(nameSearchResult);
  });

  it("should return undefined when no query is provided", async () => {
    const input = {
      limit: 5,
      offset: 0
    };
    const projectId = "abc123";
    const actual = await buildNameSuggestQuery({
      input,
      projectId,
      featureFlags
    });
    expect(actual).toBeUndefined();
  });
});
