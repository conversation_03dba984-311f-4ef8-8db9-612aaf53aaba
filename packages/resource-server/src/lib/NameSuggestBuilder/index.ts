import { KolNameSuggestionInput } from "@h1nyc/search-sdk";
import { NameSuggestFeatureFlags } from "../../services/NameSuggestResourceService";
import { buildNameSuggestQuery as buildChineseNameSuggestQuery } from "./ChineseNameSuggestBuilder";
import { buildNameSuggestQuery as buildJapaneseNameSuggestQuery } from "./JapaneseNameSuggestBuilder";
import { buildNameSuggestQuery as buildNameSuggestWithProminence } from "./EnglishNameSuggestBuilderWithProminence";

export type NameSuggestBuilder = (args: NameSuggestBuilderArg) => Promise<any>;

export interface NameSuggestBuilderArg {
  input: KolNameSuggestionInput;
  projectId: string;
  featureFlags: NameSuggestFeatureFlags;
}

export const getNameSuggestBuilder = (
  languageCode: string
): NameSuggestBuilder => {
  switch (languageCode) {
    case "cmn":
      return buildChineseNameSuggestQuery;
    case "jpn":
      return buildJapaneseNameSuggestQuery;
    default:
      return buildNameSuggestWithProminence;
  }
};
