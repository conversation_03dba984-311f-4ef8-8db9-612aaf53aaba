import { NameSuggestBuilderArg } from ".";

export const buildNameSuggestQuery = async (args: NameSuggestBuilderArg) => {
  const { input, projectId } = args;
  const { query, limit, offset } = input;

  if (!query || query.length === 0) {
    return;
  }

  return {
    track_total_hits: true,
    _source: [
      "id",
      "h1dn_id",
      "name_jpn",
      "specialty_jpn",
      "designations",
      "affiliations",
      "locations",
      "indications"
    ],
    from: offset,
    size: limit,
    sort: [
      {
        "lastName_jpn.sort": {
          order: "asc"
        }
      },
      {
        "firstName_jpn.sort": {
          order: "asc"
        }
      }
    ],
    query: {
      bool: {
        should: [
          {
            prefix: {
              name_jpn: query[0]
            }
          }
        ],
        minimum_should_match: 1,
        filter: [
          {
            term: {
              projectIds: projectId
            }
          }
        ]
      }
    }
  };
};
