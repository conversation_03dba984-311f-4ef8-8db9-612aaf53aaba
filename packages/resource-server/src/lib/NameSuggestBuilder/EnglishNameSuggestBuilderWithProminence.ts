import { QueryDslFunctionScoreContainer } from "@elastic/elasticsearch/lib/api/types";
import { NameSuggestBuilderArg } from ".";
import { PROMINENCE_SCORE_FUNCTION } from "../../services/queryBuilders/NameSearchBuilder";
import { NameSuggestFeatureFlags } from "../../services/NameSuggestResourceService";

const FUZZINESS = "AUTO:4,7";
const MINIMUM_SHOULD_MATCH_CRITERION = "2<-1 4<-2";
const BOOST_SAUT_QUERY = 1.0;
const BOOST_SAUT_REVERSE_QUERY = 0.1;
const BOOST_SAUT_OTHER_PERMUTATIONS = 0.1;

const NAME_SEARCH_SORT = [
  {
    _score: "desc"
  },
  {
    _script: {
      type: "number",
      script: {
        lang: "painless",
        source: "doc['lastName_eng.sort'].value.length()"
      },
      order: "asc"
    }
  },
  {
    "lastName_eng.sort": "asc"
  },
  {
    _script: {
      type: "number",
      script: {
        lang: "painless",
        source: "doc['firstName_eng.sort'].value.length()"
      },
      order: "asc"
    }
  },
  {
    "firstName_eng.sort": "asc"
  }
];

const PROMINENCE_SCORE_FUNCTION_FOR_SUGGEST_FUZZY_MATCH_QUERY: Readonly<
  Array<QueryDslFunctionScoreContainer>
> = [
  {
    script_score: {
      script: {
        source: "0.1*Math.tanh(doc['presentWorkInstitutionCount'].value)"
      }
    }
  },
  {
    script_score: {
      script: {
        source: "0.3*Math.log(doc['publicationCount'].value + 1)"
      }
    }
  },
  {
    script_score: {
      script: {
        source: "0.1*Math.log(doc['trialCount'].value + 1)"
      }
    }
  },
  {
    script_score: {
      script: {
        source: "0.1*Math.log(doc['congressCount'].value + 1)"
      }
    }
  }
];

const sourceFields = [
  "id",
  "h1dn_id",
  "name_eng",
  "specialty_eng",
  "designations",
  "affiliations",
  "locations",
  "indications"
];

export const buildNameSuggestQuery = async (args: NameSuggestBuilderArg) => {
  const { input, projectId, featureFlags } = args;
  const { query, limit, offset } = input;
  let body: any;

  if (query) {
    body = buildNameSuggestWithProminence(
      featureFlags,
      query,
      projectId,
      limit,
      offset
    );
  }
  return body;
};

function getNameSuggestSAUTMatchClause(query: string, boost: number) {
  return {
    multi_match: {
      query: query,
      type: "bool_prefix",
      operator: "AND",
      boost,
      fields: [
        "name_eng.autocomplete_search",
        "firstLastName_eng.autocomplete_search._2gram",
        "firstLastName_eng.autocomplete_search._3gram"
      ]
    }
  };
}

function getNameSuggestExactMatchClause(query: string) {
  return {
    multi_match: {
      query: query,
      operator: "AND",
      fields: ["name_eng", "firstLastName_eng^2"]
    }
  };
}

function getFirstInitialMiddelLastNameMatchClause(query: string) {
  return {
    multi_match: {
      query: query,
      operator: "AND",
      type: "cross_fields",
      fields: ["firstName_eng", "middleName_eng.initial", "lastName_eng"]
    }
  };
}

function getNameSuggestFuzzyMatchClause(query: string) {
  return {
    match: {
      firstLastName_eng: {
        query: query,
        prefix_length: 1,
        operator: "OR",
        fuzziness: FUZZINESS,
        boost: 0.01,
        minimum_should_match: MINIMUM_SHOULD_MATCH_CRITERION
      }
    }
  };
}

function getNameSuggestFilterClause(projectId: string) {
  return [
    {
      term: {
        projectIds: projectId
      }
    }
  ];
}

export const buildNameSuggestWithProminence = (
  featureFlags: NameSuggestFeatureFlags,
  queries: string[],
  projectId: string,
  limit = 5,
  offset = 0
) => {
  const query = queries[0].trim();

  const request = {
    _source: sourceFields,
    from: offset,
    size: limit,
    query: getNameSuggestQueryWithFuzzyMatch(featureFlags, query, projectId),
    sort: NAME_SEARCH_SORT
  };

  return request;
};

function getNameSuggestQueryWithFuzzyMatch(
  featureFlags: NameSuggestFeatureFlags,
  query: string,
  projectId: string
) {
  return {
    bool: {
      should: [
        {
          function_score: {
            query: {
              bool: {
                should: [
                  ...getNameSuggestSAUTMatchClauseWithFeatureFlag(
                    featureFlags,
                    query,
                    BOOST_SAUT_QUERY
                  ),
                  getNameSuggestExactMatchClause(query),
                  // This specifically handles scenarios like Christian A Thomas for result Christian Anton Thomas (This scenario is not handled by SAUT)
                  getFirstInitialMiddelLastNameMatchClause(query)
                ],
                minimum_should_match: 1
              }
            },
            functions: PROMINENCE_SCORE_FUNCTION,
            boost_mode: "sum",
            score_mode: "sum"
          }
        },
        {
          function_score: {
            query: {
              bool: {
                must: [getNameSuggestFuzzyMatchClause(query)]
              }
            },
            functions: PROMINENCE_SCORE_FUNCTION_FOR_SUGGEST_FUZZY_MATCH_QUERY,
            boost_mode: "sum",
            score_mode: "sum"
          }
        }
      ],
      minimum_should_match: 1,
      filter: getNameSuggestFilterClause(projectId)
    }
  };
}

/**
 * @description Get the match clause for SAUT considering the feature flags
 * @param featureFlags
 * @param query
 * @param boost
 * @returns {Array<QueryDslFunctionScoreContainer>}
 */
function getNameSuggestSAUTMatchClauseWithFeatureFlag(
  featureFlags: NameSuggestFeatureFlags,
  originalQuery: string,
  boost: number
) {
  const finalClause = [];
  if (featureFlags.enableNameSuggestWithAllPermutations) {
    //for the original query use boost 1.0 and for rest of the permutations use 0.1
    const allPermutations = getAllWordPermutationEnd(originalQuery);
    allPermutations.forEach((permutation, index) => {
      const boostValue =
        permutation === originalQuery
          ? BOOST_SAUT_QUERY
          : BOOST_SAUT_OTHER_PERMUTATIONS;
      finalClause.push(getNameSuggestSAUTMatchClause(permutation, boostValue));
    });
  } else {
    // Assuming that Name search input will be in either of following formats:
    // 1. First Name + Last Name
    // 2. Last Name + First Name
    // There are multiple cases where nicknames are prefix of first names and to handle those queries we also want to
    // search for reverse of input query (as {Nick name + Last name} variation is not handled by bool_prefix query which only
    // considers last token for prefix match)
    finalClause.push(
      getNameSuggestSAUTMatchClause(originalQuery, BOOST_SAUT_QUERY)
    );
    finalClause.push(
      getNameSuggestSAUTMatchClause(
        reverseQueryTermOrder(originalQuery),
        BOOST_SAUT_REVERSE_QUERY
      )
    );
  }

  return finalClause;
}

export function reverseQueryTermOrder(query: string) {
  return query.split(" ").reverse().join(" ");
}

/**
 * @description Get all permutations of a query with every word at the end
 * @param query
 * @returns {string[]}
 */
export function getAllWordPermutationEnd(query: string) {
  const words = query.split(" ");
  const permutations = [];
  for (let i = 0; i < words.length; i++) {
    const permutation = [...words];
    permutation.splice(i, 1);
    permutation.push(words[i]);
    permutations.push(permutation.join(" "));
  }
  return permutations;
}
