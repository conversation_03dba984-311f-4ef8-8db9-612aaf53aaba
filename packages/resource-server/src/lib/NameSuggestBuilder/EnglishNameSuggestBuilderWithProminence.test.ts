import { faker } from "@faker-js/faker";
import {
  buildNameSuggestQuery,
  reverseQueryTermOrder,
  getAllWordPermutationEnd
} from "./EnglishNameSuggestBuilderWithProminence";
import {
  prominenceQueryResultWithFuzzyMatch,
  generateProminenceQueryWithPermutationFlagEnabled,
  generateProminenceQueryWithPermutationFlagDisabled
} from "../__fixtures__/NameSuggestBuilder/EnglishNameSuggestFixtures";
import {
  featureFlagDefaults,
  NameSuggestFeatureFlags
} from "../../services/NameSuggestResourceService";

function mockFeatureFlagsValue(
  overrides: Partial<NameSuggestFeatureFlags> = {}
) {
  return Object.entries(featureFlagDefaults).reduce((acc, [key, value]) => {
    if (typeof overrides[key as keyof NameSuggestFeatureFlags] !== undefined) {
      acc[key] = overrides[key as keyof NameSuggestFeatureFlags]!;
    } else {
      acc[key] = value.default;
    }
    return acc;
  }, {} as Record<string, boolean>);
}

describe("EnglishNameSuggestBuilderWithProminence", () => {
  let featureFlags: NameSuggestFeatureFlags;

  beforeAll(() => {
    featureFlags = mockFeatureFlagsValue() as NameSuggestFeatureFlags;
  });

  it("should return undefined", async () => {
    const input = {
      limit: 5,
      offset: 0
    };
    const projectId = "abc123";
    const actual = await buildNameSuggestQuery({
      input,
      projectId,
      featureFlags
    });

    expect(actual).toBeUndefined();
  });

  it("should build a query with prominence score and handle misspellings", async () => {
    const input = {
      query: ["Elias Jabour"],
      limit: 5,
      offset: 0
    };
    const projectId = "abc123";
    const actual = await buildNameSuggestQuery({
      input,
      projectId,
      featureFlags
    });
    expect(actual).toEqual(prominenceQueryResultWithFuzzyMatch);
  });

  it("should reverse an input query string", async () => {
    const input = "Niro M A Narendran";
    const reverseInput = "Narendran A M Niro";

    expect(reverseInput).toEqual(reverseQueryTermOrder(input));
  });

  it("should return all permutations such that each for is at last exactly once", async () => {
    const input = "Niro M A Narendran";
    const expectedPermutations = [
      "M A Narendran Niro",
      "Niro A Narendran M",
      "Niro M Narendran A",
      "Niro M A Narendran"
    ];

    const permutations = getAllWordPermutationEnd(input);

    expect(expectedPermutations).toEqual(permutations);
  });

  it("should build query when enableNameSuggestWithAllPermutations is on", async () => {
    const input = {
      query: [faker.datatype.string()],
      limit: 5,
      offset: 0,
      language: "eng"
    };
    const projectId = faker.datatype.string();
    const featureFlagsWithPermutationFlagEnabled = mockFeatureFlagsValue({
      enableNameSuggestWithAllPermutations: true
    }) as NameSuggestFeatureFlags;

    const expectedEsQuery = generateProminenceQueryWithPermutationFlagEnabled(
      input.query[0].trim(),
      projectId
    );

    const actual = await buildNameSuggestQuery({
      input,
      projectId,
      featureFlags: featureFlagsWithPermutationFlagEnabled
    });

    expect(actual).toEqual(expectedEsQuery);
  });

  it("should build query when enableNameSuggestWithAllPermutations is off", async () => {
    const input = {
      query: [faker.datatype.string()],
      limit: 5,
      offset: 0,
      language: "eng"
    };
    const projectId = faker.datatype.string();
    const featureFlagsWithPermutationFlagDisabled = mockFeatureFlagsValue({
      enableNameSuggestWithAllPermutations: false
    }) as NameSuggestFeatureFlags;

    const expectedEsQuery = generateProminenceQueryWithPermutationFlagDisabled(
      input.query[0].trim(),
      projectId
    );

    const actual = await buildNameSuggestQuery({
      input,
      projectId,
      featureFlags: featureFlagsWithPermutationFlagDisabled
    });

    expect(actual).toEqual(expectedEsQuery);
  });
});
