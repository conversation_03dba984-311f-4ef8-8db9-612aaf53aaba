import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { faker } from "@faker-js/faker";
import _ from "lodash";
import {
  buildAllProfileMetricsQuery,
  buildPaginatedProfileSearchQueryForAsset,
  buildProfileMetricsQuery,
  buildProfileSearchQuery,
  buildSortedProfileSearchQuery,
  ProfileSearchAsset
} from "./ProfileSearchBuilder";
import { ProfileFilterValue, ProfileFilterValueType } from "@h1nyc/search-sdk";

describe("buildSortedProfileSearchQuery()", () => {
  it("should use QueryDslQueryContainer terms to build a query with requested agg", () => {
    const peopleIds = [faker.datatype.uuid(), faker.datatype.uuid()];
    const assets = [
      ProfileSearchAsset.ClinicalTrials,
      ProfileSearchAsset.Congresses,
      ProfileSearchAsset.DrgDiagnoses,
      ProfileSearchAsset.DrgProcedures,
      ProfileSearchAsset.Payments,
      ProfileSearchAsset.Publications
    ];
    const size = faker.datatype.number();
    const terms: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>> = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.DrgDiagnoses]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.DrgProcedures]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Payments]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      }
    };

    const query = buildSortedProfileSearchQuery(peopleIds, assets, size, {
      terms
    });

    expect(query).toEqual({
      _source: "_none",
      size: 0,
      query: {
        bool: {
          filter: {
            terms: { id: peopleIds }
          }
        }
      },
      aggs: {
        people: {
          terms: {
            field: "id",
            size: peopleIds.length
          },
          aggs: {
            publications: {
              nested: {
                path: "publications"
              },
              aggs: {
                publicationFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.Publications]
                        }
                      }
                    ]
                  }
                })
              }
            },
            trials: {
              nested: {
                path: "trials"
              },
              aggs: {
                trialFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.ClinicalTrials]
                        }
                      }
                    ]
                  }
                })
              }
            },
            congresses: {
              nested: {
                path: "congress"
              },
              aggs: {
                congressFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.Congresses]
                        }
                      }
                    ]
                  }
                })
              }
            }
          }
        }
      }
    });
  });
});

describe("buildProfileSearchQuery()", () => {
  it("throws an error when term and filters are missing", () => {
    expect(() =>
      buildProfileSearchQuery(["4667519"], [ProfileSearchAsset.Publications])
    ).toThrow();
  });

  it("should use QueryDslQueryContainer terms to build a query with requested agg", () => {
    const peopleIds = [faker.datatype.uuid(), faker.datatype.uuid()];

    const terms: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>> = {
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Payments]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      }
    };

    const query = buildProfileSearchQuery(
      peopleIds,
      [
        ProfileSearchAsset.Publications,
        ProfileSearchAsset.ClinicalTrials,
        ProfileSearchAsset.Congresses,
        ProfileSearchAsset.Payments
      ],
      terms
    );

    expect(query).toEqual({
      _source: "_none",
      size: 0,
      query: {
        bool: {
          filter: {
            terms: { id: peopleIds }
          }
        }
      },
      aggs: {
        people: {
          terms: {
            field: "id",
            size: peopleIds.length
          },
          aggs: {
            publications: {
              nested: {
                path: "publications"
              },
              aggs: {
                publicationFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.Publications]
                        }
                      }
                    ]
                  }
                })
              }
            },
            trials: {
              nested: {
                path: "trials"
              },
              aggs: {
                trialFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.ClinicalTrials]
                        }
                      }
                    ]
                  }
                })
              }
            },
            congresses: {
              nested: {
                path: "congress"
              },
              aggs: {
                congressFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.Congresses]
                        }
                      }
                    ]
                  }
                })
              }
            },
            payments: {
              nested: {
                path: "payments"
              },
              aggs: {
                paymentFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.Payments]
                        }
                      }
                    ]
                  }
                })
              }
            }
          }
        }
      }
    });
  });
});

describe("buildProfileMetricsQuery()", () => {
  it("should use QueryDslQueryContainer terms to build a query with requested agg", () => {
    const peopleIds = [faker.datatype.uuid(), faker.datatype.uuid()];
    const projectId = faker.datatype.uuid();

    const terms: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>> = {
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      }
    };

    const query = buildProfileMetricsQuery(
      peopleIds,
      [
        ProfileSearchAsset.Publications,
        ProfileSearchAsset.ClinicalTrials,
        ProfileSearchAsset.Congresses
      ],
      projectId,
      terms
    );

    expect(query).toEqual({
      _source: "_none",
      size: 0,
      query: {
        bool: {
          filter: [
            expect.termsQuery("id", peopleIds),
            expect.projectIdFilter(projectId)
          ]
        }
      },
      aggs: {
        people: {
          terms: {
            field: "id",
            size: peopleIds.length
          },
          aggs: {
            publications: {
              nested: {
                path: "publications"
              },
              aggs: {
                publicationFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.Publications]
                        }
                      }
                    ]
                  }
                })
              }
            },
            trials: {
              nested: {
                path: "trials"
              },
              aggs: {
                trialFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.ClinicalTrials]
                        }
                      }
                    ]
                  }
                })
              }
            },
            congresses: {
              nested: {
                path: "congress"
              },
              aggs: {
                congressFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.Congresses]
                        }
                      }
                    ]
                  }
                })
              }
            }
          }
        }
      }
    });
  });
});

describe("buildAllProfileMetricsQuery()", () => {
  it("should use QueryDslQueryContainer terms to build a query with requested agg", () => {
    const peopleIds = [faker.datatype.uuid(), faker.datatype.uuid()];
    const projectId = faker.datatype.uuid();

    const terms: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>> = {
      [ProfileSearchAsset.Publications]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Congresses]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.DrgProcedures]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.DrgDiagnoses]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      },
      [ProfileSearchAsset.Payments]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      }
    };

    const query = buildAllProfileMetricsQuery(
      peopleIds,
      [
        ProfileSearchAsset.Publications,
        ProfileSearchAsset.ClinicalTrials,
        ProfileSearchAsset.Congresses,
        ProfileSearchAsset.DrgProcedures,
        ProfileSearchAsset.DrgDiagnoses,
        ProfileSearchAsset.Payments
      ],
      projectId,
      {
        terms
      }
    );

    expect(query).toEqual({
      _source: "_none",
      size: 0,
      query: {
        bool: {
          filter: [
            expect.termsQuery("id", peopleIds),
            expect.projectIdFilter(projectId)
          ]
        }
      },
      aggs: {
        people: {
          terms: {
            field: "id",
            size: peopleIds.length
          },
          aggs: {
            publications: {
              nested: {
                path: "publications"
              },
              aggs: {
                publicationFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.Publications]
                        }
                      }
                    ]
                  }
                })
              }
            },
            trials: {
              nested: {
                path: "trials"
              },
              aggs: {
                trialFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.ClinicalTrials]
                        }
                      }
                    ]
                  }
                })
              }
            },
            congresses: {
              nested: {
                path: "congress"
              },
              aggs: {
                congressFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.Congresses]
                        }
                      }
                    ]
                  }
                })
              }
            },
            diagnoses: {
              nested: {
                path: "DRG_diagnoses"
              },
              aggs: {
                diagnosisFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.DrgDiagnoses]
                        }
                      }
                    ]
                  }
                })
              }
            },
            procedures: {
              nested: {
                path: "DRG_procedures"
              },
              aggs: {
                procedureFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.DrgProcedures]
                        }
                      }
                    ]
                  }
                })
              }
            },
            payments: {
              nested: {
                path: "payments"
              },
              aggs: {
                paymentFilters: expect.objectContaining({
                  filters: {
                    filters: [
                      {
                        bool: {
                          must: [terms.Payments]
                        }
                      }
                    ]
                  }
                })
              }
            }
          }
        }
      }
    });
  });
});

describe("buildPaginatedProfileSearchQueryForAsset()", () => {
  it("returns null when called with any asset besides trials", () => {
    const personId = faker.datatype.uuid();
    const projectId = faker.datatype.uuid();

    const terms: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>> = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      }
    };
    const page = {
      limit: faker.datatype.number(),
      offset: faker.datatype.number()
    };
    const filters: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: Date.now(),
          max: Date.parse(faker.date.future().toDateString())
        }
      }
    ];

    for (const asset of Object.values(ProfileSearchAsset)) {
      if (asset === ProfileSearchAsset.ClinicalTrials) {
        continue;
      }

      expect(
        buildPaginatedProfileSearchQueryForAsset(
          personId,
          projectId,
          asset,
          terms,
          filters,
          page
        )
      ).toBeNull();
    }
  });

  it("should build a query for clinical trials", () => {
    const personId = faker.datatype.uuid();
    const projectId = faker.datatype.uuid();

    const terms: Partial<Record<ProfileSearchAsset, QueryDslQueryContainer>> = {
      [ProfileSearchAsset.ClinicalTrials]: {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      }
    };
    const page = {
      limit: faker.datatype.number(),
      offset: faker.datatype.number()
    };
    const filters: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: Date.now(),
          max: Date.parse(faker.date.future().toDateString())
        }
      }
    ];

    expect(
      buildPaginatedProfileSearchQueryForAsset(
        personId,
        projectId,
        ProfileSearchAsset.ClinicalTrials,
        terms,
        filters,
        page
      )
    ).toBeDefined();
  });
});
