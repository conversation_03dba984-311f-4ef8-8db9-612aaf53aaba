import { faker } from "@faker-js/faker";
import { buildProfileSummaryMetricsQuery } from "./ProfileSummaryBuilder";

describe("buildProfileSummaryMetricsQuery()", () => {
  it("builds a query with people ids", () => {
    const peopleIds = ["4667519", "4938278"];
    const projectId = faker.datatype.uuid();
    const query = buildProfileSummaryMetricsQuery(peopleIds, projectId);

    expect(query.query).toEqual({
      bool: {
        filter: [
          expect.termsQuery("id", peopleIds),
          expect.projectIdFilter(projectId)
        ]
      }
    });
    expect(query.aggs).toEqual({
      people: expect.objectContaining({
        terms: {
          field: "id",
          size: 2
        },
        aggs: {
          publications: {
            nested: {
              path: "publications"
            },
            aggs: {
              oldestDate: {
                min: {
                  field: "publications.datePublished"
                }
              },
              last12MonthsCount: {
                filters: {
                  filters: [
                    {
                      bool: {
                        must: [
                          {
                            range: {
                              "publications.datePublished": {
                                gte: expect.anything()
                              }
                            }
                          }
                        ]
                      }
                    }
                  ]
                },
                aggs: {}
              }
            }
          },
          trials: {
            nested: {
              path: "trials"
            },
            aggs: {
              oldestDate: {
                min: {
                  field: "trials.completionDate"
                }
              },
              inProgressCount: {
                filters: {
                  filters: [
                    {
                      bool: {
                        must: [
                          {
                            terms: {
                              "trials.status_eng": [
                                "Not yet recruiting",
                                "Recruiting",
                                "Enrolling by invitation",
                                "Active, not recruiting",
                                "Suspended"
                              ]
                            }
                          }
                        ]
                      }
                    }
                  ]
                },
                aggs: {}
              }
            }
          },
          congresses: {
            nested: {
              path: "congress"
            },
            aggs: {
              oldestDate: {
                min: {
                  field: "congress.endDate"
                }
              }
            }
          },
          payments: {
            nested: {
              path: "payments"
            },
            aggs: {
              byCompany: {
                aggs: {
                  sumByCompany: {
                    sum: {
                      field: "payments.amount"
                    }
                  }
                },
                terms: {
                  field: "payments.payerCompany",
                  order: {
                    sumByCompany: "desc"
                  },
                  size: 3
                }
              },
              totalPaymentsAmount: {
                sum: {
                  field: "payments.amount"
                }
              }
            }
          }
        }
      })
    });
  });
});
