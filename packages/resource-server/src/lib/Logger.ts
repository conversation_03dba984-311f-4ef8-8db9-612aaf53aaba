import _ from "lodash";
import pino from "pino";

export type Logger = pino.Logger;

const PACKAGE_NAME = process.env.npm_package_name;
const DEFAULT_LOG_LEVEL: pino.Level = "info";

export function createLogger(
  service: string | { constructor: { name: string } } = "",
  level: pino.Level = (process.env.LOG_LEVEL! as pino.Level) ||
    DEFAULT_LOG_LEVEL
) {
  const name = _.isString(service) ? service : service.constructor.name;
  const packageQualfiedName = [PACKAGE_NAME, name].join("/");

  const logger = pino({
    level,
    name: packageQualfiedName
  });

  if (service.constructor?.name && logger.isLevelEnabled("debug")) {
    logger.debug(`Instantiated ${name}`);
  }

  return logger;
}
