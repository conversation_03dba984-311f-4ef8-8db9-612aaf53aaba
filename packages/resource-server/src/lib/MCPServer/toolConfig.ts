import { z } from "zod";

export const LIST_INDICES_TOOL_CONFIG = {
  name: "list_indices",
  description: "List all available Elasticsearch indices",
  validator: {}
};

export const GET_MAPPING_TOOL_CONFIG = {
  name: "get_mappings",
  description: "Get field mappings for a specific Elasticsearch index",
  validator: {
    index: z
      .string()
      .trim()
      .min(1, "Index name is required")
      .describe("Name of the Elasticsearch index to get mappings for")
  }
};

export const ELASTIC_SEARCH_TOOL_CONFIG = {
  name: "search_elasticsearch",
  description:
    "Perform an Elasticsearch search with the provided query DSL. Highlights are always enabled. It takes in  a index of type string and a queryBody which is a object.",
  validator: {
    index: z
      .string()
      .trim()
      .min(1, "index name is required")
      .describe("Name of the Elasticsearch index to search"),

    queryBody: z
      .record(z.any())
      .refine(
        (val) => {
          try {
            JSON.parse(JSON.stringify(val));
            return true;
          } catch (e) {
            return false;
          }
        },
        {
          message: "queryBody must be a valid Elasticsearch query DSL object"
        }
      )
      .describe(
        "Complete Elasticsearch query DSL object that can include query, size, from, sort, etc."
      )
  }
};

export const GET_PROFILE_DOCUMENT_TOOL_CONFIG = {
  name: "get_profile_document",
  description: "Get a profile document from Elasticsearch for a person, HCP",
  validator: {
    index: z
      .string()
      .trim()
      .min(1, "Index name is required")
      .describe("Name of the Elasticsearch index to search"),
    personId: z
      .string()
      .trim()
      .min(1, "Person ID is required")
      .describe("ID of the person to retrieve the profile document for"),
    source: z
      .array(z.string())
      .describe("Fields to include in the source of the document")
  }
};
