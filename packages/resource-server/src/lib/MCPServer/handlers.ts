import { Client, estypes } from "@elastic/elasticsearch";
import { Logger } from "../Logger";

export const getListIndicesHandler = (esClient: Client, logger: Logger) => {
  return async () => {
    try {
      const allIndices = await esClient.cat.indices({
        format: "json"
      });

      const indicesInfo = allIndices
        .filter((index) => index.index?.startsWith("people"))
        .map((index) => ({
          index: index.index,
          health: index.health,
          status: index.status,
          docsCount: index.docsCount
        }));

      return {
        content: [
          {
            type: "text" as const,
            text: `Found ${indicesInfo.length} indices`
          },
          {
            type: "text" as const,
            text: JSON.stringify(indicesInfo, null, 2)
          }
        ]
      };
    } catch (error) {
      logger.error(
        JSON.stringify(error, null, 2),
        "getListIndicesHandler error"
      );
      return {
        content: [
          {
            type: "text" as const,
            text: `Error: ${
              error instanceof Error ? error.message : String(error)
            }`
          }
        ]
      };
    }
  };
};

export const getMappingsHandler = (esClient: Client, logger: Logger) => {
  return async ({ index }: { index: string }) => {
    try {
      const mappingResponse = await esClient.indices.getMapping({
        index
      });

      return {
        content: [
          {
            type: "text" as const,
            text: `Mappings for index: ${index}`
          },
          {
            type: "text" as const,
            text: `Mappings for index ${index}: ${JSON.stringify(
              mappingResponse[index]?.mappings || {},
              null,
              2
            )}`
          }
        ]
      };
    } catch (error) {
      logger.error(JSON.stringify(error, null, 2), "getMappingsHandler error");
      return {
        content: [
          {
            type: "text" as const,
            text: `Error: ${
              error instanceof Error ? error.message : String(error)
            }`
          }
        ]
      };
    }
  };
};

export const elasticSearchHandler = (esClient: Client, logger: Logger) => {
  return async ({ index, queryBody }: { index: string; queryBody: any }) => {
    try {
      // Get mappings to identify text fields for highlighting
      const mappingResponse = await esClient.indices.getMapping({
        index
      });

      const indexMappings = mappingResponse[index]?.mappings || {};

      const searchRequest: estypes.SearchRequest = {
        index,
        ...queryBody
      };

      // Always do highlighting
      if (indexMappings.properties) {
        const textFields: Record<string, estypes.SearchHighlightField> = {};

        for (const [fieldName, fieldData] of Object.entries(
          indexMappings.properties
        )) {
          if (fieldData.type === "text" || "dense_vector" in fieldData) {
            textFields[fieldName] = {};
          }
        }

        searchRequest.highlight = {
          fields: textFields,
          pre_tags: ["<em>"],
          post_tags: ["</em>"]
        };
      }

      const result = await esClient.search(searchRequest);

      // Extract the 'from' parameter from queryBody, defaulting to 0 if not provided
      const from = queryBody.from || 0;

      const contentFragments = result.hits.hits.map((hit) => {
        const highlightedFields = hit.highlight || {};
        const sourceData: any = hit._source || {};

        let content = "";

        for (const [field, highlights] of Object.entries(highlightedFields)) {
          if (highlights && highlights.length > 0) {
            content += `${field} (highlighted): ${highlights.join(" ... ")}\n`;
          }
        }

        for (const [field, value] of Object.entries(sourceData || {})) {
          if (!(field in highlightedFields)) {
            content += `${field}: ${JSON.stringify(value)}\n`;
          }
        }

        return {
          type: "text" as const,
          text: content.trim()
        };
      });

      const metadataFragment = {
        type: "text" as const,
        text: `Total results: ${
          typeof result.hits.total === "number"
            ? result.hits.total
            : result.hits.total?.value || 0
        }, showing ${result.hits.hits.length} from position ${from}`
      };

      return {
        content: [metadataFragment, ...contentFragments]
      };
    } catch (error) {
      logger.error(
        JSON.stringify(error, null, 2),
        "elasticSearchHandler error"
      );
      return {
        content: [
          {
            type: "text" as const,
            text: `Error: ${
              error instanceof Error ? error.message : String(error)
            }`
          }
        ]
      };
    }
  };
};

export const getProfileDocumentHandler = (esClient: Client, logger: Logger) => {
  return async ({
    index,
    personId,
    source
  }: {
    index: string;
    personId: string;
    source: string[];
  }) => {
    try {
      const query: estypes.SearchRequest = {
        index,
        _source: source,
        query: {
          bool: {
            filter: [
              {
                term: {
                  id: personId
                }
              }
            ]
          }
        }
      };

      const result = await esClient.search(query);

      const metadataFragment = {
        type: "text" as const,
        text: `Total results: ${
          typeof result.hits.total === "number"
            ? result.hits.total
            : result.hits.total?.value || 0
        }, showing ${result.hits.hits.length} from position 0`
      };

      return {
        content: [
          metadataFragment,
          ...result.hits.hits.map((hit) => ({
            type: "text" as const,
            text: JSON.stringify(hit._source, null, 2)
          }))
        ]
      };
    } catch (error) {
      logger.error(
        JSON.stringify(error, null, 2),
        "getProfileDocumentHandler error"
      );

      return {
        content: [
          {
            type: "text" as const,
            text: `Error: ${
              error instanceof Error ? error.message : String(error)
            }`
          }
        ]
      };
    }
  };
};
