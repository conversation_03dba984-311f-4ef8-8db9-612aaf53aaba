import {
  ClinicalTrialDocumentWithCTMS,
  ConditionV4,
  TrialPersonRole,
  DesignV4,
  EligibilityV4,
  StudyV4,
  IdentifierV4,
  PersonV5,
  RoleHistoryV5,
  FacilityV5,
  PersonHistoryV5,
  StatusHistoryV5,
  StudyInterventionV4,
  SponsorV4,
  ClinicalTrialInput,
  CTMS,
  CTMSSite,
  CTMSAddress,
  CTMSInvestigator
} from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";
import { format } from "date-fns";
import _ from "lodash";
import {
  SearchHit,
  SearchResponse
} from "@elastic/elasticsearch/lib/api/types";
import { mapResultsWithCTMS } from "./ClinicalTrialsMapperWithCTMS";

// Most of these functions were shamelessly lifted from PR #804 (Trials Search Rewrite)
// Once work restarts on that PR, they should be moved to the response adapter

function hyphenatedYearMonthDay(date: Date) {
  return format(date, "yyyy-MM-dd");
}

function generateInput(
  overrides: Partial<ClinicalTrialInput> = {}
): ClinicalTrialInput {
  const input = {
    idsOnly: false,
    filters: [],
    limit: faker.datatype.number(),
    offset: faker.datatype.number(),
    sort: {
      sortBy: faker.datatype.string(),
      direction: faker.datatype.string()
    }
  };

  return { ...input, ...overrides };
}

function generateMockElasticsearchResponse<T>(
  hits: Array<SearchHit<T>> = [],
  aggregations?: any
): SearchResponse<T> {
  return {
    took: faker.datatype.number(),
    timed_out: false,
    _shards: {
      total: faker.datatype.number(),
      successful: faker.datatype.number(),
      skipped: faker.datatype.number(),
      failed: faker.datatype.number()
    },
    hits: {
      total: {
        value: faker.datatype.number(),
        relation: "eq"
      },
      max_score: faker.datatype.number(),
      hits
    },
    aggregations
  };
}

function generateMockHit(
  overrides: Partial<ClinicalTrialDocumentWithCTMS> = {}
): SearchHit<ClinicalTrialDocumentWithCTMS> {
  const hit = {
    h1_clinical_trial_id: faker.datatype.uuid(),
    is_deprecated: faker.datatype.boolean(),
    survivor_h1_clinical_trial_id: faker.datatype.uuid(),
    version: faker.datatype.string(),
    effective_date: hyphenatedYearMonthDay(faker.date.past()),
    facility: [generateMockFacility(), generateMockFacility()],
    identifiers: [generateMockIdentifier(), generateMockIdentifier()],
    person: [generateMockPerson(), generateMockPerson()],
    study: generateMockStudy(),
    ctms: [generateMockCTMSData()]
  };

  return {
    _id: faker.datatype.uuid(),
    _index: faker.datatype.string(),
    _score: faker.datatype.number(),
    _source: { ...hit, ...overrides }
  };
}

function generateMockIdentifier(
  overrides: Partial<IdentifierV4> = {}
): IdentifierV4 {
  const identifier = {
    collection_source_id: faker.datatype.number(),
    external_uuid: faker.datatype.uuid(),
    h1_clinical_trial_id: faker.datatype.uuid()
  };

  return { ...identifier, ...overrides };
}

function generateMockPerson(overrides: Partial<PersonV5> = {}): PersonV5 {
  const person = {
    collection_source_id: faker.datatype.number(),
    external_uuid: faker.datatype.uuid(),
    h1_clinical_trial_id: faker.datatype.uuid(),
    h1_person_id: faker.datatype.number(),
    name: faker.name.fullName(),
    roleHistory: [
      generateMockRoleHistory({ role: "Facility Contact" }),
      generateMockRoleHistory({ role: "Central Contact" })
    ],
    study_person_hash: faker.datatype.uuid()
  };

  return { ...person, ...overrides };
}

function generateMockRoleHistory(
  overrides: Partial<RoleHistoryV5> = {}
): RoleHistoryV5 {
  const roleHistory: RoleHistoryV5 = {
    affiliation: faker.datatype.string(),
    collection_source_id: faker.datatype.number(),
    email: faker.internet.email(),
    end_date: faker.date.future().toISOString(),
    h1_clinical_trial_id: faker.datatype.uuid(),
    hash_or_id: faker.datatype.uuid(),
    phone: faker.phone.number(),
    role: faker.commerce.department(),
    start_date: faker.date.past().toISOString(),
    study_person_hash: faker.datatype.uuid(),
    study_person_role_hash: faker.datatype.uuid()
  };

  return { ...roleHistory, ...overrides };
}

function generateMockFacility(overrides: Partial<FacilityV5> = {}): FacilityV5 {
  const facility = {
    city: [faker.datatype.string(), faker.datatype.string()],
    collection_source_id: faker.datatype.number(),
    country: [faker.datatype.string(), faker.datatype.string()],
    external_uuid: faker.datatype.uuid(),
    h1_clinical_trial_id: faker.datatype.uuid(),
    h1_organization_id: faker.datatype.number(),
    name: faker.company.name(),
    personHistory: [generateMockPersonHistory(), generateMockPersonHistory()],
    state: [faker.datatype.string(), faker.datatype.string()],
    statusHistory: [generateMockStatusHistory(), generateMockStatusHistory()],
    study_organization_hash: faker.datatype.uuid(),
    zip: faker.datatype.string()
  };

  return { ...facility, ...overrides };
}

function generateMockPersonHistory(
  overrides: Partial<PersonHistoryV5> = {}
): PersonHistoryV5 {
  const personHistory = {
    collection_source_id: faker.datatype.number(),
    end_date: faker.datatype.string(),
    facility_person_hash: faker.datatype.uuid(),
    h1_clinical_trial_id: faker.datatype.uuid(),
    hash_or_id: faker.datatype.uuid(),
    role: faker.datatype.string(),
    start_date: faker.datatype.string(),
    study_organization_hash: faker.datatype.uuid(),
    study_person_hash: faker.datatype.uuid()
  };

  return { ...personHistory, ...overrides };
}

function generateMockStatusHistory(
  overrides: Partial<StatusHistoryV5> = {}
): StatusHistoryV5 {
  const statusHistory = {
    collection_source_id: faker.datatype.number(),
    end_date: faker.datatype.uuid(),
    facility_hash: faker.datatype.uuid(),
    h1_clinical_trial_id: faker.datatype.uuid(),
    hash_or_id: faker.datatype.uuid(),
    start_date: faker.datatype.uuid(),
    status: faker.datatype.uuid(),
    study_organization_hash: faker.datatype.uuid()
  };

  return { ...statusHistory, ...overrides };
}

function generateMockCondition(
  overrides: Partial<ConditionV4> = {}
): ConditionV4 {
  const condition = {
    condition_hash: faker.datatype.uuid(),
    effective_date: faker.date.past().toISOString(),
    external_uuid: faker.datatype.uuid(),
    h1_clinical_trial_id: faker.datatype.uuid(),
    name: faker.datatype.string(),
    rare_disease: faker.datatype.boolean(),
    therapy_area: faker.datatype.string()
  };

  return { ...condition, ...overrides };
}

function generateMockIntervention(
  overrides: Partial<StudyInterventionV4> = {}
): StudyInterventionV4 {
  const intervention = {
    name: faker.datatype.string(),
    intervention_other_names: [
      faker.datatype.string(),
      faker.datatype.string()
    ],
    intervention_type: faker.datatype.string()
  };

  return { ...intervention, ...overrides } as StudyInterventionV4;
}

function generateMockEligibility(
  overrides: Partial<EligibilityV4> = {}
): EligibilityV4 {
  const eligibility = {
    minimum_age: faker.datatype.string()
  };

  return { ...eligibility, ...overrides } as EligibilityV4;
}

function generateMockDesign(overrides: Partial<DesignV4> = {}): DesignV4 {
  const design = {
    allocation: faker.datatype.string(),
    intervention_model: faker.datatype.string(),
    masking: faker.datatype.string(),
    primary_purpose: [faker.datatype.string()]
  };

  return { ...design, ...overrides } as DesignV4;
}

function generateMockStudy(overrides: Partial<StudyV4> = {}): StudyV4 {
  const study = {
    brief_title: faker.datatype.string(),
    completion_date: hyphenatedYearMonthDay(faker.date.past()),
    condition: [generateMockCondition(), generateMockCondition()],
    design: [generateMockDesign(), generateMockDesign()],
    eligibility: [generateMockEligibility(), generateMockEligibility()],
    enrollment: faker.datatype.number(),
    enrollment_type: faker.datatype.string(),
    external_uuid: faker.datatype.uuid(),
    intervention: [generateMockIntervention(), generateMockIntervention()],
    official_title: faker.datatype.string(),
    overall_status: faker.datatype.string(),
    phase: faker.datatype.string(),
    sponsor: [generateMockSponsor(), generateMockSponsor()]
  } as StudyV4;

  return { ...study, ...overrides };
}

function generateMockSponsor(overrides: Partial<SponsorV4> = {}): SponsorV4 {
  const sponsor: SponsorV4 = {
    external_uuid: faker.datatype.uuid(),
    sponsor_hash: faker.datatype.uuid()
  };

  return { ...sponsor, ...overrides };
}

function generateMockCTMSData(overrides: Partial<CTMS> = {}): CTMS {
  const ctms = {
    h1dn_clinical_trial_id: faker.datatype.uuid(),
    name: faker.commerce.productName(),
    project_id: faker.datatype.string(),
    sites: [generateMockCTMSSite()],
    status: faker.datatype.string(),
    registry: {
      id: faker.datatype.uuid(),
      name: faker.datatype.string()
    }
  };

  return { ...ctms, ...overrides };
}

function generateMockCTMSSite(overrides: Partial<CTMSSite> = {}): CTMSSite {
  const site = {
    account_name: faker.datatype.string(),
    account_number: faker.datatype.string(),
    activation_date: faker.datatype.string(),
    actual_completed_count: faker.datatype.number(),
    actual_date_of_fsfv: faker.date.future(),
    actual_date_of_fslv: faker.date.future(),
    actual_date_of_lsfv: faker.date.future(),
    actual_date_of_lslv: faker.date.future(),
    address: generateMockAddress(),
    client_id: faker.datatype.string(),
    closed_date: faker.date.future(),
    country_status: faker.datatype.string(),
    enrolled_count: faker.datatype.number(),
    h1dn_id: faker.datatype.uuid(),
    group_h1dn_organization_id: faker.datatype.uuid(),
    h1_organization_id: faker.datatype.uuid(),
    investigator: generateMockCTMSInvestigator(),
    name: faker.datatype.string(),
    planned_activation_date: faker.date.future(),
    planned_closed_date: faker.date.future(),
    planned_date_of_fsfv: faker.date.future(),
    planned_date_of_fslv: faker.date.future(),
    planned_date_of_lsfv: faker.date.future(),
    planned_date_of_lslv: faker.date.future(),
    pre_screen_failure_count: faker.datatype.number(),
    pre_screened_count: faker.datatype.number(),
    primary_contact_name: faker.datatype.string(),
    project_id: faker.datatype.string(),
    randomised_count: faker.datatype.number(),
    run_in_count: faker.datatype.number(),
    run_in_failure_count: faker.datatype.number(),
    screen_failure_count: faker.datatype.number(),
    screened_count: faker.datatype.number(),
    status: faker.datatype.string(),
    withdrawn_count: faker.datatype.number()
  };

  return { ...site, ...overrides };
}

function generateMockCTMSInvestigator(
  overrides: Partial<CTMSInvestigator> = {}
): CTMSInvestigator {
  const investigator = {
    first_name: faker.name.firstName(),
    full_name: faker.name.fullName(),
    h1dn_id: faker.datatype.uuid(),
    group_h1dn_person_id: faker.datatype.uuid(),
    h1_person_id: faker.datatype.number().toString(),
    last_name: faker.name.lastName()
  };

  return { ...investigator, ...overrides };
}

function generateMockAddress(
  overrides: Partial<CTMSAddress> = {}
): CTMSAddress {
  const address = {
    country: faker.datatype.string(),
    line1: faker.datatype.string(),
    locality: faker.datatype.string()
  };

  return { ...address, ...overrides };
}

describe("legacy tests", () => {
  describe("avgPatientsPerSitePerMonth calculations", () => {
    const input = {
      filters: [
        {
          name: "StudyPhase",
          value: '["\\"Phase 3\\""]'
        }
      ],
      limit: 25,
      offset: 0,
      sort: {
        sortBy: "StartDate",
        direction: "Desc"
      }
    };
    const initialResponse =
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([
        generateMockHit()
      ]);
    const docCount = 500;
    const avgTrialsDuration = **********0; // 10 Months
    const medianTrialsDuration = 13141440000; // 5 Months
    const facilityCount = 1000;
    const investigatorCount = 1000;
    const enrollmentDocCount = 2;
    const totalEnrollment = 200;
    const medianEnrollment = 100;
    const medianFacilityCount = 1;
    const medianInvestigatorCount = 1;
    const personRoleBuckets = [
      {
        key: TrialPersonRole.PRINCIPAL_INVESTIGATOR,
        doc_count: 2000
      },
      {
        key: TrialPersonRole.CENTRAL_CONTACT,
        doc_count: 0
      },
      {
        key: TrialPersonRole.SUB_INVESTIGATOR,
        doc_count: 3000
      },
      {
        key: TrialPersonRole.STUDY_CHAIR,
        doc_count: 300
      }
    ];

    const groupByCountry = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number()
    }));
    const statusNames = [...Array(5)].map(() => faker.datatype.string());
    const phaseBuckets = [...Array(5)].map(() => ({
      key: faker.datatype.string(),
      doc_count: faker.datatype.number(),
      group_by_status: {
        buckets: statusNames.map((status) => ({
          key: status,
          doc_count: faker.datatype.number()
        }))
      }
    }));
    const aggregations = {
      median_facility_count: {
        doc_count: 10000,
        median_facility_count: {
          value: medianFacilityCount
        }
      },
      median_investigator_count: {
        doc_count: 10000,
        median_investigator_count: {
          value: medianInvestigatorCount
        }
      },
      enrollment_duration: {
        doc_count: 100,
        avg_duration: {
          value: avgTrialsDuration
        },
        median_duration: {
          values: {
            "50.0": medianTrialsDuration
          }
        }
      },
      trial_duration: {
        avg_duration: {
          value: avgTrialsDuration
        },
        median_duration: {
          values: {
            "50.0": medianTrialsDuration
          }
        }
      },
      facility_count: {
        doc_count: facilityCount,
        filtered_facility_count: {
          doc_count: docCount
        }
      },
      investigator_count: {
        doc_count: investigatorCount,
        filtered_investigator_count: {
          doc_count: docCount
        }
      },
      group_by_person_role: {
        group_by_role: {
          buckets: personRoleBuckets
        }
      },
      group_by_country: {
        country_groups: {
          buckets: groupByCountry
        }
      },
      group_by_phase_and_status: {
        buckets: phaseBuckets
      },
      total_enrollment: {
        doc_count: enrollmentDocCount,
        enrollment_sum: {
          value: totalEnrollment
        },
        median: {
          values: {
            "50.0": medianEnrollment
          }
        }
      }
    };

    it("should return correct aggregation results from search initialResponse", () => {
      initialResponse.aggregations = aggregations;
      const result = mapResultsWithCTMS(initialResponse, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0.02
          })
        })
      );
    });
    it("should return correct aggregation results from search initialResponse with duration of 1 month", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          trial_duration: {
            ...aggregations.trial_duration,
            avg_duration: {
              value: ********** // 1 month
            }
          }
        }
      };
      const result = mapResultsWithCTMS(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0.2
          })
        })
      );
    });
    it("should return correct aggregation results from search initialResponse with facility count of 1", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          facility_count: {
            doc_count: 1,
            filtered_facility_count: {
              doc_count: 1
            }
          }
        }
      };
      const result = mapResultsWithCTMS(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 20
          })
        })
      );
    });
    it("should return correct aggregation results from search when average patient count is 1", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          total_enrollment: {
            ...aggregations.total_enrollment,
            doc_count: 1,
            enrollment_sum: {
              value: 1
            }
          }
        }
      };
      const result = mapResultsWithCTMS(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0.0001
          })
        })
      );
    });
    it("should handle the case when facility counts are 0", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          facility_count: {
            doc_count: 0,
            filtered_facility_count: {
              doc_count: 0
            }
          }
        }
      };
      const result = mapResultsWithCTMS(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0
          })
        })
      );
    });
    it("should handle the case when average trial duration is 0", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          trial_duration: {
            ...aggregations.trial_duration,
            avg_duration: {
              value: 0
            }
          }
        }
      };
      const result = mapResultsWithCTMS(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0
          })
        })
      );
    });
    it("should handle the case when average patient count is 0", () => {
      const response = {
        ...initialResponse,
        aggregations: {
          ...aggregations,
          total_enrollment: {
            ...aggregations.total_enrollment,
            doc_count: 0,
            enrollment_sum: {
              value: 0
            }
          }
        }
      };
      const result = mapResultsWithCTMS(response, input);
      expect(result).toEqual(
        expect.objectContaining({
          aggregations: expect.objectContaining({
            avgPatientsPerSitePerMonth: 0
          })
        })
      );
    });
  });
});

describe("mapResultsWithCTMS", () => {
  it("should return h1_clinical_trial_id as h1Id if defined", () => {
    const input = generateInput();
    const hit = generateMockHit();
    const response =
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([hit]);

    const mappedResult = mapResultsWithCTMS(response, input);
    expect(mappedResult).toEqual(
      expect.objectContaining({
        from: input.offset,
        pageSize: input.limit,
        results: [
          expect.objectContaining({
            h1Id: hit._source?.h1_clinical_trial_id
          })
        ]
      })
    );
  });

  it("should return h1dn_clinical_trial_id as h1Id if h1_clinical_trial_id is not defined", () => {
    const h1dn_clinical_trial_id = faker.datatype.uuid();
    const input = generateInput();
    const hit = generateMockHit({
      h1dn_clinical_trial_id
    });
    delete hit._source!.h1_clinical_trial_id;
    const response =
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([hit]);

    const mappedResult = mapResultsWithCTMS(response, input);
    expect(mappedResult).toEqual(
      expect.objectContaining({
        from: input.offset,
        pageSize: input.limit,
        results: [
          expect.objectContaining({
            h1Id: h1dn_clinical_trial_id
          })
        ]
      })
    );
  });

  it("should return ctms data from the document as is", () => {
    const input = generateInput();
    const hit = generateMockHit();
    const response =
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([hit]);

    const mappedResult = mapResultsWithCTMS(response, input);
    expect(mappedResult).toEqual(
      expect.objectContaining({
        from: input.offset,
        pageSize: input.limit,
        results: [
          expect.objectContaining({
            ctms: hit._source?.ctms
          })
        ]
      })
    );
  });
  it("should return true for hasCTMSData when ctms info is present for project", () => {
    const projectId = faker.datatype.number().toString();

    const input = generateInput();
    input.projectId = projectId;

    const hit = generateMockHit();
    hit._source!.ctms![0].project_id = projectId;
    const response =
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([hit]);

    const mappedResult = mapResultsWithCTMS(response, input);
    expect(mappedResult).toEqual(
      expect.objectContaining({
        from: input.offset,
        pageSize: input.limit,
        results: [
          expect.objectContaining({
            ctms: hit._source?.ctms,
            hasCTMSData: true
          })
        ]
      })
    );
  });
  it("should return false for hasCTMSData when ctms info is not present for project", () => {
    const projectId = faker.datatype.number().toString();

    const input = generateInput();
    input.projectId = projectId;
    const hit = generateMockHit();
    const response =
      generateMockElasticsearchResponse<ClinicalTrialDocumentWithCTMS>([hit]);

    const mappedResult = mapResultsWithCTMS(response, input);
    expect(mappedResult).toEqual(
      expect.objectContaining({
        from: input.offset,
        pageSize: input.limit,
        results: [
          expect.objectContaining({
            ctms: hit._source?.ctms,
            hasCTMSData: false
          })
        ]
      })
    );
  });
});
