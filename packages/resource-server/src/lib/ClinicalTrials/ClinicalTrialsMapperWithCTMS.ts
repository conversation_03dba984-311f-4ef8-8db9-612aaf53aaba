import { ClinicalTrialMapperType, RootAggregationV3 } from "./types";
import {
  ClinicalTrialDocumentWithCTMS,
  ClinicalTrialInput,
  ClinicalTrialsSearchResponse,
  CTMS
} from "@h1nyc/search-sdk";
import * as _ from "lodash";
import { mapAggregations, mapDocument } from "./ClinicalTrialsBaseMapper";
import { aggregationsV3Mapper } from "./ClinicalTrialsMapperV3";
import { documentV4Mapper } from "./ClinicalTrialsMapperV4";
import { estypes } from "@elastic/elasticsearch";

function notEmpty<T>(value: T | null | undefined): value is T {
  return !_.isNil(value);
}

export const mapResultsWithCTMS = (
  root: estypes.SearchResponse<ClinicalTrialDocumentWithCTMS>,
  input: ClinicalTrialInput
): ClinicalTrialsSearchResponse => {
  const hits = root.hits;

  return {
    from: input.offset,
    pageSize: input.limit,
    total: getTotal(hits.total ?? 0),
    results: input.idsOnly
      ? []
      : hits.hits.map((hit) =>
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          mapDocument(hit._source!, getMappedDocuments(input))
        ),
    resultIds: input.idsOnly
      ? hits.hits
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          .map((hit) => hit._source!.identifiers)
          .filter(notEmpty)
          .map((identifiers) => String(identifiers[0].external_uuid))
      : undefined,
    aggregations: root.aggregations
      ? mapAggregations<RootAggregationV3>(
          root.aggregations as unknown as RootAggregationV3,
          aggregationsV3Mapper,
          getTotal(root.hits.total ?? 0)
        )
      : undefined
  };
};

const getMappedDocuments = (
  input: ClinicalTrialInput
): ClinicalTrialMapperType<ClinicalTrialDocumentWithCTMS> => {
  return {
    ...(documentV4Mapper as Partial<
      ClinicalTrialMapperType<ClinicalTrialDocumentWithCTMS>
    >),
    h1Id: (doc) => doc.h1_clinical_trial_id || doc.h1dn_clinical_trial_id,
    ctms: (doc) => doc.ctms,
    hasCTMSData: (doc) => hasCTMSData(doc, input)
  };
};

const hasCTMSData = (
  doc: ClinicalTrialDocumentWithCTMS,
  input: ClinicalTrialInput
): boolean => {
  const ctmsDocs = doc.ctms?.filter(
    (ctms: CTMS) => ctms.project_id && ctms.project_id === input.projectId
  );
  return ctmsDocs?.length !== 0;
};

const getTotal = (total: number | estypes.SearchTotalHits): number => {
  return typeof total === "number" ? total : total.value;
};
