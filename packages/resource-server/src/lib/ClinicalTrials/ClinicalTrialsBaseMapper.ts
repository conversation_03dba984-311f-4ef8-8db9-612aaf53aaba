import { ClinicalTrial, TrialsAggregations } from "@h1nyc/search-sdk";
import {
  ClinicalTrialAggregationsMapperType,
  ClinicalTrialMapperType
} from "./types";

type DataItemKeys<T> = Array<keyof T & string>;

export function getObjectKeys<T extends Record<string, unknown>>(
  obj?: T
): DataItemKeys<T> {
  return obj ? (Object.keys(obj) as DataItemKeys<T>) : [];
}

export const mapDocument = <T>(
  doc: T,
  clinicalTrialMapper: ClinicalTrialMapperType<T>
): ClinicalTrial => {
  return getObjectKeys(clinicalTrialMapper)
    .map((key) => {
      const mapper = clinicalTrialMapper[key];
      if (mapper === undefined) {
        return undefined;
      }

      return {
        [key]: mapper(doc)
      };
    })
    .filter((v) => !!v)
    .reduce((prev, curr) => ({ ...prev, ...curr }), {}) as ClinicalTrial;
};

export const mapAggregations = <T>(
  agg: T,
  trialsAggregationsMapper: ClinicalTrialAggregationsMapperType<T>,
  totalTrialCount: number
): TrialsAggregations => {
  return getObjectKeys(trialsAggregationsMapper)
    .map((key) => {
      const mapper = trialsAggregationsMapper[key];
      if (mapper === undefined) {
        return undefined;
      }

      return {
        [key]: mapper(agg, totalTrialCount)
      };
    })
    .filter((v) => !!v)
    .reduce(
      (prev, curr) => ({ ...prev, ...curr }),
      {}
    ) as unknown as TrialsAggregations;
};
