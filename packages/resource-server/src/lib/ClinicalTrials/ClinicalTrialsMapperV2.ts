import {
  RootObjectV2,
  RootAggregation,
  ClinicalTrialMapperType,
  ClinicalTrialAggregationsMapperType
} from "./types";
import {
  ClinicalTrialDocumentV2,
  ClinicalTrialInput,
  ClinicalTrialsSearchResponse,
  Person,
  TrialPersonRole,
  Facility
} from "@h1nyc/search-sdk";
import * as _ from "lodash";
import { getTrialDuration, mergeArrays } from "./ClinicalTrialsMapper";
import { mapAggregations, mapDocument } from "./ClinicalTrialsBaseMapper";
import {
  getEndDateForDuration,
  getMonthsBetween,
  getTimeFromDateString,
  isActiveEndDate
} from "@h1nyc/search-sdk";

export const mapResultsV2 = (
  root: RootObjectV2,
  input: ClinicalTrialInput
): ClinicalTrialsSearchResponse => {
  const hits = root.hits;
  const aggregations = root.aggregations;

  return {
    from: input.offset,
    pageSize: input.limit,
    total: hits.total.value,
    results: input.idsOnly
      ? []
      : hits.hits.map((hit) => mapDocument(hit._source, documentV2Mapper)),
    resultIds: input.idsOnly
      ? hits.hits.map((hit) => String(hit._source.identifiers[0].external_uuid))
      : undefined,
    aggregations: aggregations
      ? mapAggregations(aggregations, aggregationsV2Mapper, hits.total.value)
      : undefined
  };
};

export const personIsActive = (person: Person, role?: string): boolean => {
  const currentTime = new Date().getTime();
  if (!person.roleHistory) {
    return false;
  }
  const roleHistory = role
    ? person.roleHistory.filter((r) => r.role === role)
    : person.roleHistory;
  const activeRoles = roleHistory
    .filter((r) => {
      const startTime = getTimeFromDateString(r.start_date);
      return !startTime || startTime < currentTime;
    })
    .filter((r) => isActiveEndDate(r.end_date || ""));
  return activeRoles.length > 0;
};

const getFacilityCount = (doc: ClinicalTrialDocumentV2) =>
  doc.facility?.filter((f) => f.statusHistory !== undefined).length || 0;

const getPatientsPerMonth = (doc: ClinicalTrialDocumentV2) => {
  const monthsBetween = getMonthsBetween(
    doc.study.start_date,
    getEndDateForDuration(doc.study.completion_date)
  );

  if (!monthsBetween || !doc.study.enrollment) {
    return undefined;
  }

  return _.round(doc.study.enrollment / monthsBetween, 2);
};

const getPatientsPerSitePerMonth = (doc: ClinicalTrialDocumentV2) => {
  const monthsBetween = getMonthsBetween(
    doc.study.start_date,
    getEndDateForDuration(doc.study.completion_date)
  );

  if (!monthsBetween || !doc.study.enrollment) {
    return undefined;
  }

  const patientsPerSitePerMonth =
    doc.study.enrollment / getFacilityCount(doc) / monthsBetween;

  return _.round(patientsPerSitePerMonth, 2);
};

const studyTypeToDisplayMap: { [index: string]: string } = {
  Interventional: "Interventional",
  Observational: "Observational",
  "Observational [Patient Registry]": "Observational",
  "Expanded Access": "N/A",
  "N/A": "N/A"
};

export const documentV2Mapper: ClinicalTrialMapperType<ClinicalTrialDocumentV2> =
  {
    id: () => Math.floor(Math.random() * 100000),
    h1Id: (doc) => doc.h1_clinical_trial_id,
    briefTitle: (doc) => doc.study.brief_title || doc.study.official_title,
    status: (doc) => doc.study.overall_status,
    startDate: (doc) => getTimeFromDateString(doc.study.start_date),
    nctId: (doc) => doc.identifiers[0].external_uuid,
    studyPhase: (doc) => doc.study.phase,
    conditions: (doc) => _.uniq(doc.study.conditions),
    interventions: (doc) =>
      _.compact(
        _.uniq(doc.study.intervention?.map((intervention) => intervention.name))
      ),
    interventionTypes: (doc) =>
      _.uniq(
        _.compact(doc.study.intervention?.map((i) => i.intervention_type))
      ),
    sponsors: (doc) => {
      const sponsorsPeople = doc.study.sponsor?.flatMap((s) =>
        doc.person
          ?.filter((p) => p.study_person_hash == s.study_person_hash)
          .map((p) => p.name)
      );

      const sponsorsFacilities = doc.study.sponsor?.flatMap((s) =>
        doc.facility
          ?.filter(
            (f) => f.study_organization_hash == s.study_organization_hash
          )
          .map((p) => p.name)
      );

      return _.uniq(_.compact(mergeArrays(sponsorsPeople, sponsorsFacilities)));
    },
    interventionOtherNames: (doc) =>
      _.uniq(
        _.compact(
          doc.study.intervention?.flatMap((i) => i.intervention_other_names)
        )
      ),
    estimatedEndDate: (doc) =>
      getTimeFromDateString(doc.study.primary_completion_date),
    primaryCompletionDate: (doc) =>
      getTimeFromDateString(doc.study.primary_completion_date),
    studyCompletionDate: (doc) =>
      getTimeFromDateString(doc.study.completion_date),
    studyType: (doc) => studyTypeToDisplayMap[doc.study.study_type || "N/A"],
    enrollment: (doc) => doc.study.enrollment,
    enrollmentType: (doc) => doc.study.enrollment_type,
    eligibilityMinAge: (doc) =>
      _.compact(doc.study.eligibility?.map((e) => e.minimum_age)),
    allocation: (doc) =>
      _.uniq(_.compact(doc.study.design?.map((d) => d.allocation))),
    interventionModel: (doc) =>
      _.uniq(_.compact(doc.study.design?.map((i) => i.intervention_model))),
    masking: (doc) =>
      _.uniq(_.compact(doc.study.design?.map((d) => d.masking))),
    primaryPurpose: (doc) =>
      _.uniq(_.compact(doc.study.design?.map((d) => d.primary_purpose))),
    investigators: (doc) => {
      const investigatorNames = doc.person
        ?.filter(
          (p) =>
            personIsActive(p, "Principal Investigator") ||
            personIsActive(p, "Sub-Investigator")
        )
        .map((p) => (!p.h1_person_id ? p.name : null));

      return _.uniq(_.compact(investigatorNames));
    },
    facilityName: (doc) => _.uniq(_.compact(doc.facility?.map((f) => f.name))),
    facilityCity: (doc) =>
      _.uniq(_.compact(_.flatMap(doc.facility?.map((f) => f.city)))),
    facilityState: (doc) =>
      _.uniq(_.compact(_.flatMap(doc.facility?.map((f) => f.state)))),
    facilityCountry: (doc) =>
      _.uniq(_.compact(_.flatMap(doc.facility?.map((f) => f.country)))),
    lastUpdatedAt: (doc) => getTimeFromDateString(doc.effective_date), // doc.study.last_update_posted_date equals doc.effective_date
    contactNames: (doc) => {
      const contactNames = [
        ...new Set(
          mergeArrays(
            doc.person
              ?.filter((p) => personIsActive(p, "Facility Contact"))
              .map((p) => p.name),
            doc.person
              ?.filter((p) => personIsActive(p, "Central Contact"))
              .map((p) => p.name)
          )
        )
      ];

      return _.uniq(contactNames);
    },
    // OPEN QUESTION: should we use current date if it is currently running?
    trialDuration: (doc) =>
      doc.study.start_date && doc.study.completion_date
        ? getTrialDuration(
            getTimeFromDateString(doc.study.start_date) || 0,
            getTimeFromDateString(doc.study.completion_date) || 0
          )
        : undefined,
    sitesCount: getFacilityCount,
    avgPatientsPerSites: (doc) =>
      _.round((doc.study.enrollment || 0) / getFacilityCount(doc), 2),
    patientsPerMonths: getPatientsPerMonth,
    patientsPerSitesPerMonth: getPatientsPerSitePerMonth,
    personIds: (doc) =>
      _.uniq(
        _.compact(
          doc.person
            ?.filter((p) => personIsActive(p))
            .map((p) => p.h1_person_id?.toString())
        )
      ),
    people: (doc) => doc.person,
    facilities: (doc) =>
      doc.facility?.map((f: Facility) => ({
        // TODO: This should be replaced with the  faciliy.study_organization_hash since it's always unique
        // but it's tied to the old data model
        id: f.h1_organization_id ?? 0,
        name: f.name || "",
        city: !f.city ? "" : f.city[0],
        state: !f.state ? "" : f.state[0],
        zip: f.zip || "",
        country: !f.country ? "" : f.country[0],
        statusHistory: f.statusHistory,
        h1MasterOrganizationId: f.h1_organization_id
      })),
    acronym: (doc) => doc.study?.acronym
  };

const getAvgPatientCount = (aggsDoc: RootAggregation) => {
  const totalEnrollment = aggsDoc.total_enrollment.enrollment_sum.value;
  const enrollmentDocCount = aggsDoc.total_enrollment.doc_count;
  return enrollmentDocCount === 0 ? null : totalEnrollment / enrollmentDocCount;
};

export const aggregationsV2Mapper: ClinicalTrialAggregationsMapperType<RootAggregation> =
  {
    avgPatientsPerSite: (aggsDoc) => {
      const avgPatientCount = getAvgPatientCount(aggsDoc);
      const facilityCount = aggsDoc.facility_count.count.value;
      return avgPatientCount === null || facilityCount === 0
        ? null
        : avgPatientCount / facilityCount;
    },
    avgPatientCount: (aggsDoc) => getAvgPatientCount(aggsDoc),
    facilityCount: (aggsDoc) => aggsDoc.facility_count.count.value,
    avgFacilityCount: (aggsDoc) => {
      const facilityCount = aggsDoc.facility_count.count.value;
      const facilityDocCount = aggsDoc.facility_count.doc_count ?? 0;
      return facilityDocCount === 0 ? null : facilityCount / facilityDocCount;
    },
    avgInvestigatorCount: (aggsDoc, totalTrialCount?: number) => {
      const investigatorCount = _.sumBy(
        aggsDoc.group_by_person_role.buckets,
        (rolBucket) =>
          [
            TrialPersonRole.PRINCIPAL_INVESTIGATOR,
            TrialPersonRole.SUB_INVESTIGATOR
          ].includes(rolBucket.key)
            ? rolBucket.doc_count
            : 0
      );
      return totalTrialCount === undefined || totalTrialCount === 0
        ? null
        : investigatorCount / totalTrialCount;
    },
    totalEnrollment: (aggsDoc) => aggsDoc.total_enrollment.enrollment_sum.value,
    groupByPersonRole: (aggsDoc) => aggsDoc.group_by_person_role.buckets,
    groupByCountry: (aggsDoc) => aggsDoc.group_by_country.buckets,
    groupByPhaseAndStatus: (aggsDoc) =>
      aggsDoc.group_by_phase_and_status.buckets,
    avgTrialsDuration: (aggsDoc) => aggsDoc.trial_duration.avg_duration.value // average duration in ms
  };
