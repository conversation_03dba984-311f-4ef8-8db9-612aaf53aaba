import {
  ClinicalTrialMapperType,
  RootAggregationV3,
  RootObjectV3
} from "./types";
import {
  ClinicalTrialSource,
  ClinicalTrialDocumentV4,
  ClinicalTrialInput,
  ClinicalTrialsSearchResponse,
  getLatestActualEnrollment,
  getEstimatedEnrollment
} from "@h1nyc/search-sdk";
import * as _ from "lodash";
import { mapAggregations, mapDocument } from "./ClinicalTrialsBaseMapper";
import {
  aggregationsV3Mapper,
  documentV3Mapper
} from "./ClinicalTrialsMapperV3";
import { getEndDateForDuration, getMonthsBetween } from "@h1nyc/search-sdk";
import { trialSourceIdMap } from "./trials-index";
import { personIsActive } from "./ClinicalTrialsMapperV2";
import { mergeArrays } from "./ClinicalTrialsMapper";

const trialSourcesById = _.invert(trialSourceIdMap);

function notEmpty<T>(value: T | null | undefined): value is T {
  return !_.isNil(value);
}

export const mapResultsV4 = (
  root: RootObjectV3<ClinicalTrialDocumentV4>,
  input: ClinicalTrialInput
): ClinicalTrialsSearchResponse => {
  const hits = root.hits;

  return {
    from: input.offset,
    pageSize: input.limit,
    total: hits.total.value,
    results: input.idsOnly
      ? []
      : hits.hits.map((hit) => mapDocument(hit._source, documentV4Mapper)),
    resultIds: input.idsOnly
      ? hits.hits
          .map((hit) => hit._source.identifiers)
          .filter(notEmpty)
          .map((identifiers) => String(identifiers[0].external_uuid))
      : undefined,
    aggregations: root.aggregations
      ? mapAggregations<RootAggregationV3>(
          root.aggregations,
          aggregationsV3Mapper,
          root.hits.total.value
        )
      : undefined
  };
};

export const documentV4Mapper: ClinicalTrialMapperType<ClinicalTrialDocumentV4> =
  {
    ...(documentV3Mapper as Partial<
      ClinicalTrialMapperType<ClinicalTrialDocumentV4>
    >),
    acronym: (doc) => doc.study?.acronym,
    patientsPerMonths: (doc) => getPatientsPerMonth(doc),
    avgPatientsPerSites: (doc) => getPatientsPerSitePerMonth(doc),
    conditions: (doc) =>
      _.uniq(
        _.compact(doc.study?.condition?.map((condition) => condition.name))
      ),
    enrollment: (doc) => getEnrollment(doc),
    primaryPurpose: (doc) =>
      _.uniq(_.compact(doc.study?.design?.flatMap((d) => d.primary_purpose))),
    nctId: (doc) => doc.study?.external_uuid,
    source: (doc) => {
      const collectionMethods = doc.identifiers?.map(
        (identifier) => identifier.collection_source_id
      );
      if (collectionMethods?.includes(29)) {
        return ClinicalTrialSource.NCT;
      } else if (collectionMethods?.includes(405)) {
        return ClinicalTrialSource.EUDRA;
      } else if (collectionMethods?.includes(1335)) {
        return ClinicalTrialSource.DRKS;
      } else if (collectionMethods?.includes(729)) {
        return ClinicalTrialSource?.ChiCTR;
      } else if (collectionMethods?.includes(10148)) {
        return ClinicalTrialSource.UMIN;
      } else if (collectionMethods?.includes(1048)) {
        return ClinicalTrialSource.CTIS;
      } else if (collectionMethods?.includes(10151)) {
        return ClinicalTrialSource.ISRCTN;
      }

      return ClinicalTrialSource.UNKNOWN;
    },
    sources: (doc) => {
      return _.uniq(
        doc.identifiers?.map((identifier) => {
          const sourceId = identifier.collection_source_id?.toString();
          return (
            (sourceId && trialSourcesById[sourceId]) ||
            ClinicalTrialSource.UNKNOWN
          );
        })
      );
    },
    sponsors: (doc) => {
      const sponsorNames = doc.study?.sponsor?.map((sponsor) => {
        return sponsor.name;
      });

      return _.uniq(_.compact(sponsorNames));
    },
    studyDirectors: (doc) => findStudyDirectors(doc),
    contactNames: (doc) => findContactNames(doc),
    contacts: (doc) => findContacts(doc),
    h1People: (doc) => findH1People(doc),
    enrollmentDurationInMonths: (doc) =>
      doc.study?.enrollment_duration_in_months,
    patientsPerSitesPerMonth: (doc) => doc.study?.patients_per_site_per_month,
    studyLinks: (doc) => findStudyLinks(doc),
    genericDrugNames: (doc) => doc.generic_drug_names,
    genericDrugNameInclusion: (doc) => doc.inclusion_generic_drug_names,
    genericDrugNameExclusion: (doc) => doc.exclusion_generic_drug_names,
    biomarkerInclusion: (doc) => doc.biomarker_inclusion,
    biomarkerExclusion: (doc) => doc.biomarker_exclusion,
    indicationInclusions: (doc) => doc.indication_inclusions,
    indicationExclusions: (doc) => doc.indication_exclusions,
    indications: (doc) => doc.indications,
    biomarkers: (doc) => doc.biomarker,
    enrollmentHistory: (doc) => doc.study?.enrollment_history,
    estimatedEnrollment: (doc) =>
      getEstimatedEnrollment(doc.study?.enrollment_history),
    actualEnrollment: (doc) =>
      getLatestActualEnrollment(doc.study?.enrollment_history)
  };

// In some EUDRA trials we don't get total enrollment count so we need to add up the planned
// population of all member states
const getEnrollment = (doc: ClinicalTrialDocumentV4) => {
  const eeaMemberState = doc.study?.eea_member_state;
  if (eeaMemberState && eeaMemberState.length > 1) {
    const plannedPopulation = eeaMemberState
      .map((ems) => ems.planned_population)
      .filter((pp): pp is number => pp !== undefined)
      .reduce((a, b) => a + b, 0);

    if (plannedPopulation > (doc.study?.enrollment || 0)) {
      return plannedPopulation;
    }
  }

  return doc.study?.enrollment;
};

const getPatientsPerMonth = (doc: ClinicalTrialDocumentV4) => {
  const monthsBetween = getMonthsBetween(
    doc.study?.start_date,
    getEndDateForDuration(doc.study?.completion_date)
  );

  const enrollment = getEnrollment(doc);

  if (!monthsBetween || !enrollment) {
    return undefined;
  }

  return _.round(enrollment / monthsBetween, 2);
};

const getFacilityCount = (doc: ClinicalTrialDocumentV4) =>
  doc.facility?.filter((f) => f.statusHistory !== undefined).length || 0;

const getPatientsPerSitePerMonth = (doc: ClinicalTrialDocumentV4) => {
  const monthsBetween = getMonthsBetween(
    doc.study?.start_date,
    getEndDateForDuration(doc.study?.completion_date)
  );

  const enrollment = getEnrollment(doc);

  if (!monthsBetween || !enrollment) {
    return undefined;
  }

  const patientsPerSitePerMonth =
    enrollment / getFacilityCount(doc) / monthsBetween;

  return _.round(patientsPerSitePerMonth, 2);
};

const findStudyDirectors = (doc: ClinicalTrialDocumentV4) =>
  doc.person
    ?.filter(
      (p) =>
        personIsActive(p, "Study Director") || personIsActive(p, "Director")
    )
    .map((p) => (!p.h1_person_id ? p.name : null))
    .filter((p) => p) as string[];

const findContactNames = (doc: ClinicalTrialDocumentV4) => {
  const contactNames = [
    ...new Set(
      mergeArrays(
        doc.person
          ?.filter((p) => personIsActive(p, "Facility Contact"))
          .map((p) => (!p.h1_person_id ? p.name : null))
          .filter((p) => p) as string[],
        doc.person
          ?.filter((p) => personIsActive(p, "Central Contact"))
          .map((p) => (!p.h1_person_id ? p.name : null))
          .filter((p) => p) as string[]
      )
    )
  ];

  return _.uniq(contactNames);
};

// This function is only supposed to return the people listed as contacts or previously listed as contacts
const findContacts = (doc: ClinicalTrialDocumentV4) => {
  const contacts = doc.person
    ?.map((p) => {
      if (
        !p.h1_person_id &&
        // Filter out people that have no contact roles
        p.roleHistory?.some(
          (r) => r.role === "Facility Contact" || r.role === "Central Contact"
        )
      ) {
        return {
          name: p.name,
          isActive:
            // We don't really care what contact role the person is active in, as long as they are currently active in one of them
            personIsActive(p, "Facility Contact") ||
            personIsActive(p, "Central Contact")
        };
      }
      return null;
    })
    .filter((p) => p) as { name: string; isActive: boolean }[];

  // This following block is used to remove duplicate roles
  const updatedContacts: {
    [key: string]: { name: string; isActive: boolean };
  } = {};

  contacts?.forEach((contact: { name: string; isActive: boolean }) => {
    // Update the contact to active if the role is active
    if (contact.isActive) {
      updatedContacts[contact.name] = contact;
    }
    // Skip updating the contact to inactive if it already exists
    if (updatedContacts[contact.name]) {
      return;
    }
    // Update the contact to inactive if it hasn't been set
    updatedContacts[contact.name] = contact;
  });

  return Object.values(updatedContacts);
};

const findH1People = (doc: ClinicalTrialDocumentV4) => {
  const people = _.compact(
    doc.person?.map((p) =>
      p.h1_person_id
        ? {
            id: p.h1_person_id?.toString(),
            isActive: personIsActive(p)
          }
        : undefined
    )
  );

  // This following block is used to remove duplicate roles
  const updatedPeople: {
    [key: string]: { id: string; isActive: boolean };
  } = {};

  people.forEach((contact) => {
    // Update the contact to active if the role is active
    if (contact.isActive) {
      updatedPeople[contact.id] = contact;
    }

    // Skip updating the contact to inactive if it already exists
    if (updatedPeople[contact.id]) {
      return;
    }

    // Update the contact to inactive if it hasn't been set
    updatedPeople[contact.id] = contact;
  });

  return Object.values(updatedPeople);
};

const findStudyLinks = (doc: ClinicalTrialDocumentV4): string[] => {
  return _.compact(_.map(doc.study?.link || [], (linkItem) => linkItem.url));
};
