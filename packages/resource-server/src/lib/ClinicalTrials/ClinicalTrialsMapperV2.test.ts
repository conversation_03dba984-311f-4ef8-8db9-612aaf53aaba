//@ts-nocheck

import { TrialPersonRole } from "@h1nyc/search-sdk";
import { format } from "date-fns";

import { mapResultsV2 } from "./ClinicalTrialsMapperV2";
import { RootObjectV2 } from "./types";
import fixture1 from "../../services/__fixtures__/GoldToProduct/TrialsV2.json";
import fixture2 from "../../services/__fixtures__/GoldToProduct/TrialsV2_withPersonAndFacility.json";
import { faker } from "@faker-js/faker";
import * as _ from "lodash";
import { Person } from "@h1nyc/search-sdk";

function generatePerson(role = "Investigator"): Person {
  return {
    h1_person_id: faker.datatype.number(),
    roleHistory: [
      {
        role,
        hash_or_id: faker.datatype.string(),
        study_person_role_hash: faker.random.alphaNumeric()
      }
    ],
    name: faker.name.fullName(),
    study_person_hash: faker.random.alphaNumeric(),
    external_uuid: faker.datatype.uuid()
  };
}

function generateFutureDate() {
  return hyphenatedYearMonthDay(faker.date.future());
}

function generatePastDate() {
  return hyphenatedYearMonthDay(faker.date.past());
}

function hyphenatedYearMonthDay(date: Date) {
  return format(date, "yyyy-MM-dd");
}

describe("mapResultsV2()", () => {
  const input = {
    filters: [
      {
        name: "StudyPhase",
        value: '["\\"Phase 3\\""]'
      }
    ],
    limit: 25,
    offset: 0,
    sort: {
      sortBy: "StartDate",
      direction: "Desc"
    }
  };
  const response: RootObjectV2 = fixture1;
  const docCount = 500;
  const avgTrialsDuration = faker.datatype.number();
  const facilityCount = 1000;
  const enrollmentDocCount = 2;
  const totalEnrollment = 200;
  const avgFacilityCount = 2;
  const avgPatientCount = 100;
  const avgPatientsPerSite = 0.1;
  const personRoleBuckets = [
    {
      key: TrialPersonRole.PRINCIPAL_INVESTIGATOR,
      doc_count: 2000
    },
    {
      key: TrialPersonRole.CENTRAL_CONTACT,
      doc_count: 0
    },
    {
      key: TrialPersonRole.SUB_INVESTIGATOR,
      doc_count: 3000
    },
    {
      key: TrialPersonRole.STUDY_CHAIR,
      doc_count: 300
    }
  ];
  const avgInvestigatorCount = 5000;

  const groupByCountry = [...Array(5)].map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number()
  }));
  const statusNames = [...Array(5)].map(() => faker.datatype.string());
  const phaseBuckets = [...Array(5)].map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number(),
    group_by_status: {
      buckets: statusNames.map((status) => ({
        key: status,
        doc_count: faker.datatype.number()
      }))
    }
  }));
  const aggregations = {
    trial_duration: {
      doc_count: docCount,
      avg_duration: {
        value: avgTrialsDuration
      }
    },
    facility_count: {
      doc_count: docCount,
      count: {
        value: facilityCount
      }
    },
    group_by_person_role: {
      buckets: personRoleBuckets
    },
    group_by_country: {
      buckets: groupByCountry
    },
    group_by_phase_and_status: {
      buckets: phaseBuckets
    },
    total_enrollment: {
      doc_count: enrollmentDocCount,
      enrollment_sum: {
        value: totalEnrollment
      }
    }
  };

  it("should return aggregation results from search response", () => {
    response.aggregations = aggregations;
    const result = mapResultsV2(response, input);
    expect(result).toEqual(
      expect.objectContaining({
        aggregations: {
          groupByPersonRole: personRoleBuckets,
          avgTrialsDuration,
          facilityCount,
          avgFacilityCount,
          avgPatientCount,
          avgPatientsPerSite,
          groupByCountry,
          groupByPhaseAndStatus: phaseBuckets,
          totalEnrollment,
          avgInvestigatorCount
        }
      })
    );
  });

  it("should return undefined when search result has no aggregation data", () => {
    response.aggregations = undefined;
    const result = mapResultsV2(response, input);
    expect(result).toEqual(
      expect.objectContaining({
        aggregations: undefined
      })
    );
  });

  it("should handle the case when facility counts are 0", () => {
    response.aggregations = {
      ...aggregations,
      facility_count: {
        doc_count: 0,
        count: {
          value: 0
        }
      }
    };
    const result = mapResultsV2(response, input);
    expect(result).toEqual(
      expect.objectContaining({
        aggregations: {
          avgTrialsDuration,
          groupByPersonRole: personRoleBuckets,
          avgFacilityCount: null,
          facilityCount: 0,
          avgPatientCount,
          avgPatientsPerSite: null,
          groupByCountry,
          groupByPhaseAndStatus: phaseBuckets,
          totalEnrollment,
          avgInvestigatorCount
        }
      })
    );
  });
  it("should extract and return unique personIds from search response", () => {
    const responseWithPersonAndFacilities = fixture2;
    const res = mapResultsV2(responseWithPersonAndFacilities, input);
    expect(res.results[0].personIds).toEqual([
      "670639",
      "3275896",
      "7817212",
      "112023",
      "5184419",
      "1097144",
      "2043661",
      "61300",
      "372304",
      "1677852",
      "5161832",
      "2330605",
      "10359830",
      "3701275",
      "2268678",
      "1400869",
      "1622182",
      "3161287",
      "1829842",
      "5366694",
      "1039271",
      "7810000",
      "1527352",
      "4910315",
      "5397935",
      "297865",
      "5153671",
      "10024279",
      "2818574",
      "3760285",
      "2683656",
      "2988988",
      "3440368",
      "3038274",
      "5374485",
      "3610327",
      "253041",
      "3231705",
      "1946052"
    ]);
  });

  it("should return empty personIds when no persons are available in search response", () => {
    const responseWithNoPersonAndFacilities = fixture1;
    const res = mapResultsV2(responseWithNoPersonAndFacilities, input);
    expect(res.results[0].personIds).toEqual([]);
  });

  it("should return empty investigators list when person has a linked profile id", () => {
    const responseWithPersonAndFacilities = fixture2;
    const res = mapResultsV2(responseWithPersonAndFacilities, input);
    expect(res.results[0].investigators).toEqual([]);
  });

  it("should return personIds and investigators for people who have no start date or end date", () => {
    const person1 = generatePerson();
    const person2 = generatePerson("Principal Investigator");
    person2.h1_person_id = undefined;

    const responseWithPersonAndFacilities = fixture2;
    responseWithPersonAndFacilities.hits.hits[0]._source.person = [
      person1,
      person2
    ];
    const res = mapResultsV2(responseWithPersonAndFacilities, input);
    expect(res.results[0].personIds).toEqual(
      expect.arrayContaining([person1.h1_person_id?.toString()])
    );
    expect(res.results[0].investigators).toEqual(
      expect.arrayContaining([person2.name])
    );
  });

  it("should return personIds and investigators for people who have a start date before today and no end date", () => {
    const person1 = generatePerson();
    const person2 = generatePerson("Principal Investigator");
    person2.h1_person_id = undefined;
    person1.roleHistory[0].start_date = generatePastDate();
    person2.roleHistory[0].start_date = generatePastDate();

    const responseWithPersonAndFacilities = fixture2;
    responseWithPersonAndFacilities.hits.hits[0]._source.person = [
      person1,
      person2
    ];
    const res = mapResultsV2(responseWithPersonAndFacilities, input);
    expect(res.results[0].personIds).toEqual(
      expect.arrayContaining([person1.h1_person_id?.toString()])
    );
    expect(res.results[0].investigators).toEqual(
      expect.arrayContaining([person2.name])
    );
  });

  it("should return personIds and investigators for people who have a start date before today and an end date in the future", () => {
    const person1 = generatePerson();
    const person2 = generatePerson("Principal Investigator");
    person2.h1_person_id = undefined;
    person1.roleHistory[0].start_date = generatePastDate();
    person1.roleHistory[0].end_date = generateFutureDate();
    person2.roleHistory[0].start_date = generatePastDate();
    person2.roleHistory[0].end_date = generateFutureDate();

    const responseWithPersonAndFacilities = fixture2;
    responseWithPersonAndFacilities.hits.hits[0]._source.person = [
      person1,
      person2
    ];
    const res = mapResultsV2(responseWithPersonAndFacilities, input);
    expect(res.results[0].personIds).toEqual(
      expect.arrayContaining([person1.h1_person_id?.toString()])
    );
    expect(res.results[0].investigators).toEqual(
      expect.arrayContaining([person2.name])
    );
  });

  it("should return personIds and investigators for people who have no start date and an end date in the future", () => {
    const person1 = generatePerson();
    const person2 = generatePerson("Principal Investigator");
    person2.h1_person_id = undefined;
    person1.roleHistory[0].end_date = generateFutureDate();
    person2.roleHistory[0].end_date = generateFutureDate();

    const responseWithPersonAndFacilities = fixture2;
    responseWithPersonAndFacilities.hits.hits[0]._source.person = [
      person1,
      person2
    ];
    const res = mapResultsV2(responseWithPersonAndFacilities, input);
    expect(res.results[0].personIds).toEqual(
      expect.arrayContaining([person1.h1_person_id?.toString()])
    );
    expect(res.results[0].investigators).toEqual(
      expect.arrayContaining([person2.name])
    );
  });

  it("should NOT return personIds and investigators for people who have an end date in the past", () => {
    const person1 = generatePerson();
    const person2 = generatePerson("Principal Investigator");
    person2.h1_person_id = undefined;
    person1.roleHistory[0].end_date = generatePastDate();
    person2.roleHistory[0].end_date = generatePastDate();

    const responseWithPersonAndFacilities = fixture2;
    responseWithPersonAndFacilities.hits.hits[0]._source.person = [
      person1,
      person2
    ];
    const res = mapResultsV2(responseWithPersonAndFacilities, input);
    expect(res.results[0].personIds).not.toEqual(
      expect.arrayContaining([person1.h1_person_id?.toString()])
    );
    expect(res.results[0].investigators).not.toEqual(
      expect.arrayContaining([person2.name])
    );
  });

  it("should NOT return personIds and investigators for people who have a start date in the future", () => {
    const person1 = generatePerson();
    const person2 = generatePerson("Principal Investigator");
    person2.h1_person_id = undefined;
    person1.roleHistory[0].start_date = generateFutureDate();
    person2.roleHistory[0].start_date = generateFutureDate();

    const responseWithPersonAndFacilities = fixture2;
    responseWithPersonAndFacilities.hits.hits[0]._source.person = [
      person1,
      person2
    ];
    const res = mapResultsV2(responseWithPersonAndFacilities, input);
    expect(res.results[0].personIds).not.toEqual(
      expect.arrayContaining([person1.h1_person_id?.toString()])
    );
    expect(res.results[0].investigators).not.toEqual(
      expect.arrayContaining([person2.name])
    );
  });
});
