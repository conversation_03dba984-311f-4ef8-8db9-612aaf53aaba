import { mapDocumentV1 } from "./ClinicalTrialsMapper";

describe("Trials search document", () => {
  it("Correctly builds patients stats", () => {
    const input = {
      studyId: 1234,
      studyStartDate: 94694400000,
      studyEndDate: 1767139200000,
      studyEnrollment: 10000,
      facilityName: ["TestSite-1", "TestSite-2"],
      uniqueCountOfSites: 2,
      avgPatientsPerSite: 5000,
      patientsPerMonth: 15.75,
      patientsPerSitePerMonth: 7.87
    };
    const expectedDoc = {
      trialDuration: "52 yrs 11 mos",
      sitesCount: 2,
      avgPatientsPerSites: 5000,
      patientsPerMonths: 15.75,
      patientsPerSitesPerMonth: 7.87
    };

    const documentResult = mapDocumentV1(input);
    expect(documentResult).toEqual(expect.objectContaining(expectedDoc));
  });

  it("Correctly builds trial duration when no difference in dates", () => {
    const input = {
      studyId: 1234,
      studyStartDate: 1693526400000,
      studyEndDate: 1693526400000,
      studyEnrollment: 10000,
      facilityName: ["TestSite-1", "TestSite-2"]
    };
    const expectedDoc = {
      trialDuration: "0 yr 0 mo"
    };

    const documentResult = mapDocumentV1(input);
    expect(documentResult).toEqual(expect.objectContaining(expectedDoc));
  });
});
