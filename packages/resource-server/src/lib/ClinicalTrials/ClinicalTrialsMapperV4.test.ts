import { ClinicalTrialDocumentV4, TrialPersonRole } from "@h1nyc/search-sdk";
import fixture1 from "../../services/__fixtures__/GoldToProduct/TrialsV4.json";
import { mapResultsV4 } from "./ClinicalTrialsMapperV4";
import { RootAggregationV3, RootObjectV3 } from "./types";
import { faker } from "@faker-js/faker";

const input = {
  filters: [
    {
      name: "StudyPhase",
      value: '["\\"Phase 3\\""]'
    }
  ],
  limit: 25,
  offset: 0,
  sort: {
    sortBy: "StartDate",
    direction: "Desc"
  }
};

const initialResponse: RootObjectV3<ClinicalTrialDocumentV4> = fixture1;

describe("avgPatientsPerSitePerMonth calculations", () => {
  const docCount = 500;
  const avgTrialsDuration = **********0; // 10 Months
  const medianTrialsDuration = 13141440000; // 5 Months
  const facilityCount = 1000;
  const investigatorCount = 1000;
  const enrollmentDocCount = 2;
  const totalEnrollment = 200;
  const medianEnrollment = 100;
  const medianFacilityCount = 1;
  const medianInvestigatorCount = 1;
  const avgPatientsPerSitePerMonths = 100;
  const medianPatientsPerSitePerMonth = 10;
  const personRoleBuckets = [
    {
      key: TrialPersonRole.PRINCIPAL_INVESTIGATOR,
      doc_count: 2000
    },
    {
      key: TrialPersonRole.CENTRAL_CONTACT,
      doc_count: 0
    },
    {
      key: TrialPersonRole.SUB_INVESTIGATOR,
      doc_count: 3000
    },
    {
      key: TrialPersonRole.STUDY_CHAIR,
      doc_count: 300
    }
  ];

  const groupByCountry = [...Array(5)].map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number()
  }));
  const groupByEnrollmentDuration = [...Array(5)].map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number()
  }));
  const groupByPatientsPerSitePerEnrollmentDuration = [...Array(5)].map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number()
  }));
  const statusNames = [...Array(5)].map(() => faker.datatype.string());
  const phaseBuckets = [...Array(5)].map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number(),
    group_by_status: {
      buckets: statusNames.map((status) => ({
        key: status,
        doc_count: faker.datatype.number()
      }))
    }
  }));
  const groupByTopCountrySites = [...Array(5)].map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number()
  }));
  const groupByTopCountryTrials = [...Array(5)].map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number(),
    trials: {
      doc_count: faker.datatype.number()
    }
  }));
  const groupByTopCountryAvgEnrollmentRate = [...Array(5)].map(() => ({
    key: faker.datatype.string(),
    doc_count: faker.datatype.number(),
    trials: {
      doc_count: faker.datatype.number(),
      sum_patients_per_site_per_month: {
        value: faker.datatype.number()
      }
    },
    avg_patients_per_site_per_month: {
      value: faker.datatype.number()
    }
  }));
  const aggregations: RootAggregationV3 = {
    median_facility_count: {
      doc_count: 10000,
      median_facility_count: {
        value: medianFacilityCount
      }
    },
    filtered_facility_count: {
      doc_count: 10000,
      median: {
        values: {
          "50.0": medianFacilityCount
        }
      }
    },
    median_investigator_count: {
      doc_count: 10000,
      median_investigator_count: {
        value: medianInvestigatorCount
      }
    },
    filtered_investigator_count: {
      doc_count: 10000,
      median: {
        values: {
          "50.0": medianInvestigatorCount
        }
      }
    },
    enrollment_duration: {
      doc_count: 100,
      avg_duration: {
        value: avgTrialsDuration
      },
      median_duration: {
        values: {
          "50.0": medianTrialsDuration
        }
      }
    },
    trial_duration: {
      avg_duration: {
        value: avgTrialsDuration
      },
      median_duration: {
        values: {
          "50.0": medianTrialsDuration
        }
      }
    },
    facility_count: {
      doc_count: facilityCount,
      filtered_facility_count: {
        doc_count: docCount
      }
    },
    investigator_count: {
      doc_count: investigatorCount,
      filtered_investigator_count: {
        doc_count: docCount
      }
    },
    group_by_person_role: {
      group_by_role: {
        buckets: personRoleBuckets
      }
    },
    group_by_country: {
      country_groups: {
        buckets: groupByCountry
      }
    },
    group_by_phase_and_status: {
      buckets: phaseBuckets
    },
    total_enrollment: {
      doc_count: enrollmentDocCount,
      median: {
        values: {
          "50.0": medianEnrollment
        }
      },
      enrollment_sum: {
        value: totalEnrollment
      }
    },
    enrollment_graphs: {
      group_by_enrollment_duration: {
        buckets: groupByEnrollmentDuration
      },
      group_by_patients_per_site_per_month: {
        buckets: groupByPatientsPerSitePerEnrollmentDuration
      }
    },
    enrollment_duration_v2: {
      doc_count: 100,
      avg_duration: {
        value: avgTrialsDuration
      },
      median_duration: {
        values: {
          "50.0": medianTrialsDuration
        }
      }
    },
    patients_per_site_per_month: {
      doc_count: 100,
      avg_patients_per_site_per_month: {
        value: avgPatientsPerSitePerMonths
      },
      median_patients_per_site_per_month: {
        values: {
          "50.0": medianPatientsPerSitePerMonth
        }
      }
    },
    group_by_top_countries: {
      group_by_sites: {
        order_by_sites: {
          buckets: groupByTopCountrySites
        }
      },
      group_by_trials: {
        order_by_trials: {
          buckets: groupByTopCountryTrials
        }
      },
      group_by_avg_enrollment_rate: {
        filtered_trials: {
          order_by_avg_enrollment_rate: {
            buckets: groupByTopCountryAvgEnrollmentRate
          }
        }
      }
    }
  };

  it("should return correct aggregation results from search initialResponse", () => {
    initialResponse.aggregations = aggregations;
    const result = mapResultsV4(initialResponse, input);
    expect(result).toEqual(
      expect.objectContaining({
        aggregations: expect.objectContaining({
          avgPatientsPerSitePerMonth: 0.02
        })
      })
    );
  });
  it("should return correct aggregation results from search initialResponse with duration of 1 month", () => {
    const response = {
      ...initialResponse,
      aggregations: {
        ...aggregations,
        trial_duration: {
          ...aggregations.trial_duration,
          avg_duration: {
            value: ********** // 1 month
          }
        }
      }
    };
    const result = mapResultsV4(response, input);
    expect(result).toEqual(
      expect.objectContaining({
        aggregations: expect.objectContaining({
          avgPatientsPerSitePerMonth: 0.2
        })
      })
    );
  });
  it("should return correct aggregation results from search initialResponse with facility count of 1", () => {
    const response = {
      ...initialResponse,
      aggregations: {
        ...aggregations,
        facility_count: {
          doc_count: 1,
          filtered_facility_count: {
            doc_count: 1
          }
        }
      }
    };
    const result = mapResultsV4(response, input);
    expect(result).toEqual(
      expect.objectContaining({
        aggregations: expect.objectContaining({
          avgPatientsPerSitePerMonth: 20
        })
      })
    );
  });
  it("should return correct aggregation results from search when average patient count is 1", () => {
    const response = {
      ...initialResponse,
      aggregations: {
        ...aggregations,
        total_enrollment: {
          ...aggregations.total_enrollment,
          doc_count: 1,
          enrollment_sum: {
            value: 1
          }
        }
      }
    };
    const result = mapResultsV4(response, input);
    expect(result).toEqual(
      expect.objectContaining({
        aggregations: expect.objectContaining({
          avgPatientsPerSitePerMonth: 0.0001
        })
      })
    );
  });
  it("should handle the case when facility counts are 0", () => {
    const response = {
      ...initialResponse,
      aggregations: {
        ...aggregations,
        facility_count: {
          doc_count: 0,
          filtered_facility_count: {
            doc_count: 0
          }
        }
      }
    };
    const result = mapResultsV4(response, input);
    expect(result).toEqual(
      expect.objectContaining({
        aggregations: expect.objectContaining({
          avgPatientsPerSitePerMonth: 0
        })
      })
    );
  });
  it("should handle the case when average trial duration is 0", () => {
    const response = {
      ...initialResponse,
      aggregations: {
        ...aggregations,
        trial_duration: {
          ...aggregations.trial_duration,
          avg_duration: {
            value: 0
          }
        }
      }
    };
    const result = mapResultsV4(response, input);
    expect(result).toEqual(
      expect.objectContaining({
        aggregations: expect.objectContaining({
          avgPatientsPerSitePerMonth: 0
        })
      })
    );
  });
  it("should handle the case when average patient count is 0", () => {
    const response = {
      ...initialResponse,
      aggregations: {
        ...aggregations,
        total_enrollment: {
          ...aggregations.total_enrollment,
          doc_count: 0,
          enrollment_sum: {
            value: 0
          }
        }
      }
    };
    const result = mapResultsV4(response, input);
    expect(result).toEqual(
      expect.objectContaining({
        aggregations: expect.objectContaining({
          avgPatientsPerSitePerMonth: 0
        })
      })
    );
  });
});

describe("studyDirectors field", () => {
  it("should build study directors", () => {
    const result = mapResultsV4(initialResponse, input);
    expect(result.results).toEqual([
      expect.objectContaining({
        studyDirectors: ["William K. Ward, MD"]
      })
    ]);
  });
  it("should dedup study directors", () => {
    const result = mapResultsV4(initialResponse, input);
    expect(result.results).toEqual([
      expect.objectContaining({
        studyDirectors: ["William K. Ward, MD"]
      })
    ]);
  });
});

describe("contacts field", () => {
  it("should dedup contacts", () => {
    const result = mapResultsV4(initialResponse, input);
    expect(result.results).toEqual([
      expect.objectContaining({
        contactNames: ["Thomas Seidl, PhD", "William K. Ward, MD"],
        contacts: [
          { name: "Thomas Seidl, PhD", isActive: true },
          { name: "William K. Ward, MD", isActive: true }
        ]
      })
    ]);
  });
});
