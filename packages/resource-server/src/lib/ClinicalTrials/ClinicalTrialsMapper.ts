import {
  ClinicalTrial,
  ClinicalTrialDocument,
  ClinicalTrialInput,
  ClinicalTrialsSearchResponse
} from "@h1nyc/search-sdk";
import * as DateFns from "date-fns";
import { RootObjectV1 } from "./types";

export const mapResults = (
  root: RootObjectV1,
  input: ClinicalTrialInput
): ClinicalTrialsSearchResponse => {
  const hits = root.hits;

  return {
    from: input.offset,
    pageSize: input.limit,
    total: hits.total.value,
    results: input.idsOnly
      ? []
      : hits.hits.map((hit) => mapDocumentV1(hit._source)),
    resultIds: input.idsOnly
      ? hits.hits.map((hit) => String(hit._source.studyId))
      : undefined
  };
};

export const mapDocumentV1 = (doc: ClinicalTrialDocument): ClinicalTrial => {
  let studyType: string | undefined;

  if (doc.designInterventionModel?.length) {
    studyType = "Interventional";
  } else if (doc.designObservationalModel?.length) {
    studyType = "Observational";
  }

  return {
    id: doc.studyId,
    briefTitle: doc.studyBriefTitle,
    status: doc.studyOverallStatus,
    startDate: doc.studyStartDate,
    nctId: doc.studyNctId,
    studyPhase: doc.studyPhase,
    conditions: doc.conditionName,
    interventions: doc.interventionName,
    interventionTypes: doc.interventionType,
    sponsors: doc.sponsorName,
    interventionOtherNames: doc.interventionOtherName,
    estimatedEndDate: doc.studyPrimaryCompletionDate,
    primaryCompletionDate: doc.studyPrimaryCompletionDate,
    studyCompletionDate: doc.studyEndDate,
    studyType,
    enrollment: doc.studyEnrollment,
    enrollmentType: doc.studyEnrollmentType,
    eligibilityMinAge: doc.eligibilityMinimumAgeRaw,
    allocation: doc.designAllocation,
    interventionModel: doc.designInterventionModel,
    masking: doc.designMasking,
    primaryPurpose: doc.designPrimaryPurpose,
    investigators: [
      ...new Set(
        mergeArrays(doc.facilityInvestigatorName, doc.overallOfficialName)
      )
    ],
    facilityName: doc.facilityName,
    facilityCity: doc.facilityCity,
    facilityState: doc.facilityState,
    facilityCountry: doc.facilityCountry,
    lastUpdatedAt: doc.studyLastUpdatePostedDate,
    contactNames: [
      ...new Set(mergeArrays(doc.centralContactName, doc.facilityContactName))
    ],
    trialDuration:
      doc.studyEndDate && doc.studyStartDate
        ? getTrialDuration(doc.studyEndDate, doc.studyStartDate)
        : undefined,
    sitesCount: doc.uniqueCountOfSites,
    avgPatientsPerSites: doc.avgPatientsPerSite,
    patientsPerMonths: doc.patientsPerMonth,
    patientsPerSitesPerMonth: doc.patientsPerSitePerMonth
  };
};

export const getTrialDuration = (
  startDate: number,
  endDate: number
): string | undefined => {
  const duration = DateFns.intervalToDuration({
    start: new Date(startDate),
    end: new Date(endDate)
  });
  const monthSpec = (duration.months || 0) > 1 ? "mos" : "mo";
  const yearSpec = (duration.years || 0) > 1 ? "yrs" : "yr";
  return `${duration.years} ${yearSpec} ${duration.months} ${monthSpec}`;
};

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const mergeArrays = (...arrays: any) => {
  // **
  // merge null, undefined or string of arrays.
  return [].concat(...arrays.filter(Array.isArray));
};
