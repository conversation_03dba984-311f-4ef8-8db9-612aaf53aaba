import {
  ClinicalTrialFilterAutocompleteFilterField,
  ClinicalTrialFilterAutocompleteInput,
  ClinicalTrialFilterInput,
  ClinicalTrialInput
} from "@h1nyc/search-sdk";
import {
  filterMapV1,
  filterMapV2,
  filterMapV3,
  filterMapV4,
  trialsAggregations
} from "./trials-index";
import { Logger } from "../Logger";
import {
  AggregationsAggregationContainer,
  QueryDslBoolQuery,
  QueryDslQueryContainer,
  Sort
} from "@elastic/elasticsearch/lib/api/types";
import { estypes } from "@elastic/elasticsearch";
import _ from "lodash";
import { QueryParserService } from "../../services/QueryParserService";
import { ParsedQueryTreeToElasticsearchQueriesService } from "../../services/ParsedQueryTreeToElasticsearchQueries";
import { QueryUnderstandingServiceClient } from "../../services/QueryUnderstandingServiceClient";
import {
  ALL_ANDs,
  ALL_ORs,
  ALL_UNICODE_DOUBLE_QUOTES,
  ASCII_DOUBLE_QUOTES,
  HAS_ADVANCED_OPERATORS,
  OR
} from "../../services/KeywordSearchResourceServiceRewrite";
// dummy study id so we return empty results
const NO_STUDY_ID_PLACEHOLDER = "99999999";
const PROJECT_ID_FIELD = "project_id";

const DOUBLE_QUOTES_REGEX = /[\\"]/g;
const EMPTY_STRING = "";

export const fieldsToSearchRootTrials = [
  "identifiers.external_uuid",
  "study.brief_title",
  "study.official_title",
  "study.phase",
  "study.overall_status",
  "study.design.intervention_model",
  "study.design.observational_model",
  "study.design.primary_purpose",
  "study.design.time_perspective",
  "study.design.masking",
  "study.design.masking_description",
  "study.design.intervention_model_description",
  "study.designOutcome.outcome_type",
  "study.designOutcome.measure",
  "study.designOutcome.time_frame",
  "study.designOutcome.description",
  "study.designGroup.group_type",
  "study.designGroup.title",
  "study.designGroup.description",
  "study.conditions",
  "study.keywords",
  "study.intervention.intervention_type",
  "study.intervention.name",
  "study.intervention.description",
  "study.intervention.intervention_other_names",
  "study.sponsor.agency_class",
  "study.enrollment_type",
  "study.eligibility.minimum_age"
];

export enum QueryVersion {
  V1,
  V2,
  V3, // supporting nested properties
  V4 // supporting new conditions
}

const getFilterMapForQueryVersion = (
  version: QueryVersion,
  useAllIndications = false
) => {
  const defaultFilterMap = useAllIndications ? filterMapV4 : filterMapV3;

  switch (version) {
    case QueryVersion.V1:
      return filterMapV1;
    case QueryVersion.V2:
    case QueryVersion.V3:
      return filterMapV2;
    case QueryVersion.V4:
      return defaultFilterMap;
    default:
      return defaultFilterMap;
  }
};

const addClauses = async (
  filter: any[],
  mustClauses: any[],
  filters: ClinicalTrialFilterInput[],
  version: QueryVersion = QueryVersion.V3,
  queryParserService?: QueryParserService,
  parsedQueryTreeToElasticsearchQueriesService?: ParsedQueryTreeToElasticsearchQueriesService,
  queryUnderstandingServiceClient?: QueryUnderstandingServiceClient,
  logger?: Logger,
  useAllIndications = false
) => {
  const useNestedPath = [QueryVersion.V3, QueryVersion.V4].includes(version);
  const filterMap = getFilterMapForQueryVersion(version, useAllIndications);

  for (const filterInput of filters) {
    const mapping = filterMap[filterInput.name];

    if (mapping && !mapping.disabled && filterInput.value) {
      let filterValues = JSON.parse(filterInput.value);

      if (filterValues) {
        if (mapping.transformValues) {
          filterValues = mapping.transformValues(filterValues);
        }

        if (mapping.isDate) {
          filter.push({
            range: {
              [mapping.fields[0]]: {
                gte: filterValues.from,
                lte: filterValues.to
              }
            }
          });
        } else if (mapping.isRange) {
          const name = mapping.fields[0];
          const range = {
            [name]: {
              gte: filterValues.min,
              lte: filterValues.max
            }
          };
          filter.push({ range });
        } else if (mapping.isMultiFieldRange) {
          const minField = mapping.fields[0];
          const maxField = mapping.fields[1];
          const minRange = {
            range: {
              [minField]: {
                gte: filterValues.min
              }
            }
          };
          const maxRange = {
            range: {
              [maxField]: {
                lte: filterValues.max
              }
            }
          };

          filter.push(minRange, maxRange);
        } else if (mapping.scriptFilter) {
          const script = mapping.scriptFilter(filterValues);

          if (script) {
            console.log(`Adding script filter ${script}`);
            filter.push(script);
          } else {
            console.log(
              `Unable to create script for filter: ${filterInput.name}`
            );
          }
        } else if (
          mapping.isMulti &&
          Array.isArray(filterValues) &&
          filterValues.length
        ) {
          // This will create set of should clauses to be included
          // in the must, where one of the values should match this field

          const should: any[] = [];
          const must: QueryDslQueryContainer[] = [];
          const must_not: QueryDslQueryContainer[] = [];
          let synonymizedQueries: string[] = [];
          if (
            queryUnderstandingServiceClient &&
            filterInput.name === "Conditions"
          ) {
            synonymizedQueries =
              await fetchSynonymsFromQueryUnderstandingService(
                filterValues,
                queryUnderstandingServiceClient,
                logger
              );
          } else {
            synonymizedQueries.push(...filterValues);
          }

          if (mapping.useQueryComponents) {
            const queryComponents =
              mapping.getQueryComponents &&
              mapping.getQueryComponents(filterValues);

            if (queryComponents?.musts) {
              must.push(...queryComponents.musts);
            }

            if (queryComponents?.should) {
              should.push(...queryComponents.should);
            }

            if (queryComponents?.must_not) {
              must_not.push(...queryComponents.must_not);
            }
          } else {
            for (const [index] of filterValues.entries()) {
              let query;
              if (filterInput.name === "Conditions") {
                query = {
                  simple_query_string: {
                    query: synonymizedQueries[index],
                    fields: mapping.useAutocomplete
                      ? mapping.fields.map((f) => `${f}.autocomplete`)
                      : mapping.fields,
                    default_operator: "AND"
                  }
                };
              } else if (mapping.termQuery) {
                // Use terms query for exact matching when termQuery is enabled
                const fieldName = mapping.fields[0] + ".keyword";
                // Remove extra quotes from the value (e.g., "China" -> China)
                const cleanValue = synonymizedQueries[index].replace(
                  DOUBLE_QUOTES_REGEX,
                  EMPTY_STRING
                );

                query = {
                  terms: {
                    [fieldName]: [cleanValue]
                  }
                };
              } else {
                query = {
                  query_string: {
                    query: synonymizedQueries[index],
                    fields: mapping.useAutocomplete
                      ? mapping.fields.map((f) => `${f}.autocomplete`)
                      : mapping.fields
                  }
                };
              }

              if (mapping.nestedPath && useNestedPath) {
                should.push({
                  nested: {
                    path: mapping.nestedPath,
                    query
                  }
                });
              } else {
                should.push(query);
              }
            }
          }

          // Any custom matches that need to be applied
          mapping.addShouldsToMust &&
            mapping.addShouldsToMust(filterValues, should);

          // Add a new "must" clause that incorporates the "should", "must", and "must_not" conditions.
          // - "should" defines optional conditions, at least one of which must match if present.
          // - "must" defines mandatory conditions that all documents must satisfy if present.
          // - "must_not" defines conditions that documents must not satisfy if present.
          // If no conditions exist for a specific clause, it is omitted to simplify the query.
          mustClauses.push({
            bool: {
              should: should.length ? should : undefined,
              must: must.length ? must : undefined,
              must_not: must_not.length ? must_not : undefined
            }
          });
        } else if (
          (filterInput.name == "Inclusion" ||
            filterInput.name == "Exclusion") &&
          Array.isArray(filterValues) &&
          filterValues.length &&
          Array.isArray(filterValues[0])
        ) {
          let query = "";
          //Create advanced query to send to parser for IE criteria
          filterValues.forEach((value, index) => {
            query +=
              "(" +
              value.map((criteria: string) => criteria).join(" OR ") +
              ")";
            query += index == filterValues.length - 1 ? EMPTY_STRING : " AND ";
          });
          if (
            queryParserService &&
            parsedQueryTreeToElasticsearchQueriesService
          ) {
            const parsedQueryTree = queryParserService.parseQuery(query);

            const elasticQueryGenerated =
              parsedQueryTreeToElasticsearchQueriesService.parse(
                parsedQueryTree,
                mapping.fields
              );
            filter.push(elasticQueryGenerated);
          }
        } else {
          filter.push({
            simple_query_string: {
              query: filterValues,
              flags: "phrase",
              default_operator: "AND",
              fields: mapping.useAutocomplete
                ? mapping.fields.map((f) => `${f}.autocomplete`)
                : mapping.fields
            }
          });
        }
      }
    } else {
      if (!mapping) {
        console.log(`Mapping not found for: ${filterInput.name}`);
      } else if (mapping.disabled) {
        console.log(
          `Mapping found, but it is disabled for ES: ${filterInput.name}`
        );
      }
    }
  }
};

export const createQuery = async (
  input: ClinicalTrialInput | ClinicalTrialFilterAutocompleteInput,
  version = QueryVersion.V1,
  aggregations = trialsAggregations as Record<
    string,
    AggregationsAggregationContainer
  >,
  logger?: Logger,
  queryParserService?: QueryParserService,
  parsedQueryTreeToElasticsearchQueriesService?: ParsedQueryTreeToElasticsearchQueriesService,
  queryUnderstandingServiceClient?: QueryUnderstandingServiceClient
): Promise<any> => {
  let { filters: inputFilters } = input;
  const autocompleteFilterField = (
    input as ClinicalTrialFilterAutocompleteInput
  ).filterField;
  const useNDP = [QueryVersion.V2, QueryVersion.V3, QueryVersion.V4].includes(
    version
  );
  const filterMap = getFilterMapForQueryVersion(version);
  const aggsMap = useNDP && input.idsOnly !== true ? { ...aggregations } : {};

  logger &&
    logger.debug(
      `ES query input with useNDP=${useNDP}: ${JSON.stringify(input, null, 2)}`
    );

  const queryFilter = inputFilters?.find((f) => f.name === "SearchQuery");
  const query = queryFilter?.value as string;
  const isAdvancedQuery = HAS_ADVANCED_OPERATORS.test(query);
  let synonymizedQuery = [query];
  let parsedQuery;
  let trialsRootQueryForAdvancedOperators;
  if (queryUnderstandingServiceClient && !isAdvancedQuery) {
    synonymizedQuery = await fetchSynonymsFromQueryUnderstandingService(
      [query],
      queryUnderstandingServiceClient,
      logger
    );
  } else if (
    isAdvancedQuery &&
    queryParserService &&
    parsedQueryTreeToElasticsearchQueriesService
  ) {
    parsedQuery = queryParserService.parseQuery(query);
    trialsRootQueryForAdvancedOperators =
      parsedQueryTreeToElasticsearchQueriesService.parse(
        parsedQuery,
        fieldsToSearchRootTrials
      );
  }

  const filters: QueryDslQueryContainer[] = [];
  const mustClauses: any[] = [];
  const shouldClauses: any[] = [];
  const finalTrialsRootQuery = isAdvancedQuery
    ? trialsRootQueryForAdvancedOperators
    : buildRootTrialsQuery(synonymizedQuery[0]);
  if (query) {
    mustClauses.push({
      bool: {
        should: [
          buildNestedFacilityQuery(query),
          buildNestedPersonQuery(query),
          finalTrialsRootQuery
        ]
      }
    });
  }

  if (inputFilters) {
    // **
    // Special case for Study Completion Date to use Estimated End Date.
    inputFilters = inputFilters.map((val) =>
      val.name === "StudyCompletionDate"
        ? { ...val, name: "EstimatedEndDate" }
        : val
    );

    await addClauses(
      filters,
      mustClauses,
      inputFilters.filter(
        (f) =>
          f.name !== "SearchQuery" &&
          /**
           * Ignore sponsor fields if we are querying for sponsor autocomplete
           * We do this to insure that counts are specific to that individual sponsor not filtered by other sponsors in the filter inputs
           */
          (f.name !== "Sponsor" ||
            autocompleteFilterField !==
              ClinicalTrialFilterAutocompleteFilterField.SPONSORS) &&
          (f.name !== "Biomarkers" ||
            autocompleteFilterField !==
              ClinicalTrialFilterAutocompleteFilterField.BIOMARKERS)
      ),
      version,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient,
      logger
    );
  }

  const enrollmentFilter = inputFilters?.find((val) => {
    return val.name === "Enrollment";
  });

  if (enrollmentFilter && enrollmentFilter.value) {
    const filterValues = JSON.parse(enrollmentFilter.value);
    (aggsMap as Record<string, unknown>).total_enrollment = {
      ...(aggsMap as Record<string, object>).total_enrollment,
      filter: {
        range: {
          "study.enrollment": {
            gte: filterValues.min,
            lte: filterValues.max
          }
        }
      }
    };
  }

  const idField = useNDP ? "identifiers.external_uuid" : "studyId";
  if (input.ids && input.ids.length) {
    if (useNDP) {
      const nonH1Ids = input.ids.filter(
        (inputId) => !inputId.startsWith("CT-")
      );
      const h1Ids = input.ids.filter((inputId) => inputId.startsWith("CT-"));
      if (nonH1Ids.length > 0) {
        mustClauses.push({
          terms: { [`${idField}.keyword`]: nonH1Ids }
        });
      }
      if (h1Ids.length > 0) {
        mustClauses.push({
          ids: { values: h1Ids }
        });
      }
    } else {
      const ids = input.ids
        .filter((inputId) => !inputId.startsWith("NCT"))
        .filter((inputId) => !inputId.startsWith("CT-"));
      const nctIds = input.ids
        .filter((inputId) => inputId.startsWith("NCT"))
        .filter((inputId) => !inputId.startsWith("CT-"));
      if (ids.length > 0) {
        mustClauses.push({ terms: { studyId: ids } });
      }
      if (nctIds.length > 0) {
        mustClauses.push({ terms: { studyNctId: nctIds } });
      }
      if (nctIds.length === 0 && ids.length === 0) {
        mustClauses.push({ terms: { studyId: [NO_STUDY_ID_PLACEHOLDER] } });
      }
    }
  }

  const _source = input.idsOnly ? { _source: [idField] } : undefined;

  const sortFilterField = filterMap[input.sort.sortBy];
  const sortField = sortFilterField.fields[0];

  const sortDirection = input.sort.direction;

  // Custom script sort, or standard desc/asc
  const sort = [
    sortFilterField.scriptSort
      ? { _script: sortFilterField.scriptSort(sortDirection) }
      : {
          [sortField + (sortFilterField.isKeywordSort ? ".keyword" : "")]:
            sortDirection === "Desc" ? "desc" : "asc"
        }
  ];

  const boolSection = {
    filter: filters
  } as { must?: any[]; filter: any[]; should?: any };

  if (shouldClauses.length > 0) {
    boolSection.should = shouldClauses;
  }

  if (mustClauses.length > 0) {
    boolSection.must = mustClauses;
  }

  return {
    from: input.offset,
    size: input.limit,
    track_total_hits: true,
    ...(input.idsOnly ? _source : {}),

    sort,
    query:
      filters.length || mustClauses.length || shouldClauses.length
        ? {
            bool: boolSection
          }
        : { match_all: {} },

    aggs: aggsMap
  };
};

export const createQueryForAutocomplete = async (
  input: ClinicalTrialInput | ClinicalTrialFilterAutocompleteInput,
  version = QueryVersion.V1,
  aggregations: Record<string, AggregationsAggregationContainer>,
  logger?: Logger,
  queryParserService?: QueryParserService,
  parsedQueryTreeToElasticsearchQueriesService?: ParsedQueryTreeToElasticsearchQueriesService,
  queryUnderstandingServiceClient?: QueryUnderstandingServiceClient
): Promise<any> => {
  const request = await createQuery(
    input,
    version,
    aggregations,
    logger,
    queryParserService,
    parsedQueryTreeToElasticsearchQueriesService,
    queryUnderstandingServiceClient
  );

  return {
    size: 0,
    track_total_hits: true,
    _source: false,
    sort: request.sort,
    query: request.query,
    aggs: request.aggs
  };
};

export const createQueryWithCTMS = async (
  input: ClinicalTrialInput | ClinicalTrialFilterAutocompleteInput,
  aggregations = trialsAggregations as Record<
    string,
    AggregationsAggregationContainer
  >,
  isBulkSearch = false,
  queryParserService?: QueryParserService,
  parsedQueryTreeToElasticsearchQueriesService?: ParsedQueryTreeToElasticsearchQueriesService,
  queryUnderstandingServiceClient?: QueryUnderstandingServiceClient
): Promise<estypes.SearchRequest> => {
  let { filters: inputFilters } = input;
  const autocompleteFilterField = (
    input as ClinicalTrialFilterAutocompleteInput
  ).filterField;
  let aggs = {};

  if (!isBulkSearch && !input.idsOnly) {
    aggs = { ...aggregations };
  }

  const useAllIndications =
    "useAllIndications" in input && typeof input.useAllIndications === "boolean"
      ? input.useAllIndications
      : false;
  const queryFilter = inputFilters?.find((f) => f.name === "SearchQuery");
  const query = queryFilter?.value as string;
  const isAdvancedQuery = HAS_ADVANCED_OPERATORS.test(query);
  let synonymizedQuery = [query];
  let parsedQuery;
  let trialsRootQueryForAdvancedOperators;
  if (queryUnderstandingServiceClient && !isAdvancedQuery) {
    synonymizedQuery = await fetchSynonymsFromQueryUnderstandingService(
      [query],
      queryUnderstandingServiceClient
    );
  } else if (
    isAdvancedQuery &&
    queryParserService &&
    parsedQueryTreeToElasticsearchQueriesService
  ) {
    parsedQuery = queryParserService.parseQuery(query);
    trialsRootQueryForAdvancedOperators =
      parsedQueryTreeToElasticsearchQueriesService.parse(
        parsedQuery,
        fieldsToSearchRootTrials
      );
  }
  const filters: QueryDslQueryContainer[] = [];
  const mustClauses: QueryDslQueryContainer[] = [];
  const shouldClauses: QueryDslQueryContainer[] = [];

  if (
    inputFilters &&
    _.find(inputFilters, { name: "HasCTMSData", value: "true" }) &&
    input.projectId
  ) {
    mustClauses.push({
      nested: {
        path: "ctms",
        query: {
          term: {
            "ctms.project_id": input.projectId
          }
        }
      }
    });
    _.remove(inputFilters, { name: "HasCTMSData" });
  }
  const finalTrialsRootQuery = isAdvancedQuery
    ? trialsRootQueryForAdvancedOperators
    : buildRootTrialsQuery(synonymizedQuery[0]);
  if (query) {
    mustClauses.push({
      bool: {
        should: [
          buildNestedFacilityQuery(query),
          buildNestedPersonQuery(query),
          finalTrialsRootQuery!
        ]
      }
    });
  }

  if (inputFilters) {
    // **
    // Special case for Study Completion Date to use Estimated End Date.
    inputFilters = inputFilters.map((val) =>
      val.name === "StudyCompletionDate"
        ? { ...val, name: "EstimatedEndDate" }
        : val
    );

    await addClauses(
      filters,
      mustClauses,
      inputFilters.filter(
        (f) =>
          f.name !== "SearchQuery" &&
          /**
           * Ignore sponsor fields if we are querying for sponsor autocomplete
           * We do this to insure that counts are specific to that individual sponsor not filtered by other sponsors in the filter inputs
           */
          (f.name !== "Sponsor" ||
            autocompleteFilterField !==
              ClinicalTrialFilterAutocompleteFilterField.SPONSORS) &&
          (f.name !== "Biomarkers" ||
            autocompleteFilterField !==
              ClinicalTrialFilterAutocompleteFilterField.BIOMARKERS)
      ),
      QueryVersion.V4,
      queryParserService,
      parsedQueryTreeToElasticsearchQueriesService,
      queryUnderstandingServiceClient,
      undefined, // logger
      useAllIndications
    );
  }

  const enrollmentFilter = inputFilters?.find((val) => {
    return val.name === "Enrollment";
  });

  if (enrollmentFilter && enrollmentFilter.value) {
    const filterValues = JSON.parse(enrollmentFilter.value);
    (aggs as Record<string, unknown>).total_enrollment = {
      ...(aggs as Record<string, object>).total_enrollment,
      filter: {
        range: {
          "study.enrollment": {
            gte: filterValues.min,
            lte: filterValues.max
          }
        }
      }
    };
  }

  if (input.ids && input.ids.length) {
    const nonH1Ids = input.ids.filter((inputId) => !inputId.startsWith("CT-"));
    const h1Ids = input.ids.filter((inputId) => inputId.startsWith("CT-"));
    if (nonH1Ids.length > 0) {
      mustClauses.push({
        bool: {
          should: [
            {
              terms: { ["identifiers.external_uuid.keyword"]: nonH1Ids }
            },
            {
              terms: {
                h1dn_clinical_trial_id: nonH1Ids
              }
            }
          ]
        }
      });
    }
    if (h1Ids.length > 0) {
      mustClauses.push({
        ids: { values: h1Ids }
      });
    }
  }

  let _source = undefined;

  if (isBulkSearch) {
    _source = { _source: ["h1_clinical_trial_id"] };
  } else if (input.idsOnly) {
    _source = { _source: ["identifiers.external_uuid"] };
  }

  const sortFilterField = filterMapV3[input.sort.sortBy];
  const sortField = sortFilterField.fields[0];

  const sortDirection = input.sort.direction;

  // Custom script sort, or standard desc/asc
  const sort: Sort = [
    sortFilterField.scriptSort
      ? { _script: sortFilterField.scriptSort(sortDirection) }
      : {
          [sortField + (sortFilterField.isKeywordSort ? ".keyword" : "")]:
            sortDirection === "Desc" ? "desc" : "asc"
        }
  ];

  filters.push(buildProjectIdFilter(input.projectId));

  const bool: QueryDslBoolQuery = {
    filter: filters
  };

  if (shouldClauses.length > 0) {
    bool.should = shouldClauses;
  }

  if (mustClauses.length > 0) {
    bool.must = mustClauses;
  }

  return {
    from: input.offset,
    size: input.limit,
    track_total_hits: true,
    ..._source,
    sort,
    query: {
      bool
    },
    aggs
  };
};

export const createQueryWithCTMSForAutocomplete = async (
  input: ClinicalTrialInput,
  aggregations: Record<string, AggregationsAggregationContainer>,
  isBulkSearch = false,
  queryParserService?: QueryParserService,
  parsedQueryTreeToElasticsearchQueriesService?: ParsedQueryTreeToElasticsearchQueriesService,
  queryUnderstandingServiceClient?: QueryUnderstandingServiceClient
): Promise<estypes.SearchRequest> => {
  const request = await createQueryWithCTMS(
    input,
    aggregations,
    isBulkSearch,
    queryParserService,
    parsedQueryTreeToElasticsearchQueriesService,
    queryUnderstandingServiceClient
  );

  return {
    size: 0,
    track_total_hits: true,
    _source: false,
    sort: request.sort,
    query: request.query,
    aggs: request.aggs
  };
};

const buildProjectIdFilter = (projectId?: string) => {
  const should: QueryDslQueryContainer[] = [
    {
      bool: {
        must_not: [buildFieldExistsQuery(PROJECT_ID_FIELD)]
      }
    }
  ];

  if (projectId) {
    should.push({
      bool: {
        must: [
          buildFieldExistsQuery(PROJECT_ID_FIELD),
          {
            term: {
              [PROJECT_ID_FIELD]: projectId
            }
          }
        ]
      }
    });
  }
  return {
    bool: {
      should
    }
  };
};

const buildFieldExistsQuery = (field: string) => {
  return {
    exists: {
      field
    }
  };
};

const buildNestedFacilityQuery = (query: string): QueryDslQueryContainer => {
  return {
    nested: {
      path: "facility",
      query: {
        bool: {
          filter: [
            {
              simple_query_string: {
                query,
                default_operator: "AND",
                fields: [
                  "facility.name",
                  "facility.city",
                  "facility.state",
                  "facility.zip",
                  "facility.country"
                ]
              }
            }
          ]
        }
      }
    }
  };
};
const buildNestedPersonQuery = (query: string): QueryDslQueryContainer => {
  return {
    nested: {
      path: "person",
      query: {
        bool: {
          filter: [
            {
              simple_query_string: {
                query,
                default_operator: "AND",
                fields: ["person.name"]
              }
            }
          ]
        }
      }
    }
  };
};
const buildRootTrialsQuery = (query: string): QueryDslQueryContainer => {
  return {
    bool: {
      filter: [
        {
          simple_query_string: {
            query: query,
            default_operator: "AND",
            fields: [
              "identifiers.external_uuid",
              "study.brief_title",
              "study.official_title",
              "study.phase",
              "study.overall_status",
              "study.design.intervention_model",
              "study.design.observational_model",
              "study.design.primary_purpose",
              "study.design.time_perspective",
              "study.design.masking",
              "study.design.masking_description",
              "study.design.intervention_model_description",
              "study.designOutcome.outcome_type",
              "study.designOutcome.measure",
              "study.designOutcome.time_frame",
              "study.designOutcome.description",
              "study.designGroup.group_type",
              "study.designGroup.title",
              "study.designGroup.description",
              "study.conditions",
              "study.keywords",
              "study.intervention.intervention_type",
              "study.intervention.name",
              "study.intervention.description",
              "study.intervention.intervention_other_names",
              "study.sponsor.agency_class",
              "study.enrollment_type",
              "study.eligibility.minimum_age"
            ]
          }
        }
      ]
    }
  };
};

async function fetchSynonymsFromQueryUnderstandingService(
  filterValues: string[],
  queryUnderstandingServiceClient: QueryUnderstandingServiceClient,
  logger?: Logger
) {
  try {
    const synonymizedQueries = await Promise.all(
      filterValues.map(async (value) => {
        return (
          await queryUnderstandingServiceClient.analyze(
            value.replace(DOUBLE_QUOTES_REGEX, EMPTY_STRING),
            "eng"
          )
        )
          .getAugmentedQuery()
          .replace(ALL_ORs, OR)
          .replace(ALL_ANDs, EMPTY_STRING)
          .replace(ALL_UNICODE_DOUBLE_QUOTES, ASCII_DOUBLE_QUOTES)
          .replace(/([.*+?^=!:${}[\]/\\])/g, "\\$1");
      })
    );
    return synonymizedQueries;
  } catch (err) {
    logger &&
      logger.error(
        { err },
        "Call to Query Understanding Service failed to fetch synonyms for conditions"
      );
    return filterValues;
  }
}
