import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { ProfileFilterValue } from "@h1nyc/search-sdk";
import { AbstractProfileAsset } from "./ProfileAsset";

export class DrgDiagnosisProfileAsset extends AbstractProfileAsset {
  matchFields = [
    "DRG_diagnoses.description_eng",
    "DRG_diagnoses.diagnosisCode_eng.autocomplete_search"
  ];

  buildMetricsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    let aggs;

    if (terms || filters?.length) {
      aggs = this.getFilteredAggregation(this.getMetrics(), terms, filters);
    } else {
      aggs = this.getMetrics();
    }

    return {
      diagnoses: {
        nested: {
          path: "DRG_diagnoses"
        },
        aggs
      }
    };
  }

  private getFilteredAggregation(
    subAggs: any,
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    const must: Array<QueryDslQueryContainer> = [];

    if (terms) {
      must.push(terms);
    }

    if (filters?.length) {
      must.push(...this.buildFilters(filters));
    }

    // TODO: this appears to be unreachable and therefore untestable
    if (!must.length) {
      throw new Error(
        "Must clause cannot be empty. Terms or filters must be applied"
      );
    }

    return {
      diagnosisFilters: {
        filters: {
          filters: [
            {
              bool: {
                must
              }
            }
          ]
        },
        aggs: subAggs
      }
    };
  }

  private getMetrics() {
    return {
      internalCount: {
        sum: {
          field: "DRG_diagnoses.internalCount"
        }
      }
    };
  }
}
