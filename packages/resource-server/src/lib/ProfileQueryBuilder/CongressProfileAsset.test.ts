import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { ProfileFilterValue, ProfileFilterValueType } from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";
import { CongressProfileAsset } from "./CongressProfileAsset";

describe("buildMetricsAggregation", () => {
  it("should return unfiltered aggregations when neither terms nor filters are supplied", () => {
    const congressProfileAsset = new CongressProfileAsset();

    const query = congressProfileAsset.buildMetricsAggregation();

    expect(query).toEqual({
      congresses: {
        nested: {
          path: "congress"
        },
        aggs: {
          yearTotals: {
            date_histogram: {
              field: "congress.endDate",
              calendar_interval: "year"
            }
          },
          postersTotal: {
            filter: {
              term: {
                "congress.type": "Poster"
              }
            }
          },
          sessionsTotal: {
            filter: {
              term: {
                "congress.type": "Session"
              }
            }
          },
          topCongresses: {
            terms: {
              field: "congress.name_eng",
              size: 3,
              order: {
                _count: "desc"
              }
            }
          }
        }
      }
    });
  });

  it("should include all supplied filters in the query", () => {
    const congressProfileAsset = new CongressProfileAsset();

    const minDate = new Date(faker.date.past()).getTime();
    const maxDate = new Date(faker.date.past()).getTime();

    const filters: Array<ProfileFilterValue> = [
      {
        type: ProfileFilterValueType.CongressDate,
        value: {
          min: minDate,
          max: maxDate
        }
      }
    ];

    const query = congressProfileAsset.buildMetricsAggregation(
      undefined,
      filters
    );

    expect(query).toEqual({
      congresses: {
        nested: {
          path: "congress"
        },
        aggs: {
          congressFilters: {
            filters: {
              filters: [
                {
                  bool: {
                    must: [
                      {
                        range: {
                          "congress.endDate": {
                            gte: minDate,
                            lte: maxDate
                          }
                        }
                      }
                    ]
                  }
                }
              ]
            },
            aggs: {
              yearTotals: {
                date_histogram: {
                  field: "congress.endDate",
                  calendar_interval: "year"
                }
              },
              postersTotal: {
                filter: {
                  term: {
                    "congress.type": "Poster"
                  }
                }
              },
              sessionsTotal: {
                filter: {
                  term: {
                    "congress.type": "Session"
                  }
                }
              },
              topCongresses: {
                terms: {
                  field: "congress.name_eng",
                  size: 3,
                  order: {
                    _count: "desc"
                  }
                }
              }
            }
          }
        }
      }
    });
  });

  it("should not apply any aggregation filters when filters is an empty array", () => {
    const congressProfileAsset = new CongressProfileAsset();

    const query = congressProfileAsset.buildMetricsAggregation(undefined, []);

    expect(query).toEqual({
      congresses: {
        nested: {
          path: "congress"
        },
        aggs: {
          yearTotals: {
            date_histogram: {
              field: "congress.endDate",
              calendar_interval: "year"
            }
          },
          postersTotal: {
            filter: {
              term: {
                "congress.type": "Poster"
              }
            }
          },
          sessionsTotal: {
            filter: {
              term: {
                "congress.type": "Session"
              }
            }
          },
          topCongresses: {
            terms: {
              field: "congress.name_eng",
              size: 3,
              order: {
                _count: "desc"
              }
            }
          }
        }
      }
    });
  });

  describe("query container", () => {
    it("should only include filter for terms when no filters are supplied", () => {
      const congressProfileAsset = new CongressProfileAsset();

      const queryContainer: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };

      const query =
        congressProfileAsset.buildMetricsAggregation(queryContainer);

      expect(query).toEqual({
        congresses: {
          nested: {
            path: "congress"
          },
          aggs: {
            congressFilters: {
              filters: {
                filters: [
                  {
                    bool: {
                      must: [queryContainer]
                    }
                  }
                ]
              },
              aggs: {
                yearTotals: {
                  date_histogram: {
                    field: "congress.endDate",
                    calendar_interval: "year"
                  }
                },
                postersTotal: {
                  filter: {
                    term: {
                      "congress.type": "Poster"
                    }
                  }
                },
                sessionsTotal: {
                  filter: {
                    term: {
                      "congress.type": "Session"
                    }
                  }
                },
                topCongresses: {
                  terms: {
                    field: "congress.name_eng",
                    size: 3,
                    order: {
                      _count: "desc"
                    }
                  }
                }
              }
            }
          }
        }
      });
    });

    it("should apply all supplied filters in addition to terms", () => {
      const congressProfileAsset = new CongressProfileAsset();

      const queryContainer: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };

      const minDate = new Date(faker.date.past()).getTime();
      const maxDate = new Date(faker.date.past()).getTime();

      const filters: Array<ProfileFilterValue> = [
        {
          type: ProfileFilterValueType.CongressDate,
          value: {
            min: minDate,
            max: maxDate
          }
        }
      ];

      const query = congressProfileAsset.buildMetricsAggregation(
        queryContainer,
        filters
      );

      expect(query).toEqual({
        congresses: {
          nested: {
            path: "congress"
          },
          aggs: {
            congressFilters: {
              filters: {
                filters: [
                  {
                    bool: {
                      must: [
                        queryContainer,
                        {
                          range: {
                            "congress.endDate": {
                              gte: minDate,
                              lte: maxDate
                            }
                          }
                        }
                      ]
                    }
                  }
                ]
              },
              aggs: {
                yearTotals: {
                  date_histogram: {
                    field: "congress.endDate",
                    calendar_interval: "year"
                  }
                },
                postersTotal: {
                  filter: {
                    term: {
                      "congress.type": "Poster"
                    }
                  }
                },
                sessionsTotal: {
                  filter: {
                    term: {
                      "congress.type": "Session"
                    }
                  }
                },
                topCongresses: {
                  terms: {
                    field: "congress.name_eng",
                    size: 3,
                    order: {
                      _count: "desc"
                    }
                  }
                }
              }
            }
          }
        }
      });
    });
  });
});
