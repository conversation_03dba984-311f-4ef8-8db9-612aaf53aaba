import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import {
  DateFilterValue,
  ProfileFilterValue,
  TermFilterValue,
  TermsFilterValue,
  SortOptions
} from "@h1nyc/search-sdk";

interface ProfileAggregation {
  [key: string]: any;
}

interface FilterTypeMap {
  [key: string]: ProfileFilterType;
}

interface FilterFieldMap {
  [key: string]: string;
}

export enum ProfileFilterType {
  Term,
  Terms,
  Date,
  Status,
  Keyword,
  HCPRole,
  Phase,
  Sponsor,
  SponsorType,
  StudyType,
  TrialId,
  Indication,
  Biomarker
}

export interface ProfileAsset {
  matchFields: string[];
  filterTypeMap: FilterTypeMap;
  filterFieldMap: FilterFieldMap;

  buildIdsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[],
    size?: number,
    sortOptions?: SortOptions<string>
  ): ProfileAggregation;

  buildMetricsAggregation(
    terms?: QueryDslQueryContainer,
    filter?: ProfileFilterValue[]
  ): ProfileAggregation;
}

export abstract class AbstractProfileAsset implements ProfileAsset {
  matchFields: string[] = [];
  filterTypeMap: FilterTypeMap = {};
  filterFieldMap: FilterFieldMap = {};

  buildIdsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[],
    size?: number,
    sortOptions?: SortOptions<string>
  ): ProfileAggregation {
    throw new Error("Method not implemented.");
  }

  buildMetricsAggregation(
    terms?: QueryDslQueryContainer,
    filter?: ProfileFilterValue[]
  ): ProfileAggregation {
    throw new Error("Method not implemented.");
  }

  protected buildFilters(filters: ProfileFilterValue[]) {
    return filters.map((filter) => {
      const type = this.filterTypeMap[filter.type];
      const field = this.filterFieldMap[filter.type];

      switch (type) {
        case ProfileFilterType.Terms:
        case ProfileFilterType.Keyword:
        case ProfileFilterType.Status:
        case ProfileFilterType.HCPRole:
        case ProfileFilterType.Phase:
        case ProfileFilterType.Sponsor:
        case ProfileFilterType.TrialId:
        case ProfileFilterType.StudyType:
        case ProfileFilterType.SponsorType:
        case ProfileFilterType.Indication:
        case ProfileFilterType.Biomarker:
          return this.buildTermsFilter(
            field,
            (filter as TermsFilterValue).value
          );

        case ProfileFilterType.Term:
          return this.buildTermFilter(field, (filter as TermFilterValue).value);

        case ProfileFilterType.Date:
          return this.buildDateFilter(field, (filter as DateFilterValue).value);

        default:
          throw new Error(`Unknown filter type "${type}" passed to filters`);
      }
    });
  }

  private buildTermsFilter(field: string, value: TermsFilterValue["value"]) {
    return {
      terms: {
        [field]: value
      }
    };
  }

  private buildTermFilter(field: string, value: TermFilterValue["value"]) {
    return {
      term: {
        [field]: value
      }
    };
  }

  private buildDateFilter(field: string, value: DateFilterValue["value"]) {
    return {
      range: {
        [field]: {
          ...(value.min && { gte: value.min }),
          ...(value.max && { lte: value.max })
        }
      }
    };
  }
}
