import {
  AggregationsAggregation<PERSON>ontainer,
  QueryDslQueryContainer
} from "@elastic/elasticsearch/lib/api/types";
import {
  ProfileFilterValue,
  ProfileFilterValueType,
  SortDirection,
  TrialFields,
  TrialSortOptions
} from "@h1nyc/search-sdk";
import { AbstractProfileAsset, ProfileFilterType } from "./ProfileAsset";
import { Page } from "@h1nyc/account-sdk/dist/interfaces/Pagination";

const DEFAULT_FROM = 0;
const DEFAULT_SIZE = 10000;
export class TrialProfileAsset extends AbstractProfileAsset {
  matchFields = [
    "trials.briefTitle_eng",
    "trials.conditions_eng",
    "trials.interventions_eng",
    "trials.keywords_eng",
    "trials.officialTitle_eng",
    "trials.summary_eng"
  ];

  filterTypeMap = {
    [ProfileFilterValueType.TrialDate]: ProfileFilterType.Date,
    [ProfileFilterValueType.TrialStatus]: ProfileFilterType.Status,
    [ProfileFilterValueType.TrialKeyword]: ProfileFilterType.Keyword,
    [ProfileFilterValueType.TrialHCPRole]: ProfileFilterType.HCPRole,
    [ProfileFilterValueType.TrialPhase]: ProfileFilterType.Phase,
    [ProfileFilterValueType.TrialSponsor]: ProfileFilterType.Sponsor,
    [ProfileFilterValueType.TrialSponsorType]: ProfileFilterType.SponsorType,
    [ProfileFilterValueType.TrialStudyType]: ProfileFilterType.StudyType,
    [ProfileFilterValueType.TrialId]: ProfileFilterType.TrialId,
    [ProfileFilterValueType.Indication]: ProfileFilterType.Indication,
    [ProfileFilterValueType.Biomarker]: ProfileFilterType.Biomarker
  };

  filterFieldMap = {
    [ProfileFilterValueType.TrialDate]: "trials.startDate",
    [ProfileFilterValueType.TrialStatus]: "trials.status_eng",
    [ProfileFilterValueType.TrialKeyword]: "trials.keywords_eng",
    [ProfileFilterValueType.TrialHCPRole]: "trials.roles",
    [ProfileFilterValueType.TrialPhase]: "trials.phase_eng",
    [ProfileFilterValueType.TrialSponsor]: "trials.sponsor_eng",
    [ProfileFilterValueType.TrialSponsorType]: "trials.sponsorType_eng",
    [ProfileFilterValueType.TrialStudyType]: "trials.type_eng",
    [ProfileFilterValueType.TrialId]: "trials.id",
    [ProfileFilterValueType.Indication]: "trials.kgIndications",
    [ProfileFilterValueType.Biomarker]: "trials.biomarker"
  };

  buildIdsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[],
    size?: number,
    sortOptions?: TrialSortOptions
  ) {
    const aggs = this.getFilteredAggregation(
      {
        ids: {
          terms: {
            size: size ?? 10000,
            field: "trials.id",
            ...(!!sortOptions?.direction &&
              !!sortOptions.sortBy && {
                order: {
                  sort_field: sortOptions.direction
                }
              })
          },
          ...(!!sortOptions?.direction &&
            !!sortOptions.sortBy && {
              aggs: {
                sort_field: {
                  ...(sortOptions.direction === SortDirection.Desc && {
                    max: {
                      field: sortOptions.sortBy
                    }
                  }),
                  ...(sortOptions.direction === SortDirection.Asc && {
                    min: {
                      field: sortOptions.sortBy
                    }
                  })
                }
              }
            })
        }
      },
      terms,
      filters
    );

    return {
      trials: {
        nested: {
          path: "trials"
        },
        aggs
      }
    };
  }

  buildPaginatedTrialsQuery(
    personId: string,
    projectId: string,
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[],
    page?: Page,
    sortOptions?: TrialSortOptions
  ): QueryDslQueryContainer {
    return {
      bool: {
        filter: [
          this.buildProjectIdFilter(projectId),
          this.buildPersonIdTermFilter(personId),
          this.buildNestedTrialQuery(terms, filters, page, sortOptions)
        ]
      }
    };
  }

  private buildProjectIdFilter(projectId: string) {
    return {
      term: {
        projectIds: projectId
      }
    };
  }

  private buildNestedTrialQuery(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[],
    page?: Page,
    sortOptions: TrialSortOptions = {
      sortBy: TrialFields.startDate,
      direction: SortDirection.Desc
    }
  ): QueryDslQueryContainer {
    const filter: QueryDslQueryContainer[] = [];

    if (terms) {
      filter.push(terms);
    }

    if (filters?.length) {
      filter.push(...this.buildFilters(filters));
    }

    const sort = this.buildSort(sortOptions.sortBy, sortOptions.direction);

    let query;
    if (!filter.length) {
      query = {
        match_all: {}
      };
    } else {
      query = {
        bool: {
          filter
        }
      };
    }

    return {
      nested: {
        path: "trials",
        query,
        inner_hits: {
          _source: false,
          docvalue_fields: [
            "trials.id",
            "trials.source",
            "trials.affiliatedBy"
          ],
          from: page?.offset || DEFAULT_FROM,
          size: page?.limit || DEFAULT_SIZE,
          sort
        }
      }
    };
  }

  private buildSort(
    sortBy = TrialFields.startDate,
    sortDirection = SortDirection.Desc
  ) {
    return [
      {
        [sortBy]: {
          order: sortDirection
        }
      }
    ];
  }

  private buildPersonIdTermFilter(personId: string): QueryDslQueryContainer {
    return {
      bool: {
        should: [
          {
            term: {
              id: personId
            }
          },
          {
            term: {
              h1dn_id: personId
            }
          }
        ],
        minimum_should_match: 1
      }
    };
  }

  buildMetricsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    let aggs;

    if (terms || filters?.length) {
      aggs = this.getFilteredAggregation(this.getMetrics(), terms, filters);
    } else {
      aggs = this.getMetrics();
    }

    return {
      trials: {
        nested: {
          path: "trials"
        },
        aggs
      }
    };
  }

  private getFilteredAggregation(
    subAggs: any,
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    const must: Array<QueryDslQueryContainer> = [];

    if (terms) {
      must.push(terms);
    }

    if (filters?.length) {
      must.push(...this.buildFilters(filters));
    }

    // TODO: this appears to be unreachable and therefore untestable
    if (!must.length) {
      throw new Error(
        "Must clause cannot be empty. Terms or filters must be applied"
      );
    }

    return {
      trialFilters: {
        filters: {
          filters: [
            {
              bool: {
                must
              }
            }
          ]
        },
        aggs: subAggs
      }
    };
  }

  private getMetrics(): Record<string, AggregationsAggregationContainer> {
    return {
      ids: {
        terms: {
          size: 10000,
          field: "trials.id"
        }
      },
      yearTotals: {
        date_histogram: {
          field: "trials.primaryCompletionDate",
          calendar_interval: "year"
        }
      },
      completedCount: {
        filter: {
          bool: {
            must: [
              {
                range: {
                  "trials.completionDate": {
                    lt: new Date().getTime()
                  }
                }
              }
            ]
          }
        }
      },
      terminatedCount: {
        filter: {
          bool: {
            should: [
              { term: { "trials.status_eng": "Terminated" } },
              { term: { "trials.status_eng": "Withdrawn" } },
              { term: { "trials.status_eng": "Terminated or Withdrawn" } }
            ]
          }
        }
      },
      inProgressCount: {
        filter: {
          bool: {
            must: [
              {
                terms: {
                  "trials.status_eng": [
                    "Not yet recruiting",
                    "Recruiting",
                    "Enrolling by invitation",
                    "Active, not recruiting",
                    "Suspended"
                  ]
                }
              }
            ]
          }
        }
      },
      topConditions: {
        terms: {
          field: "trials.conditions_eng.keyword",
          size: 5,
          order: { _count: "desc" }
        }
      },
      topInterventions: {
        terms: {
          field: "trials.interventions_eng.keyword",
          size: 5,
          order: { _count: "desc" }
        }
      }
    };
  }
}
