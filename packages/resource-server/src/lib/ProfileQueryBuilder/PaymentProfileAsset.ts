import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { ProfileFilterValue, ProfileFilterValueType } from "@h1nyc/search-sdk";
import { AbstractProfileAsset, ProfileFilterType } from "./ProfileAsset";

export class PaymentProfileAsset extends AbstractProfileAsset {
  matchFields: [
    "payments.associatedDrugOrDevice",
    "payments.payerCompany",
    "payments.natureOfPayment_eng"
  ];

  filterTypeMap = {
    [ProfileFilterValueType.PaymentDate]: ProfileFilterType.Date
  };

  filterFieldMap = {
    [ProfileFilterValueType.PaymentDate]: "payments.paymentDate"
  };

  buildIdsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    const aggs = this.getFilteredAggregation(
      {
        ids: {
          terms: {
            size: 10000,
            field: "payments.id"
          }
        }
      },
      terms,
      filters
    );

    return {
      payments: {
        nested: {
          path: "payments"
        },
        aggs
      }
    };
  }

  buildMetricsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    let aggs;

    if (terms || filters?.length) {
      aggs = this.getFilteredAggregation(this.getMetrics(), terms, filters);
    } else {
      aggs = this.getMetrics();
    }

    return {
      payments: {
        nested: {
          path: "payments"
        },
        aggs
      }
    };
  }

  private getFilteredAggregation(
    subAggs: any,
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    const must: Array<QueryDslQueryContainer> = [];

    if (terms) {
      must.push(terms);
    }

    if (filters?.length) {
      must.push(...this.buildFilters(filters));
    }

    // TODO: this appears to be unreachable and therefore untestable
    if (!must.length) {
      throw new Error(
        "Must clause cannot be empty. Terms or filters must be applied"
      );
    }

    return {
      paymentFilters: {
        filters: {
          filters: [
            {
              bool: {
                must
              }
            }
          ]
        },
        aggs: subAggs
      }
    };
  }

  private getMetrics() {
    return {
      totalPaymentsAmount: {
        sum: {
          field: "payments.amount"
        }
      }
    };
  }
}
