import {
  AggregationsAggregationContainer,
  QueryDslQueryContainer
} from "@elastic/elasticsearch/lib/api/types";
import {
  ProfileFilterValue,
  ProfileFilterValueType,
  SortDirection,
  PublicationSortOptions,
  PublicationsIsFirstOrderFilterValue,
  PublicationsIsLastOrderFilterValue,
  PublicationIsGuidelineFilterValue
} from "@h1nyc/search-sdk";
import { AbstractProfileAsset, ProfileFilterType } from "./ProfileAsset";

export class PublicationProfileAsset extends AbstractProfileAsset {
  matchFields = [
    "publications.publicationAbstract_eng",
    "publications.keywords_eng",
    "publications.title_eng",
    "publications.publicationAbstract_cmn",
    "publications.keywords_cmn",
    "publications.title_cmn",
    "publications.publicationAbstract_jpn",
    "publications.keywords_jpn",
    "publications.title_jpn"
  ];

  filterTypeMap = {
    [ProfileFilterValueType.PublicationType]: ProfileFilterType.Term,
    [ProfileFilterValueType.PublicationJournal]: ProfileFilterType.Terms,
    [ProfileFilterValueType.PublicationDate]: ProfileFilterType.Date,
    [ProfileFilterValueType.PublicationIsFirstOrder]: ProfileFilterType.Term,
    [ProfileFilterValueType.PublicationIsLastOrder]: ProfileFilterType.Term,
    [ProfileFilterValueType.PublicationIsGuideline]: ProfileFilterType.Term
  };

  filterFieldMap = {
    [ProfileFilterValueType.PublicationType]: "publications.type_eng",
    [ProfileFilterValueType.PublicationJournal]: "publications.journalName_eng",
    [ProfileFilterValueType.PublicationDate]: "publications.datePublished",
    [ProfileFilterValueType.PublicationIsFirstOrder]:
      "publications.isFirstOrder",
    [ProfileFilterValueType.PublicationIsLastOrder]: "publications.isLastOrder",
    [ProfileFilterValueType.PublicationIsGuideline]: "publications.type_eng"
  };

  buildIdsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[],
    size?: number,
    sortOptions?: PublicationSortOptions
  ) {
    const aggs = this.getFilteredAggregation(
      {
        ids: {
          terms: {
            size: size ?? 10000,
            field: "publications.id",
            ...(!!sortOptions?.direction &&
              !!sortOptions.sortBy && {
                order: {
                  sort_field: sortOptions.direction
                }
              })
          },
          ...(!!sortOptions?.direction &&
            !!sortOptions.sortBy && {
              aggs: {
                sort_field: {
                  ...(sortOptions.direction === SortDirection.Desc && {
                    max: {
                      field: sortOptions.sortBy
                    }
                  }),
                  ...(sortOptions.direction === SortDirection.Asc && {
                    min: {
                      field: sortOptions.sortBy
                    }
                  })
                }
              }
            })
        }
      },
      terms,
      filters
    );

    return {
      publications: {
        nested: {
          path: "publications"
        },
        aggs
      }
    };
  }

  buildMetricsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    let aggs;

    if (terms || filters?.length) {
      aggs = this.getFilteredAggregation(this.getMetrics(), terms, filters);
    } else {
      aggs = this.getMetrics();
    }

    return {
      publications: {
        nested: {
          path: "publications"
        },
        aggs
      }
    };
  }

  private getFilteredAggregation(
    subAggs: any,
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    const must: Array<QueryDslQueryContainer> = [];

    if (terms) {
      must.push(terms);
    }

    if (filters?.length) {
      const firstAuthorFilter = filters.find(
        (filter) =>
          filter.type === ProfileFilterValueType.PublicationIsFirstOrder
      ) as PublicationsIsFirstOrderFilterValue | undefined;
      const lastAuthorFilter = filters.find(
        (filter) =>
          filter.type === ProfileFilterValueType.PublicationIsLastOrder
      ) as PublicationsIsLastOrderFilterValue | undefined;
      const isGuidelineFilter = filters.find(
        (filter) =>
          filter.type === ProfileFilterValueType.PublicationIsGuideline
      ) as PublicationIsGuidelineFilterValue | undefined;

      const filtersToApply = filters.filter(
        (filter) =>
          filter.type !== ProfileFilterValueType.PublicationIsFirstOrder &&
          filter.type !== ProfileFilterValueType.PublicationIsLastOrder &&
          filter.type !== ProfileFilterValueType.PublicationIsGuideline
      );

      if (filtersToApply.length) {
        must.push(...this.buildFilters(filtersToApply));
      }

      const authorFilters = [];

      if (firstAuthorFilter?.value) {
        authorFilters.push({
          term: {
            [this.filterFieldMap[firstAuthorFilter.type]]: true
          }
        });
      }

      if (lastAuthorFilter?.value) {
        authorFilters.push({
          term: {
            [this.filterFieldMap[lastAuthorFilter.type]]: true
          }
        });
      }

      if (authorFilters.length) {
        must.push({
          bool: {
            should: authorFilters,
            minimum_should_match: 1
          }
        });
      }

      if (isGuidelineFilter?.value) {
        must.push(this.buildGuidelineFilters());
      }
    }

    // TODO: this appears to be unreachable and therefore untestable
    if (!must.length) {
      throw new Error(
        "Must clause cannot be empty. Terms or filters must be applied"
      );
    }

    return {
      publicationFilters: {
        filters: {
          filters: [
            {
              bool: {
                must
              }
            }
          ]
        },
        aggs: subAggs
      }
    };
  }

  private getMetrics(): Record<string, AggregationsAggregationContainer> {
    return {
      yearTotals: {
        date_histogram: {
          field: "publications.datePublished",
          calendar_interval: "year"
        }
      },
      citationCount: {
        sum: {
          field: "publications.citationCount"
        }
      },
      microBloggingCount: {
        sum: {
          field: "publications.microBloggingCount"
        }
      },
      guidelineCount: {
        filter: this.buildGuidelineFilters()
      }
    };
  }

  private buildGuidelineFilters(): QueryDslQueryContainer {
    return {
      bool: {
        should: [
          {
            terms: {
              "publications.type_eng": ["Guideline", "Practice Guideline"]
            }
          },
          {
            simple_query_string: {
              query: [
                '"practice update"',
                '"clinical practice guideline"',
                '"clinical practice guidelines"'
              ].join(" | "),
              fields: ["publications.keywords_eng"],
              default_operator: "OR",
              flags: "PHRASE"
            }
          }
        ],
        minimum_should_match: 1
      }
    };
  }
}
