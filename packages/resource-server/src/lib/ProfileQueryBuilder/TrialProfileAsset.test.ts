import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { ProfileFilterValue, ProfileFilterValueType } from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";

import { TrialProfileAsset } from "./TrialProfileAsset";
import { TrialFields } from "@h1nyc/search-sdk";
import { SortDirection } from "@h1nyc/search-sdk";
import { ALL_FLAGS_EXCEPT_WHITESPACE } from "../../services/ParsedQueryTreeToElasticsearchQueries";

const expectedNested = { path: "trials" };

describe("buildIdsAggregation()", () => {
  const expectedAggs = {
    ids: { terms: { size: 10000, field: "trials.id" } }
  };

  it("throws an error when both terms and filters are missing", () => {
    const asset = new TrialProfileAsset();
    expect(() => asset.buildIdsAggregation()).toThrowError();
  });

  it("adds range filter clause for min date filter", () => {
    const asset = new TrialProfileAsset();
    const now = new Date().getTime();
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: now
        }
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              range: {
                "trials.startDate": { gte: now }
              }
            }
          ]
        }
      }
    ];

    expect(aggs.trials).toHaveProperty("nested", expectedNested);
    expect(aggs.trials.aggs.trialFilters.aggs).toEqual(expectedAggs);

    expect(aggs.trials.aggs.trialFilters.filters.filters).toEqual(
      expectedFilters
    );
  });

  it("adds range filter clause for max date filter", () => {
    const asset = new TrialProfileAsset();
    const now = new Date().getTime();
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          max: now
        }
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              range: {
                "trials.startDate": { lte: now }
              }
            }
          ]
        }
      }
    ];

    expect(aggs.trials).toHaveProperty("nested", expectedNested);
    expect(aggs.trials.aggs.trialFilters.aggs).toEqual(expectedAggs);

    expect(aggs.trials.aggs.trialFilters.filters.filters).toEqual(
      expectedFilters
    );
  });

  it("adds range filter clause for min and max date filter", () => {
    const asset = new TrialProfileAsset();
    const now = new Date().getTime();
    const later = now + 1000;
    const filterValues: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: now,
          max: later
        }
      }
    ];
    const aggs = asset.buildIdsAggregation(undefined, filterValues);

    const expectedFilters = [
      {
        bool: {
          must: [
            {
              range: {
                "trials.startDate": { gte: now, lte: later }
              }
            }
          ]
        }
      }
    ];

    expect(aggs.trials).toHaveProperty("nested", expectedNested);
    expect(aggs.trials.aggs.trialFilters.aggs).toEqual(expectedAggs);

    expect(aggs.trials.aggs.trialFilters.filters.filters).toEqual(
      expectedFilters
    );
  });
});

describe("buildMetricsAggregation", () => {
  beforeAll(() => {
    jest.useFakeTimers().setSystemTime(new Date("2021-11-30").getTime());
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it("should return unfiltered aggregations when neither terms nor filters are supplied", () => {
    const trialProfileAsset = new TrialProfileAsset();

    const query = trialProfileAsset.buildMetricsAggregation();

    expect(query).toEqual({
      trials: {
        nested: {
          path: "trials"
        },
        aggs: {
          ids: {
            terms: {
              size: 10000,
              field: "trials.id"
            }
          },
          yearTotals: {
            date_histogram: {
              field: "trials.primaryCompletionDate",
              calendar_interval: "year"
            }
          },
          completedCount: {
            filter: {
              bool: {
                must: [
                  {
                    range: {
                      "trials.completionDate": {
                        lt: new Date().getTime()
                      }
                    }
                  }
                ]
              }
            }
          },
          terminatedCount: {
            filter: {
              bool: {
                should: [
                  { term: { "trials.status_eng": "Terminated" } },
                  { term: { "trials.status_eng": "Withdrawn" } },
                  { term: { "trials.status_eng": "Terminated or Withdrawn" } }
                ]
              }
            }
          },
          inProgressCount: {
            filter: {
              bool: {
                must: [
                  {
                    terms: {
                      "trials.status_eng": [
                        "Not yet recruiting",
                        "Recruiting",
                        "Enrolling by invitation",
                        "Active, not recruiting",
                        "Suspended"
                      ]
                    }
                  }
                ]
              }
            }
          },
          topConditions: {
            terms: {
              field: "trials.conditions_eng.keyword",
              size: 5,
              order: { _count: "desc" }
            }
          },
          topInterventions: {
            terms: {
              field: "trials.interventions_eng.keyword",
              size: 5,
              order: { _count: "desc" }
            }
          }
        }
      }
    });
  });

  it("should include all supplied filters in the query", () => {
    const trialProfileAsset = new TrialProfileAsset();

    const minDate = new Date(faker.date.past()).getTime();
    const maxDate = new Date(faker.date.past()).getTime();

    const filters: Array<ProfileFilterValue> = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: minDate,
          max: maxDate
        }
      }
    ];

    const query = trialProfileAsset.buildMetricsAggregation(undefined, filters);

    expect(query).toEqual({
      trials: {
        nested: {
          path: "trials"
        },
        aggs: {
          trialFilters: {
            filters: {
              filters: [
                {
                  bool: {
                    must: [
                      {
                        range: {
                          "trials.startDate": {
                            gte: minDate,
                            lte: maxDate
                          }
                        }
                      }
                    ]
                  }
                }
              ]
            },
            aggs: {
              ids: {
                terms: {
                  size: 10000,
                  field: "trials.id"
                }
              },
              yearTotals: {
                date_histogram: {
                  field: "trials.primaryCompletionDate",
                  calendar_interval: "year"
                }
              },
              completedCount: {
                filter: {
                  bool: {
                    must: [
                      {
                        range: {
                          "trials.completionDate": {
                            lt: new Date().getTime()
                          }
                        }
                      }
                    ]
                  }
                }
              },
              terminatedCount: {
                filter: {
                  bool: {
                    should: [
                      { term: { "trials.status_eng": "Terminated" } },
                      { term: { "trials.status_eng": "Withdrawn" } },
                      {
                        term: { "trials.status_eng": "Terminated or Withdrawn" }
                      }
                    ]
                  }
                }
              },
              inProgressCount: {
                filter: {
                  bool: {
                    must: [
                      {
                        terms: {
                          "trials.status_eng": [
                            "Not yet recruiting",
                            "Recruiting",
                            "Enrolling by invitation",
                            "Active, not recruiting",
                            "Suspended"
                          ]
                        }
                      }
                    ]
                  }
                }
              },
              topConditions: {
                terms: {
                  field: "trials.conditions_eng.keyword",
                  size: 5,
                  order: { _count: "desc" }
                }
              },
              topInterventions: {
                terms: {
                  field: "trials.interventions_eng.keyword",
                  size: 5,
                  order: { _count: "desc" }
                }
              }
            }
          }
        }
      }
    });
  });

  it("should not apply any aggregation filters when filters is an empty array", () => {
    const trialProfileAsset = new TrialProfileAsset();

    const query = trialProfileAsset.buildMetricsAggregation(undefined, []);

    expect(query).toEqual({
      trials: {
        nested: {
          path: "trials"
        },
        aggs: {
          ids: {
            terms: {
              size: 10000,
              field: "trials.id"
            }
          },
          yearTotals: {
            date_histogram: {
              field: "trials.primaryCompletionDate",
              calendar_interval: "year"
            }
          },
          completedCount: {
            filter: {
              bool: {
                must: [
                  {
                    range: {
                      "trials.completionDate": {
                        lt: new Date().getTime()
                      }
                    }
                  }
                ]
              }
            }
          },
          terminatedCount: {
            filter: {
              bool: {
                should: [
                  { term: { "trials.status_eng": "Terminated" } },
                  { term: { "trials.status_eng": "Withdrawn" } },
                  { term: { "trials.status_eng": "Terminated or Withdrawn" } }
                ]
              }
            }
          },
          inProgressCount: {
            filter: {
              bool: {
                must: [
                  {
                    terms: {
                      "trials.status_eng": [
                        "Not yet recruiting",
                        "Recruiting",
                        "Enrolling by invitation",
                        "Active, not recruiting",
                        "Suspended"
                      ]
                    }
                  }
                ]
              }
            }
          },
          topConditions: {
            terms: {
              field: "trials.conditions_eng.keyword",
              size: 5,
              order: { _count: "desc" }
            }
          },
          topInterventions: {
            terms: {
              field: "trials.interventions_eng.keyword",
              size: 5,
              order: { _count: "desc" }
            }
          }
        }
      }
    });
  });

  describe("query container", () => {
    it("should only include filter for terms when no filters are supplied", () => {
      const trialProfileAsset = new TrialProfileAsset();

      const queryContainer: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };

      const query = trialProfileAsset.buildMetricsAggregation(queryContainer);

      expect(query).toEqual({
        trials: {
          nested: {
            path: "trials"
          },
          aggs: {
            trialFilters: {
              filters: {
                filters: [
                  {
                    bool: {
                      must: [queryContainer]
                    }
                  }
                ]
              },
              aggs: {
                ids: {
                  terms: {
                    size: 10000,
                    field: "trials.id"
                  }
                },
                yearTotals: {
                  date_histogram: {
                    field: "trials.primaryCompletionDate",
                    calendar_interval: "year"
                  }
                },
                completedCount: {
                  filter: {
                    bool: {
                      must: [
                        {
                          range: {
                            "trials.completionDate": {
                              lt: new Date().getTime()
                            }
                          }
                        }
                      ]
                    }
                  }
                },
                terminatedCount: {
                  filter: {
                    bool: {
                      should: [
                        { term: { "trials.status_eng": "Terminated" } },
                        { term: { "trials.status_eng": "Withdrawn" } },
                        {
                          term: {
                            "trials.status_eng": "Terminated or Withdrawn"
                          }
                        }
                      ]
                    }
                  }
                },
                inProgressCount: {
                  filter: {
                    bool: {
                      must: [
                        {
                          terms: {
                            "trials.status_eng": [
                              "Not yet recruiting",
                              "Recruiting",
                              "Enrolling by invitation",
                              "Active, not recruiting",
                              "Suspended"
                            ]
                          }
                        }
                      ]
                    }
                  }
                },
                topConditions: {
                  terms: {
                    field: "trials.conditions_eng.keyword",
                    size: 5,
                    order: { _count: "desc" }
                  }
                },
                topInterventions: {
                  terms: {
                    field: "trials.interventions_eng.keyword",
                    size: 5,
                    order: { _count: "desc" }
                  }
                }
              }
            }
          }
        }
      });
    });

    it("should apply all supplied filters in addition to terms", () => {
      const trialProfileAsset = new TrialProfileAsset();

      const queryContainer: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };

      const minDate = new Date(faker.date.past()).getTime();
      const maxDate = new Date(faker.date.past()).getTime();

      const filters: Array<ProfileFilterValue> = [
        {
          type: ProfileFilterValueType.TrialDate,
          value: {
            min: minDate,
            max: maxDate
          }
        }
      ];

      const query = trialProfileAsset.buildMetricsAggregation(
        queryContainer,
        filters
      );

      expect(query).toEqual({
        trials: {
          nested: {
            path: "trials"
          },
          aggs: {
            trialFilters: {
              filters: {
                filters: [
                  {
                    bool: {
                      must: [
                        queryContainer,
                        {
                          range: {
                            "trials.startDate": {
                              gte: minDate,
                              lte: maxDate
                            }
                          }
                        }
                      ]
                    }
                  }
                ]
              },
              aggs: {
                ids: {
                  terms: {
                    size: 10000,
                    field: "trials.id"
                  }
                },
                yearTotals: {
                  date_histogram: {
                    field: "trials.primaryCompletionDate",
                    calendar_interval: "year"
                  }
                },
                completedCount: {
                  filter: {
                    bool: {
                      must: [
                        {
                          range: {
                            "trials.completionDate": {
                              lt: new Date().getTime()
                            }
                          }
                        }
                      ]
                    }
                  }
                },
                terminatedCount: {
                  filter: {
                    bool: {
                      should: [
                        { term: { "trials.status_eng": "Terminated" } },
                        { term: { "trials.status_eng": "Withdrawn" } },
                        {
                          term: {
                            "trials.status_eng": "Terminated or Withdrawn"
                          }
                        }
                      ]
                    }
                  }
                },
                inProgressCount: {
                  filter: {
                    bool: {
                      must: [
                        {
                          terms: {
                            "trials.status_eng": [
                              "Not yet recruiting",
                              "Recruiting",
                              "Enrolling by invitation",
                              "Active, not recruiting",
                              "Suspended"
                            ]
                          }
                        }
                      ]
                    }
                  }
                },
                topConditions: {
                  terms: {
                    field: "trials.conditions_eng.keyword",
                    size: 5,
                    order: { _count: "desc" }
                  }
                },
                topInterventions: {
                  terms: {
                    field: "trials.interventions_eng.keyword",
                    size: 5,
                    order: { _count: "desc" }
                  }
                }
              }
            }
          }
        }
      });
    });
  });
});

describe("buildPaginatedTrialsQuery", () => {
  it("should add a projectId filter", () => {
    const asset = new TrialProfileAsset();
    const personId = faker.datatype.uuid();
    const projectId = faker.datatype.number().toString();
    const terms = {
      simple_query_string: {
        query: faker.datatype.string(),
        default_operator: "AND",
        fields: [faker.random.word(), faker.random.word()],
        flags: ALL_FLAGS_EXCEPT_WHITESPACE
      }
    } as QueryDslQueryContainer;
    const filters: ProfileFilterValue[] = [];
    const page = {
      limit: faker.datatype.number(),
      offset: faker.datatype.number()
    };

    const sortOptions = {
      sortBy: TrialFields.startDate,
      direction: SortDirection.Desc
    };

    const queryReceived = asset.buildPaginatedTrialsQuery(
      personId,
      projectId,
      terms,
      filters,
      page,
      sortOptions
    );

    expect(queryReceived).toEqual({
      bool: {
        filter: expect.arrayContaining([
          {
            term: {
              projectIds: projectId
            }
          }
        ])
      }
    });
  });

  it("should add a personId filter", () => {
    const asset = new TrialProfileAsset();
    const personId = faker.datatype.uuid();
    const projectId = faker.datatype.number().toString();
    const terms = {
      simple_query_string: {
        query: faker.datatype.string(),
        default_operator: "AND",
        fields: [faker.random.word(), faker.random.word()],
        flags: ALL_FLAGS_EXCEPT_WHITESPACE
      }
    } as QueryDslQueryContainer;
    const filters: ProfileFilterValue[] = [];
    const page = {
      limit: faker.datatype.number(),
      offset: faker.datatype.number()
    };

    const sortOptions = {
      sortBy: TrialFields.startDate,
      direction: SortDirection.Desc
    };

    const queryReceived = asset.buildPaginatedTrialsQuery(
      personId,
      projectId,
      terms,
      filters,
      page,
      sortOptions
    );

    expect(queryReceived).toEqual({
      bool: {
        filter: expect.arrayContaining([
          {
            bool: {
              should: [
                {
                  term: {
                    id: personId
                  }
                },
                {
                  term: {
                    h1dn_id: personId
                  }
                }
              ],
              minimum_should_match: 1
            }
          }
        ])
      }
    });
  });

  it("should add a nested trials query filter using supplied terms and filters", () => {
    const asset = new TrialProfileAsset();
    const personId = faker.datatype.uuid();
    const projectId = faker.datatype.number().toString();
    const terms = {
      simple_query_string: {
        query: faker.datatype.string(),
        default_operator: "AND",
        fields: [faker.random.word(), faker.random.word()],
        flags: ALL_FLAGS_EXCEPT_WHITESPACE
      }
    } as QueryDslQueryContainer;
    const minDate = faker.date.future().getTime();
    const maxDate = faker.date.past().getTime();
    const filters: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: minDate,
          max: maxDate
        }
      }
    ];
    const page = {
      limit: faker.datatype.number(),
      offset: faker.datatype.number()
    };

    const sortOptions = {
      sortBy: TrialFields.startDate,
      direction: SortDirection.Desc
    };

    const queryReceived = asset.buildPaginatedTrialsQuery(
      personId,
      projectId,
      terms,
      filters,
      page,
      sortOptions
    );

    expect(queryReceived).toEqual({
      bool: {
        filter: expect.arrayContaining([
          {
            nested: {
              path: "trials",
              query: {
                bool: {
                  filter: [
                    terms,
                    {
                      range: {
                        "trials.startDate": {
                          gte: minDate,
                          lte: maxDate
                        }
                      }
                    }
                  ]
                }
              },
              inner_hits: {
                _source: false,
                docvalue_fields: [
                  "trials.id",
                  "trials.source",
                  "trials.affiliatedBy"
                ],
                from: page.offset,
                size: page.limit,
                sort: [
                  {
                    [sortOptions.sortBy]: {
                      order: sortOptions.direction
                    }
                  }
                ]
              }
            }
          }
        ])
      }
    });
  });

  it("should sort by trials startDate in descending order by default", () => {
    const asset = new TrialProfileAsset();
    const personId = faker.datatype.uuid();
    const projectId = faker.datatype.number().toString();
    const terms = {
      simple_query_string: {
        query: faker.datatype.string(),
        default_operator: "AND",
        fields: [faker.random.word(), faker.random.word()],
        flags: ALL_FLAGS_EXCEPT_WHITESPACE
      }
    } as QueryDslQueryContainer;
    const minDate = faker.date.future().getTime();
    const maxDate = faker.date.past().getTime();
    const filters: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: minDate,
          max: maxDate
        }
      }
    ];
    const page = {
      limit: faker.datatype.number(),
      offset: faker.datatype.number()
    };

    const queryReceived = asset.buildPaginatedTrialsQuery(
      personId,
      projectId,
      terms,
      filters,
      page
    );

    expect(queryReceived).toEqual({
      bool: {
        filter: expect.arrayContaining([
          {
            nested: {
              path: "trials",
              query: {
                bool: {
                  filter: [
                    terms,
                    {
                      range: {
                        "trials.startDate": {
                          gte: minDate,
                          lte: maxDate
                        }
                      }
                    }
                  ]
                }
              },
              inner_hits: {
                _source: false,
                docvalue_fields: [
                  "trials.id",
                  "trials.source",
                  "trials.affiliatedBy"
                ],
                from: page.offset,
                size: page.limit,
                sort: [
                  {
                    "trials.startDate": {
                      order: "desc"
                    }
                  }
                ]
              }
            }
          }
        ])
      }
    });
  });

  it("should add a nested trials query filter with a match_all when no terms or filters supplied", () => {
    const asset = new TrialProfileAsset();
    const personId = faker.datatype.uuid();
    const projectId = faker.datatype.number().toString();
    const page = {
      limit: faker.datatype.number(),
      offset: faker.datatype.number()
    };

    const sortOptions = {
      sortBy: TrialFields.startDate,
      direction: SortDirection.Desc
    };

    const queryReceived = asset.buildPaginatedTrialsQuery(
      personId,
      projectId,
      undefined,
      undefined,
      page,
      sortOptions
    );

    expect(queryReceived).toEqual({
      bool: {
        filter: expect.arrayContaining([
          {
            nested: {
              path: "trials",
              query: {
                match_all: {}
              },
              inner_hits: {
                _source: false,
                docvalue_fields: [
                  "trials.id",
                  "trials.source",
                  "trials.affiliatedBy"
                ],
                from: page.offset,
                size: page.limit,
                sort: [
                  {
                    [sortOptions.sortBy]: {
                      order: sortOptions.direction
                    }
                  }
                ]
              }
            }
          }
        ])
      }
    });
  });

  it("should set from and size to 0 and 10000 by default", () => {
    const asset = new TrialProfileAsset();
    const personId = faker.datatype.uuid();
    const projectId = faker.datatype.number().toString();
    const terms = {
      simple_query_string: {
        query: faker.datatype.string(),
        default_operator: "AND",
        fields: [faker.random.word(), faker.random.word()],
        flags: ALL_FLAGS_EXCEPT_WHITESPACE
      }
    } as QueryDslQueryContainer;
    const minDate = faker.date.future().getTime();
    const maxDate = faker.date.past().getTime();
    const filters: ProfileFilterValue[] = [
      {
        type: ProfileFilterValueType.TrialDate,
        value: {
          min: minDate,
          max: maxDate
        }
      }
    ];

    const sortOptions = {
      sortBy: TrialFields.startDate,
      direction: SortDirection.Desc
    };

    const queryReceived = asset.buildPaginatedTrialsQuery(
      personId,
      projectId,
      terms,
      filters,
      undefined,
      sortOptions
    );

    expect(queryReceived).toEqual({
      bool: {
        filter: expect.arrayContaining([
          {
            nested: {
              path: "trials",
              query: {
                bool: {
                  filter: [
                    terms,
                    {
                      range: {
                        "trials.startDate": {
                          gte: minDate,
                          lte: maxDate
                        }
                      }
                    }
                  ]
                }
              },
              inner_hits: {
                _source: false,
                docvalue_fields: [
                  "trials.id",
                  "trials.source",
                  "trials.affiliatedBy"
                ],
                from: 0,
                size: 10000,
                sort: [
                  {
                    [sortOptions.sortBy]: {
                      order: sortOptions.direction
                    }
                  }
                ]
              }
            }
          }
        ])
      }
    });
  });
});
