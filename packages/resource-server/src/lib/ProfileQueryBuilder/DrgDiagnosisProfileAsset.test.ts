import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { ProfileFilterValue } from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";
import { DrgDiagnosisProfileAsset } from "./DrgDiagnosisProfileAsset";

describe("buildMetricsAggregation", () => {
  it("should return unfiltered aggregations when neither terms nor filters are supplied", () => {
    const drgDiagnosisProfileAsset = new DrgDiagnosisProfileAsset();

    const query = drgDiagnosisProfileAsset.buildMetricsAggregation();

    expect(query).toEqual({
      diagnoses: {
        nested: {
          path: "DRG_diagnoses"
        },
        aggs: {
          internalCount: {
            sum: {
              field: "DRG_diagnoses.internalCount"
            }
          }
        }
      }
    });
  });

  it("should not apply any aggregation filters when filters is an empty array", () => {
    const drgDiagnosisProfileAsset = new DrgDiagnosisProfileAsset();

    const terms = undefined;
    const filters: Array<ProfileFilterValue> = [];
    const query = drgDiagnosisProfileAsset.buildMetricsAggregation(
      terms,
      filters
    );

    expect(query).toEqual({
      diagnoses: {
        nested: {
          path: "DRG_diagnoses"
        },
        aggs: {
          internalCount: {
            sum: {
              field: "DRG_diagnoses.internalCount"
            }
          }
        }
      }
    });
  });

  describe("query container", () => {
    it("should only include filter for terms when no filters are supplied", () => {
      const drgDiagnosisProfileAsset = new DrgDiagnosisProfileAsset();

      const queryContainer: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };

      const query =
        drgDiagnosisProfileAsset.buildMetricsAggregation(queryContainer);

      expect(query).toEqual({
        diagnoses: {
          nested: {
            path: "DRG_diagnoses"
          },
          aggs: {
            diagnosisFilters: {
              filters: {
                filters: [
                  {
                    bool: {
                      must: [queryContainer]
                    }
                  }
                ]
              },
              aggs: {
                internalCount: {
                  sum: {
                    field: "DRG_diagnoses.internalCount"
                  }
                }
              }
            }
          }
        }
      });
    });

    it("should apply all supplied filters in addition to terms", () => {
      const drgDiagnosisProfileAsset = new DrgDiagnosisProfileAsset();

      const queryContainer: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };
      const filters: Array<ProfileFilterValue> = [];

      const query = drgDiagnosisProfileAsset.buildMetricsAggregation(
        queryContainer,
        filters
      );

      expect(query).toEqual({
        diagnoses: {
          nested: {
            path: "DRG_diagnoses"
          },
          aggs: {
            diagnosisFilters: {
              filters: {
                filters: [
                  {
                    bool: {
                      must: [queryContainer]
                    }
                  }
                ]
              },
              aggs: {
                internalCount: {
                  sum: {
                    field: "DRG_diagnoses.internalCount"
                  }
                }
              }
            }
          }
        }
      });
    });
  });
});
