import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { ProfileFilterValue } from "@h1nyc/search-sdk";
import { AbstractProfileAsset } from "./ProfileAsset";

export class DrgProcedureProfileAsset extends AbstractProfileAsset {
  matchFields = [
    "DRG_procedures.description_eng",
    "DRG_procedures.procedureCode_eng.autocomplete_search"
  ];

  buildMetricsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    let aggs;

    if (terms || filters?.length) {
      aggs = this.getFilteredAggregation(this.getMetrics(), terms, filters);
    } else {
      aggs = this.getMetrics();
    }

    return {
      procedures: {
        nested: {
          path: "DRG_procedures"
        },
        aggs
      }
    };
  }

  private getFilteredAggregation(
    subAggs: any,
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    const must: Array<QueryDslQueryContainer> = [];

    if (terms) {
      must.push(terms);
    }

    if (filters?.length) {
      must.push(...this.buildFilters(filters));
    }

    // TODO: this appears to be unreachable and therefore untestable
    if (!must.length) {
      throw new Error(
        "Must clause cannot be empty. Terms or filters must be applied"
      );
    }

    return {
      procedureFilters: {
        filters: {
          filters: [
            {
              bool: {
                must
              }
            }
          ]
        },
        aggs: subAggs
      }
    };
  }

  private getMetrics() {
    return {
      internalCount: {
        sum: {
          field: "DRG_procedures.internalCount"
        }
      }
    };
  }
}
