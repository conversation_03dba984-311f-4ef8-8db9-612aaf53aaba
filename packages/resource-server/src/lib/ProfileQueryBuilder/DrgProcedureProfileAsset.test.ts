import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { ProfileFilterValue } from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";
import { DrgProcedureProfileAsset } from "./DrgProcedureProfileAsset";

describe("buildMetricsAggregation", () => {
  it("should return unfiltered aggregations when neither terms nor filters are supplied", () => {
    const drgProcedureProfileAsset = new DrgProcedureProfileAsset();

    const query = drgProcedureProfileAsset.buildMetricsAggregation();

    expect(query).toEqual({
      procedures: {
        nested: {
          path: "DRG_procedures"
        },
        aggs: {
          internalCount: {
            sum: {
              field: "DRG_procedures.internalCount"
            }
          }
        }
      }
    });
  });

  it("should not apply any aggregation filters when filters is an empty array", () => {
    const drgProcedureProfileAsset = new DrgProcedureProfileAsset();

    const terms = undefined;
    const filters: Array<ProfileFilterValue> = [];
    const query = drgProcedureProfileAsset.buildMetricsAggregation(
      terms,
      filters
    );

    expect(query).toEqual({
      procedures: {
        nested: {
          path: "DRG_procedures"
        },
        aggs: {
          internalCount: {
            sum: {
              field: "DRG_procedures.internalCount"
            }
          }
        }
      }
    });
  });

  describe("query container", () => {
    it("should only include filter for terms when no filters are supplied", () => {
      const drgProcedureProfileAsset = new DrgProcedureProfileAsset();

      const queryContainer: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };

      const query =
        drgProcedureProfileAsset.buildMetricsAggregation(queryContainer);

      expect(query).toEqual({
        procedures: {
          nested: {
            path: "DRG_procedures"
          },
          aggs: {
            procedureFilters: {
              filters: {
                filters: [
                  {
                    bool: {
                      must: [queryContainer]
                    }
                  }
                ]
              },
              aggs: {
                internalCount: {
                  sum: {
                    field: "DRG_procedures.internalCount"
                  }
                }
              }
            }
          }
        }
      });
    });

    it("should apply all supplied filters in addition to terms", () => {
      const drgProcedureProfileAsset = new DrgProcedureProfileAsset();

      const queryContainer: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };
      const filters: Array<ProfileFilterValue> = [];

      const query = drgProcedureProfileAsset.buildMetricsAggregation(
        queryContainer,
        filters
      );

      expect(query).toEqual({
        procedures: {
          nested: {
            path: "DRG_procedures"
          },
          aggs: {
            procedureFilters: {
              filters: {
                filters: [
                  {
                    bool: {
                      must: [queryContainer]
                    }
                  }
                ]
              },
              aggs: {
                internalCount: {
                  sum: {
                    field: "DRG_procedures.internalCount"
                  }
                }
              }
            }
          }
        }
      });
    });
  });
});
