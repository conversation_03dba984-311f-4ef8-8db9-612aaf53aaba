import {
  AggregationsAggregationContainer,
  QueryDslQueryContainer
} from "@elastic/elasticsearch/lib/api/types";
import {
  ProfileFilterValue,
  ProfileFilterValueType,
  SortDirection,
  CongressSortOptions
} from "@h1nyc/search-sdk";
import { AbstractProfileAsset, ProfileFilterType } from "./ProfileAsset";

export class CongressProfileAsset extends AbstractProfileAsset {
  // currently clinical trials on supports English
  matchFields = [
    "congress.title_eng",
    "congress.keywords_eng",
    "congress.organizer_eng.search",
    "congress.name_eng.search"
  ];

  filterTypeMap = {
    [ProfileFilterValueType.CongressDate]: ProfileFilterType.Date
  };

  filterFieldMap = {
    [ProfileFilterValueType.CongressDate]: "congress.endDate"
  };

  constructor() {
    super();
  }

  buildIdsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[],
    size?: number,
    sortOptions?: CongressSortOptions
  ) {
    const aggs = this.getFilteredAggregation(
      {
        ids: {
          terms: {
            size: size ?? 10000,
            field: "congress.id",
            ...(!!sortOptions?.direction &&
              !!sortOptions.sortBy && {
                order: {
                  sort_field: sortOptions.direction
                }
              })
          },
          ...(!!sortOptions?.direction &&
            !!sortOptions.sortBy && {
              aggs: {
                sort_field: {
                  ...(sortOptions.direction === SortDirection.Desc && {
                    max: {
                      field: sortOptions.sortBy
                    }
                  }),
                  ...(sortOptions.direction === SortDirection.Asc && {
                    min: {
                      field: sortOptions.sortBy
                    }
                  })
                }
              }
            })
        }
      },
      terms,
      filters
    );

    return {
      congresses: {
        nested: {
          path: "congress"
        },
        aggs
      }
    };
  }

  buildMetricsAggregation(
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    let aggs;

    if (terms || filters?.length) {
      aggs = this.getFilteredAggregation(this.getMetrics(), terms, filters);
    } else {
      aggs = this.getMetrics();
    }

    return {
      congresses: {
        nested: {
          path: "congress"
        },
        aggs
      }
    };
  }

  private getFilteredAggregation(
    subAggs: any,
    terms?: QueryDslQueryContainer,
    filters?: ProfileFilterValue[]
  ) {
    const must: Array<QueryDslQueryContainer> = [];

    if (terms) {
      must.push(terms);
    }

    if (filters?.length) {
      must.push(...this.buildFilters(filters));
    }

    // TODO: this appears to be unreachable and therefore untestable
    if (!must.length) {
      throw new Error(
        "Must clause cannot be empty. Terms or filters must be applied"
      );
    }

    return {
      congressFilters: {
        filters: {
          filters: [
            {
              bool: {
                must
              }
            }
          ]
        },
        aggs: subAggs
      }
    };
  }

  private getMetrics(): Record<string, AggregationsAggregationContainer> {
    return {
      yearTotals: {
        date_histogram: {
          field: "congress.endDate",
          calendar_interval: "year"
        }
      },
      postersTotal: {
        filter: {
          term: {
            "congress.type": "Poster"
          }
        }
      },
      sessionsTotal: {
        filter: {
          term: {
            "congress.type": "Session"
          }
        }
      },
      topCongresses: {
        terms: {
          field: "congress.name_eng",
          size: 3,
          order: {
            _count: "desc"
          }
        }
      }
    };
  }
}
