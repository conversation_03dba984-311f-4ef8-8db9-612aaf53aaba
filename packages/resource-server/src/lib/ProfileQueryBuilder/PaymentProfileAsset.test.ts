import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { ProfileFilterValue, ProfileFilterValueType } from "@h1nyc/search-sdk";
import { faker } from "@faker-js/faker";
import { PaymentProfileAsset } from "./PaymentProfileAsset";

describe("buildMetricsAggregation", () => {
  it("should return unfiltered aggregations when neither terms nor filters are supplied", () => {
    const paymentProfileAsset = new PaymentProfileAsset();

    const query = paymentProfileAsset.buildMetricsAggregation();

    expect(query).toEqual({
      payments: {
        nested: {
          path: "payments"
        },
        aggs: {
          totalPaymentsAmount: {
            sum: {
              field: "payments.amount"
            }
          }
        }
      }
    });
  });

  it("should include all supplied filters in the query", () => {
    const paymentProfileAsset = new PaymentProfileAsset();

    const minDate = new Date(faker.date.past()).getTime();
    const maxDate = new Date(faker.date.past()).getTime();

    const filters: Array<ProfileFilterValue> = [
      {
        type: ProfileFilterValueType.PaymentDate,
        value: {
          min: minDate,
          max: maxDate
        }
      }
    ];

    const query = paymentProfileAsset.buildMetricsAggregation(
      undefined,
      filters
    );

    expect(query).toEqual({
      payments: {
        nested: {
          path: "payments"
        },
        aggs: {
          paymentFilters: {
            filters: {
              filters: [
                {
                  bool: {
                    must: [
                      {
                        range: {
                          "payments.paymentDate": {
                            gte: minDate,
                            lte: maxDate
                          }
                        }
                      }
                    ]
                  }
                }
              ]
            },
            aggs: {
              totalPaymentsAmount: {
                sum: {
                  field: "payments.amount"
                }
              }
            }
          }
        }
      }
    });
  });

  it("should not apply any aggregation filters when filters is an empty array", () => {
    const paymentProfileAsset = new PaymentProfileAsset();

    const query = paymentProfileAsset.buildMetricsAggregation(undefined, []);

    expect(query).toEqual({
      payments: {
        nested: {
          path: "payments"
        },
        aggs: {
          totalPaymentsAmount: {
            sum: {
              field: "payments.amount"
            }
          }
        }
      }
    });
  });

  describe("query container terms", () => {
    it("should only include filter for terms when no filters are supplied", () => {
      const paymentProfileAsset = new PaymentProfileAsset();

      const queryContainer: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };

      const query = paymentProfileAsset.buildMetricsAggregation(queryContainer);

      expect(query).toEqual({
        payments: {
          nested: {
            path: "payments"
          },
          aggs: {
            paymentFilters: {
              filters: {
                filters: [
                  {
                    bool: {
                      must: [queryContainer]
                    }
                  }
                ]
              },
              aggs: {
                totalPaymentsAmount: {
                  sum: {
                    field: "payments.amount"
                  }
                }
              }
            }
          }
        }
      });
    });

    it("should apply all supplied filters in addition to terms", () => {
      const paymentProfileAsset = new PaymentProfileAsset();

      const queryContainer: QueryDslQueryContainer = {
        term: {
          [faker.database.column()]: faker.datatype.string()
        }
      };

      const minDate = new Date(faker.date.past()).getTime();
      const maxDate = new Date(faker.date.past()).getTime();

      const filters: Array<ProfileFilterValue> = [
        {
          type: ProfileFilterValueType.PaymentDate,
          value: {
            min: minDate,
            max: maxDate
          }
        }
      ];

      const query = paymentProfileAsset.buildMetricsAggregation(
        queryContainer,
        filters
      );

      expect(query).toEqual({
        payments: {
          nested: {
            path: "payments"
          },
          aggs: {
            paymentFilters: {
              filters: {
                filters: [
                  {
                    bool: {
                      must: [
                        queryContainer,
                        {
                          range: {
                            "payments.paymentDate": {
                              gte: minDate,
                              lte: maxDate
                            }
                          }
                        }
                      ]
                    }
                  }
                ]
              },
              aggs: {
                totalPaymentsAmount: {
                  sum: {
                    field: "payments.amount"
                  }
                }
              }
            }
          }
        }
      });
    });
  });
});
