export interface MustFilter {
  [key: string]: any;
}

export interface DocType {
  id: string;
  type: number;
  internalCount?: number;
}

interface Shards {
  total: number;
  successful: number;
  skipped: number;
  failed: number;
}

interface Hits {
  total: number;
  max_score: number;
  hits: any[];
}

interface Bucket {
  key: string;
  doc_count: number;
}

interface TrialsIds {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: Bucket[];
}

interface TrialsFilters {
  buckets: Array<{
    trialsIds: TrialsIds;
  }>;
}

interface Trials {
  doc_count: number;
  trialsFilters: TrialsFilters;
}

interface CongressIds {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: Bucket[];
}

interface CongressFilters {
  buckets: Array<{
    congressIds: CongressIds;
  }>;
}

interface Congress {
  doc_count: number;
  congressFilters: CongressFilters;
}

interface PublicationsIds {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: Bucket[];
}

interface PublicationFilters {
  buckets: Array<{
    publicationsIds: PublicationsIds;
  }>;
}

interface Publications {
  doc_count: number;
  publicationFilters: PublicationFilters;
}

interface DiagnosesIds {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: Array<{
    key: string;
    doc_count: number;
    internalCount: {
      value: number;
    };
  }>;
}

interface PaymentValues {
  doc_count_error_upper_bound: number;
  sum_other_doc_count: number;
  buckets: Array<{
    key: string;
    doc_count: number;
    amount: {
      value: number;
    };
  }>;
}

interface DiagnosesFilters {
  buckets: Array<{
    diagnosesIds: DiagnosesIds;
  }>;
}

interface Diagnoses {
  doc_count: number;
  diagnosesFilters: DiagnosesFilters;
}

interface PaymentsFilters {
  buckets: Array<{
    paymentValues: PaymentValues;
  }>;
}

interface Payments {
  doc_count: number;
  paymentsFilters: PaymentsFilters;
}

interface ProceduresFilters {
  buckets: Array<{
    internalCount: {
      value: number;
    };
  }>;
}

interface Procedures {
  doc_count: number;
  proceduresFilters: ProceduresFilters;
}

interface Aggregations {
  trials: Trials;
  congress: Congress;
  publications: Publications;
  diagnoses?: Diagnoses;
  payments: Payments;
  procedures?: Procedures;
}

export interface ProfileSearch {
  took: number;
  timed_out: boolean;
  _shards: Shards;
  hits: Hits;
  aggregations: Aggregations;
}
