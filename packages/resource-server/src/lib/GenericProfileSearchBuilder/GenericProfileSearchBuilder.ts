import { ProjectFeatures } from "@h1nyc/account-user-entities";
import { MustFilter } from "./typedef";

const buildMultiMatchFilter = (
  terms: string[],
  fields: string[]
): MustFilter => {
  const filters: MustFilter[] = [];
  if (terms.length === 0) {
    filters.push({ match_all: {} });
    return {
      filters
    };
  }

  const anyQuotes = terms.some((term) => term.indexOf('"') > -1);

  if (anyQuotes) {
    // Proximity in same field
    filters.push({
      bool: {
        must: [
          {
            simple_query_string: {
              query: terms.join(" "),
              flags: "phrase",
              default_operator: "AND",
              fields
            }
          }
        ]
      }
    });
  } else {
    terms.forEach((term) => {
      filters.push({
        bool: {
          must: [
            {
              simple_query_string: {
                query: term,
                flags: "phrase",
                default_operator: "AND",
                fields
              }
            }
          ]
        }
      });
    });
  }

  return {
    filters
  };
};

export function buildGenericProfileSearchQuery(
  personId: string,
  queries: string[],
  features: ProjectFeatures
): any {
  try {
    return {
      _source: "_none",
      size: 0,
      query: {
        bool: {
          filter: {
            term: {
              id: personId
            }
          }
        }
      },
      aggs: {
        payments: {
          nested: {
            path: "payments"
          },
          aggs: {
            paymentsFilters: {
              filters: buildMultiMatchFilter(queries, [
                "payments.associatedDrugOrDevice",
                "payments.payerCompany"
              ]),
              aggs: {
                paymentValues: {
                  terms: {
                    size: 10000,
                    field: "payments.amount"
                  },

                  aggs: {
                    amount: {
                      sum: {
                        field: "payments.amount"
                      }
                    }
                  }
                }
              }
            }
          }
        },
        publications: {
          nested: {
            path: "publications"
          },
          aggs: {
            publicationFilters: {
              filters: buildMultiMatchFilter(queries, [
                "publications.publicationAbstract_eng",
                "publications.keywords_eng",
                "publications.title_eng"
              ]),
              aggs: {
                publicationsIds: {
                  terms: {
                    size: 10000,
                    field: "publications.id"
                  }
                }
              }
            }
          }
        },
        trials: {
          nested: {
            path: "trials"
          },
          aggs: {
            trialsFilters: {
              filters: buildMultiMatchFilter(queries, [
                "trials.briefTitle_eng",
                "trials.conditions_eng",
                "trials.interventions_eng",
                "trials.keywords_eng",
                "trials.officialTitle_eng",
                "trials.summary_eng"
              ]),
              aggs: {
                trialsIds: {
                  terms: {
                    size: 10000,
                    field: "trials.id"
                  }
                }
              }
            }
          }
        },
        congress: {
          nested: {
            path: "congress"
          },
          aggs: {
            congressFilters: {
              filters: buildMultiMatchFilter(queries, [
                "congress.keywords_eng",
                "congress.title_eng"
              ]),
              aggs: {
                congressIds: {
                  terms: {
                    size: 10000,
                    field: "congress.id"
                  }
                }
              }
            }
          }
        },
        ...(features?.claims && {
          diagnoses: {
            nested: {
              path: "DRG_diagnoses"
            },
            aggs: {
              diagnosesFilters: {
                filters: buildMultiMatchFilter(queries, [
                  "DRG_diagnoses.diagnosisCodeAndDescription_eng"
                ]),
                aggs: {
                  diagnosesIds: {
                    terms: {
                      size: 15000,
                      field: "DRG_diagnoses.codeAndDescription_eng"
                    },
                    aggs: {
                      internalCount: {
                        sum: {
                          field: "DRG_diagnoses.internalCount"
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }),
        ...(features?.claims && {
          procedures: {
            nested: {
              path: "DRG_procedures"
            },
            aggs: {
              proceduresFilters: {
                filters: buildMultiMatchFilter(queries, [
                  "DRG_procedures.procedureCodeAndDescription_eng"
                ]),
                aggs: {
                  internalCount: {
                    sum: {
                      field: "DRG_procedures.internalCount"
                    }
                  }
                }
              }
            }
          }
        })
      }
    };
  } catch (e) {
    console.error(e);
    return undefined;
  }
}
