import {
  QueryDslQueryContainer,
  SearchRequest
} from "@elastic/elasticsearch/lib/api/types";
import { sub } from "date-fns";

export interface ProfileSummaryMetricsResponse {
  aggregations: {
    people?: {
      buckets: {
        key: string;
        publications: {
          // the total career publications
          doc_count: number;
          // the date of the oldest publication
          oldestDate: {
            value: number | null;
          };
          // the total publications in the last 12 months
          last12MonthsCount: {
            buckets: {
              doc_count: number;
            }[];
          };
        };
        trials: {
          // the total career trials
          doc_count: number;
          // the date of the oldest trial
          oldestDate: {
            value: number | null;
          };
          // the total trials that are currently in progress
          inProgressCount: {
            buckets: {
              doc_count: number;
            }[];
          };
        };
        congresses: {
          // the total career congresses
          doc_count: number;
          // the date of the oldest congress
          oldestDate: {
            value: number | null;
          };
        };
        payments: {
          // the total career payments
          doc_count: number;
          // the total amount for all payments
          totalPaymentsAmount: {
            value: number;
          };
          // the companies with the highest total payment amounts
          byCompany: {
            buckets: {
              key: string;
              sumByCompany: {
                value: number;
              };
            }[];
          };
        };
      }[];
    };
  };
}

interface PublicationYearTotalsResponse {
  buckets: {
    key_as_string: string;
    doc_count: number;
  }[];
}

interface PublicationMeshTerms {
  sum_other_doc_count: number;
  buckets: {
    key: string;
    doc_count: number;
    meshTermsByYear: PublicationYearTotalsResponse;
  }[];
}

export interface PublicationMeshTermsResponse {
  aggregations: {
    people: {
      buckets: {
        key: string;
        publications: {
          doc_count: number;
          meshKeywordFilters: {
            buckets: {
              doc_count: number;
              publicationsByYear: PublicationYearTotalsResponse;
              meshTerms: PublicationMeshTerms;
            }[];
          };
        };
      }[];
    };
  };
  doc_count: number;
  publicationsByYear: PublicationYearTotalsResponse;
  meshTerms: PublicationMeshTermsResponse;
}

function buildPeopleIdTermsFilter(peopleIds: string[]) {
  return {
    terms: {
      id: peopleIds
    }
  };
}

function buildProjectIdFilter(projectId: string) {
  return {
    term: {
      projectIds: projectId
    }
  };
}

export function buildProfileSummaryMetricsQuery(
  peopleIds: string[],
  projectId?: string
): SearchRequest {
  const filtersToUse = [];
  filtersToUse.push(buildPeopleIdTermsFilter(peopleIds));
  if (projectId) {
    filtersToUse.push(buildProjectIdFilter(projectId));
  }

  return {
    _source: "_none",
    size: 0,
    query: {
      bool: {
        filter: filtersToUse
      }
    },
    aggs: {
      people: {
        terms: {
          field: "id",
          size: peopleIds.length
        },
        aggs: {
          publications: {
            nested: {
              path: "publications"
            },
            aggs: {
              oldestDate: {
                min: {
                  field: "publications.datePublished"
                }
              },
              last12MonthsCount: {
                filters: {
                  filters: [
                    {
                      bool: {
                        must: [
                          {
                            range: {
                              "publications.datePublished": {
                                gte: sub(new Date(), { months: 12 }).getTime()
                              }
                            }
                          }
                        ]
                      }
                    }
                  ]
                },
                aggs: {}
              }
            }
          },
          trials: {
            nested: {
              path: "trials"
            },
            aggs: {
              oldestDate: {
                min: {
                  field: "trials.completionDate"
                }
              },
              inProgressCount: {
                filters: {
                  filters: [
                    {
                      bool: {
                        must: [
                          {
                            terms: {
                              "trials.status_eng": [
                                "Not yet recruiting",
                                "Recruiting",
                                "Enrolling by invitation",
                                "Active, not recruiting",
                                "Suspended"
                              ]
                            }
                          }
                        ]
                      }
                    }
                  ]
                },
                aggs: {}
              }
            }
          },
          congresses: {
            nested: {
              path: "congress"
            },
            aggs: {
              oldestDate: {
                min: {
                  field: "congress.endDate"
                }
              }
            }
          },
          payments: {
            nested: {
              path: "payments"
            },
            aggs: {
              totalPaymentsAmount: {
                sum: {
                  field: "payments.amount"
                }
              },
              byCompany: {
                terms: {
                  field: "payments.payerCompany",
                  order: {
                    sumByCompany: "desc"
                  },
                  size: 3
                },
                aggs: {
                  sumByCompany: {
                    sum: {
                      field: "payments.amount"
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  };
}

export function buildProfileMeshTermsQuery(
  peopleIds: string[],
  size: number,
  startDate: Date,
  endDate: Date,
  excludeTerms: string[],
  projectId?: string
): SearchRequest {
  const filtersToUse: QueryDslQueryContainer[] = [];
  filtersToUse.push(buildPeopleIdTermsFilter(peopleIds));
  if (projectId) {
    filtersToUse.push(buildProjectIdFilter(projectId));
  }

  return {
    _source: "_none",
    size: 0,
    query: {
      bool: {
        filter: filtersToUse
      }
    },
    aggs: {
      people: {
        terms: {
          field: "id",
          size: 10000
        },
        aggs: {
          publications: {
            nested: {
              path: "publications"
            },
            aggs: {
              meshKeywordFilters: {
                filters: {
                  filters: [
                    {
                      bool: {
                        must: [
                          {
                            range: {
                              "publications.datePublished": {
                                gte: startDate.toString(),
                                lte: endDate.toString()
                              }
                            }
                          }
                        ]
                      }
                    }
                  ]
                },
                aggs: {
                  meshTerms: {
                    terms: {
                      field: "publications.keywords_eng.keyword_lower",
                      size: size,
                      exclude: excludeTerms
                    },
                    aggs: {
                      meshTermsByYear: {
                        date_histogram: {
                          field: "publications.datePublished",
                          calendar_interval: "year",
                          min_doc_count: 0
                        }
                      }
                    }
                  },
                  publicationsByYear: {
                    date_histogram: {
                      field: "publications.datePublished",
                      calendar_interval: "year",
                      min_doc_count: 0
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  };
}
