import { createConnection, Connection, ConnectionOptions } from "typeorm";
import { Entities, PostgresNamingStrategy } from "@h1nyc/pipeline-entities";
import { Inject, Service } from "typedi";
import { ConfigService } from "../../services/ConfigService";
import { Logger, createLogger } from "../Logger";

const entities = Object.values(Entities);

@Service()
export class PipelineDb {
  @Inject()
  private config: ConfigService;

  private connection: PipelineDbConnection;

  private logger: Logger;

  constructor() {
    this.logger = createLogger(PipelineDb.name);
  }

  async getConnection() {
    if (this.connection) return this.connection;

    const opts: ConnectionOptions = {
      type: "postgres",
      name: PipelineDb.name,
      host: this.config.pipelinePostgresHost,
      port: this.config.pipelinePostgresPort,
      database: this.config.pipelinePostgresDb,
      username: this.config.pipelinePostgresUsername,
      password: this.config.pipelinePostgresPassword,
      logging: this.config.ormLogging,
      namingStrategy: new PostgresNamingStrategy(),
      poolErrorHandler: (error) => this.logger.error(error),
      // @ts-ignore
      entities
    };

    this.connection = await createConnection(opts);
    return this.connection;
  }
}

@Service()
export class PipelineDbConnection extends Connection {}
