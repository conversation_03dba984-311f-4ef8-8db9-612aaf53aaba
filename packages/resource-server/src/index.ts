import "./Tracer";
import Container from "typedi";
import { registerEnv } from "@h1nyc/systems-config";
import {
  EngagementResourceClient,
  EngagementResourceClientV2,
  ProjectFeaturesResourceClient,
  TagResourceClient,
  UserResourceClient,
  InstitutionTagResourceClient,
  EntityTagResourceClient,
  AnalyticsResourceClient,
  OnboardingSubmissionResourceClient,
  SavedTerritoryResourceClient
} from "@h1nyc/account-sdk";
import { CTMSResourceClient } from "@h1nyc/search-sdk";
import { RpcResourceService, setRpcLogger } from "@h1nyc/systems-rpc";

import { createLogger } from "./lib/Logger";
import { createHealthProbe } from "./app";
import { ClaimsResourceService } from "./services/ClaimsResourceService";
import { DiversityResourceService } from "./services/DiversityResourceService";
import { CollaboratorsResourceService } from "./services/CollaboratorsResourceService";
import { NameSuggestResourceService } from "./services/NameSuggestResourceService";
import { addClient, RpcServer } from "./util/addClient";
import { ProfileSearchResourceService } from "./services/ProfileSearchResourceService";
import { ConfigService } from "./services/ConfigService";
import { InfluencesResourceService } from "./services/InfluencesResourceService";
import { LanguageDetectService } from "./services/LanguageDetectService";
import { ProfileSummaryResourceService } from "./services/ProfileSummaryResourceService";
import { ClinicalTrialSearchResourceService } from "./services/ClinicalTrialSearchResourceService";
import { ReferralAutosuggestService } from "./services/ReferralAutosuggestService";
import { QueryParserService } from "./services/QueryParserService";
import { InstitutionsResourceService } from "./services/InstitutionsResourceService";
import { KeywordAutocompleteResourceService } from "./services/KeywordAutocompleteResourceService";
import { KeywordSearchResourceServiceRewrite } from "./services/KeywordSearchResourceServiceRewrite";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { CTMSResourceService } from "./services/CTMSResourceService";
import { NetworkResourceService } from "./services/NetworkResourceService";
import { QueryUnderstandingResourceService } from "./services/QueryUnderstandingResourceService";
import { IndicationsTreeSearchResourceService } from "./services/IndicationsTreeSearchResourceService";
import { SearchGrpcServerService } from "./services/SearchGrpcServerService";
import { NameSearchResourceServiceRewrite } from "./services/NameSearchResourceServiceRewrite";
import { InstitutionNameSuggestResourceService } from "./services/InstitutionNameSuggestResourceService";
import {
  PipelineDb,
  PipelineDbConnection
} from "./lib/utils/PipelineDBconnection";
import {
  IndicationInstitutionCountsRepository,
  IndicationIcdInstitutionCountsRepository,
  CcsrIcdMappingRepository,
  CcsrPxMappingRepository,
  ClaimCodesRepository,
  DiseasePrevalenceRepository
} from "@h1nyc/pipeline-repositories";
import { getCustomRepository } from "typeorm";
import { CongressSearchResourceService } from "./services/CongressSearchResourceService";
import { FollowRecordResourceClient } from "@h1nyc/notifications-sdk";
import { CongressEventPageSearchResourceService } from "./services/CongressEventPageSearchResourceService";

// Add additional resource clients here. These will be added to the container
// and then will be utilized in shutdown process as well.
const resourceClients = [
  { name: TagResourceClient, server: RpcServer.Account },
  { name: ProjectFeaturesResourceClient, server: RpcServer.Account },
  { name: UserResourceClient, server: RpcServer.Account },
  { name: EngagementResourceClient, server: RpcServer.Account },
  { name: EngagementResourceClientV2, server: RpcServer.Account },
  { name: InstitutionTagResourceClient, server: RpcServer.Account },
  { name: EntityTagResourceClient, server: RpcServer.Account },
  { name: CTMSResourceClient, server: RpcServer.Search },
  { name: AnalyticsResourceClient, server: RpcServer.Account },
  { name: OnboardingSubmissionResourceClient, server: RpcServer.Account },
  { name: SavedTerritoryResourceClient, server: RpcServer.Account },
  { name: FollowRecordResourceClient, server: RpcServer.Notifications }
];

// Add additional resource services here. These will be added to the container
// and then will be utilized in shutdown process as well.
const resourceServices = [
  ClaimsResourceService,
  DiversityResourceService,
  CollaboratorsResourceService,
  ClinicalTrialSearchResourceService,
  ProfileSearchResourceService,
  NameSuggestResourceService,
  InfluencesResourceService,
  ProfileSummaryResourceService,
  ReferralAutosuggestService,
  InstitutionsResourceService,
  KeywordAutocompleteResourceService,
  KeywordSearchResourceServiceRewrite,
  IndicationsTreeSearchResourceService,
  CTMSResourceService,
  NetworkResourceService,
  QueryUnderstandingResourceService,
  NameSearchResourceServiceRewrite,
  InstitutionNameSuggestResourceService,
  CongressSearchResourceService,
  CongressEventPageSearchResourceService
];

// Temporary flag. Can remove else case once everyone is running on elion-cli
if (process.env.ELION_LOCAL) {
  console.log("Skipping local .env");
  registerEnv();
} else {
  registerEnv("../../.env");
}

async function shutdown() {
  const logger = createLogger();

  logger.info("Gracefully shutting down search gRPC server...");

  const grpcServer = Container.get(SearchGrpcServerService);
  await grpcServer.stopServer();

  logger.info("Search gRPC server shutdown complete");

  logger.info("Gracefully shutting down search resource server.");

  setTimeout(async () => {
    // perform shutdown on all services
    await Promise.all(
      resourceServices.map((serviceName) => {
        const server = Container.get<RpcResourceService>(serviceName);
        return server.rpcServer.shutDown();
      })
    );

    // once servers are shutdown there shouldn't be any messages in the clients anymore.
    // so the reource clients can be forcibly closed.
    await Promise.all(
      resourceClients.map((clientConf) => {
        // @ts-ignore complaining about types not matching. Seems weird to me.
        const client = Container.get<RpcResourceClient>(clientConf.name);
        return client.rpcClient.disconnect(true);
      })
    );

    logger.info("Search resource server shutdown complete");

    process.exit(0);
  }, 10 * 1000); // delay for 10 seconds to give new pod time to spin up if necessary (likely unnecessary).
}

async function run() {
  const logger = createLogger();

  setRpcLogger(logger);

  try {
    const config = Container.get(ConfigService);
    const featureFlagsService = await FeatureFlagsService.create(
      config.launchDarklyClientID
    );
    Container.set(FeatureFlagsService, featureFlagsService);
    const pipelineDb = Container.get(PipelineDb);
    logger.debug("Creating connection to pipeline db");
    Container.set(PipelineDbConnection, await pipelineDb.getConnection());
    logger.error;
    Container.set(
      IndicationInstitutionCountsRepository,
      getCustomRepository(
        IndicationInstitutionCountsRepository,
        PipelineDb.name
      )
    );
    Container.set(
      IndicationIcdInstitutionCountsRepository,
      getCustomRepository(
        IndicationIcdInstitutionCountsRepository,
        PipelineDb.name
      )
    );
    Container.set(
      DiseasePrevalenceRepository,
      getCustomRepository(DiseasePrevalenceRepository, PipelineDb.name)
    );
    Container.set(
      CcsrIcdMappingRepository,
      getCustomRepository(CcsrIcdMappingRepository, PipelineDb.name)
    );
    Container.set(
      CcsrPxMappingRepository,
      getCustomRepository(CcsrPxMappingRepository, PipelineDb.name)
    );
    Container.set(
      ClaimCodesRepository,
      getCustomRepository(ClaimCodesRepository, PipelineDb.name)
    );

    resourceClients.forEach((client) => addClient(client.name, client.server));

    resourceServices.forEach((service) =>
      Container.get<RpcResourceService>(service)
    );

    Container.get(LanguageDetectService);
    Container.get(QueryParserService);

    const app = createHealthProbe();
    const healthPort = config.healthPort;

    const grpcServer = Container.get(SearchGrpcServerService);
    const grpcPort = await grpcServer.startServer();
    logger.info(`gRPC server listening on port: ${grpcPort}`);

    app.listen(healthPort, () => {
      logger.info(
        `The search-resource-server health check listens on ${healthPort}`
      );
    });

    logger.info("Started resource server");
  } catch (err) {
    logger.error("Failed to start up application");
    throw err;
  }
}

run();

process.on("SIGTERM", () => {
  shutdown();
  // this is not actually asynchronous (other signals that kill node,
  // can cause this and other async methods to be abandoned).
  // put all logic in the shutdown() function
});
