import {
  EditOperation,
  getEditDistanceMatrix,
  getOperationsFromEditDistanceMatrix
} from "./editDistanceUtil";

describe("editDistanceUtil", () => {
  describe("getEditDistanceMatrix", () => {
    it("should compute edit distance matrix ignoring character casing", () => {
      const editDist = getEditDistanceMatrix(
        "gastrontology",
        "Gastroenterology"
      );

      const expectedEditDistanceMatrix = [
        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        [1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
        [2, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],
        [3, 2, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
        [4, 3, 2, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        [5, 4, 3, 2, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
        [6, 5, 4, 3, 2, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        [7, 6, 5, 4, 3, 2, 1, 1, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        [8, 7, 6, 5, 4, 3, 2, 2, 2, 1, 2, 3, 4, 5, 6, 7, 8],
        [9, 8, 7, 6, 5, 4, 3, 3, 3, 2, 2, 3, 3, 4, 5, 6, 7],
        [10, 9, 8, 7, 6, 5, 4, 4, 4, 3, 3, 3, 4, 3, 4, 5, 6],
        [11, 10, 9, 8, 7, 6, 5, 5, 5, 4, 4, 4, 3, 4, 3, 4, 5],
        [12, 11, 10, 9, 8, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 4],
        [13, 12, 11, 10, 9, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 3]
      ];

      expect(editDist).toEqual(expectedEditDistanceMatrix);
    });

    it("should return max edits to be equal to length of one string if another is empty", () => {
      const str1 = "test string";
      const editDist = getEditDistanceMatrix(str1, "");

      expect(editDist[str1.length][0]).toEqual(str1.length);
    });
  });

  describe("getOperationsFromEditDistanceMatrix", () => {
    it("should compute edit operations from given edit distance matrix", () => {
      // edit matrix for "gastrontology" and "Gastroenterology"
      const editDistMatrix = [
        [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        [1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
        [2, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],
        [3, 2, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
        [4, 3, 2, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        [5, 4, 3, 2, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
        [6, 5, 4, 3, 2, 1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        [7, 6, 5, 4, 3, 2, 1, 1, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        [8, 7, 6, 5, 4, 3, 2, 2, 2, 1, 2, 3, 4, 5, 6, 7, 8],
        [9, 8, 7, 6, 5, 4, 3, 3, 3, 2, 2, 3, 3, 4, 5, 6, 7],
        [10, 9, 8, 7, 6, 5, 4, 4, 4, 3, 3, 3, 4, 3, 4, 5, 6],
        [11, 10, 9, 8, 7, 6, 5, 5, 5, 4, 4, 4, 3, 4, 3, 4, 5],
        [12, 11, 10, 9, 8, 7, 6, 6, 6, 5, 5, 5, 4, 4, 4, 3, 4],
        [13, 12, 11, 10, 9, 8, 7, 7, 7, 6, 6, 6, 5, 5, 5, 4, 3]
      ];

      const editOps = getOperationsFromEditDistanceMatrix(editDistMatrix);
      const expectedEditOps: EditOperation[] = [
        { operationType: "keep", i: 0, j: 0 }, // g -> G (keep)
        { operationType: "keep", i: 1, j: 1 }, // a -> a (keep)
        { operationType: "keep", i: 2, j: 2 }, // s -> s (keep)
        { operationType: "keep", i: 3, j: 3 }, // t -> t (keep)
        { operationType: "keep", i: 4, j: 4 }, // r -> r (keep)
        { operationType: "keep", i: 5, j: 5 }, // o -> o (keep)
        { operationType: "insert", i: 6, j: 6 }, // '' -> e (insert)
        { operationType: "keep", i: 6, j: 7 }, // n -> n (keep)
        { operationType: "keep", i: 7, j: 8 }, // t -> t (keep)
        { operationType: "insert", i: 8, j: 9 }, // '' -> e (insert)
        { operationType: "insert", i: 8, j: 10 }, // '' -> r (insert)
        { operationType: "keep", i: 8, j: 11 }, // o -> o (keep)
        { operationType: "keep", i: 9, j: 12 }, // l -> l (keep)
        { operationType: "keep", i: 10, j: 13 }, // o -> o (keep)
        { operationType: "keep", i: 11, j: 14 }, // g -> g (keep)
        { operationType: "keep", i: 12, j: 15 } // y -> y (keep)
      ];

      const totalEdits = editOps.filter(
        (editOp) => editOp.operationType !== "keep"
      ).length;

      expect(totalEdits).toEqual(3);
      expect(editOps).toEqual(expectedEditOps);
    });
  });
});
