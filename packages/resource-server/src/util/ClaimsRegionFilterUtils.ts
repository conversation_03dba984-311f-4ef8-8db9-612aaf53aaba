import { InstitutionSearchFeatureFlags } from "../services/InstitutionsResourceService";
import { AutocompleteFeatureFlags } from "../services/KeywordAutocompleteResourceService";
import { KeywordSearchFeatureFlags } from "../services/KeywordSearchResourceServiceRewrite";
import { NameSearchFeatureFlags } from "../services/NameSearchResourceServiceRewrite";
import { NetworkFeatureFlags } from "../services/NetworkResourceService";
import { buildTermsQuery } from "./QueryBuildingUtils";

type FeatureFlags =
  | NameSearchFeatureFlags
  | KeywordSearchFeatureFlags
  | AutocompleteFeatureFlags
  | NetworkFeatureFlags;

export function getCountriesToFilterOutForInstitutionsSearch(
  featureFlags: InstitutionSearchFeatureFlags
) {
  const { enableBrazilianClaims } = featureFlags;
  const countriesToFilterOut = [];

  if (!enableBrazilianClaims) {
    countriesToFilterOut.push("br");
  }

  return countriesToFilterOut;
}

export function getCountriesToFilterOutForPeopleSearch(
  featureFlags: FeatureFlags
) {
  const { enableBrazilianClaims } = featureFlags;
  const countriesToFilterOut = [];

  if (!enableBrazilianClaims) {
    countriesToFilterOut.push("Brazil");
  }

  return countriesToFilterOut;
}

export function buildClaimsRegionFilterForPeopleSearch(
  featureFlags: FeatureFlags
) {
  const countriesToFilterOut =
    getCountriesToFilterOutForPeopleSearch(featureFlags);

  if (countriesToFilterOut.length === 0) {
    return undefined;
  }

  return buildTermsQuery("country_multi", countriesToFilterOut);
}

export function buildClaimsRegionFilterForInstitutionsSearch(
  featureFlags: InstitutionSearchFeatureFlags
) {
  const countryCodes =
    getCountriesToFilterOutForInstitutionsSearch(featureFlags);

  if (countryCodes.length === 0) {
    return undefined;
  }

  return buildTermsQuery("filters.country", countryCodes);
}

export function hasAccessToAllClaimsRegions({
  enableBrazilianClaims
}: FeatureFlags) {
  return enableBrazilianClaims;
}
