import { RedisOptions } from "@h1nyc/systems-rpc";
import Container from "typedi";
import { ConfigService } from "../services/ConfigService";

export enum RpcServer {
  Account = "account",
  Pipeline = "pipeline",
  Search = "search",
  Notifications = "notifications"
}

function getRpcSigningKey(server: RpcServer, config: ConfigService): string {
  switch (server) {
    case RpcServer.Account: {
      return config.accountRpcSigningKey;
    }
    case RpcServer.Pipeline: {
      return config.pipelineRpcSigningKey;
    }
    case RpcServer.Search: {
      return config.searchRpcSigningKey;
    }
    case RpcServer.Notifications: {
      return config.notificationsRpcSigningKey;
    }
    default:
      throw new Error(`Unknown rpc server type: ${server}`);
  }
}

function getRpcRedisOptions(
  server: RpcServer,
  config: ConfigService
): RedisOptions {
  switch (server) {
    case RpcServer.Account: {
      return config.accountRedisOptions;
    }
    case RpcServer.Pipeline: {
      return config.pipelineRedisOptions;
    }
    case RpcServer.Search: {
      return config.searchRedisOptions;
    }
    case RpcServer.Notifications: {
      return config.notificationsRpcRedisOptions;
    }
    default:
      throw new Error(`Unknown rpc server type: ${server}`);
  }
}

/**
 * Adds a RPC client to the default container so services can inject and use
 * more easily
 *
 * @param client
 * @param server
 */
export function addClient(client: any, server: RpcServer): void {
  const config = Container.get(ConfigService);
  const rpcSigningKey = getRpcSigningKey(server, config);
  const redisOptions = getRpcRedisOptions(server, config);

  Container.set(client, new client(rpcSigningKey, redisOptions));
}
