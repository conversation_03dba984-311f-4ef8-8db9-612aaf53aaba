import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import { KeywordSearchFeatureFlags } from "../services/KeywordSearchResourceServiceRewrite";
import { hasAccessToAllClaimsRegions } from "./ClaimsRegionFilterUtils";
import { AutocompleteFeatureFlags } from "../services/KeywordAutocompleteResourceService";

export type NestedPath =
  | "congress"
  | "trials"
  | "publications"
  | "payments"
  | ClaimsPath;
export type ClaimsPath =
  | "DRG_diagnoses"
  | "DRG_procedures"
  | "prescriptions"
  | "ccsr"
  | "ccsr_px";

function findInNestedQuery(
  filter: QueryDslQueryContainer,
  nestedPath: NestedPath
) {
  return filter.nested?.path === nestedPath;
}

function findInNestedFunctionScoreQuery(
  filter: QueryDslQueryContainer | undefined,
  nestedPath: NestedPath
) {
  return filter?.function_score?.query?.nested?.path === nestedPath;
}

function findInNestedQueryOrNestedFunctionScoreQuery(
  filter: QueryDslQueryContainer,
  nestedPath: NestedPath
) {
  return (
    findInNestedQuery(filter, nestedPath) ||
    findInNestedFunctionScoreQuery(filter, nestedPath)
  );
}

function findClaimsFiltersInRegionFilterBool(
  filters: Array<QueryDslQueryContainer>,
  nestedPath: ClaimsPath
) {
  const boolContainer = filters.find((filter) => {
    const regionFilterBoolFilterClause = filter.bool?.filter as
      | Array<QueryDslQueryContainer>
      | undefined;

    if (!regionFilterBoolFilterClause) {
      return false;
    }

    return findClaimsFiltersWithoutRegionFilterBool(
      regionFilterBoolFilterClause,
      nestedPath
    );
  });

  const regionFilterBoolFilterClause = boolContainer?.bool?.filter as
    | Array<QueryDslQueryContainer>
    | undefined;
  if (!regionFilterBoolFilterClause) {
    return [];
  }

  return findClaimsFiltersWithoutRegionFilterBool(
    regionFilterBoolFilterClause,
    nestedPath
  );
}

const findClaimsFiltersInTimeframeFunctionScoreShould =
  findClaimsFiltersInTimeframeFunctionScore.bind(this, "should");

const findClaimsFiltersInTimeframeFunctionScoreMust =
  findClaimsFiltersInTimeframeFunctionScore.bind(this, "must");

function findClaimsFiltersInTimeframeFunctionScore(
  clause: "should" | "must",
  filters: Array<QueryDslQueryContainer>,
  nestedPath: ClaimsPath
) {
  const functionScoreContainer = filters.find((filter) => {
    const nestedClaimsBool = filter.function_score?.query?.bool;
    const nestedClaimsFunctionScore = nestedClaimsBool?.[clause] as
      | Array<QueryDslQueryContainer>
      | undefined;

    return nestedClaimsFunctionScore?.find((nestedFilter) =>
      findInNestedQueryOrNestedFunctionScoreQuery(nestedFilter, nestedPath)
    );
  });

  if (functionScoreContainer) {
    const nestedFilters = functionScoreContainer.function_score?.query?.bool?.[
      clause
    ] as Array<QueryDslQueryContainer>;

    return findNestedFilters(nestedFilters, nestedPath);
  }

  return undefined;
}

export function findNestedClaimsFilters(
  filters: Array<QueryDslQueryContainer>,
  nestedPath: ClaimsPath,
  featureFlags: KeywordSearchFeatureFlags | AutocompleteFeatureFlags
) {
  if (hasAccessToAllClaimsRegions(featureFlags)) {
    return findClaimsFiltersWithoutRegionFilterBool(filters, nestedPath);
  }

  return findClaimsFiltersInRegionFilterBool(filters, nestedPath);
}

function findClaimsFiltersWithoutRegionFilterBool(
  filters: Array<QueryDslQueryContainer>,
  nestedPath: ClaimsPath
) {
  const nestedClaimsInShould = findClaimsFiltersInTimeframeFunctionScoreShould(
    filters,
    nestedPath
  );

  const nestedClaimsInMust = findClaimsFiltersInTimeframeFunctionScoreMust(
    filters,
    nestedPath
  );

  return (
    nestedClaimsInShould ||
    nestedClaimsInMust ||
    findNestedFilters(filters, nestedPath)
  );
}

export function findNestedFilters(
  filters: Array<QueryDslQueryContainer>,
  nestedPath: NestedPath
): Array<QueryDslQueryContainer> {
  const nestedFilters =
    filters.find((filter) => findInNestedQuery(filter, nestedPath)) ??
    filters
      .flatMap(
        (filter) => (filter.bool?.should as QueryDslQueryContainer[]) || []
      )
      .find((inner) => findInNestedQuery(inner, nestedPath));

  if (nestedFilters) {
    return (
      (nestedFilters.nested?.query?.bool
        ?.filter as Array<QueryDslQueryContainer>) || []
    );
  }

  const nestedFunctionScore = filters.find((filter) =>
    findInNestedFunctionScoreQuery(filter, nestedPath)
  );

  if (nestedFunctionScore) {
    return (nestedFunctionScore.function_score?.query?.nested?.query?.bool
      ?.filter ||
      nestedFunctionScore.function_score?.query?.nested?.query?.function_score
        ?.query?.bool?.must) as Array<QueryDslQueryContainer>;
  }

  const functionScoreInsideFunctionScore = filters.find((filter) => {
    const mustClause = filter.function_score?.query?.bool
      ?.must as QueryDslQueryContainer[];
    const shouldClause = filter.function_score?.query?.bool
      ?.should as QueryDslQueryContainer[];
    if (mustClause?.length) {
      return findInNestedFunctionScoreQuery(mustClause[0], nestedPath);
    } else if (shouldClause?.length) {
      return findInNestedFunctionScoreQuery(shouldClause[0], nestedPath);
    }
    return false;
  });

  if (functionScoreInsideFunctionScore) {
    const mustClause = functionScoreInsideFunctionScore.function_score?.query
      ?.bool?.must as QueryDslQueryContainer[];
    const shouldClause = functionScoreInsideFunctionScore.function_score?.query
      ?.bool?.should as QueryDslQueryContainer[];
    if (mustClause?.length) {
      return (mustClause[0].function_score?.query?.nested?.query?.bool
        ?.filter ||
        mustClause[0].function_score?.query?.nested?.query?.function_score
          ?.query?.bool?.must) as Array<QueryDslQueryContainer>;
    } else if (shouldClause?.length) {
      return (shouldClause[0].function_score?.query?.nested?.query?.bool
        ?.filter ||
        shouldClause[0].function_score?.query?.nested?.query?.function_score
          ?.query?.bool?.must) as Array<QueryDslQueryContainer>;
    }
  }

  const shouldClause = filters.find((filter) => !!filter.bool?.should);

  if (shouldClause) {
    const shouldFilterClause = (
      shouldClause?.bool?.should as QueryDslQueryContainer[]
    ).find((filter) => findInNestedQuery(filter, nestedPath));
    if (shouldFilterClause) {
      return shouldFilterClause.nested?.query?.bool
        ?.filter as QueryDslQueryContainer[];
    }
  }

  return [];
}
