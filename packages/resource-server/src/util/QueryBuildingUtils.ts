import {
  QueryDslFunctionBoostMode,
  QueryDslFunctionScoreContainer,
  QueryDslFunctionScoreMode,
  QueryDslQueryContainer
} from "@elastic/elasticsearch/lib/api/types";
import { CcsrIcdMapping, CcsrPxMapping } from "@h1nyc/pipeline-entities";

export function buildScriptScore(
  source: string,
  params?: Record<string, any>
): QueryDslFunctionScoreContainer {
  return {
    script_score: {
      script: {
        source,
        params
      }
    }
  };
}

export function buildTermsQuery(
  fieldName: string,
  terms: (string | number)[]
): QueryDslQueryContainer {
  return {
    terms: {
      [fieldName]: terms
    }
  };
}

export function buildTermQuery(
  fieldName: string,
  term: string | number | boolean
): QueryDslQueryContainer {
  return {
    term: {
      [fieldName]: term
    }
  };
}

export function buildFunctionScoreQueryWithOnlyFunctions(
  functions: QueryDslFunctionScoreContainer[]
): QueryDslQueryContainer {
  return buildFunctionScoreQuery(undefined, functions);
}

export function buildFunctionScoreQuery(
  query: QueryDslQueryContainer | undefined,
  functions: QueryDslFunctionScoreContainer[],
  boostMode?: QueryDslFunctionBoostMode,
  scoreMode?: QueryDslFunctionScoreMode
): QueryDslQueryContainer {
  return {
    function_score: {
      query,
      boost_mode: boostMode,
      score_mode: scoreMode,
      functions
    }
  };
}

export function buildFieldExistsQuery(field: string) {
  return {
    exists: {
      field
    }
  };
}

export function buildFieldMustNotExistsQuery(field: string) {
  return {
    bool: {
      must_not: buildFieldExistsQuery(field)
    }
  };
}

export function buildBoolShouldQuery(
  should: Array<QueryDslQueryContainer>
): QueryDslQueryContainer {
  return {
    bool: {
      should,
      minimum_should_match: 1
    }
  };
}

export function buildGreaterThanRangeQuery(
  fieldName: string,
  value: number
): QueryDslQueryContainer {
  return {
    range: {
      [fieldName]: {
        gt: value
      }
    }
  };
}

export function buildMatchPhraseQuery(
  fieldName: string,
  value: string
): QueryDslQueryContainer {
  return {
    match_phrase: {
      [fieldName]: {
        query: value
      }
    }
  };
}

export function buildMatchPhrasePrefixQuery(
  fieldName: string,
  value: string
): QueryDslQueryContainer {
  return {
    match_phrase_prefix: {
      [fieldName]: {
        query: value
      }
    }
  };
}

export function sortIcdCodesBySchemeAndAlphabet(
  a: CcsrIcdMapping,
  b: CcsrIcdMapping
): number {
  // Helper function to determine if a code is ICD-9 (numeric-only)
  const isIcd9 = (code: string): boolean => /^\d+$/.test(code);

  // Sorting logic: ICD-10 (alphanumeric) comes before ICD-9 (numeric)
  if (!isIcd9(a.icdCode) && isIcd9(b.icdCode)) return -1;
  if (isIcd9(a.icdCode) && !isIcd9(b.icdCode)) return 1;

  // If both codes are of the same type, sort alphabetically
  return a.icdCode.localeCompare(b.icdCode);
}

export function sortProcedureCodesByAlphabet(
  a: CcsrPxMapping,
  b: CcsrPxMapping
): number {
  return a.procedureCode.localeCompare(b.procedureCode);
}
