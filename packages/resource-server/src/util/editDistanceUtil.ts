export interface EditOperation {
  operationType: "keep" | "replace" | "insert" | "delete";
  i: number; // Row index in edit distance matrix
  j: number; // Column index in edit distance matrix
}

export function getEditDistanceMatrix(str1: string, str2: string) {
  const m = str1.length;
  const n = str2.length;

  const dist: number[][] = Array(m + 1)
    .fill(0)
    .map(() => Array(n + 1).fill(0));

  for (let index = 1; index <= m; index++) {
    dist[index][0] = index;
  }

  for (let index = 1; index <= n; index++) {
    dist[0][index] = index;
  }

  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      let cost = 0;
      if (str1[i - 1].toLowerCase() == str2[j - 1].toLowerCase()) {
        cost = 0;
      } else {
        cost = 1;
      }
      dist[i][j] = Math.min(
        dist[i - 1][j] + 1, // Deletion of character
        dist[i][j - 1] + 1, // Insertion of character
        dist[i - 1][j - 1] + cost
      );
    }
  }

  return dist;
}

export function getOperationsFromEditDistanceMatrix(mat: number[][]) {
  const ops: EditOperation[] = [];
  let row = mat.length - 1;
  let col = mat[0].length - 1;

  while (row > 0 && col > 0) {
    const diag = mat[row - 1][col - 1];
    const ver = mat[row - 1][col];
    const hor = mat[row][col - 1];
    const curr = mat[row][col];

    if (diag <= ver && diag <= hor && diag <= curr) {
      row--;
      col--;

      if (diag == curr - 1) {
        ops.push({ operationType: "replace", i: row, j: col });
      } else if (hor <= ver && hor <= curr) {
        ops.push({ operationType: "keep", i: row, j: col });
        col--;
        ops.push({ operationType: "insert", i: row, j: col });
      } else if (ver <= hor && ver <= curr) {
        ops.push({ operationType: "keep", i: row, j: col });
        row--;
        ops.push({ operationType: "delete", i: row, j: -1 });
      } else {
        ops.push({ operationType: "keep", i: row, j: col });
      }
    } else if (hor <= ver && hor <= curr) {
      col--;
      ops.push({ operationType: "insert", i: row, j: col });
    } else {
      row--;
      ops.push({ operationType: "delete", i: row, j: -1 });
    }
  }

  return ops.reverse();
}
