import { InstitutionSearchFeatureFlags } from "../services/InstitutionsResourceService";
import {
  getCountriesToFilterOutForInstitutionsSearch,
  getCountriesToFilterOutForPeopleSearch,
  buildClaimsRegionFilterForPeopleSearch,
  buildClaimsRegionFilterForInstitutionsSearch
} from "./ClaimsRegionFilterUtils";

describe("ClaimsRegionFilterUtils", () => {
  describe("getCountriesToFilterOutForPeopleSearch", () => {
    it("should return ['Brazil'] if enableBrazilianClaims is false", () => {
      const featureFlags = {
        enableBrazilianClaims: false
      };

      const countriesToFilterOut =
        getCountriesToFilterOutForPeopleSearch(featureFlags);

      expect(countriesToFilterOut).toEqual(["Brazil"]);
    });
  });

  describe("getCountriesToFilterOutForInstitutionsSearch", () => {
    it("should return ['br'] if enableBrazilianClaims is false", () => {
      const featureFlags = {
        enableBrazilianClaims: false
      } as InstitutionSearchFeatureFlags;

      const countriesToFilterOut =
        getCountriesToFilterOutForInstitutionsSearch(featureFlags);

      expect(countriesToFilterOut).toEqual(["br"]);
    });
  });

  describe("buildClaimsRegionFilterForPeopleSearch", () => {
    it("should return undefined if enableBrazilianClaims is true", () => {
      const featureFlags = {
        enableBrazilianClaims: true
      };

      const claimsRegionFilter =
        buildClaimsRegionFilterForPeopleSearch(featureFlags);

      expect(claimsRegionFilter).toBeUndefined();
    });

    it("should return a terms query on the country_multi field if enableBrazilianClaims is false", () => {
      const featureFlags = {
        enableBrazilianClaims: false
      };

      const claimsRegionFilter =
        buildClaimsRegionFilterForPeopleSearch(featureFlags);

      expect(claimsRegionFilter).toEqual({
        terms: {
          country_multi: ["Brazil"]
        }
      });
    });
  });

  describe("buildClaimsRegionFilterForInstitutionsSearch", () => {
    it("should return undefined if enableBrazilianClaims is true", () => {
      const featureFlags = {
        enableBrazilianClaims: true
      } as InstitutionSearchFeatureFlags;

      const claimsRegionFilter =
        buildClaimsRegionFilterForInstitutionsSearch(featureFlags);

      expect(claimsRegionFilter).toBeUndefined();
    });

    it("should return a terms query on the filters.country field if enableBrazilianClaims is false", () => {
      const featureFlags = {
        enableBrazilianClaims: false
      } as InstitutionSearchFeatureFlags;

      const claimsRegionFilter =
        buildClaimsRegionFilterForInstitutionsSearch(featureFlags);

      expect(claimsRegionFilter).toEqual({
        terms: {
          "filters.country": ["br"]
        }
      });
    });
  });
});
