import { QueryDslQueryContainer } from "@elastic/elasticsearch/lib/api/types";
import {
  findNestedFilters,
  findNestedClaimsFilters,
  NestedPath,
  ClaimsPath
} from "./QueryParsingUtils";
import { generateMockElasticsearchTermQuery } from "../services/HCPDocumentTestingUtils";
import { faker } from "@faker-js/faker";
import { AutocompleteFeatureFlags } from "../services/KeywordAutocompleteResourceService";

const claimsNestedPaths: ClaimsPath[] = ["DRG_diagnoses", "DRG_procedures"];
const nestedPathsWithoutClaims: NestedPath[] = [
  "congress",
  "trials",
  "publications",
  "payments"
];
const nestedPathsWithClaims: NestedPath[] = [
  ...nestedPathsWithoutClaims,
  ...claimsNestedPaths
];

function generateFeatureFlags(overrides: Partial<AutocompleteFeatureFlags>) {
  return {
    enableBrazilianClaims: faker.datatype.boolean(),
    ...overrides
  } as AutocompleteFeatureFlags;
}

describe("findNestedFilters", () => {
  it("should return nested filters when they exist", () => {
    for (const nestedPath of nestedPathsWithoutClaims) {
      const filters: Array<QueryDslQueryContainer> = [];
      const otherPaths = nestedPathsWithClaims.filter(
        (path) => path !== nestedPath
      );

      for (const otherPath of otherPaths) {
        filters.push({
          nested: {
            path: otherPath,
            query: {
              bool: {
                filter: [generateMockElasticsearchTermQuery()]
              }
            }
          }
        });
      }

      const expectedFilters = [generateMockElasticsearchTermQuery()];
      const targetNestedPath = [
        {
          nested: {
            path: nestedPath,
            query: {
              bool: {
                filter: expectedFilters
              }
            }
          }
        }
      ];

      const indexToInsertTarget = faker.datatype.number(filters.length - 1);
      filters.splice(indexToInsertTarget, 0, ...targetNestedPath);

      const nestedFilters = findNestedFilters(filters, nestedPath);

      expect(nestedFilters).toEqual(expectedFilters);
    }
  });

  it("should return nested filters when they exist in function score query in filter clause", () => {
    for (const nestedPath of nestedPathsWithoutClaims) {
      const filters: Array<QueryDslQueryContainer> = [];
      const otherPaths = nestedPathsWithClaims.filter(
        (path) => path !== nestedPath
      );

      for (const otherPath of otherPaths) {
        filters.push({
          nested: {
            path: otherPath,
            query: {
              bool: {
                filter: [generateMockElasticsearchTermQuery()]
              }
            }
          }
        });
      }

      const expectedFilters = [generateMockElasticsearchTermQuery()];
      const targetNestedPath = [
        {
          function_score: {
            query: {
              nested: {
                path: nestedPath,
                query: {
                  bool: {
                    filter: expectedFilters
                  }
                }
              }
            }
          }
        }
      ];

      const indexToInsertTarget = faker.datatype.number(filters.length - 1);
      filters.splice(indexToInsertTarget, 0, ...targetNestedPath);

      const nestedFilters = findNestedFilters(filters, nestedPath);

      expect(nestedFilters).toEqual(expectedFilters);
    }
  });

  it("should return nested filters when they exist in function score query within another function score query in a must clause", () => {
    for (const nestedPath of nestedPathsWithoutClaims) {
      const filters: Array<QueryDslQueryContainer> = [];
      const otherPaths = nestedPathsWithClaims.filter(
        (path) => path !== nestedPath
      );

      for (const otherPath of otherPaths) {
        filters.push({
          nested: {
            path: otherPath,
            query: {
              bool: {
                filter: [generateMockElasticsearchTermQuery()]
              }
            }
          }
        });
      }

      const expectedFilters = [generateMockElasticsearchTermQuery()];
      const targetNestedPath = [
        {
          function_score: {
            query: {
              nested: {
                path: nestedPath,
                query: {
                  function_score: {
                    query: {
                      bool: {
                        must: expectedFilters
                      }
                    }
                  }
                }
              }
            }
          }
        }
      ];

      const indexToInsertTarget = faker.datatype.number(filters.length - 1);
      filters.splice(indexToInsertTarget, 0, ...targetNestedPath);

      const nestedFilters = findNestedFilters(filters, nestedPath);

      expect(nestedFilters).toEqual(expectedFilters);
    }
  });

  it("should return nested filters when they exist in a should clause", () => {
    for (const nestedPath of nestedPathsWithoutClaims) {
      const filters: Array<QueryDslQueryContainer> = [];
      const otherPaths = nestedPathsWithClaims.filter(
        (path) => path !== nestedPath
      );

      for (const otherPath of otherPaths) {
        filters.push({
          nested: {
            path: otherPath,
            query: {
              bool: {
                filter: [generateMockElasticsearchTermQuery()]
              }
            }
          }
        });
      }

      const expectedFilters = [generateMockElasticsearchTermQuery()];
      const targetNestedPath = [
        {
          bool: {
            should: [
              {
                nested: {
                  path: nestedPath,
                  query: {
                    bool: {
                      filter: expectedFilters
                    }
                  }
                }
              }
            ]
          }
        }
      ];

      const indexToInsertTarget = faker.datatype.number(filters.length - 1);
      filters.splice(indexToInsertTarget, 0, ...targetNestedPath);

      const nestedFilters = findNestedFilters(filters, nestedPath);

      expect(nestedFilters).toEqual(expectedFilters);
    }
  });
});

describe("findNestedClaimsFilters", () => {
  describe("when feature flags allow access to all claims regions", () => {
    it("should return nested filters when they exist", () => {
      for (const nestedPath of claimsNestedPaths) {
        const filters: Array<QueryDslQueryContainer> = [];

        for (const otherPath of nestedPathsWithoutClaims) {
          filters.push({
            nested: {
              path: otherPath,
              query: {
                bool: {
                  filter: [generateMockElasticsearchTermQuery()]
                }
              }
            }
          });
        }

        const expectedFilters = [generateMockElasticsearchTermQuery()];
        const targetNestedPath = [
          {
            nested: {
              path: nestedPath,
              query: {
                bool: {
                  filter: expectedFilters
                }
              }
            }
          }
        ];

        const indexToInsertTarget = faker.datatype.number(filters.length - 1);
        filters.splice(indexToInsertTarget, 0, ...targetNestedPath);

        const nestedFilters = findNestedClaimsFilters(
          filters,
          nestedPath,
          generateFeatureFlags({
            enableBrazilianClaims: true
          })
        );

        expect(nestedFilters).toEqual(expectedFilters);
      }
    });

    describe("when timeframe filter applied", () => {
      it("should return nested filters when they exist in should clause in timeframe function score", () => {
        for (const nestedPath of claimsNestedPaths) {
          const filters: Array<QueryDslQueryContainer> = [];

          for (const otherPath of nestedPathsWithoutClaims) {
            filters.push({
              nested: {
                path: otherPath,
                query: {
                  bool: {
                    filter: [generateMockElasticsearchTermQuery()]
                  }
                }
              }
            });
          }

          const expectedFilters = [generateMockElasticsearchTermQuery()];
          const targetNestedPath = [
            {
              function_score: {
                query: {
                  bool: {
                    should: [
                      {
                        nested: {
                          path: nestedPath,
                          query: {
                            bool: {
                              filter: expectedFilters
                            }
                          }
                        }
                      }
                    ]
                  }
                }
              }
            }
          ];

          const indexToInsertTarget = faker.datatype.number(filters.length - 1);
          filters.splice(indexToInsertTarget, 0, ...targetNestedPath);

          const nestedFilters = findNestedClaimsFilters(
            filters,
            nestedPath,
            generateFeatureFlags({
              enableBrazilianClaims: true
            })
          );

          expect(nestedFilters).toEqual(expectedFilters);
        }
      });

      it("should return nested filters when they exist in must clause in timeframe function score", () => {
        for (const nestedPath of claimsNestedPaths) {
          const filters: Array<QueryDslQueryContainer> = [];

          for (const otherPath of nestedPathsWithoutClaims) {
            filters.push({
              nested: {
                path: otherPath,
                query: {
                  bool: {
                    filter: [generateMockElasticsearchTermQuery()]
                  }
                }
              }
            });
          }

          const expectedFilters = [generateMockElasticsearchTermQuery()];
          const targetNestedPath = [
            {
              function_score: {
                query: {
                  bool: {
                    should: [
                      {
                        nested: {
                          path: nestedPath,
                          query: {
                            bool: {
                              filter: expectedFilters
                            }
                          }
                        }
                      }
                    ]
                  }
                }
              }
            }
          ];

          const indexToInsertTarget = faker.datatype.number(filters.length - 1);
          filters.splice(indexToInsertTarget, 0, ...targetNestedPath);

          const nestedFilters = findNestedClaimsFilters(
            filters,
            nestedPath,
            generateFeatureFlags({
              enableBrazilianClaims: true
            })
          );

          expect(nestedFilters).toEqual(expectedFilters);
        }
      });
    });
  });

  describe("when feature flags do not allow access to all claims regions", () => {
    it("should return nested filters when they exist", () => {
      for (const nestedPath of claimsNestedPaths) {
        const filters: Array<QueryDslQueryContainer> = [];

        for (const otherPath of nestedPathsWithoutClaims) {
          filters.push({
            nested: {
              path: otherPath,
              query: {
                bool: {
                  filter: [generateMockElasticsearchTermQuery()]
                }
              }
            }
          });
        }

        const expectedFilters = [generateMockElasticsearchTermQuery()];
        const targetNestedPath = [
          {
            bool: {
              filter: [
                {
                  nested: {
                    path: nestedPath,
                    query: {
                      bool: {
                        filter: expectedFilters
                      }
                    }
                  }
                }
              ]
            }
          }
        ];

        const indexToInsertTarget = faker.datatype.number(filters.length - 1);
        filters.splice(indexToInsertTarget, 0, ...targetNestedPath);

        const nestedFilters = findNestedClaimsFilters(
          filters,
          nestedPath,
          generateFeatureFlags({
            enableBrazilianClaims: false
          })
        );

        expect(nestedFilters).toEqual(expectedFilters);
      }
    });

    describe("when timeframe filter applied", () => {
      it("should return nested filters when they exist in should clause in timeframe function score", () => {
        for (const nestedPath of claimsNestedPaths) {
          const filters: Array<QueryDslQueryContainer> = [];

          for (const otherPath of nestedPathsWithoutClaims) {
            filters.push({
              nested: {
                path: otherPath,
                query: {
                  bool: {
                    filter: [generateMockElasticsearchTermQuery()]
                  }
                }
              }
            });
          }

          const expectedFilters = [generateMockElasticsearchTermQuery()];
          const targetNestedPath = [
            {
              bool: {
                filter: [
                  {
                    function_score: {
                      query: {
                        bool: {
                          should: [
                            {
                              nested: {
                                path: nestedPath,
                                query: {
                                  bool: {
                                    filter: expectedFilters
                                  }
                                }
                              }
                            }
                          ]
                        }
                      }
                    }
                  }
                ]
              }
            }
          ];

          const indexToInsertTarget = faker.datatype.number(filters.length - 1);
          filters.splice(indexToInsertTarget, 0, ...targetNestedPath);

          const nestedFilters = findNestedClaimsFilters(
            filters,
            nestedPath,
            generateFeatureFlags({
              enableBrazilianClaims: false
            })
          );

          expect(nestedFilters).toEqual(expectedFilters);
        }
      });

      it("should return nested filters when they exist in must clause in timeframe function score", () => {
        for (const nestedPath of claimsNestedPaths) {
          const filters: Array<QueryDslQueryContainer> = [];

          for (const otherPath of nestedPathsWithoutClaims) {
            filters.push({
              nested: {
                path: otherPath,
                query: {
                  bool: {
                    filter: [generateMockElasticsearchTermQuery()]
                  }
                }
              }
            });
          }

          const expectedFilters = [generateMockElasticsearchTermQuery()];
          const targetNestedPath = [
            {
              bool: {
                filter: [
                  {
                    function_score: {
                      query: {
                        bool: {
                          should: [
                            {
                              nested: {
                                path: nestedPath,
                                query: {
                                  bool: {
                                    filter: expectedFilters
                                  }
                                }
                              }
                            }
                          ]
                        }
                      }
                    }
                  }
                ]
              }
            }
          ];

          const indexToInsertTarget = faker.datatype.number(filters.length - 1);
          filters.splice(indexToInsertTarget, 0, ...targetNestedPath);

          const nestedFilters = findNestedClaimsFilters(
            filters,
            nestedPath,
            generateFeatureFlags({
              enableBrazilianClaims: false
            })
          );

          expect(nestedFilters).toEqual(expectedFilters);
        }
      });
    });
  });
});
