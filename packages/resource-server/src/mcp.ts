import { Container } from "typedi";
import { FeatureFlagsService } from "@h1nyc/systems-feature-flags";
import { registerEnv } from "@h1nyc/systems-config";

import { createLogger } from "./lib/Logger";
import { ElasticMCPServerService } from "./services/ElasticMCPServerService";
import { ConfigService } from "./services/ConfigService";
import { ElasticSearchService } from "./services/ElasticSearchService";

async function run() {
  registerEnv();
  const logger = createLogger();
  const config = Container.get(ConfigService);
  const featureFlagService = await FeatureFlagsService.create(
    config.launchDarklyClientID
  );

  const elasticSearchService = new ElasticSearchService(
    config,
    featureFlagService
  );
  try {
    const elasticMCPServerService = new ElasticMCPServerService(
      elasticSearchService
    );

    await elasticMCPServerService.startServer();
  } catch (error) {
    logger.error("Failed to start MCP server");
    throw error;
  }
}

run();
