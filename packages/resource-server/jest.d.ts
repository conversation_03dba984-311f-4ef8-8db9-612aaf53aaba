import { MatcherFunction } from "expect";

declare global {
  namespace jest {
    interface Matchers<R, T> {
      termQuery(path: string, value: string | number | boolean): R;
      termsQuery(path: string, values: Readonly<Array<string>>): R;
      fieldExists(path: string): R;
      positiveRange(path: string): R;
      containsOnly(values: Readonly<Array<string | number>>): R;
      projectIdFilter(value: string): R;
    }

    interface Expect {
      termQuery(path: string, value: string | number | boolean): T;
      termsQuery(path: string, values: Readonly<Array<string>>): T;
      fieldExists(path: string): T;
      positiveRange(path: string): T;
      containsOnly(values: Readonly<Array<string | number>>): T;
      projectIdFilter(value: string): T;
    }

    interface ExpectExtendMap {
      termQuery: MatcherFunction<
        [path: string, value: string | number | boolean]
      >;
      termsQuery: MatcherFunction<
        [path: string, values: Readonly<Array<string>>]
      >;
      fieldExists: MatcherFunction<[path: string]>;
      positiveRange: MatcherFunction<[path: string]>;
      containsOnly: MatcherFunction<[value: Readonly<Array<string | number>>]>;
      projectIdFilter: MatcherFunction<[value: string]>;
    }
  }
}

export {};
