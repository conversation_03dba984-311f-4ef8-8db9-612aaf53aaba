version: "3.8"

services:
  search:
    image: h1-search:${CIRCLE_SHA1}
    build:
      context: .
      dockerfile: ./docker/Dockerfile.local
    environment:
      - SEARCH_RPC_REDIS_HOST=redis
      - ACCOUNT_RPC_REDIS_HOST=redis
      - PIPELINE_RPC_REDIS_HOST=redis
    depends_on:
      - redis

  deps:
    image: alpine
    command: echo "Ready"
    depends_on:
      - redis

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
